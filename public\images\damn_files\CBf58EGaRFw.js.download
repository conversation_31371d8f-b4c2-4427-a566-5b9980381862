;/*FB_PKG_DELIM*/

__d("FocusGroup.react",["FocusManager","Locale","ReactFocusEvent.react","ReactKeyboardEvent.react","focusKeyboardEventPropagation","react","setElementCanTab"],(function(a,b,c,d,e,f,g){var h,i=h||(h=d("react"));b=h;var j=b.useContext,k=b.useMemo,l=b.useRef,m=5;function n(a){return a.length===1}function o(a,b,c,e){d("focusKeyboardEventPropagation").stopFocusKeyboardEventPropagation(c);b=b.DO_NOT_USE_queryFirstNode(a);b!==null&&(document.activeElement!=null&&d("setElementCanTab").setElementCanTab(document.activeElement,!1),d("setElementCanTab").setElementCanTab(b,!0),d("FocusManager").focusElement(b,{preventScroll:e}),c.preventDefault())}function p(a,b,c,d,e,f){b=b.onNavigate;if(b&&d){var g=!1,h=q(d,e);e={currentIndex:h,event:c,focusItem:function(a,b){a=a.scopeRef.current;a&&o(b||f,a,c)},getItem:function(a){return u(d,a)},getItemByTag:function(a){var b=d.length,c=h+1;while(!0){if(c===h)return null;if(c>b-1){c=0;continue}var e=d[c];if(e){var f=e.disabled,g=e.scopeRef,i=e.tag;g=g.current;if(g&&f!==!0&&i===a)return e}c++}return null},preventDefault:function(){g=!0},type:a};b(e);if(g)return!0}return!1}function q(a,b){for(var c=0;c<a.length;c++){var d=a[c];if(d&&d.scopeRef.current===b)return c}return-1}function r(a,b,c){var d=a.scopeRef.current;if(d===null)return null;if(c!==null){d=q(c,b);b=a.wrap;a=v(c,d-1);return!a&&b===!0?v(c,c.length-1):a}return null}function s(a,b,c){var d=a.scopeRef.current;if(d===null)return null;if(c.length>0){d=q(c,b);b=a.wrap;a=t(c,d+1);return!a&&b===!0?t(c,0):a}return null}function t(a,b){var c=a.length;if(b>c)return null;b=b;while(b<c){var d=a[b];if(d!==null&&d.disabled!==!0)return d.scopeRef.current;b++}return null}function u(a,b){b=b;while(b>=0){var c=a[b];if(c!==null&&c.disabled!==!0)return c;b--}return null}function v(a,b){a=u(a,b);return a?a.scopeRef.current:null}function w(a){var b=a.altKey,c=a.ctrlKey,d=a.metaKey;a=a.shiftKey;return b===!0||c===!0||d===!0||a===!0}function a(a){var b=i.unstable_Scope,c=i.createContext(null),e=i.createContext(null);function g(e){var f=e.children,g=e.orientation,j=e.wrap,n=e.tabScopeQuery,o=e.allowModifiers,p=e.preventScrollOnFocus,q=p===void 0?!1:p;p=e.pageJumpSize;var r=p===void 0?m:p,s=e.onNavigate,t=l(null);p=k(function(){return{scopeRef:t,orientation:g,wrap:j,tabScopeQuery:n,allowModifiers:o,pageJumpSize:r,preventScrollOnFocus:q,onNavigate:s}},[g,j,n,o,r,q,s]);var u=l(!1);e=d("ReactFocusEvent.react").useFocusWithin_DEPRECATED(t,k(function(){return{onFocusWithin:function(b){u.current||(u.current=!0,t.current&&a&&(h(t.current,a),d("setElementCanTab").setElementCanTab(b.target,!0)))}}},[u]));return i.jsx(c.Provider,{value:p,children:i.jsx(b,{ref:e,children:f})})}g.displayName=g.name+" [from "+f.id+"]";function h(a,b){var c=document.activeElement;a=a.DO_NOT_USE_queryAllNodes(b);if(a!==null)for(b=0;b<a.length;b++){var e=a[b];e!==c?d("setElementCanTab").setElementCanTab(e,!1):d("setElementCanTab").setElementCanTab(e,!0)}}function u(f){var g=f.children,m=f.disabled;f=f.tag;var u=l(null),x=j(c);d("ReactKeyboardEvent.react").useKeyboard(u,k(function(){return{onKeyDown:function(b){if(d("focusKeyboardEventPropagation").hasFocusKeyboardEventPropagationStopped(b))return;var c=u.current;if(c!==null&&x!==null){var f=x.orientation==="vertical"||x.orientation==="both",g=x.orientation==="horizontal"||x.orientation==="both",i=x.scopeRef.current,j=b.key,k=x.preventScrollOnFocus;if(j==="Tab"&&i!==null){var l=x.tabScopeQuery;if(l){if(x.onNavigate){var m=i.getChildContextValues(e);if(p("TAB",x,b,m,c,l))return}h(i,l)}return}if(w(b)){m=x.allowModifiers;if(m!==!0)return}if(i===null)return;l=j;d("Locale").isRTL()&&(j==="ArrowRight"?l="ArrowLeft":j==="ArrowLeft"&&(l="ArrowRight"));switch(l){case"Home":m=i.getChildContextValues(e);if(p("HOME",x,b,m,c,a))return;l=t(m,0);if(l){o(a,l,b,k);return}break;case"End":m=i.getChildContextValues(e);if(p("END",x,b,m,c,a))return;l=v(m,m.length-1);if(l){o(a,l,b,k);return}break;case"PageUp":m=i.getChildContextValues(e);if(p("PAGE_UP",x,b,m,c,a))return;l=x.pageJumpSize;var y=q(m,c);m=t(m,Math.max(0,y-l));if(m){o(a,m,b,k);return}break;case"PageDown":y=i.getChildContextValues(e);if(p("PAGE_DOWN",x,b,y,c,a))return;l=x.pageJumpSize;m=q(y,c);y=v(y,Math.min(y.length-1,m+l));if(y){o(a,y,b,k);return}break;case"ArrowUp":if(f){m=i.getChildContextValues(e);if(p("PREV_ITEM",x,b,m,c,a))return;l=b.metaKey||b.ctrlKey?t(m,0):r(x,c,m);if(l){o(a,l,b,k);return}}break;case"ArrowDown":if(f){y=i.getChildContextValues(e);if(p("NEXT_ITEM",x,b,y,c,a))return;m=b.metaKey||b.ctrlKey?v(y,y.length-1):s(x,c,y);if(m){o(a,m,b,k);return}}break;case"ArrowLeft":if(g){l=i.getChildContextValues(e);if(p("PREV_ITEM",x,b,l,c,a))return;f=b.metaKey||b.ctrlKey?t(l,0):r(x,c,l);if(f){o(a,f,b,k);return}}break;case"ArrowRight":if(g){y=i.getChildContextValues(e);if(p("NEXT_ITEM",x,b,y,c,a))return;m=b.metaKey||b.ctrlKey?v(y,y.length-1):s(x,c,y);m&&o(a,m,b,k)}break;default:if(n(j)&&x.onNavigate){l=i.getChildContextValues(e);p("PRINT_CHAR",x,b,l,c,a)}}}}}},[x]));var y=d("ReactFocusEvent.react").useFocusWithin_DEPRECATED(u,k(function(){return{onFocusWithin:function(b){if(a!=null){var c;c=(c=u.current)==null?void 0:c.DO_NOT_USE_queryFirstNode(a);b=b.target===c;if(b&&(c&&!d("setElementCanTab").canElementTab(c))){b=x==null?void 0:x.scopeRef.current;b&&h(b,a)}}}}},[x==null?void 0:x.scopeRef]));m={scopeRef:u,disabled:m,tag:f};return i.jsx(e.Provider,{value:m,children:i.jsx(b,{ref:y,children:g})})}u.displayName=u.name+" [from "+f.id+"]";return[g,u]}g.createFocusGroup=a}),98);
__d("IGDSWarningPanoOutline24Icon.react",["IGDSSVGIconBase.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(3),e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e=i.jsx("path",{d:"M12 7.875a1.1 1.1 0 0 0-1.141 1.134l.359 5.005a.79.79 0 0 0 1.565 0l.36-5.005A1.1 1.1 0 0 0 12 7.875Zm0 8.184a1.137 1.137 0 1 0 1.137 1.136A1.137 1.137 0 0 0 12 16.06Zm10.325.63L15.03 4.08a3.5 3.5 0 0 0-6.059 0L1.677 16.688a3.5 3.5 0 0 0 3.029 5.253h14.59a3.5 3.5 0 0 0 3.03-5.253Zm-1.73 2.501a1.484 1.484 0 0 1-1.3.751H4.706a1.5 1.5 0 0 1-1.297-2.25l7.295-12.608a1.5 1.5 0 0 1 2.596 0l7.295 12.607a1.484 1.484 0 0 1 .001 1.5Z"}),b[0]=e):e=b[0];b[1]!==a?(e=i.jsx(c("IGDSSVGIconBase.react"),babelHelpers["extends"]({},a,{viewBox:"0 0 24 24",children:e})),b[1]=a,b[2]=e):e=b[2];return e}b=i.memo(a);g["default"]=b}),98);
__d("InstagramAdReelImpressionFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("3435");b=d("FalcoLoggerInternal").create("instagram_ad_reel_impression",a);e=b;g["default"]=e}),98);
__d("InstagramAdReelSubImpressionFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("3436");b=d("FalcoLoggerInternal").create("instagram_ad_reel_sub_impression",a);e=b;g["default"]=e}),98);
__d("PolarisDirectActionActionToStory",["PolarisDirectAPI","PolarisDirectActionThreadLoaded","PolarisDirectActionsLogger","PolarisDirectStrings","PolarisToastActions","polarisDirectSelectors"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,e,f,g){return function(h,i){i=d("polarisDirectSelectors").getThreadIdForUserId(i(),a);d("PolarisDirectActionsLogger").directActionsLogger.logDirectEvent("http_message_attempt",{type:g});return i!=null?f(b,c,i,e).then(function(){d("PolarisDirectActionsLogger").directActionsLogger.logDirectEvent("http_message_success",{type:g})})["catch"](function(a){d("PolarisDirectActionsLogger").directActionsLogger.logError("reply_to_story_failed_existing_thread",a,{type:g}),h(d("PolarisToastActions").showToast({text:d("PolarisDirectStrings").GENERIC_ERROR_MESSAGE}))}):d("PolarisDirectAPI").createGroupThread([a]).then(function(a){var i=a.thread_id;h(d("PolarisDirectActionThreadLoaded").threadLoaded(a));f(b,c,i,e).then(function(){d("PolarisDirectActionsLogger").directActionsLogger.logDirectEvent("http_message_success",{type:g})})["catch"](function(a){d("PolarisDirectActionsLogger").directActionsLogger.logError("reply_to_story_failed_new_thread",a,{type:g}),h(d("PolarisToastActions").showToast({text:d("PolarisDirectStrings").GENERIC_ERROR_MESSAGE}))})},function(a){d("PolarisDirectActionsLogger").directActionsLogger.logError("reply_to_story_failed_thread_creation",a,{type:g}),h(d("PolarisToastActions").showToast({text:d("PolarisDirectStrings").GENERIC_ERROR_MESSAGE}))})}}g.actionToStory=a}),98);
__d("PolarisDirectActionReactToStory",["PolarisDirectAPI","PolarisDirectActionActionToStory","PolarisDirectConstants"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,e){return d("PolarisDirectActionActionToStory").actionToStory(a,b,c,e,d("PolarisDirectAPI").reelReact,d("PolarisDirectConstants").ThreadItemType.REEL_REACT)}g.reactToStory=a}),98);
__d("PolarisDirectActionReplyToStory",["PolarisDirectAPI","PolarisDirectActionActionToStory","PolarisDirectConstants"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,e){return d("PolarisDirectActionActionToStory").actionToStory(a,b,c,e,d("PolarisDirectAPI").reelShare,d("PolarisDirectConstants").ThreadItemType.REEL_SHARE)}g.replyToStory=a}),98);
__d("PolarisDirectActionShareStory",["InstagramODS","MercuryLocalIDs","PolarisDirectAPI","PolarisDirectActionConstants","PolarisDirectActionSendText","PolarisDirectActionThreadLoaded","PolarisDirectActionsLogger","PolarisDirectConstants","PolarisDirectStrings","PolarisToastActions","Promise","isStringNullOrEmpty","nullthrows","polarisDirectSelectors"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,e,f,g,i,j,k){return function(l,m){e.forEach(function(e,n){var o;e.type==="user"?o=d("polarisDirectSelectors").getThreadIdForUserId(m(),n):e.type==="thread"&&(o=n);d("PolarisDirectActionsLogger").directActionsLogger.logDirectEvent("http_message_attempt",{existingThreadId:o,recipientType:e.type,type:d("PolarisDirectConstants").ThreadItemType.STORY_SHARE});return o!=null?d("PolarisDirectAPI").storyShare(f,g,o).then(function(){j==null?void 0:j(n),d("PolarisDirectActionsLogger").directActionsLogger.logDirectEvent("http_message_success",{recipientType:e.type,type:d("PolarisDirectConstants").ThreadItemType.STORY_SHARE}),c("InstagramODS").incr("web.share_sheet.send_story")}).then(function(){var f=d("MercuryLocalIDs").generateOfflineThreadingID();o!=null&&!c("isStringNullOrEmpty")(i)&&new(h||(h=b("Promise")))(function(a){window.setTimeout(a,d("PolarisDirectActionConstants").NUM_SEC_DELAY_FOR_SEND_TEXT*1e3)}).then(function(){return d("PolarisDirectActionSendText").sendText(a,c("nullthrows")(o),i,f)}).then(function(){d("PolarisDirectActionsLogger").directActionsLogger.logDirectEvent("http_message_success",{recipientType:e.type,type:d("PolarisDirectConstants").ThreadItemType.TEXT}),c("InstagramODS").incr("web.share_sheet.send_story_with_comment")})["catch"](function(a){d("PolarisDirectActionsLogger").directActionsLogger.logError("direct_send_message_to_users_failed",a),l(d("PolarisToastActions").showToast({text:d("PolarisDirectStrings").GENERIC_ERROR_MESSAGE}))})})["catch"](function(a){k==null?void 0:k(a,n),d("PolarisDirectActionsLogger").directActionsLogger.logError("direct_share_story_failed",a,{recipientType:e.type,type:d("PolarisDirectConstants").ThreadItemType.STORY_SHARE}),l(d("PolarisToastActions").showToast({text:d("PolarisDirectStrings").GENERIC_ERROR_MESSAGE}))}):d("PolarisDirectAPI").createGroupThread([n]).then(function(m){var o=m.thread_id;l(d("PolarisDirectActionThreadLoaded").threadLoaded(m));d("PolarisDirectAPI").storyShare(f,g,o).then(function(){j==null?void 0:j(n),d("PolarisDirectActionsLogger").directActionsLogger.logDirectEvent("http_message_success",{type:d("PolarisDirectConstants").ThreadItemType.STORY_SHARE})}).then(function(){var f=d("MercuryLocalIDs").generateOfflineThreadingID();o!=null&&!c("isStringNullOrEmpty")(i)&&new(h||(h=b("Promise")))(function(a){window.setTimeout(a,d("PolarisDirectActionConstants").NUM_SEC_DELAY_FOR_SEND_TEXT*1e3)}).then(function(){return d("PolarisDirectActionSendText").sendText(a,c("nullthrows")(o),i,f)}).then(function(){d("PolarisDirectActionsLogger").directActionsLogger.logDirectEvent("http_message_success",{recipientType:e.type,type:d("PolarisDirectConstants").ThreadItemType.TEXT})})["catch"](function(a){d("PolarisDirectActionsLogger").directActionsLogger.logError("direct_send_message_to_users_failed",a),l(d("PolarisToastActions").showToast({text:d("PolarisDirectStrings").GENERIC_ERROR_MESSAGE}))})})["catch"](function(a){k==null?void 0:k(a,n),d("PolarisDirectActionsLogger").directActionsLogger.logError("share_story_failed_existing_thread",a,{type:d("PolarisDirectConstants").ThreadItemType.STORY_SHARE}),l(d("PolarisToastActions").showToast({text:d("PolarisDirectStrings").GENERIC_ERROR_MESSAGE}))})},function(a){k==null?void 0:k(a,n),d("PolarisDirectActionsLogger").directActionsLogger.logError("share_story_failed_new_thread",a,{type:d("PolarisDirectConstants").ThreadItemType.STORY_SHARE}),l(d("PolarisToastActions").showToast({text:d("PolarisDirectStrings").GENERIC_ERROR_MESSAGE}))})})}}g.shareStory=a}),98);
__d("PolarisHideNativeAppBanner",["PolarisBatchDOM","emptyFunction","polarisGetAppPlatform"],(function(a,b,c,d,e,f,g){"use strict";function a(a){a===void 0&&(a=c("emptyFunction")),d("polarisGetAppPlatform").isIOS()&&d("PolarisBatchDOM").mutate(function(){window.setTimeout(function(){window&&document.body&&(window.scrollTo(0,document.body.scrollHeight),a())},0)})}g.hideNativeAppBanner_HACK=a}),98);
__d("PolarisFullscreenShell.react",["PolarisAppStoreLauncher.react","PolarisBaseShell.react","PolarisHideNativeAppBanner","PolarisToastWrapper.react","PolarisUA","gkx","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j="launch_app_store",k={shell:{height:"x5yr21d",overflowX:"x6ikm8r",overflowY:"x10wlt62",width:"xh8yej3",$$css:!0}};a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}var e=b.prototype;e.componentDidMount=function(){d("PolarisHideNativeAppBanner").hideNativeAppBanner_HACK()};e.componentDidUpdate=function(a){a.deviceOrientation!=null&&a.deviceOrientation!==this.props.deviceOrientation&&d("PolarisHideNativeAppBanner").hideNativeAppBanner_HACK()};e.render=function(){var a=this.props,b=a.children;a=a.xstyle;var e=d("PolarisUA").isMobile()&&new URLSearchParams(location.search).get(j)!=null&&c("gkx")("9007");return i.jsxs(c("PolarisBaseShell.react"),{xstyle:[k.shell,a],children:[b,i.jsx(c("PolarisToastWrapper.react"),{}),e&&i.jsx(c("PolarisAppStoreLauncher.react"),{})]})};return b}(i.Component);g["default"]=a}),98);
__d("PolarisFullscreenShellWithLogo.react",["fbt","ix","IGCoreImage.react","IGDSIconButton.react","IGDSXPanoFilledIcon.react","PolarisFastLink.react","PolarisFullscreenShell.react","PolarisGenericStrings","XPolarisFeedControllerRouteBuilder","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h,i){"use strict";var j,k=j||d("react"),l={shell:{backgroundColor:"x5qyhuo",height:"x5yr21d",justifyContent:"xl56j7k",overflowX:"x6ikm8r",overflowY:"x10wlt62",position:"x1n2onr6",width:"xh8yej3",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(10),e=a.children;a=a.onClose;var f;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(f=c("XPolarisFeedControllerRouteBuilder").buildURL({}),b[0]=f):f=b[0];f=f;var g;b[1]===Symbol["for"]("react.memo_cache_sentinel")?(g={className:"xjbqb8w x1ypdohk xw7yly9 x1ys307a x1yztbdb xyqm7xq x10l6tqk x13vifvy x1o0tod"},b[1]=g):g=b[1];b[2]===Symbol["for"]("react.memo_cache_sentinel")?(g=k.jsx("div",babelHelpers["extends"]({},g,{children:k.jsx(c("PolarisFastLink.react"),{href:f,children:k.jsx(c("IGCoreImage.react"),{alt:h._(/*BTDS*/"Instagram"),src:{light:i("163077")}})})})),f={className:"xjbqb8w x1ypdohk xw7yly9 x1ys307a x1yztbdb xyqm7xq x10l6tqk x13vifvy xtijo5x"},b[2]=g,b[3]=f):(g=b[2],f=b[3]);var j;b[4]===Symbol["for"]("react.memo_cache_sentinel")?(j=k.jsx(c("IGDSXPanoFilledIcon.react"),{alt:d("PolarisGenericStrings").CLOSE_TEXT,color:"web-always-white"}),b[4]=j):j=b[4];b[5]!==a?(f=k.jsx("div",babelHelpers["extends"]({},f,{children:k.jsx(c("IGDSIconButton.react"),{onClick:a,children:j})})),b[5]=a,b[6]=f):f=b[6];b[7]!==e||b[8]!==f?(j=k.jsxs(c("PolarisFullscreenShell.react"),{xstyle:l.shell,children:[e,g,f]}),b[7]=e,b[8]=f,b[9]=j):j=b[9];return j}g["default"]=a}),226);
__d("PolarisGetStoryMediaLayout",[],(function(a,b,c,d,e,f){"use strict";var g=1.3,h=1;function a(a,b,c,d){if(a==null)return[c,d];b=b?h:g;c=c;d=d;var e=c/d;a=a.width/a.height;e/a>b||a/e>b?e>a?c=d*a:d=c/a:e>a?d=c/a:c=d*a;return[d,c]}f.getStoryMediaLayout=a}),66);
__d("PolarisSponsoredTextWidthUtils",["memoize"],(function(a,b,c,d,e,f,g){"use strict";var h=13,i="",j=c("memoize")(function(){var a=document.createElement("canvas");return a.getContext("2d")});function a(a,b,c,d){var e=0,f=a.length-1;while(e<=f){var g=Math.floor((e+f)/2),h=a.slice(0,g+1).join(i);k(h,b,c)<=d?e=g+1:f=g-1}return e}function k(a,b,c){var d=j();if(d==null)return h;d.font=b;b=c!=null?a.replace(" ","").length*c:0;c=d.measureText(a);return c.width+b}g.getMaxWordIndexForWidth=a;g.getTextWidthForAdPreviewCaption=k}),98);
__d("PolarisSponsoredStoryCaptionUtils",["Locale","PolarisSponsoredTextWidthUtils","UnicodeBidi","isStringNullOrEmpty"],(function(a,b,c,d,e,f,g){"use strict";var h=3e3,i="",j=80;function a(a,b){a=(a-b)/2;b=a-j;a=Math.max((a-j)/2,0);var c=a/2;return{captionOffset:c,maxCaptionHeight:b,mediaOffset:a}}function b(a){a=d("UnicodeBidi").isDirectionRTL(a);var b=d("Locale").isRTL(),c="";a&&!b?c="rtl":!a&&b&&(c="ltr");return c}function e(a,b,c,e,f,g,j){var k=[];b=b+"px "+c;c=[];var l=0,m=0;j!=null&&(b=j+" "+b);while(l<a.length||c.length>0){m++;if(m>h)throw new Error("something went wrong while rendering caption. please report this issue.");j=void 0;c.length>0?j=c:j=a[l];if(j.length===0){k.push("");l++;continue}var n=l===0&&c.length===0&&g!=null?f-g:f,o=d("PolarisSponsoredTextWidthUtils").getMaxWordIndexForWidth(j,b,e,n);if(o===0){n=d("PolarisSponsoredTextWidthUtils").getMaxWordIndexForWidth(Array.from(j[0]),b,e,n);var p=j[0].slice(0,n);k.push(p);n<j[0].length?c=[j[0].slice(n)].concat(j.slice(1,j.length)):c=j.slice(1,j.length)}else{p=j.slice(0,o).join(i);k.push(p);c=j.slice(o)}if(c.length>0)continue;l++}return k}function f(a){a=a.split(l).filter(function(a){return!c("isStringNullOrEmpty")(a)});var b=[];a.forEach(function(a){if(!a.startsWith("@")&&!a.startsWith("#")){var d=a.split(k).filter(function(a){return!c("isStringNullOrEmpty")(a)});d.forEach(function(a){return b.push(a)})}else b.push(a)});return b}var k=/((?:[0-9\xB2\xB3\xB9\xBC-\xBE\u0660-\u0669\u06F0-\u06F9\u07C0-\u07C9\u0966-\u096F\u09E6-\u09EF\u09F4-\u09F9\u0A66-\u0A6F\u0AE6-\u0AEF\u0B66-\u0B6F\u0B72-\u0B77\u0BE6-\u0BF2\u0C66-\u0C6F\u0C78-\u0C7E\u0CE6-\u0CEF\u0D58-\u0D5E\u0D66-\u0D78\u0DE6-\u0DEF\u0E50-\u0E59\u0ED0-\u0ED9\u0F20-\u0F33\u1040-\u1049\u1090-\u1099\u1369-\u137C\u16EE-\u16F0\u17E0-\u17E9\u17F0-\u17F9\u1810-\u1819\u1946-\u194F\u19D0-\u19DA\u1A80-\u1A89\u1A90-\u1A99\u1B50-\u1B59\u1BB0-\u1BB9\u1C40-\u1C49\u1C50-\u1C59\u2070\u2074-\u2079\u2080-\u2089\u2150-\u2182\u2185-\u2189\u2460-\u249B\u24EA-\u24FF\u2776-\u2793\u2CFD\u3007\u3021-\u3029\u3038-\u303A\u3192-\u3195\u3220-\u3229\u3248-\u324F\u3280-\u3289\u32B1-\u32BF\uA620-\uA629\uA6E6-\uA6EF\uA830-\uA835\uA8D0-\uA8D9\uA900-\uA909\uA9D0-\uA9D9\uA9F0-\uA9F9\uAA50-\uAA59\uABF0-\uABF9\uFF10-\uFF19]|\uD800[\uDD07-\uDD33\uDD40-\uDD78\uDD8A\uDD8B\uDEE1-\uDEFB\uDF20-\uDF23\uDF41\uDF4A\uDFD1-\uDFD5]|\uD801[\uDCA0-\uDCA9]|\uD802[\uDC58-\uDC5F\uDC79-\uDC7F\uDCA7-\uDCAF\uDCFB-\uDCFF\uDD16-\uDD1B\uDDBC\uDDBD\uDDC0-\uDDCF\uDDD2-\uDDFF\uDE40-\uDE48\uDE7D\uDE7E\uDE9D-\uDE9F\uDEEB-\uDEEF\uDF58-\uDF5F\uDF78-\uDF7F\uDFA9-\uDFAF]|\uD803[\uDCFA-\uDCFF\uDD30-\uDD39\uDE60-\uDE7E\uDF1D-\uDF26\uDF51-\uDF54]|\uD804[\uDC52-\uDC6F\uDCF0-\uDCF9\uDD36-\uDD3F\uDDD0-\uDDD9\uDDE1-\uDDF4\uDEF0-\uDEF9]|\uD805[\uDC50-\uDC59\uDCD0-\uDCD9\uDE50-\uDE59\uDEC0-\uDEC9\uDF30-\uDF3B]|\uD807[\uDC50-\uDC6C\uDD50-\uDD59\uDDA0-\uDDA9\uDFC0-\uDFD4]|\uD809[\uDC00-\uDC6E]|\uD81A[\uDE60-\uDE69\uDF50-\uDF59\uDF5B-\uDF61]|\uD81B[\uDE80-\uDE96]|\uD834[\uDEE0-\uDEF3\uDF60-\uDF78]|\uD835[\uDFCE-\uDFFF]|\uD838[\uDD40-\uDD49\uDEF0-\uDEF9]|\uD83A[\uDCC7-\uDCCF\uDD50-\uDD59]|\uD83B[\uDC71-\uDCAB\uDCAD-\uDCAF\uDCB1-\uDCB4\uDD01-\uDD2D\uDD2F-\uDD3D]|\uD83C[\uDD00-\uDD0C])+|(?:(?:[\u0E01-\u0E3A\u0E40-\u0E5B\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u1000-\u109F\u1780-\u17DD\u17E0-\u17E9\u17F0-\u17F9\u19E0-\u19FF\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u3005\u3007\u3021-\u3029\u3038-\u303B\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FD-\u30FF\u31F0-\u31FF\u32D0-\u32FE\u3300-\u3357\u3400-\u4DB5\u4E00-\u9FEF\uA9E0-\uA9FE\uAA60-\uAA7F\uF900-\uFA6D\uFA70-\uFAD9\uFF66-\uFF6F\uFF71-\uFF9D]|\uD82C[\uDC00-\uDD1E\uDD50-\uDD52\uDD64-\uDD67]|\uD83C\uDE00|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D])(?:[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]|\uD800[\uDDFD\uDEE0\uDF76-\uDF7A]|\uD802[\uDE01-\uDE03\uDE05\uDE06\uDE0C-\uDE0F\uDE38-\uDE3A\uDE3F\uDEE5\uDEE6]|\uD803[\uDD24-\uDD27]|\uD804[\uDC00-\uDC02\uDC38-\uDC46\uDC7F-\uDC82\uDCB0-\uDCBA\uDD00-\uDD02\uDD27-\uDD34\uDD45\uDD46\uDD73\uDD80-\uDD82\uDDB3-\uDDC0\uDDC9-\uDDCC\uDE2C-\uDE37\uDE3E\uDEDF-\uDEEA\uDF00-\uDF03\uDF3B\uDF3C\uDF3E-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF57\uDF62\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC35-\uDC46\uDC5E\uDCB0-\uDCC3\uDDAF-\uDDB5\uDDB8-\uDDC0\uDDDC\uDDDD\uDE30-\uDE40\uDEAB-\uDEB7\uDF1D-\uDF2B]|\uD806[\uDC2C-\uDC3A\uDDD1-\uDDD7\uDDDA-\uDDE0\uDDE4\uDE01-\uDE0A\uDE33-\uDE39\uDE3B-\uDE3E\uDE47\uDE51-\uDE5B\uDE8A-\uDE99]|\uD807[\uDC2F-\uDC36\uDC38-\uDC3F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD31-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD45\uDD47\uDD8A-\uDD8E\uDD90\uDD91\uDD93-\uDD97\uDEF3-\uDEF6]|\uD81A[\uDEF0-\uDEF4\uDF30-\uDF36]|\uD81B[\uDF4F\uDF51-\uDF87]|\uD82F[\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A\uDD30-\uDD36\uDEEC-\uDEEF]|\uD83A[\uDCD0-\uDCD6\uDD44-\uDD4A]|\uDB40[\uDD00-\uDDEF])*))/,l=/([\t-\r \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF]+)|(\B@[0-9A-Z_a-z]+(?:\.(?:[\0-\t\x0B\f\x0E-\u2027\u202A-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])[0-9A-Z_a-z]+)*)|(&?#(?:\\w)+)/;g.getSponsoredStoryCaptionMediaValues=a;g.getTextDirection=b;g.transformWordsListToInlineSentences=e;g.tokenizeCaption=f}),98);
__d("PolarisSponsoredStoryCaption.react",["fbt","IGDSThemeConstantsHelpers","PolarisAdsGatingHelpers","PolarisPostImpressionTrackingNode.react","PolarisSponsoredStoryCaptionUtils","PolarisSponsoredTextWidthUtils","filterNulls","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||(i=d("react")),k=i.useRef,l=" \u2026 ",m="",n="#FFF",o="#000",p=15,q=.5,r=1,s=350,t=120,u=150,v=h._(/*BTDS*/"more");function w(a){var b=d("react-compiler-runtime").c(24),c=a.captionStyle,e=a.inlineSentencesList,f=a.maxLines,g=a.maxWidth;a=a.onMoreClick;var h;b[0]!==c?(h=babelHelpers["extends"]({},c,{background:"none",border:"none",cursor:"pointer",opacity:.8,padding:0,position:"relative",textDecoration:"underline",zIndex:100}),b[0]=c,b[1]=h):h=b[1];h=h;var i=c.fontSize+"px "+c.fontFamily;if(b[2]!==c.letterSpacing||b[3]!==i||b[4]!==e||b[5]!==f||b[6]!==g){var k=[];f>0&&e.length>f&&(k=e[f-1].split(m));var n=d("PolarisSponsoredTextWidthUtils").getTextWidthForAdPreviewCaption(l+v.toString(),i,c.letterSpacing);n=d("PolarisSponsoredTextWidthUtils").getMaxWordIndexForWidth(k,i,c.letterSpacing,g-n);k=k.slice(0,n).join(m);b[2]=c.letterSpacing;b[3]=i;b[4]=e;b[5]=f;b[6]=g;b[7]=k}else k=b[7];n=k;if(b[8]!==c||b[9]!==e||b[10]!==f){b[12]!==c?(i=function(a,b){return j.jsxs("span",{style:c,children:[a,j.jsx("br",{})]},b)},b[12]=c,b[13]=i):i=b[13];g=e.slice(0,f-1).map(i);b[8]=c;b[9]=e;b[10]=f;b[11]=g}else g=b[11];b[14]!==h||b[15]!==a?(k=j.jsx("button",{onClick:a,style:h,children:v}),b[14]=h,b[15]=a,b[16]=k):k=b[16];b[17]!==c||b[18]!==n||b[19]!==k?(i=j.jsxs("span",{style:c,children:[n,l,k]},"lastCaption"),b[17]=c,b[18]=n,b[19]=k,b[20]=i):i=b[20];b[21]!==g||b[22]!==i?(e=j.jsxs("span",{children:[g,i]}),b[21]=g,b[22]=i,b[23]=e):e=b[23];return e}function x(a,b){b=b*1.4;a=a!=null?Math.floor(a/b):r;return a<=0?1:a}function a(a){var b=d("react-compiler-runtime").c(12),e=a.caption,f=a.maxCaptionHeight,g=a.maxCaptionWidth,h=a.onMoreClick,i=a.textBackgroundColor,l=a.textBackgroundColorAlpha,m=a.textColor;a=a.textSize;i=i!=null&&l!=null?i+l:o;l=m!=null?m:n;m=a!=null?a:p;a=g>u?g-t:s;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(g=d("IGDSThemeConstantsHelpers").getNumericValue("font-weight-system-bold"),b[0]=g):g=b[0];g=g;f=x(f,m);b[1]!==i||b[2]!==l||b[3]!==m?(g={backgroundColor:i,borderRadius:"4px",color:l,fontFamily:"Tahoma, Helvetica, Arial, sans-serif",fontSize:m,fontWeight:g,letterSpacing:q,lineHeight:"140%",padding:"2px 2px 5px 2px"},b[1]=i,b[2]=l,b[3]=m,b[4]=g):g=b[4];var r=g;i=e.split("\n").map(d("PolarisSponsoredStoryCaptionUtils").tokenizeCaption);l=c("filterNulls")(d("PolarisSponsoredStoryCaptionUtils").transformWordsListToInlineSentences(i,m,"Tahoma, Helvetica, Arial, sans-serif",q,a));e=d("PolarisSponsoredStoryCaptionUtils").getTextDirection((g=l[0])!=null?g:"");i=l.length>f;m=k(null);b[5]===Symbol["for"]("react.memo_cache_sentinel")?(g={className:"x1cnzs8 x5zjp28 xx6bls6 x162tt16 x1n2onr6"},b[5]=g):g=b[5];var v="sponsored-story-caption";v=i?j.jsx(w,{captionStyle:r,inlineSentencesList:l,maxLines:f,maxWidth:a,onMoreClick:h}):l.map(function(a,b){return j.jsxs("span",{style:r,children:[a,j.jsx("br",{})]},b)});b[6]!==g||b[7]!==v||b[8]!==e?(i=j.jsx("div",babelHelpers["extends"]({},g,{"data-testid":void 0,dir:e,children:v})),b[6]=g,b[7]=v,b[8]=e,b[9]=i):i=b[9];f=i;if(d("PolarisAdsGatingHelpers").enableStoryPostImpressionLogging()){b[10]!==f?(a=j.jsx(c("PolarisPostImpressionTrackingNode.react"),{childrenRef:m,componentType:"text",elementId:"captionText",trackingNode:"CAPTION",children:j.jsx("div",{ref:m,children:f})}),b[10]=f,b[11]=a):a=b[11];return a}return f}g["default"]=a}),226);
__d("PolarisSponsoredStoryLoggingConstants",[],(function(a,b,c,d,e,f){"use strict";a=2e3;b=6e4;f.ADBLOCK_CHECK_DELAY=a;f.SUB_IMPRESSION_GAP=b}),66);
__d("PolarisStoriesImmersiveReshareAttribution.react",["fbt","IGDSResharePanoOutlineIcon.react","IGDSTextVariants.react","PolarisFastLink.react","PolarisUserAvatar.react","XPolarisProfileControllerRouteBuilder","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react"),k={avatar:{marginInlineStart:"xdwrcjd",$$css:!0},root:{alignItems:"x6s0dn4",display:"x78zum5",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(12),e=a.profilePicUrl;a=a.username;var f;b[0]!==a?(f=c("XPolarisProfileControllerRouteBuilder").buildUri({username:a}).toString(),b[0]=a,b[1]=f):f=b[1];f=f;var g;b[2]===Symbol["for"]("react.memo_cache_sentinel")?(g=j.jsx(c("IGDSResharePanoOutlineIcon.react"),{alt:h._(/*BTDS*/"Reshare icon"),color:"ig-stroke-on-media",size:12}),b[2]=g):g=b[2];var i;b[3]!==e?(i=j.jsx(c("PolarisUserAvatar.react"),{isLink:!1,profilePictureUrl:e,size:14,xstyle:k.avatar}),b[3]=e,b[4]=i):i=b[4];b[5]===Symbol["for"]("react.memo_cache_sentinel")?(e={className:"xdwrcjd"},b[5]=e):e=b[5];b[6]!==a?(e=j.jsx("div",babelHelpers["extends"]({},e,{children:j.jsx(d("IGDSTextVariants.react").IGDSTextFootnote,{color:"textOnMedia",children:a})})),b[6]=a,b[7]=e):e=b[7];b[8]!==i||b[9]!==e||b[10]!==f?(a=j.jsxs(c("PolarisFastLink.react"),{"data-testid":void 0,href:f,xstyle:k.root,children:[g,i,e]}),b[8]=i,b[9]=e,b[10]=f,b[11]=a):a=b[11];return a}g["default"]=a}),226);
__d("PolarisStoryConstants",["IGDSThemeConstantsHelpers"],(function(a,b,c,d,e,f,g){"use strict";a=d("IGDSThemeConstantsHelpers").getNumericValue("story-progressbar-update-tick");b=a*1e3;c=5;e=82;f=26;g.STORY_UPDATE_TICK_S=a;g.STORY_UPDATE_TICK_MS=b;g.STORY_IMAGE_DURATION_S=c;g.STORY_DESKTOP_MARGIN_TOP_PX=e;g.STORY_DESKTOP_MARGIN_BOTTOM_PX=f}),98);
__d("PolarisStoryFooterCTA.react",["CometPressable.react","IGDSBox.react","IGDSChevronIcon.react","IGDSTextVariants.react","PolarisExternalLink.react","PolarisGenericStrings","PolarisIGTheme.react","PolarisUA","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(44),e=a.color,f=a.externalURL,g=a.icon,h=a.isActive,j=a.onClick,k=a.showChevron,l=a.text,m=a.textAlign;a=a.textThemedColor;k=k===void 0?!1:k;m=m===void 0?"center":m;var n=d("PolarisIGTheme.react").useTheme(),o=g!=null,p;b[0]!==g||b[1]!==o?(p=o?i.jsxs(i.Fragment,{children:[i.jsx(c("IGDSBox.react"),{flex:"none",position:"relative",children:g}),i.jsx(c("IGDSBox.react"),{marginEnd:3,position:"relative"})]}):null,b[0]=g,b[1]=o,b[2]=p):p=b[2];g=p;b[3]!==k||b[4]!==a?(p=k?i.jsxs(i.Fragment,{children:[i.jsx(c("IGDSBox.react"),{flex:"grow",height:"100%",marginEnd:7,position:"relative",right:!0}),i.jsx(c("IGDSChevronIcon.react"),{alt:d("PolarisGenericStrings").RIGHT_CHEVRON,color:a!=null?a:"ig-tertiary-icon",direction:"next",size:16})]}):null,b[3]=k,b[4]=a,b[5]=p):p=b[5];k=p;b[6]===Symbol["for"]("react.memo_cache_sentinel")?(p={fontSize:12,lineHeight:"16px"},b[6]=p):p=b[6];b[7]!==l?(p=i.jsx("span",{style:p,children:l}),b[7]=l,b[8]=p):p=b[8];p=p;n=n.getTheme()===d("PolarisIGTheme.react").IGTheme.Dark?a==null?void 0:a.dark:a==null?void 0:a.light;if(n!=null){b[9]!==n?(a={color:n,fontSize:12,lineHeight:"16px"},b[9]=n,b[10]=a):a=b[10];b[11]!==a||b[12]!==l?(n=i.jsx("span",{style:a,children:l}),b[11]=a,b[12]=l,b[13]=n):n=b[13];p=n}b[14]!==e||b[15]!==m||b[16]!==p?(a=i.jsx(c("IGDSBox.react"),{flex:"shrink",position:"relative",children:i.jsx(d("IGDSTextVariants.react").IGDSTextBodyEmphasized,{color:e,textAlign:m,children:p})}),b[14]=e,b[15]=m,b[16]=p,b[17]=a):a=b[17];b[18]!==k||b[19]!==g||b[20]!==a?(l=i.jsxs(c("IGDSBox.react"),{alignItems:"center",direction:"row",justifyContent:"start",position:"relative",width:"100%",children:[g,a,k]}),b[18]=k,b[19]=g,b[20]=a,b[21]=l):l=b[21];n=l;if(f!=null){b[22]!==n||b[23]!==f||b[24]!==j?(e=i.jsx(c("PolarisExternalLink.react"),{href:f,onClick:j,children:n}),b[22]=n,b[23]=f,b[24]=j,b[25]=e):e=b[25];n=e}else if(j!=null){b[26]!==n||b[27]!==j?(m=i.jsx(c("CometPressable.react"),{expanding:!0,onPress:j,overlayDisabled:!0,children:n}),b[26]=n,b[27]=j,b[28]=m):m=b[28];n=m}b[29]===Symbol["for"]("react.memo_cache_sentinel")?(p=d("PolarisUA").isMobile(),b[29]=p):p=b[29];k=p;b[30]!==h?(g={0:{className:"x1qjc9v5 xvbhtw8 x1gx9kpi xk39tu8 xoeu3cm x10w6328 x1ey2m1c x1ro42cg x9f619 x78zum5 xdt5ytf xl56j7k x6ikm8r x10wlt62 x10l6tqk"},1:{className:"x1qjc9v5 xvbhtw8 x1gx9kpi xk39tu8 xoeu3cm x10w6328 x1ey2m1c x1ro42cg x9f619 x78zum5 xdt5ytf xl56j7k x6ikm8r x10wlt62 x10l6tqk xc26acl xt7dq6l x1v2xrh1 xtuq3kr xjhpzjm xvc5jky x12rq6i6 xktia5q"}}[!!h<<0],b[30]=h,b[31]=g):g=b[31];b[32]!==h||b[33]!==o?(a={0:{className:"xexx8yu xyri2b x18d9i69 x1c1uobl"},2:{className:"x1pg5gke xz9dl7a xpdmqnj xsag5q8 x13jy36j"},1:{className:"xz9dl7a xpdmqnj xsag5q8 xf7dkkf"},3:{className:"x1pg5gke xz9dl7a xpdmqnj xsag5q8 xf7dkkf"}}[!!h<<1|!!o<<0],b[32]=h,b[33]=o,b[34]=a):a=b[34];b[35]!==n||b[36]!==a?(l=i.jsx("div",babelHelpers["extends"]({},a,{children:n})),b[35]=n,b[36]=a,b[37]=l):l=b[37];b[38]!==h?(f=k&&i.jsx("div",babelHelpers["extends"]({className:"xexx8yu x1pic42t x18d9i69 x1onr9mi xh8yej3"},{children:i.jsx("div",babelHelpers["extends"]({},{0:{},1:{className:"xh05dso x1q0q8m5 xso031l xh8yej3"}}[!!h<<0]))})),b[38]=h,b[39]=f):f=b[39];b[40]!==l||b[41]!==f||b[42]!==g?(e=i.jsxs("div",babelHelpers["extends"]({},g,{children:[l,f]})),b[40]=l,b[41]=f,b[42]=g,b[43]=e):e=b[43];return e}g["default"]=a}),98);
__d("PolarisStoryMediaOverlayInfoFooterCTA.react",["fbt","CometPlaceholder.react","IGDSInfoPanoOutlineIcon.react","IGDSMisinformationPanoOutline24Icon.react","IGDSWarningPanoOutline24Icon.react","JSResourceForInteraction","PolarisInformTreatmentBanner.react","PolarisInformTreatmentDialogRootEntrypoint.entrypoint","PolarisInformTreatmentSensitivityType","PolarisInstagramMediaOverlayFalcoEvent","PolarisMediaOverlayInfoTypes","PolarisMisinformationConstants","PolarisStoryFooterCTA.react","gkx","justknobx","lazyLoadComponent","react","react-compiler-runtime","useIGDSEntryPointDialog","useIGDSLazyDialog"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||(i=d("react"));b=i;var k=b.useEffect,l=b.useMemo,m=b.useRef,n=b.useState,o=c("lazyLoadComponent")(c("JSResourceForInteraction")("IGWebBloksApp").__setRef("PolarisStoryMediaOverlayInfoFooterCTA.react")),p=c("JSResourceForInteraction")("PolarisInformTreatmentSensitivityDetailsDialog.react").__setRef("PolarisStoryMediaOverlayInfoFooterCTA.react");function q(a){var b=d("react-compiler-runtime").c(8);a=a.bannerInfo;var e=a.bannerButton,f=a.iconAltText;a=a.iconGlyph;e=e.text_color;var g;b[0]!==f?(g=f!=null?h._(/*BTDS*/"{text}",[h._param("text",f)]):d("PolarisMisinformationConstants").POST_FOOTER_CTA_ALT_TEXT,b[0]=f,b[1]=g):g=b[1];f=g;if(a===d("PolarisMediaOverlayInfoTypes").MEDIA_OVERLAY_ICON_GLYPHS.INFO){g=e!=null?e:"ig-primary-icon";var i;b[2]!==f||b[3]!==g?(i=j.jsx(c("IGDSInfoPanoOutlineIcon.react"),{alt:f,color:g,size:16}),b[2]=f,b[3]=g,b[4]=i):i=b[4];return i}else if(a===d("PolarisMediaOverlayInfoTypes").MEDIA_OVERLAY_ICON_GLYPHS.WARNING){b[5]!==f||b[6]!==e?(g=c("justknobx")._("2450")?j.jsx(c("IGDSMisinformationPanoOutline24Icon.react"),{alt:f,color:e!=null?e:"ig-primary-icon",size:24}):j.jsx(c("IGDSWarningPanoOutline24Icon.react"),{alt:f,color:"ig-error-or-destructive",size:16}),b[5]=f,b[6]=e,b[7]=g):g=b[7];return g}return null}function a(a){var b=n(!1),e=b[0],f=b[1];b=n(0);var g=b[0],h=b[1];b=a.bannerInfo;var i=a.entityID,m=a.onCloseBloksApp,s=a.onOpenBloksApp,t=a.trayEntrypoint,u=a.viewerIsOwner,v=b.animationInfo,w=b.bannerButton,x=b.overlayLayout,y=b.overlayType;b=b.text;var z=v.isHidden,A=w.action,B=w.action_url;v=w.has_chevron;var C=w.is_text_centered,D=w.secondary_text,E=w.text_color,F=A===d("PolarisMediaOverlayInfoTypes").MEDIA_OVERLAY_BUTTON_ACTIONS.OPEN_EXTERNAL_URL?B:void 0,G=r(a),H=G!==i;G=c("useIGDSEntryPointDialog")(c("PolarisInformTreatmentDialogRootEntrypoint.entrypoint"),{media_id:i});var I=G[0];G=c("useIGDSLazyDialog")(p);var J=G[0];k(function(){(H||e===!1)&&z!==!0?f(!0):H&&f(!1)},[z,H,e,f]);var K=function(){if(A===d("PolarisMediaOverlayInfoTypes").MEDIA_OVERLAY_BUTTON_ACTIONS.NO_OP)return;L();A===d("PolarisMediaOverlayInfoTypes").MEDIA_OVERLAY_BUTTON_ACTIONS.OPEN_BLOKS_APP&&(s==null?void 0:s());h(g+1)},L=function(){d("PolarisInstagramMediaOverlayFalcoEvent").PolarisInstagramMediaOverlayFalcoEvent.log(function(){return d("PolarisInstagramMediaOverlayFalcoEvent").PolarisInstagramMediaOverlayFalcoEvent.buildPayloadForLog({button:w,containerModule:d("PolarisInstagramMediaOverlayFalcoEvent").PolarisInstagramMediaOverlayFalcoEvent.getLoggableContainerModuleFromAnalyticsContext(t),entityID:i,event:d("PolarisInstagramMediaOverlayFalcoEvent").IG_MEDIA_OVERLAY_FALCO_CLIENT_EVENTS.CLICK,overlayLayout:x,overlayType:y})})};G=function(){if(A!==d("PolarisMediaOverlayInfoTypes").MEDIA_OVERLAY_BUTTON_ACTIONS.NO_OP&&B!=null){L();switch(B){case"com.instagram.misinformation.fact_check_sheet.action":I({});break;case"com.bloks.www.instagram.igwb.yci.sensitive_resource_bottomsheet":case"com.instagram.sensitivity.see_why_sheets.sensitivity_sheet_action":J({sensitivityType:d("PolarisInformTreatmentSensitivityType").SensitivityType.SENSITIVE});break;case"com.instagram.sensitivity.see_why_sheets.violence_sheet_action":J({sensitivityType:d("PolarisInformTreatmentSensitivityType").SensitivityType.VIOLENCE});break;default:K()}}};var M=l(function(){return{media_id:i,module:t||""}},[i,t]);return j.jsxs(j.Fragment,{children:[c("gkx")("5268")?j.jsx(c("PolarisInformTreatmentBanner.react"),{externalURL:F,isStory:!0,onClick:G,secondaryText:D,text:b,viewerIsOwner:u}):j.jsx(c("PolarisStoryFooterCTA.react"),{externalURL:F,icon:q(a),isActive:!z,onClick:K,showChevron:v!=null?v:!1,text:b,textAlign:C===!0?"center":"start",textThemedColor:E}),A===d("PolarisMediaOverlayInfoTypes").MEDIA_OVERLAY_BUTTON_ACTIONS.OPEN_BLOKS_APP&&g>0&&B!=null&&j.jsx(c("CometPlaceholder.react"),{fallback:null,children:j.jsx(o,{appId:B,bridgeOverrides_DEPRECATED:{onResumeStoryPlayback:function(){m==null?void 0:m(),h(0)}},params:M},g)})]})}a.displayName=a.name+" [from "+f.id+"]";function r(a){var b=a.entityID,c=m();k(function(){c.current=b},void 0);return c.current}g["default"]=a}),226);
__d("PolarisStoryReplyReaction.react",["fbt","CometPressable.react","FocusGroup.react","IGDSBox.react","IGDSText.react","MessengerWebUXLogger","PolarisEmojiHelper","PolarisUA","focusScopeQueries","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react"),k=h._(/*BTDS*/"Quick Reactions");b=51;e=8;var l=(b+2*e)*4,m=d("PolarisEmojiHelper").emojiListFromCodePoint([[128514],[128558],[128525],[128546]]),n=d("PolarisEmojiHelper").emojiListFromCodePoint([[128079],[128293],[127881],[128175]]);f=d("FocusGroup.react").createFocusGroup(d("focusScopeQueries").tabbableScopeQuery);var o=f[0],p=f[1];function q(a){var b=d("react-compiler-runtime").c(11),e=a.emoji,f=a.flowInstanceId,g=a.onReactToStory,h=c("MessengerWebUXLogger").useInteractionLogger();b[0]!==e||b[1]!==f||b[2]!==h||b[3]!==g?(a=function(){g(e),h==null?void 0:h({eventName:"igd_story_reply_reaction_sent",extraData:{isReaction:"true",shareType:"ig_story_reply"},flowInstanceId:f})},b[0]=e,b[1]=f,b[2]=h,b[3]=g,b[4]=a):a=b[4];a=a;var i;b[5]===Symbol["for"]("react.memo_cache_sentinel")?(i={0:{},2:{className:"x13wtedm xo5v014"},1:{className:"xcg35fi xo5v014 x1skpowl x1c2o835 xfczyey xsdrrl"},3:{className:"xcg35fi xo5v014 x1skpowl x1c2o835 xfczyey xsdrrl"}}[!!d("PolarisUA").isMobile()<<1|!!!d("PolarisUA").isMobile()<<0],b[5]=i):i=b[5];b[6]!==e?(i=j.jsx("span",babelHelpers["extends"]({},i,{children:e})),b[6]=e,b[7]=i):i=b[7];var k;b[8]!==a||b[9]!==i?(k=j.jsx(p,{children:j.jsx(c("IGDSBox.react"),{margin:2,position:"relative",children:j.jsx(c("CometPressable.react"),{onPress:a,overlayDisabled:!0,children:i})})}),b[8]=a,b[9]=i,b[10]=k):k=b[10];return k}function a(a){var b=d("react-compiler-runtime").c(18),e=a.flowInstanceId,f=a.onReactToStory;a=a.xstyle;var g=d("PolarisUA").isMobile()?l:void 0,h=d("PolarisUA").isMobile()?17:4,i,p;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(i={className:"x67bb7w"},p=j.jsx(c("IGDSText.react"),{color:"textOnMedia",elementType:"h2",size:"title",textAlign:"center",weight:"semibold",children:k}),b[0]=i,b[1]=p):(i=b[0],p=b[1]);var r;b[2]!==e||b[3]!==f?(r=m.map(function(a){return j.jsx(q,{emoji:a,flowInstanceId:e,onReactToStory:f},a)}),b[2]=e,b[3]=f,b[4]=r):r=b[4];var s;b[5]!==r?(s=j.jsx(c("IGDSBox.react"),{direction:"row",display:"flex",position:"relative",children:r}),b[5]=r,b[6]=s):s=b[6];b[7]!==e||b[8]!==f?(r=n.map(function(a){return j.jsx(q,{emoji:a,flowInstanceId:e,onReactToStory:f},a)}),b[7]=e,b[8]=f,b[9]=r):r=b[9];var t;b[10]!==r?(t=j.jsx(c("IGDSBox.react"),{direction:"row",display:"flex",position:"relative",children:r}),b[10]=r,b[11]=t):t=b[11];b[12]!==s||b[13]!==t?(r=j.jsxs("div",babelHelpers["extends"]({},i,{children:[p,j.jsxs(c("IGDSBox.react"),{direction:"row",display:"flex",justifyContent:"center",marginBottom:h,marginTop:6,position:"relative",width:g,wrap:!0,children:[s,t]})]})),b[12]=s,b[13]=t,b[14]=r):r=b[14];b[15]!==r||b[16]!==a?(i=j.jsx(o,{orientation:"horizontal",tabScopeQuery:d("focusScopeQueries").tabbableScopeQuery,wrap:!0,children:j.jsx(c("IGDSBox.react"),{alignItems:"center",position:"relative",xstyle:a,children:r})}),b[15]=r,b[16]=a,b[17]=i):i=b[17];return i}g["default"]=a}),226);
__d("PolarisStorySensitivityOverlayCoverContents.react",["IGDSBox.react","IGDSText.react","IGDSTextVariants.react","gkx","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={secondaryTextOnMedia:{color:"xtz6rxn",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(10),e=a.centerButtonNode,f=a.children,g=a.description,h=a.icon;a=a.title;var k;b[0]!==g||b[1]!==a?(k=c("gkx")("5268")?i.jsxs(i.Fragment,{children:[i.jsx(c("IGDSBox.react"),{marginBottom:3,marginTop:5,position:"relative",children:i.jsx(c("IGDSText.react"),{color:"webAlwaysWhite",size:"headline2",textAlign:"center",children:a})}),i.jsx(c("IGDSBox.react"),{marginBottom:3,position:"relative",children:i.jsx(c("IGDSBox.react"),{xstyle:j.secondaryTextOnMedia,children:g})})]}):i.jsxs(i.Fragment,{children:[i.jsx(c("IGDSBox.react"),{marginBottom:6,marginTop:7,position:"relative",children:i.jsx(d("IGDSTextVariants.react").IGDSTextHeadline1,{color:"textOnMedia",textAlign:"center",children:a})}),i.jsx(c("IGDSBox.react"),{marginBottom:6,position:"relative",children:i.jsx(d("IGDSTextVariants.react").IGDSTextBody,{color:"textOnMedia",textAlign:"center",children:g})})]}),b[0]=g,b[1]=a,b[2]=k):k=b[2];b[3]!==e?(g=i.jsx(c("IGDSBox.react"),{alignItems:"center",direction:"row",justifyContent:"center",position:"relative",width:"100%",children:e}),b[3]=e,b[4]=g):g=b[4];b[5]!==f||b[6]!==h||b[7]!==k||b[8]!==g?(a=i.jsxs(c("IGDSBox.react"),{alignContent:"center",direction:"column",height:"100%",justifyContent:"center",paddingX:8,position:"relative",children:[h,k,f,g]}),b[5]=f,b[6]=h,b[7]=k,b[8]=g,b[9]=a):a=b[9];return a}g["default"]=a}),98);
__d("usePolarisStorySensitivityOverlayLogger",["PolarisInstagramMediaOverlayFalcoEvent","PolarisLogger","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useCallback;b.useMemo;function i(a){var b=a.button,c=a.containerModule,e=a.customAction,f=a.customSourceOfAction,g=a.entityID,h=a.event;a=a.mediaOverlayCoverInfo;a=a!=null?a:{};var i=a.overlayLayout,j=a.overlayType;d("PolarisInstagramMediaOverlayFalcoEvent").PolarisInstagramMediaOverlayFalcoEvent.log(function(){return d("PolarisInstagramMediaOverlayFalcoEvent").PolarisInstagramMediaOverlayFalcoEvent.buildPayloadForLog({button:b,containerModule:c,customAction:e,customSourceOfAction:f,entityID:g,event:h,overlayLayout:i,overlayType:j})})}function j(a,b){d("PolarisLogger").logGatingEvent_DEPRECATED(a,b)}function a(){var a=d("react-compiler-runtime").c(2),b=n,c=m,e=l,f;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(f=function(a){var c=a.button,d=a.containerModule,e=a.gatingEvent_DEPRECATED,f=a.mediaId;a=a.mediaOverlayCoverInfo;b({button:c,containerModule:d,mediaId:f,mediaOverlayCoverInfo:a});j("reel_instagram_organic_gate_clear_dialog_shown",e)},a[0]=f):f=a[0];f=f;var g=k;a[1]===Symbol["for"]("react.memo_cache_sentinel")?(c={logButtonClick:b,logConfirmationDialogAccept:c,logConfirmationDialogDismiss:e,logConfirmationDialogShow:f,logImpression:g},a[1]=c):c=a[1];e=c;return e}function k(a){var b=a.containerModule,c=a.gatingEvent_DEPRECATED,e=a.mediaId;a=a.mediaOverlayCoverInfo;i({containerModule:b,entityID:e,event:d("PolarisInstagramMediaOverlayFalcoEvent").IG_MEDIA_OVERLAY_FALCO_CLIENT_EVENTS.IMPRESSION,mediaOverlayCoverInfo:a});j("reel_instagram_organic_gate_impression",c)}function l(a){var b=a.containerModule,c=a.mediaId;a=a.mediaOverlayCoverInfo;i({containerModule:b,customAction:"story_see_post_confirmation_dismiss",customSourceOfAction:"see_post_confirmation",entityID:c,event:d("PolarisInstagramMediaOverlayFalcoEvent").IG_MEDIA_OVERLAY_FALCO_CLIENT_EVENTS.ACTION,mediaOverlayCoverInfo:a});j("reel_instagram_organic_gate_clear_dialog_dismiss")}function m(a){var b=a.containerModule,c=a.gatingEvent_DEPRECATED,e=a.mediaId;a=a.mediaOverlayCoverInfo;i({containerModule:b,customAction:"story_see_post_confirmation_continue",customSourceOfAction:"see_post_confirmation",entityID:e,event:d("PolarisInstagramMediaOverlayFalcoEvent").IG_MEDIA_OVERLAY_FALCO_CLIENT_EVENTS.ACTION,mediaOverlayCoverInfo:a});j("reel_instagram_organic_gate_clear_dialog_accepted",c)}function n(a){var b=a.button,c=a.containerModule,e=a.mediaId;a=a.mediaOverlayCoverInfo;i({button:b,containerModule:c,entityID:e,event:d("PolarisInstagramMediaOverlayFalcoEvent").IG_MEDIA_OVERLAY_FALCO_CLIENT_EVENTS.ACTION,mediaOverlayCoverInfo:a})}g["default"]=a}),98);
__d("PolarisStorySensitivityOverlayMediaOverlayInfoCoverContents.react",["JSResourceForInteraction","PolarisInformTreatmentDialogRootEntrypoint.entrypoint","PolarisInformTreatmentSensitivityType","PolarisSensitivityOverlayCenterButton.react","PolarisSensitivityOverlayIcon.react","PolarisStorySensitivityOverlayCoverContents.react","gkx","react","react-compiler-runtime","useIGDSEntryPointDialog","useIGDSLazyDialog","usePolarisStorySensitivityOverlayLogger"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));h.useMemo;var j=c("JSResourceForInteraction")("PolarisInformTreatmentSensitivityDetailsDialog.react").__setRef("PolarisStorySensitivityOverlayMediaOverlayInfoCoverContents.react");function a(a){var b=d("react-compiler-runtime").c(27),e=a.children,f=a.mediaId,g=a.mediaOverlayCoverInfo,h=a.onMediaOverlayInfoButtonClick,k=c("usePolarisStorySensitivityOverlayLogger")(),l=g.centerButton;a=g.description;var m=g.rootIconGlyph,n=g.title,o;b[0]!==f||b[1]!==g?(o={containerModule:"unknown",mediaId:f,mediaOverlayCoverInfo:g},b[0]=f,b[1]=g,b[2]=o):o=b[2];o=o;var p=o;b[3]!==f?(o=f.split("_"),b[3]=f,b[4]=o):o=b[4];f=o[0];b[5]!==f?(o={media_id:f},b[5]=f,b[6]=o):o=b[6];f=c("useIGDSEntryPointDialog")(c("PolarisInformTreatmentDialogRootEntrypoint.entrypoint"),o);var q=f[0];o=c("useIGDSLazyDialog")(j);var r=o[0],s=l==null?void 0:l.action_url;b[7]!==s||b[8]!==l||b[9]!==k||b[10]!==p||b[11]!==q||b[12]!==r?(f=function(){if(s!=null){bb10:switch(s){case"com.instagram.misinformation.fact_check_sheet.action":q({});break bb10;case"com.bloks.www.instagram.igwb.yci.sensitive_resource_bottomsheet":case"com.instagram.sensitivity.see_why_sheets.sensitivity_sheet_action":r({sensitivityType:d("PolarisInformTreatmentSensitivityType").SensitivityType.SENSITIVE});break bb10;case"com.instagram.sensitivity.see_why_sheets.violence_sheet_action":r({sensitivityType:d("PolarisInformTreatmentSensitivityType").SensitivityType.VIOLENCE});break bb10;default:}k.logButtonClick(babelHelpers["extends"]({},p,{button:l}))}},b[7]=s,b[8]=l,b[9]=k,b[10]=p,b[11]=q,b[12]=r,b[13]=f):f=b[13];o=f;b[14]!==l||b[15]!==o||b[16]!==g||b[17]!==h?(f=l!=null?i.jsx(c("PolarisSensitivityOverlayCenterButton.react"),{buttonText:l.text,onClick:c("gkx")("5268")?o:function(){return h(g,l)}}):null,b[14]=l,b[15]=o,b[16]=g,b[17]=h,b[18]=f):f=b[18];o=f;b[19]!==m?(f=i.jsx(c("PolarisSensitivityOverlayIcon.react"),{mediaOverlayIconGlyph:m}),b[19]=m,b[20]=f):f=b[20];m=f;b[21]!==o||b[22]!==e||b[23]!==a||b[24]!==m||b[25]!==n?(f=i.jsx(c("PolarisStorySensitivityOverlayCoverContents.react"),{centerButtonNode:o,description:a,icon:m,title:n,children:e}),b[21]=o,b[22]=e,b[23]=a,b[24]=m,b[25]=n,b[26]=f):f=b[26];return f}g["default"]=a}),98);
__d("PolarisStoryTapToPlay.react",["IGDSBox.react","IGDSButton.react","IGDSText.react","PolarisIGCoreConstants","PolarisStoriesStrings","PolarisUserAvatar.react","react","react-compiler-runtime","usePolarisViewer"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(13),e=a.onTapToPlay;a=a.username;var f=c("usePolarisViewer")(),g=f==null?void 0:f.username;f=f==null?void 0:f.profilePictureUrl;var h;b[0]!==f?(h=f!=null&&i.jsx(c("PolarisUserAvatar.react"),{isLink:!1,profilePictureUrl:f,size:c("PolarisIGCoreConstants").AVATAR_SIZES.XL}),b[0]=f,b[1]=h):h=b[1];b[2]!==g?(f=g!=null&&i.jsx(c("IGDSBox.react"),{marginTop:6,position:"relative",children:i.jsx(c("IGDSText.react"),{color:"webAlwaysWhite",size:"title",textAlign:"center",weight:"semibold",children:d("PolarisStoriesStrings").viewStoryTitleText(g)})}),b[2]=g,b[3]=f):f=b[3];b[4]!==a?(g=a!=null&&i.jsx(c("IGDSBox.react"),{marginTop:4,position:"relative",children:i.jsx(c("IGDSText.react"),{color:"webAlwaysWhite",size:"body",textAlign:"center",children:d("PolarisStoriesStrings").viewStorySubtitleText(a)})}),b[4]=a,b[5]=g):g=b[5];b[6]!==e?(a=i.jsx(c("IGDSBox.react"),{marginTop:8,position:"relative",children:i.jsx(c("IGDSButton.react"),{"data-testid":void 0,display:"block",label:d("PolarisStoriesStrings").VIEW_STORY,onClick:e,size:"large",variant:"white"})}),b[6]=e,b[7]=a):a=b[7];b[8]!==h||b[9]!==f||b[10]!==g||b[11]!==a?(e=i.jsxs(c("IGDSBox.react"),{alignItems:"center",direction:"column",display:"flex",children:[h,f,g,a]}),b[8]=h,b[9]=f,b[10]=g,b[11]=a,b[12]=e):e=b[12];return e}g["default"]=a}),98);
__d("maybeIssueIGDThreadPointQuery",["I64","LSAuthorityLevel","LSFactory","LSIntEnum","LSVerifyThreadRowExistsV2StoredProcedure","asyncToGeneratorRuntime","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=c("requireDeferred")("LSDatabaseSingletonLazyWrapper").__setRef("maybeIssueIGDThreadPointQuery");function a(a){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=(yield j.load());b=(yield b());return b.runInTransaction(function(b){return c("LSVerifyThreadRowExistsV2StoredProcedure")(c("LSFactory")(b),{authorityLevel:(h||(h=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").OPTIMISTIC),syncGroup:(i||(i=d("I64"))).of_int32(1),threadKey:i.of_string(a),threadType:i.of_int32(1)})},"readwrite")});return k.apply(this,arguments)}g["default"]=a}),98);
__d("replyOrReactToStoryViaInstamadillo",["FBLogger","QPLUserFlow","Random","asyncToGeneratorRuntime","promiseDone","qpl","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h=c("requireDeferred")("getSendStoryToInstamadilloRecipient").__setRef("replyOrReactToStoryViaInstamadillo");function a(a,e,f,g,i,j,k,l,m){var n=d("Random").uint32();c("QPLUserFlow").start(c("qpl")._(379192358,"2221"),{annotations:{bool:{is_dm:k,is_instamadillo:!0},string:{action_type:String(i)}},instanceKey:n});if(j==null){c("QPLUserFlow").endFailure(c("qpl")._(379192358,"2221"),"thread key is null");throw c("FBLogger")("igd_web").mustfix("replyOrReactToStoryViaInstamadillo: missing threadKey: storyOwnerId %s, mediaId %s",f,e)}c("promiseDone")(h.load().then(function(){var d=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){b=b(a,e,f,g,i);yield b(j,k,l,m,function(){c("QPLUserFlow").endSuccess(c("qpl")._(379192358,"2221"),{instanceKey:n})},function(a){c("QPLUserFlow").endFailure(c("qpl")._(379192358,"2221"),a.name,{error:a,instanceKey:n})})});return function(a){return d.apply(this,arguments)}}()))}g["default"]=a}),98);
__d("useIGDShareSheetSendStoryToOpenRecipients",["PolarisDirectActionShareStory","PolarisReactRedux.react","react-compiler-runtime","usePolarisMQTT","useStableCallback"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){var e=d("react-compiler-runtime").c(5),f=c("usePolarisMQTT")(),g=d("PolarisReactRedux.react").useDispatch(),h;e[0]!==g||e[1]!==a||e[2]!==f||e[3]!==b?(h=function(c,e,h,i){g(d("PolarisDirectActionShareStory").shareStory(f,c,b,a,e,h,i))},e[0]=g,e[1]=a,e[2]=f,e[3]=b,e[4]=h):h=e[4];return c("useStableCallback")(h)}g["default"]=a}),98);
__d("useIGDShareSheetOnSubmitStory",["IGDDataclassTypes.flow","IGDShareSheetOnSubmitFactory","IGDThreadTTLCUtils","asyncToGeneratorRuntime","react-compiler-runtime","requireDeferred","useIGDShareSheetSendStoryToOpenRecipients","useStableCallback"],(function(a,b,c,d,e,f,g){"use strict";var h=c("requireDeferred")("getSendStoryToInstamadilloRecipient").__setRef("useIGDShareSheetOnSubmitStory");function a(a,e,f,g){var j=d("react-compiler-runtime").c(6),k=a+"_"+f,l=c("useIGDShareSheetSendStoryToOpenRecipients")(k,e);j[0]!==f||j[1]!==g||j[2]!==a||j[3]!==e||j[4]!==l?(k=function(){var j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){for(var b=arguments.length,j=new Array(b),k=0;k<b;k++)j[k]=arguments[k];var m=j,n=(yield h.load());return c("IGDShareSheetOnSubmitFactory")(l,n(e,a,f,g,d("IGDDataclassTypes.flow").XmsgIgXmaActionType.Share),i).apply(void 0,m)});return function(){return j.apply(this,arguments)}}(),j[0]=f,j[1]=g,j[2]=a,j[3]=e,j[4]=l,j[5]=k):k=j[5];return c("useStableCallback")(k)}function i(a){return d("IGDThreadTTLCUtils").isIGDTTLCEnabledForIGDCandidate(a,String("story"))}g["default"]=a}),98);
__d("usePolarisIsTallDevice",["usePolarisDisplayProperties"],(function(a,b,c,d,e,f,g){"use strict";var h=16/9;function a(){var a=c("usePolarisDisplayProperties")(),b=a.viewportHeight;a=a.viewportWidth;return b/a>h}g["default"]=a}),98);
__d("usePolarisSponsoredStoryMediaImpressionLogger",["FBLogger","InstagramAdImpressionFalcoEvent","InstagramAdSubImpressionFalcoEvent","InstagramODS","PolarisNavChain","PolarisSponsoredStoryLoggingConstants","cometGHLContentDisplayCheck","cr:6627","err","qex","react-compiler-runtime","setTimeout","usePartialViewImpression"],(function(a,b,c,d,e,f,g){"use strict";var h=c("qex")._("1229"),i=new Map(),j=new Set();function a(a){var e=d("react-compiler-runtime").c(2),f;e[0]!==a?(f={onImpressionStart:function(e){var f=e.entry;e=a();var g=e.adId,k=e.adInsertedPosition,l=e.containerModule,m=e.currentReelItemIndex,n=e.followStatus,o=e.gapToLastAd,p=e.mediaType,q=e.mpk,r=e.postedAt,s=e.postId,t=e.postOwnerId,u=e.reelId,v=e.reelSize,w=e.reelType,x=e.reelViewerPosition,y=e.sessionReelCounter,z=e.trackingToken,A=e.traySession,B=e.viewerId;e=e.viewerSession;if(g==null||z==null||B==null){var C=c("err")("Missing critical logging field for InstagramAdImpressionFalcoEvent for story media.");c("FBLogger")("ig_web").catching(C);return}var D=i.get(s),E=Date.now(),F={a_pk:t,ad_id:g,ad_inserted_position:k,follow_status:n,gap_to_last_ad:String(o),m_pk:q,m_t:p,m_ts:r,nav_chain:(C=c("PolarisNavChain").getInstance())==null?void 0:C.getNavChainForSend(),pigeon_reserved_keyword_module:l,pk:B,post_id:s,reel_id:u,reel_position:m.toString(),reel_size:v,reel_type:w,reel_viewer_position:x,session_reel_counter:y,source_of_action:l,tracking_token:z,tray_session_id:A,viewer_session_id:e};if(j.has(s))return;j.add(s);c("setTimeout")(function(){var a=c("cometGHLContentDisplayCheck")(f.target);D==null?a?(c("InstagramODS").incr("web.ads.story.media.impression"),c("InstagramAdImpressionFalcoEvent").logImmediately(function(){return F}),h===!0&&b("cr:6627")!=null&&b("cr:6627").log(function(){return{client_token:z,container_module:l,event:"impression_client"}}),i.set(s,E)):c("InstagramODS").incr("web.ads.story.media.impression.hidden"):E-D>=d("PolarisSponsoredStoryLoggingConstants").SUB_IMPRESSION_GAP&&(a?(c("InstagramODS").incr("web.ads.story.media.sub_impression"),c("InstagramAdSubImpressionFalcoEvent").log(function(){return F}),h===!0&&b("cr:6627")!=null&&b("cr:6627").log(function(){return{client_token:z,container_module:l,event:"sub_impression_client"}}),i.set(s,E)):c("InstagramODS").incr("web.ads.story.media.sub_impression.hidden"));j["delete"](s)},d("PolarisSponsoredStoryLoggingConstants").ADBLOCK_CHECK_DELAY)}},e[0]=a,e[1]=f):f=e[1];return c("usePartialViewImpression")(f)}g["default"]=a}),98);
__d("usePolarisSponsoredStoryMediaImpressionSecondChannelLogger",["InstagramAdImpressionSecondChannelFalcoEvent","InstagramAdSubImpressionSecondChannelFalcoEvent","InstagramODS","PolarisContainerModuleUtils","PolarisSponsoredStoryLoggingConstants","cometGHLContentDisplayCheck","cr:6627","qex","react-compiler-runtime","setTimeout","usePolarisAnalyticsContext","usePolarisViewer","useScrollBasedImpressionTracker"],(function(a,b,c,d,e,f,g){"use strict";var h=c("qex")._("1229"),i=new Map(),j=new Set();function a(a){var e=d("react-compiler-runtime").c(11),f=a.adId,g=a.mediaType,k=a.mpk,l=a.postId,m=a.trackingToken,n=c("usePolarisAnalyticsContext")(),o=c("usePolarisViewer")();e[0]!==n?(a=d("PolarisContainerModuleUtils").getContainerModule(n),e[0]=n,e[1]=a):a=e[1];var p=a;e[2]!==f||e[3]!==n||e[4]!==p||e[5]!==g||e[6]!==k||e[7]!==l||e[8]!==m||e[9]!==(o==null?void 0:o.id)?(a=function(a){if(f!=null&&m!=null&&(o==null?void 0:o.id)!=null&&n!=null){var e=i.get(l),q=Date.now(),r={ad_id:f,inventory_source:d("PolarisContainerModuleUtils").getContainerModule(n),m_pk:k,m_t:g,pigeon_reserved_keyword_module:d("PolarisContainerModuleUtils").getContainerModule(n),pk:o==null?void 0:o.id,tracking_token:m};if(a.status!=="ENTER")return;if(j.has(l))return;j.add(l);c("setTimeout")(function(){var f=c("cometGHLContentDisplayCheck")(a.target);e==null?f?(c("InstagramODS").incr("web.ads.story.media.impression.second_channel"),c("InstagramAdImpressionSecondChannelFalcoEvent").logImmediately(function(){return r}),h===!0&&b("cr:6627")!=null&&b("cr:6627").log(function(){return{client_token:m,container_module:p,event:"impression_second_channel_client"}}),i.set(l,q)):c("InstagramODS").incr("web.ads.story.media.impression.second_channel.hidden"):q-e>=d("PolarisSponsoredStoryLoggingConstants").SUB_IMPRESSION_GAP&&(f?(c("InstagramODS").incr("web.ads.story.media.sub_impression.second_channel"),c("InstagramAdSubImpressionSecondChannelFalcoEvent").log(function(){return r}),h===!0&&b("cr:6627")!=null&&b("cr:6627").log(function(){return{client_token:m,container_module:p,event:"sub_impression_second_channel_client"}}),i.set(l,q)):c("InstagramODS").incr("web.ads.story.media.sub_impression.second_channel.hidden"));j["delete"](l)},d("PolarisSponsoredStoryLoggingConstants").ADBLOCK_CHECK_DELAY)}},e[2]=f,e[3]=n,e[4]=p,e[5]=g,e[6]=k,e[7]=l,e[8]=m,e[9]=o==null?void 0:o.id,e[10]=a):a=e[10];return c("useScrollBasedImpressionTracker")(a)}g["default"]=a}),98);
__d("usePolarisSponsoredStoryMediaTimeSpentLogger",["FBLogger","InstagramAdTimeSpentFalcoEvent","InstagramODS","err","react-compiler-runtime","usePartialViewImpression"],(function(a,b,c,d,e,f,g){"use strict";var h=new Map();function a(a){var b=d("react-compiler-runtime").c(2),e;b[0]!==a?(e={onImpressionEnd:function(){var b=a(),d=b.adId,e=b.containerModule,f=b.currentReelItemIndex,g=b.followStatus,i=b.mediaType,j=b.mpk,k=b.postedAt,l=b.postId,m=b.postOwnerId,n=b.reelId,o=b.reelSize,p=b.reelType,q=b.reelViewerPosition,r=b.sessionReelCounter,s=b.trackingToken,t=b.traySession,u=b.viewerId;b=b.viewerSession;if(d==null||s==null||g==null||j==null){var v=c("err")("Missing critical logging field for InstagramAdTimeSpentFalcoEvent.");c("FBLogger")("ig_web").catching(v);return}v=h.get(l);var w=Date.now();if(v==null){var x=c("err")("Missing logs for story media impression on start");c("FBLogger")("ig_web").catching(x);return}var y={a_pk:m,ad_id:d,ad_inserted_position:q,follow_status:g,m_pk:j,m_t:i,m_ts:k,pigeon_reserved_keyword_module:e,pk:u,post_id:l,reel_id:n,reel_position:f.toString(),reel_size:o,reel_type:p,reel_viewer_position:q,session_reel_counter:r,source_of_action:e,timespent:(w-v)/1e3,tracking_token:s,tray_session_id:t,viewer_session_id:b};c("InstagramODS").incr("web.ads.story.media.time_spent.end");c("InstagramAdTimeSpentFalcoEvent").log(function(){return y})},onImpressionStart:function(){var b=a(),d=b.adId,e=b.followStatus,f=b.mpk,g=b.postId,i=b.trackingToken;b=b.viewerId;if(d==null||i==null||e==null||f==null||b==null){d=c("err")("Missing critical logging field for InstagramAdTimeSpentFalcoEvent.");c("FBLogger")("ig_web").catching(d);return}i=Date.now();c("InstagramODS").incr("web.ads.story.media.time_spent.start");h.set(g,i)}},b[0]=a,b[1]=e):e=b[1];return c("usePartialViewImpression")(e)}g["default"]=a}),98);
__d("usePolarisSponsoredStoryMediaViewabilityLogger",["FBLogger","InstagramAdViewabilityFalcoEvent","InstagramODS","err","react-compiler-runtime","usePartialViewImpression"],(function(a,b,c,d,e,f,g){"use strict";var h=new Map();function a(a){var b=d("react-compiler-runtime").c(2),e;b[0]!==a?(e={onImpressionEnd:function(){var b=a(),d=b.adId,e=b.containerModule,f=b.currentReelItemIndex,g=b.followStatus,i=b.mediaType,j=b.mpk,k=b.postedAt,l=b.postId,m=b.postOwnerId,n=b.reelId,o=b.reelSize,p=b.reelType,q=b.reelViewerPosition,r=b.sessionReelCounter,s=b.trackingToken,t=b.traySession,u=b.viewerId;b=b.viewerSession;if(d==null||s==null||u==null){var v=c("err")("Missing critical logging field for InstagramAdViewabilityFalcoEvent.");c("FBLogger")("ig_web").catching(v);return}v=h.get(l);var w=Date.now();if(v==null){var x=c("err")("Missing logs for story media viewability on start");c("FBLogger")("ig_web").catching(x);return}var y={a_pk:m,ad_id:d,ad_inserted_position:q,duration:(w-v)/1e3,follow_status:g,m_pk:j,m_t:i,m_ts:k,pigeon_reserved_keyword_module:e,pk:u,post_id:l,reel_id:n,reel_position:f.toString(),reel_size:o,reel_type:p,reel_viewer_position:q,session_reel_counter:r,source_of_action:e,tracking_token:s!=null?s:"",tray_session_id:t,viewer_session_id:b,visibility_unit:"feed_unit"};c("InstagramODS").incr("web.ads.story.media.viewability.end");c("InstagramAdViewabilityFalcoEvent").log(function(){return y})},onImpressionStart:function(){var b=a();b=b.postId;var d=Date.now();c("InstagramODS").incr("web.ads.story.media.viewability.start");h.set(b,d)}},b[0]=a,b[1]=e):e=b[1];return c("usePartialViewImpression")(e)}g["default"]=a}),98);
__d("usePolarisSponsoredStoryMediaVpvdImpressionLogger",["FBLogger","InstagramAdVpvdImpFalcoEvent","InstagramODS","err","react-compiler-runtime","usePartialViewImpression"],(function(a,b,c,d,e,f,g){"use strict";var h=new Map(),i=new Map(),j=new Map(),k=250;function a(a){var b=d("react-compiler-runtime").c(2),e;b[0]!==a?(e={onImpressionEnd:function(){var b=a(),d=b.adId,e=b.containerModule,f=b.currentReelItemIndex,g=b.followStatus,l=b.mediaType,m=b.mpk,n=b.postedAt,o=b.postId,p=b.postOwnerId,q=b.reelId,r=b.reelSize,s=b.reelType,t=b.reelViewerPosition,u=b.sessionReelCounter,v=b.trackingToken,w=b.traySession,x=b.viewerId;b=b.viewerSession;if(d==null||v==null||m==null){var y=c("err")("Missing critical logging field for InstagramAdVpvdImpFalcoEvent.");c("FBLogger")("ig_web").catching(y);return}y=h.get(o);var z=Date.now();if(y==null){var A=c("err")("Missing logs for story media vpvd on start");c("FBLogger")("ig_web").catching(A);return}A=z-y;if(A<k)return;y=Math.max(A,(z=i.get(o))!=null?z:0);i.set(o,y);z=((z=j.get(o))!=null?z:0)+A;j.set(o,z);var B={a_pk:p,ad_id:d,ad_inserted_position:t,follow_status:g,m_pk:m,m_t:l!=null?l:"",m_ts:n,max_duration_ms:y.toString(),pigeon_reserved_keyword_module:e,pk:x,post_id:o,reel_id:q,reel_position:f.toString(),reel_size:r,reel_type:s,reel_viewer_position:t,session_reel_counter:u,source_of_action:e,sum_duration_ms:z,timespent:A/1e3,tracking_token:v!=null?v:"",tray_session_id:w,viewer_session_id:b};c("InstagramODS").incr("web.ads.story.media.vpvd.end");c("InstagramAdVpvdImpFalcoEvent").log(function(){return B});z>=2e3&&d!=null&&c("InstagramAdVpvdImpFalcoEvent").log(function(){return babelHelpers["extends"]({},B,{module_name:"engaged_view"})})},onImpressionStart:function(){var b=a(),d=b.adId,e=b.mpk,f=b.postId,g=b.trackingToken;b=b.viewerId;if(d==null||g==null||e==null||b==null){d=c("err")("Missing critical logging field for InstagramAdVpvdImpFalcoEvent.");c("FBLogger")("ig_web").catching(d);return}g=Date.now();c("InstagramODS").incr("web.ads.story.media.vpvd.start");h.set(f,g)}},b[0]=a,b[1]=e):e=b[1];return c("usePartialViewImpression")(e)}g["default"]=a}),98);
__d("usePolarisSponsoredStoryReelImpressionLogger",["FBLogger","InstagramAdReelImpressionFalcoEvent","InstagramAdReelSubImpressionFalcoEvent","InstagramODS","PolarisContainerModuleUtils","PolarisNavChain","PolarisSponsoredStoryLoggingConstants","cometGHLContentDisplayCheck","cr:6627","err","qex","react-compiler-runtime","setTimeout","usePartialViewImpression","usePolarisAnalyticsContext","usePolarisViewer"],(function(a,b,c,d,e,f,g){"use strict";var h=c("qex")._("1229"),i=new Map(),j=new Set();function a(a){var e=d("react-compiler-runtime").c(20),f=a.adId,g=a.adInsertedPosition,k=a.followStatus,l=a.mpk,m=a.postedAt,n=a.postId,o=a.postMediaType,p=a.postOwnerId,q=a.reelId,r=a.reelPosition,s=a.reelSize,t=a.reelType,u=a.reelViewerPosition,v=a.sessionReelCounter,w=a.trackingToken,x=a.traySession,y=a.viewerSession,z=c("usePolarisViewer")(),A=c("usePolarisAnalyticsContext")();e[0]!==f||e[1]!==g||e[2]!==A||e[3]!==k||e[4]!==l||e[5]!==n||e[6]!==o||e[7]!==p||e[8]!==m||e[9]!==q||e[10]!==r||e[11]!==s||e[12]!==t||e[13]!==u||e[14]!==v||e[15]!==w||e[16]!==x||e[17]!==(z==null?void 0:z.id)||e[18]!==y?(a={onImpressionStart:function(a){var e=a.entry;if(f==null||w==null||(z==null?void 0:z.id)==null){a=c("err")("Missing critical logging field for InstagramAdReelImpressionFalcoEvent.");c("FBLogger")("ig_web").catching(a);return}var B=i.get(q),C=Date.now(),D=d("PolarisContainerModuleUtils").getContainerModule(A),E={a_pk:p,ad_id:f,ad_inserted_position:g.toString(),follow_status:k,m_pk:l,m_t:o,m_ts:m==null?void 0:m.toString(),nav_chain:(a=c("PolarisNavChain").getInstance())==null?void 0:a.getNavChainForSend(),pigeon_reserved_keyword_module:D,pk:z==null?void 0:z.id,post_id:n,reel_id:q,reel_position:r.toString(),reel_size:s==null?void 0:s.toString(),reel_type:t,reel_viewer_position:u.toString(),session_reel_counter:v.toString(),source_of_action:D,tracking_token:w,tray_session_id:x,viewer_session_id:y};if(j.has(f))return;j.add(f);c("setTimeout")(function(){var a=c("cometGHLContentDisplayCheck")(e.target);B==null?a?(c("InstagramODS").incr("web.ads.story.reel.impression"),c("InstagramAdReelImpressionFalcoEvent").log(function(){return E}),h===!0&&b("cr:6627")!=null&&b("cr:6627").log(function(){return{client_token:w,container_module:D,event:"reel_impression_client"}}),i.set(q,C)):c("InstagramODS").incr("web.ads.story.reel.impression.hidden"):C-B>=d("PolarisSponsoredStoryLoggingConstants").SUB_IMPRESSION_GAP&&(a?(c("InstagramODS").incr("web.ads.story.reel.sub_impression"),c("InstagramAdReelSubImpressionFalcoEvent").log(function(){return E}),h===!0&&b("cr:6627")!=null&&b("cr:6627").log(function(){return{client_token:w,container_module:D,event:"reel_sub_impression_client"}}),i.set(q,C)):c("InstagramODS").incr("web.ads.story.reel.sub_impression.hidden"));j["delete"](f)},d("PolarisSponsoredStoryLoggingConstants").ADBLOCK_CHECK_DELAY)}},e[0]=f,e[1]=g,e[2]=A,e[3]=k,e[4]=l,e[5]=n,e[6]=o,e[7]=p,e[8]=m,e[9]=q,e[10]=r,e[11]=s,e[12]=t,e[13]=u,e[14]=v,e[15]=w,e[16]=x,e[17]=z==null?void 0:z.id,e[18]=y,e[19]=a):a=e[19];return c("usePartialViewImpression")(a)}g["default"]=a}),98);
__d("usePolarisSponsoredStoryReelImpressionSecondChannelLogger",["InstagramODS","PolarisContainerModuleUtils","PolarisSponsoredStoryLoggingConstants","cometGHLContentDisplayCheck","cr:6627","qex","react-compiler-runtime","setTimeout","usePolarisAnalyticsContext","usePolarisViewer","useScrollBasedImpressionTracker"],(function(a,b,c,d,e,f,g){"use strict";var h=c("qex")._("1229"),i=new Map(),j=new Set();function a(a){var e=d("react-compiler-runtime").c(8),f=a.adId,g=a.trackingToken,k=c("usePolarisAnalyticsContext")(),l=c("usePolarisViewer")();e[0]!==k?(a=d("PolarisContainerModuleUtils").getContainerModule(k),e[0]=k,e[1]=a):a=e[1];var m=a;e[2]!==f||e[3]!==k||e[4]!==m||e[5]!==g||e[6]!==(l==null?void 0:l.id)?(a=function(a){if(f!=null&&g!=null&&(l==null?void 0:l.id)!=null&&k!=null){if(a.status!=="ENTER")return;var e=i.get(f),n=Date.now();if(j.has(f))return;j.add(f);c("setTimeout")(function(){var k=c("cometGHLContentDisplayCheck")(a.target);e==null?k?(c("InstagramODS").incr("web.ads.story.reel.impression.second_channel"),h===!0&&b("cr:6627")!=null&&b("cr:6627").log(function(){return{client_token:g,container_module:m,event:"reel_impression_second_channel_client"}}),i.set(f,n)):c("InstagramODS").incr("web.ads.story.reel.impression.second_channel.hidden"):n-e>=d("PolarisSponsoredStoryLoggingConstants").SUB_IMPRESSION_GAP&&(k?(c("InstagramODS").incr("web.ads.story.reel.sub_impression.second_channel"),h===!0&&b("cr:6627")!=null&&b("cr:6627").log(function(){return{client_token:g,container_module:m,event:"reel_sub_impression_second_channel_client"}}),i.set(f,n)):c("InstagramODS").incr("web.ads.story.reel.sub_impression.second_channel.hidden"));j["delete"](f)},d("PolarisSponsoredStoryLoggingConstants").ADBLOCK_CHECK_DELAY)}},e[2]=f,e[3]=k,e[4]=m,e[5]=g,e[6]=l==null?void 0:l.id,e[7]=a):a=e[7];return c("useScrollBasedImpressionTracker")(a)}g["default"]=a}),98);
__d("usePolarisStoryOpenShareDialog",["IGDContactSearchDialogType.flow","IGDSecureShareSheetDialogPlaceholder.react","PolarisPostTypeUtils","polarisLogAction","react","react-compiler-runtime","useIGDSLazyDialog","useIGDSecureShareSheetInteractionLogger","useIGDShareSheetOnSubmitStory","usePolarisAnalyticsContext","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));h.useCallback;function a(a){var b=d("react-compiler-runtime").c(8),e=a.adsStoryLoggingData,f=a.dialogResource,g=a.onClose,h=a.post;a=a.reelId;f=c("useIGDSLazyDialog")(f,j);var i=f[0],k=c("usePolarisAnalyticsContext")(),l=d("useIGDSecureShareSheetInteractionLogger").useLogShareSheetInteraction(),m=c("useIGDShareSheetOnSubmitStory")(h.id,a,(a=(f=h.owner)==null?void 0:f.id)!=null?a:"",(a=(f=h.owner)==null?void 0:f.username)!=null?a:"");b[0]!==e||b[1]!==k||b[2]!==l||b[3]!==g||b[4]!==m||b[5]!==i||b[6]!==h?(f=function(){c("polarisLogAction")("shareClick",{source:k,type:d("PolarisPostTypeUtils").getPostType(h)});var a=c("uuidv4")();l("igd_sharesheet_open",{flowInstanceId:a,shareType:d("IGDContactSearchDialogType.flow").IGDContactSearchDialogType.SHARESHEET,source:k});i({adsStoryLoggingData:e,flowInstanceId:a,onSubmit:m,post:h},g)},b[0]=e,b[1]=k,b[2]=l,b[3]=g,b[4]=m,b[5]=i,b[6]=h,b[7]=f):f=b[7];return f}function j(a){return i.jsx(c("IGDSecureShareSheetDialogPlaceholder.react"),{onClose:a})}j.displayName=j.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("usePolarisStoryTapToPlayOverlayLogger",["polarisLogAction","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useMemo;var i=b.useRef;function a(){var a=d("react-compiler-runtime").c(1),b=i(!1),e;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(e={logImpression:function(){b.current||(b.current=!0,c("polarisLogAction")("storiesTapToPlayOverlaySeen"))},logTapToPlay:j},a[0]=e):e=a[0];a=e;return a}function j(){c("polarisLogAction")("storiesTapToPlayOverlayTapped")}g["default"]=a}),98);