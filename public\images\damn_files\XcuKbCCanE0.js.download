;/*FB_PKG_DELIM*/

__d("IGDChatTabMessageNotificationUtils",["fbt","I64","LSBitFlag","LSIntEnum","MNXMAPreviewImageDecorationType","MessagingAttachmentType","ReQL","asyncToGeneratorRuntime","getLSMediaContactProfilePictureUrl","intlList"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j;function a(a,b){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){a=(yield d("ReQL").firstAsync(d("ReQL").fromTableDescending(a.tables.attachments).getKeyRange(b.threadKey,b.messageId)));if(a!=null){var e=a.attachmentType;b=(i||(i=d("I64"))).equal(b.displayedContentTypes,(j||(j=d("LSIntEnum"))).ofNumber(4096))||a.stickerType!=null;e=a.hasMedia&&(i||(i=d("I64"))).equal(e,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").IMAGE))||(i||(i=d("I64"))).equal(e,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").VIDEO))||(i||(i=d("I64"))).equal(e,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").ANIMATED_IMAGE));var f=a.hasXma&&a.defaultCtaType!=="xma_web_url";if(b||e||f)return a.previewUrl}return null});return k.apply(this,arguments)}function e(a,b){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e;a=(yield d("ReQL").firstAsync(d("ReQL").fromTableDescending(a.tables.attachments).getKeyRange(b.threadKey,b.messageId)));if(a!=null){if((i||(i=d("I64"))).equal(b.displayedContentTypes,(j||(j=d("LSIntEnum"))).ofNumber(4096))||a.stickerType!=null)return h._(/*BTDS*/"Sent a sticker.");if(a.hasXma){var f;f=(f=a.headerTitle)!=null?f:"Instagram User";var g=a.defaultCtaType,k=a.previewImageDecorationType;k!=null&&(i||(i=d("I64"))).equal(k,(j||(j=d("LSIntEnum"))).ofNumber(c("MNXMAPreviewImageDecorationType").REEL))?e=h._(/*BTDS*/"Sent a reel by {author}",[h._param("author",f)]):g==="igd_web_post_share"?e=h._(/*BTDS*/"Sent a post by {author}",[h._param("author",f)]):g==="xma_montage_share"?e=d("LSBitFlag").has((j||(j=d("LSIntEnum"))).ofNumber(512),b.displayedContentTypes)?h._(/*BTDS*/"Replied to your story"):h._(/*BTDS*/"Sent a story by {author}",[h._param("author",f)]):(a.playableUrlMimeType==="image/gif"||a.playableUrlMimeType==="video/mp4")&&(e=h._(/*BTDS*/"Sent an attachment."))}else if(a.hasMedia){k=a.attachmentType;(i||(i=d("I64"))).equal(k,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").IMAGE))?e=h._(/*BTDS*/"Sent a photo."):(i||(i=d("I64"))).equal(k,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").VIDEO))?e=h._(/*BTDS*/"Sent a video."):(i||(i=d("I64"))).equal(k,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").AUDIO))?e=h._(/*BTDS*/"Sent a voice message."):(i||(i=d("I64"))).equal(k,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").ANIMATED_IMAGE))&&(e=h._(/*BTDS*/"Sent an attachment."))}}return e});return l.apply(this,arguments)}function m(a,b){return(i||(i=d("I64"))).to_float(a.muteExpireTimeMs)===-1||(i||(i=d("I64"))).to_float(a.muteExpireTimeMs)>b}function f(a,b,c,d,e,f){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e,f,g){var h;h=(h=e.reactionCreationTimestampMs)!=null?h:e.timestampMs;if((i||(i=d("I64"))).equal(e.actorId,b)||(i||(i=d("I64"))).to_float(h)<f||(g==null?void 0:g.has((i||(i=d("I64"))).to_string(e.threadKey))))return!1;g=(yield a.tables.threads.get(e.threadKey));if(g==null)return!1;a=m(g,f);if(a)return!1;if(c==null)return(i||(i=d("I64"))).equal(h,g.lastActivityTimestampMs);return(i||(i=d("I64"))).equal(c.senderId,e.actorId)||!(i||(i=d("I64"))).equal(c.senderId,b)?!1:!0});return n.apply(this,arguments)}function o(a,b,c,d,e){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e,f){if(b.isAdminMessage||(i||(i=d("I64"))).equal(b.senderId,c)||(i||(i=d("I64"))).to_float(b.timestampMs)<e||(f==null?void 0:f.has((i||(i=d("I64"))).to_string(b.threadKey))))return!1;c=(yield a.tables.threads.get(b.threadKey));return c!=null&&m(c,e)?!1:!0});return p.apply(this,arguments)}function q(a,b){var e=a.length,f=String(h._(/*BTDS*/"Instagram User")),g=a.filter(function(a){return!(i||(i=d("I64"))).equal(a[0].contactId,b)}).map(function(a){var b=a[0].nickname;a=a[1];if(b!=null)return b;else{return(b=a==null?void 0:a.name)!=null?b:f}});g.splice(0,0,"you");if(e===0)return;else if(e===1){a=a[0];var j=a[0];a=a[1];a=j.nickname!=null?j.nickname:(j=a==null?void 0:a.name)!=null?j:f}else if(e===2||e===3)a=c("intlList")(g,c("intlList").CONJUNCTIONS.AND,c("intlList").DELIMITERS.COMMA).toString();else{g.splice(2);j=String(e-2).concat(" others");g.push(j);a=c("intlList")(g,c("intlList").CONJUNCTIONS.AND,c("intlList").DELIMITERS.COMMA).toString()}return h._(/*BTDS*/"To {subtitle}",[h._param("subtitle",a.replace("and","&"))])}function r(a,b,c,d,e,f,g){return s.apply(this,arguments)}function s(){s=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f,g,h,j){e=(yield a.tables.contacts.get(e));a=(yield a.tables.threads.get(b));e=e!=null?{mediaPreviewSrc:g,message:h,messageId:f,senderImageSrc:c("getLSMediaContactProfilePictureUrl")(e),senderName:e.name,thread:a,threadKey:(i||(i=d("I64"))).to_string(b),timestampMs:j}:{mediaPreviewSrc:g,message:h,messageId:f,thread:a,threadKey:(i||(i=d("I64"))).to_string(b),timestampMs:j};return e});return s.apply(this,arguments)}g.getMediaPreviewSrc=a;g.generateChatTabNotifPreviewTextForAttachment=e;g.shouldRenderChatTabReactionNotification=f;g.shouldRenderChatTabMessageNotification=o;g.generateChatTabNotifSubtitleTextForGroupMessage=q;g.generateChatTabsPreviewNotification=r}),226);
__d("IGDChatTabMessageNotification.react",["fbt","BaseImage.react","I64","IGDChatTabMessageNotificationUtils","IGDFallbackProfileUrl","IGDSAvatar.react","IGDSBox.react","IGDSTextVariants.react","IGDThreadListNewMessageLoggingDataContext","LSIntEnum","MWPActor.react","MessengerWebUXLogger","ReQL","ReQLSuspense","qex","react","useReStore"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k,l,m=l||d("react"),n=60,o={fadeIn:{animationDuration:"xs4xyr0",animationName:"x1rp1485",animationTimingFunction:"x4hg4is",$$css:!0},mediaPreview:{borderStartStartRadius:"xjwep3j",borderStartEndRadius:"x1t39747",borderEndEndRadius:"x1wcsgtt",borderEndStartRadius:"x1pczhz8",objectFit:"xl1xv1r",$$css:!0},messageBox:{flexBasis:"x1t1x2f9",flexGrow:"x1iyjqo2",flexShrink:"xs83m0k",$$css:!0},root:{backgroundColor:"x7r02ix",borderStartStartRadius:"x6nl9eh",borderStartEndRadius:"x1a5l9x9",borderEndEndRadius:"x7vuprf",borderEndStartRadius:"x1mg3h75",boxShadow:"x1wp8tw6",zIndex:"x1vjfegm",":hover_backgroundColor":"x2nact",$$css:!0}};function a(a){a=a.previewData;var b=a.mediaPreviewSrc,e=a.message,g=a.senderImageSrc,l=a.senderName,p=a.thread,q=a.threadKey;a=d("IGDThreadListNewMessageLoggingDataContext").useFlowInstanceIdContext();a=c("MessengerWebUXLogger").useImpressionLoggerRef({eventName:"igd_chat_tabs_notification",flowInstanceId:a});var r=(i||(i=c("useReStore")))(),s=d("MWPActor.react").useActor(),t=d("ReQLSuspense").useArray(function(){return d("ReQL").leftJoin(d("ReQL").fromTableAscending(r.tables.participants,["contactId","nickname"]).getKeyRange((j||(j=d("I64"))).of_string(q)),d("ReQL").fromTableAscending(r.tables.contacts,["name"])).take(5)},[r,q],f.id+":98");p=p!=null&&(j||(j=d("I64"))).equal(p.threadType,(k||(k=d("LSIntEnum"))).ofNumber(2))?p.threadName!=null?p.threadName:d("IGDChatTabMessageNotificationUtils").generateChatTabNotifSubtitleTextForGroupMessage(t,s):null;t=c("qex")._("4526")===!0;return m.jsx(c("IGDSBox.react"),{containerRef:a,children:m.jsxs(c("IGDSBox.react"),{alignItems:"center",direction:"row",height:80,overflow:"hidden",width:350,xstyle:[o.root,o.fadeIn],children:[m.jsx(c("IGDSBox.react"),{margin:3,children:m.jsx(c("IGDSAvatar.react"),{alt:h._(/*BTDS*/"Instagram Avatar Profile Picture"),size:"large",src:g!=null?g:d("IGDFallbackProfileUrl").FALLBACK_PROFILE_PIC_URL})}),m.jsxs(c("IGDSBox.react"),{direction:"column",height:"100%",justifyContent:"center",marginEnd:3,overflow:"hidden",paddingY:3,xstyle:o.messageBox,children:[m.jsx(c("IGDSBox.react"),{children:m.jsx(d("IGDSTextVariants.react").IGDSTextBodyEmphasized,{maxLines:1,textAlign:"start",zeroMargin:!0,children:l!=null?l:h._(/*BTDS*/"Instagram User")})}),p!=null&&p!==""?m.jsx(c("IGDSBox.react"),{marginTop:1,width:"100%",children:m.jsx(d("IGDSTextVariants.react").IGDSTextBodyEmphasized,{maxLines:1,textAlign:"start",zeroMargin:!0,children:p})}):null,m.jsx(c("IGDSBox.react"),{marginTop:1,width:"100%",children:m.jsx(d("IGDSTextVariants.react").IGDSTextBody,{color:"secondaryText",maxLines:p!=null?1:2,textAlign:"start",zeroMargin:!0,children:e!=null?e:h._(/*BTDS*/"Sent a message.")})})]}),t&&b!=null&&m.jsx(c("IGDSBox.react"),{padding:4,children:m.jsx(c("BaseImage.react"),{height:n,src:b,width:n,xstyle:o.mediaPreview})})]})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("IGDChatTabsNUXUtil",["PolarisDismissEntry","gkx"],(function(a,b,c,d,e,f,g){"use strict";a=function(){var a=c("gkx")("14330");a&&!d("PolarisDismissEntry").isDismissed(d("PolarisDismissEntry").IGD_CHAT_TABS_NUX_TYPE)&&d("PolarisDismissEntry").setDismissEntry(d("PolarisDismissEntry").IGD_CHAT_TABS_NUX_TYPE)};g.dismissChatTabsNUX=a}),98);
__d("useAddToNotificationQueue",["I64","IGDChatTabsStateContext.react","bx","qex","react","requireDeferred","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(h||d("react")).useCallback,k=c("requireDeferred")("Sound").__setRef("useAddToNotificationQueue"),l=5e3,m=c("bx").getURL(c("bx")("32259")),n=c("bx").getURL(c("bx")("32260")),o=c("qex")._("4558")===!0;function a(){var a=d("IGDChatTabsStateContext.react").useIGDChatTabsDispatch();return j(function(b){a==null?void 0:a({notification:b,type:"push_notification"}),c("setTimeout")(function(){a==null?void 0:a({messageId:b.messageId,type:"clear_notification"})},l),o&&p(b)},[a])}function p(a){return k.onReady(function(b){b.play([n,m],(i||(i=d("I64"))).to_float(a.timestampMs),!1)})}g["default"]=a}),98);
__d("useSubscribeToMessages",["I64","IGDChatTabMessageNotificationUtils","IGDChatTabsStateContext.react","LSIntEnum","MWPActor.react","ReQL","asyncToGeneratorRuntime","react","useAddToNotificationQueue","useServerTime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(h||d("react")).useCallback;function a(){var a=d("MWPActor.react").useActor(),e=c("useServerTime")(),f=d("IGDChatTabsStateContext.react").useIGDChatTabsState(),g=c("useAddToNotificationQueue")();return k(function(c){var h=d("ReQL").fromTableDescending(c.tables.messages.index("timestampMs")).subscribe(function(){var h=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b,h){if(h.operation!=="add")return;b=h.value;h=(yield d("IGDChatTabMessageNotificationUtils").shouldRenderChatTabMessageNotification(c,b,a,e.valueOf(),f==null?void 0:f.openedTabs));if(h){h=(i||(i=d("I64"))).equal(b.displayedContentTypes,(j||(j=d("LSIntEnum"))).ofNumber(1));var k=h?b.text:yield d("IGDChatTabMessageNotificationUtils").generateChatTabNotifPreviewTextForAttachment(c,b);h=h?null:yield d("IGDChatTabMessageNotificationUtils").getMediaPreviewSrc(c,b);h=(yield d("IGDChatTabMessageNotificationUtils").generateChatTabsPreviewNotification(c,b.threadKey,b.senderId,b.messageId,h,k,b.timestampMs));g(h)}});return function(a,b){return h.apply(this,arguments)}}());return h},[a,g,e,f==null?void 0:f.openedTabs])}g["default"]=a}),98);
__d("useSubscribeToReactions",["fbt","IGDChatTabMessageNotificationUtils","IGDChatTabsStateContext.react","MWPActor.react","ReQL","asyncToGeneratorRuntime","react","useAddToNotificationQueue","useServerTime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=(i||d("react")).useCallback,k=function(a){return h._(/*BTDS*/"Reacted {emoji} to your message",[h._param("emoji",a)])},l=function(){return h._(/*BTDS*/"Liked your message")};function a(){var a=d("MWPActor.react").useActor(),e=c("useServerTime")(),f=d("IGDChatTabsStateContext.react").useIGDChatTabsState(),g=c("useAddToNotificationQueue")();return j(function(c){var h=d("ReQL").fromTableDescending(c.tables.reactions.index("fk_messages")).subscribe(function(){var h=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b,h){if(h.operation!=="add")return;b=h.value;h=(yield c.tables.messages.index("messageId").get(b.messageId));h=(yield d("IGDChatTabMessageNotificationUtils").shouldRenderChatTabReactionNotification(c,a,h,b,e.valueOf(),f==null?void 0:f.openedTabs));if(h){h=b.reaction==="\u2764"?l():k(b.reaction);h=(yield d("IGDChatTabMessageNotificationUtils").generateChatTabsPreviewNotification(c,b.threadKey,b.actorId,b.messageId,null,h,b.timestampMs));g(h)}});return function(a,b){return h.apply(this,arguments)}}());return h},[a,g,e,f==null?void 0:f.openedTabs])}g["default"]=a}),226);
__d("useSubscribeToNotifications",["promiseDone","react","useAsyncReStore","useSubscribeToMessages","useSubscribeToReactions"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(){var a=c("useAsyncReStore")(),b=c("useSubscribeToMessages")(),d=c("useSubscribeToReactions")();i(function(){var d=a.then(function(a){return b(a)});return function(){c("promiseDone")(d.then(function(a){a()}))}},[a,b]);i(function(){var b=a.then(function(a){return d(a)});return function(){c("promiseDone")(b.then(function(a){a()}))}},[a,d])}g["default"]=a}),98);
__d("IGDChatTabMessageNotificationsContainer.react",["CometPressable.react","IGDChatTabMessageNotification.react","IGDChatTabsNUXUtil","IGDChatTabsQPLInstanceKeyContext.react","IGDChatTabsStateContext.react","IGDChatTabsStateTypes","IGDSBox.react","IGDThreadListNewMessageLoggingDataContext","MessengerWebUXLogger","PolarisDismissEntry","QuickPerformanceLogger","Random","gkx","qpl","react","usePolarisIsSmallScreen","useSubscribeToNotifications"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useCallback,l={chatTabsClosedRoot:{bottom:"xrsf3xo",$$css:!0},chatTabsOpenRoot:{bottom:"xqxrpsi",$$css:!0},chatTabsWithNuxRoot:{bottom:"x18bbb1i",$$css:!0},mobileNavRoot:{marginBottom:"xui9b5u",$$css:!0},pressableContainer:{borderStartStartRadius:"x6nl9eh",borderStartEndRadius:"x1a5l9x9",borderEndEndRadius:"x7vuprf",borderEndStartRadius:"x1mg3h75",$$css:!0},root:{insetInlineEnd:"x145d82y",left:null,right:null,position:"xixxii4",rowGap:"x3pnbk8",zIndex:"x1vjfegm",$$css:!0}},m=3;function a(){var a=d("IGDChatTabsStateContext.react").useIGDChatTabsDispatch(),b=d("IGDChatTabsStateContext.react").useIGDChatTabsState(),e=c("usePolarisIsSmallScreen")(),f=b==null?void 0:b.notifications;b=(b==null?void 0:b.activeView)!=null;var g=c("gkx")("14330");c("useSubscribeToNotifications")();var i=d("IGDThreadListNewMessageLoggingDataContext").useFlowInstanceIdContext(),n=d("IGDChatTabsQPLInstanceKeyContext.react").useIGDChatTabsQPLInstanceKeyContext(),o=c("MessengerWebUXLogger").useInteractionLogger(),p=b?d("IGDChatTabsStateTypes").IGDChatTabsMessagingInitiationSource.NotificationWithTabOpen:d("IGDChatTabsStateTypes").IGDChatTabsMessagingInitiationSource.NotificationWithJewel,q=k(function(b){d("IGDChatTabsNUXUtil").dismissChatTabsNUX();a==null?void 0:a({source:p,threadKey:b.threadKey,type:"open_tab"});a==null?void 0:a({messageId:b.messageId,type:"clear_notification"});o==null?void 0:o({eventName:"igd_chat_tabs_notification_clicked",flowInstanceId:i});var e=n.threadViewKeys[b.threadKey]==null,f=d("Random").uint32();n.setThreadViewKey(b.threadKey,f);(h||(h=c("QuickPerformanceLogger"))).markerStart(c("qpl")._(882771792,"2790"),f);h.markerAnnotate(c("qpl")._(882771792,"2790"),{bool:{isFirstLoad:e},string:{trigger:"notification"}},{instanceKey:f})},[a,i,p,o,n]);return f==null||f.size===0?null:j.jsx(c("IGDSBox.react"),{direction:"columnReverse",xstyle:[l.root,e&&l.mobileNavRoot,b?l.chatTabsOpenRoot:l.chatTabsClosedRoot,g&&!d("PolarisDismissEntry").isDismissed(d("PolarisDismissEntry").IGD_CHAT_TABS_NUX_TYPE)&&l.chatTabsWithNuxRoot],children:Array.from(f.values()).slice(-m).map(function(a,b){return j.jsx(c("CometPressable.react"),{expanding:!0,onPress:function(){q(a)},role:"button",xstyle:l.pressableContainer,children:j.jsx(c("IGDChatTabMessageNotification.react"),{previewData:a})},b)})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDChatTabsContainer.react",["fbt","CometComponentWithKeyCommands.react","CometKeys","CometPlaceholder.react","IGDChatTabPlaceholder.react","IGDChatTabsStateContext.react","IGDChatTabsStateTypes","IGDSBox.react","JSResourceForInteraction","MWPVisibleMessageContext.react","lazyLoadComponent","qex","react","useRoutePassthroughProps"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||(i=d("react"));b=i;var k=b.useCallback,l=b.useEffect,m=b.useMemo,n=c("lazyLoadComponent")(c("JSResourceForInteraction")("IGDChatTab.react").__setRef("IGDChatTabsContainer.react"));function a(){var a=d("IGDChatTabsStateContext.react").useIGDChatTabsState(),b=a==null?void 0:a.openedTabs,e=d("IGDChatTabsStateContext.react").useIGDChatTabsDispatch(),f=c("useRoutePassthroughProps")(),g=a==null?void 0:a.activeView,i=a==null?void 0:a.previousView,o=c("qex")._("4543")===!0,p=k(function(){for(var a of b!=null?b:[]){var c=a[0];e==null?void 0:e({threadKey:c,type:"close_tab"})}},[e,b]);a=m(function(){return[{command:{key:c("CometKeys").ESCAPE},description:h._(/*BTDS*/"Close chat tab"),handler:p,triggerFromInputs:!0}]},[p]);l(function(){if((f==null?void 0:f.threadKey)!=null&&(f==null?void 0:f.entryPoint)==="inboxInThread"&&g==null&&i==null&&!o){var a=f==null?void 0:f.chatTabsMessagingSource;a=a!=null&&Array.from(d("IGDChatTabsStateTypes").IGDChatTabsMessagingInitiationSource.members()).includes(a)?a:d("IGDChatTabsStateTypes").IGDChatTabsMessagingInitiationSource.Unknown;e==null?void 0:e({source:a,threadKey:String(f==null?void 0:f.threadKey),type:"open_tab"})}},[g,e,o,f,f==null?void 0:f.entryPoint,f==null?void 0:f.threadKey,i]);if(b==null||b.size<=0)return null;var q=Array.from(b).map(function(a){a=a[0];return j.jsx(c("CometPlaceholder.react"),{fallback:j.jsx(c("IGDChatTabPlaceholder.react"),{threadKey:a}),children:j.jsx(n,{threadKey:a})},a)});return j.jsx(c("CometComponentWithKeyCommands.react"),{commandConfigs:a,children:j.jsx(c("IGDSBox.react"),{direction:"row",children:j.jsx(d("MWPVisibleMessageContext.react").MWPVisibleMessageContextProvider,{children:q})})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("IGDChatTabsPresenceUnifiedClientSingleton",["BladerunnerHandlersPresencePresenceHandlerTypes","PresenceCapabilityBit","PresenceCommonPresenceCommonTypes","PresenceUnifiedClient","RealtimeNexusSessionDataTypes","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";b=new(d("PresenceUnifiedClient").PresenceUnifiedClient)({appFamily:d("PresenceCommonPresenceCommonTypes").AppFamily.Instagram,pollingMode:d("BladerunnerHandlersPresencePresenceHandlerTypes").PollingMode.BuddyListPolling,appId:String(936619743392459),presenceReportingRequest:{capabilities:(a=d("PresenceCapabilityBit")).combine(a.VoiceIpEnabled,a.WebrtcEnabled,a.ReceivePublishWithEIMUID),mutationId:c("uuidv4")(),availability:d("RealtimeNexusSessionDataTypes").PresenceAvailability.IDLE}});g.IGDChatTabsPresenceUnifiedClientSingleton=b}),98);
__d("IGDChatTabsPresenceUnifiedContextProvider.react",["IGDChatTabsPresenceUnifiedClientSingleton","IGPresenceUnifiedContext.react","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=a.children;a=a.presenceSetupQueryRef;return i.jsx(c("IGPresenceUnifiedContext.react"),{client:d("IGDChatTabsPresenceUnifiedClientSingleton").IGDChatTabsPresenceUnifiedClientSingleton,presenceSetupQueryRef:a,children:b})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDChatTabsPresenceUnifiedContextProviderWrapper.react",["IGDChatTabsPresenceUnifiedContextProvider.react","IGDChatTabsStateContext.react","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=a.children;a=a.presenceSetupQueryRef;var e=d("IGDChatTabsStateContext.react").useIGDChatTabsState();return(e==null?void 0:e.activeView)==null||a==null?b:i.jsx(c("IGDChatTabsPresenceUnifiedContextProvider.react"),{presenceSetupQueryRef:a,children:b})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDChatTabsThreadListContainer.react",["CometPlaceholder.react","IGDChatTabsStateContext.react","IGDChatTabsStateTypes","IGDChatTabsThreadListContainerPlaceholder.react","IGDSBox.react","JSResourceForInteraction","lazyLoadComponent","qex","react","useRoutePassthroughProps"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useEffect,k=c("lazyLoadComponent")(c("JSResourceForInteraction")("IGDChatTabsThreadListContent.react").__setRef("IGDChatTabsThreadListContainer.react")),l={hidden:{visibility:"xlshs6z",$$css:!0}};function a(){var a=d("IGDChatTabsStateContext.react").useIGDChatTabsDispatch(),b=d("IGDChatTabsStateContext.react").useIGDChatTabsState(),e=b==null?void 0:b.activeView,f=b==null?void 0:b.previousView,g=c("qex")._("4543")===!0,h=c("useRoutePassthroughProps")();j(function(){(h==null?void 0:h.entryPoint)==="inboxThreadList"&&e==null&&f==null&&!g&&(a==null?void 0:a({entryPoint:"inboxThreadList",type:"open_view",view:d("IGDChatTabsStateTypes").IGDChatTabsView.ChatTabsThreadListView}))},[e,a,g,h,f]);return e===d("IGDChatTabsStateTypes").IGDChatTabsView.ChatTabsThreadListView||f===d("IGDChatTabsStateTypes").IGDChatTabsView.ChatTabsThreadListView?i.jsx(c("IGDSBox.react"),{xstyle:f===d("IGDChatTabsStateTypes").IGDChatTabsView.ChatTabsThreadListView&&l.hidden,children:i.jsx(c("CometPlaceholder.react"),{fallback:i.jsx(c("IGDChatTabsThreadListContainerPlaceholder.react"),{}),children:i.jsx(k,{})})}):null}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDChatTabsContent.react",["IGDChatTabsContainer.react","IGDChatTabsPresenceUnifiedContextProviderWrapper.react","IGDChatTabsThreadListContainer.react","IGDTypingIndicatorProvider.react","cr:20016","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var d=a.presenceSetupQueryRef;a=a.viewerSettingsQueryRef;return i.jsx(c("IGDChatTabsPresenceUnifiedContextProviderWrapper.react"),{presenceSetupQueryRef:d,children:i.jsxs(c("IGDTypingIndicatorProvider.react"),{children:[i.jsx(c("IGDChatTabsThreadListContainer.react"),{}),i.jsx(b("cr:20016"),{viewerSettingsQueryRef:a,children:i.jsx(c("IGDChatTabsContainer.react"),{})})]})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDChatTabsJewelV2.react",["fbt","CometPressable.react","IGDChatTabsJewelStyles","IGDChatTabsNUXUtil","IGDChatTabsQPLInstanceKeyContext.react","IGDChatTabsStateContext.react","IGDChatTabsStateTypes","IGDSBox.react","IGDSText.react","IGDSTooltip.react","IGDThreadListNewMessageLoggingDataContext","MessengerWebUXLogger","PolarisBadgeConstants","PolarisDirectNavItemBadge.react","PolarisDismissEntry","QuickPerformanceLogger","Random","cr:19742","cr:21425","deferredLoadComponent","gkx","qex","qpl","react","requireDeferred","useIGDChatTabsGetUnreadThreads.react","useIGDChatTabsReelsPageResize.react","usePolarisIsSmallScreen"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||(j=d("react")),l=j.useCallback,m=c("deferredLoadComponent")(c("requireDeferred")("IGDChatTabsPOGs.react").__setRef("IGDChatTabsJewelV2.react")),n={directIcon:{marginTop:"xvijh9v",$$css:!0},jewelForMobileNav:{width:"x15yg21f",$$css:!0}},o=h._(/*BTDS*/"Messages");function a(){var a=d("IGDChatTabsStateContext.react").useIGDChatTabsDispatch(),e=d("IGDChatTabsStateContext.react").useIGDChatTabsState(),f=d("IGDThreadListNewMessageLoggingDataContext").useFlowInstanceIdContext(),g=d("IGDChatTabsQPLInstanceKeyContext.react").useIGDChatTabsQPLInstanceKeyContext(),j=c("MessengerWebUXLogger").useInteractionLogger(),p=c("usePolarisIsSmallScreen")(),q=c("useIGDChatTabsGetUnreadThreads.react")(),r=q.length,s=r<d("PolarisBadgeConstants").BADGE_COUNT_LIMIT?String(r):d("PolarisBadgeConstants").BADGE_COUNT_LIMIT+"+",t=c("gkx")("14330"),u=c("useIGDChatTabsReelsPageResize.react")();u=u[0];u=p||u;var v=l(function(){d("IGDChatTabsNUXUtil").dismissChatTabsNUX();a==null?void 0:a({type:"open_view",view:d("IGDChatTabsStateTypes").IGDChatTabsView.ChatTabsThreadListView});j==null?void 0:j({eventName:"igd_chat_tabs_jewel_clicked",extraData:{unSeenCount:String(r)},flowInstanceId:f});var b=d("Random").uint32();(i||(i=c("QuickPerformanceLogger"))).markerStart(c("qpl")._(882771984,"2789"),b);g.threadListKey===0&&(i||(i=c("QuickPerformanceLogger"))).markerAnnotate(c("qpl")._(882771984,"2789"),{bool:{isFirstLoad:!0}},{instanceKey:b});g.setThreadListKey(b)},[a,j,r,f,g]),w=l(function(){j==null?void 0:j({eventName:"igd_chat_tabs_nux",flowInstanceId:f}),v()},[f,j,v]),x=c("MessengerWebUXLogger").useImpressionLoggerRef({eventName:"igd_chat_tabs_jewel",flowInstanceId:f},function(){(i||(i=c("QuickPerformanceLogger"))).markerEnd(c("qpl")._(882775198,"2787"),2),i.markerAnnotate(c("qpl")._(25305590,"1127"),{string:{entrypoint:"igd_chat_tabs"}})}),y=c("MessengerWebUXLogger").useImpressionLogger(),z=l(function(){y==null?void 0:y({eventName:"igd_chat_tabs_badge",flowInstanceId:f})},[y,f]),A=c("MessengerWebUXLogger").useImpressionLoggerRef({eventName:"igd_chat_tabs_nux",flowInstanceId:f}),B=c("qex")._("3938"),C=B===!0?o:h._(/*BTDS*/"Direct messages");C=k.jsx(b("cr:19742"),{alt:C,color:"ig-primary-icon"});B=k.jsxs(c("IGDSBox.react"),{display:"flex",position:"relative",xstyle:B===!0&&n.directIcon,children:[C,r>0&&k.jsx(c("IGDSBox.react"),{containerRef:z,children:k.jsx(c("PolarisDirectNavItemBadge.react"),{badgeText:r>0?s:void 0})})]});C=k.jsxs(c("IGDSBox.react"),{alignItems:"center",direction:u?"column":"row",display:"flex",justifyContent:"between",position:"relative",width:"100%",children:[k.jsxs(c("IGDSBox.react"),{alignItems:"center",direction:"row",children:[k.jsx(c("IGDSBox.react"),{marginEnd:r===0?2:r<9?3:4,children:B}),k.jsx(c("IGDSText.react"),{size:"label",weight:"semibold",children:o})]}),k.jsx(m,{onChatTabJewelClick:v,unreadThreads:q})]});z=h._(/*BTDS*/"Now you can chat with friends while you browse.");s=k.jsx(c("IGDSBox.react"),{containerRef:A,"data-testid":void 0,paddingY:2,children:k.jsx(c("IGDSText.react"),{color:"primaryText",size:"label",weight:"medium",children:z})});return k.jsxs(c("IGDSBox.react"),{containerRef:x,xstyle:[d("IGDChatTabsJewelStyles").IGDChatTabsJewelStyles.root,p&&d("IGDChatTabsJewelStyles").IGDChatTabsJewelStyles.rootForMobileNav],children:[b("cr:21425")&&k.jsx(b("cr:21425"),{badgeConfig:{badgeCount:r,badgeCountLimit:d("PolarisBadgeConstants").BADGE_COUNT_LIMIT}}),k.jsx(c("IGDSTooltip.react"),{align:u?"end":"middle",disableAutoAlign:!1,disableAutoFlip:!1,isVisible:t&&!d("PolarisDismissEntry").isDismissed(d("PolarisDismissEntry").IGD_CHAT_TABS_NUX_TYPE)&&(e==null?void 0:e.activeView)==null,onClick:w,position:"above",tooltip:s,useFadeTransition:!0,children:k.jsx(c("CometPressable.react"),{onPress:v,role:"button",testid:void 0,xstyle:[d("IGDChatTabsJewelStyles").IGDChatTabsJewelStyles.button,d("IGDChatTabsJewelStyles").IGDChatTabsJewelStyles.buttonExpanded,u&&n.jewelForMobileNav],children:u?B:C})})]})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("IGDChatTabsOmnipickerPlaceholder.react",["IGDChatTabSingleTabContainer.react","IGDChatTabsOmnipickerHeader.react","IGDSBox.react","IGDSGlimmer.react","MWXSpinner.react","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={chatButtonGlimmer:{borderStartStartRadius:"x1obq294",borderStartEndRadius:"x5a5i1n",borderEndEndRadius:"xde0f50",borderEndStartRadius:"x15x8krk",$$css:!0},glimmer:{height:"x5yr21d",width:"xh8yej3",$$css:!0}};function a(){return i.jsxs(c("IGDChatTabSingleTabContainer.react"),{children:[i.jsx(c("IGDChatTabsOmnipickerHeader.react"),{}),i.jsxs(c("IGDSBox.react"),{alignItems:"center",direction:"column",children:[i.jsx(c("IGDSBox.react"),{height:400,justifyContent:"center",children:i.jsx(c("MWXSpinner.react"),{color:"disabled",size:32})}),i.jsx(c("IGDSBox.react"),{height:44,paddingX:4,width:"100%",children:i.jsx(c("IGDSGlimmer.react"),{index:2,xstyle:[j.glimmer,j.chatButtonGlimmer]})})]})]})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDChatTabsOmnipickerContainerLazyLoaded.react",["CometPlaceholder.react","IGDChatTabsOmnipickerPlaceholder.react","IGDChatTabsStateContext.react","IGDChatTabsStateTypes","JSResourceForInteraction","lazyLoadComponent","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("lazyLoadComponent")(c("JSResourceForInteraction")("IGDChatTabsOmnipickerContainer.react").__setRef("IGDChatTabsOmnipickerContainerLazyLoaded.react"));function a(){var a=d("IGDChatTabsStateContext.react").useCurrentChatTabsView();return a===d("IGDChatTabsStateTypes").IGDChatTabsView.ChatTabsOmnipickerView?i.jsx(c("CometPlaceholder.react"),{fallback:i.jsx(c("IGDChatTabsOmnipickerPlaceholder.react"),{}),children:i.jsx(j,{})}):null}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDChatTabsRootContent.react",["CometPageletWithDiv.react","HeroInteractionIgnoreWithDiv.react","IGDChatTabMessageNotificationsContainer.react","IGDChatTabsContent.react","IGDChatTabsOmnipickerContainerLazyLoaded.react","IGDChatTabsQPLInstanceKeyContext.react","IGDChatTabsThemeProvider.react","IGDPageSetup.react","cr:19922","cr:19923","react","usePolarisIsTinyScreen"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var e=a.presenceSetupQueryRef;a=a.viewerSettingsQueryRef;var f=c("usePolarisIsTinyScreen")();return i.jsx(d("CometPageletWithDiv.react").Placeholder,{className:{0:"x1vjfegm",1:"x1vjfegm xlshs6z"}[!!f<<0],fallback:i.jsx(b("cr:19923"),{}),name:"IGDChatTabsRootContent",children:i.jsx(d("IGDChatTabsQPLInstanceKeyContext.react").IGDChatTabsQPLInstanceKeyContextProvider,{children:i.jsx(c("IGDChatTabsThemeProvider.react"),{children:i.jsx(c("HeroInteractionIgnoreWithDiv.react"),{children:i.jsxs(c("IGDPageSetup.react"),{disableRouteProvider:!0,placeholder:i.jsx(b("cr:19923"),{}),shouldUseDeferredLoadingStrategy:!1,threadDisplayType:"ChatTab",children:[i.jsx(b("cr:19922"),{}),i.jsx(c("IGDChatTabMessageNotificationsContainer.react"),{}),i.jsx(c("IGDChatTabsOmnipickerContainerLazyLoaded.react"),{}),i.jsx(c("IGDChatTabsContent.react"),{presenceSetupQueryRef:e,viewerSettingsQueryRef:a})]})})})})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);