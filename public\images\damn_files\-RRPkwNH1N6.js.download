;/*FB_PKG_DELIM*/

__d("ArmadilloDataTraceCheckPoint",["I64"],(function(a,b,c,d,e,f,g){"use strict";var h;a=(h||(h=d("I64"))).of_string("63");b=h.of_string("65");c=h.of_string("98");e=h.of_string("504");f=h.of_string("1000");d=h.of_string("1001");var i=h.of_string("1204"),j=h.of_string("1210"),k=h.of_string("1224"),l=h.of_string("1234"),m=h.of_string("1225"),n=h.of_string("2220");g.traceGroupMessage=a;g.armadilloActMessageSend=b;g.traceCreated=c;g.richMediaMediaSendAttachmentType=e;g.traceTextMessage=f;g.traceMediaMessage=d;g.advancedCryptoMEMSendTaskComplete=i;g.deliveryReceiptReceived=j;g.advancedCryptoMEMEncryptionDeviceCount=k;g.advancedCryptoGroupDualSend=l;g.advancedCryptoDualSend=m;g.armadilloTlcControlOpenThread=n}),98);
__d("EBMainThreadListeners",["EBReenrollmentTriggerListener","LSDatabaseSingleton","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield (h||(h=d("LSDatabaseSingleton"))).LSDatabaseSingleton);d("EBReenrollmentTriggerListener").initEBReenrollmentTriggerListener(a)});return i.apply(this,arguments)}g.initEBListeners=a}),98);
__d("LSSetIGE2EEEligibility",["LSIssueNewTask"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.filter(c.db.table(176).fetch([[[a[0]]]]),function(b){return c.i64.eq(b.contactId,a[0])&&c.i64.eq(b.e2eeEligibility,a[1])}).next().then(function(e,f){f=e.done;e=e.value;return f?(d[1]=new c.Map(),d[1].set("contact_id",a[0]),d[1].set("new_eligibility",a[1]),d[2]=c.toJSON(d[1]),c.storedProcedure(b("LSIssueNewTask"),"ig_set_e2ee_eligibility",c.i64.cast([0,60001]),d[2],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))):(e.item,0)})},function(a){return c.resolve(e)}])}a.__sproc_name__="LSContactSetIGE2EEEligibilityStoredProcedure";a.__tables__=["ig_contact_info"];e.exports=a}),null);
__d("LSSetIGE2EEEligibilityStoredProcedure",["LSSetIGE2EEEligibility","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSSetIGE2EEEligibility"),e.id,e.e2eeEligibility);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("IGDSetIGE2EEEligibility",["I64","LSDatabaseSingleton","LSFactory","LSInstagramE2EEEligibility","LSIntEnum","LSSetIGE2EEEligibilityStoredProcedure","MAWCurrentUser","asyncToGeneratorRuntime","justknobx","promiseDone","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=(h||d("react")).useEffect;function m(a,b){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e=(yield (i||(i=d("LSDatabaseSingleton"))).LSDatabaseSingleton);return e.runInTransaction(function(e){return c("LSSetIGE2EEEligibilityStoredProcedure")(c("LSFactory")(e),{e2eeEligibility:(j||(j=d("LSIntEnum"))).ofNumber(b),id:(k||(k=d("I64"))).of_string(a)})},"readwrite",void 0,void 0,f.id+":31")});return n.apply(this,arguments)}function a(){var a=d("react-compiler-runtime").c(1),b;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=[],a[0]=b):b=a[0];l(o,b)}function o(){if(c("justknobx")._("2058"))return;var a=d("MAWCurrentUser").getID();c("promiseDone")(m(a,c("LSInstagramE2EEEligibility").ELIGIBLE))}g.useUpsertSetIGE2EEEligibilityOnMount=a}),98);
__d("LSPlatformGraphQLLightspeedRequestQuery.graphql",["relay-runtime"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"deviceId"},{defaultValue:null,kind:"LocalArgument",name:"requestId"},{defaultValue:null,kind:"LocalArgument",name:"requestPayload"},{defaultValue:null,kind:"LocalArgument",name:"requestType"}],b=[{alias:null,args:null,concreteType:"Viewer",kind:"LinkedField",name:"viewer",plural:!1,selections:[{alias:null,args:[{kind:"Variable",name:"device_id",variableName:"deviceId"},{kind:"Variable",name:"request_id",variableName:"requestId"},{kind:"Variable",name:"request_payload",variableName:"requestPayload"},{kind:"Variable",name:"request_type",variableName:"requestType"}],concreteType:"LightspeedWebResponse",kind:"LinkedField",name:"lightspeed_web_request",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"payload",storageKey:null},{alias:null,args:null,concreteType:"LightspeedWebRequestDependency",kind:"LinkedField",name:"dependencies",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"value",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"experiments",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"target",storageKey:null}],storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"LSPlatformGraphQLLightspeedRequestQuery",selections:b,type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"LSPlatformGraphQLLightspeedRequestQuery",selections:b},params:{id:"8996647210435532",metadata:{},name:"LSPlatformGraphQLLightspeedRequestQuery",operationKind:"query",text:null}}}();b("relay-runtime").PreloadableQueryRegistry.set(a.params.id,a);e.exports=a}),null);
__d("LSPlatformGraphQLLightspeedRequestQuery",["LSPlatformGraphQLLightspeedRequestQuery.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h!==void 0?h:h=b("LSPlatformGraphQLLightspeedRequestQuery.graphql");g.query=a}),98);
__d("LSPlatformGraphQLLightspeedRequest",["CometRelay","LSPlatformGraphQLLightspeedRequestQuery","XPlatRelayEnvironment"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,e){return d("CometRelay").fetchQuery(d("XPlatRelayEnvironment").getRelayEnvironment(),d("LSPlatformGraphQLLightspeedRequestQuery").query,{deviceId:a,requestId:b,requestPayload:c,requestType:e}).toPromise().then(function(a){a=a==null?void 0:(a=a.viewer)==null?void 0:a.lightspeed_web_request;if(a!=null)return babelHelpers["extends"]({dependencies:void 0},a)})}g["default"]=a}),98);
__d("MAWAbPropsClient",["WAAbPropsToUI"],(function(a,b,c,d,e,f,g){"use strict";var h={contents:d("WAAbPropsToUI").prepareAbPropsForUI()};function a(a){h.contents=a}function b(){return h.contents}g.rewrite=a;g.getAbProps=b}),98);
__d("WAChatState",[],(function(a,b,c,d,e,f){"use strict";a="typing";b="recording_audio";c="idle";d={TYPING:a,RECORDING_AUDIO:b};e={IDLE:c};f.TYPING=a;f.RECORDING_AUDIO=b;f.IDLE=c;f.ACTIVE_CHAT_STATE_TYPE=d;f.IDLE_CHAT_STATE_TYPE=e}),66);
__d("MAWBridgeReceivedChatStateHandler",["I64","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","Promise","ServerTime","WAChatState","emptyFunction"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,e){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.threadJid,"MAWBridgeReceivedChatStateHandler",function(a,f){var g=(i||(i=d("I64"))).of_string(e.senderId),j=i.of_float(3e3+d("ServerTime").getMillis());if(e.state===d("WAChatState").TYPING)return a.typing_indicator.upsert([f,g],{expirationTimestampMs:j,senderId:g,threadKey:f}).then(c("emptyFunction"));else if(e.state===d("WAChatState").IDLE)return a.typing_indicator["delete"](f,g).then(c("emptyFunction"));else return(h||(h=b("Promise"))).resolve()})}g.call=a}),98);
__d("MAWFTSRawStringMatcher",["JSResourceForInteraction","asyncToGeneratorRuntime","nullthrows"],(function(a,b,c,d,e,f,g){"use strict";var h=c("JSResourceForInteraction")("WAFtsMultiLangTokenizer").__setRef("MAWFTSRawStringMatcher"),i=null;function a(){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){i=(yield h.load());return l});return j.apply(this,arguments)}function k(a){return new(c("nullthrows")(i))().tokenize(a)}function l(a,b){a=Array.from(k(a)).filter(function(a){return a.length>1});if(a.length===0)return!1;var c=Array.from(k(b));return a.every(function(a){return c.some(function(b){return b.startsWith(a)})})}g.load=a}),98);
__d("LSThreadPointQueryAndRestoreMessagesWithGroupJID",["LSIssueNewTask"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return d[0]=new c.Map(),d[0].set("wa_jid",a[0]),d[0].set("tam_thread_subtype",c.i64.cast([0,0])),d[1]=d[0].get("wa_jid"),d[2]=c.toJSON(d[0]),c.storedProcedure(b("LSIssueNewTask"),c.i64.to_string(d[1]),c.i64.cast([0,1021]),d[2],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))},function(a){return c.resolve(e)}])}a.__sproc_name__="LSE2EEMessagingMetadataMailboxThreadPointQueryAndRestoreMessagesWithGroupJIDStoredProcedure";a.__tables__=[];e.exports=a}),null);
__d("LSThreadPointQueryAndRestoreMessagesWithGroupJIDStoredProcedure",["LSSynchronousPromise","LSThreadPointQueryAndRestoreMessagesWithGroupJID","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSThreadPointQueryAndRestoreMessagesWithGroupJID"),e.waJid);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("waitForMiActMappingForUniversalSearch",["FBLogger","I64","LSAuthorityLevel","LSFactory","LSThreadPointQueryAndRestoreMessagesWithGroupJIDStoredProcedure","LSThreadPointQueryAndRestoreMessagesWithJIDStoredProcedure","MAWCurrentUser","MAWJids","Promise","ReQL","WAJids","asyncToGeneratorRuntime","emptyFunction","err","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=3e4,k=3e4;function l(a){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){a=(yield d("ReQL").toArrayAsync(a));return a.some(function(a){return a==null||(i||(i=d("I64"))).lt(a.authorityLevel,(i||(i=d("I64"))).of_int32(c("LSAuthorityLevel").AUTHORITATIVE))})===!1});return m.apply(this,arguments)}function n(a,b,c,d){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f,g){f===void 0&&(f=c("emptyFunction"));g===void 0&&(g=c("emptyFunction"));var h=(i||(i=d("I64"))).of_string(d("MAWCurrentUser").getID()),j=d("ReQL").leftJoin(d("ReQL").fromTableAscending(a.tables.participants,["contactId"]).getKeyRange(e.serverThreadKey).filter(function(a){return!(i||(i=d("I64"))).equal(a.contactId,h)}),d("ReQL").fromTableAscending(a.tables.contacts)).map(function(a){a[0];a=a[1];return a}),m=null,n={contents:function(){}};function o(){f(e),n.contents()}if(yield l(j))return o();n.contents=j.subscribe(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){if(b.operation==="delete")return;m!=null&&window.clearTimeout(m);if(yield l(j))return o()});return function(b,c){return a.apply(this,arguments)}}());m=window.setTimeout(function(){n.contents(),g(c("err")("Contacts timed out"))},k)});return o.apply(this,arguments)}function a(a,b){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){var g=d("MAWJids").convertChatJidToIntJid(e),i=d("WAJids").interpretAsGroupJid(e)!=null,k=d("ReQL").fromTableAscending(a.tables.mi_act_mapping_table.index("jid")).getKeyRange(g);e=(yield d("ReQL").firstAsync(k));if(e!=null){yield n(a,e);return(h||(h=b("Promise"))).resolve(e)}var l=null,m=new(h||(h=b("Promise")))(function(d,e){var f={contents:function(){}};function g(a){f.contents(),d(a)}function h(a){f.contents(),e(a)}f.contents=k.subscribe(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b,c){if(c.operation==="delete")return;l!=null&&window.clearTimeout(l);yield n(a,c.value,g,h)});return function(a,b){return c.apply(this,arguments)}}());l=window.setTimeout(function(){h(c("err")("MiActMapping timed out"))},j)});c("promiseDone")(a.runInTransaction(function(a){return i?c("LSThreadPointQueryAndRestoreMessagesWithGroupJIDStoredProcedure")(c("LSFactory")(a),{waJid:g}):c("LSThreadPointQueryAndRestoreMessagesWithJIDStoredProcedure")(c("LSFactory")(a),{waJid:g})},"readwrite",void 0,void 0,f.id+":149"));try{e=(yield m)}catch(a){c("FBLogger")("messenger_web").warn("[FTS] Issue waiting for mi_act_mapping to be ready",a)}return e});return p.apply(this,arguments)}g["default"]=a}),98);
__d("MAWBridgeAddMessageSearchResultHandler",["I64","LSIntEnum","LSMessageSearchType","MAWCurrentUser","MAWFTSRawStringMatcher","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","Promise","ReQL","asyncToGeneratorRuntime","cr:17064","cr:17065","emptyFunction","gkx","isMAWUniversalSearchWithEBEnabled","promiseDone","waitForMiActMappingForUniversalSearch"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(j||(j=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").MESSAGE),l=j.ofNumber(c("LSMessageSearchType").THREAD);function a(a,d,e){return e.length===0?(h||(h=b("Promise"))).resolve():(h||(h=b("Promise"))).all([o(a,d,e),q(a,d,e)]).then(c("emptyFunction"))}function m(a,b){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=(yield d("MAWFTSRawStringMatcher").load());return b.filter(function(b){b=(b=b.msg)==null?void 0:b.content;return b==null?!1:c(a,b)})});return n.apply(this,arguments)}function o(a,b,c){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f){var g=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(e.message_search_queries).getKeyRange(k)));if(g.length===0)return;return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(e,f[0].chatJid,"MAWBridgeAddMessageSearchResultHandler",function(e,h){return b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var c=g.filter(function(a){return(i||(i=d("I64"))).equal(a.threadKeyV2,h)})[0];if(c==null)return;var j=c.query;if(j==null||j.length<2)return;var k=(i||(i=d("I64"))).to_int32(c.resultCount);j=(yield m(j,f));if(j.length===0)return;yield b("cr:17064").addBridgeSearchMsgsToSearchResult(a,e,c.query,j,k+j.length,i.of_string(d("MAWCurrentUser").getID()))})().then(c("emptyFunction"))})});return p.apply(this,arguments)}function q(a,b,c){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f){if(!c("isMAWUniversalSearchWithEBEnabled")())return;var g=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(e.message_search_queries).getKeyRange(l).filter(function(a){return(i||(i=d("I64"))).equal(a.threadKeyV2,i.one)})));if(g==null)return;var h=g.query;if(h==null||h.length<2)return;var j=(yield m(h,f));if(j.length===0)return;if(c("gkx")("11537"))c("promiseDone")(c("waitForMiActMappingForUniversalSearch")(a,f[0].chatJid),function(c){if(c!=null)return b("cr:17065")(a,void 0,c.serverThreadKey,h,j,g)});else{yield c("waitForMiActMappingForUniversalSearch")(a,f[0].chatJid);return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(e,f[0].chatJid,"MAWBridgeAddMessageSearchResultHandler",function(c,d){return b("cr:17065")(a,c,d,h,j,g)})}});return r.apply(this,arguments)}g.call=a}),98);
__d("MAWBridgeCallRestoreNativeOpHandler",["I64","Promise","WAJids","WALogger","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] Unable to call restore native op - echo and protobuf messages are both null"]);j=function(){return a};return a}var k=c("requireDeferred")("LSProtobufRestoreHandler").__setRef("MAWBridgeCallRestoreNativeOpHandler"),l=c("requireDeferred")("LSRestoreEchoBlobs.nop").__setRef("MAWBridgeCallRestoreNativeOpHandler");function a(a,c){var e=c.chatJid,f=c.decryptedMessagesProtobufs,g=c.echoMessages,m=c.hasMoreAfter,n=c.hasMoreBefore,o=c.isPointQuery,p=c.nextMessageTimestampMsAfter,q=c.nextMessageTimestampMsBefore,r=c.referenceTimestamp,s=c.requestId,t=c.taskSource;if(f!=null)return k.load().then(function(a){return a.handleProtobufRestoreHelper(d("WAJids").threadIdForChatJid(e),{convertedProtobufs:f,type:"convertedProtobufs"},n,q,m,p,o,s,r,void 0,void 0,t,e)});else if(g!=null)return l.load().then(function(c){return c(a,0,(i||(i=d("I64"))).zero,g,n,q,m,p,o,d("WAJids").threadIdForChatJid(e),s,r,t,e).then(function(){return(h||(h=b("Promise"))).resolve()})});else d("WALogger").ERROR(j());return(h||(h=b("Promise"))).resolve()}g.call=a}),98);
__d("LSBumpE2EEMetadataThread",["LSCancelTaskByQueueName","LSIssueNewTask"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.sequence([function(e){return d[0]=new c.Map(),d[0].set("bump_timestamp_ms",a[1]),d[0].set("thread_key",a[0]),d[0].set("bumped_by_local_device_send",a[2]),d[0].set("tam_thread_subtype",c.i64.cast([0,0])),d[0].set("wa_jid",void 0),d[0].set("message_ephemeral_duration_in_sec",c.i64.cast([0,0])),d[0].set("message_product_type",void 0),d[0].set("is_message_deletion",!1),d[0].set("is_unbump",a[3]),d[1]=d[0].get("thread_key"),c.storedProcedure(b("LSCancelTaskByQueueName"),c.i64.to_string(d[1]),c.i64.cast([0,389]))},function(e){return d[2]=new c.Map(),d[2].set("bump_timestamp_ms",a[1]),d[2].set("thread_key",a[0]),d[2].set("bumped_by_local_device_send",a[2]),d[2].set("tam_thread_subtype",c.i64.cast([0,0])),d[2].set("wa_jid",void 0),d[2].set("message_ephemeral_duration_in_sec",c.i64.cast([0,0])),d[2].set("message_product_type",void 0),d[2].set("is_message_deletion",!1),d[2].set("is_unbump",a[3]),d[3]=d[2].get("thread_key"),d[4]=c.toJSON(d[2]),c.storedProcedure(b("LSIssueNewTask"),c.i64.to_string(d[3]),c.i64.cast([0,389]),d[4],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))}])},function(a){return c.resolve(e)}])}a.__sproc_name__="LSE2EEMessagingMetadataMailboxBumpE2EEMetadataThreadStoredProcedure";a.__tables__=[];e.exports=a}),null);
__d("LSBumpE2EEMetadataThreadStoredProcedure",["LSBumpE2EEMetadataThread","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSBumpE2EEMetadataThread"),e.threadKey,e.serverAuthoritativeTimestampMs,e.bumpedByLocalDeviceSend,e.isUnbump);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("isStringNullOrWhitespaceOnly",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a==null||a.trim()===""}f["default"]=a}),66);
__d("MAWMemoriesShareXMASnippetFbt",["fbt","isStringNullOrWhitespaceOnly"],(function(a,b,c,d,e,f,g,h){"use strict";a=function(){return h._(/*BTDS*/"You sent a memory")};b=function(a){return c("isStringNullOrWhitespaceOnly")(a)?h._(/*BTDS*/"This user sent a memory"):h._(/*BTDS*/"{user_name} sent a memory",[h._param("user_name",a)])};g.getCurrentUserSendMemoriesShareSnippetFbt=a;g.getParticipantSendMemoriesShareSnippetFbt=b}),226);
__d("MAWPostMentionXMASnippetFbt",["fbt","isStringNullOrWhitespaceOnly"],(function(a,b,c,d,e,f,g,h){"use strict";a=function(a){return c("isStringNullOrWhitespaceOnly")(a)?h._(/*BTDS*/"You tagged this user in your post"):h._(/*BTDS*/"You tagged {user_name} in your post",[h._param("user_name",a)])};b=function(a){return c("isStringNullOrWhitespaceOnly")(a)?h._(/*BTDS*/"This user tagged you in their post"):h._(/*BTDS*/"{user_name} tagged you in their post",[h._param("user_name",a)])};g.getCurrentUserSendPostMentionSnippetFbt=a;g.getParticipantSendPostMentionSnippetFbt=b}),226);
__d("getMAWLocalizedBumpMsgReplySnippet",["fbt","FBLogger"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a,b,d){switch(a){case"currentUserBumpedOwnMessage":return h._(/*BTDS*/"You bumped your message");case"currentUserBumpedMessage":return d!=null?h._(/*BTDS*/"You bumped {name}'s message",[h._param("name",d)]):h._(/*BTDS*/"You bumped a message");case"participantBumpedCurrentUserMessage":return h._(/*BTDS*/"{sender_name} bumped your message",[h._param("sender_name",b)]);case"participantBumpedOwnMessage":return h._(/*BTDS*/"{name} bumped their message",[h._param("name",b)]);case"participantBumpedParticipantMessage":return d!=null?h._(/*BTDS*/"{sender_name} bumped {reply_source_sender_name}'s message",[h._param("sender_name",b),h._param("reply_source_sender_name",d)]):h._(/*BTDS*/"{user_name} bumped a message",[h._param("user_name",b)]);default:a;c("FBLogger")("messenger_web").mustfix("unexpected bump type %s",a);return""}}g["default"]=a}),226);
__d("getMAWLocalizedFallbackMsgSnippet",["fbt","FBLogger"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a){switch(a){case"userSendEncryptedMessage":return h._(/*BTDS*/"Waiting for this message. This might take a while.");case"currentUserSendUnrenderableMessage":return h._(/*BTDS*/"You sent a message but this browser can't show it.");case"participantSendUnrenderableMessage":return h._(/*BTDS*/"You received a message but this browser can't show it.");case"currentUserSendLiveLocationFallback":return h._(/*BTDS*/"You sent a live location. Use the Messenger mobile app to see it.");case"currentUserSendLocationFallback":return h._(/*BTDS*/"You sent a pinned location. Use the Messenger mobile app to see it.");case"participantSendLiveLocation":return h._(/*BTDS*/"You received a live location. Use the Messenger mobile app to see it.");case"participantSendLocation":return h._(/*BTDS*/"You received a pinned location. Use the Messenger mobile app to see it.");case"currentUserSendPollCreationFallback":return h._(/*BTDS*/"You sent a poll. Use the Messenger mobile app to see it.");case"participantSendPollCreation":return h._(/*BTDS*/"You received a poll. Use the Messenger mobile app to see it.");case"currentUserSendContactShareFallback":return h._(/*BTDS*/"You sent a contact card. Use the Messenger mobile app to see it.");case"participantSendContactShareFallback":return h._(/*BTDS*/"You received a contact card. Use the Messenger mobile app to see it.");case"currentUserSendStoryMentionFallback":return h._(/*BTDS*/"You sent a story mention. Use the Messenger mobile app to see it.");case"participantSendStoryMentionFallback":return h._(/*BTDS*/"You received a story mention. Use the Messenger mobile app to see it.");case"currentUserSendPostMentionFallback":return h._(/*BTDS*/"You sent a post mention. Use the Messenger mobile app to see it.");case"participantSendPostMentionFallback":return h._(/*BTDS*/"You received a post mention. Use the Messenger mobile app to see it.");case"currentUserSendNoteMentionFallback":return h._(/*BTDS*/"You mentioned someone in your note. Use the Messenger mobile app to see it.");case"participantSendNoteMentionFallback":return h._(/*BTDS*/"Someone mentioned you in their note. Use the Messenger mobile app to see it.");case"messengerMemoryEncryptedMessageFallback":return h._(/*BTDS*/"To see this memory, open the Messenger app on your phone.");case"metaAISendMessageFallback":return h._(/*BTDS*/"You received a message from Meta AI. Use the Messenger mobile app to see it.");case"currentUserSendBumpMessageFallback":return h._(/*BTDS*/"You sent a bumped message. Use the Messenger mobile app to see it.");case"participantSendBumpMessageFallback":return h._(/*BTDS*/"You received a bumped message. Use the Messenger mobile app to see it.");case"participantSendStickerReceiverFetchMessageFallback":return h._(/*BTDS*/"You received a sticker. Use the Messenger mobile app to see it.");case"currentUserSendStickerReceiverFetchMessageFallback":return h._(/*BTDS*/"You sent a sticker. Use the Messenger mobile app to see it.");case"userSendUnavailableMessage":return h._(/*BTDS*/"Message sent before this device was added to this conversation. This message cannot be delivered.");case"participantSendBumpMessageOriginalUnavailable":return h._(/*BTDS*/"Message unavailable.");case"unavailableStory":return h._(/*BTDS*/"Story unavailable");case"viewOncePhotoMessageFallback":return h._(/*BTDS*/"This photo can only be viewed once. Use the mobile app to view.");case"viewOnceVideoMessageFallback":return h._(/*BTDS*/"This video can only be viewed once. Use the mobile app to view.");default:a;c("FBLogger")("messenger_web").mustfix("unexpected content fallback type %s",a);return""}}g["default"]=a}),226);
__d("MAWAdminMsg",["fbt","MAWLocalizationType","MAWMemoriesShareXMASnippetFbt","MAWPostMentionXMASnippetFbt","getMAWLocalizedBumpMsgReplySnippet","getMAWLocalizedFallbackMsgSnippet"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a,b,e){switch(e){case d("MAWLocalizationType").LOCALIZATION_TYPE.EMPTY_SNIPPET:return"";case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CREATED_GROUP:return h._(/*BTDS*/"You created this group").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CREATED_GROUP:return h._(/*BTDS*/"{actor} created this group",[h._param("actor",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_CREATED_GROUP:return h._(/*BTDS*/"This group was created.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_NAMED_GROUP:return h._(/*BTDS*/"You named the group {custom group name}.",[h._param("custom group name",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_NAMED_GROUP:return h._(/*BTDS*/"The group name was set to {custom group name}.",[h._param("custom group name",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_NAMED_GROUP:return h._(/*BTDS*/"{actor} named the group {custom group name}.",[h._param("actor",b[0]),h._param("custom group name",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_ADDED_DEVICE:return h._(/*BTDS*/"You added a new device to this conversation.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_DEVICE:return h._(/*BTDS*/"{actor} added a new device to this conversation.",[h._param("actor",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_REMOVED_DEVICE:return h._(/*BTDS*/"You removed a device from this conversation.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_REMOVED_DEVICE:return h._(/*BTDS*/"{actor} removed a device from this conversation.",[h._param("actor",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_UPDATED_DEVICE:return h._(/*BTDS*/"Your key has changed.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_UPDATED_DEVICE:return h._(/*BTDS*/"{user_name}'s key has changed.",[h._param("user_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_ADDED_ONE_PARTICIPANT:return h._(/*BTDS*/"You added {user_name} to the group. They can only see messages sent after they were added.",[h._param("user_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_ADDED_TWO_PARTICIPANTS:return h._(/*BTDS*/"You added {user_name_1} and {user_name_2} to the group. They can only see messages sent after they were added.",[h._param("user_name_1",b[0]),h._param("user_name_2",b[1])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_ADDED_MORE_THAN_TWO_PARTICIPANTS:return h._(/*BTDS*/"You added {user_name} and {number of participants} others to the group. They can only see messages sent after they were added.",[h._param("user_name",b[0]),h._param("number of participants",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_YOU:return h._(/*BTDS*/"{actor} added you to the group. You can only see messages sent after you were added.",[h._param("actor",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_YOU_AND_ONE_USER:return h._(/*BTDS*/"{actor} added you and {user_name} to the group. You can only see messages sent after you were added.",[h._param("actor",b[0]),h._param("user_name",b[1])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_YOU_AND_MORE_THAN_ONE_USERS:return h._(/*BTDS*/"{actor} added you and {number of participants} others to the group. You can only see messages sent after you were added.",[h._param("actor",b[0]),h._param("number of participants",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_ONE_USER:return h._(/*BTDS*/"{actor} added {user_name} to the group. They can only see messages sent after they were added.",[h._param("actor",b[0]),h._param("user_name",b[1])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_TWO_USER:return h._(/*BTDS*/"{actor} added {user_name_1} and {user_name_2} to the group. They can only see messages sent after they were added.",[h._param("actor",b[0]),h._param("user_name_1",b[1]),h._param("user_name_2",b[2])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_MORE_THAN_TWO_USER:return h._(/*BTDS*/"{actor} added {user_name_1} and {number of participants} others to the group. They can only see messages sent after they were added.",[h._param("actor",b[0]),h._param("user_name_1",b[1]),h._param("number of participants",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_REMOVED_ONE_PARTICIPANT:return h._(/*BTDS*/"You removed {user_name} from the group.",[h._param("user_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_REMOVED_YOU:return h._(/*BTDS*/"{user_name_1} removed you from the group.",[h._param("user_name_1",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_REMOVED_ONE_USER:return h._(/*BTDS*/"{user_name_1} removed {user_name_2} from the group.",[h._param("user_name_1",b[0]),h._param("user_name_2",b[1])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_LEFT_GROUP:return h._(/*BTDS*/"{name} left the group.",[h._param("name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_LEFT_GROUP:return h._(/*BTDS*/"You left the group.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_GOT_PROMOTED:return h._(/*BTDS*/"{user_name} is now an admin.",[h._param("user_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_GOT_DEMOTED:return h._(/*BTDS*/"{user_name} is no longer an admin.",[h._param("user_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_GOT_PROMOTED:return h._(/*BTDS*/"You are now an admin.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_GOT_DEMOTED:return h._(/*BTDS*/"You are no longer an admin.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_PROMOTED_YOU:return h._(/*BTDS*/"{actor} added you as a group admin.",[h._param("actor",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_DEMOTED_YOU:return h._(/*BTDS*/"{actor} removed you as a group admin.",[h._param("actor",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_PROMOTED_PARTICIPANT:return h._(/*BTDS*/"{actor} added {target} as a group admin.",[h._param("actor",b[0]),h._param("target",b[1])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_DEMOTED_PARTICIPANT:return h._(/*BTDS*/"{actor} removed {target} as a group admin.",[h._param("actor",b[0]),h._param("target",b[1])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_DEMOTED_PARTICIPANT:return h._(/*BTDS*/"You removed {target} as a group admin.",[h._param("target",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_PROMOTED_PARTICIPANT:return h._(/*BTDS*/"You added {target} as a group admin.",[h._param("target",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SELF_DEMOTED:return h._(/*BTDS*/"{actor} removed themself as a group admin.",[h._param("actor",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SELF_DEMOTED:return h._(/*BTDS*/"You removed yourself as a group admin.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_TEXT:return h._(/*BTDS*/"You").toString()+": "+a[0];case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_TEXT:return a[0];case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_TEXT_IN_GROUP:return b[0]+": "+a[0];case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_IMAGE:return h._(/*BTDS*/"You sent a photo.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_VIDEO:return h._(/*BTDS*/"You sent a video.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_AUDIO:return h._(/*BTDS*/"You sent a voice message.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_GIF:return h._(/*BTDS*/"You sent a GIF.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_STICKER:return h._(/*BTDS*/"You sent a sticker.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_ATTACHMENT:return h._(/*BTDS*/"You sent an attachment.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SENT_POST:return h._(/*BTDS*/"You sent a post").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SENT_REEL:return h._(/*BTDS*/"You sent a reel").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SENT_STORY:return h._(/*BTDS*/"You sent a story.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_CONTACT_SHARE:case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_AI_CONTACT_SHARE:return h._(/*BTDS*/"You sent a contact card.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SENT_EVENT:return h._(/*BTDS*/"You sent an event.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SENT_STORY_HIGHLIGHT:return h._(/*BTDS*/"You sent a story highlight").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_MENTIONED_STORY_IG:return h._(/*BTDS*/"You mentioned \u0040{user} in your story.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_RECEIVED_STORY_MENTION_IG:return h._(/*BTDS*/"Mentioned you in their story").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_STORY_MENTION:return h._(/*BTDS*/"You mentioned this user in your story").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_STORY_MENTION:return h._(/*BTDS*/"{user} mentioned you in their story",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_IMAGE:return h._(/*BTDS*/"{user} sent a photo.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_VIDEO:return h._(/*BTDS*/"{user} sent a video.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_AUDIO:return h._(/*BTDS*/"{user} sent a voice message.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_GIF:return h._(/*BTDS*/"{user} sent a GIF.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_STICKER:return h._(/*BTDS*/"{user} sent a sticker.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SENT_POST:return h._(/*BTDS*/"Sent a post").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SENT_REEL:return h._(/*BTDS*/"Sent a reel").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_ATTACHMENT:return h._(/*BTDS*/"{user} sent an attachment.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_POST_MENTION:return d("MAWPostMentionXMASnippetFbt").getCurrentUserSendPostMentionSnippetFbt(b[1]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_POST_MENTION:return d("MAWPostMentionXMASnippetFbt").getParticipantSendPostMentionSnippetFbt(b[0]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_LOCATION:return h._(/*BTDS*/"You sent a location.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_LOCATION:return h._(/*BTDS*/"{user} sent a location.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SENT_STORY:return h._(/*BTDS*/"{user} sent a story.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_CONTACT_SHARE:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_AI_CONTACT_SHARE:return h._(/*BTDS*/"{user} sent a contact card.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SENT_EVENT:return h._(/*BTDS*/"{user} sent an event.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SENT_STORY_HIGHLIGHT:return h._(/*BTDS*/"Sent a story highlight").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNAVAILABLE_SNIPPET:return h._(/*BTDS*/"Unavailable message").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.TWO_USERS_CONNECTED:return h._(/*BTDS*/"You can now message and call each other and see info like active status and when you have read messages.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.TWO_USERS_CONNECTED_ONE_MSPLIT:return h._(/*BTDS*/"You can now message and call each other.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.REACHABILITY_ERROR:return h._(/*BTDS*/"This account can't receive your message because they don't allow new message requests from everyone.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_TURNED_ON_SECONDS:return h._(/*BTDS*/"_j{\"*\":\"Disappearing messages were turned on. Messages will disappear {number} seconds after they're sent.\",\"_1\":\"Disappearing messages were turned on. Messages will disappear 1 second after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_TURNED_ON_MINUTES:return h._(/*BTDS*/"_j{\"*\":\"Disappearing messages were turned on. Messages will disappear {number} minutes after they're sent.\",\"_1\":\"Disappearing messages were turned on. Messages will disappear 1 minute after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_TURNED_ON_HOURS:return h._(/*BTDS*/"_j{\"*\":\"Disappearing messages were turned on. Messages will disappear {number} hours after they're sent.\",\"_1\":\"Disappearing messages were turned on. Messages will disappear 1 hour after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_TURNED_ON_DAYS:return h._(/*BTDS*/"_j{\"*\":\"Disappearing messages were turned on. Messages will disappear {number} days after they're sent.\",\"_1\":\"Disappearing messages were turned on. Messages will disappear 1 day after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_TURNED_ON_SECONDS:return h._(/*BTDS*/"_j{\"*\":\"You turned on disappearing messages. Messages will disappear {number} seconds after they're sent.\",\"_1\":\"You turned on disappearing messages. Messages will disappear 1 second after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_TURNED_ON_MINUTES:return h._(/*BTDS*/"_j{\"*\":\"You turned on disappearing messages. Messages will disappear {number} minutes after they're sent.\",\"_1\":\"You turned on disappearing messages. Messages will disappear 1 minute after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_TURNED_ON_HOURS:return h._(/*BTDS*/"_j{\"*\":\"You turned on disappearing messages. Messages will disappear {number} hours after they're sent.\",\"_1\":\"You turned on disappearing messages. Messages will disappear 1 hour after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_TURNED_ON_DAYS:return h._(/*BTDS*/"_j{\"*\":\"You turned on disappearing messages. Messages will disappear {number} days after they're sent.\",\"_1\":\"You turned on disappearing messages. Messages will disappear 1 day after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_TURNED_ON_SECONDS:return h._(/*BTDS*/"_j{\"*\":\"{user} turned on disappearing messages. Messages will disappear {number} seconds after they're sent.\",\"_1\":\"{user} turned on disappearing messages. Messages will disappear 1 second after they're sent.\"}",[h._plural(parseInt(a[0],10),"number"),h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_TURNED_ON_MINUTES:return h._(/*BTDS*/"_j{\"*\":\"{user} turned on disappearing messages. Messages will disappear {number} minutes after they're sent.\",\"_1\":\"{user} turned on disappearing messages. Messages will disappear 1 minute after they're sent.\"}",[h._plural(parseInt(a[0],10),"number"),h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_TURNED_ON_HOURS:return h._(/*BTDS*/"_j{\"*\":\"{user} turned on disappearing messages. Messages will disappear {number} hours after they're sent.\",\"_1\":\"{user} turned on disappearing messages. Messages will disappear 1 hour after they're sent.\"}",[h._plural(parseInt(a[0],10),"number"),h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_TURNED_ON_DAYS:return h._(/*BTDS*/"_j{\"*\":\"{user} turned on disappearing messages. Messages will disappear {number} days after they're sent.\",\"_1\":\"{user} turned on disappearing messages. Messages will disappear 1 day after they're sent.\"}",[h._plural(parseInt(a[0],10),"number"),h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_CHANGE_SECONDS:return h._(/*BTDS*/"_j{\"*\":\"Disappearing messages were changed. Messages will disappear {number} seconds after they're sent.\",\"_1\":\"Disappearing messages were changed. Messages will disappear 1 second after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_CHANGE_MINUTES:return h._(/*BTDS*/"_j{\"*\":\"Disappearing messages were changed. Messages will disappear {number} minutes after they're sent.\",\"_1\":\"Disappearing messages were changed. Messages will disappear 1 minute after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_CHANGE_HOURS:return h._(/*BTDS*/"_j{\"*\":\"Disappearing messages were changed. Messages will disappear {number} hours after they're sent.\",\"_1\":\"Disappearing messages were changed. Messages will disappear 1 hour after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_CHANGE_DAYS:return h._(/*BTDS*/"_j{\"*\":\"Disappearing messages were changed. Messages will disappear {number} days after they're sent.\",\"_1\":\"Disappearing messages were changed. Messages will disappear 1 day after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_THREAD_ADMIN_MESSAGE:return h._(/*BTDS*/"New messages and calls are secured with end-to-end encryption. Only people in this chat can read, listen to, or share them.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.DUAL_THREAD_CUTOVER_ADMIN_MESSAGE:return h._(/*BTDS*/"Your other chat has been archived.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_IGD_THREAD_ADMIN_MESSAGE:return h._(/*BTDS*/"Instagram upgraded the security of this chat. Messages and calls will be secured with end-to-end encryption.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_ROLLBACK_ADMIN_MESSAGE:return h._(/*BTDS*/"Previous messages may be in your other chat.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOUR_DEVICE_ICDC_ALERT_ADMIN_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.ICDC_ALERT_ADMIN_MESSAGE:return h._(/*BTDS*/"An unrecognized device was detected on your account. We recommend comparing your keys with your contacts to secure the chat.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_CHANGE_SECONDS:return h._(/*BTDS*/"_j{\"*\":\"You changed disappearing messages. Messages will disappear {number} seconds after they're sent.\",\"_1\":\"You changed disappearing messages. Messages will disappear 1 second after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_CHANGE_MINUTES:return h._(/*BTDS*/"_j{\"*\":\"You changed disappearing messages. Messages will disappear {number} minutes after they're sent.\",\"_1\":\"You changed disappearing messages. Messages will disappear 1 minute after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_CHANGE_HOURS:return h._(/*BTDS*/"_j{\"*\":\"You changed disappearing messages. Messages will disappear {number} hours after they're sent.\",\"_1\":\"You changed disappearing messages. Messages will disappear 1 hour after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_CHANGE_DAYS:return h._(/*BTDS*/"_j{\"*\":\"You changed disappearing messages. Messages will disappear {number} days after they're sent.\",\"_1\":\"You changed disappearing messages. Messages will disappear 1 day after they're sent.\"}",[h._plural(parseInt(a[0],10),"number")]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_CHANGE_SECONDS:return h._(/*BTDS*/"_j{\"*\":\"{user} changed disappearing messages. Messages will disappear {number} seconds after they're sent.\",\"_1\":\"{user} changed disappearing messages. Messages will disappear 1 second after they're sent.\"}",[h._plural(parseInt(a[0],10),"number"),h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_CHANGE_MINUTES:return h._(/*BTDS*/"_j{\"*\":\"{user} changed disappearing messages. Messages will disappear {number} minutes after they're sent.\",\"_1\":\"{user} changed disappearing messages. Messages will disappear 1 minute after they're sent.\"}",[h._plural(parseInt(a[0],10),"number"),h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_CHANGE_HOURS:return h._(/*BTDS*/"_j{\"*\":\"{user} changed disappearing messages. Messages will disappear {number} hours after they're sent.\",\"_1\":\"{user} changed disappearing messages. Messages will disappear 1 hour after they're sent.\"}",[h._plural(parseInt(a[0],10),"number"),h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_CHANGE_DAYS:return h._(/*BTDS*/"_j{\"*\":\"{user} changed disappearing messages. Messages will disappear {number} days after they're sent.\",\"_1\":\"{user} changed disappearing messages. Messages will disappear 1 day after they're sent.\"}",[h._plural(parseInt(a[0],10),"number"),h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_TURNED_OFF:return h._(/*BTDS*/"Disappearing messages were turned off.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_TURNED_OFF:return h._(/*BTDS*/"You turned off disappearing messages.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_TURNED_OFF:return h._(/*BTDS*/"{user} turned off disappearing messages.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.EPHEMERAL_SETTINGS_AUTO_RESET:return h._(/*BTDS*/"Disappearing messages were turned off. Turn them back on to have messages disappear 24 hours after they're sent.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_UNSENT_MESSAGE:return h._(/*BTDS*/"You unsent a message").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_UNSENT_MESSAGE:return h._(/*BTDS*/"{first name} unsent a message",[h._param("first name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_REACT_MESSAGE_IN_GROUP:return h._(/*BTDS*/"{name} reacted {emoji} to your message",[h._param("name",b[0]),h._param("emoji",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_REACT_MESSAGE:return h._(/*BTDS*/"Reacted {emoji} to your message",[h._param("emoji",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_TAKE_SCREENSHOT:return h._(/*BTDS*/"A screenshot was taken.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_RECORD_SCREEN:return h._(/*BTDS*/"Current screen was recorded.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_TAKE_SCREENSHOT:return h._(/*BTDS*/"You took a screenshot.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_RECORD_SCREEN:return h._(/*BTDS*/"You recorded the screen.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_TAKE_SCREENSHOT:return h._(/*BTDS*/"{user} took a screenshot.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_RECORD_SCREEN:return h._(/*BTDS*/"{user} recorded the screen.",[h._param("user",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.E2EE_THREAD_DESCRIPTION:return h._(/*BTDS*/"Messages and calls are secured with end-to-end encryption.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_ADDED_YOU:return h._(/*BTDS*/"Someone added you to the group. You can only see messages sent after you were added.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_ADDED_YOU_AND_ONE_USER:return h._(/*BTDS*/"Someone added you and {user_name} to the group. You can only see messages sent after you were added.",[h._param("user_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_ADDED_YOU_AND_MORE_THAN_ONE_USER:return h._(/*BTDS*/"Someone added you and {number of participants} others to the group. You can only see messages sent after you were added.",[h._param("number of participants",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_ADDED_ONE_USER:return h._(/*BTDS*/"Someone added {user_name} to the group. They can only see messages sent after they were added.",[h._param("user_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_ADDED_TWO_USER:return h._(/*BTDS*/"Someone added {user_name_1} and {user_name_2} to the group. They can only see messages sent after they were added.",[h._param("user_name_1",b[0]),h._param("user_name_2",b[1])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_ADDED_MORE_THAN_TWO_USER:return h._(/*BTDS*/"Someone added {user_name_1} and {number of participants} others to the group. They can only see messages sent after they were added.",[h._param("user_name_1",b[0]),h._param("number of participants",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.DEBUG_MSG:return a[0];case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_MISSED_AUDIO_CALL:e=b[0];return e!=null?h._(/*BTDS*/"You missed an audio call from {user_name}.",[h._param("user_name",e)]).toString():h._(/*BTDS*/"You missed a call from a contact.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_MISSED_AUDIO_CALL:e=b[0];return e!=null?h._(/*BTDS*/"{user_name} missed your audio call.",[h._param("user_name",e)]).toString():h._(/*BTDS*/"A contact missed your audio call.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_MISSED_VIDEO_CALL:e=b[0];return e!=null?h._(/*BTDS*/"You missed a video call from {user_name}.",[h._param("user_name",e)]).toString():h._(/*BTDS*/"You missed a video call from a contact.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_MISSED_VIDEO_CALL:e=b[0];return e!=null?h._(/*BTDS*/"{user} missed your video call.",[h._param("user",e)]).toString():h._(/*BTDS*/"A contact missed your video chat.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_AUDIO_CALLED:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_AUDIO_CALLED:return h._(/*BTDS*/"The audio call ended.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_VIDEO_CALLED:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_VIDEO_CALLED:return h._(/*BTDS*/"The video call ended.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_STARTED_GROUP_AUDIO_CALL:return h._(/*BTDS*/"You started a call.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_STARTED_GROUP_AUDIO_CALL:e=b[0];return e!=null?h._(/*BTDS*/"{user} started a call.",[h._param("user",e)]).toString():h._(/*BTDS*/"A contact started a call.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_STARTED_GROUP_VIDEO_CALL:return h._(/*BTDS*/"You started a video chat.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_STARTED_GROUP_VIDEO_CALL:e=b[0];return e!=null?h._(/*BTDS*/"{user} started a video chat.",[h._param("user",e)]).toString():h._(/*BTDS*/"A contact started a video chat.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CUSTOMIZE_HOTLIKE:return h._(/*BTDS*/"You set the quick reaction to {emoji}.",[h._param("emoji",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CUSTOMIZE_HOTLIKE:return h._(/*BTDS*/"{participant_name} set the quick reaction to {emoji}.",[h._param("participant_name",b[0]),h._param("emoji",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CUSTOMIZE_NICKNAME:return h._(/*BTDS*/"You set the nickname for {participant_name} to {nickname}.",[h._param("participant_name",b[0]),h._param("nickname",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CUSTOMIZE_THEME:return a[1]!=null?h._(/*BTDS*/"You changed the theme to {theme_name} {theme_emoji}.",[h._param("theme_name",a[0]),h._param("theme_emoji",a[1])]).toString():h._(/*BTDS*/"You changed the theme to {theme_name}.",[h._param("theme_name",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CUSTOMIZE_AI_THEME:return a[1]!=null?h._(/*BTDS*/"You changed the theme using AI to \"{theme_name}\" {theme_emoji}.",[h._param("theme_name",a[0]),h._param("theme_emoji",a[1])]).toString():h._(/*BTDS*/"You changed the theme using AI to \"{theme_name}\".",[h._param("theme_name",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CUSTOMIZE_PHOTO:return h._(/*BTDS*/"You changed the group photo.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_PINNED_MESSAGE:return h._(/*BTDS*/"You pinned a message.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_UNPINNED_MESSAGE:return h._(/*BTDS*/"You unpinned a message.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.E2EE_THREAD_UK_OSA_ADMIN_MESSAGE:return h._(/*BTDS*/"You're messaging {participant_name} for the first time. If you ever feel uncomfortable, there are",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CUSTOMIZE_PARTICIPANT_NICKNAME:return h._(/*BTDS*/"{participant_name_1} set the nickname for {participant_name_2} to {nickname}.",[h._param("participant_name_1",b[0]),h._param("participant_name_2",b[1]),h._param("nickname",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CLEAR_PARTICIPANT_NICKNAME:return h._(/*BTDS*/"{participant_name_1} cleared the nickname for {participant_name_2}.",[h._param("participant_name_1",b[0]),h._param("participant_name_2",b[1])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CUSTOMIZE_THEME:return a[1]!=null?h._(/*BTDS*/"{participant_name} changed the theme to {theme_name} {theme_emoji}.",[h._param("participant_name",b[0]),h._param("theme_name",a[0]),h._param("theme_emoji",a[1])]).toString():h._(/*BTDS*/"{participant_name} changed the theme to {theme_name}.",[h._param("participant_name",b[0]),h._param("theme_name",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CUSTOMIZE_AI_THEME:return a[1]!=null?h._(/*BTDS*/"{participant_name} changed the theme using AI to \"{theme_name}\" {theme_emoji}.",[h._param("participant_name",b[0]),h._param("theme_name",a[0]),h._param("theme_emoji",a[1])]).toString():h._(/*BTDS*/"{participant_name} changed the theme using AI to \"{theme_name}\".",[h._param("participant_name",b[0]),h._param("theme_name",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CREATED_POLL:return h._(/*BTDS*/"You created a poll: {poll_name}.",[h._param("poll_name",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CREATED_POLL:return h._(/*BTDS*/"{participant_name} created a poll: {poll_name}.",[h._param("participant_name",b[0]),h._param("poll_name",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_POLL_OPTION:return h._(/*BTDS*/"{participant_name} added \"{poll_option}\" to the poll.",[h._param("participant_name",b[0]),h._param("poll_option",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_POLL_VOTE:return h._(/*BTDS*/"{participant_name} voted for \"{poll_option}\" in the poll.",[h._param("participant_name",b[0]),h._param("poll_option",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_ADDED_POLL_OPTION:return h._(/*BTDS*/"You added \"{poll_option}\" to the poll.",[h._param("poll_option",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_ADDED_POLL_VOTE:return h._(/*BTDS*/"You voted for \"{poll_option}\" in the poll.",[h._param("poll_option",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_MULTIPLE_POLL_OPTIONS:e=parseInt(a[1],10)-1;return h._(/*BTDS*/"_j{\"*\":\"{participant_name} added \\\"{poll_option}\\\" and {num_other_options} other options to the poll.\",\"_1\":\"{participant_name} added \\\"{poll_option}\\\" and {num_other_options} other option to the poll.\"}",[h._plural(e),h._param("participant_name",b[0]),h._param("poll_option",a[0]),h._param("num_other_options",e)]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_ADDED_MULTIPLE_POLL_OPTIONS:e=parseInt(a[1],10)-1;return h._(/*BTDS*/"_j{\"*\":\"You added \\\"{poll_option}\\\" and {num_other_options} other options to the poll.\",\"_1\":\"You added \\\"{poll_option}\\\" and {num_other_options} other option to the poll.\"}",[h._plural(e),h._param("poll_option",a[0]),h._param("num_other_options",e)]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_ADDED_MULTIPLE_POLL_VOTES:e=parseInt(a[1],10)-1;return h._(/*BTDS*/"_j{\"*\":\"You voted for \\\"{poll_option}\\\" and {num_other_votes} other options in the poll.\",\"_1\":\"You voted for \\\"{poll_option}\\\" and {num_other_votes} other option in the poll.\"}",[h._plural(e),h._param("poll_option",a[0]),h._param("num_other_votes",e)]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_ADDED_MULTIPLE_POLL_VOTES:e=parseInt(a[1],10)-1;return h._(/*BTDS*/"_j{\"*\":\"{participant_name} voted for \\\"{poll_option}\\\" and {num_other_votes} other options in the poll.\",\"_1\":\"{participant_name} voted for \\\"{poll_option}\\\" and {num_other_votes} other option in the poll.\"}",[h._plural(e),h._param("participant_name",b[0]),h._param("poll_option",a[0]),h._param("num_other_votes",e)]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_REMOVED_MULTIPLE_POLL_VOTES:e=parseInt(a[1],10)-1;return h._(/*BTDS*/"_j{\"*\":\"You removed your vote for \\\"{poll_option}\\\" and {num_other_removed_votes} other options in the poll.\",\"_1\":\"You removed your vote for \\\"{poll_option}\\\" and {num_other_removed_votes} other option in the poll.\"}",[h._plural(e),h._param("poll_option",a[0]),h._param("num_other_removed_votes",e)]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_REMOVED_MULTIPLE_POLL_VOTES:e=parseInt(a[1],10)-1;return h._(/*BTDS*/"_j{\"*\":\"{participant_name} removed their vote for \\\"{poll_option}\\\" and {num_other_removed_votes} other options in the poll.\",\"_1\":\"{participant_name} removed their vote for \\\"{poll_option}\\\" and {num_other_removed_votes} other option in the poll.\"}",[h._plural(e),h._param("participant_name",b[0]),h._param("poll_option",a[0]),h._param("num_other_removed_votes",e)]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_REMOVED_POLL_VOTE:return h._(/*BTDS*/"You removed your vote for \"{poll_option}\" in the poll.",[h._param("poll_option",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_REMOVED_POLL_VOTE:return h._(/*BTDS*/"{participant_name} removed their vote for \"{poll_option}\" in the poll.",[h._param("participant_name",b[0]),h._param("poll_option",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_MIXED_POLL_UPDATE:return h._(/*BTDS*/"{participant_name}'s poll has multiple updates.",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_MIXED_POLL_UPDATE:return h._(/*BTDS*/"Your poll has multiple updates.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CHANGED_POLL_VOTE:return h._(/*BTDS*/"You changed your vote to \"{poll_option}\" in the poll.",[h._param("poll_option",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CHANGED_POLL_VOTE:return h._(/*BTDS*/"{participant_name} changed their vote to \"{poll_option}\" in the poll.",[h._param("participant_name",b[0]),h._param("poll_option",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CHANGED_MULTIPLE_POLL_VOTES:e=parseInt(a[1],10)-1;return h._(/*BTDS*/"_j{\"*\":\"You changed your vote to \\\"{poll_option}\\\" and {num_other_changed_votes} other options in the poll.\",\"_1\":\"You changed your vote to \\\"{poll_option}\\\" and {num_other_changed_votes} other option in the poll.\"}",[h._plural(e),h._param("poll_option",a[0]),h._param("num_other_changed_votes",e)]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CHANGED_MULTIPLE_POLL_VOTES:e=parseInt(a[1],10)-1;return h._(/*BTDS*/"_j{\"*\":\"{participant_name} changed their vote to \\\"{poll_option}\\\" and {num_other_changed_votes} other options in the poll.\",\"_1\":\"{participant_name} changed their vote to \\\"{poll_option}\\\" and {num_other_changed_votes} other option in the poll.\"}",[h._plural(e),h._param("participant_name",b[0]),h._param("poll_option",a[0]),h._param("num_other_changed_votes",e)]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.POLL_UNSENT_UPDATE:return h._(/*BTDS*/"A poll has been unsent.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CUSTOMIZE_PHOTO:return h._(/*BTDS*/"{participant_name} changed the group photo.",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_PINNED_MESSAGE:return h._(/*BTDS*/"{participant_name} pinned a message.",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_UNPINNED_MESSAGE:return h._(/*BTDS*/"{participant_name} unpinned a message.",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CUSTOMIZE_CURRENT_USER_NICKNAME:return h._(/*BTDS*/"{participant_name} set your nickname to {nickname}.",[h._param("participant_name",b[0]),h._param("nickname",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SET_OWN_NICKNAME:return h._(/*BTDS*/"You set your nickname to {nickname}.",[h._param("nickname",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CLEAR_OWN_NICKNAME:return h._(/*BTDS*/"You cleared your nickname.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CLEAR_PARTICIPANT_NICKNAME:return h._(/*BTDS*/"You cleared the nickname for {participant_name}.",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SET_OWN_NICKNAME:return h._(/*BTDS*/"{participant_name} set their own nickname to {nickname}.",[h._param("participant_name",b[0]),h._param("nickname",a[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CLEAR_OWN_NICKNAME:return h._(/*BTDS*/"{participant_name} cleared their own nickname.",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CLEAR_CURRENT_USER_NICKNAME:return h._(/*BTDS*/"{participant_name} cleared your nickname.",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SET_ADD_MODE_ADMIN_ONLY:return h._(/*BTDS*/"You changed who can add people to admins only.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SET_ADD_MODE_ALL_MEMBERS:return h._(/*BTDS*/"You changed who can add people to all members.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SET_ADD_MODE_ADMIN_ONLY:return h._(/*BTDS*/"{participant_name} changed who can add people to admins only.",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SET_ADD_MODE_ALL_MEMBERS:return h._(/*BTDS*/"{participant_name} changed who can add people to all members.",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.SERVER_SET_ADD_MODE_ADMIN_ONLY:return h._(/*BTDS*/"A chat admin changed who can add people to all members.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.SERVER_SET_ADD_MODE_ALL_MEMBERS:return h._(/*BTDS*/"A chat admin changed who can add people to admins only.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_BUMPED_OWN_MESSAGE:return c("getMAWLocalizedBumpMsgReplySnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_BUMPED_OWN_MESSAGE,null,null).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_BUMPED_MESSAGE:return c("getMAWLocalizedBumpMsgReplySnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_BUMPED_MESSAGE,null,b[0]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_BUMPED_CURRENT_USER_MESSAGE:return c("getMAWLocalizedBumpMsgReplySnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_BUMPED_CURRENT_USER_MESSAGE,b[0],null).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_BUMPED_OWN_MESSAGE:return c("getMAWLocalizedBumpMsgReplySnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_BUMPED_OWN_MESSAGE,b[0],null).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_BUMPED_PARTICIPANT_MESSAGE:return c("getMAWLocalizedBumpMsgReplySnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_BUMPED_PARTICIPANT_MESSAGE,b[0],b[1]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.USER_SEND_UNAVAILABLE_MESSAGE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.USER_SEND_UNAVAILABLE_MESSAGE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.USER_SEND_ENCRYPTED_MESSAGE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.USER_SEND_ENCRYPTED_MESSAGE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_UNRENDERABLE_MESSAGE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_UNRENDERABLE_MESSAGE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_UNRENDERABLE_MESSAGE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_UNRENDERABLE_MESSAGE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_LIVE_LOCATION_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_LIVE_LOCATION_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_LOCATION_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_LOCATION_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_LIVE_LOCATION_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_LIVE_LOCATION_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_LOCATION_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_LOCATION_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_POLL_CREATION_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_POLL_CREATION_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_POLL_CREATION_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_POLL_CREATION_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_CONTACT_SHARE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_CONTACT_SHARE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_CONTACT_SHARE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_CONTACT_SHARE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_STORY_MENTION_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_STORY_MENTION_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_STORY_MENTION_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_STORY_MENTION_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.MESSENGER_MEMORY_ENCRYPTED_MESSAGE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.MESSENGER_MEMORY_ENCRYPTED_MESSAGE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_MEMORIES_SHARE:return d("MAWMemoriesShareXMASnippetFbt").getCurrentUserSendMemoriesShareSnippetFbt().toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_MEMORIES_SHARE:return d("MAWMemoriesShareXMASnippetFbt").getParticipantSendMemoriesShareSnippetFbt(b[0]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.META_AI_SEND_MESSAGE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.META_AI_SEND_MESSAGE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_BUMP_MESSAGE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_BUMP_MESSAGE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_BUMP_MESSAGE:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_BUMP_MESSAGE).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_STICKER_RECEIVER_FETCH_MESSAGE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_STICKER_RECEIVER_FETCH_MESSAGE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_STICKER_RECEIVER_FETCH_MESSAGE_FALLBACK:return c("getMAWLocalizedFallbackMsgSnippet")(d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_STICKER_RECEIVER_FETCH_MESSAGE_FALLBACK).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_LIMIT_SHARING_DISABLED:return h._(/*BTDS*/"You turned on message sharing permissions for this chat. Everyone in this chat can now share messages with Meta AI or auto-save photos.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_LIMIT_SHARING_ENABLED:return h._(/*BTDS*/"You turned off message sharing permissions for this chat.").toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_LIMIT_SHARING_DISABLED:return h._(/*BTDS*/"{participant_name} turned on message sharing permissions for this chat. Everyone in this chat can now share messages with Meta AI or auto-save photos.",[h._param("participant_name",b[0])]).toString();case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_LIMIT_SHARING_ENABLED:return h._(/*BTDS*/"{participant_name} turned off message sharing permissions for this chat.",[h._param("participant_name",b[0])]).toString();default:return""}}g.buildLocalizedString=a}),226);
__d("MAWThreadCustomizationUtils",["MAWLocalizationType"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return a===d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CUSTOMIZE_NICKNAME||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SET_OWN_NICKNAME||a===d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SET_OWN_NICKNAME||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CLEAR_OWN_NICKNAME||a===d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_CLEAR_PARTICIPANT_NICKNAME}function b(a){return a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CUSTOMIZE_PARTICIPANT_NICKNAME||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_CLEAR_PARTICIPANT_NICKNAME}g.skipNicknameCheck=a;g.nicknameOnlyForFirstParticipantChecking=b}),98);
__d("fillArray",[],(function(a,b,c,d,e,f){function a(a,b){var c=new Array(a);for(var d=0;d<a;d++)c[d]=b;return c}f["default"]=a}),66);
__d("mapPull",[],(function(a,b,c,d,e,f){function a(a,b,c){var d=new Map();for(a of a)d.set(c(a),b(a));return d}f["default"]=a}),66);
__d("MAWBridgeBuildMsgMentions",["MAWLocalizationType","MAWSubscribeToContactNames","MAWThreadCustomizationUtils","WACommon.pb","WAJids","emptyFunction","fillArray","justknobx","mapPull"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return a.length>0?a.join(","):void 0}function i(a,b){return a.index-b.index}function j(a,b){return a==null||a===""?a:Array.from(b).reduce(function(a,b){var c=b[0];b=b[1];c=new RegExp(c,"g");return a.replace(c,b)},a)}function k(a,b){return a==null||a===""?[]:Array.from(b.keys()).flatMap(function(b){return Array.from(a.matchAll(b))}).sort(i)}function l(a,b,e,f,g){g===void 0&&(g=[]);e=c("mapPull")(e,function(a){var c=a[0];a[1];return d("MAWSubscribeToContactNames").getContactNameFromLookup(b,c)},function(a){a[0];a=a[1];return a});var i=k(a,e),l=[],m=[],n=[],o=0;for(i of i){var p=d("WAJids").unsafeCoerceToUserJid(i[0]);l.push(d("WAJids").userIdFromJid(p));var q=d("MAWSubscribeToContactNames").getContactNameFromLookup(e,p);m.push(q.length+1);n.push(Math.max(i.index-1+o,0));o+=q.length-p.length}q=c("fillArray")(l.length,"p");for(p of g){i=p.commandType;o=p.length;g=p.offset;if(o==null||g==null)continue;switch(i){case d("WACommon.pb").COMMAND_COMMAND_TYPE.EVERYONE:q.push("t");l.push("t");n.push(g);m.push(o);break;case d("WACommon.pb").COMMAND_COMMAND_TYPE.AI:q.push("ai");l.push("ai");n.push(g);m.push(o);break;case d("WACommon.pb").COMMAND_COMMAND_TYPE.AI_IMAGINE:q.push("im");l.push("im");n.push(g);m.push(o);break;default:break}}i=k(f,e);for(g of i){o=d("WAJids").unsafeCoerceToUserJid(g[0]);l.push(d("WAJids").userIdFromJid(o))}return{mentionIds:h(l),mentionLengths:h(m),mentionOffsets:h(n),mentionTypes:h(q),replyMessageText:j(f,e),text:j(a,e)}}function m(a,b,c,e){a=l(a,c,e);e=a.text;a=b.map(function(a){return d("MAWSubscribeToContactNames").getContactNameFromLookup(c,a)});return[e,a]}function a(a,b,e,f){b=c("mapPull")(b,c("emptyFunction").thatReturnsArgument,d("WAJids").userIdFromJid);return l(a,b,b,e,f)}function n(a,b,c,e,f){return b&&c>0||e?f===d("MAWSubscribeToContactNames").ContactNameMode.FIRST_NAME?"$CONTACT_FIRST_NAME("+a+")":"$CONTACT_FULL_NAME("+a+")":f===d("MAWSubscribeToContactNames").ContactNameMode.FIRST_NAME?"$PARTICIPANT_FIRST_NAME("+a+")":"$PARTICIPANT_FULL_NAME("+a+")"}function b(a,b,e,f){b=c("mapPull")(b!=null?b:[],c("emptyFunction").thatReturnsArgument,d("WAJids").userIdFromJid);if(b.size===0||a==null||a===""){var g=o(f),h=d("MAWThreadCustomizationUtils").nicknameOnlyForFirstParticipantChecking(f),i=d("MAWThreadCustomizationUtils").skipNicknameCheck(f);f=e.map(function(a,b){return n(a,h,b,i,g)});return[a,f]}f=new Map([].concat(Array.from(b.keys()),e).map(function(a){return[a,"$PARTICIPANT_FULL_NAME("+a+")"]}));return m(a,e,f,b)}function o(a){return c("justknobx")._("4603")?d("MAWSubscribeToContactNames").getContactNameModeFromLocalizationType(a):a===d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_MENTIONED_STORY_IG||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_LEFT_GROUP?d("MAWSubscribeToContactNames").ContactNameMode.FULL_NAME:d("MAWSubscribeToContactNames").ContactNameMode.FIRST_NAME}g.buildMsgTextWithMentions=a;g.buildThreadSnippetWithMentions=b}),98);
__d("MAWBridgeBuildThreadSnippet",["MAWAdminMsg","MAWBridgeBuildMsgMentions","MAWLocalizationType","MAWVault","first","justknobx"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return a!=null?d("MAWVault").unvault(a):void 0}function i(a,b,c){if(a==null)return b;b=[].concat(b);switch(c){case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_SEND_TEXT:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_TEXT:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_SEND_TEXT_IN_GROUP:b[0]=a;break}return b}function j(a){return a.replace(/\$/g,"$$$$")}function a(a,b){var e=a.snippetContactIDs,f=a.snippetMentionJIDs,g=a.snippetParams,k=a.snippetSenderContactId;a=a.snippetType;if(g==null||a==null||e==null)return b;k!=null&&!e.includes(k)&&c("justknobx")._("3140")&&e.push(k);b=h(c("first")(g));k=d("MAWBridgeBuildMsgMentions").buildThreadSnippetWithMentions(b==null?null:j(b),f,e,a);b=k[0];f=k[1];return d("MAWAdminMsg").buildLocalizedString(i(b,g,a),f,a)}g.buildBridgeThreadSnippet=a}),98);
__d("getMAWLastMessageCtaType",["MAWLocalizationType"],(function(a,b,c,d,e,f,g){"use strict";function a(a){switch(a){case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_MISSED_AUDIO_CALL:return"xma_rtc_missed_audio";case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_MISSED_AUDIO_CALL:return"xma_rtc_missed_group_audio";case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_MISSED_VIDEO_CALL:return"xma_rtc_missed_video";case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_MISSED_VIDEO_CALL:return"xma_rtc_missed_group_video"}}g["default"]=a}),98);
__d("MAWBridgeCentralizedThreadUpdateHandler",["I64","LSAuthorityLevel","LSBumpE2EEMetadataThreadStoredProcedure","LSDatabaseSingleton","LSFactory","LSIntEnum","MAWBridgeBuildThreadSnippet","MAWBridgeFireAndForget","MAWChatJid","MAWJids","MAWLocalizationType","MAWMiActGetThreadLifecycleState","MAWODSProxy","MAWThreadBumpType","MAWThreadUpdateMiddlewareGatingUtil","MAWThreadUpdateType","MWFBLogger","ODS","ReQL","WAJids","WAOdsEnums","WATimeUtils","asyncToGeneratorRuntime","getMAWLastMessageCtaType","justknobx","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k;function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["Encountered lastActivityTs == 0 for thread jid : "," with authority level: ",", threadType : ",", threadStatus : ",", folderType : ",", numMsgs (limit 5): "," and msgContentTypes: [","]"]);l=function(){return a};return a}function m(){var a=babelHelpers.taggedTemplateLiteralLoose(["Missing thread for bumping jid: "," with thread state: ",""]);m=function(){return a};return a}function n(){var a=babelHelpers.taggedTemplateLiteralLoose(["[Occamadillo][Middleware] Skipping receiver bump for own message on thread ",""]);n=function(){return a};return a}function o(){var a=babelHelpers.taggedTemplateLiteralLoose(["[Occamadillo][Middleware] Starting Thread Update on "," via middleware. threadKey: ",", authorityLevel: ",", lastActivityTimestampMs: ",", bumpTimestampMs: ",", bumpedByLocalDeviceSend: ",", bumpType: ",", threadUpdateType: ",", isUnbump: ",", isThrottled: "," from ",""]);o=function(){return a};return a}function p(){var a=babelHelpers.taggedTemplateLiteralLoose(["[Occamadillo][Middleware] Updated thread via middleware. threadKey: ",", bumpTimestampMs: ",",\n    original lastActivityTimestampMs: ","\n    , bumpedByLocalDeviceSend: ",", bumpType: ",", threadUpdateType: ",",  from ",""]);p=function(){return a};return a}var q=d("MWFBLogger").MWLogger.tags(["bridgeUIEvent","Occam","Middleware"]);function a(a,b){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e=(yield d("MAWChatJid").toThreadMaybe(a,b.jid));if(e==null){var f=(yield d("MAWMiActGetThreadLifecycleState").getThreadLifecycleStateByJid(a,d("MAWJids").convertChatJidToIntJid(b.jid),"MAWBridgeCentralizedThreadUpdateHandler"));q.MUSTFIX(m(),b.jid,f.type);return}return(h||(h=d("I64"))).lt(e.authorityLevel,(i||(i=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").AUTHORITATIVE))&&!b.bumpedByLocalDeviceSend?G(e,b):s(a,e,b)});return r.apply(this,arguments)}function s(a,b,c){return t.apply(this,arguments)}function t(){t=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){var f=b;b=f;b=b.threadKey;var g=e.author,i=e.bumpedByLocalDeviceSend,k=e.bumpTimestampMs,m=e.centralizedThreadSnippetData,n=e.threadUpdateType;if(n===d("MAWThreadUpdateType").THREAD_UPDATE_TYPE.NO_SNIPPET_OR_ACTIVITY_TS_UPDATE)return;var o=f.lastActivityTimestampMs;B(e,f,"Local",e.isUnbump,e.threadBumpType);if((h||(h=d("I64"))).equal(o,(h||(h=d("I64"))).zero)){var p;J("zero_last_activity_timestamp");var r=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.messages).getKeyRange(f.threadKey).take(5))),s=r.length;r=r.map(function(a){return(h||(h=d("I64"))).to_string(a.displayedContentTypes)}).join(",");q.WARN(l(),e.jid,(h||(h=d("I64"))).to_string(f.authorityLevel),h.to_string(f.threadType),h.to_string((p=f.threadStatus)!=null?p:(h||(h=d("I64"))).neg_one),f.folderName,s,r)}p=C(e,o);s=p.isUnbump;r=p.threadBumpType;if(n===d("MAWThreadUpdateType").THREAD_UPDATE_TYPE.BUMP_THREAD||n===d("MAWThreadUpdateType").THREAD_UPDATE_TYPE.MARK_THREAD_AS_UNREAD||s){J("bump_on_local");s&&(f=w(s,k,f),n!==d("MAWThreadUpdateType").THREAD_UPDATE_TYPE.MARK_THREAD_AS_UNREAD&&(f=y(k,f,s)));p=D(g,i,r,k,o,f.threadKey,m.snippetType);if(p){n={bumpedByLocalDeviceSend:i,isUnbump:s,serverAuthoritativeTimestampMs:k,threadKey:b};(j||(j=d("ODS"))).bumpEntityKey(3185,"maw_tc_server_thread_bump",""+((g=e.centralizedThreadSnippetData.snippetType)!=null?g:"undefined"));B(e,f,"Server",s,r);yield u(c("LSFactory")(a),n)}}var t=z(e.centralizedThreadSnippetData,f);return a.threads.put(t).then(function(){f!=null&&A(e,o,t)})});return t.apply(this,arguments)}function u(a,b){return v.apply(this,arguments)}function v(){v=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){J("bump_on_server");return c("LSBumpE2EEMetadataThreadStoredProcedure")(a,b)});return v.apply(this,arguments)}function w(a,b,c){return!x(a,b,c)?c:babelHelpers["extends"]({},c,{lastActivityTimestampMs:b})}function x(a,b,c){return a?(h||(h=d("I64"))).lt(b,c.lastActivityTimestampMs):(h||(h=d("I64"))).gt(b,c.lastActivityTimestampMs)}function y(a,b,c){return!c&&(h||(h=d("I64"))).lt(a,b.lastReadWatermarkTimestampMs)?b:babelHelpers["extends"]({},b,{lastReadWatermarkTimestampMs:a})}function z(a,b){var e=a.snippetSenderContactId!=null?(h||(h=d("I64"))).of_string(a.snippetSenderContactId):(h||(h=d("I64"))).zero,f=c("getMAWLastMessageCtaType")(a.snippetType);a=d("MAWBridgeBuildThreadSnippet").buildBridgeThreadSnippet(a,b.snippet);return babelHelpers["extends"]({},b,{lastMessageCtaType:f,snippet:a,snippetSenderContactId:e})}function A(a,b,c){q.DEBUG(p(),(h||(h=d("I64"))).to_string(c.threadKey),h.to_string(c.lastActivityTimestampMs),h.to_string(b),a.bumpedByLocalDeviceSend,a.threadBumpType,a.threadUpdateType,a.description)}function B(a,b,c,e,f){c=[c,(h||(h=d("I64"))).to_string(b.threadKey),h.to_string(b.authorityLevel),h.to_string(b.lastActivityTimestampMs),h.to_string(a.bumpTimestampMs),a.bumpedByLocalDeviceSend,f,a.threadUpdateType,e,a.isThrottled,a.description];q.DEBUG(o(),c[0],c[1],c[2],c[3],c[4],c[5],c[6],c[7],c[8],c[9],c[10])}function C(a,b){var e=c("justknobx")._("3068");if(e)return{isUnbump:a.isUnbump,threadBumpType:a.threadBumpType};e=(h||(h=d("I64"))).lt(a.bumpTimestampMs,b);if(a.isThrottled&&e){switch(a.tableName){case"messages":J("unbump_from_message_in_throttle");break;case"reactions":J("unbump_from_reaction_in_throttle");break;default:J("unbump_from_throttle")}e=a.tableName==="reactions";e&&d("MAWThreadUpdateMiddlewareGatingUtil").isPeristedUnbumpEnabled()&&d("MAWBridgeFireAndForget").fireAndForget("backend","unbumpThread",{incomingUnbumpFromTs:d("WATimeUtils").castToMillisTime((h||(h=d("I64"))).to_float(b)),threadJid:a.jid});return{isUnbump:!0,threadBumpType:d("MAWThreadBumpType").THREAD_BUMP_TYPE.LOCAL_ONLY}}return{isUnbump:a.isUnbump,threadBumpType:a.threadBumpType}}function D(a,b,c,e,f,g,i){if(c!==d("MAWThreadBumpType").THREAD_BUMP_TYPE.LOCAL_AND_SERVER)return!1;if(E(a,b,i)){q.DEBUG(n(),(h||(h=d("I64"))).to_string(g));return!1}J("check_if_bump_is_newer_than_current_activity");c=(h||(h=d("I64"))).gt(e,f);c||J("server_bump_prevented");return c}function E(a,b,c){return a!=null&&d("WAJids").isAuthorMe(a)&&b===!1&&!F(c)}function F(a){return a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_MISSED_AUDIO_CALL||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_MISSED_VIDEO_CALL||a===d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_AUDIO_CALLED||a===d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_VIDEO_CALLED}function G(a,b){return H.apply(this,arguments)}function H(){H=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e=(yield (k||(k=d("LSDatabaseSingleton"))).LSDatabaseSingleton);J("wait_for_authoritative_thread_start");I(a.threadKey,e,function(a){J("wait_for_authoritative_thread_end");var g=(h||(h=d("I64"))).to_float(a.lastActivityTimestampMs);J("ts_of_authoritative_thread_after_wait_"+(g>0?"gt_0":"eq_0"));c("promiseDone")(e.runInTransaction(function(c){return s(c,a,b)},"readwrite",void 0,void 0,f.id+":505"))});return});return H.apply(this,arguments)}function I(a,b,e){var f=d("ReQL").fromTableAscending(b.tables.threads).getKeyRange(a).subscribe(function(a,b){if(b.operation==="delete")return;a=b.value;if((h||(h=d("I64"))).lt(a.authorityLevel,(i||(i=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").AUTHORITATIVE)))return;e(a);return f()})}function J(a){d("MAWODSProxy").odsBumpEntityKey({entity:d("WAOdsEnums").Entity.MAW_THREAD_UPDATE_MIDDLEWARE,key:a})}g.call=a}),98);
__d("MAWBridgeDeleteGroupInviteHandler",["LSThreadBitOffset","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","Promise","ReQL","asyncToGeneratorRuntime","justknobx","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,e){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.threadJid,"MAWBridgeDeleteGroupInviteHandler",function(){var e=b("asyncToGeneratorRuntime").asyncToGenerator(function*(e,f){var g=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(e.group_invites).getKeyRange(f)));return(h||(h=b("Promise"))).all(g.map(function(a){return e.group_invites["delete"](a.threadKey,a.inviterId,a.inviteeId)})).then(function(){if(!c("justknobx")._("1162"))return;c("promiseDone")(d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(f)),function(b){if(b==null)return;var c=d("LSThreadBitOffset").clear([105],b.capabilities,b.capabilities2,b.capabilities3,b.capabilities4,b.capabilities5),e=c[0],f=c[1],g=c[2],h=c[3];c=c[4];return a.threads.put(babelHelpers["extends"]({},b,{capabilities:e,capabilities2:f,capabilities3:g,capabilities4:h,capabilities5:c,snippet:void 0}))})})});return function(a,b){return e.apply(this,arguments)}}())}g.call=a}),98);
__d("MAWBridgeDeleteMessageRangesV2Handler",["I64","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.threadJid,"MAWBridgeDeleteMessageRangesV2Handler",function(a,c){return a.messages_ranges_v2__generated["delete"](c,(h||(h=d("I64"))).of_float(b.minTimestampMs),b.minMsgId)})}g.call=a}),98);
__d("MAWBridgeDeleteMessagesHandler",["LSIntEnum","LSReplyMessageStatus","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWMpsGating","MAWTimeUtils","Promise","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,c){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,c.threadJid,"MAWBridgeDeleteMessagesHandler",function(){var e=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b,e){for(var f of c.messages)if(d("MAWMpsGating").isFullMpsEnabled()){var g=(yield a.messages.index("messageId").get(f.msgId));g!=null&&(yield b.messages["delete"](g.threadKey,g.timestampMs,g.messageId));yield j(b,f.msgId)}else yield b.messages["delete"](e,d("MAWTimeUtils").toTimestamp(f.ts),f.msgId)});return function(a,b){return e.apply(this,arguments)}}())}function j(a,b){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){e=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.messages.index("replySourceIdMessageID")).getKeyRange(e)));yield (i||(i=b("Promise"))).all(e.map(function(b){b=babelHelpers["extends"]({},b,{replyStatus:(h||(h=d("LSIntEnum"))).ofNumber(c("LSReplyMessageStatus").DELETED)});return a.messages.put(b)}))});return k.apply(this,arguments)}g.call=a}),98);
__d("MAWBridgeDeleteReactionHandler",["I64","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.chatJid,"MAWBridgeDeleteReactionHandler",function(a,c){return a.reactions["delete"](c,b.messageId,(h||(h=d("I64"))).of_string(b.actorId))})}g.call=a}),98);
__d("MAWBridgeEditMsgHistoryAddedHandler",["FBLogger","I64","LSMessageSendStatus","MAWChatJid","Promise","ReQL","asyncToGeneratorRuntime","emptyFunction"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a,b){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){a=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.messages.index("optimistic")).getKeyRange(b)));return a==null?void 0:a.messageId});return k.apply(this,arguments)}function l(a,b,c){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){var f=(yield j(a,b.originalMsgExternalId));if(f==null){c("FBLogger")("messenger_web_e2ee").warn("original message for edit msg history is not present in lsdb");return}f={messageContent:b.msgContent.content,originalMessagePk:f,sendStatus:b.sendStatus!=null?(i||(i=d("I64"))).of_int32(b.sendStatus):(i||(i=d("I64"))).of_int32(c("LSMessageSendStatus").SERVER_RECEIVED),serverAdjustedEditTsMs:(i||(i=d("I64"))).of_int32(b.editTs),threadKey:e};return a.edit_message_history.put(f)});return m.apply(this,arguments)}function n(a,b,e,f,g){g={messageContent:b.msgContent.content,originalMessagePk:g,pk:f,sendStatus:b.sendStatus!=null?(i||(i=d("I64"))).of_int32(b.sendStatus):(i||(i=d("I64"))).of_int32(c("LSMessageSendStatus").SERVER_RECEIVED),serverAdjustedEditTsMs:(i||(i=d("I64"))).of_int32(b.editTs),threadKey:e};return a.edit_message_history.put(g)}function o(a,b,c){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){a=(yield d("MAWChatJid").toThreadKeyMaybe(a.tables,c.threadJid));if(a==null)return;var e=c.editMsgHistoryId,f=c.originalMsgId;b=f==null||e==null?l(b,c,a):n(b,c,a,e,f);return b});return p.apply(this,arguments)}function a(a,d,e){return(h||(h=b("Promise"))).all(e.map(function(b){return o(a,d,b)})).then(c("emptyFunction"))}g.call=a}),98);
__d("MAWBridgeFulfillRestorePromisePayloadHandler",["MAWAsyncEBPendingQueryPromise","MAWEBRestoreTrackingUtils","Promise","err"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){a.success?(d("MAWAsyncEBPendingQueryPromise").resolvePendingQueryUponReStore(a.value.traceId),d("MAWEBRestoreTrackingUtils").markEBRestoreSuccess(a.value.traceId)):(d("MAWAsyncEBPendingQueryPromise").rejectPendingQueryIfPresent(a.error.traceId,c("err")(a.error.message)),d("MAWEBRestoreTrackingUtils").markEBRestoreFail(a.error.traceId));return(h||(h=b("Promise"))).resolve()}g.call=a}),98);
__d("MAWBridgeGroupInfoUpdatedHandler",["I64","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MWFBLogger","ODS","ReQL","asyncToGeneratorRuntime","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(){var a=babelHelpers.taggedTemplateLiteralLoose(['MAWDB groupInfo is missing "name" field for threadKey: ',""]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["Missing thread for updating group with threadKey: ",""]);k=function(){return a};return a}var l=d("MWFBLogger").MWLogger.tags(["bridgeUIEvent","Occam","Middleware"]);function a(a,e){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.chatJid,"MAWBridgeGroupInfoUpdatedHandler",function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var f;f=(f=e.threadName)!=null?f:void 0;a=a.threads;var g=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a).getKeyRange(b)));if(g==null){(h||(h=d("ODS"))).bumpEntityKey(3185,"armadillo_group_info_update","missing_thread");l.WARN(k(),(i||(i=d("I64"))).to_string(b));return}else f==null&&c("justknobx")._("4129")&&(f=g.threadName,l.WARN(j(),(i||(i=d("I64"))).to_string(b)));b=e.memberAddMode!=="all_member_add";yield a.put(babelHelpers["extends"]({},g,{needsAdminApprovalForNewParticipant:b,threadName:f}))});return function(b,c){return a.apply(this,arguments)}}())}g.call=a}),98);
__d("MAWBridgeGroupInviteUpdateHandler",["I64","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWUpdateLSThreadCapabilities","Promise","emptyFunction"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,e){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.threadJid,"MAWBridgeGroupInviteUpdateHandler",function(a,f){var g=(i||(i=d("I64"))).of_string(e.inviterId);return(h||(h=b("Promise"))).all([d("MAWUpdateLSThreadCapabilities").setGroupInviteTxn(a,f,g)]).then(c("emptyFunction"))})}g.call=a}),98);
__d("MAWBridgeHandleCOPDigest",["Promise"],(function(a,b,c,d,e,f){"use strict";var g;function a(a,c){return(g||(g=b("Promise"))).resolve()}f.call=a}),66);
__d("MAWBridgeMediaExpiredHandler",["MAWMiActOnMiThreadExistsForJid__DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.threadJid,"MAWBridgeMediaExpiredHandler.bs",function(a,c){return a.attachments["delete"](c,b.msgId,String(b.plaintextHash))})}g.call=a}),98);
__d("MAWLocalizationEphemeralSettingSetAdminMsg",["MAWLocalizationType"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return a===d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_TURNED_ON_SECONDS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_TURNED_ON_MINUTES||a===d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_TURNED_ON_HOURS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_TURNED_ON_DAYS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_TURNED_ON_SECONDS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_TURNED_ON_MINUTES||a===d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_TURNED_ON_HOURS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_TURNED_ON_DAYS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_TURNED_ON_SECONDS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_TURNED_ON_MINUTES||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_TURNED_ON_HOURS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_TURNED_ON_DAYS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_CHANGE_SECONDS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_CHANGE_MINUTES||a===d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_CHANGE_HOURS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_CHANGE_DAYS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_CHANGE_SECONDS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_CHANGE_MINUTES||a===d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_CHANGE_HOURS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_CHANGE_DAYS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_CHANGE_SECONDS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_CHANGE_MINUTES||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_CHANGE_HOURS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_CHANGE_DAYS||a===d("MAWLocalizationType").LOCALIZATION_TYPE.UNKNOWN_USER_EPHEMERAL_SETTING_TURNED_OFF||a===d("MAWLocalizationType").LOCALIZATION_TYPE.YOU_EPHEMERAL_SETTING_TURNED_OFF||a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_EPHEMERAL_SETTING_TURNED_OFF||a===d("MAWLocalizationType").LOCALIZATION_TYPE.EPHEMERAL_SETTINGS_AUTO_RESET}g.isEphemeralSettingSetAdminMsg=a}),98);
__d("MAWAdminMsgCTA",["fbt","I64","MAWLocalizationEphemeralSettingSetAdminMsg","MAWLocalizationType","MAWMsgType","MAWTimeUtils","Promise","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=new Set([(c=d("MAWLocalizationType")).LOCALIZATION_TYPE.CURRENT_USER_CREATED_POLL,c.LOCALIZATION_TYPE.CURRENT_USER_ADDED_POLL_VOTE,c.LOCALIZATION_TYPE.PARTICIPANT_CREATED_POLL,c.LOCALIZATION_TYPE.PARTICIPANT_ADDED_POLL_VOTE,c.LOCALIZATION_TYPE.PARTICIPANT_ADDED_MULTIPLE_POLL_OPTIONS,c.LOCALIZATION_TYPE.PARTICIPANT_ADDED_MULTIPLE_POLL_VOTES,c.LOCALIZATION_TYPE.PARTICIPANT_REMOVED_POLL_VOTE,c.LOCALIZATION_TYPE.PARTICIPANT_REMOVED_MULTIPLE_POLL_VOTES,c.LOCALIZATION_TYPE.CURRENT_USER_REMOVED_POLL_VOTE,c.LOCALIZATION_TYPE.CURRENT_USER_REMOVED_MULTIPLE_POLL_VOTES,c.LOCALIZATION_TYPE.PARTICIPANT_MIXED_POLL_UPDATE,c.LOCALIZATION_TYPE.CURRENT_USER_MIXED_POLL_UPDATE,c.LOCALIZATION_TYPE.PARTICIPANT_CHANGED_POLL_VOTE,c.LOCALIZATION_TYPE.CURRENT_USER_CHANGED_POLL_VOTE,c.LOCALIZATION_TYPE.PARTICIPANT_CHANGED_MULTIPLE_POLL_VOTES,c.LOCALIZATION_TYPE.CURRENT_USER_CHANGED_MULTIPLE_POLL_VOTES,c.LOCALIZATION_TYPE.PARTICIPANT_ADDED_POLL_OPTION,c.LOCALIZATION_TYPE.CURRENT_USER_ADDED_POLL_OPTION]),l=new Set([c.LOCALIZATION_TYPE.CURRENT_USER_CUSTOMIZE_THEME,c.LOCALIZATION_TYPE.CURRENT_USER_CUSTOMIZE_AI_THEME,c.LOCALIZATION_TYPE.PARTICIPANT_CUSTOMIZE_THEME,c.LOCALIZATION_TYPE.PARTICIPANT_CUSTOMIZE_AI_THEME]),m=h._(/*BTDS*/"Change").toString(),n=h._(/*BTDS*/"See All").toString(),o=h._(/*BTDS*/"Learn more").toString(),p=h._(/*BTDS*/"Learn more").toString(),q=h._(/*BTDS*/"actions you can take.").toString(),r=h._(/*BTDS*/"Go to other chat").toString(),s=h._(/*BTDS*/"View archived chat").toString(),t=h._(/*BTDS*/"View poll").toString(),u=h._(/*BTDS*/"Change").toString();function v(a){return w.apply(this,arguments)}function w(){w=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){a=(yield d("ReQL").firstAsync(d("ReQL").fromTableDescending(a.admin_message_ctas.index("ctaId"))));return a!=null?(j||(j=d("I64"))).add(a.ctaId,(j||(j=d("I64"))).one):(j||(j=d("I64"))).zero});return w.apply(this,arguments)}function x(a,b,c){b=[b,c];return d("ReQL").firstAsync(d("ReQL").fromTableDescending(a.admin_message_ctas).bounds({gte:b,lte:b}))}function y(a,b,c){if(a==null)return{showAdChoiceIcon:!1};if(d("MAWLocalizationEphemeralSettingSetAdminMsg").isEphemeralSettingSetAdminMsg(a))return{actionUrl:void 0,adClientToken:void 0,adHelpUrl:void 0,adminMessageNicknameParticipantId:void 0,adPreferenceUrl:void 0,showAdChoiceIcon:!1,targetId:void 0,targetTitle:void 0,title:m};if(k.has(a))return{actionUrl:void 0,adClientToken:void 0,adHelpUrl:void 0,adminMessageNicknameParticipantId:void 0,adPreferenceUrl:void 0,showAdChoiceIcon:!1,targetId:b,targetTitle:c,title:t};if(l.has(a))return{actionUrl:void 0,adClientToken:void 0,adHelpUrl:void 0,adminMessageNicknameParticipantId:void 0,adPreferenceUrl:void 0,showAdChoiceIcon:!1,targetId:void 0,targetTitle:void 0,title:u};switch(a){case d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_THREAD_ADMIN_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_IGD_THREAD_ADMIN_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.E2EE_THREAD_DESCRIPTION:return{actionUrl:void 0,adClientToken:void 0,adHelpUrl:void 0,adminMessageNicknameParticipantId:void 0,adPreferenceUrl:void 0,showAdChoiceIcon:!1,targetId:void 0,targetTitle:void 0,title:o};case d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_ROLLBACK_ADMIN_MESSAGE:return{actionUrl:void 0,adClientToken:void 0,adHelpUrl:void 0,adminMessageNicknameParticipantId:void 0,adPreferenceUrl:void 0,showAdChoiceIcon:!1,targetId:void 0,targetTitle:void 0,title:r};case d("MAWLocalizationType").LOCALIZATION_TYPE.DUAL_THREAD_CUTOVER_ADMIN_MESSAGE:return{actionUrl:void 0,adClientToken:void 0,adHelpUrl:void 0,adminMessageNicknameParticipantId:void 0,adPreferenceUrl:void 0,showAdChoiceIcon:!1,targetId:void 0,targetTitle:void 0,title:s};case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_PINNED_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_UNPINNED_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_PINNED_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_UNPINNED_MESSAGE:return{actionUrl:void 0,adClientToken:void 0,adHelpUrl:void 0,adminMessageNicknameParticipantId:void 0,adPreferenceUrl:void 0,showAdChoiceIcon:!1,targetId:void 0,targetTitle:void 0,title:n};case d("MAWLocalizationType").LOCALIZATION_TYPE.E2EE_THREAD_UK_OSA_ADMIN_MESSAGE:return{actionUrl:"https://www.messenger.com/help/1145318292241859/?helpref=uk_lm",adClientToken:void 0,adHelpUrl:void 0,adminMessageNicknameParticipantId:void 0,adPreferenceUrl:void 0,showAdChoiceIcon:!1,targetId:void 0,targetTitle:void 0,title:q};case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_LIMIT_SHARING_DISABLED:case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_LIMIT_SHARING_ENABLED:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_LIMIT_SHARING_DISABLED:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_LIMIT_SHARING_ENABLED:return{actionUrl:"https://www.facebook.com/help/messenger-app/2016594885417423?ref=ipl",adClientToken:void 0,adHelpUrl:void 0,adminMessageNicknameParticipantId:void 0,adPreferenceUrl:void 0,showAdChoiceIcon:!1,targetId:void 0,targetTitle:void 0,title:p};default:return{actionUrl:void 0,adClientToken:void 0,adHelpUrl:void 0,adminMessageNicknameParticipantId:void 0,adPreferenceUrl:void 0,showAdChoiceIcon:!1,targetId:void 0,targetTitle:void 0,title:void 0}}}function z(a){if(a==null)return;if(d("MAWLocalizationEphemeralSettingSetAdminMsg").isEphemeralSettingSetAdminMsg(a))return"admin_msg_armadillo_ephemeral_change_duration";if(k.has(a))return"admin_msg_poll_details";if(l.has(a))return"admin_msg_change_theme";switch(a){case d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_THREAD_ADMIN_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_IGD_THREAD_ADMIN_MESSAGE:return"admin_msg_thread_level_cutover";case d("MAWLocalizationType").LOCALIZATION_TYPE.E2EE_THREAD_DESCRIPTION:return"admin_msg_e2ee_thread";case d("MAWLocalizationType").LOCALIZATION_TYPE.E2EE_THREAD_UK_OSA_ADMIN_MESSAGE:return"admin_msg_e2ee_uk_osa";case d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_ROLLBACK_ADMIN_MESSAGE:return"admin_msg_cutover_rollback";case d("MAWLocalizationType").LOCALIZATION_TYPE.DUAL_THREAD_CUTOVER_ADMIN_MESSAGE:return"admin_msg_dual_thread_cutover";case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_PINNED_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_UNPINNED_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_PINNED_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_UNPINNED_MESSAGE:return"admin_msg_view_pin_msgs_v2";case d("MAWLocalizationType").LOCALIZATION_TYPE.YOUR_DEVICE_ICDC_ALERT_ADMIN_MESSAGE:case d("MAWLocalizationType").LOCALIZATION_TYPE.ICDC_ALERT_ADMIN_MESSAGE:return"admin_msg_icdc";case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_LIMIT_SHARING_DISABLED:case d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_LIMIT_SHARING_ENABLED:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_LIMIT_SHARING_DISABLED:case d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_LIMIT_SHARING_ENABLED:return"admin_msg_change_thread_limit_sharing_permission"}}function A(a,b,c,d,e){return B.apply(this,arguments)}function B(){B=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e,f){yield a.admin_message_ctas.add({actionContentBlob:f.actionContentBlob,actionUrl:f.actionUrl,adClientToken:f.adClientToken,adHelpUrl:f.adHelpUrl,adminMessageNicknameParticipantId:f.adminMessageNicknameParticipantId,adPreferenceUrl:f.adPreferenceUrl,ctaId:b,messageId:e.msgId,showAdChoiceIcon:f.showAdChoiceIcon,targetId:f.targetId,targetTitle:f.targetTitle,threadKey:c,timestampMs:d("MAWTimeUtils").toTimestamp(e.ts),title:f.title})});return B.apply(this,arguments)}function C(a,b,c){return D.apply(this,arguments)}function D(){D=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,d){var e=(yield (i||(i=b("Promise"))).all([x(a,c,d.msgId),v(a)])),f=e[0];e=e[1];if(f!=null)return{ctaId:f.ctaId,ctaType:z(d.adminType),title:f.title};else{f=E(d.adminType,d);var g=F(d.adminType,d);f=y(d.adminType,f,g);yield A(a,e,c,d,f);return{ctaId:e,ctaType:z(d.adminType),title:f.title}}});return D.apply(this,arguments)}function a(a,c,e){var f=e.adminType;return f!=null&&(d("MAWLocalizationEphemeralSettingSetAdminMsg").isEphemeralSettingSetAdminMsg(f)||f===d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_THREAD_ADMIN_MESSAGE||f===d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_IGD_THREAD_ADMIN_MESSAGE||f===d("MAWLocalizationType").LOCALIZATION_TYPE.E2EE_THREAD_DESCRIPTION||f===d("MAWLocalizationType").LOCALIZATION_TYPE.E2EE_THREAD_UK_OSA_ADMIN_MESSAGE||f===d("MAWLocalizationType").LOCALIZATION_TYPE.CUTOVER_ROLLBACK_ADMIN_MESSAGE||f===d("MAWLocalizationType").LOCALIZATION_TYPE.DUAL_THREAD_CUTOVER_ADMIN_MESSAGE||f===d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_PINNED_MESSAGE||f===d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_UNPINNED_MESSAGE||f===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_PINNED_MESSAGE||f===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_UNPINNED_MESSAGE||f===d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_LIMIT_SHARING_DISABLED||f===d("MAWLocalizationType").LOCALIZATION_TYPE.CURRENT_USER_LIMIT_SHARING_ENABLED||f===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_LIMIT_SHARING_DISABLED||f===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_LIMIT_SHARING_ENABLED||f!=null&&k.has(f)||f!=null&&l.has(f))?C(a,c,e):(i||(i=b("Promise"))).resolve()}function E(a,b){if(a==null)return void 0;if(b.type_===d("MAWMsgType").MSG_TYPE.GROUP_POLL_CREATE)return(j||(j=d("I64"))).of_string(b.externalId);return k.has(a)&&b.pollStanzaId!=null?(j||(j=d("I64"))).of_string(b.pollStanzaId):void 0}function F(a,b){if(a==null)return void 0;return k.has(a)?b.adminContent==null||b.adminContent.length===0?void 0:b.adminContent[b.adminContent.length-1]:void 0}g.getAdminMsgCTAContentByAdminType=y;g.getCtaTypeByAdminType=z;g.getAdminMsgCtaStep=a}),226);
__d("LSMessageUnsendabilityStatus",[],(function(a,b,c,d,e,f){a=Object.freeze({CAN_UNSEND:0,DENY_LOG_MESSAGE:1,DENY_TOMBSTONE_MESSAGE:2,DENY_FOR_NON_SENDER:3,DENY_P2P_PAYMENT:4,DENY_STORY_REACTION:5,DENY_BLOB_ATTACHMENT:6,DENY_MESSAGE_NOT_FOUND:7,DENY_MESSAGE_INSTAGRAM_DIRECT_WRITE_RESTRICTION:8});f["default"]=a}),66);
__d("MAWBridgeBuildAdminMsg",["MAWAdminMsg","MAWAdminMsgNormalized","MAWLocalizationType","MAWSubscribeToContactNames","MAWThreadCustomizationUtils","justknobx"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return a==null?void 0:a.replace(/\$/g,"$$$$")}function a(a,b,c){c===void 0&&(c=0);a=d("MAWAdminMsgNormalized").deconstructContactIdsFromAdminContent(b,a);var e=a.contactIds;a=a.contents;var f=d("MAWThreadCustomizationUtils").nicknameOnlyForFirstParticipantChecking(b),g=d("MAWThreadCustomizationUtils").skipNicknameCheck(b);if(c===1){var j=i(b);return d("MAWAdminMsg").buildLocalizedString(a.map(h),e.map(function(a,b){return f&&b>0||g?j===d("MAWSubscribeToContactNames").ContactNameMode.FIRST_NAME?"$CONTACT_FIRST_NAME("+a+")":"$CONTACT_FULL_NAME("+a+")":j===d("MAWSubscribeToContactNames").ContactNameMode.FIRST_NAME?"$PARTICIPANT_FIRST_NAME("+a+")":"$PARTICIPANT_FULL_NAME("+a+")"}),b)}return d("MAWAdminMsg").buildLocalizedString(a,e,b)}function i(a){return c("justknobx")._("4603")?d("MAWSubscribeToContactNames").getContactNameModeFromLocalizationType(a):a===d("MAWLocalizationType").LOCALIZATION_TYPE.PARTICIPANT_NAMED_GROUP?d("MAWSubscribeToContactNames").ContactNameMode.FIRST_NAME:d("MAWSubscribeToContactNames").ContactNameMode.FULL_NAME}g.buildAdminMsgText=a}),98);
__d("LSTransportKey",[],(function(a,b,c,d,e,f){a=Object.freeze({ADVANCED_CRYPTO:"AdvancedCrypto",CARRIER_MESSAGING:"CarrierMessaging",FB_BROKER:"FBBroker",FB_LEGACY_BROKER:"FBLegacyBroker",TAM:"TAM",WHATS_APP:"WhatsApp",INSTAMADILLO:"Instamadillo",ENCRYPTED_BACKUPS:"EncryptedBackups",CLIENT_CONTACTS_PLACEHOLDER:"",ENCRYPTED_BACKUPS_MEDIA:"EncryptedBackupsMedia"});f["default"]=a}),66);
__d("MAWBridgeMsgUtils",["LSTransportKey"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=Object.keys(c("LSTransportKey")).find(function(b){return c("LSTransportKey")[b]===a});return b?c("LSTransportKey")[b]:"WhatsApp"}g.convertStringToLSTransportKeyType=a}),98);
__d("MAWMsgErrorUtils",["gkx"],(function(a,b,c,d,e,f,g){"use strict";var h=[126];function a(a){var b=c("gkx")("7471");return!b?!1:h.includes(a)}g.isWellbeingError=a}),98);
__d("MAWMsgReply",["EntMessageReplyType","FBLogger","I64","LSIntEnum","LSMessageReplySourceTypeV2","LSReplyMessageAttachmentType","MAWMsg","MAWMsgType","WATimeUtils","cr:2472","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a){return a.replySourceId!=null}function a(a){a=a.replyToUserId;return a==null?void 0:(h||(h=d("I64"))).of_string(a)}function e(a){var b=a.isFBPostMention,e=a.isFBStoryMention,f=a.isForwarded,g=a.isMsgHighlightsTabFriendUpdatesReply,h=a.isNoteReply,k=a.isStoryProducerReply,l=a.isStoryReply,m=a.isStoryShare,n=a.replyMediaExpirationTimestampMs;a=j(a);if(b===!0)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").FB_POST_MENTION);if(f)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").FORWARD);if(m!=null&&m)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").FB_STORY_SHARE);if(e!=null&&e)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").FB_STORY_MENTION);if(g!=null&&g)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").HIGHLIGHTS_TAB_POST_REPLY);if(n!=null&&n<d("WATimeUtils").unixTime()&&h!==!0)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").EXPIRED_STORY);if(a)if(l!=null&&l)return k!=null&&k?(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").FB_PRODUCER_STORY_REPLY):(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").STORY);else if(h===!0)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").LIGHTWEIGHT_STATUS);else return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").MESSAGE)}function f(a){var b=j(a),e=a.isMsgHighlightsTabFriendUpdatesReply,f=a.isNoteReply,g=a.isStoryReply;a=a.isStoryShare;if(a!=null&&a)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").FB_STORY_SHARE);if(e!=null&&e)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").HIGHLIGHTS_TAB_POST_REPLY);if(b)if(g!=null&&g)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").STORY);else if(f===!0)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").LIGHTWEIGHT_STATUS);else return(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageReplySourceTypeV2").MESSAGE)}function k(a){if(a.replySourceId!=null)return a.replySourceId}function l(a){if(a.replySourceTimestampMs!=null)return(h||(h=d("I64"))).of_float(a.replySourceTimestampMs)}function m(a){if(a.replySourceId!=null)return a.replyMessageText}function n(a){if(a.replySourceId!=null)return a.replyMentionedJids}function o(a){return a.type_===d("MAWMsg").bumpExistingMessage?(i||(i=d("LSIntEnum"))).ofNumber(c("EntMessageReplyType").BUMP):j(a)?(i||(i=d("LSIntEnum"))).ofNumber(c("EntMessageReplyType").REGULAR):void 0}function p(a){return a.replySourceId==null?!1:q(a.replyStatusType)}function q(a){switch(a){case d("MAWMsgType").MSG_TYPE.IMAGE:case d("MAWMsgType").MSG_TYPE.VIDEO:case d("MAWMsgType").MSG_TYPE.GIF:case d("MAWMsgType").MSG_TYPE.STICKER:case d("MAWMsgType").MSG_TYPE.DOCUMENT_FILE:case d("MAWMsgType").MSG_TYPE.PTT:case d("MAWMsgType").MSG_TYPE.RAVEN:case d("MAWMsgType").MSG_TYPE.XMA:case d("MAWMsgType").MSG_TYPE.RECEIVER_FETCH:return!0;default:return!1}}function r(a){return a.replyStatusType==="XMA"&&a.replyMessageText!=null}function s(a){if(p(a))return a.replyMediaPreviewHeight!=null?(h||(h=d("I64"))).of_int32(a.replyMediaPreviewHeight):void 0}function t(a){if(p(a))return a.replyMediaPreviewWidth!=null?(h||(h=d("I64"))).of_int32(a.replyMediaPreviewWidth):void 0}function u(a){var b=w(a),e=v(a);b!==e&&c("FBLogger")("messenger_e2ee_web").mustfix("[getReplyAttachmentType result inconsistency] V1: %s, V2: %s, msg replyStatusType: %s",(h||(h=d("I64"))).to_string(b!=null?b:(h||(h=d("I64"))).zero),h.to_string(e!=null?e:(h||(h=d("I64"))).zero),a.replyStatusType);return b}function v(a){var b=a.replyStatusType;if(b==null)return;var e=b==="Raven";e=p(a)||e;if(!e)return;switch(b){case"Gif":return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").GIF);case"Ptt":return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").OTHER);case"Sticker":return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").STICKER);case"Video":return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").VIDEO);case"Image":return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").PHOTO);case"XMA":if(a.isIGXMAPostShare===!0||a.isIGXMAStoryShare===!0||a.isStoryHighlightShare===!0||a.isPostPrivateReply===!0||a.isMsgHighlightsTabFriendUpdatesReply===!0)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").FEED_POST);if(r(a))return;return c("justknobx")._("2738")?(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").XMA):(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").PHOTO);case"DocumentFile":return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").OTHER);case"ReceiverFetch":return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").STICKER);case"Raven":return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").EPHEMERAL_RAVEN_MESSAGE);default:}}function w(a){var e=a.replyMessageText;e=e!=null?b("cr:2472")==null?e:b("cr:2472").unvault(e):"";e=e==="\ud83d\udc4d"&&a.replyStatusType==="Sticker";var f=a.replyStatusType==="Raven";f=p(a)||f;var g=a.replyStatusType;if(f){if(g==null)return;switch(g){case"Gif":if(e)return;else return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").GIF);case"Ptt":if(e)return;else return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").OTHER);case"Sticker":if(!e)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").STICKER);break;case"Video":if(e)return;else return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").VIDEO);case"Image":return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").PHOTO);case"XMA":if(e)return;if(a.isIGXMAPostShare===!0||a.isIGXMAStoryShare===!0||a.isStoryHighlightShare===!0||a.isPostPrivateReply===!0||a.isMsgHighlightsTabFriendUpdatesReply===!0)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").FEED_POST);if(r(a))return;return c("justknobx")._("2738")?(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").XMA):(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").PHOTO);case"DocumentFile":if(e)return;return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").OTHER);case"ReceiverFetch":if(e)return;return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").STICKER);case"Raven":return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").EPHEMERAL_RAVEN_MESSAGE);default:}}if(g!=null&&g==="Sticker"&&e)return(i||(i=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").STICKER)}function x(a){var b=a.replyMediaId,c=a.replyXMAId;if(p(a)&&b!=null)return c!=null?void 0:(h||(h=d("I64"))).of_float(b);if(b!=null||c==null)return;if(c!=null&&r(a))return;return(h||(h=d("I64"))).of_int32(c)}function y(a){var b=a.replyPlaintextHash;if(p(a)&&b!=null)return""+b}function z(a){var b=a.isNoteReply,c=a.isStoryReply,e=a.replyMediaExpirationTimestampMs,f=a.replyMessageText,g=a.replyStatusType;if(b===!0){if(e!=null)return(h||(h=d("I64"))).of_float(e);return}b=u(a);e=x(a);if(g!=null&&g==="XMA"&&b==null){if(e!=null)return;if(f==null)return(h||(h=d("I64"))).zero}if(e!=null||!(c!=null&&c))return;return(h||(h=d("I64"))).zero}g.getReplyUserId=a;g.getReplySourceTypeV2=e;g.getReplySourceType=f;g.getReplySourceId=k;g.getReplyTimestampMs=l;g.getReplyMessageText=m;g.getReplyMentionedJids=n;g.getReplyType=o;g.getReplyMediaHeight=s;g.getReplyMediaWidth=t;g.getReplyAttachmentType=u;g.getReplyAttachmentTypeV2=v;g.getReplyAttachmentTypeV1=w;g.getReplyAttachmentId=x;g.getReplyAttachmentPlaintextHash=y;g.getReplyMediaExpirationTimestampMs=z}),98);
__d("MAWBumpMessageMsgReplySnippet",["getMAWLocalizedBumpMsgReplySnippet"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=a.isAuthorMe,d=a.recipientName,e=a.replySenderIsMe,f=a.replySenderIsRecipient;a=a.senderName;var g="participantBumpedParticipantMessage";b&&e?g="currentUserBumpedOwnMessage":b?g="currentUserBumpedMessage":e?g="participantBumpedCurrentUserMessage":f&&(g="participantBumpedOwnMessage");return c("getMAWLocalizedBumpMsgReplySnippet")(g,a,d)}g.buildBumpMessageReplySnippet=a}),98);
__d("MAWMsgReplyStatus",["LSIntEnum","LSReplyMessageStatus","MAWMsgType"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a){switch(a){case d("MAWMsgType").MSG_TYPE.DELETE_FOR_ME:case d("MAWMsgType").MSG_TYPE.REVOKED:return c("LSReplyMessageStatus").DELETED;case d("MAWMsgType").MSG_TYPE.TEXT:case d("MAWMsgType").MSG_TYPE.IMAGE:case d("MAWMsgType").MSG_TYPE.VIDEO:case d("MAWMsgType").MSG_TYPE.PTT:case d("MAWMsgType").MSG_TYPE.ADMIN:case d("MAWMsgType").MSG_TYPE.GIF:case d("MAWMsgType").MSG_TYPE.STICKER:case d("MAWMsgType").MSG_TYPE.XMA:case d("MAWMsgType").MSG_TYPE.DOCUMENT_FILE:case d("MAWMsgType").MSG_TYPE.RAVEN:case d("MAWMsgType").MSG_TYPE.RECEIVER_FETCH:return c("LSReplyMessageStatus").VALID;case d("MAWMsgType").MSG_TYPE.FUTUREPROOF:case d("MAWMsgType").MSG_TYPE.CIPHERTEXT:case d("MAWMsgType").MSG_TYPE.UNAVAILABLE:return c("LSReplyMessageStatus").TEMPORARY_UNAVAILABLE;case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SCREENSHOT_ACTION:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_ADMIN:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_CHANGE_FROM_CURRENT_DEVICE:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SYNC_RESPONSE:return c("LSReplyMessageStatus").UNKNOWN;case d("MAWMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:return c("LSReplyMessageStatus").EXPIRED;default:return void 0}}function a(a){a=i(a.replyStatusType);if(a!=null)return(h||(h=d("LSIntEnum"))).ofNumber(a)}g.getReplyMessageStatus=i;g.getLSReplyMessageStatus=a}),98);
__d("MAWMsgReplySnippet",["fbt","FBLogger","I64","LSIntEnum","LSReplyMessageAttachmentType","LSReplyMessageStatus","MAWBumpMessageMsgReplySnippet","MAWMsgReplyStatus","MAWMsgType","MAWPostMentionXMASnippetFbt","ODS","justknobx"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k;function l(a){var b=a.isAuthorMe,e=a.isBumpMsg;e=e===void 0?!1:e;var f=a.isDeletedMsg,g=a.isFBPostMention;g=g===void 0?!1:g;var k=a.isFBStoryMention;k=k===void 0?!1:k;var l=a.isIGStoryMention;l=l===void 0?!1:l;var m=a.isIGXMAStoryShare;m=m===void 0?!1:m;var n=a.isMsgHighlightsTabFriendUpdatesReply;n=n===void 0?!1:n;var o=a.isNoteReply;o=o===void 0?!1:o;var p=a.isPostPrivateReply;p=p===void 0?!1:p;var q=a.isStoryHighlightShare;q=q===void 0?!1:q;var r=a.isStoryProducerReply,s=a.isStoryReaction;s=s===void 0?!1:s;var t=a.isStoryReply;t=t===void 0?!1:t;var u=a.isStoryShare;u=u===void 0?!1:u;var v=a.recipientName,w=a.replyAttachmentType,x=a.replySenderIsMe;x=x===void 0?!1:x;var y=a.replySenderIsRecipient;y=y===void 0?!1:y;var z=a.senderName;a=a.storyShareAuthor;switch(!0){case g:return b?d("MAWPostMentionXMASnippetFbt").getCurrentUserSendPostMentionSnippetFbt(v):d("MAWPostMentionXMASnippetFbt").getParticipantSendPostMentionSnippetFbt(z);case e:return d("MAWBumpMessageMsgReplySnippet").buildBumpMessageReplySnippet({isAuthorMe:b,recipientName:v,replySenderIsMe:x,replySenderIsRecipient:y,senderName:z});case q:return a!=null?h._(/*BTDS*/"Sent {username}'s story highlight",[h._param("username",a)]):h._(/*BTDS*/"Sent a story highlight");case m&&w!=null&&(i||(i=d("I64"))).equal(w,(j||(j=d("LSIntEnum"))).ofNumber(c("LSReplyMessageAttachmentType").FEED_POST)):return a!=null?h._(/*BTDS*/"Sent {username}'s story",[h._param("username",a)]):h._(/*BTDS*/"Sent a story");case b&&f:return h._(/*BTDS*/"You replied to a removed message");case b&&u:return h._(/*BTDS*/"You shared a story");case b&&s:return h._(/*BTDS*/"You reacted to their story");case b&&l:return v!=null?h._(/*BTDS*/"You mentioned \u0040{name} in your story",[h._param("name",v)]):h._(/*BTDS*/"You mentioned a participant in your story");case b&&k:return v!=null?h._(/*BTDS*/"You mentioned {name} in your story",[h._param("name",v)]):h._(/*BTDS*/"You mentioned a participant in your story");case l:case k:return h._(/*BTDS*/"Mentioned you in their story");case!b&&n:return h._(/*BTDS*/"{name} replied to your post",[h._param("name",z)]);case b&&n:return h._(/*BTDS*/"You replied to their post");case b&&p:return v!=null?h._(/*BTDS*/"You replied to {name}'s post",[h._param("name",v)]):h._(/*BTDS*/"You replied to a participant's post");case p:return h._(/*BTDS*/"{name} replied to your post",[h._param("name",z)]);case!b&&o:return h._(/*BTDS*/"{name} replied to your note",[h._param("name",z)]);case b&&o:return h._(/*BTDS*/"You replied to their note");case b&&x:return h._(/*BTDS*/"You replied to yourself");case b&&t:return r===!0?h._(/*BTDS*/"You replied to your story"):v!=null?h._(/*BTDS*/"You replied to {name}'s story",[h._param("name",v)]):h._(/*BTDS*/"You replied to a participant's story");case b:return v!=null?h._(/*BTDS*/"You replied to {other_name}",[h._param("other_name",v)]):h._(/*BTDS*/"You replied to a participant");case f:return h._(/*BTDS*/"{name} replied to a removed message",[h._param("name",z)]);case u:return h._(/*BTDS*/"{user} shared a story",[h._param("user",z)]);case s:return h._(/*BTDS*/"Reacted to your story");case y:return h._(/*BTDS*/"{other} replied to themself",[h._param("other",z)]);case x&&t:return r===!0?h._(/*BTDS*/"{name} replied to their story",[h._param("name",z)]):h._(/*BTDS*/"{name} replied to your story",[h._param("name",z)]);case x:return h._(/*BTDS*/"{other_name} replied to you",[h._param("other_name",z)]);default:return v!=null?h._(/*BTDS*/"{other} replied to {other_name}",[h._param("other",z),h._param("other_name",v)]):h._(/*BTDS*/"{other} replied to participant",[h._param("other",z)])}}function a(a){var b=a.isAuthorMe,e=a.isFBPostMention;e=e===void 0?!1:e;var f=a.isFBStoryMention;f=f===void 0?!1:f;var g=a.isIGStoryMention;g=g===void 0?!1:g;var h=a.isIGXMAStoryShare,i=a.isMsgHighlightsTabFriendUpdatesReply;i=i===void 0?!1:i;var j=a.isNoteReply;j=j===void 0?!1:j;var m=a.isPostPrivateReply;m=m===void 0?!1:m;var n=a.isStoryHighlightShare;n=n===void 0?!1:n;var o=a.isStoryProducerReply,p=a.isStoryReaction,q=a.isStoryReply,r=a.isStoryShare;r=r===void 0?!1:r;var s=a.replySenderIsMe,t=a.replySourceId,u=a.replyStatusType,v=a.replyToUserId,w=a.sender,x=a.storyShareAuthor;a=a.type_;a=a===d("MAWMsgType").MSG_TYPE.BUMP_EXISTING_MESSAGE;if(t==null&&!(c("justknobx")._("2998")&&Boolean(q))&&!r&&!g&&!f&&!n&&!j&&!e)return;if(v==null||w==null)throw c("FBLogger")("messenger_web").mustfixThrow("Error getting reply snippet without replyToUserId or sender");(k||(k=d("ODS"))).bumpEntityKey(3185,"armadillo_reply","creating_reply_snippet");return(t=l({isAuthorMe:b,isBumpMsg:a,isDeletedMsg:d("MAWMsgReplyStatus").getReplyMessageStatus(u)===c("LSReplyMessageStatus").DELETED,isFBPostMention:e,isFBStoryMention:f,isIGStoryMention:g,isIGXMAStoryShare:h,isMsgHighlightsTabFriendUpdatesReply:i,isNoteReply:j,isPostPrivateReply:m,isStoryHighlightShare:n,isStoryProducerReply:o,isStoryReaction:p,isStoryReply:q,isStoryShare:r,recipientName:"$PARTICIPANT_FIRST_NAME("+v+")",replySenderIsMe:s,replySenderIsRecipient:w===v,senderName:"$PARTICIPANT_FIRST_NAME("+w+")",storyShareAuthor:x}))==null?void 0:t.toString()}g.buildLocalizedReplySnippet=l;g.getReplySnippet=a}),226);
__d("MAWBridgeBuildMsg",["fbt","I64","LSAuthorityLevel","LSIntEnum","LSMessageSendStatus","LSMessageUnsendabilityStatus","MAWAckLevel","MAWBridgeBuildAdminMsg","MAWBridgeBuildMsgMentions","MAWBridgeMsg","MAWBridgeMsgUtils","MAWJids","MAWMsg","MAWMsgErrorUtils","MAWMsgReply","MAWMsgReplySnippet","MAWMsgReplyStatus","MAWMsgType","MAWTimeUtils","MAWVault","MWPBumpEntityKey","gkx","isMWBumpMessage"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j;function k(){for(var a=arguments.length,b=new Array(a),c=0;c<a;c++)b[c]=arguments[c];return b.filter(Boolean).reduce(function(a,b){return[].concat(a,b)},[])}function l(a){return a!=null?d("MAWVault").unvault(a):void 0}function m(a){return a!=null?d("MAWVault").vault(a):void 0}function n(a){return babelHelpers["extends"]({},a,{replyMessageText:m(a.replyMessageText),text:m(a.text)})}function o(a){var b=d("MAWMsgReply").getReplyMessageText(a),e=a.adminContent,f=a.adminType,g=a.adminVersion,i=a.content,j=a.isAdminMessage;i=i;j&&e!=null&&f!=null&&(i=d("MAWBridgeBuildAdminMsg").buildAdminMsgText(e,f,g));j=d("MAWMsgReplySnippet").getReplySnippet(a);a.type_===d("MAWMsgType").MSG_TYPE.BUMP_EXISTING_MESSAGE&&(i=m(a.replySourceId==null?"":j!=null?j:h._(/*BTDS*/"Bumped a message").toString()));e=k(a.mentionedJids,d("MAWMsgReply").getReplyMentionedJids(a));f=a.commands?a.commands:[];g=f.length>0&&c("gkx")("7846");return e.length>0||g?n(d("MAWBridgeBuildMsgMentions").buildMsgTextWithMentions(l(i),e,l(b),g?f:void 0)):{replyMessageText:b,text:i}}function p(a){a=a.ephemeralDurationInSec;return a!=null?(i||(i=d("I64"))).of_int32(a):void 0}function a(a,b,e,f){var g=c("isMWBumpMessage")(b==null?void 0:b.replyType)?b==null?void 0:b.replyMessageText:b==null?void 0:b.text;b=k(e,b==null?void 0:(e=b.mentionIds)==null?void 0:e.split(",").map(d("MAWJids").toUserJid));e=f!=null?f:[];return b.length>0||e.length>0?n(d("MAWBridgeBuildMsgMentions").buildMsgTextWithMentions(l(a),b,l(g),e)):{replyMessageText:g,text:a}}function b(a,b){var e,f=o(a);e=d("MAWTimeUtils").toTimestamp(a.type_===d("MAWMsg").unsent?(e=a.originalTs)!=null?e:a.ts:a.ts);if(d("MAWMsgReply").getReplyAttachmentId(a)!=null&&d("MAWMsgReply").getReplyAttachmentPlaintextHash(a)==null){var g;d("MWPBumpEntityKey").bumpEntityKeyWithAppId("maw.reply_attachment_media_missing_plaintext_hash","new_message_handler_for_reply_type_"+(i||(i=d("I64"))).to_float((g=d("MAWMsgReply").getReplyAttachmentType(a))!=null?g:(i||(i=d("I64"))).zero))}g=d("MAWBridgeMsgUtils").convertStringToLSTransportKeyType(a.transportKey);return babelHelpers["extends"]({},f,{adminMsgCtaId:b==null?void 0:b.ctaId,adminMsgCtaTitle:b==null?void 0:b.title,adminMsgCtaType:b==null?void 0:b.ctaType,authorityLevel:(j||(j=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").AUTHORITATIVE),cannotUnsendReason:a.type_===d("MAWMsg").unsent||!a.isAuthorMe?(j||(j=d("LSIntEnum"))).ofNumber(c("LSMessageUnsendabilityStatus").DENY_FOR_NON_SENDER):void 0,displayedContentTypes:d("MAWMsg").getDisplayedContentTypes(a.type_,a.isForwarded,a.isExpiredXmaMsg,a.replySourceId!=null,a.unsupportedType,a.xmaMessageType),editCount:(i||(i=d("I64"))).of_int32((f=a.editCount)!=null?f:0),ephemeralDurationInSec:p(a),groupId:(b=a.groupId)!=null?b:void 0,groupIndex:a.groupIndex!=null?(i||(i=d("I64"))).of_float(a.groupIndex):void 0,groupSize:a.groupSize!=null?(i||(i=d("I64"))).of_float(a.groupSize):void 0,hotEmojiSize:d("MAWBridgeMsg").getSpecialTextSize(a),isCollapsed:(f=a.isCollapsed)!=null?f:!1,isUnsent:a.type_===d("MAWMsg").unsent,messageId:a.msgId,primarySortKey:i.of_float(a.sortOrderMs),replyAttachmentId:d("MAWMsgReply").getReplyAttachmentId(a),replyAttachmentPlaintextHash:d("MAWMsgReply").getReplyAttachmentPlaintextHash(a),replyAttachmentType:d("MAWMsgReply").getReplyAttachmentType(a),replyMediaExpirationTimestampMs:d("MAWMsgReply").getReplyMediaExpirationTimestampMs(a),replyMediaPreviewHeight:d("MAWMsgReply").getReplyMediaHeight(a),replyMediaPreviewWidth:d("MAWMsgReply").getReplyMediaWidth(a),replySnippet:d("MAWMsgReplySnippet").getReplySnippet(a),replySourceId:d("MAWMsgReply").getReplySourceId(a),replySourceTimestampMs:d("MAWMsgReply").getReplyTimestampMs(a),replySourceType:d("MAWMsgReply").getReplySourceType(a),replySourceTypeV2:d("MAWMsgReply").getReplySourceTypeV2(a),replyStatus:d("MAWMsgReplyStatus").getLSReplyMessageStatus(a),replyToUserId:d("MAWMsgReply").getReplyUserId(a),sendStatus:d("MAWAckLevel").levelToMessagingSendStatus(a.ack),sendStatusV2:d("MAWAckLevel").levelToMessagingSendStatusV2(a.ack),subscriptErrorMessage:q(a),timestampMs:e,transportKey:g,unsentTimestampMs:d("MAWMsg").unsent?d("MAWTimeUtils").toTimestamp(a.ts):void 0})}function q(a){var b=c("gkx")("7471");if(!b)return;b=a.ack;a=a.applicationErrorCode;b=d("MAWAckLevel").levelToMessagingSendStatusV2(b);b=(i||(i=d("I64"))).equal(b,(j||(j=d("LSIntEnum"))).ofNumber(c("LSMessageSendStatus").RETRIABLE_ERROR))||(i||(i=d("I64"))).equal(b,(j||(j=d("LSIntEnum"))).ofNumber(c("LSMessageSendStatus").NON_RETRIABLE_ERROR));if(!b)return;return d("MAWMsgErrorUtils").isWellbeingError(a)?h._(/*BTDS*/"Couldn't send.").toString():h._(/*BTDS*/"Couldn't send. Something went wrong.").toString()}g.getEphemeralDurationInSec=p;g.buildOptimisticMessage=a;g.buildNewAndUpdatedMessageSharedParams=b;g.getSubscriptErrorMessage=q}),226);
__d("MAWUpdateMsg",["MAWBridgeBuildMsg","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,d){return h.apply(this,arguments)}function h(){h=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e){b=d("MAWBridgeBuildMsg").buildNewAndUpdatedMessageSharedParams(b,e);e=babelHelpers["extends"]({},c,{},b);yield a.messages.upsert([c.threadKey,c.timestampMs,c.messageId],e)});return h.apply(this,arguments)}g.call=a}),98);
__d("MAWBridgeMsgUpdatedHandler",["FBLogger","MAWAdminMsgCTA","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWUpdateMsg","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function a(a,e){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.chatJid,"MAWBridgeMsgUpdatedHandler",function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var f,g=(yield (f=d("ReQL")).firstAsync(f.fromTableAscending(a.messages.index("messageId")).getKeyRange(e.msgId)));f=(yield f.firstAsync(f.fromTableAscending(a.attachments).getKeyRange(b,e.msgId)));if(g==null){c("FBLogger")("messenger_web").info("existing message is null for type %s, and attachment is null: %s",e.type_,f==null);return}f=(yield d("MAWAdminMsgCTA").getAdminMsgCtaStep(a,b,e));return d("MAWUpdateMsg").call(a,e,g,f)});return function(b,c){return a.apply(this,arguments)}}())}g.call=a}),98);
__d("LSClearMessagePlaceholderRangeStoredProcedure",["LSClearMessagePlaceholderRange","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSClearMessagePlaceholderRange"),e.threadKey,e.messageId,e.maxTimestampMs);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("LSInsertNewMessageRangeStoredProcedure",["LSInsertNewMessageRange","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSInsertNewMessageRange"),e.threadKey,e.newMinTs,e.newMaxTs,e.newMinMid,e.newMaxMid,e.lowerOverlap,e.upperOverlap,e.hasMoreBefore,e.hasMoreAfter,e.requestedMessageId);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("MAWBridgeMsgsLoadedHandler",["I64","LSClearMessagePlaceholderRangeStoredProcedure","LSFactory","LSInsertNewMessageRangeStoredProcedure","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWTimeUtils","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.chatJid,"MAWBridgeMsgsLoadedHandler",function(a,e){return c("LSClearMessagePlaceholderRangeStoredProcedure")(c("LSFactory")(a),{maxTimestampMs:d("MAWTimeUtils").MAX_TIMESTAMP_MS,messageId:"",threadKey:e}).then(function(){if(c("gkx")("23927")){var f,g,i,j,k,l,m,n;f=(f=(f=b.rangeExtensionType)==null?void 0:f.hasMoreBefore)!=null?f:!1;g=(g=(g=b.rangeExtensionType)==null?void 0:g.hasMoreAfter)!=null?g:!1;i=f&&((i=b.rangeExtensionType)==null?void 0:i.minMsgTs)!=null?(h||(h=d("I64"))).of_float(b.rangeExtensionType.minMsgTs):d("MAWTimeUtils").MIN_TIMESTAMP_MS;j=g&&((j=b.rangeExtensionType)==null?void 0:j.maxMsgTs)!=null?(h||(h=d("I64"))).of_float(b.rangeExtensionType.maxMsgTs):d("MAWTimeUtils").MAX_TIMESTAMP_MS;k=(k=(k=b.rangeExtensionType)==null?void 0:k.minMsgId)!=null?k:"";l=(l=(l=b.rangeExtensionType)==null?void 0:l.maxMsgId)!=null?l:"";m=((m=b.rangeExtensionType)==null?void 0:m.rangeKind)==="before"?i:d("MAWTimeUtils").MIN_TIMESTAMP_MS;n=((n=b.rangeExtensionType)==null?void 0:n.rangeKind)==="after"?j:d("MAWTimeUtils").MAX_TIMESTAMP_MS;return c("LSInsertNewMessageRangeStoredProcedure")(c("LSFactory")(a),{hasMoreAfter:g,hasMoreBefore:f,lowerOverlap:m,newMaxMid:l,newMaxTs:j,newMinMid:k,newMinTs:i,threadKey:e,upperOverlap:n})}else{f=((g=b.rangeExtensionType)==null?void 0:g.minMsgTs)!=null?(h||(h=d("I64"))).of_float(b.rangeExtensionType.minMsgTs):d("MAWTimeUtils").MIN_TIMESTAMP_MS;l=((m=b.rangeExtensionType)==null?void 0:m.maxMsgTs)!=null?(h||(h=d("I64"))).of_float(b.rangeExtensionType.maxMsgTs):d("MAWTimeUtils").MAX_TIMESTAMP_MS;i=(k=(j=b.rangeExtensionType)==null?void 0:j.minMsgId)!=null?k:"";m=(g=(n=b.rangeExtensionType)==null?void 0:n.maxMsgId)!=null?g:"";k=((j=b.rangeExtensionType)==null?void 0:j.rangeKind)==="extensionBefore"?f:d("MAWTimeUtils").MIN_TIMESTAMP_MS;g=((n=b.rangeExtensionType)==null?void 0:n.rangeKind)==="extensionAfter"?l:d("MAWTimeUtils").MAX_TIMESTAMP_MS;j=(n=(j=b.rangeExtensionType)==null?void 0:j.hasMoreBefore)!=null?n:!1;n=(n=(n=b.rangeExtensionType)==null?void 0:n.hasMoreAfter)!=null?n:!1;return c("LSInsertNewMessageRangeStoredProcedure")(c("LSFactory")(a),{hasMoreAfter:n,hasMoreBefore:j,lowerOverlap:k,newMaxMid:m,newMaxTs:l,newMinMid:i,newMinTs:f,threadKey:e,upperOverlap:g})}})})}g.call=a}),98);
__d("MAWEphemeralMessageExpirationStorage",["LSDatabaseSingleton","MAWBridgeDeleteMessagesHandler","Promise","asyncToGeneratorRuntime","clearTimeout","promiseDone","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=new Map();function k(a,b,c){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e){yield (i||(i=b("Promise"))).all(c.map(function(b){var c=b.ts,e=b.msgId;e={msgId:e,ts:c};c=b.threadJid;b=[e];e={messages:b,threadJid:c};return d("MAWBridgeDeleteMessagesHandler").call(a,e)})),j["delete"](e)});return l.apply(this,arguments)}function m(a,e,g){return c("setTimeout")(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var g=(yield (h||(h=d("LSDatabaseSingleton"))).LSDatabaseSingleton);c("promiseDone")(g.runInTransaction(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){yield k(b,a,e)});return function(a){return c.apply(this,arguments)}}(),"readwrite",void 0,void 0,f.id+":66"))}),g)}function a(a,b,d,e){a=j.get(d);var f;if(a==null)f=[b];else{c("clearTimeout")(a.timeoutId);var g=a.msgs.map(function(a){return a.msgId}).indexOf(b.msgId)===-1;g&&a.msgs.push(b);f=a.msgs}g=m(f,d,e);j.set(d,{expirationTs:d,msgs:f,timeoutId:g})}function e(a){var b=a.msgId;a=j.get(a.countdownTs);if(a==null)return;b=a.msgs.map(function(a){return a.msgId}).indexOf(b);if(b>-1){a.msgs.splice(b,1);return}}g.setMessageForExpiration=a;g.clearMessageFromExpiration=e}),98);
__d("MAWBridgeMsgsStartCountdownHandler",["I64","MAWEphemeralMessageExpirationStorage","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWTimeUtils","Promise","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,c){return c.msgs.reduce(function(c,e){return c.then(function(){d("MAWEphemeralMessageExpirationStorage").setMessageForExpiration(a,e,e.countdownTs,e.millisecondsUntilCountdownTs);(i||(i=d("I64"))).of_int32(e.millisecondsUntilCountdownTs);return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.threadJid,"MAWBridgeMsgsStartCountdownHandler",function(a,c){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.messages).getKeyRange(c,d("MAWTimeUtils").toTimestamp(e.ts),e.msgId)).then(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(c){if(c==null)return(h||(h=b("Promise"))).resolve();c=babelHelpers["extends"]({},c,{ephemeralExpirationTs:(i||(i=d("I64"))).of_float(e.countdownTs)});yield a.messages.put(c)});return function(a){return c.apply(this,arguments)}}())})})},(h||(h=b("Promise"))).resolve())}g.call=a}),98);
__d("MAWMedia",["LSIntEnum","MAWDbMedia","MessagingAttachmentType"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){if(a===d("MAWDbMedia").IMAGE)return b!=null?(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").EPHEMERAL_IMAGE):(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").IMAGE);else if(a===d("MAWDbMedia").VIDEO)return b!=null?(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").EPHEMERAL_VIDEO):(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").VIDEO);else if(a===d("MAWDbMedia").PTT)return(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").AUDIO);else if(a===d("MAWDbMedia").GIF)return(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").ANIMATED_IMAGE);else if(a===d("MAWDbMedia").STICKER)return(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").STICKER);else if(a===d("MAWDbMedia").DOCUMENT_FILE)return(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").FILE);return(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").NONE)}g.mediaTypeToMessageAttachmentType=a}),98);
__d("MAWBridgeNewMediaHandler",["FBLogger","I64","LSAuthorityLevel","LSIntEnum","LSThreadMediaGalleryGroup","MAWDbMedia","MAWMedia","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","Promise","ReQL","WAMediaHdType","asyncToGeneratorRuntime","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(a,b,e,f,g,h){var k=b.messagesSortOrderMs?(i||(i=d("I64"))).of_float(b.messagesSortOrderMs[e]):b.sortOrderMs!=null?(i||(i=d("I64"))).of_float(b.sortOrderMs):(i||(i=d("I64"))).of_float(b.ts*1e3);return{accessibilitySummaryText:b.accessibilitySummaryText,attachmentFbid:f,attachmentIndex:(f=a==null?void 0:a.attachmentIndex)!=null?f:(i||(i=d("I64"))).zero,attachmentType:(f=a==null?void 0:a.attachmentType)!=null?f:d("MAWMedia").mediaTypeToMessageAttachmentType(b.mediaType,b.ephemeralMediaViewMode),authorityLevel:(j||(j=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").AUTHORITATIVE),ephemeralMediaState:b.ephemeralMediaState!=null?(i||(i=d("I64"))).of_int32(b.ephemeralMediaState):a==null?void 0:a.ephemeralMediaState,ephemeralMediaViewMode:b.ephemeralMediaViewMode!=null?(i||(i=d("I64"))).of_int32(b.ephemeralMediaViewMode):a==null?void 0:a.ephemeralMediaViewMode,filename:(f=(f=b.filenames!=null?b.filenames[e]:null)!=null?f:b.defaultFilename)!=null?f:a==null?void 0:a.filename,filesize:b.filesize==null?a==null?void 0:a.filesize:(i||(i=d("I64"))).of_int32(b.filesize),hasMedia:b.hasMedia,hasXma:!1,isHd:c("gkx")("9576")?((f=b.hdTypes)==null?void 0:f[e])===d("WAMediaHdType").HD_TYPE_HQ_4K:void 0,isSharable:!1,messageId:e,offlineAttachmentId:g,playableDurationMs:b.duration==null?a==null?void 0:a.playableDurationMs:(i||(i=d("I64"))).of_int32(b.duration),previewHeight:b.previewHeight==null?a==null?void 0:a.previewHeight:(i||(i=d("I64"))).of_int32(b.previewHeight),previewHeightLarge:b.previewHeightLarge==null?a==null?void 0:a.previewHeightLarge:(i||(i=d("I64"))).of_int32(b.previewHeightLarge),previewWidth:b.previewWidth==null?a==null?void 0:a.previewWidth:(i||(i=d("I64"))).of_int32(b.previewWidth),previewWidthLarge:b.previewWidthLarge==null?a==null?void 0:a.previewWidthLarge:(i||(i=d("I64"))).of_int32(b.previewWidthLarge),threadKey:h,timestampMs:k,titleText:b.accessibilitySummaryText,transportKey:(f=b.transportKey)!=null?f:"WhatsApp"}}function l(a,b){var c=b.hasPreviewMedia!=null;return{id:a,mainMediaStatus:b.hasMedia?(j||(j=d("LSIntEnum"))).ofNumber(1):(j||(j=d("LSIntEnum"))).ofNumber(4),mainMediaStatusDetails:"initial_media_download",previewMediaStatus:c?b.hasPreviewMedia===!0?(j||(j=d("LSIntEnum"))).ofNumber(1):(j||(j=d("LSIntEnum"))).ofNumber(4):void 0,previewMediaStatusDetails:c?"initial_media_download":void 0}}function m(a,b,c){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f){var g=e.mediaId,j=e.msgIds,l=e.offlineAttachmentId,m=e.plaintextHash;yield o(a,e);var n=q(g,m),p=l!=null?l:n;g=!1;var s=r(e),t=null;c("gkx")("4219")&&(t=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.attachments_ranges_v2__generated).getKeyRange(f).filter(function(a){return(i||(i=d("I64"))).equal(a.mediaGroup,s)}))));yield (h||(h=b("Promise"))).all(j.map(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){var c=a.attachments,h=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(c).getKeyRange(f,b)));b=k(h,e,b,n,p,f);e.skipShouldUpdateHasMore!==!0&&t!=null&&(i||(i=d("I64"))).le(b.timestampMs,t.minTimestampMs)&&(g=!0);if(h!=null){return c.upsert([h.threadKey,h.messageId,h.attachmentFbid],babelHelpers["extends"]({},b,{transportKey:(h=h.transportKey)!=null?h:b.transportKey}))}else return c.add(b)});return function(a){return c.apply(this,arguments)}}()));if(t!=null&&t.hasMoreBefore!==!0&&g&&c("gkx")("4219")){m=babelHelpers["extends"]({},t,{hasMoreBefore:!0});yield a.attachments_ranges_v2__generated.upsert([t.threadKey,t.mediaGroup,t.minTimestampMs],m)}});return n.apply(this,arguments)}function o(a,b){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){if(c("gkx")("11968")!==!0)return;var e=b.mediaId,f=b.plaintextHash;if(f==null){c("FBLogger")("messenger_web_media").mustfix("[NewMedia] No valid media plaintextHash found in reducer %s",e);return}e=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.client_media_status).getKeyRange(f)));e=e!=null&&!(i||(i=d("I64"))).equal(e.mainMediaStatus,(j||(j=d("LSIntEnum"))).ofNumber(4));if(e)return;yield a.client_media_status.add(l(f,b))});return p.apply(this,arguments)}function q(a,b){return""+(b!=null?b:a.toString())}function r(a){switch(a.mediaType){case d("MAWDbMedia").MEDIA_TYPE.IMAGE:case d("MAWDbMedia").MEDIA_TYPE.VIDEO:return(j||(j=d("LSIntEnum"))).ofNumber(c("LSThreadMediaGalleryGroup").PHOTOS_AND_VIDEOS);case d("MAWDbMedia").MEDIA_TYPE.DOCUMENT_FILE:return(j||(j=d("LSIntEnum"))).ofNumber(c("LSThreadMediaGalleryGroup").FILES_ONLY);default:return(j||(j=d("LSIntEnum"))).ofNumber(c("LSThreadMediaGalleryGroup").PHOTOS_AND_VIDEOS)}}function a(a,b){var c=b.threadJid;return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,c,"MAWBridgeNewMediaHandler",function(a,c){return m(a,b,c)})}g.callWithoutWaitingForAnything=m;g.call=a}),98);
__d("LSUpsertAttachmentRange",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[];return b.sequence([function(c){return b.sequence([function(c){return b.forEach(b.db.table(17).fetch([[[a[0],a[1]]]]),function(a){return a["delete"]()})},function(c){return b.db.table(17).add({threadKey:a[0],mediaGroup:a[1],minTimestampMs:a[2],maxTimestampMs:b.i64.cast([2328,1316134911]),hasMoreBefore:a[3],hasMoreAfter:!1,isLoadingBefore:!1,isLoadingAfter:!1,lastLoadedMessageId:a[4]})}])},function(a){return b.resolve(c)}])}a.__sproc_name__="LSMailboxUpsertAttachmentRangeStoredProcedure";a.__tables__=["attachments_ranges_v2__generated"];e.exports=a}),null);
__d("LSUpsertAttachmentRangeStoredProcedure",["LSSynchronousPromise","LSUpsertAttachmentRange","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSUpsertAttachmentRange"),e.threadKey,e.mediaGroup,e.newMinTs,e.hasMoreBefore,e.newLastLoadedMessageId);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("MAWBridgeNewMediaRangeHandler",["I64","LSFactory","LSIntEnum","LSUpsertAttachmentRangeStoredProcedure","MAWMediaGalleryEBTaggingUtils","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","cr:20238","emptyFunction"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a,e){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.threadJid,"MAWBridgeNewMediaRangeHandler",function(a,f){var g=(h||(h=d("I64"))).of_float(e.earliestLoadedMediaTs),j=e.mediaGalleryGroup,k=b("cr:20238")!=null?b("cr:20238").getMediaRestoreEBRange().getRange(f):null;k=Boolean(k==null?void 0:k.hasMoreBefore)&&d("MAWMediaGalleryEBTaggingUtils").isMediaGalleryGroupSupportedForTaggedRestore(j);k=e.hasMoreBefore||k;return c("LSUpsertAttachmentRangeStoredProcedure")(c("LSFactory")(a),{hasMoreBefore:k,mediaGroup:(i||(i=d("LSIntEnum"))).ofNumber(j),newLastLoadedMessageId:e.lastLoadedMessageId,newMinTs:g,threadKey:f})})}function a(a,b){return j(a,b).then(c("emptyFunction"))}g.call=a}),98);
__d("MAWBridgeNewMsgHandler",["FBLogger","I64","LSIntEnum","LSMessageRenderingType","LSQuickReplyType","MAWAdminMsgCTA","MAWBridgeBuildMsg","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWMpsGating","MAWMsgReply","MAWMsgType","MAWUpdateMsg","MAWXMAUtils","MWPBumpEntityKey","MpsMessageToBridge","MpsOverBridge","MpsTypes","Promise","ReQL","asyncToGeneratorRuntime","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(a,b,c,d){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f){e.unsupportedType==="stickerReceiverFetchMessage"&&d("MWPBumpEntityKey").bumpEntityKeyWithAppId("maw.new_msg_handler","sticker_receiver_fetch_fallback");f=babelHelpers["extends"]({},d("MAWBridgeBuildMsg").buildNewAndUpdatedMessageSharedParams(e,f),{forwardScore:(i||(i=d("I64"))).of_float(e.forwardingScore),isAdminMessage:e.isAdminMessage,isForwarded:e.isForwarded,messageRenderingType:(j||(j=d("LSIntEnum"))).ofNumber(c("LSMessageRenderingType").DEFAULT),offlineThreadingId:e.externalId,quickReplyType:j.ofNumber(c("LSQuickReplyType").NONE),replyType:d("MAWMsgReply").getReplyType(e),senderId:i.of_string(e.sender),threadKey:b,viewFlags:j.ofNumber(0)});yield a.messages.add(f)});return l.apply(this,arguments)}function m(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.chatJid,"BridgeNewMsgHandler",function(a,c){return p(a,b,c)})}function n(a){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=a.replySourceId;if(!d("MAWMpsGating").isFullMpsEnabled()||b==null)return a;var c=(yield d("MpsOverBridge").mps().loadMessage({config:{shouldFetchSupplementals:!0,strategy:"local-first"},debug:{purpose:"reply-fetch-local"},messageId:d("MpsTypes").toMessageId(b),threadId:d("MpsTypes").toThreadId(a.chatJid)})),e=c.success;c=c.value;if(e===!1)return a;if(c==null)return babelHelpers["extends"]({},a,{replySourceId:b,replyStatusType:d("MAWMsgType").MSG_TYPE.REVOKED});b=(e=d("MpsMessageToBridge").mpsFullMessagetoBridge(c))==null?void 0:e.topLevel;return b==null?a:babelHelpers["extends"]({},a,{replyMentionedJids:b.value.mentionedJids,replyMessageText:b.value.content,replySenderIsMe:b.value.isAuthorMe,replySourceId:b.value.msgId,replySourceTimestampMs:b.value.sortOrderMs,replyStatusType:b.value.type_,replyToUserId:b.value.sender})});return o.apply(this,arguments)}function p(a,b,c){return q.apply(this,arguments)}function q(){q=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){b=(yield n(b));if(d("MAWXMAUtils").isXMAStoryReply(b.xmaMessageType))return;if(b.isExpiredMsg===!0&&c("justknobx")._("3991")){c("FBLogger")("messenger_web").mustfix("Attempted to insert an expired message");return}var f=(yield d("MAWAdminMsgCTA").getAdminMsgCtaStep(a,e,b)),g=b.offlineMsg,h;g!=null?h=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.messages).getKeyRange(e,(i||(i=d("I64"))).of_float(g.ts),g.msgId))):d("MAWMpsGating").isFullMpsEnabled()&&(h=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.messages.index("messageId")).getKeyRange(b.msgId))));h==null?yield k(a,e,b,f):yield d("MAWUpdateMsg").call(a,b,h,f);d("MAWMpsGating").isFullMpsEnabled()&&((g=b.editCount)!=null?g:0)>0&&(yield r(a,b))});return q.apply(this,arguments)}function r(a,b){return s.apply(this,arguments)}function s(){s=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){var e=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.messages.index("replySourceIdMessageID")).getKeyRange(c.msgId)));yield (h||(h=b("Promise"))).all(e.map(function(b){b=babelHelpers["extends"]({},b,{replyMessageText:c.content});return a.messages.put(b)}))});return s.apply(this,arguments)}function a(a,b){return m(a,b)}function e(a,b){return t.apply(this,arguments)}function t(){t=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){yield (h||(h=b("Promise"))).all(c.map(function(b){return m(a,b)}))});return t.apply(this,arguments)}g.callWithoutWaitingForAnything=p;g.call=a;g.bulkCall=e}),98);
__d("LSGroupPollEventType",[],(function(a,b,c,d,e,f){a=Object.freeze({QUESTION_CREATION:1,ADD_OPTION:2,ADD_UNVOTED_OPTION:3,UPDATE_VOTE:4,MULTIPLE_UPDATES:5,QUESTION_CLOSED:6,QUESTION_BUMPED:7,QUESTION_DELETION_SUCCESS:8,QUESTION_DELETION_FAILURE:9});f["default"]=a}),66);
__d("LSPollQuestionType",[],(function(a,b,c,d,e,f){a=Object.freeze({CHOOSE_MULTIPLE:0,MOST_LIKELY_TO:1,NON_POLL:2});f["default"]=a}),66);
__d("MAWBridgePollUtils",["fbt","FBLogger","I64","MAWUserJidWrapper","WAJids"],(function(a,b,c,d,e,f,g,h){"use strict";var i;function a(a){return a.toSorted(function(a,b){if(a.numVotes!==b.numVotes)return b.numVotes-a.numVotes;return!(i||(i=d("I64"))).equal(a.sortKeyVotingTimestamp,b.sortKeyVotingTimestamp)?(i||(i=d("I64"))).compare(b.sortKeyVotingTimestamp,a.sortKeyVotingTimestamp):(i||(i=d("I64"))).compare(a.sortKeyCreationTimestamp,b.sortKeyCreationTimestamp)})}function b(a){if(a===d("WAJids").AUTHOR_SYSTEM)throw c("FBLogger")("messenger_web").mustfixThrow("Not a valid author for poll vote");return a===d("WAJids").AUTHOR_ME?(i||(i=d("I64"))).of_string(d("WAJids").userIdFromJid(d("MAWUserJidWrapper").getMyUserJid())):(i||(i=d("I64"))).of_string(d("WAJids").userIdFromJid(a))}function e(a,b){switch(a.length){case 0:return h._(/*BTDS*/"{option_name}, no votes",[h._param("option_name",b)]);case 1:return h._(/*BTDS*/"{option_name}, Voted for by {voter_name}",[h._param("option_name",b),h._param("voter_name",a[0])]);case 2:return h._(/*BTDS*/"{option_name}, Voted for by {first_voter_name}, {second_voter_name}",[h._param("option_name",b),h._param("first_voter_name",a[0]),h._param("second_voter_name",a[1])]);case 3:return h._(/*BTDS*/"{option_name}, Voted for by {first_voter_name}, {second_voter_name} and {third_voter_name}",[h._param("option_name",b),h._param("first_voter_name",a[0]),h._param("second_voter_name",a[1]),h._param("third_voter_name",a[2])]);default:return h._(/*BTDS*/"{option_name}, Voted for by {first_voter_name}, {second_voter_name} and {remaining_count} others",[h._param("option_name",b),h._param("first_voter_name",a[0]),h._param("second_voter_name",a[1]),h._param("remaining_count",a.length-2)])}}g.sortPollXMAOptions=a;g.getContactIdForAuthor=b;g.getListItemAccessibilityText=e}),226);
__d("MNLSXMATemplateType",[],(function(a,b,c,d,e,f){a=Object.freeze({GENERIC_SHARE:0,DEFAULT:-1});f["default"]=a}),66);
__d("QuestionPollStage",[],(function(a,b,c,d,e,f){a=Object.freeze({NOMINATING:0,VOTING:1,CLOSED:2});f["default"]=a}),66);
__d("MAWBridgeNewPollHandler",["fbt","FBLogger","I64","LSAuthorityLevel","LSGroupPollEventType","LSIntEnum","LSPollQuestionType","MAWBridgePollUtils","MAWDbPoll","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MNLSXMALayoutType","MNLSXMATemplateType","MessagingAttachmentType","Promise","QuestionPollStage","ReQL","WAJids","asyncToGeneratorRuntime","nullthrows"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k,l=3;function m(a,b,c){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){var e=b.contactIdForCurrentUser,f=b.dbPoll,g=b.latestUpdateMsgId,h=b.latestUpdateTimestampMs;b=b.transportKey;var i=f.pollStanzaId;i=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.attachments.index("idx_attachments_collapsible_id")).getKeyRange(c,(k||(k=d("I64"))).of_string(i))));i!=null&&(yield a.attachments["delete"](i.threadKey,i.messageId,i.attachmentFbid));yield o(a,f,c,g,h,e,b)});return n.apply(this,arguments)}function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.dbPoll.chatJid,"BridgeNewPollHandler",function(a,c){return m(a,b,c)})}function o(a,b,c,d,e,f,g){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f,g,h,j,m){var n=e.latestSenderTimestampsMs,o=e.pollParticipantCount,p=e.pollStanzaId,r=(k||(k=d("I64"))).of_string(p),t=(yield d("ReQL").firstAsync(d("ReQL").fromTableDescending(a.polls).getKeyRange(r)));t==null&&(yield s(a,r,g,h,f));t=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableDescending(a.poll_options_v2).getKeyRange(r)));var v=new Map(e.pollOptions.entries().map(function(a){var b=a[0];a=a[1];return[d("MAWDbPoll").generateStableIdFromOptionHash(b,p).toString(),a]})),w=new Map();t.forEach(function(a){var b=(k||(k=d("I64"))).to_string(a.optionId);w.set(b,babelHelpers["extends"]({},w.get(b),{lsOption:a}))});v.entries().forEach(function(a){var b=a[0];a=a[1];a.optionText!=null&&w.set(b,babelHelpers["extends"]({},w.get(b),{mawDbOption:a}))});t=w.entries().toArray().sort(function(a,b){a[0];a=a[1];var d=a.lsOption;a=a.mawDbOption;b[0];b=b[1];var e=b.lsOption;b=b.mawDbOption;d=c("nullthrows")((d=d==null?void 0:d.optionText)!=null?d:a==null?void 0:a.optionText);e=c("nullthrows")((a=e==null?void 0:e.optionText)!=null?a:b==null?void 0:b.optionText);return d.localeCompare(e)});v=(yield (i||(i=b("Promise"))).all(t.map(function(){var e=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b,e){var f=b[0];b=b[1];var g=b.lsOption;b=b.mawDbOption;e=(k||(k=d("I64"))).of_int32(e);if(g==null&&b==null)return null;f=k.of_string(f);if(b==null){var h=c("nullthrows")(g);h=h.optionText;yield a.poll_options_v2.upsert([r,f],babelHelpers["extends"]({},c("nullthrows")(g),{sortKeyCreationTimestamp:e}));var i=(yield d("ReQL").firstAsync(d("ReQL").fromTableDescending(a.poll_votes_v2).getKeyRange(r,f,j)));return{contactIds:[j],hasVoteFromCurrentUser:!0,numVotes:1,optionId:f,optionText:h,progressBarFilledPercentage:Math.floor(1/o*100),sortKeyCreationTimestamp:e,sortKeyVotingTimestamp:i!=null?i.timestampMs:(k||(k=d("I64"))).zero}}return q(a,r,f,c("nullthrows")(b.optionText),g,b,e,n,j,o)});return function(a,b){return e.apply(this,arguments)}}())).then(function(a){return a.filter(Boolean)}));t=v.some(function(a){return a.hasVoteFromCurrentUser});v=d("MAWBridgePollUtils").sortPollXMAOptions(v).slice(0,l);yield u(a,e,f,g,h,v,t,m)});return p.apply(this,arguments)}function q(a,b,c,d,e,f,g,h,i,j){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f,g,h,j,l,m,n,o){j=j.voteAuthors;j=Array.from(j).map(function(a){var b=m.get(a);if(b==null)throw c("FBLogger")("messenger_web").mustfixThrow("Missing latest sender timestamp for poll voter from dbPoll");return{contactId:d("MAWBridgePollUtils").getContactIdForAuthor(a),latestSenderTimestampMs:b}});var p=h!=null?yield d("ReQL").toArrayAsync(d("ReQL").fromTableDescending(a.poll_votes_v2).getKeyRange(e,f)):[],q=p.find(function(a){return(k||(k=d("I64"))).equal(a.contactId,n)}),r=m.get(d("WAJids").AUTHOR_ME),s=q!=null&&(r==null||r<(k||(k=d("I64"))).to_float(q.timestampMs));yield (i||(i=b("Promise"))).all(p.map(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){if((k||(k=d("I64"))).equal(b.contactId,n)&&s)return;yield a.poll_votes_v2["delete"](e,f,b.contactId)});return function(a){return c.apply(this,arguments)}}()));yield i.all(j.map(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){var c=b.contactId;b=b.latestSenderTimestampMs;yield a.poll_votes_v2.put({contactId:c,optionId:f,pollId:e,timestampMs:(k||(k=d("I64"))).of_float(b)})});return function(a){return c.apply(this,arguments)}}()));r=(k||(k=d("I64"))).of_float(Math.max.apply(Math,j.map(function(a){return a.latestSenderTimestampMs}).concat([0])));p=s?(k||(k=d("I64"))).max(c("nullthrows")(q==null?void 0:q.timestampMs),r):r;q=j.length+(s?1:0);r={optionId:f,optionText:g,pollId:e,sortKeyCreationTimestamp:l,sortKeyVotingTimestamp:p,voteCount:k.of_int32(q)};h!=null?yield a.poll_options_v2.upsert([e,f],r):yield a.poll_options_v2.add(r);h=j.map(function(a){return a.contactId});s&&h.push(n);return{contactIds:h,hasVoteFromCurrentUser:s||j.find(function(a){return(k||(k=d("I64"))).equal(a.contactId,n)})!=null,numVotes:q,optionId:f,optionText:g,progressBarFilledPercentage:Math.floor(q/o*100),sortKeyCreationTimestamp:l,sortKeyVotingTimestamp:p}});return r.apply(this,arguments)}function s(a,b,c,d,e){return t.apply(this,arguments)}function t(){t=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f,g){e={authorityLevel:(j||(j=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").AUTHORITATIVE),lastUpdateMessageEventType:j.ofNumber(c("LSGroupPollEventType").QUESTION_CREATION),lastUpdateMessageId:e,lastUpdateMessageTimestampMs:(k||(k=d("I64"))).of_float(f),pollId:b,pollStage:j.ofNumber(c("QuestionPollStage").VOTING),pollType:j.ofNumber(c("LSPollQuestionType").CHOOSE_MULTIPLE),threadKey:g};yield a.polls.put(e)});return t.apply(this,arguments)}function u(a,b,c,d,e,f,g,h){return v.apply(this,arguments)}function v(){v=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f,g,m,n,o,p){var q=e.pollStanzaId,r=e.title;if(n.length===0){c("FBLogger")("messenger_web").mustfix("Items length for poll XMA should be greater than 0");return}var s=(yield d("ReQL").toArrayAsync(d("ReQL").mergeJoin(d("ReQL").fromTableAscending(a.participants,[]).getKeyRange(f),d("ReQL").fromTableAscending(a.contacts,["name","id","profilePictureFallbackUrl","profilePictureUrl","profilePictureUrlExpirationTimestampMs"])).map(function(a){a[0];a=a[1];return a}).filter(Boolean))),t=new Map(s.map(function(a){return[(k||(k=d("I64"))).to_string(a.id),a]}));s=n.map(function(a){var b=a.contactIds,c=a.optionId,e=a.optionText;a=a.progressBarFilledPercentage;var f=b.reduce(function(a,b){var c;b=t.get((k||(k=d("I64"))).to_string(b));a.contactUrlExpirations.push((b==null?void 0:b.profilePictureUrlExpirationTimestampMs)!=null?(k||(k=d("I64"))).to_string(b.profilePictureUrlExpirationTimestampMs):"");a.contactUrlFallbacks.push((c=b==null?void 0:b.profilePictureFallbackUrl)!=null?c:"");a.contactUrls.push((c=b==null?void 0:b.profilePictureUrl)!=null?c:"");(b==null?void 0:b.name)!=null&&a.contactNames.push(b.name);return a},{contactNames:[],contactUrlExpirations:[],contactUrlFallbacks:[],contactUrls:[]}),g=f.contactNames,h=f.contactUrlExpirations,i=f.contactUrlFallbacks;f=f.contactUrls;return{listItemAccessibilityText:d("MAWBridgePollUtils").getListItemAccessibilityText(g,e).toString(),listItemContactUrlExpirationTimestampList:h.length>0?h.join(", "):void 0,listItemContactUrlFallbackList:i.length>0?i.join(", "):void 0,listItemContactUrlList:f.length>0?f.join(", "):void 0,listItemId:c,listItemProgressBarFilledPercentage:(k||(k=d("I64"))).of_int32(a),listItemTitleText:e,listItemTotalCount:k.of_int32(b.length)}});var u=(k||(k=d("I64"))).of_string(q);o=o?h._(/*BTDS*/"Change vote").toString():h._(/*BTDS*/"Vote").toString();var v={actionContentBlob:r,attachmentFbid:q,attachmentIndex:k.zero,ctaId:u,messageId:g,targetId:k.of_string(q),threadKey:f,title:o,type_:"xma_poll_details_card"},w={attachmentCta1Id:u,attachmentFbid:q,attachmentIndex:k.zero,cta1Title:o,cta1Type:"xma_poll_details_card",defaultActionEnableExtensions:!1,defaultCtaId:u,defaultCtaTitle:o,defaultCtaType:"xma_poll_details_card",faviconUrlExpirationTimestampMs:k.zero,messageId:g,threadKey:f};o={attachmentCta1Id:u,attachmentFbid:q,attachmentIndex:k.zero,attachmentType:(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").XMA),authorityLevel:j.ofNumber(c("LSAuthorityLevel").AUTHORITATIVE),collapsibleId:k.of_string(q),cta1Title:o,cta1Type:"xma_poll_details_card",defaultCtaId:u,defaultCtaTitle:o,defaultCtaType:"xma_poll_details_card",hasMedia:!1,hasXma:!0,isSharable:!1,listItemsDescriptionText:r,listItemsId:k.of_string(q),messageId:g,threadKey:f,timestampMs:k.of_float(m),transportKey:p!=null?p:"WhatsApp",xmaLayoutType:j.ofNumber(c("MNLSXMALayoutType").XCENTER),xmasTemplateType:j.ofNumber(c("MNLSXMATemplateType").GENERIC_SHARE)};r=e.pollOptions.values().filter(function(a){return a.optionText!=null}).toArray().length;m=r>l?h._(/*BTDS*/"_j{\"*\":\"{additional_option_count} more options\",\"_1\":\"{additional_option_count} more option\"}",[h._plural(r-l),h._param("additional_option_count",r-l)]).toString():void 0;p=babelHelpers["extends"]({},o,{listItemAccessibilityText1:s[0].listItemAccessibilityText,listItemAccessibilityText2:s.length>1?s[1].listItemAccessibilityText:void 0,listItemAccessibilityText3:s.length>2?s[2].listItemAccessibilityText:void 0,listItemContactUrlExpirationTimestampList1:s[0].listItemContactUrlExpirationTimestampList,listItemContactUrlExpirationTimestampList2:s.length>1?s[1].listItemContactUrlExpirationTimestampList:void 0,listItemContactUrlExpirationTimestampList3:s.length>2?s[2].listItemContactUrlExpirationTimestampList:void 0,listItemContactUrlFallbackList1:s[0].listItemContactUrlFallbackList,listItemContactUrlFallbackList2:s.length>1?s[1].listItemContactUrlFallbackList:void 0,listItemContactUrlFallbackList3:s.length>2?s[2].listItemContactUrlFallbackList:void 0,listItemContactUrlList1:s[0].listItemContactUrlList,listItemContactUrlList2:s.length>1?s[1].listItemContactUrlList:void 0,listItemContactUrlList3:s.length>2?s[2].listItemContactUrlList:void 0,listItemId1:s[0].listItemId,listItemId2:s.length>1?s[1].listItemId:void 0,listItemId3:s.length>2?s[2].listItemId:void 0,listItemProgressBarFilledPercentage1:s[0].listItemProgressBarFilledPercentage,listItemProgressBarFilledPercentage2:s.length>1?s[1].listItemProgressBarFilledPercentage:void 0,listItemProgressBarFilledPercentage3:s.length>2?s[2].listItemProgressBarFilledPercentage:void 0,listItemsSecondaryDescriptionText:m,listItemTitleText1:n[0].optionText,listItemTitleText2:n.length>1?n[1].optionText:void 0,listItemTitleText3:n.length>2?n[2].optionText:void 0,listItemTotalCount1:s[0].listItemTotalCount,listItemTotalCount2:s.length>1?s[1].listItemTotalCount:void 0,listItemTotalCount3:s.length>2?s[2].listItemTotalCount:void 0});yield (i||(i=b("Promise"))).all([a.attachments.upsert([f,g,q],p),a.attachment_ctas.upsert([u],v),a.attachment_items.upsert([q,k.zero],w)])});return v.apply(this,arguments)}g.POLL_OPTIONS_THRESHOLD=l;g.call=a}),226);
__d("MAWBridgeNewReceiverFetchInfoHandler",["FBLogger","I64","LSAuthorityLevel","LSIntEnum","LSMediaUrlUtils","MAWImageUtils","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MessagingAttachmentType","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a,b,c){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){var e=b.msgId,f=b.receiverFetchId,g=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.attachments).getKeyRange(c,e)));b=l(b,c,g);yield a.attachments.upsert([c,e,f],b)});return k.apply(this,arguments)}function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.threadJid,"BridgeNewReceiverFetchInfoHandler",function(a,c){return j(a,b,c)})}function l(a,b,e){var f=a.accessibilitySummaryText,g=a.mimetype,j=a.msgId,k=a.previewHeight,l=a.previewUrl,n=a.previewUrlExpirationTimestampMs,o=a.previewWidth,p=a.ravenSettings,q=a.receiverFetchId,r=a.sortOrderMs,s=a.transportKey;a=a.type;a=m(a);k=d("MAWImageUtils").boundHeightWidth(k,o,d("MAWImageUtils").STICKER_THUMBNAIL_MAX_SIZE);o=k.height;k=k.width;var t=l!=null?l:e==null?void 0:e.previewUrl,u=n!=null?(h||(h=d("I64"))).of_float(n):e==null?void 0:e.previewUrlExpirationTimestampMs;t=d("LSMediaUrlUtils").hasValidReceiverFetchPreviewUrl(t,u);return{accessibilitySummaryText:f,attachmentFbid:q,attachmentIndex:(h||(h=d("I64"))).zero,attachmentType:a,authorityLevel:(i||(i=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").AUTHORITATIVE),ephemeralMediaState:(p==null?void 0:p.ephemeralMediaState)!=null?(h||(h=d("I64"))).of_int32(p.ephemeralMediaState):void 0,ephemeralMediaViewMode:(p==null?void 0:p.ephemeralMediaViewMode)!=null?(h||(h=d("I64"))).of_int32(p.ephemeralMediaViewMode):void 0,hasMedia:t,hasXma:!1,isSharable:!1,messageId:j,previewHeight:h.of_int32(o),previewUrl:l!=null?l:e==null?void 0:e.previewUrl,previewUrlExpirationTimestampMs:n!=null?(h||(h=d("I64"))).of_float(n):e==null?void 0:e.previewUrlExpirationTimestampMs,previewUrlMimeType:g,previewWidth:h.of_int32(k),receiverFetchId:q,threadKey:b,timestampMs:h.of_float(r),transportKey:s!=null?s:"WhatsApp"}}function m(a){switch(a){case"sticker":return(i||(i=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").STICKER);default:throw c("FBLogger")("messenger_web_media").mustfixThrow("Unsupported type for MAWBridgeNewReceiverFetchInfoHandler")}}g.call=a;g.composeAttachment=l}),98);
__d("MAWBridgeHandleReceiverFetchXMA",["FBLogger","I64","LSIntEnum","LSXmaContentType","MAWBridgeXMAUtils","MessagingAttachmentType","ReQL","asyncToGeneratorRuntime","cr:3468"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,b,c){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f){var g,j=e.contentRef,k=e.defaultPreviewMediaId,l=e.msgId,m=e.subtitleText,n=e.titleText,o=e.xmaId;if(l==null)return;g=(g=e==null?void 0:(g=e.defaultCTA)==null?void 0:g.actionUrl)!=null?g:e==null?void 0:(g=e.defaultCTA)==null?void 0:g.nativeUrl;var p=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.attachments).getKeyRange(f,l)));if(p!=null)return;p=String(o);o=b("cr:3468")!=null?(h||(h=d("I64"))).of_int32(b("cr:3468").getInChatMsgIdFromMsgId(l)):(h||(h=d("I64"))).zero;var q;if(j!=null)try{j=JSON.parse(j);j!=null&&(q=JSON.stringify(j))}catch(a){c("FBLogger")("messenger_web_sharing").mustfix("Failed to parse msg.contentRef as XmsgMsgrXmaContentRefDataclass")}else c("FBLogger")("messenger_web_sharing").mustfix("contentRef is empty for a MSG_RECEIVER_FETCH XMA");j=(yield d("MAWBridgeXMAUtils").addAttachmentCtas(a,e,f,l,p,{}));e=j.attachmentCta1Id;var r=j.attachmentCta2Id,s=j.attachmentCta3Id;j=j.defaultCtaId;yield a.attachments.add({actionUrl:g!=null?g:void 0,attachmentCta1Id:e,attachmentCta2Id:r,attachmentCta3Id:s,attachmentFbid:p,attachmentIndex:(h||(h=d("I64"))).zero,attachmentType:(i||(i=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").XMA),authorityLevel:h.zero,defaultCtaId:j,hasMedia:!1,hasXma:!0,hasXmaPreview:k!=null,isSharable:!0,messageId:l,receiverFetchId:q,subtitleText:m,threadKey:f,timestampMs:o,titleText:n,transportKey:"WhatsApp",xmaContentType:i.ofNumber(c("LSXmaContentType").MSG_RECEIVER_FETCH)});return});return j.apply(this,arguments)}g.call=a}),98);
__d("isReceiverFetchReceivingEnabled",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(a){if(a)return c("gkx")("6743");else return c("gkx")("6744")}g["default"]=a}),98);
__d("MAWBridgeNewXMAHandler",["MAWBridgeHandleReceiverFetchXMA","MAWBridgeXMAUtils","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","WAArmadilloXMA.pb","asyncToGeneratorRuntime","isReceiverFetchReceivingEnabled"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b,c){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){var f=String(b.xmaId),g=b.msgId;if(g==null)return;var h=b.targetType===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_RECEIVER_FETCH;if(h&&c("isReceiverFetchReceivingEnabled")(!0))return d("MAWBridgeHandleReceiverFetchXMA").call(a,b,e);h=(yield d("MAWBridgeXMAUtils").getExistingAttachmentAndAttachmentCtas(a,e,g));var i=h[0];h=h[1];h=(yield d("MAWBridgeXMAUtils").addAttachmentCtas(a,b,e,g,f,h));var j=h.attachmentCta1Id,k=h.attachmentCta2Id,l=h.attachmentCta3Id;h=h.defaultCtaId;b=d("MAWBridgeXMAUtils").composeAttachmentXMA(b,e,f,g,j,k,l,h,"MAWBridgeNewXMAHandler");i!=null?yield a.attachments.upsert([i.threadKey,i.messageId,i.attachmentFbid],b):yield a.attachments.add(b)});return i.apply(this,arguments)}function j(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.threadJid,"MAWBridgeNewXMAHandler",function(a,c){return h(a,b,c)})}function a(a,b){return j(a,b)}g.callWithoutWaitingForAnything=h;g.call=a}),98);
__d("MAWBridgeOccamadilloCreateE2EEMetadataThreadHandler",["FBLogger","I64","LSAuthorityLevel","LSCreateE2EEMetadataThreadStoredProcedure","LSFactory","LSIntEnum","MAWCreateOptimisticThread","MAWFolderUtils","MAWJids","MAWThreadMappingState","MAWThreadUpdateMiddlewareGatingUtil","MAWThreadUtils","Promise","WALogger","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k="[Occamadillo][CreateE2EEMetadataThreadHandler]";function a(a,b){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){d("WALogger").LOG([k+" Handler called from "+e.creationSource+" for "+("jid: "+e.jid)]);if(!d("MAWThreadUpdateMiddlewareGatingUtil").isNewMiThreadCreationFlowEnabled("exposure_logging_disabled")){d("WALogger").LOG([k+" Early return due to failed eligibility check"]);return(j||(j=b("Promise"))).resolve()}var f=e.bumpTimestampMs,g=e.creationSource,i=e.folderId,l=e.jid;e=e.threadKeys;var n=(yield m(a,e));if(!s(g,n,e)){d("WALogger").LOG([k+" Early return due to no SPROC call required: jid: "+l+", "+("threadKey: "+((n==null?void 0:n.threadKey)?(h||(h=d("I64"))).to_string(n==null?void 0:n.threadKey):"null"))]);return(j||(j=b("Promise"))).resolve()}g=t(g);i=d("MAWFolderUtils").systemFolderToLSCoreClientFolder(i);n=(yield q(a,n,e,l));e=d("MAWThreadUtils").getLSThreadTypeFromJid(l);u({bumpTimestampMs:f,createdByLocalDevice:g,folderType:i,jid:l,offlineThreadKeyDetails:n,threadType:e});return c("LSCreateE2EEMetadataThreadStoredProcedure")(c("LSFactory")(a),{bumpTimestampMs:f,createdByLocalDevice:g,folderType:i,offlineThreadKey:n.offlineThreadKey,threadType:e,waJid:d("MAWJids").convertChatJidToIntJid(l)})});return l.apply(this,arguments)}function m(a,b){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){a=(yield (j||(j=b("Promise"))).all([yield o(a,c.authoritative),yield o(a,c.optimistic)]));c=a[0];a=a[1];return c!=null?c:a});return n.apply(this,arguments)}function o(a,b){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){if(c==null)return(j||(j=b("Promise"))).resolve();a=(yield a.threads.get((h||(h=d("I64"))).of_string(c)));return a});return p.apply(this,arguments)}function q(a,b,c,d){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f,g){if(e!=null)return{offlineThreadKey:e.threadKey,source:"Pre-existing LSDB Thread"};e=null;var i=null;f.authoritative!=null?(e=(h||(h=d("I64"))).of_string(f.authoritative),i="MAW: authoritative"):f.optimistic!=null?(e=(h||(h=d("I64"))).of_string(f.optimistic),i="MAW: optimistic"):(e=(yield d("MAWCreateOptimisticThread").createOfflineThreadingId(a)),i="New Offline Threading ID");yield (j||(j=b("Promise"))).all([d("MAWCreateOptimisticThread").createOptimisticThreadWithThreadKey(a,e),d("MAWThreadMappingState").markActThreadMappingAsCompleted(a,{intJid:d("MAWJids").convertChatJidToIntJid(g),threadKey:e})]);c("FBLogger")("messenger_web").info(k.concat(" Created new optimistic thread in LSDB with threadKey: ",(h||(h=d("I64"))).to_string(e),", threadKey source: ",i,", for jid: ",g));return{offlineThreadKey:e,source:i}});return r.apply(this,arguments)}function s(a,b,e){var f=b!=null?(i||(i=d("LSIntEnum"))).unwrapIntEnum(b.authorityLevel):null;c("FBLogger")("messenger_web").info(k.concat(" Validating authoritativeThreadKey optimisation. authorityLevel: %",", has authoritativeThreadKey: %",", creationSource: %",", optimistic thread key: %",", authoritative thread key: %"),f!=null?f.toString():"null",e.authoritative!=null?"true":"false",a,e.optimistic,e.authoritative);switch(a){case"incoming_message":return b==null;case"outgoing_message":switch(f){case null:case void 0:case c("LSAuthorityLevel").CLIENT_PARTIAL:case c("LSAuthorityLevel").OPTIMISTIC:case c("LSAuthorityLevel").SERVER_PARTIAL:return!0;case c("LSAuthorityLevel").AUTHORITATIVE:case c("LSAuthorityLevel").AUTHORITATIVE_PENDING_REPLACEMENT:case c("LSAuthorityLevel").CLIENT_AUTHORITATIVE_DELETE:return!1;default:f;throw c("FBLogger")("messenger_web").mustfixThrow("Unknown authority level")}default:a;throw c("FBLogger")("messenger_web").mustfixThrow("Unknown creation source")}}function t(a){switch(a){case"incoming_message":return!1;case"outgoing_message":return!0;default:a;throw c("FBLogger")("messenger_web").mustfixThrow("Unknown creation source")}}function u(a){var b=a.bumpTimestampMs,e=a.createdByLocalDevice,f=a.folderType,g=a.jid,j=a.offlineThreadKeyDetails;a=a.threadType;c("FBLogger")("messenger_web").info(k.concat(" Calling LSCreateE2EEMetadataThreadStoredProcedure with params: ","bumpTimestampMs: ",(h||(h=d("I64"))).to_float(b).toString(),", createdByLocalDevice: ",e.toString(),", folderType: ",(i||(i=d("LSIntEnum"))).toNumber(f).toString(),", offlineThreadKey: ",h.to_string(j.offlineThreadKey),", offlineThreadKeySource: ",j.source,", threadType: ",i.toNumber(a).toString(),", waJid: ",g))}g.call=a}),98);
__d("MAWBridgeOccamadilloCreateE2EEMetadataThreadHandlerV2",["DedupMiThreadCreationCache","FBLogger","I64","LSAuthorityLevel","LSCreateE2EEMetadataThreadStoredProcedure","LSFactory","LSIntEnum","MAWCreateOptimisticThread","MAWFolderUtils","MAWJids","MAWThreadMappingState","MAWThreadUtils","Promise","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k="[Occamadillo][CreateE2EEMetadataThreadHandlerV2]";function a(a,b){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e=b.bumpTimestampMs,f=b.creationSource,g=b.folderId,h=b.jid;b=b.optimisticThreadKey;p(f,h);b=b!=null?yield a.threads.get((i||(i=d("I64"))).of_string(b)):null;if(b!=null&&d("DedupMiThreadCreationCache").DedupMiThreadCreationCache.has(h)){t(h);return}if(b!=null&&m(b)){q(h,(i||(i=d("I64"))).to_string(b.threadKey));return}e={bumpTimestampMs:e,createdByLocalDevice:f==="outgoing_message",folderType:d("MAWFolderUtils").systemFolderToLSCoreClientFolder(g),offlineThreadKey:yield n(b,h,a),threadType:d("MAWThreadUtils").getLSThreadTypeFromJid(h),waJid:d("MAWJids").convertChatJidToIntJid(h)};s(e);return c("LSCreateE2EEMetadataThreadStoredProcedure")(c("LSFactory")(a),e).then(function(){d("DedupMiThreadCreationCache").addToDedupMiThreadCreationCache(h)})});return l.apply(this,arguments)}function m(a){a=(j||(j=d("LSIntEnum"))).unwrapIntEnum(a.authorityLevel);switch(a){case c("LSAuthorityLevel").CLIENT_PARTIAL:case c("LSAuthorityLevel").OPTIMISTIC:case c("LSAuthorityLevel").SERVER_PARTIAL:return!1;case c("LSAuthorityLevel").AUTHORITATIVE:case c("LSAuthorityLevel").AUTHORITATIVE_PENDING_REPLACEMENT:case c("LSAuthorityLevel").CLIENT_AUTHORITATIVE_DELETE:return!0;default:a;throw c("FBLogger")("messenger_web").mustfixThrow("Unknown authority level")}}function n(a,b,c){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e){if(a!=null)return(h||(h=b("Promise"))).resolve(a.threadKey);a=(yield d("MAWCreateOptimisticThread").createOfflineThreadingId(e));r(c,a);yield (h||(h=b("Promise"))).all([d("MAWCreateOptimisticThread").createOptimisticThreadWithThreadKey(e,a),d("MAWThreadMappingState").markActThreadMappingAsCompleted(e,{intJid:d("MAWJids").convertChatJidToIntJid(c),threadKey:a})]);return a});return o.apply(this,arguments)}function p(a,b){c("FBLogger")("messenger_web").info(k+" handler called from %s for jid %s",a,b)}function q(a,b){c("FBLogger")("messenger_web").info(k+" handler returned early for jid %s because thread %s was already authoritative",a,b)}function r(a,b){c("FBLogger")("messenger_web").info(k+" no thread exists for jid %s, creating optimistic thread with key %s",a,(i||(i=d("I64"))).to_string(b))}function s(a){c("FBLogger")("messenger_web").info(k+" Calling LSCreateE2EEMetadataThreadStoredProcedure with params: bumpTimestampMs: %s, createdByLocalDevice: %s, folderType: %s, offlineThreadKey: %s, offlineThreadKeySource: %s, threadType: %s, waJid: %s",(i||(i=d("I64"))).to_string(a.bumpTimestampMs),a.createdByLocalDevice,i.to_string(a.folderType),i.to_string(a.offlineThreadKey),i.to_string(a.threadType),i.to_string(a.waJid))}function t(a){c("FBLogger")("messenger_web").info(k+" LSCreateE2EEMetadataThreadStoredProcedure has already been called for jid %s, early returning",a)}g.call=a}),98);
__d("MAWBridgeParticipantRemovedHandler",["I64","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.threadJid,"MAWBridgeParticipantRemovedHandler",function(a,c){return a.participants["delete"](c,(h||(h=d("I64"))).of_string(b.userId))})}g.call=a}),98);
__d("MAWBridgeParticipantUpdatedHandler",["fbt","I64","LSAuthorityLevel","LSContactBlockedByViewerStatus","LSContactGender","LSContactIdType","LSContactType","LSContactViewerRelationship","LSContactWorkForeignEntityType","LSFactory","LSGroupParticipantJoinState","LSIntEnum","LSVerifyContactRowExistsStoredProcedure","MAWBridgeParticipantsUpdatedHandler","MAWCurrentUser","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWTimeUtils","MAWUpdateLSThreadCapabilities","Promise","asyncToGeneratorRuntime","gkx","vulture"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k,l=d("MAWCurrentUser").getID(),m=(j||(j=d("I64"))).of_string(l);function n(a,b,c){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){var f=(j||(j=d("I64"))).of_string(b.fbid),g=d("MAWTimeUtils").toTimestamp(b.readWatermarkTs),i=d("MAWTimeUtils").toTimestamp(b.deliveredWatermarkTs),n=d("MAWTimeUtils").toTimestamp(b.lastReadActionTs),o=b.isSuperAdmin?h._(/*BTDS*/"Group creator").toString():void 0,p=(yield a.participants.get(e,f));p!=null?yield a.participants.put({authorityLevel:p.authorityLevel,contactId:f,deliveredWatermarkTimestampMs:i,groupParticipantJoinState:b.isInvited?(k||(k=d("LSIntEnum"))).ofNumber(c("LSGroupParticipantJoinState").INVITED):(k||(k=d("LSIntEnum"))).ofNumber(c("LSGroupParticipantJoinState").MEMBER),isAdmin:b.isAdmin,isModerator:!1,isSuperAdmin:p.isSuperAdmin,nickname:p.nickname,normalizedSearchTerms:p.normalizedSearchTerms,participantCapabilities:p.participantCapabilities,readActionTimestampMs:n,readWatermarkTimestampMs:g,subscribeSource:o,threadKey:e}):yield a.participants.add({authorityLevel:(j||(j=d("I64"))).zero,contactId:f,deliveredWatermarkTimestampMs:i,groupParticipantJoinState:b.isInvited?(k||(k=d("LSIntEnum"))).ofNumber(c("LSGroupParticipantJoinState").INVITED):(k||(k=d("LSIntEnum"))).ofNumber(c("LSGroupParticipantJoinState").MEMBER),isAdmin:b.isAdmin,isModerator:!1,isSuperAdmin:b.isSuperAdmin,nickname:void 0,normalizedSearchTerms:void 0,participantCapabilities:void 0,readActionTimestampMs:j.zero,readWatermarkTimestampMs:g,subscribeSource:o,threadKey:e});yield d("MAWUpdateLSThreadCapabilities").disableBlockerCapabilitiesTxn(a,e);n=b.isAdmin;(j||(j=d("I64"))).equal(m,f)&&(n?yield d("MAWUpdateLSThreadCapabilities").enableAddMembersTxn(a,e):yield d("MAWUpdateLSThreadCapabilities").disableAddMembersTxn(a,e));p==null&&(yield c("LSVerifyContactRowExistsStoredProcedure")(c("LSFactory")(a),{authorityLevel:(k||(k=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").OPTIMISTIC),blockedByViewerStatus:k.ofNumber(c("LSContactBlockedByViewerStatus").UNBLOCKED),contactIdType:k.ofNumber(c("LSContactIdType").FBID),contactType:k.ofNumber(c("LSContactType").USER),contactViewerRelationship:k.ofNumber(c("LSContactViewerRelationship").UNKNOWN),gender:k.ofNumber(c("LSContactGender").UNKNOWN),id:f,isBlocked:!1,isMemorialized:!1,isSelf:l===b.fbid,workForeignEntityType:k.ofNumber(c("LSContactWorkForeignEntityType").UNKNOWN)}))});return o.apply(this,arguments)}function a(a,e){if(c("gkx")("13628"))return d("MAWBridgeParticipantsUpdatedHandler").call(a,{participants:[e]});if(e.fbid.startsWith("shadow_")){c("vulture")("Hxg8SseiWP5IZUyhTKsM26kY__w=");return(i||(i=b("Promise"))).resolve()}return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.threadJid,"MAWBridgeParticipantUpdatedHandler",function(a,b){return n(a,e,b)})}g.callWithoutWaitingForAnything=n;g.call=a}),226);
__d("MAWBridgeRavenActionMsgForUIHandler",["I64","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWMsg","MAWRavenUtils","Promise","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,c){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,c.threadJid,"MAWBridgeRavenActionMsgForUIHandler",function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){a=a.attachments;e=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a).getKeyRange(e,c.ravenMsgId)));if(e==null)return(i||(i=b("Promise"))).resolve();if(e.ephemeralMediaState==null)return(i||(i=b("Promise"))).resolve();var f=(h||(h=d("I64"))).to_int32(e.ephemeralMediaState);if(e.ephemeralMediaViewMode==null)return(i||(i=b("Promise"))).resolve();var g=h.to_int32(e.ephemeralMediaViewMode);f=d("MAWMsg").MAWRavenMsgEphemeralMediaState.cast(f);g=d("MAWMsg").MAWRavenMsgEphemeralType.cast(g);if(f==null||g==null)return(i||(i=b("Promise"))).resolve();f=d("MAWRavenUtils").getNextRavenMessageEphemeralState(f,g);if(f==null)return(i||(i=b("Promise"))).resolve();e.ephemeralMediaState;g=babelHelpers.objectWithoutPropertiesLoose(e,["ephemeralMediaState"]);e=babelHelpers["extends"]({ephemeralMediaState:h.of_int32(f)},g,{transportKey:"WhatsApp"});f=[g.threadKey,g.messageId,g.attachmentFbid];return a.upsert(f,e)});return function(b,c){return a.apply(this,arguments)}}())}g.call=a}),98);
__d("MAWBridgeReactionUpsertHandler",["I64","LSAuthorityLevel","LSIntEnum","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWTimeUtils"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a,b,e,f,g){return{actorId:(h||(h=d("I64"))).of_string(a),authorityLevel:(i||(i=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").AUTHORITATIVE),messageId:e,reaction:g,reactionCreationTimestampMs:d("MAWTimeUtils").toTimestamp(f),threadKey:b,timestampMs:d("MAWTimeUtils").toTimestamp(f),transportKey:"WhatsApp"}}function k(a,b,c,e,f,g){return a.reactions.upsert([c,e,(h||(h=d("I64"))).of_string(b)],j(b,c,e,f,g))}function a(a,b,c,d,e,f){return k(a,b,c,d,e,f)}function b(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.chatJid,"MAWBridgeReactionUpsertHandler",function(a,c){return k(a,b.actorId,c,b.messageId,b.ts,b.reaction)})}g.composeReaction=j;g.attachmentMutationsOptimisticHandler=a;g.call=b}),98);
__d("MAWBridgeReceivedReceiptHandler",["I64","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWTimeUtils","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a,b,c){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){var e=(h||(h=d("I64"))).of_string(b.fbid),f=d("MAWTimeUtils").toTimestamp(b.lastReadWatermarkTs),g=d("MAWTimeUtils").toTimestamp(b.deliveredWatermarkTs);b=d("MAWTimeUtils").toTimestamp(b.lastReadActionTs);c=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.participants).getKeyRange(c,e)));c!=null&&(yield a.participants.put({authorityLevel:c.authorityLevel,contactId:c.contactId,deliveredWatermarkTimestampMs:(h||(h=d("I64"))).equal(g,(h||(h=d("I64"))).zero)?c.deliveredWatermarkTimestampMs:g,groupParticipantJoinState:c.groupParticipantJoinState,isAdmin:c.isAdmin,isModerator:c.isModerator,isSuperAdmin:c.isSuperAdmin,nickname:c.nickname,normalizedSearchTerms:c.normalizedSearchTerms,participantCapabilities:c.participantCapabilities,readActionTimestampMs:(h||(h=d("I64"))).equal(b,(h||(h=d("I64"))).zero)?c.readActionTimestampMs:b,readWatermarkTimestampMs:(h||(h=d("I64"))).equal(f,(h||(h=d("I64"))).zero)?c.readWatermarkTimestampMs:f,subscribeSource:c.subscribeSource,threadKey:c.threadKey,threadRoles:(h||(h=d("I64"))).zero}))});return j.apply(this,arguments)}function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.chatJid,"MAWBridgeReceivedReceiptHandler",function(a,c){return i(a,b,c)})}g.callWithoutWaitingForAnything=i;g.call=a}),98);
__d("MAWBridgeRefreshContactHandler",["LSFactory","LSRefetchContactRowIfExistsStoredProcedure"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){return c("LSRefetchContactRowIfExistsStoredProcedure")(c("LSFactory")(a),{id:b})}g.call=a}),98);
__d("MAWBridgeRemoveMessageSearchResultHandler",["Promise","ReQL","asyncToGeneratorRuntime","emptyFunction"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,e){return b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var c=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.message_search_results).filter(function(a){return e.includes(a.messageOtid)})));yield (h||(h=b("Promise"))).all(c.map(function(b){return a.message_search_results["delete"](b.type_,b.query,b.threadKey,b.globalIndex)}))})().then(c("emptyFunction"))}g.call=a}),98);
__d("MAWBridgeSyncContacts",["CurrentMessengerUser","I64","LSAuthorityLevel","LSContactBlockedByViewerStatus","LSContactGender","LSContactIdType","LSContactType","LSContactViewerRelationship","LSContactWorkForeignEntityType","LSFactory","LSIntEnum","LSVerifyContactRowExistsStoredProcedure","Promise","emptyFunction"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=d("CurrentMessengerUser").getID();function a(a,e){return e.reduce(function(b,e){var f=(i||(i=d("I64"))).of_string(e);return b.then(function(){return c("LSVerifyContactRowExistsStoredProcedure")(c("LSFactory")(a),{authorityLevel:(j||(j=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").OPTIMISTIC),blockedByViewerStatus:j.ofNumber(c("LSContactBlockedByViewerStatus").UNBLOCKED),contactIdType:j.ofNumber(c("LSContactIdType").FBID),contactType:j.ofNumber(c("LSContactType").USER),contactViewerRelationship:j.ofNumber(c("LSContactViewerRelationship").UNKNOWN),gender:j.ofNumber(c("LSContactGender").UNKNOWN),id:f,isBlocked:!1,isMemorialized:!1,isSelf:k===e,workForeignEntityType:j.ofNumber(c("LSContactWorkForeignEntityType").UNKNOWN)})}).then(c("emptyFunction"))},(h||(h=b("Promise"))).resolve())}g.call=a}),98);
__d("LSDeleteThread",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[],d=[];return b.sequence([function(d){return b.sequence([function(d){return b.db.table(9).fetch([[[a[0]]]]).next().then(function(a,d){var e=a.done;a=a.value;return e?(e=[b.i64.cast([0,0]),void 0],c[0]=e[0],c[1]=e[1],e):(d=a.item,e=[d.parentThreadKey,d.disappearingThreadKey],c[0]=e[0],c[1]=e[1],e)})},function(a){return b.count(b.db.table(9).fetch([[[c[0]]],"parentThreadKeyLastActivityTimestampMs"])).then(function(a){return c[3]=a})},function(d){return a[1]&&b.i64.neq(c[0],b.i64.cast([0,0]))&&b.i64.eq(c[3],b.i64.cast([0,1]))?b.sequence([function(d){return b.forEach(b.db.table(9).fetch([[[a[0]],[c[0]]]]),function(a){return a["delete"]()})},function(a){return b.i64.neq(c[1],void 0)?0:0}]):b.sequence([function(c){return b.forEach(b.db.table(9).fetch([[[a[0]]]]),function(a){return a["delete"]()})},function(a){return b.i64.neq(c[1],void 0)?0:0}])}])},function(a){return b.resolve(d)}])}a.__sproc_name__="LSMailboxDeleteThreadStoredProcedure";a.__tables__=["threads"];e.exports=a}),null);
__d("LSMoveThreadToArchivedFolder",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[];return b.sequence([function(c){return b.db.table(9).fetch([[[a[0]]]]).next().then(function(c,d){d=c.done;c=c.value;return d?0:(c.item,b.forEach(b.db.table(9).fetch([[[a[0]]]]),function(a){var c=a.update;a=a.item;return c({folderName:"",parentThreadKey:b.i64.cast([-1,4294967286]),capabilities2:b.i64.or_(a.capabilities2,b.i64.cast([0,2]))})}))})},function(a){return b.resolve(c)}])}a.__sproc_name__="LSMailboxMoveThreadToArchivedFolderStoredProcedure";a.__tables__=["threads"];e.exports=a}),null);
__d("LSOptimisticRemoveThread",["LSDeleteThread","LSIssueNewTaskAndGetTaskID","LSMoveThreadToArchivedFolder"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.sequence([function(b){return c.db.table(9).fetch([[[a[0]]]]).next().then(function(a,b){var e=a.done;a=a.value;return e?d[0]=c.i64.cast([0,1]):(b=a.item,d[4]=b.syncGroup,c.i64.neq(d[4],void 0)?d[3]=d[4]:d[3]=c.i64.cast([0,1]),d[0]=d[3])})},function(d){return[c.i64.cast([0,1]),c.i64.cast([0,2])].some(function(b){return c.i64.eq(a[1],b)})?c.storedProcedure(b("LSMoveThreadToArchivedFolder"),a[0]):c.storedProcedure(b("LSDeleteThread"),a[0],!1)},function(e){return c.storedProcedure(b("LSIssueNewTaskAndGetTaskID"),c.i64.to_string(a[0]),c.i64.cast([0,146]),"",void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),d[0],void 0,c.i64.cast([0,0]),c.i64.cast([0,0])).then(function(a){return a=a,d[2]=a[0],a})},function(b){return c.db.table(32).add({taskId:d[2],threadKey:a[0],removeType:a[1],syncGroup:d[0]})}])},function(a){return c.resolve(e)}])}a.__sproc_name__="LSMailboxOptimisticRemoveThreadStoredProcedure";a.__tables__=["threads","threads_optimistic_context"];e.exports=a}),null);
__d("LSOptimisticRemoveThreadStoredProcedure",["LSOptimisticRemoveThread","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSOptimisticRemoveThread"),e.threadKey,e.removeType);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("LSThreadRemoveType",[],(function(a,b,c,d,e,f){a=Object.freeze({DELETE_THREAD:0,ARCHIVE_THREAD:1,IGNORE_THREAD:2});f["default"]=a}),66);
__d("MAWBridgeThreadHiddenV2Handler",["LSFactory","LSIntEnum","LSOptimisticRemoveThreadStoredProcedure","LSThreadRemoveType","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b,"MAWBridgeThreadHiddenV2Handler",function(a,b){a=c("LSFactory")(a);return c("LSOptimisticRemoveThreadStoredProcedure")(a,{removeType:(h||(h=d("LSIntEnum"))).ofNumber(c("LSThreadRemoveType").DELETE_THREAD),threadKey:b})})}g.call=a}),98);
__d("MAWBridgeThreadTimestampsHandler",["CurrentMessengerUser","I64","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWTimeUtils","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a,b,c){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){var e=d("MAWTimeUtils").millisTimeToTimestamp(c.lastReadWatermarkTs),f=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(b)));if(f==null||c.markUnread!==null&&c.markUnread===!1&&(h||(h=d("I64"))).ge(f.lastReadWatermarkTimestampMs,e))return;e=babelHelpers["extends"]({},f,{lastReadWatermarkTimestampMs:d("MAWTimeUtils").millisTimeToTimestamp(c.lastReadWatermarkTs)});yield a.threads.put(e);f=(h||(h=d("I64"))).of_string(d("CurrentMessengerUser").getID());e=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.participants).getKeyRange(b,f)));e!=null&&(yield a.participants.put(babelHelpers["extends"]({},e,{readActionTimestampMs:d("MAWTimeUtils").millisTimeToTimestamp(c.lastActivityTimestampMs),readWatermarkTimestampMs:d("MAWTimeUtils").millisTimeToTimestamp(c.lastReadWatermarkTs)})))});return j.apply(this,arguments)}function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.threadJid,"MAWBridgeThreadTimestampsHandler",function(a,c){return i(a,c,b)})}g.callWithoutWaitingForAnything=i;g.call=a}),98);
__d("MAWBridgeThreadUpdatedHandler",["MAWBridgeBuildThreadSnippet","MAWBridgeUIEventDataValidation","MAWFolderUtils","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","ReQL","asyncToGeneratorRuntime","getMAWLastMessageCtaType"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b){var c=b.folder!=null?d("MAWFolderUtils").getMessagingFolderTag(b.folder):a.folderName;return(b=b.cannotReplyReason)!=null?b:c==="inbox"?void 0:a.cannotReplyReason}function i(a,b,c){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){e=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(e)));if(e==null)return;var f=d("MAWBridgeBuildThreadSnippet").buildBridgeThreadSnippet(b,e.snippet),g=h(e,b),i=c("getMAWLastMessageCtaType")(b.snippetType);g=babelHelpers["extends"]({},e,{cannotReplyReason:g,folderName:e.folderName,lastMessageCtaType:i,parentThreadKey:e.parentThreadKey,snippet:f,snippetSenderContactId:b.snippetSenderContactId==null?e.snippetSenderContactId:d("MAWBridgeUIEventDataValidation").stringToI64Opt(b.snippetSenderContactId)});yield a.threads.put(g)});return j.apply(this,arguments)}function a(a,b){var c=b.threadJid;return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,c,"MAWBridgeThreadUpdatedHandler",function(a,c){return i(a,b,c)})}g.callWithoutWaitingForAnything=i;g.call=a}),98);
__d("MAWBridgeUIEventQueueQPLLogger",["QPLUserFlow","qpl"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){c("QPLUserFlow").start(c("qpl")._(1056849266,"1765"),{annotations:{string_array:{events:b}},instanceKey:a})}function b(a,b){c("QPLUserFlow").addAnnotations(c("qpl")._(1056849266,"1765"),b,{instanceKey:a})}function d(a){c("QPLUserFlow").endSuccess(c("qpl")._(1056849266,"1765"),{instanceKey:a})}function e(a,b){c("QPLUserFlow").endFailure(c("qpl")._(1056849266,"1765"),"MAWBridgeUIEventHandlerFailure",{annotations:{string:{failedEventTag:b}},instanceKey:a})}g.start=a;g.addAnnotations=b;g.endSuccess=d;g.endFailure=e}),98);
__d("MAWBridgeUnArchivedSelfDeviceChangeAlertsHandler",["Promise"],(function(a,b,c,d,e,f){"use strict";var g;function a(a,c){return(g||(g=b("Promise"))).resolve()}f.call=a}),66);
__d("MAWBridgeUnbumpOnThreadLoadHandler",["FBLogger","I64","MAWChatJid","MAWJids","MAWMiActGetThreadLifecycleState","MAWODSProxy","WAOdsEnums","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Set();function a(a,b){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e=b.jid,f=b.unbumpFromTs;b=b.unbumpToTs;var g=(yield d("MAWChatJid").toThreadMaybe(a,e));if(g==null){var j=(yield d("MAWMiActGetThreadLifecycleState").getThreadLifecycleStateByJid(a,d("MAWJids").convertChatJidToIntJid(e),"MAWBridgeCentralizedThreadUpdateHandler"));c("FBLogger")("UnbumpOnRefresh").mustfix("[UnbumpOnRefresh] missing thread for unbumping jid: %s with thread state: %s",e,j.type);return}if(i.has(e)){d("MAWODSProxy").odsBumpEntityKey({entity:d("WAOdsEnums").Entity.MAW_THREAD_UPDATE_MIDDLEWARE,key:"unbump_already_processed"});return}i.add(e);if(!(h||(h=d("I64"))).equal(g.lastActivityTimestampMs,f))return;d("MAWODSProxy").odsBumpEntityKey({entity:d("WAOdsEnums").Entity.MAW_THREAD_UPDATE_MIDDLEWARE,key:"unbump_on_thread_load"});j=babelHelpers["extends"]({},g,{lastActivityTimestampMs:b});yield a.threads.put(j)});return j.apply(this,arguments)}g.call=a}),98);
__d("MAWBridgeUpdateClientRestoreStatus",["I64","LSEncryptedBackupsClientRestoreState","LSIntEnum","MAWUpdateClientRestoreStatusOperationType","Promise","ReQL","asyncToGeneratorRuntime","emptyFunction"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(a,e){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.encrypted_backups_client_restore_status).filter(function(a){return a.threadId===e})).then(function(d){if(d!=null)return a.encrypted_backups_client_restore_status["delete"](d.threadId).then(c("emptyFunction"));else return(j||(j=b("Promise"))).resolve()})}function l(a,e,f,g){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.encrypted_backups_client_restore_status).getKeyRange(e)).then(function(g){if(g!=null)return a.encrypted_backups_client_restore_status.put({lastUpdated:(h||(h=d("I64"))).of_float(Date.now()),restoreStatus:(i||(i=d("LSIntEnum"))).ofNumber(c("LSEncryptedBackupsClientRestoreState").RESTORE_COMPLETE),serverThreadKey:f,threadId:e});else return(j||(j=b("Promise"))).resolve()})}function a(a){return d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.encrypted_backups_client_restore_status)).then(function(c){return c.reduce(function(b,c){return b.then(function(){return k(a,c.threadId)})},(j||(j=b("Promise"))).resolve())})}function e(a,b){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){var f=e.threadId,g=e.action;g===d("MAWUpdateClientRestoreStatusOperationType").upsertClientRestoreStatus?yield a.encrypted_backups_client_restore_status.put({lastUpdated:(h||(h=d("I64"))).of_float(Date.now()),restoreStatus:(i||(i=d("LSIntEnum"))).ofNumber(c("LSEncryptedBackupsClientRestoreState").RESTORE_IN_PROGRESS),serverThreadKey:void 0,threadId:f}):yield l(a,e.threadId,void 0,e.ebPrefetch);return(j||(j=b("Promise"))).resolve()});return m.apply(this,arguments)}g.truncateClientRestoreStatusTable=a;g.call=e}),98);
__d("LSAddMessengerContact",["LSIssueNewTask"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return d[0]=new c.Map(),d[0].set("contact_id",a[0]),d[1]=c.toJSON(d[0]),c.storedProcedure(b("LSIssueNewTask"),"add_messenger_contact",c.i64.cast([0,308]),d[1],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))},function(a){return c.resolve(e)}])}a.__sproc_name__="LSContactAddMessengerContactStoredProcedure";a.__tables__=[];e.exports=a}),null);
__d("LSAddMessengerContactStoredProcedure",["LSAddMessengerContact","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSAddMessengerContact"),e.contactId);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("LSOptimisticAcceptMessageRequest",["LSIssueNewTaskWithExtraOperations"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.db.table(9).fetch([[[a[0]]]]).next().then(function(e,f){var g=e.done;e=e.value;return g?0:(f=e.item,c.sequence([function(b){return c.forEach(c.db.table(9).fetch([[[a[0]]]]),function(b){var d=b.update;b.item;return d({folderName:"inbox",parentThreadKey:c.i64.cast([0,0]),igFolder:a[1],capabilities:c.i64.and_(f.capabilities,c.i64.cast([-1,4292870143]))})})},function(b){return c.db.table(173).fetch([[[a[0]]]]).next().then(function(b,e){var a=b.done;b=b.value;return a?0:(e=b.item,d[4]=e.clientThreadPk,c.db.table(276).fetch([[[d[4]]]]).next().then(function(a,b){b=a.done;a=a.value;return b?0:(a.item,c.forEach(c.db.table(276).fetch([[[d[4]]]]),function(a){var b=a.update;a.item;return b({folderType:c.i64.cast([0,0])})}))}))})},function(b){return c.db.table(9).fetch([[[a[0]]]]).next().then(function(b,e){var a=b.done;b=b.value;return a?d[0]=c.i64.cast([0,1]):(e=b.item,d[5]=e.syncGroup,c.i64.neq(d[5],void 0)?d[4]=d[5]:d[4]=c.i64.cast([0,1]),d[0]=d[4])})},function(e){return d[2]=new c.Map(),d[2].set("thread_key",a[0]),d[2].set("sync_group",d[0]),d[2].set("ig_folder",a[1]),d[3]=c.toJSON(d[2]),c.storedProcedure(b("LSIssueNewTaskWithExtraOperations"),"message_request",c.i64.cast([0,66]),d[3],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),void 0,c.i64.cast([0,0]))}]))})},function(a){return c.resolve(e)}])}a.__sproc_name__="LSMailboxOptimisticAcceptMessageRequestStoredProcedure";a.__tables__=["threads","mi_act_mapping_table","client_threads"];e.exports=a}),null);
__d("LSOptimisticAcceptMessageRequestStoredProcedure",["LSOptimisticAcceptMessageRequest","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSOptimisticAcceptMessageRequest"),e.threadKey,e.igFolder);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("MAWBridgeUpdateContactAsConnectedHandler",["FBLogger","I64","LSAddMessengerContactStoredProcedure","LSContactViewerRelationship","LSFactory","LSIntEnum","LSOptimisticAcceptMessageRequestStoredProcedure","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWUpdateLSThreadCapabilities","Promise","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function a(a,b){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){var f=e.contactUserId;e=e.threadJid;var g=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.contacts).getKeyRange((i||(i=d("I64"))).of_string(f))));if(g==null){c("FBLogger")("messenger_web").warn("Missing contact in MAWBridgeUpdateContactAsConnectedHandler");return}var k=babelHelpers["extends"]({},g,{contactViewerRelationship:(j||(j=d("LSIntEnum"))).ofNumber(c("LSContactViewerRelationship").CONTACT_OF_VIEWER)});return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e,"MAWBridgeUpdateContactAsConnectedHandler",function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){yield (h||(h=b("Promise"))).allSettled([a.contacts.upsert([g.id],k),c("LSAddMessengerContactStoredProcedure")(c("LSFactory")(a),{contactId:g.id}),d("MAWUpdateLSThreadCapabilities").updateNonMessageRequestThreadTxn(a,e),c("LSOptimisticAcceptMessageRequestStoredProcedure")(c("LSFactory")(a),{threadKey:e})])});return function(b,c){return a.apply(this,arguments)}}())});return k.apply(this,arguments)}g.call=a}),98);
__d("LSUpdateE2EEMetadataParticipants",["LSIssueNewTask"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return d[0]=new c.Map(),d[0].set("thread_key",a[0]),d[0].set("user_self_remove",!1),d[1]=d[0].get("thread_key"),d[2]=c.toJSON(d[0]),c.storedProcedure(b("LSIssueNewTask"),c.i64.to_string(d[1]),c.i64.cast([0,394]),d[2],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))},function(a){return c.resolve(e)}])}a.__sproc_name__="LSE2EEMessagingMetadataMailboxUpdateE2EEMetadataParticipantsStoredProcedure";a.__tables__=[];e.exports=a}),null);
__d("LSUpdateE2EEMetadataParticipantsStoredProcedure",["LSSynchronousPromise","LSUpdateE2EEMetadataParticipants","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSUpdateE2EEMetadataParticipants"),e.threadKey);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("MAWBridgeUpdateE2EEMetadataParticipantsHandler",["LSFactory","LSUpdateE2EEMetadataParticipantsStoredProcedure","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.threadJid,"MAWBridgeThreadsLoadedHandler",function(a,b){return c("LSUpdateE2EEMetadataParticipantsStoredProcedure")(c("LSFactory")(a),{threadKey:b})})}g.call=a}),98);
__d("MAWBridgeUpdateMinMessageHandler",["FBLogger","I64","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","ReQL","ReQLTable"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){var e=(h||(h=d("I64"))).of_float(b.minTimestampMs),f=b.minMessageId;return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.threadJid,"MAWBridgeUpdateMinMessageHandler",function(a,g){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.messages_ranges_v2__generated).getKeyRange(g)).then(function(h){if(h==null)return;var i=b.hasMoreBefore,j=i!=null?i:h.hasMoreBefore;return d("ReQLTable").update(a.messages_ranges_v2__generated,[g,h.minTimestampMs,h.minMessageId],function(a){if(a!=null)return{hasMoreAfter:a.hasMoreAfter,hasMoreBefore:j,isLoadingAfter:a.isLoadingAfter,isLoadingBefore:a.isLoadingBefore,maxMessageId:a.maxMessageId,maxTimestampMs:a.maxTimestampMs,minMessageId:f,minTimestampMs:e,threadKey:a.threadKey};throw c("FBLogger")("wmi_eb").mustfixThrow("Thread is missing in updating the min message in message range")})})})}g.call=a}),98);
__d("MAWBridgeUpdateThreadActivityHandler",["I64","LSBumpE2EEMetadataThreadStoredProcedure","LSFactory","MAWChatJid","MAWJids","MAWMiActGetThreadLifecycleState","MWFBLogger","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=d("MWFBLogger").MWLogger.tags(["bridgeUIEvent","Occam","ActivityUpdate"]);function a(a,b){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=(yield d("MAWChatJid").toThreadMaybe(a,b.chatJid));if(c==null){yield q(a,b.chatJid);return}switch(b.source){case"outgoing_msg":case"incoming_msg":return k(a,c,b);case"deleted_msg":return m(a,c,b);case"inserted_admin_msg":return p(c,b,"No bump for admin message");case"eb_restore":return i.warn("Attempted bump from EB restore");default:b.source;throw i.mustfixThrow("Unknown source")}});return j.apply(this,arguments)}function k(a,b,c){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,d){if(o(b,d)){p(b,d,"Stale incoming bump");return}yield c("LSBumpE2EEMetadataThreadStoredProcedure")(c("LSFactory")(a),{bumpedByLocalDeviceSend:d.source==="outgoing_msg",isUnbump:!1,serverAuthoritativeTimestampMs:d.bumpTimestampMs,threadKey:b.threadKey});p(b,d,"Thread bumped")});return l.apply(this,arguments)}function m(a,b,c){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){yield a.threads.put(babelHelpers["extends"]({},b,{lastActivityTimestampMs:(h||(h=d("I64"))).min(b.lastActivityTimestampMs,c.bumpTimestampMs),lastReadWatermarkTimestampMs:h.min(b.lastReadWatermarkTimestampMs,c.bumpTimestampMs)})),p(b,c,"Thread unbumped")});return n.apply(this,arguments)}function o(a,b){return b.source==="incoming_msg"&&(h||(h=d("I64"))).le(b.bumpTimestampMs,a.lastActivityTimestampMs)}function p(a,b,c){b=[(h||(h=d("I64"))).to_string(a.threadKey),b.source,h.to_string(b.bumpTimestampMs),h.to_string(a.lastActivityTimestampMs),h.to_string(a.lastReadWatermarkTimestampMs)];i.debug.apply(i,[c+". threadKey: %s, source: %s, bumpTimestampMs: %s, originalLastActivityTimestampMs: %s, originalLastReadWatermarkTimestampMs: %s"].concat(b))}function q(a,b){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){a=(yield d("MAWMiActGetThreadLifecycleState").getThreadLifecycleStateByJid(a,d("MAWJids").convertChatJidToIntJid(b),"MAWBridgeUpdateThreadActivityHandler"));i.mustfix("Missing LSDBThread when updating thread activity. jid: %s, thread state: %s",b,a.type)});return r.apply(this,arguments)}g.call=a}),98);
__d("MAWBridgeUpdateThreadListItemHandler",["I64","LSBumpE2EEMetadataThreadStoredProcedure","LSFactory","MAWBridgeBuildThreadSnippet","MAWChatJid","MAWJids","MAWMiActGetThreadLifecycleState","MWFBLogger","asyncToGeneratorRuntime","getMAWLastMessageCtaType"],(function(a,b,c,d,e,f,g){"use strict";var h,i=d("MWFBLogger").MWLogger.tags(["bridgeUIEvent","Occam","Middleware"]);function a(a,b){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=b.activityUpdate,d=b.chatJid;b=b.snippetUpdate;c!=null&&(yield k(a,d,c));b!=null&&(yield q(a,d,b))});return j.apply(this,arguments)}function k(a,b,c){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){var e=(yield d("MAWChatJid").toThreadMaybe(a,b));if(e==null){yield v(a,b);return}switch(c.source){case"outgoing_msg":case"incoming_msg":return m(a,e,c);case"deleted_msg":return o(a,e,c);case"inserted_admin_msg":return t(e,c,"No bump for admin message");case"eb_restore":return i.warn("Attempted bump from EB restore");default:c.source;throw i.mustfixThrow("Unknown source")}});return l.apply(this,arguments)}function m(a,b,c){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,d){if(s(b,d)){t(b,d,"Stale incoming bump");return}yield c("LSBumpE2EEMetadataThreadStoredProcedure")(c("LSFactory")(a),{bumpedByLocalDeviceSend:d.source==="outgoing_msg",isUnbump:!1,serverAuthoritativeTimestampMs:d.bumpTimestampMs,threadKey:b.threadKey});t(b,d,"Thread bumped")});return n.apply(this,arguments)}function o(a,b,c){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){yield a.threads.put(babelHelpers["extends"]({},b,{lastActivityTimestampMs:(h||(h=d("I64"))).min(b.lastActivityTimestampMs,c.bumpTimestampMs),lastReadWatermarkTimestampMs:h.min(b.lastReadWatermarkTimestampMs,c.bumpTimestampMs)})),t(b,c,"Thread unbumped")});return p.apply(this,arguments)}function q(a,b,c){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){var f=(yield d("MAWChatJid").toThreadMaybe(a,b));if(f==null){yield v(a,b);return}yield a.threads.put(babelHelpers["extends"]({},f,{lastMessageCtaType:c("getMAWLastMessageCtaType")(e.snippetType),snippet:d("MAWBridgeBuildThreadSnippet").buildBridgeThreadSnippet(e,f.snippet),snippetSenderContactId:e.snippetSenderContactId!=null?(h||(h=d("I64"))).of_string(e.snippetSenderContactId):(h||(h=d("I64"))).zero}));u(f.threadKey)});return r.apply(this,arguments)}function s(a,b){return b.source==="incoming_msg"&&(h||(h=d("I64"))).le(b.bumpTimestampMs,a.lastActivityTimestampMs)}function t(a,b,c){b=[(h||(h=d("I64"))).to_string(a.threadKey),b.source,h.to_string(b.bumpTimestampMs),h.to_string(a.lastActivityTimestampMs),h.to_string(a.lastReadWatermarkTimestampMs)];i.debug.apply(i,[c+". threadKey: %s, source: %s, bumpTimestampMs: %s, originalLastActivityTimestampMs: %s, originalLastReadWatermarkTimestampMs: %s"].concat(b))}function u(a){i.debug("Updated thread snippet. threadKey: %s",(h||(h=d("I64"))).to_string(a))}function v(a,b){return w.apply(this,arguments)}function w(){w=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){a=(yield d("MAWMiActGetThreadLifecycleState").getThreadLifecycleStateByJid(a,d("MAWJids").convertChatJidToIntJid(b),"MAWBridgeUpdateThreadListItemHandler"));i.mustfix("Missing LSDBThread when updating threadlist item. jid: %s, thread state: %s",b,a.type)});return w.apply(this,arguments)}g.call=a}),98);
__d("MAWBridgeXMAShareExpiredHandler",["MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","Promise"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a,c,d){var e=String(d.xmaId);return a.attachments.get(c,d.msgId,e).then(function(f){if(f==null)return(h||(h=b("Promise"))).resolve();f=[f.defaultCtaId,f.attachmentCta1Id,f.attachmentCta2Id,f.attachmentCta3Id].filter(Boolean);f=f.map(function(b){return a.attachment_ctas["delete"](b)});return(h||(h=b("Promise"))).all(f).then(function(){return a.attachments["delete"](c,d.msgId,e)})})}function a(a,b){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.threadJid,"MAWBridgeXMAShareExpiredHandler",function(a,c){return i(a,c,b)})}g.callWithoutWaitingForAnything=i;g.call=a}),98);
__d("MAWBridgeXMAShareTombstonedHandler",["FBLogger","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","Promise","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,e){var f=String(e.xmaId);return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.threadJid,"MAWBridgeXMAShareTombstonedHandler",function(a,g){return a.attachments.get(g,e.msgId,f).then(function(i){if(i==null)return(h||(h=b("Promise"))).resolve();i=[i.defaultCtaId,i.attachmentCta1Id,i.attachmentCta2Id,i.attachmentCta3Id].filter(Boolean);i=i.map(function(b){return a.attachment_ctas["delete"](b)});return(h||(h=b("Promise"))).all(i).then(function(){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.attachments).getKeyRange(g,e.msgId,f)).then(function(){var d=b("asyncToGeneratorRuntime").asyncToGenerator(function*(d){if(d!=null){d=babelHelpers["extends"]({},d,{attachmentCta1Id:void 0,attachmentCta2Id:void 0,attachmentCta3Id:void 0,cta1Title:void 0,cta1Type:void 0,cta2Title:void 0,cta2Type:void 0,cta3Title:void 0,cta3Type:void 0,defaultCtaId:void 0,defaultCtaTitle:void 0,maxSubtitleNumOfLines:void 0,maxTitleNumOfLines:void 0,previewHeight:void 0,previewHeightLarge:void 0,previewWidth:void 0,previewWidthLarge:void 0,subtitleText:void 0,titleText:void 0});yield a.attachments.put(d)}c("FBLogger")("messenger_web_sharing").blameToPreviousFrame().mustfix("Trying to tombstone a secure xma attachment but no existing attachment");return(h||(h=b("Promise"))).resolve()});return function(a){return d.apply(this,arguments)}}())})})})}g.call=a}),98);
__d("MAWBridgeUIEventHandler",["EBLogger","MAWAbPropsClient","MAWBridgeAddMessageSearchResultHandler","MAWBridgeCallRestoreNativeOpHandler","MAWBridgeCentralizedThreadUpdateHandler","MAWBridgeDeleteGroupInviteHandler","MAWBridgeDeleteMessageRangesV2Handler","MAWBridgeDeleteMessagesHandler","MAWBridgeDeleteReactionHandler","MAWBridgeDropAllSecureThreadsHandler","MAWBridgeEditMsgHistoryAddedHandler","MAWBridgeFulfillRestorePromisePayloadHandler","MAWBridgeGroupInfoUpdatedHandler","MAWBridgeGroupInviteLoadedHandler","MAWBridgeGroupInviteUpdateHandler","MAWBridgeHandleCOPDigest","MAWBridgeMediaExpiredHandler","MAWBridgeMsgUpdatedHandler","MAWBridgeMsgsLoadedHandler","MAWBridgeMsgsStartCountdownHandler","MAWBridgeNewMediaHandler","MAWBridgeNewMediaRangeHandler","MAWBridgeNewMsgHandler","MAWBridgeNewPollHandler","MAWBridgeNewReceiverFetchInfoHandler","MAWBridgeNewXMAHandler","MAWBridgeOccamadilloCreateE2EEMetadataThreadHandler","MAWBridgeOccamadilloCreateE2EEMetadataThreadHandlerV2","MAWBridgeOccamadilloVerifyThreadExistsHandler","MAWBridgeOneToOneMessageRequestLoadedHandler","MAWBridgeParticipantRemovedHandler","MAWBridgeParticipantUpdatedHandler","MAWBridgeParticipantsUpdatedHandler","MAWBridgeRavenActionMsgForUIHandler","MAWBridgeReactionUpsertHandler","MAWBridgeReceivedChatStateHandler","MAWBridgeReceivedReceiptHandler","MAWBridgeRefreshContactHandler","MAWBridgeRemoveMessageSearchResultHandler","MAWBridgeSyncContacts","MAWBridgeThreadHiddenV2Handler","MAWBridgeThreadTimestampsHandler","MAWBridgeThreadUpdatedHandler","MAWBridgeUIEventQueueQPLLogger","MAWBridgeUnArchivedSelfDeviceChangeAlertsHandler","MAWBridgeUnbumpOnThreadLoadHandler","MAWBridgeUpdateClientRestoreStatus","MAWBridgeUpdateContactAsConnectedHandler","MAWBridgeUpdateE2EEMetadataParticipantsHandler","MAWBridgeUpdateMinMessageHandler","MAWBridgeUpdateThreadActivityHandler","MAWBridgeUpdateThreadListItemHandler","MAWBridgeXMAShareExpiredHandler","MAWBridgeXMAShareTombstonedHandler","MAWEphemeralMessageExpirationStorage","MAWLoggerUtils","MWFBLogger","Promise","WAResultOrError","WATimeUtils","asyncToGeneratorRuntime","cr:15985","cr:15986","cr:15987","cr:16021","cr:16022","cr:16023","cr:16024","cr:16025","cr:16026","cr:16027","cr:16028","emptyFunction","getErrorSafe","isArmadillo","isInstamadilloDYI","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["Handle UI Events: ",""]);i=function(){return a};return a}function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["Attempted to call the old DYI V1 flow"]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["Attempted to call the old DYI V1 flow"]);k=function(){return a};return a}var l=d("MWFBLogger").MWLogger.tags(["MAWBridgeUIEvent"]);function m(a,c,e){switch(e.tag){case"NewMsg":return d("MAWBridgeNewMsgHandler").call(c,e.value);case"MsgsStartCountdown":return d("MAWBridgeMsgsStartCountdownHandler").call(c,e.value);case"MsgClearCountdown":return(h||(h=b("Promise"))).resolve(d("MAWEphemeralMessageExpirationStorage").clearMessageFromExpiration(e.value));case"DropAllSecureThreads":return d("MAWBridgeDropAllSecureThreadsHandler").call(c);case"VerifyThreadExists":var f=e.value;return d("MAWBridgeOccamadilloVerifyThreadExistsHandler").call(c,f);case"CreateE2EEMetadataThread":return d("MAWBridgeOccamadilloCreateE2EEMetadataThreadHandler").call(c,e.value);case"CreateE2EEMetadataThreadV2":return d("MAWBridgeOccamadilloCreateE2EEMetadataThreadHandlerV2").call(c,e.value);case"EditMsgHistoryAdded":return d("MAWBridgeEditMsgHistoryAddedHandler").call(a,c,e.value);case"MsgUpdated":return d("MAWBridgeMsgUpdatedHandler").call(c,e.value);case"NewMedia":return d("MAWBridgeNewMediaHandler").call(c,e.value);case"NewMediaRange":return d("MAWBridgeNewMediaRangeHandler").call(c,e.value);case"MediaExpired":return d("MAWBridgeMediaExpiredHandler").call(c,e.value);case"XMAShareExpired":return d("MAWBridgeXMAShareExpiredHandler").call(c,e.value);case"NewXMA":return d("MAWBridgeNewXMAHandler").call(c,e.value);case"XMAShareTombstoned":return d("MAWBridgeXMAShareTombstonedHandler").call(c,e.value);case"ParticipantUpdated":return d("MAWBridgeParticipantUpdatedHandler").call(c,e.value);case"ParticipantRemoved":return d("MAWBridgeParticipantRemovedHandler").call(c,e.value);case"UpdateE2EEMetadataParticipants":return d("MAWBridgeUpdateE2EEMetadataParticipantsHandler").call(c,e.value);case"ReceivedReceipt":return d("MAWBridgeReceivedReceiptHandler").call(c,e.value);case"ThreadUpdated":return d("MAWBridgeThreadUpdatedHandler").call(c,e.value);case"GroupInfoUpdated":return d("MAWBridgeGroupInfoUpdatedHandler").call(c,e.value);case"CentralizedThreadUpdate":return d("MAWBridgeCentralizedThreadUpdateHandler").call(c,e.value);case"UpdateThreadListItem":return d("MAWBridgeUpdateThreadListItemHandler").call(c,e.value);case"UpdateThreadActivity":return d("MAWBridgeUpdateThreadActivityHandler").call(c,e.value);case"UnbumpOnThreadLoad":return d("MAWBridgeUnbumpOnThreadLoadHandler").call(c,e.value);case"ThreadTimestampsUpdated":return d("MAWBridgeThreadTimestampsHandler").call(c,e.value);case"MsgsLoaded":return d("MAWBridgeMsgsLoadedHandler").call(c,e.value);case"StartTrace":return b("cr:16023").call(c,e.value);case"UpdateTrace":return b("cr:16026").call(c,e.value);case"ThreadHiddenV2":return d("MAWBridgeThreadHiddenV2Handler").call(c,e.value);case"SyncContacts":return d("MAWBridgeSyncContacts").call(c,e.value);case"ReceivedChatState":return d("MAWBridgeReceivedChatStateHandler").call(c,e.value);case"ClockSkewUpdated":return(h||(h=b("Promise"))).resolve(d("WATimeUtils").setClockSkew(e.value));case"ABPropsUpdated":l.warn("ABPropsUpdated has been deprecated. Please use the bridge with ABPropsUpdated");return(h||(h=b("Promise"))).resolve(d("MAWAbPropsClient").rewrite(e.value));case"COPDigestReceived":return d("MAWBridgeHandleCOPDigest").call(c,e.value);case"UpsertReaction":return d("MAWBridgeReactionUpsertHandler").call(c,e.value);case"DeleteReaction":return d("MAWBridgeDeleteReactionHandler").call(c,e.value);case"UploadMessage":return b("cr:16028").call(c,e.value);case"UploadMessageByBatch":return b("cr:16027").call(c,e.value);case"DeleteMessagesOfThread":return b("cr:16021").call(c,e.value);case"UploadAttachment":return b("cr:15987").call(c,e.value);case"ResignAttachmentCDNUrl":return b("cr:15986").call(c,e.value);case"DeleteMessages":return d("MAWBridgeDeleteMessagesHandler").call(c,e.value);case"RestoreMessageBatch":d("EBLogger").EBLogger.tags(["labyrinth_dyi"]).WARN(k());return(h||(h=b("Promise"))).resolve();case"OneToOneMessageRequestLoaded":return d("MAWBridgeOneToOneMessageRequestLoadedHandler").call(c,e.value);case"GroupInviteUpdate":return d("MAWBridgeGroupInviteUpdateHandler").call(c,e.value);case"UpdateDyiStatus":d("EBLogger").EBLogger.tags(["labyrinth_dyi"]).WARN(j());return(h||(h=b("Promise"))).resolve();case"DeleteGroupInvite":return d("MAWBridgeDeleteGroupInviteHandler").call(c,e.value);case"TraceRecordCheckpoint":return b("cr:16025").call(c,e.value);case"DeleteMessageRangesV2":return d("MAWBridgeDeleteMessageRangesV2Handler").call(c,e.value);case"UpdateMinMessage":return d("MAWBridgeUpdateMinMessageHandler").call(c,e.value);case"IssuePointQuery":return b("cr:16022").call(c,e.value);case"ParticipantsUpdated":return d("MAWBridgeParticipantsUpdatedHandler").call(c,e.value);case"StartTraceWithTraceId":return b("cr:16024").call(c,e.value);case"UpdateClientRestoreStatus":return d("MAWBridgeUpdateClientRestoreStatus").call(c,e.value);case"NewMsgs":return d("MAWBridgeNewMsgHandler").bulkCall(c,e.value.msgs);case"UnArchivedSelfDeviceChangeAlerts":return d("MAWBridgeUnArchivedSelfDeviceChangeAlertsHandler").call(c,e.value);case"EphemeralSettingsUpdatedForUI":return b("cr:15985").call(c,e.value);case"RavenActionUpdate":return d("MAWBridgeRavenActionMsgForUIHandler").call(c,e.value);case"GroupInviteLoaded":return d("MAWBridgeGroupInviteLoadedHandler").call(c,e.value);case"RemoveMessageSearchResult":return d("MAWBridgeRemoveMessageSearchResultHandler").call(c,e.value);case"AddMessageSearchResult":return d("MAWBridgeAddMessageSearchResultHandler").call(a,c,e.value);case"FulfillRestorePromise":return d("MAWBridgeFulfillRestorePromisePayloadHandler").call(e.value);case"UpdateContactAsConnected":return d("MAWBridgeUpdateContactAsConnectedHandler").call(c,e.value);case"RefreshContact":return d("MAWBridgeRefreshContactHandler").call(c,e.value);case"NewReceiverFetchInfo":return d("MAWBridgeNewReceiverFetchInfoHandler").call(c,e.value);case"RestoreNativeOp":return d("MAWBridgeCallRestoreNativeOpHandler").call(c,e.value);case"NewPoll":return d("MAWBridgeNewPollHandler").call(c,e.value);default:return(h||(h=b("Promise"))).resolve()}}function a(a,b){if(!c("isArmadillo")()&&!c("isInstamadilloDYI")()){l.mustfix("User is neither part of armadillo QE and dual send QE but somehow loaded this handler");return}if(!(b.length===1&&b[0].tag==="TraceRecordCheckpoint")){var d={};d=b.reduce(function(a,b){a[b.tag]=((b=a[b.tag])!=null?b:0)+1;return a},d);l.DEBUG(i(),String(d))}c("promiseDone")(n(a,b),c("emptyFunction"),function(a){l.catching(a).mustfix("[MAWBridgeUIEventHandler] Failed to handle UI events: %s",b.map(function(a){return a.tag}).join(", "))})}function n(a,b){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){var g=(yield a);a=d("MAWLoggerUtils").getInstanceKey();var h=e.map(function(a){return a.tag}),i="";d("MAWBridgeUIEventQueueQPLLogger").start(a,h);d("MAWBridgeUIEventQueueQPLLogger").addAnnotations(a,{"int":{queue_size:e.length}});var j;try{j=(yield g.runInTransaction(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){for(var b=0;b<e.length;b++){var f=e[b];try{yield m(g,a,f)}catch(a){i=f.tag;return d("WAResultOrError").makeError(c("getErrorSafe")(a))}}return d("WAResultOrError").makeResult(!0)});return function(b){return a.apply(this,arguments)}}(),"readwrite","background",{events:e,type:"maw_ui_bridge"},f.id+":322"))}catch(a){j=d("WAResultOrError").makeError(c("getErrorSafe")(a))}!j.success?(d("MAWBridgeUIEventQueueQPLLogger").endFailure(a,i),l.catching(j.error).mustfix("MAWBridgeUIEventHandler] Did not commit UI events to DB, failed at %s, all events: %s",i,h.toString())):d("MAWBridgeUIEventQueueQPLLogger").endSuccess(a)});return o.apply(this,arguments)}function e(a,b,c){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,d){try{yield m(a,b,d)}catch(b){a=c("getErrorSafe")(b);l.catching(a).mustfix("[handleShimEvent] Failed to handle UI event: %s",d.tag);throw b}});return p.apply(this,arguments)}g.handleEvents=a;g.handleShimEvent=e}),98);
__d("toMsgrUserJid",["MAWJids"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=d("MAWJids").toUserJid}),98);
__d("MAWFetchContacts",["I64","LSContactTypeExact","LSIntEnum","MAWContactRelationshipType","ReQL","WAJids","asyncToGeneratorRuntime","requireDeferred","toMsgrUserJid"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=c("requireDeferred")("LSDatabaseSingletonLazyWrapper").__setRef("MAWFetchContacts");function k(a){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=(yield j.load());b=(yield b());var e=new Set(a.map(function(a){return d("WAJids").extractUserId(a)}));return d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(b.tables.contacts).filter(function(a){return e.has((h||(h=d("I64"))).to_string(a.id))})).then(function(a){return new Map(a.map(function(a){return[c("toMsgrUserJid")((h||(h=d("I64"))).to_string(a.id)),a]}))})});return l.apply(this,arguments)}function a(a){return k(a).then(function(a){var b=new Map();a.forEach(function(a,c){b.set(c,d("MAWContactRelationshipType").getContactRelationshipType(a))});return b})}function e(a){return k(a).then(function(a){var b=new Map();a.forEach(function(a,e){a=a.contactTypeExact!=null&&(h||(h=d("I64"))).equal(a.contactTypeExact,(i||(i=d("LSIntEnum"))).ofNumber(c("LSContactTypeExact").MESSENGER_ONLY_NOT_ALLOWED_ON_FB));b.set(e,a)});return b})}g.fetchContacts=k;g.fetchContactsRelationships=a;g.fetchContactsIsMsplit=e}),98);
__d("MAWSetupBridgeOfflineSnapshot",["InteractionTracingMetrics","MAWMIC","MWChatInteraction","MWFBLogger","WAOfflineUtils","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=babelHelpers.taggedTemplateLiteralLoose(["WA infra offline connection lost"]);h=function(){return a};return a}function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["Stats end: "," / ",", messages: "," / ",""]);i=function(){return a};return a}function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["Progress: "," / ",", messages: "," / ",""]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["Stats start: ",", messages: ",""]);k=function(){return a};return a}var l=d("MWFBLogger").MWLogger.tags(["ServerRPC","Offline"]);function m(a){d("MAWMIC").addIntAnnotation("offlineQueueSize",a);var b=d("MWChatInteraction").get(d("MWChatInteraction").MW_AUTO_CHAT_TAB_OPEN);b!=null&&c("InteractionTracingMetrics").addAnnotationInt(b,"offlineQueueSize",a)}a=function(a){return function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){var c=b.downloaded,e=b.expected,f=b.status;b=b.timestamp;switch(f){case d("WAOfflineUtils").WAClientInfraOfflineProgress.Initializing:l.DEBUG(k(),e.count,e.message);m(e.count);a({tag:"InitOfflineQueueSyncStart",value:e.count});d("MAWMIC").addPoint("download_offline_queue_start",b);return;case d("WAOfflineUtils").WAClientInfraOfflineProgress.Processing:l.DEBUG(j(),c.count,e.count,c.message,e.message);a({tag:"InitOfflineQueueSyncProgress",value:c.count});return;case d("WAOfflineUtils").WAClientInfraOfflineProgress.Complete:l.DEBUG(i(),c.count,e.count,c.message,e.message);d("MAWMIC").addIntAnnotation("offlineQueueDownloadedCount",c.count);a({tag:"InitOfflineQueueSyncProgress",value:e.count});d("MAWMIC").addPoint("download_offline_queue_end",b);return;case d("WAOfflineUtils").WAClientInfraOfflineProgress.Failed:l.DEBUG(h());d("MAWMIC").addPoint("download_offline_queue_failed",b);return}});return function(a){return c.apply(this,arguments)}}()};e=a;g["default"]=e}),98);
__d("MWWorkerOwnedDbKeychain",["Deferred","MAWCryptoConsts","MWFBLogger"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=babelHelpers.taggedTemplateLiteralLoose(["handleEARKeyChange"]);h=function(){return a};return a}c={accountKey:null,databaseEncryptionKeys:new Map(),dbReady:new(c("Deferred"))(),latestVersion:d("MAWCryptoConsts").VERSION};var i=babelHelpers["extends"]({},c),j="uninitialized",k=d("MWFBLogger").MWLogger.tags(["MWWorkerOwnedDbKeychain"]);function a(){return i}function b(a){k.DEBUG(h()),j="initialized",i=a}function e(){return j==="initialized"}function f(){return i.latestVersion}g.getEARKey=a;g.handleEARKeyChange=b;g.isInitialised=e;g.getLatestVersion=f}),98);
__d("IGDSetupBridge",["DTSG","FBLogger","JSResourceForInteraction","LSDatabaseSingleton","LSGetLatestThreadIds.nop","LSInitSyncCompleteSubscription","LSPlatformGraphQLLightspeedRequest","LSPlatformGraphQLLightspeedRequestForIGD","MAWAbPropsClient","MAWBridgeReceivedChatStateHandler","MAWBridgeUIEventHandler","MAWCommonSetupBridge","MAWFetchContacts","MAWGetEphemeralSettings","MAWSetupBridgeOfflineSnapshot","MAWUnrecoverableDbErrors","MWWorkerOwnedDbKeychain","Promise","Run","WAExceededStorageQuota","asyncToGeneratorRuntime","cr:3411","emptyFunction","gkx","isInstamadillo","promiseDone","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(e=c("requireDeferred"))("ArmadilloCutoverClientFalcoEvent").__setRef("IGDSetupBridge"),k=e("IGDBridgeResignCdnUrlHandler").__setRef("IGDSetupBridge"),l=e("MAWBridgeUpdateDYIStatusV2").__setRef("IGDSetupBridge"),m=e("MAWSetupBridgeOfflineConsumer").__setRef("IGDSetupBridge"),n=(i||(i=d("LSDatabaseSingleton"))).LSDatabaseSingleton.then(d("LSInitSyncCompleteSubscription").use).then(function(){return(i||(i=d("LSDatabaseSingleton"))).LSDatabaseSingleton}),o=function(a){j.loadImmediately(function(b){b=b.log;return b(function(){return{armadillo_thread_id:a.armadilloThreadId,domain:a.domain,event:a.event,event_details:a.eventDetails,open_thread_id:a.openThreadId,success:a.success,trace_id:a.traceId}})})},p=new Set(["StartTraceWithTraceId","TraceRecordCheckpoint"]),q=new Set(["ABPropsUpdated","ClockSkewUpdated","DropAllSecureThreads","LargeClockSkewValue","ReceivedChatState","UpdateMediaStatus"]);function a(a){var e=a.dispatch,g=a.logout,j=a.reregisterDevice,r=a.rotateCryptoAuthToken,s=a.worker;function t(){s.postMessage({type:"force-flush-data"})}d("Run").onUnload(t);d("Run").onBeforeUnload(t,!1);return d("MAWCommonSetupBridge").initBridge({customizeEventHandlers:{abPropsUpdated:function(a){d("MAWAbPropsClient").rewrite(a.abProps)},earKeyChange:function(a){d("MWWorkerOwnedDbKeychain").handleEARKeyChange(a)},executeGraphQLLightSpeedRequest:function(a){a=a.args;return c("gkx")("3889")?c("LSPlatformGraphQLLightspeedRequestForIGD").apply(void 0,a):c("LSPlatformGraphQLLightspeedRequest").apply(void 0,a)},getDTSGToken:function(){return(h||(h=b("Promise"))).resolve(d("DTSG").getToken())},getLSDBEphemeralSetting:function(a){return d("MAWGetEphemeralSettings").getEphemeralSetting(a.chatJid)},getLSDBLatestThreadIds:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=(yield (i||(i=d("LSDatabaseSingleton"))).LSDatabaseSingleton);return b.runInTransaction(function(b){return c("LSGetLatestThreadIds.nop")(b,0,"",a.numThreads,a.miActMappingFilteredThreads)},"readonly",void 0,void 0,f.id+":140")});function e(b){return a.apply(this,arguments)}return e}(),getLSDBUsersIsMsplit:function(a){return d("MAWFetchContacts").fetchContactsIsMsplit(a.users)},getLSDBUsersRelationships:function(a){return d("MAWFetchContacts").fetchContactsRelationships(a.users)},getMediaStorageResult:function(){return(h||(h=b("Promise"))).reject("should not be called for IGD")},getMinosEpochDataForUser:function(){return(h||(h=b("Promise"))).reject("should not be called for IGD")},isDbMigrating:function(a){e({tag:"IsDbMigrating",value:!1})},logCutoverEvent:o,logout:g,offlineConsumerProgress:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){return(yield m.load())(n,e).apply(void 0,arguments)});function c(){return a.apply(this,arguments)}return c}(),offlineSnapshot:c("MAWSetupBridgeOfflineSnapshot")(e),receivedChatState:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var c=(yield n);yield c.runInTransaction(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){yield d("MAWBridgeReceivedChatStateHandler").call(b,a)});return function(a){return c.apply(this,arguments)}}(),"readwrite",void 0,void 0,f.id+":177")});function c(b){return a.apply(this,arguments)}return c}(),reregisterDevice:j,resignCdnUrl:function(a){var b=a.deliveryObjectId,c=a.payload;return k.load().then(function(a){return a.call(c,b)})},rotateCryptoAuthToken:r,storageQuota:function(a){a=a.exceededStorageQuota;d("WAExceededStorageQuota").setExceededStorageQuota(a)},trackEbConsistency:function(){},uiUpdate:function(a){a=a.events;var e=[],f=a.filter(function(a){if(p.has(a.tag)){e.push(a);return!1}if(q.has(a.tag))return!1});e.length>0&&(b("cr:3411")==null?void 0:b("cr:3411").recordBridgeUIEvent(a),d("MAWBridgeUIEventHandler").handleEvents(n,a));if(f.length===0)return;c("isInstamadillo")()?c("FBLogger")("igd_web").warn("[Instamadillo] Unsupported invocation of UI Event Handler. Please deprecate UI Event Handler: %s",a.map(function(a){return a.tag}).join(", ")):c("FBLogger")("igd_web").warn("[IGD] Deprecating UIEventHandlers, please update LSDB directly for UI changes: %s",a.map(function(a){return a.tag}).join(", "))},unrecoverableDbError:function(a){a=a.error;return d("MAWUnrecoverableDbErrors").setError(a)},updateDYIStatus:function(a){var b=a.qplEvent,d=a.qplInstanceKeyE2E,e=a.status;c("promiseDone")(l.load().then(function(a){return a.call({qplEvent:b,qplInstanceKeyE2E:d,status:e})}))},updateEBLSInWokerStateInMainThread:function(){return(h||(h=b("Promise"))).reject("should not be called for IGD")},updateMediaStatus:c("emptyFunction"),uploadMessagesToBackupV2:function(){return(h||(h=b("Promise"))).reject("should not be called for IGD")},uploadOpenMessages:function(a){var b=a.uploadPayloads;c("promiseDone")(c("JSResourceForInteraction")("IGDMessageUploadMutation").__setRef("IGDSetupBridge").load().then(function(a){return a.uploadOpenMessageOverGQL(b)}))}},worker:s})}g.setupBridge=a}),98);
__d("MessengerWaitForEvent",["Promise"],(function(a,b,c,d,e,f){"use strict";var g;function a(a,c,d){var e,f=d;return new(g||(g=b("Promise")))(function(b){f?e=function(){f.apply(this,arguments)&&b.apply(this,arguments)}:e=b,a==null?void 0:a.addEventListener==null?void 0:a.addEventListener(c,e)})["finally"](function(){a==null?void 0:a.removeEventListener==null?void 0:a.removeEventListener(c,e)})}f.waitForEvent=a}),66);
__d("LocalStorageMutex",["FBLogger","MessengerWaitForEvent","Promise","QPLUserFlow","emptyFunction","pageID","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h,i=null,j=1e3,k=c("qpl")._(1056847375,"584");a=function(){function a(a){var b=this,d=a.mutexKey,e=a.localTakeoverKey,f=a.log,g=a.logError,h=a.getStorage,i=a.setItemGuarded,k=a.mutexTimeoutMs;k=k===void 0?j:k;a=a.mutexTimeoutCallback;this.mutexFilter=function(a){if(a.key!==b.$1)return!1;a=a.newValue||"";return!a.includes(c("pageID"))};this.$1=d;this.$2=e;this.$5=h;this.$6=i;this.$3=f;this.$4=g;this.$7=k;this.$8=a}var e=a.prototype;e.init=function(){c("QPLUserFlow").start(k);this.$3("APPMUTEX - this tab is "+c("pageID"));var a=this.getMutex();if(a===c("pageID")){this.$3("APPMUTEX - WE ALREADY HAVE THE MUTEX LOCK WITHIN INIT");c("QPLUserFlow").endSuccess(k);return(h||(h=b("Promise"))).resolve(!0)}if(a!=null){this.$3("APPMUTEX - initiating localTAKEOVER");return this.localTakeover()}else{this.$3("APPMUTEX - NO1 ELSE IS ALIVE, GET THE MUTEX");a=this.$9(c("pageID"));var d=this.$10(c("pageID"));if(!a.success||!d.success){c("QPLUserFlow").addAnnotations(k,{bool:{setMutexResult:d.success,setTakeoverResult:a.success},string:{setMutexError:d.error,setTakeoverError:a.error}});c("QPLUserFlow").endFailure(k,"*****************************");return(h||(h=b("Promise"))).resolve(!1)}c("QPLUserFlow").endSuccess(k);return(h||(h=b("Promise"))).resolve(!0)}};e.localTakeover=function(){var a,e=this,f=this.$9(c("pageID"));if(!f.success){c("QPLUserFlow").addAnnotations(k,{string:{initTakeoverError:f.error}});c("QPLUserFlow").endFailure(k,"local_takeover_init_takeover_failure");return(h||(h=b("Promise"))).resolve(!1)}var g,i=function(){g!=null&&clearTimeout(g)};f=d("MessengerWaitForEvent").waitForEvent(window,"storage",this.mutexFilter);var l=(a=this.$7)!=null?a:j;return(h||(h=b("Promise"))).race([f,new h(function(a){g=setTimeout(function(){a()},l)}),this.$8?this.$8():new(h||(h=b("Promise")))(c("emptyFunction"))]).then(function(a){if(a==null){e.$3("AppMutex - Forcefully took the mutex and takeover lock");var b=e.$9(c("pageID")),d=e.$10(c("pageID"));if(!b.success||!d.success){c("QPLUserFlow").addAnnotations(k,{string:{setMutexError:d.error,setTakeoverError:b.error}});c("QPLUserFlow").endFailure(k,"local_takeover_forcefully_takeover_failure");return!1}c("QPLUserFlow").endSuccess(k);return!0}e.$3("APPMUTEX - GOT MUTEX STORAGE EVENT CHANGE FROM ANOTHER TAB");d=e.$11(a.newValue);if(!d&&e.hasTakeoverPermission()){e.$3("APPMUTEX - setting mutex on localtakeover");b=e.$10(c("pageID"));if(!b.success){c("QPLUserFlow").addAnnotations(k,{string:{setMutexError:b.error}});c("QPLUserFlow").endFailure(k,"local_takeover_set_mutex_failure");return!1}c("QPLUserFlow").endSuccess(k);return!0}else{e.hasTakeoverPermission()&&e.$12();c("QPLUserFlow").endFailure(k,"local_takeover_no_takeover_permission");return!1}}).then(function(a){i();return a})["catch"](function(a){i();e.$4("AppMutex - localTakeover error no permission - "+a);c("QPLUserFlow").endFailure(k,"local_takeover_general_failure");return!1})};e.onMutexDeath=function(){var a=this.getMutex();a!=null&&a===c("pageID")&&(this.$3("APPMUTEX - removing mutex on death"),this.$13(),this.hasTakeoverPermission()&&this.$12())};e.takeoverFilter=function(a){if(a.key!==this.$2)return!1;a=a.newValue||"";return!a.includes(c("pageID"))};e.$14=function(){if(!i){var a=this.$5();if(!a)throw c("FBLogger")("messenger_web").mustfixThrow("Could not get localStorage");i=a}return i};e.getMutex=function(){i=this.$14();return i.getItem(this.$1)};e.$10=function(a){i=this.$14();return this.$6(i,this.$1,a)};e.$13=function(){i=this.$14(),i.removeItem(this.$1)};e.getTakeover=function(){i=this.$14();return i.getItem(this.$2)};e.$9=function(a){i=this.$14();return this.$6(i,this.$2,a)};e.$12=function(){i=this.$14(),i.removeItem(this.$2)};e.$11=function(a){return a!=null?a:""};e.hasTakeoverPermission=function(){var a=this.getTakeover();return a!=null&&a===c("pageID")};return a}();g.THIS_TAB=c("pageID");g.LocalStorageMutex=a}),98);
__d("MAWAppMutex",["LocalStorageMutex","MAWLocalStorage","WALogger"],(function(a,b,c,d,e,f,g){"use strict";var h="armadillo_msgr_mutex",i="armadillo_msgr_local_takeover",j=null;function a(){j==null&&(j=new(d("LocalStorageMutex").LocalStorageMutex)({getStorage:d("MAWLocalStorage").getStorage,localTakeoverKey:i,log:function(a){d("WALogger").LOG([a])},logError:function(a){d("WALogger").ERROR([a])},mutexKey:h,setItemGuarded:d("MAWLocalStorage").setItemGuarded}));return j}g.MAW_MUTEX_KEY=h;g.MAW_LOCALTAKEOVER_KEY=i;g.use=a}),98);
__d("LSIssueNewErrorStoredProcedure",["LSIssueNewError","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSIssueNewError"),e.taskId,e.errorCode,e.errorTitleText,e.errorBodyText,e.errorExtraData,e.presentationStyle);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("MAWIssueNewUserVisibleErrors",["fbt","$InternalEnum","LSDatabaseSingleton","LSFactory","LSIssueNewErrorStoredProcedure","LSRemoveErrorStoredProcedure","Promise","ReQL","asyncToGeneratorRuntime","gkx","promiseDone"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j;function a(a,b,e,g){c("promiseDone")((j||(j=d("LSDatabaseSingleton"))).LSDatabaseSingleton.then(function(d){return d.runInTransaction(function(d){return c("LSIssueNewErrorStoredProcedure")(c("LSFactory")(d),{errorBodyText:a!=null?a.toString():void 0,errorExtraData:g,errorTitleText:b.toString(),presentationStyle:String(e)})},"readwrite",void 0,void 0,f.id+":37")}))}function k(a,b){return a.runInTransaction(function(a){return c("LSRemoveErrorStoredProcedure")(c("LSFactory")(a),{errorId:b})},"readwrite",void 0,void 0,f.id+":56")}function e(){c("promiseDone")(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield (j||(j=d("LSDatabaseSingleton"))).LSDatabaseSingleton),c=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.tables.user_visible_errors)));yield (i||(i=b("Promise"))).all(c.map(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){yield k(a,b.errorId)});return function(a){return c.apply(this,arguments)}}()))})())}function l(a){c("promiseDone")(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var c=(yield (j||(j=d("LSDatabaseSingleton"))).LSDatabaseSingleton),e=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(c.tables.user_visible_errors)));yield (i||(i=b("Promise"))).all(e.map(function(){var d=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){b.errorExtraData===a&&(yield k(c,b.errorId))});return function(a){return d.apply(this,arguments)}}()))})())}var m=b("$InternalEnum")({TOAST:"toast",BANNER:"banner"}),n=h._(/*BTDS*/"Couldn't load all end-to-end encrypted chats"),o=h._(/*BTDS*/"Try refreshing the page to see them."),p=h._(/*BTDS*/"You may need to refresh this page"),q=h._(/*BTDS*/"There may be issues sending or receiving messages on this page. Refresh it to reload your chats."),r=c("gkx")("4424")?h._(/*BTDS*/"This browser is not supported"):h._(/*BTDS*/"You may need to update your browser");h=c("gkx")("4424")?h._(/*BTDS*/"Some messages are missing and certain messaging features are not available. Try using another browser or mobile device."):h._(/*BTDS*/"End-to-end encrypted chats won't appear in this browser. Try updating your browser or use another one.");var s=new Map([[n.toString(),n],[p.toString(),p]]),t=new Map([[o.toString(),o],[q.toString(),q]]);g.issueError=a;g.deleteUserVisibleError=k;g.deleteAllErrors=e;g.deleteUserVisibleErrorByJSErrorMessage=l;g.MAWUserVisibleErrorPresentationType=m;g.MAWGeneralErrorMsgTitle=n;g.MAWGeneralErrorMsgBody=o;g.MAWLegacyUserErrorMsgTitle=p;g.MAWLegacyUserErrorMsgBody=q;g.MAWUnsupportedIndexedDBErrorMsgTitle=r;g.MAWUnsupportedIndexedDBErrorMsg=h;g.errorMsgTitleToFbtMap=s;g.errorMsgBodyToFbtMap=t}),226);
__d("MAWCommonBackendSetup",["EARInitType","MAWAppMutex","MAWEncryptionIndexedDbV2","MAWInit","MAWInitError","MAWIssueNewUserVisibleErrors","MAWLocalStorage","MAWLoggerUtils","MAWSetupWorker","MAWVaultMaterialsStorage","MAWWorkerReboot","MWSetupDBEncryption","Promise","asyncToGeneratorRuntime","gkx","requireDeferredForDisplay","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("requireDeferredForDisplay")("MAWMainThreadLogger").__setRef("MAWCommonBackendSetup"),j=function(a,e){var f=c("gkx")("9301");if(f===!0)if(e)return(h||(h=b("Promise"))).resolve(!0);else return a.measurePerformance("acquire_mutex",function(){return d("MAWAppMutex").use().init()});else return a.measurePerformance("acquire_mutex",function(){try{var a=d("MAWAppMutex").use().init();return e?a.then(function(){return(h||(h=b("Promise"))).resolve(!0)},function(){return(h||(h=b("Promise"))).resolve(!0)}):a}catch(a){if(e)return(h||(h=b("Promise"))).resolve(!0);throw a}})};function a(a,b,c,d,e){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e,f,g){var i=d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker();if(!i){if(!d("MAWLocalStorage").isLocalStorageAvailable())throw new(d("MAWInitError").MAWInitError)("localstorage_not_supported");return(h||(h=b("Promise"))).resolve()}var j=(yield d("MAWVaultMaterialsStorage").setupVaultMaterials());yield d("MAWSetupWorker").getOrSetupWorker(j,a,c,e,f,g)["catch"](function(b){return d("MAWWorkerReboot").rebootWorker(j,a,c,e,"backend_setup_failure",g)["catch"](function(a){throw new(d("MAWInitError").MAWInitError)("worker_setup_error",a)})})});return k.apply(this,arguments)}function e(a,b,c,d,e,f){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f,g,k,l){yield a.measurePerformance("prepare_logger",b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=i.load();(yield a).setupMainWaLogger()}));d("MAWLoggerUtils").initMessengerWebLogging();var m=d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker();m||(yield a.measurePerformance("detection_support",function(){if(!d("MAWLocalStorage").isLocalStorageAvailable())throw new(d("MAWInitError").MAWInitError)("localstorage_not_supported");return(h||(h=b("Promise"))).resolve()}));var n=a.measurePerformance("setup_vault_materials",function(){return d("MAWVaultMaterialsStorage").setupVaultMaterials()});m=(yield j(a,m));var o=(yield a.measurePerformance("main_ear_setup",b("asyncToGeneratorRuntime").asyncToGenerator(function*(){if(c("gkx")("12397"))return(h||(h=b("Promise"))).resolve({success:!0});d("MWSetupDBEncryption").setPerformanceMeasurementTool(d("MAWInit").MAWInit.measurePerformance);var a=(yield d("MWSetupDBEncryption").init(c("EARInitType").MAW_INIT));d("MAWEncryptionIndexedDbV2").maybeSetEncryptionDbOnVersionChange(function(){var a;(a=d("MAWIssueNewUserVisibleErrors")).issueError(a.MAWLegacyUserErrorMsgBody,a.MAWLegacyUserErrorMsgTitle,a.MAWUserVisibleErrorPresentationType.BANNER)});return a})));if(!o.success){a.logPoint("keychain_setup_failure");throw new(d("MAWInitError").MAWInitError)("keychain_setup_failure",o.payload)}if(!m){a.logPoint("maw_appmutex_no_permission");throw new(d("MAWInitError").MAWInitError)("maw_appmutex_no_permission")}var p=(yield n);yield a.measurePerformance("setup_worker",function(){return d("MAWSetupWorker").getOrSetupWorker(p,e,f,g,k,l)["catch"](function(b){a.logPoint("reboot_worker_on_backend_setup_failure");a.addStringAnnotation("backend_setup_failure_reason",b.message);return d("MAWWorkerReboot").rebootWorker(p,e,f,g,"backend_setup_failure",l)["catch"](function(a){throw new(d("MAWInitError").MAWInitError)("worker_setup_error",a)})})})});return l.apply(this,arguments)}g.getMutexPermission=j;g.setupArmadilloBackendForCalling=a;g.setupArmadilloBackend=e}),98);
__d("MAWStateContext.react",["$InternalEnum","EncryptedBackupsDYITypes","FBLogger","MAWSharedProtocolQueueConst","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=i.createContext,k=i.useCallback,l=i.useContext,m=i.useEffect,n=i.useRef,o=i.useState;i=b("$InternalEnum")({NOT_STARTED:"not_started",IN_PROGRESS:"in_progress",READY:"ready"});b={ebDYIState:d("EncryptedBackupsDYITypes").EncryptedBackupsDYIState.NotStarted,ebState:null,ephemeralSettings:{},isMAWDBinUILoaded:!1,mediaDownloadRetryCount:{},mediaDownloadStatus:{},messageLatencies:{},offlineQueueCount:0,offlineQueueProgressDownloaded:0,offlineQueueProgressProcessed:0,offlineQueueSyncState:d("MAWSharedProtocolQueueConst").OfflineConsumerStatus.Initializing,offlineQueueThreadStatus:{},unArchivedSelfDeviceChangeAlerts:0,workerState:i.NOT_STARTED};function a(){var a=l(r);return k(function(b,c){c===void 0&&(c=Date.now()),a({optimisticMsgId:b,tag:"OptimisticSendMessage",timestamp:c})},[a])}function e(){var a=l(r);return k(function(){a({tag:"ClearEphemeralSettings"})},[a])}var p={state:b,subscriptions:new Set()},q=j(function(){return p});d=j(p);var r=j(function(a){throw c("FBLogger")("messenger_web").mustfixThrow("MAW Dispatch not implemented")});function f(a){var b=l(q)(),c=b.state,d=b.subscriptions;b=o(function(){return a(c)});var e=b[0],f=b[1],g=n(e);m(function(){var b=function(b){b=a(b);b!==g.current&&(g.current=b,f(b))};d.add(b);return function(){d["delete"](b)}},[d,a]);return e}g.WorkerState=i;g.initialState=b;g.useDispatchOptimisticSendMessage=a;g.useDispatchClearEphemeralSettings=e;g.MAWNonRerenderingStateContext=q;g.MAWStateContext=d;g.MAWDispatchContext=r;g.useMAWSelector=f}),98);
__d("WADynamicRouterSync",["ExecutionEnvironment","Promise","WALogger","WAMapWithDefault","err","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(){var a=babelHelpers.taggedTemplateLiteralLoose([""," is not defined for ",""]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["No handler for bridge with namespace: "," route: "," called in environment: ",""]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["Bridge handler: ",""]);l=function(){return a};return a}function m(a,b){if(a==null)return;var c=a.toLastActiveClient;a=babelHelpers.objectWithoutPropertiesLoose(a,["toLastActiveClient"]);if(c===!0&&b!=null){return babelHelpers["extends"]({},a,{toClientId:(c=a.toClientId)!=null?c:b})}return a}a=function(){function a(){var a=this;this.$2=new Map();this.$4=new(d("WAMapWithDefault").MapWithDefault)(function(){return new Set()});this.$5=null;this.fireAndForget=function(b,c,d,e,f,g){e===void 0&&(e=!1),a.$3==null?void 0:a.$3(b,c,d),a.$4.get(b).add({route:c,arg:d,resolver:null,silentLog:e,eventCallbacks:f,opts:m(g,a.$5)}),a.$6(b,c)};this.sendAndReceive=function(c,d,e,f,g,h){f===void 0&&(f=!1);return new(i||(i=b("Promise")))(function(b){a.$3==null?void 0:a.$3(c,d,e),a.$4.get(c).add({route:d,arg:e,resolver:function(a){b(a)},silentLog:f,eventCallbacks:g,opts:m(h,a.$5)}),a.$6(c,d)})}}var e=a.prototype;e.setAckPayload=function(a){this.$1=a};e.getAckPayoad=function(){return this.$1};e.hasHandlerForNamespace=function(a){return this.$2.has(a)};e.getHandledNamespaces=function(){return Array.from(this.$2.keys())};e.setOnRouteCalled=function(a){this.$3=a};e.setNamespaceHandler=function(a,b){var c=this.$2,d=c.get(a);if(d===b)return;c.set(a,b);d==null&&this.$6(a)};e.$7=function(a,c){var e=a.route,f=a.arg,g=a.resolver,h=a.silentLog,j=a.eventCallbacks;a=a.opts;try{var k;this.$5=(k=a==null?void 0:a.fromClientId)!=null?k:this.$5;c(e,f,g,h,j,a)}catch(a){g==null?void 0:g((i||(i=b("Promise"))).reject(a)),d("WALogger").ERROR(l(),a)}};e.$6=function(a,b){var e=this,f=this.$4.get(a),g=this.$2.get(a);if(g==null){var i=(h||(h=c("ExecutionEnvironment"))).isInWorker?"worker":"main-thread";d("WALogger").ERROR(k(),a,b,i);return}f.forEach(function(a){c("gkx")("14383")?(f["delete"](a),e.$7(a,g)):(e.$7(a,g),f["delete"](a))})};e.setHandlers=function(a,b){function e(e,f,g,h,i,k){if(b[e]==null){d("WALogger").ERROR(j(),e,a);throw c("err")(e+" is not defined for "+a)}h=b[e](f,k);g&&g(h)}this.setNamespaceHandler(a,e)};return a}();g.normaliseHandlerOptions=m;g.DynamicRouterSync=a}),98);
__d("shouldHideChatOnAppsSubdomain",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return!c("gkx")("738")?!1:document.location.hostname==="apps.facebook.com"}g["default"]=a}),98);
__d("shouldDisableWorkerOnAppsSubdomain",["gkx","shouldHideChatOnAppsSubdomain"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("8929")&&c("shouldHideChatOnAppsSubdomain")()}g["default"]=a}),98);
__d("useMWEncryptedBackupsProcessBackupIdsDeferred",["promiseDone","react","requireDeferred","useAsyncReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect,j=c("requireDeferred")("MWEncryptedBackupsProcessBackupIdsDEPRECATED").__setRef("useMWEncryptedBackupsProcessBackupIdsDeferred");function a(a){var b=c("useAsyncReStore")();i(function(){var d,e=!1;j.onReadyImmediately(function(f){c("promiseDone")(b,function(b){e||(d=f.subscribeToEncryptedBackupsRow(b,a))})});return function(){e=!0,d==null?void 0:d()}},[b,a])}g["default"]=a}),98);
__d("MAWCommonSetup",["EBMainThreadEBDBApiDeferred","EBMainThreadListeners","ExecutionEnvironment","MAWBridgeFireAndForget","MAWCommonBackendSetup","MAWEncryptedBackupsStateManager","MAWInit","MAWInitError","MAWIsIDBOperational","MAWIssueNewUserVisibleErrors","MAWJobDefinitions","MAWLoggerUtils","MAWMIC","MAWStateContext.react","MAWUIJob","MAWWaitForBackendSetup","WABridge","WADynamicRouterSync","WAJobOrchestratorTypes","WALogger","asyncToGeneratorRuntime","cr:13007","cr:17068","cr:19609","cr:9939","getErrorSafe","gkx","performance","promiseDone","react","requireDeferred","shouldDisableWorkerOnAppsSubdomain","useMWEncryptedBackupsProcessBackupIdsDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["Armadillo is disabled on apps subdomain"]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["Armadillo is disabled on apps subdomain"]);l=function(){return a};return a}f=j||d("react");var m=f.useContext,n=f.useEffect,o=c("requireDeferred")("FBLogger").__setRef("MAWCommonSetup"),p=b("cr:19609")!=null?b("cr:19609")==null?void 0:b("cr:19609").MAWInitialiseMAWDbInUI:b("cr:13007")==null?void 0:b("cr:13007").MAWInitialiseMAWDbInUI;c("requireDeferred")("MAWTimedJob").__setRef("MAWCommonSetup");var q=!1,r=function(){return(h||(h=c("performance"))).now()},s=function(a){c("gkx")("12414")||(d("MAWEncryptedBackupsStateManager").setEncryptedBackupsState(a),d("MAWBridgeFireAndForget").fireAndForget("backend","setEncryptedBackupsState",{newState:a}))};function t(){d("MAWBridgeFireAndForget").fireAndForget("backend","resendEBLSInWorkerEbEnabledState")}function a(a){d("MAWLoggerUtils").initMessengerWebLogging();var b=m(d("MAWStateContext.react").MAWDispatchContext),e=d("MAWIsIDBOperational").useIsIDBOperational();n(function(){e&&(i||(i=c("ExecutionEnvironment"))).isInBrowser&&c("promiseDone")(w(b,a))},[a,b,e]);n(function(){e&&c("promiseDone")(d("EBMainThreadListeners").initEBListeners())},[e]);c("useMWEncryptedBackupsProcessBackupIdsDeferred")(s);t()}function e(a){var b=m(d("MAWStateContext.react").MAWDispatchContext),e=d("MAWIsIDBOperational").useIsIDBOperational();n(function(){e&&c("promiseDone")(u(b,a))},[a,b,e])}function u(a,b){return v.apply(this,arguments)}function v(){v=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e=b.logout,f=b.reregisterDevice,g=b.rotateCryptoAuthToken,h=b.setupBridge;if(q){void o.load().then(function(a){a("messenger_web").warn("Armadillo already setup - return")});return}if(c("shouldDisableWorkerOnAppsSubdomain")()){d("WALogger").LOG(l());return}q=!0;d("WABridge").makeWABridge(new(d("WADynamicRouterSync").DynamicRouterSync)());try{d("MAWIssueNewUserVisibleErrors").deleteAllErrors(),yield d("MAWCommonBackendSetup").setupArmadilloBackendForCalling(function(b){return h({dispatch:a,logout:e,reregisterDevice:f,rotateCryptoAuthToken:g,worker:b})},function(){a({tag:"InitOfflineQueueSyncComplete",value:void 0}),d("MAWMIC").onConnectToExistingWorker()},function(){a({tag:"InitOfflineQueueSyncComplete",value:void 0})},"bridgeForCalling",function(a){})}catch(a){void o.load().then(function(b){return b("messenger_web").catching(a).mustfix("UI Dexie initialisation failed: %s",a.message)})}});return v.apply(this,arguments)}function w(a,b){return x.apply(this,arguments)}function x(){x=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){var f=e.logout,g=e.reregisterDevice,h=e.rotateCryptoAuthToken,j=e.setupBridge;if(q){void o.load().then(function(a){a("messenger_web").warn("Armadillo already setup - return")});return}if(c("shouldDisableWorkerOnAppsSubdomain")()){d("WALogger").LOG(k());return}q=!0;d("MAWMIC").startMAWMICFlow();d("MAWInit").MAWInit.start();b("cr:9939")!=null&&(i||(i=c("ExecutionEnvironment"))).isInBrowser&&(yield b("cr:9939").getOrSetupMAWCacheDB());p!=null&&(i||(i=c("ExecutionEnvironment"))).isInBrowser&&c("promiseDone")(p(a),function(){},function(a){return o.load().then(function(b){return b("messenger_web").catching(a).mustfix("UI Dexie initialisation failed: %s",a.message)})});b("cr:17068")!=null&&c("promiseDone")(b("cr:17068").getDeviceAge());d("WABridge").makeWABridge(new(d("WADynamicRouterSync").DynamicRouterSync)());try{d("MAWIssueNewUserVisibleErrors").deleteAllErrors(),yield d("MAWCommonBackendSetup").setupArmadilloBackend(d("MAWInit").MAWInit,function(b){return j({dispatch:a,logout:f,reregisterDevice:g,rotateCryptoAuthToken:h,worker:b})},function(){a({tag:"InitOfflineQueueSyncComplete",value:void 0}),a({tag:"IsDbMigrating",value:!1}),d("MAWMIC").onConnectToExistingWorker()},function(){a({tag:"InitOfflineQueueSyncComplete",value:void 0}),a({tag:"IsDbMigrating",value:!1})},"mawInit",function(a){}),d("MAWUIJob").UIJobStarters.fireAndForget(d("MAWJobDefinitions").jobSerializers.handleFutureproofMsg({priority:d("WAJobOrchestratorTypes").JOB_PRIORITY.LOW})),d("MAWInit").MAWInit.addIntAnnotation("endTimePageRelative",r()),d("MAWInit").MAWInit.endSuccess()}catch(a){var l,m=c("getErrorSafe")(a);e=m instanceof d("MAWInitError").MAWInitError?m.message:"maw_init_failure";m.message=(l=m.message)!=null?l:"Unknown";l=m.message==="maw_appmutex_no_permission"&&document.visibilityState!=="visible";l?d("MAWInit").MAWInit.cancel(e,m):(d("MAWWaitForBackendSetup").rejectBackendSetup(m),d("MAWInit").MAWInit.fail(e,m));d("MAWIssueNewUserVisibleErrors").issueError(d("MAWIssueNewUserVisibleErrors").MAWGeneralErrorMsgBody,d("MAWIssueNewUserVisibleErrors").MAWGeneralErrorMsgTitle,d("MAWIssueNewUserVisibleErrors").MAWUserVisibleErrorPresentationType.BANNER,m.message);m&&void o.load().then(function(a){a("messenger_web").catching(m).mustfix("MAW backend setup failed -- %s",m.message)});throw m}void d("EBMainThreadEBDBApiDeferred").startListeningDeviceRegistrations()});return x.apply(this,arguments)}g.useArmadillo=a;g.useArmadilloForCalling=e}),98);
__d("MAWOccamadilloOnMutexChange",["I64","LSDefaultSyncGroups","LSIntEnum","LSSyncGroupsUtils","Promise","WALogger","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["[Occamadillo] Resetting sync cursor and network request"]);k=function(){return a};return a}function l(a){var c=d("LSDefaultSyncGroups").e2eeMetadataSyncGroup;if(c!=null)return a.sync_groups.put(babelHelpers["extends"]({},d("LSSyncGroupsUtils").defaultSyncGroup,{groupId:(j||(j=d("LSIntEnum"))).ofNumber(c.groupId)}));else return(h||(h=b("Promise"))).resolve()}function m(a){return a.network_requests.put({epochId:void 0,failureCount:(i||(i=d("I64"))).zero,lastDelayedRequestTimestampMs:void 0,lastSentTimestampMs:i.zero,lastSyncRequestTimestampMs:i.zero,networkTaskIdentifier:void 0,syncDatabaseId:(j||(j=d("LSIntEnum"))).ofNumber(95),taskQueueName:""})}function a(a){d("WALogger").LOG(k()),c("promiseDone")(a.runInTransaction(function(a){return(h||(h=b("Promise"))).all([l(a),m(a)])},"readwrite",void 0,void 0,f.id+":53"))}g.onOccamadilloMutexChange=a}),98);
__d("MAWReinit",["ExecutionEnvironment","MAWCurrentUser","QPLUserFlow","QuickPerformanceLogger","WAGetStorageQplAnnotations","asyncToGeneratorRuntime","gkx","qpl","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=c("qpl")._(25310984,"169");function a(a){var e=a!=null?{instanceKey:a}:void 0,f=!1,g=!1;function k(){if(g)return;g=!0;c("QPLUserFlow").start(j,babelHelpers["extends"]({cancelOnUnload:!0,onFlowTimeout:function(){f=!0},timeoutInMs:(h||(h=c("ExecutionEnvironment"))).canUseDOM?6e4:0},e));void d("WAGetStorageQplAnnotations").getStorageQplAnnotations().then(function(a){c("QPLUserFlow").addAnnotations(j,a)});t()}function l(a){if(!g||f)return;t();c("QPLUserFlow").addPoint(j,a,e)}function m(a,b){var d;if(!g||f)return;c("QPLUserFlow").addAnnotations(j,{bool:(d={},d[a]=b,d)})}function n(a,b){var d;if(!g||f)return;c("QPLUserFlow").addAnnotations(j,{string:(d={},d[a]=b,d)})}function o(a,b){var d;if(!g||f)return;c("QPLUserFlow").addAnnotations(j,{string_array:(d={},d[a]=b,d)})}function p(a,b){var d;if(!g||f)return;c("QPLUserFlow").addAnnotations(j,{"int":(d={},d[a]=b,d)})}function q(a){t(),f=!0,c("QPLUserFlow").addAnnotations(j,{string:{failReason:a}},e),c("QPLUserFlow").endFailure(j,"maw_init_fail",e),s=!1}function r(a){t(),f=!0,s=!1,c("QPLUserFlow").addAnnotations(j,{string:{dropReason:a}},e),(i||(i=c("QuickPerformanceLogger"))).markerDrop(j)}var s=!1;function t(){if(s)return;s=!0;c("QPLUserFlow").addAnnotations(j,{bool:{armadillo_init_sync_api_improvements:c("gkx")("24025"),isArmadilloPublicLaunchUser:c("gkx")("23405"),isTlcPublicUser:d("MAWCurrentUser").isTlcPublicUser(),useSharedWorker:d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker()}})}function u(a){t(),f=!0,c("QPLUserFlow").addAnnotations(j,{string:{cancelReason:a}},e),c("QPLUserFlow").endCancel(j,e),s=!1}function v(){t(),f=!0,c("QPLUserFlow").endSuccess(j,e),s=!1}function w(a,b){return x.apply(this,arguments)}function x(){x=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=a+"_start";a=a+"_end";l(c);c=(yield b());l(a);return c});return x.apply(this,arguments)}return{addBoolAnnotation:m,addIntAnnotation:p,addStringAnnotation:n,addStringArrayAnnotation:o,cancel:u,drop:r,endSuccess:v,fail:q,logPoint:l,measurePerformance:w,start:k}}g.getMAWReinit=a}),98);
__d("MAWReinitWorker",["MAWCommonBackendSetup","MAWOccamadilloOnMutexChange","MAWReinit","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return h.apply(this,arguments)}function h(){h=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=a.allTablesPersisted,c=a.db,e=a.dispatch,f=a.dispatchClearEphemeralSettings,g=a.instanceKey,h=a.logout,i=a.reregisterDevice,j=a.rotateCryptoAuthToken,k=a.setupBridge;a=a.workerCreateReason;g=d("MAWReinit").getMAWReinit(g);g.start();var l=function(){e({tag:"InitOfflineQueueSyncComplete",value:void 0})};yield d("MAWCommonBackendSetup").setupArmadilloBackend(g,function(a){a=k({dispatch:e,logout:h,reregisterDevice:i,rotateCryptoAuthToken:j,worker:a});b||d("MAWOccamadilloOnMutexChange").onOccamadilloMutexChange(c);return a},l,l,a,function(a){});g.endSuccess();f()});return h.apply(this,arguments)}g["default"]=a}),98);
__d("MAWSetupMutex",["LocalStorageMutex","MAWAppMutex","MAWBridge","MAWInit","MAWIssueNewUserVisibleErrors","MAWLocalStorage","MAWMIC","MAWReinitWorker","MAWSetupWorker","MAWStateContext.react","MAWWaitForBackendSetup","MAWWebWorkerSingleton","MWFBLogger","QPLUserFlow","Run","asyncToGeneratorRuntime","isInstamadillo","promiseDone","qpl","react","useReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i;e=i||d("react");var j=e.useCallback,k=e.useEffect,l=e.useMemo,m=d("MWFBLogger").MWLogger.tags(["MAWInit"]),n=!1,o=0;function p(a){return q.apply(this,arguments)}function q(){q=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){if(document.visibilityState!=="visible")return!1;var b=window.indexedDB!=null,c=d("MAWLocalStorage").isLocalStorageAvailable();if(a){m.debug("AppMutex - Shouldn't reinitialize, worker already exists");return!1}return!b||!c?!1:!0});return q.apply(this,arguments)}function a(a,e){var f=a.logout,g=a.reregisterDevice,i=a.rotateCryptoAuthToken,q=a.setupBridge,r=l(function(){return d("MAWAppMutex").use()},[]),s=(h||(h=c("useReStore")))(),t=d("MAWStateContext.react").useDispatchClearEphemeralSettings(),u=s.persistenceTypes.includes("indexeddb"),v=j(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=r.takeoverFilter(a);if(!b||a.newValue===d("LocalStorageMutex").THIS_TAB||a.newValue==="")return;!u&&!c("isInstamadillo")()&&d("MAWBridge").getBridge().fireAndForget("event","uiUpdate",{events:[{tag:"DropAllSecureThreads",value:void 0}]});yield d("MAWSetupWorker").terminateDedicatedWorker("MAWSetupMutexV2:handleStoreChange");d("MAWWaitForBackendSetup").resetBackendSetup();d("MAWMIC").cancel("workerMutexLost");d("MAWInit").MAWInit.cancel("workerMutexLost");r.onMutexDeath()});return function(b){return a.apply(this,arguments)}}(),[r,u]),w=j(function(){r.onMutexDeath(),c("promiseDone")(d("MAWSetupWorker").terminateDedicatedWorker("MAWSetupMutexV2:handleUnload"))},[r]),x=j(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield d("MAWWebWorkerSingleton").doesWorkerExist());a=(yield p(a));if(!a||n)return;n=!0;o++;try{yield c("MAWReinitWorker")({allTablesPersisted:u,db:s,dispatch:e,dispatchClearEphemeralSettings:t,instanceKey:o,logout:f,reregisterDevice:g,rotateCryptoAuthToken:i,setupBridge:q,workerCreateReason:"mawReinitSetupMutex"}),d("MAWIssueNewUserVisibleErrors").deleteUserVisibleErrorByJSErrorMessage("maw_appmutex_no_permission")}catch(a){m.catching(a).mustfix("AppMutex - failed to reinitialize worker"),c("QPLUserFlow").endFailure(c("qpl")._(25310984,"169"),"failed_to_reinitialize_worker",{annotations:{string:{error:a.message.concat(": ",a.error)}},error:a,instanceKey:o})}finally{n=!1}}),[u,s,e,t,f,g,i,q]);k(function(){window.addEventListener("storage",v);var a=d("Run").onBeforeUnload(w,!1),b=d("Run").onUnload(w);window.addEventListener("visibilitychange",x);return function(){window.removeEventListener("storage",v),a.remove(),b.remove(),window.removeEventListener("visibilitychange",x)}},[v,w,x])}g.useMutex=a}),98);
__d("MAWSetupMutex.react",["MAWSetupMutex"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=a.dispatch;a.environment;a.fbId;a=a.platformSetupUtils;d("MAWSetupMutex").useMutex(a,b);return null}g["default"]=a}),98);
__d("LSGetThreadDataFromTam.nop",["I64","LSMessagingThreadTypeUtil","LSResult","LSShape","LSVec","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){b=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.threads).filter(function(a){a=a.threadType;return d("LSMessagingThreadTypeUtil").isArmadilloSecure(a)}).take((h||(h=d("I64"))).to_int32(e))));a=b.map(function(a,b){var c=a.lastActivityTimestampMs,e=a.lastReadWatermarkTimestampMs,f=a.threadKey;a=a.threadType;return d("LSShape").ofRecord({client_thread_pk:f,display_timestamp_ms:c,is_group_thread:d("LSMessagingThreadTypeUtil").isGroup(a),last_activity_timestamp_ms:c,last_read_watermark_timestamp_ms:e,server_thread_key:f,sort_order:(h||(h=d("I64"))).of_int32(b)})});return c("LSResult")(c("LSVec").ofArray(a))});function e(b,c,d){return a.apply(this,arguments)}return e}();a.__nop_name__="LSGetThreadDataFromTam";g["default"]=a}),98);
__d("LSScheduleOccamThreadsIntegrityChecker",["LSGetCursor","LSGetThreadDataFromTam.nop","LSIssueNewTask"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.sequence([function(a){return c.storedProcedure(b("LSGetCursor"),c.i64.cast([0,95])).then(function(a){return a=a,d[0]=a[0],a})},function(b){return d[1]=c.createArray(),d[2]=c.createArray(),c.i64.neq(a[1],void 0)?d[3]=a[1]:d[3]=c.i64.cast([0,20]),c.forEach(c.db.table(220).fetch([[[c.i64.cast([0,95]),c.i64.cast([0,0])]]]),function(a){a=a.item;return d[10]=a.minLastActivityTimestampMs,d[9]=new c.Map(),d[9].set("has_more_before",a.hasMoreBefore),d[9].set("is_loading_before",a.isLoadingBefore),d[9].set("min_last_activity_timestamp_ms",d[10]),d[9].set("min_thread_key",a.minThreadKey),d[9].set("parent_thread_key",c.i64.cast([0,0])),d[11]=(d[2].push(d[9]),d[2]),c.forEach(c.islc(c.filter(c.db.table(9).fetch([[[{gte:d[10]}]],"lastActivityTimestampMs"]),function(a){return c.i64.ge(a.lastActivityTimestampMs,d[10])&&c.i64.eq(a.syncGroup,c.i64.cast([0,95]))&&c.i64.eq(a.authorityLevel,c.i64.cast([0,80]))}),0,c.i64.to_float(d[3])),function(a){a=a.item;return d[11]=new c.Map(),d[11].set("authority_level",a.authorityLevel),d[11].set("folder_name",a.folderName),d[11].set("last_activity_timestamp_ms",a.lastActivityTimestampMs),d[11].set("last_read_watermark_timestamp_ms",a.lastReadWatermarkTimestampMs),d[11].set("thread_key",a.threadKey),d[12]=(d[1].push(d[11]),d[1])})})},function(a){return d[4]=c.createArray(),c.nativeOperation(b("LSGetThreadDataFromTam.nop"),d[3]).then(function(a){return a=a,d[5]=a[0],a})},function(a){return d[6]=c.createArray(),d[7]=new c.Map(),d[7].set("cursor",d[0]),d[7].set("mi_act_mapping_columns_for_integrity_check",d[6]),d[7].set("occam_threads_columns_for_integrity_check",d[1]),d[7].set("threads_ranges_columns_for_integrity_check",d[2]),d[7].set("tam_threads_columns_for_integrity_check",d[5]),d[8]=c.toJSON(d[7]),c.storedProcedure(b("LSIssueNewTask"),"occam-threads-integrity-check",c.i64.cast([0,701]),d[8],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))}])},function(a){return c.resolve(e)}])}a.__sproc_name__="LSE2EEMessagingMetadataMailboxScheduleOccamThreadsIntegrityCheckerStoredProcedure";a.__tables__=["sync_group_threads_ranges","threads"];e.exports=a}),null);
__d("LSScheduleOccamThreadsIntegrityCheckerStoredProcedure",["LSScheduleOccamThreadsIntegrityChecker","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){e===void 0&&(e={});a=a.storedProcedure(c("LSScheduleOccamThreadsIntegrityChecker"),e.delayMs,e.maxThreadCount);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("MWChatFetchThreadMessageCounts",["asyncToGeneratorRuntime"],(function(a,b,c,d,e,f){"use strict";function a(a){return g.apply(this,arguments)}function g(){g=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){return{}});return g.apply(this,arguments)}function c(a,b){return h.apply(this,arguments)}function h(){h=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){return 0});return h.apply(this,arguments)}f.fetchArmadilloThreadMessageCounts=a;f.fetchArmadilloThreadMessageCount=c}),66);
__d("globalCryptoSubtle",[],(function(a,b,c,d,e,f){"use strict";b=a.crypto.subtle;c=b;f["default"]=c}),66);
__d("useDataLossStatisticsStorageListener",["LSDatabaseSingleton","MWChatFetchThreadMessageCounts","Visibility","WebStorage","promiseDone","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(i||d("react")).useEffect,l="armadillo_msgr_data_loss_stats";function a(){k(function(){var a;window.setTimeout(function(){a=c("Visibility").addListener(c("Visibility").HIDDEN,function(){c("promiseDone")((j||(j=d("LSDatabaseSingleton"))).LSDatabaseSingleton.then(function(a){return d("MWChatFetchThreadMessageCounts").fetchArmadilloThreadMessageCounts(a).then(function(a){(h||(h=c("WebStorage"))).setItemGuarded(h.getLocalStorage(),l,JSON.stringify(a))})}))})},3e4);return function(){var b;(b=a)==null?void 0:b.remove()}},[])}g.DATA_LOSS_STATISTICS_STORAGE_KEY=l;g.useDataLossStatisticsStorageListener=a}),98);
__d("MWChatDataLossCheckerUtil",["FBLogger","I64","MWChatFetchThreadMessageCounts","Promise","QuickPerformanceLogger","WebStorage","asyncToGeneratorRuntime","gkx","globalCryptoSubtle","promiseDone","qpl","useDataLossStatisticsStorageListener"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k;function l(a){a=Array.from(new Uint8Array(a));return a.map(function(a){return a.toString(16).padStart(2,"0")}).join("")}function m(a){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){try{if(c("gkx")("24059"))return a;var b=new TextEncoder();b=b.encode(a).buffer;a=(yield c("globalCryptoSubtle").digest("SHA-256",b));return l(a)}catch(a){return a}});return n.apply(this,arguments)}function o(a){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=a.currentIDBCnt,d=a.currentMsgCnt,e=a.logThreadId,f=a.prevMsgCnt,g=a.qplEvent;a=a.threadId;if(e!=null&&e){e=(yield m(a));b!=null?((j||(j=c("QuickPerformanceLogger"))).markerStartFromNavStart(g),j.markerAnnotate(g,{"int":{current_idb_count:b,current_msg_count:d,prev_msg_count:f},string:{encrypted_threadId:e}}),j.markerEnd(g,2)):((j||(j=c("QuickPerformanceLogger"))).markerStartFromNavStart(g),j.markerAnnotate(g,{"int":{current_msg_count:d,prev_msg_count:f},string:{encrypted_threadId:e}}),j.markerEnd(g,2))}else b!=null?((j||(j=c("QuickPerformanceLogger"))).markerStartFromNavStart(g),j.markerAnnotate(g,{"int":{current_idb_count:b,current_msg_count:d,prev_msg_count:f}}),j.markerEnd(g,2)):((j||(j=c("QuickPerformanceLogger"))).markerStartFromNavStart(g),j.markerAnnotate(g,{"int":{current_msg_count:d,prev_msg_count:f}}),j.markerEnd(g,2))});return p.apply(this,arguments)}function q(a){var b=a.currentMsgCnt,c=a.logThreadId,d=a.prevMsgCnt,e=a.qplEvent;a=a.threadId;return o({currentMsgCnt:b,logThreadId:c,prevMsgCnt:d,qplEvent:e,threadId:a})}function a(a,b){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){try{a=(yield d("MWChatFetchThreadMessageCounts").fetchArmadilloThreadMessageCount(a,b));var e=(k||(k=c("WebStorage"))).getAllowlistedKeyFromLocalStorage(d("useDataLossStatisticsStorageListener").DATA_LOSS_STATISTICS_STORAGE_KEY);if(e==null)return{isThreadMissingMessages:!1};e=JSON.parse(e);b=(e=e[(i||(i=d("I64"))).to_string(b)])!=null?e:null;if(b==null)return{isThreadMissingMessages:!1};return b>0&&a===0?{currCount:a,isThreadMissingMessages:!0,prevCount:b}:{isThreadMissingMessages:!1}}catch(a){c("FBLogger")("labyrinth").catching(a).warn("Failed to check for data loss");return{isThreadMissingMessages:!1}}});return r.apply(this,arguments)}function e(a){a=d("MWChatFetchThreadMessageCounts").fetchArmadilloThreadMessageCounts(a);a.then(function(a){var e=(k||(k=c("WebStorage"))).getAllowlistedKeyFromLocalStorage(d("useDataLossStatisticsStorageListener").DATA_LOSS_STATISTICS_STORAGE_KEY);if(e==null)return(h||(h=b("Promise"))).resolve(null);e=JSON.parse(e);return(h||(h=b("Promise"))).resolve({current:a,previous:e})}).then(function(a){if(a==null)return(h||(h=b("Promise"))).resolve();var c=a.current;a=a.previous;return s(a,c)})["catch"](function(a){return c("FBLogger")("labyrinth").catching(a).warn("Failed to check for data loss")});return a.then(function(){})}function s(a,b){return t.apply(this,arguments)}function t(){t=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,d){var e=[];for(var f in a)Object.prototype.hasOwnProperty.call(d,f)&&e.push(f);f=[];for(var g=0;g<e.length;g++){var i=e[g],j={currentMsgCnt:d[i],prevMsgCnt:a[i],threadId:i};a[i]>0&&d[i]===0?(f.push(q(babelHelpers["extends"]({},j,{qplEvent:c("qpl")._(717553665,"1183")}))),f.push(q(babelHelpers["extends"]({},j,{logThreadId:!0,qplEvent:c("qpl")._(717562243,"1358")})))):(f.push(q(babelHelpers["extends"]({},j,{qplEvent:c("qpl")._(717553666,"1184")}))),f.push(q(babelHelpers["extends"]({},j,{logThreadId:!0,qplEvent:c("qpl")._(717556541,"1361")}))))}yield (h||(h=b("Promise"))).all(f)});return t.apply(this,arguments)}function f(a){var b=function(b){c("promiseDone")(m(b).then(function(d){(j||(j=c("QuickPerformanceLogger"))).markerStartFromNavStart(c("qpl")._(717567419,"1383")),j.markerAnnotate(c("qpl")._(717567419,"1383"),{string:{encryptedThreadId:d,msg_count:a[b].toString()}}),j.markerEnd(c("qpl")._(717567419,"1383"),2)}))};for(var d in a)b(d)}g.qplLog=q;g.checkIsThreadMissingMessages=a;g.fetchCountsAndLogDataLoss=e;g.logThreadCompleteDataLoss=s;g.logLocalStatistics=f}),98);
__d("useOnMawSetupComplete.react",["emptyFunction","promiseDone","react","waitForMAWMIC"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(a,b,e){b===void 0&&(b=c("emptyFunction")),i(function(){var e=!1;c("promiseDone")(d("waitForMAWMIC").promise.then(function(){if(e)return;a()})["catch"](function(){if(e)return;b()}));return function(){e=!0}},e)}g.useOnMawSetupComplete=a}),98);
__d("useDataLossChecker",["FBLogger","I64","LSDatabaseSingleton","LSFactory","LSScheduleOccamThreadsIntegrityCheckerStoredProcedure","MWChatDataLossCheckerUtil","asyncToGeneratorRuntime","gkx","justknobx","promiseDone","useDataLossStatisticsStorageListener","useOnMawSetupComplete.react"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(){d("useDataLossStatisticsStorageListener").useDataLossStatisticsStorageListener(),d("useOnMawSetupComplete.react").useOnMawSetupComplete(function(){c("promiseDone")(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield (h||(h=d("LSDatabaseSingleton"))).LSDatabaseSingleton);yield d("MWChatDataLossCheckerUtil").fetchCountsAndLogDataLoss(a)})()),j()},j(),[])}function j(){try{var a=c("gkx")("24061"),e=c("justknobx")._("1683");a&&e&&c("promiseDone")(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield (h||(h=d("LSDatabaseSingleton"))).LSDatabaseSingleton);return a.runInTransaction(function(a){return c("LSScheduleOccamThreadsIntegrityCheckerStoredProcedure")(c("LSFactory")(a),{delayMs:(i||(i=d("I64"))).of_int32(6e4),maxThreadCount:i.of_int32(20)})},"readonly",void 0,void 0,f.id+":61")})())}catch(a){c("FBLogger")("labyrinth_web").warn("Error running thread integrity checker",a)}}g["default"]=a}),98);
__d("useLegacyUserWatchdog",["MAWIssueNewUserVisibleErrors","clearTimeout","react","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;b=24*60*60*1e3;var j=5*b;function a(){i(function(){var a=c("setTimeout")(function(){var a;(a=d("MAWIssueNewUserVisibleErrors")).issueError(a.MAWLegacyUserErrorMsgBody,a.MAWLegacyUserErrorMsgTitle,a.MAWUserVisibleErrorPresentationType.BANNER)},j);return function(){return c("clearTimeout")(a)}},void 0)}g["default"]=a}),98);
__d("useMAWReregisterDeviceOnError",["LSPersistedDbGating","MAWIssueNewUserVisibleErrors","MAWLoggerUtils","MAWReinitWorker","MAWSetupWorker","MAWStateContext.react","MAWWaitForBackendSetup","ODS","WABridgedAPI","WATagsLogger","asyncToGeneratorRuntime","promiseDone","react","useAsyncReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["Unable to reinit worker ",""]);j=function(){return a};return a}e=h||d("react");var k=e.useCallback,l=e.useContext,m=!1,n=typeof window!=="undefined"?window:self;function o(){m=!0,c("promiseDone")(d("MAWSetupWorker").terminateWorkerPermanently(),function(){(i||(i=d("ODS"))).bumpEntityKey(3185,"armadillo_forced_reregister_fail",n.location.hostname),d("MAWIssueNewUserVisibleErrors").issueError(d("MAWIssueNewUserVisibleErrors").MAWLegacyUserErrorMsgBody,d("MAWIssueNewUserVisibleErrors").MAWLegacyUserErrorMsgTitle,d("MAWIssueNewUserVisibleErrors").MAWUserVisibleErrorPresentationType.BANNER)})}function a(a){var e=a.rotateCryptoAuthToken,f=a.setupBridge,g=c("useAsyncReStore")(),h=d("MAWStateContext.react").useDispatchClearEphemeralSettings(),p=l(d("MAWStateContext.react").MAWDispatchContext);return k(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){if(m)return;(i||(i=d("ODS"))).bumpEntityKey(3185,"armadillo_forced_deregister",n.location.hostname);yield c("WABridgedAPI").removeCurrentDevice();yield d("MAWSetupWorker").terminateWorker("useMAWReregisterDeviceOnError");d("MAWWaitForBackendSetup").resetBackendSetup();try{var a=(yield g);yield c("MAWReinitWorker")({allTablesPersisted:d("LSPersistedDbGating").allTablesPersisted,db:a,dispatch:p,dispatchClearEphemeralSettings:h,logout:o,reregisterDevice:o,rotateCryptoAuthToken:e,setupBridge:f,workerCreateReason:"mawReinitReregDevice"})}catch(b){d("MAWWaitForBackendSetup").rejectBackendSetup(b);a=b.description||b.message||b;d("WATagsLogger").TAGS([d("MAWLoggerUtils").Tag.DeviceRegistration,d("MAWLoggerUtils").Tag.LogoutHandler,d("MAWLoggerUtils").Tag.WorkerSetup,d("MAWLoggerUtils").Tag.MAWWorker,d("MAWLoggerUtils").Tag.MAWInit]).ERROR(j(),a);d("MAWIssueNewUserVisibleErrors").issueError(d("MAWIssueNewUserVisibleErrors").MAWLegacyUserErrorMsgBody,d("MAWIssueNewUserVisibleErrors").MAWLegacyUserErrorMsgTitle,d("MAWIssueNewUserVisibleErrors").MAWUserVisibleErrorPresentationType.BANNER)}}),[g,p,h,e,f])}g["default"]=a}),98);
__d("useStorageUsageMetricsLogger",["cr:20551","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(){i(function(){void (b("cr:20551")==null?void 0:b("cr:20551").logMAWDBUsageMetrics())},[])}g["default"]=a}),98);
__d("MAWSetupImpl.react",["BaseHeadingContext","CometRelay","MAWCommonSetup","MAWCryptoAuthToken","MAWSetupMutex.react","MAWStateContext.react","cr:6159","cr:6160","cr:8800","promiseDone","react","shouldUseMAWSharedWorker","useDataLossChecker","useLegacyUserWatchdog","useMAWReregisterDeviceOnError","useStorageUsageMetricsLogger"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));e=h;var j=e.useCallback,k=e.useContext,l=e.useMemo;window.XPlatTesting=b("cr:6159");window.XPlatTestingGroups=b("cr:6160");window.XPlatMockServer=b("cr:8800");function a(a){var b=a.children,e=a.fbid,f=a.logout,g=a.setupBridge;a=k(d("MAWStateContext.react").MAWDispatchContext);var h=d("CometRelay").useRelayEnvironment(),m=j(function(){return d("MAWCryptoAuthToken").fetchFreshCAT(h)},[h]),n=c("useMAWReregisterDeviceOnError")({rotateCryptoAuthToken:m,setupBridge:g}),o=l(function(){return{logout:f!=null?f:function(){return c("promiseDone")(n())},reregisterDevice:function(){return c("promiseDone")(n())},rotateCryptoAuthToken:m,setupBridge:g}},[m,f,n,g]);d("MAWCommonSetup").useArmadillo(o);c("useLegacyUserWatchdog")();c("useDataLossChecker")();c("useStorageUsageMetricsLogger")();var p=k(c("BaseHeadingContext")),q=!d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker();return i.jsxs(c("BaseHeadingContext").Provider,{value:p+1,children:[b,q?i.jsx(c("MAWSetupMutex.react"),{dispatch:a,environment:h,fbId:e,platformSetupUtils:o}):null]})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("MAWBridgeInitOfflineQueueSyncStartHandler",["requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h=c("requireDeferred")("MessengerLogHistory").__setRef("MAWBridgeInitOfflineQueueSyncStartHandler");function a(){h.onReady(function(a){a=a.getInstance("maw_setup");a.debug("Init Offline Queue Sync Start")})}g.call=a}),98);
__d("MAWDeviceChangeState-resolver",[],(function(a,b,c,d,e,f){"use strict";var g=0,h=[],i={getDeviceAlertCount:function(){return g},subscribe:function(a){h.push(a);return function(){h=h.filter(function(b){return b!==a})}}};function a(a){g!==a&&(g=a,h.forEach(function(a){return a()}))}function b(){return{read:function(){return i.getDeviceAlertCount()},subscribe:function(a){return i.subscribe(a)}}}f.updateDeviceCount=a;f.deviceAlertCount=b}),66);
__d("MAWMediaDownloadStatusReducersUtils",["FBLogger","MAWGetIsMediaDownloadStatusEnabled","MAWMediaDownloadStatus","isEmpty","nullthrows"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){b=b.reduce(function(b,d){if(d.plaintextHash==null){c("FBLogger")("messenger_web_media").mustfix("[NewMedia] No valid media plaintextHash found in reducer %s",d.mediaId);return b}var e=c("nullthrows")(d.plaintextHash).toString();if(a.mediaDownloadStatus[e]!=null&&a.mediaDownloadStatus[e].mainMediaStatus!==c("MAWMediaDownloadStatus").MISSING_FILE)return b;var f=d.hasPreviewMedia!=null;b[e]={mainMediaStatus:d.hasMedia?c("MAWMediaDownloadStatus").SUCCESS:c("MAWMediaDownloadStatus").MISSING_FILE,mainMediaStatusDetails:"initial_media_download",previewMediaStatus:f?d.hasPreviewMedia===!0?c("MAWMediaDownloadStatus").SUCCESS:c("MAWMediaDownloadStatus").MISSING_FILE:void 0,previewMediaStatusDetails:f?"initial_media_download":void 0};return b},{});return(h||(h=c("isEmpty")))(b)?a:babelHelpers["extends"]({},a,{mediaDownloadStatus:babelHelpers["extends"]({},a.mediaDownloadStatus,{},b)})}function b(a,b){if(!d("MAWGetIsMediaDownloadStatusEnabled").getIsMediaDownloadStatusForXMAsEnabled())return a;b=b.reduce(function(b,d){if(d.faviconPlaintextHash!=null){var e;b[d.faviconPlaintextHash]=babelHelpers["extends"]({},a.mediaDownloadStatus[d.faviconPlaintextHash],{mainMediaStatus:d.hasXmaFaviconMedia===!0?c("MAWMediaDownloadStatus").SUCCESS:(e=(e=a.mediaDownloadStatus[d.faviconPlaintextHash])==null?void 0:e.mainMediaStatus)!=null?e:c("MAWMediaDownloadStatus").MISSING_FILE,mainMediaStatusDetails:"initial_xma_favicon_status"})}if(d.headerMediaPlaintextHash!=null){b[d.headerMediaPlaintextHash]=babelHelpers["extends"]({},a.mediaDownloadStatus[d.headerMediaPlaintextHash],{mainMediaStatus:d.hasXmaHeaderMedia===!0?c("MAWMediaDownloadStatus").SUCCESS:(e=(e=a.mediaDownloadStatus[d.headerMediaPlaintextHash])==null?void 0:e.mainMediaStatus)!=null?e:c("MAWMediaDownloadStatus").MISSING_FILE,mainMediaStatusDetails:"initial_xma_header_status"})}if(d.defaultPreviewMediaPlaintextHash==null)return b;e=c("nullthrows")(d.defaultPreviewMediaPlaintextHash).toString();if(a.mediaDownloadStatus[e]!=null&&a.mediaDownloadStatus[e].mainMediaStatus!==c("MAWMediaDownloadStatus").MISSING_FILE)return b;b[e]=babelHelpers["extends"]({},a.mediaDownloadStatus[e],{mainMediaStatus:d.hasMedia===!0?c("MAWMediaDownloadStatus").SUCCESS:c("MAWMediaDownloadStatus").MISSING_FILE,mainMediaStatusDetails:"initial_media_download"});return b},{});return(h||(h=c("isEmpty")))(b)?a:babelHelpers["extends"]({},a,{mediaDownloadStatus:babelHelpers["extends"]({},a.mediaDownloadStatus,{},b)})}g.handleNewMedias=a;g.handleNewXMAs=b}),98);
__d("MAWOfflineQueueThreadStatus",[],(function(a,b,c,d,e,f){"use strict";var g=function(a,b){return Object.entries(a).filter(function(a){var c,d=a[0];a=a[1];return((c=b[d])==null?void 0:c.chatStatus)!==a.chatStatus||((c=b[d])==null?void 0:c.snippetStatus)!==a.snippetStatus})},h=function(a,b){return a.reduce(function(a,b){var c=b[0];b=b[1];a[c]={chatStatus:b.chatStatus,snippetStatus:b.snippetStatus};return a},b)};function a(a,b){a=a!=null?g(a,b):null;a=a==null||a.length===0?b:h(a,babelHelpers["extends"]({},b));return a}f.calculateOfflineQueueThreadStatus=a}),66);
__d("MAWStateContextProvider.react",["MAWBridgeInitOfflineQueueSyncCompleteHandler","MAWBridgeInitOfflineQueueSyncStartHandler","MAWDeviceChangeState-resolver","MAWMediaDownloadStatus","MAWMediaDownloadStatusReducersUtils","MAWOfflineQueueThreadStatus","MAWSharedProtocolQueueConst","MAWStateContext.react","cr:19624","gkx","react","useUnsafeRef_DEPRECATED"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));e=i;var k=e.useCallback,l=e.useEffect,m=e.useMemo,n=e.useReducer,o=e.useTransition,p=(e=c("gkx")("12254"))!=null?e:!1,q=(e=c("gkx")("12254"))!=null?e:!1,r=(e=c("gkx")("12254"))!=null?e:!1,s=(e=c("gkx")("12254"))!=null?e:!1,t=function(a,b){switch(b.tag){case"OptimisticSendMessage":var e;return p?a:babelHelpers["extends"]({},a,{messageLatencies:babelHelpers["extends"]({},a.messageLatencies,(e={},e[b.optimisticMsgId]={sent:b.timestamp},e))});case"NewMsg":case"MsgUpdated":var f,g;if(p)return a;e=b.value.ack;var h=b.value.msgId;f=(f=b.value.offlineMsg)==null?void 0:f.msgId;f!=null&&a.messageLatencies[f]!=null&&(a.messageLatencies[h]=a.messageLatencies[f],delete a.messageLatencies[f]);f=a.messageLatencies[h];if((f==null?void 0:f.sent)!=null&&(f==null?void 0:f.acked)!=null)return a;f=(f==null?void 0:f.sent)||Date.now();e=e!==0?Date.now():void 0;return babelHelpers["extends"]({},a,{messageLatencies:babelHelpers["extends"]({},a.messageLatencies,(g={},g[h]={acked:e,sent:f},g))});case"InitOfflineQueueSyncProgress":h=b.value;return babelHelpers["extends"]({},a,{offlineQueueProgressDownloaded:h});case"InitOfflineQueueConsumerSyncProgress":e=b.value;f=e.chatJidStatus;g=e.processed;h=a.offlineQueueThreadStatus||{};e=d("MAWOfflineQueueThreadStatus").calculateOfflineQueueThreadStatus(f,h);return babelHelpers["extends"]({},a,{offlineQueueProgressProcessed:g,offlineQueueThreadStatus:e});case"InitOfflineQueueSyncStart":d("MAWBridgeInitOfflineQueueSyncStartHandler").call();f=b.value;return babelHelpers["extends"]({},a,{offlineQueueCount:f});case"InitOfflineQueueSyncComplete":d("MAWBridgeInitOfflineQueueSyncCompleteHandler").call();h=b.value;g=a.offlineQueueThreadStatus||{};e=d("MAWOfflineQueueThreadStatus").calculateOfflineQueueThreadStatus(h,g);return babelHelpers["extends"]({},a,{offlineQueueSyncState:d("MAWSharedProtocolQueueConst").OfflineConsumerStatus.Complete,offlineQueueThreadStatus:e});case"UnArchivedSelfDeviceChangeAlerts":f=b.value;return babelHelpers["extends"]({},a,{unArchivedSelfDeviceChangeAlerts:f});case"EphemeralSettingsUpdatedForUI":if(r)return a;h=b.value;g=h.chatJid;e=h.ephemeralSettingArgs;f=e.ephemeralExpirationInSec;h=e.ephemeralLastUpdatedOrSetTimestamp;e=g;return babelHelpers["extends"]({},a,{ephemeralSettings:babelHelpers["extends"]({},a.ephemeralSettings,(g={},g[e]={ephemeralExpirationInSec:f,ephemeralLastUpdatedOrSetTimestamp:h},g))});case"ClearEphemeralSettings":return r?a:babelHelpers["extends"]({},a,{ephemeralSettings:{}});case"UpdateMediaStatus":var i,j,k,l,m;e=b.value;f=e.details;h=e.key;g=e.status;var n=e.type;e=e.validationResult;h=h.toString();i=(i=a.mediaDownloadStatus[h])==null?void 0:i.mainMediaStatus;j=(j=a.mediaDownloadStatus[h])==null?void 0:j.mainMediaStatusDetails;k=(k=a.mediaDownloadStatus[h])==null?void 0:k.previewMediaStatus;l=(l=a.mediaDownloadStatus[h])==null?void 0:l.previewMediaStatusDetails;m=(m=a.mediaDownloadStatus[h])==null?void 0:m.validationResult;var o=n==="main"?i===c("MAWMediaDownloadStatus").SUCCESS:k===c("MAWMediaDownloadStatus").SUCCESS;return g===c("MAWMediaDownloadStatus").DOWNLOADING&&o?a:babelHelpers["extends"]({},a,{mediaDownloadStatus:babelHelpers["extends"]({},a.mediaDownloadStatus,(o={},o[h]={mainMediaStatus:n==="main"?g:i,mainMediaStatusDetails:n==="main"?f:j,previewMediaStatus:n==="preview"?g:k,previewMediaStatusDetails:n==="preview"?f:l,validationResult:e!=null?e:m},o))});case"SetMediaDownloadStatusState":h=b.value;i=h.key;j=h.newMediaDownloadState;g=i.toString();return(j==null?void 0:j.mainMediaStatus)===c("MAWMediaDownloadStatus").MISSING_FILE&&((k=a.mediaDownloadStatus[g])==null?void 0:k.mainMediaStatus)!=null?a:babelHelpers["extends"]({},a,{mediaDownloadStatus:babelHelpers["extends"]({},a.mediaDownloadStatus,(n={},n[g]=j,n))});case"IncreaseMediaDownloadRetryCount":f=b.value.key;l=f.toString();return babelHelpers["extends"]({},a,{mediaDownloadRetryCount:babelHelpers["extends"]({},a.mediaDownloadRetryCount,(e={},e[l]=((m=a.mediaDownloadRetryCount[l])!=null?m:0)+1,e))});case"NewMedia":return c("gkx")("11968")===!0?a:d("MAWMediaDownloadStatusReducersUtils").handleNewMedias(a,[b.value]);case"NewMedias":return d("MAWMediaDownloadStatusReducersUtils").handleNewMedias(a,b.value);case"SetMediaValidatedResult":o=b.value;h=o.key;i=o.validatedResult;k=h.toString();g=babelHelpers["extends"]({},a.mediaDownloadStatus[k],{validationResult:i});return babelHelpers["extends"]({},a,{mediaDownloadStatus:babelHelpers["extends"]({},a.mediaDownloadStatus,(j={},j[k]=g,j))});case"NewXMA":return d("MAWMediaDownloadStatusReducersUtils").handleNewXMAs(a,[b.value]);case"NewXMAs":return d("MAWMediaDownloadStatusReducersUtils").handleNewXMAs(a,b.value);case"UpdateMediaStatus_FOR_DEBUG_ONLY":n=b.value;f=n.details;l=n.key;m=n.status;e=l.toString();return babelHelpers["extends"]({},a,{mediaDownloadStatus:babelHelpers["extends"]({},a.mediaDownloadStatus,(o={},o[e]={mainMediaStatus:m,mainMediaStatusDetails:f,previewMediaStatus:m,previewMediaStatusDetails:f},o))});case"SetIsMAWDBinUILoaded":return q?a:babelHelpers["extends"]({},a,{isMAWDBinUILoaded:!0});case"UpdateEBState":return babelHelpers["extends"]({},a,{ebState:b.value.newState});case"UpdateWorkerState":return babelHelpers["extends"]({},a,{workerState:b.value.newState})}return a},u=function(a){if(b("cr:19624")==null)return!1;if(a.tag==="InitOfflineQueueSyncProgress"){var c=a.value;b("cr:19624").initOfflineQueueSyncProgress(c);return!0}if(a.tag==="InitOfflineQueueConsumerSyncProgress"){c=a.value;var e=c.chatJidStatus;c=c.processed;b("cr:19624").initOfflineQueueConsumerSyncProgress(c,e);return!0}if(a.tag==="InitOfflineQueueSyncStart"){d("MAWBridgeInitOfflineQueueSyncStartHandler").call();c=a.value;b("cr:19624").initOfflineQueueSyncStart(c);return!0}if(a.tag==="InitOfflineQueueSyncComplete"){d("MAWBridgeInitOfflineQueueSyncCompleteHandler").call();e=a.value;b("cr:19624").initOfflineQueueSyncComplete(e);return!0}return!1},v=function(a){if(!s)return!1;if(a.tag==="UnArchivedSelfDeviceChangeAlerts"){a=a.value;d("MAWDeviceChangeState-resolver").updateDeviceCount(a);return!0}return!1};function a(a){var b;a=a.children;var c=n(t,(b=d("MAWStateContext.react")).initialState),e=c[0],f=c[1];c=o();c[0];var g=c[1];c=k(function(a){var b=u(a);if(!b){b=v(a);b||g(function(){return f(a)})}},[]);var h=m(function(){return new Set()},[]),i=m(function(){return{state:e,subscriptions:h}},[e,h]),p=w(i),q=k(function(){return p.current},[p]);l(function(){h==null?void 0:h.forEach(function(a){return a(e)})},[e,h]);return j.jsx(b.MAWDispatchContext.Provider,{value:c,children:j.jsx(b.MAWStateContext.Provider,{value:i,children:j.jsx(b.MAWNonRerenderingStateContext.Provider,{value:q,children:a})})})}a.displayName=a.name+" [from "+f.id+"]";function w(a){var b=(h||(h=c("useUnsafeRef_DEPRECATED")))(a);b.current=a;return b}g.MAWStateContextProvider=a}),98);
__d("IGDAWSetup.react",["FBLogger","IGDSetIGE2EEEligibility","IGDSetupBridge","MAWCurrentUser","MAWSetupImpl.react","MAWStateContextProvider.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));h.useCallback;function a(a){var b=d("react-compiler-runtime").c(4);a=a.children;var e=j;d("IGDSetIGE2EEEligibility").useUpsertSetIGE2EEEligibilityOnMount();var f;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(f={className:"xjp7ctv"},b[0]=f):f=b[0];var g;b[1]===Symbol["for"]("react.memo_cache_sentinel")?(g=d("MAWCurrentUser").getID(),b[1]=g):g=b[1];b[2]!==a?(f=i.jsx("div",babelHelpers["extends"]({},f,{"data-testid":void 0,children:i.jsx(d("MAWStateContextProvider.react").MAWStateContextProvider,{children:i.jsx(c("MAWSetupImpl.react"),{fbid:g,logout:e,setupBridge:d("IGDSetupBridge").setupBridge,children:a})})})),b[2]=a,b[3]=f):f=b[3];return f}function j(){}g["default"]=a}),98);
__d("LSIssueAttachmentBackupTask",["LSArrayGetObjectAt","LSBase64Encode.nop","LSGetBlobFromString.nop","LSHmac_v2.nop","LSIsEncryptionVersionSecure","LSIssueNewTask","LSLogMebClientEvent.nop","LSOcmfClientMap.nop"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.sequence([function(e){return d[0]=c.i64.of_float(Date.now()),function(a){c.logger(a).info(a)}("[EncryptedBackups][LSEncryptedBackupsIssueAttachmentBackupTaskStoredProcedure] Beginning execution."),a[5]!==void 0?d[1]=a[5]:(d[27]=c.i64.random(),d[1]=c.i64.to_string(d[27])),c.islc(c.filter(c.db.table(168).fetch(),function(a){return c.i64.eq(a.authorityLevel,c.i64.cast([0,80]))}),0,c.i64.to_float(c.i64.cast([0,1]))).next().then(function(a,e){var f=a.done;a=a.value;return f?(f=[void 0,void 0,void 0,void 0,void 0,void 0,void 0,c.i64.cast([0,1]),void 0,void 0,void 0,void 0,void 0,void 0,void 0,c.i64.cast([0,20]),void 0,void 0],d[2]=f[0],d[3]=f[1],d[4]=f[2],d[5]=f[3],d[6]=f[4],d[7]=f[5],d[8]=f[6],d[9]=f[7],d[10]=f[8],d[11]=f[9],d[12]=f[10],d[13]=f[11],d[14]=f[12],d[15]=f[13],d[16]=f[14],d[17]=f[15],d[18]=f[16],d[19]=f[17],f):(e=a.item,c.sequence([function(a){return d[32]=e.backupTenancy,d[31]=e.encryptionVersion,d[27]=void 0,c.i64.neq(d[27],void 0)?c.resolve(d[28]=d[27]):c.sequence([function(a){return d[33]=c.createArray(),c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,0])).then(function(a){return a=a,d[34]=a[0],a})},function(a){return d[34]?d[43]=(d[33].push(c.i64.cast([0,0])),d[33]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,2])).then(function(a){return a=a,d[35]=a[0],a})},function(a){return d[35]?d[43]=(d[33].push(c.i64.cast([0,2])),d[33]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,3])).then(function(a){return a=a,d[36]=a[0],a})},function(a){return d[36]?d[43]=(d[33].push(c.i64.cast([0,3])),d[33]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,4])).then(function(a){return a=a,d[37]=a[0],a})},function(a){return d[37]?d[43]=(d[33].push(c.i64.cast([0,4])),d[33]):0,d[38]=c.createArray(),d[39]=c.i64.of_int32(d[33].length),c.i64.gt(d[39],c.i64.cast([0,0]))?c.loopAsync(d[39],function(a){return d[43]=a,c.sequence([function(a){return c.nativeTypeOperation("Array",b("LSArrayGetObjectAt"),d[33],d[43]).then(function(a){return a=a,d[44]=a[0],d[45]=a[1],a})},function(a){return d[46]=(d[38].push(d[44]),d[38])}])}):c.resolve()},function(a){return c.sequence([function(a){return d[43]=c.createArray(),d[44]=c.i64.of_int32(d[38].length),c.i64.gt(d[44],c.i64.cast([0,0]))?c.loopAsync(d[44],function(a){return d[46]=a,c.sequence([function(a){return c.nativeTypeOperation("Array",b("LSArrayGetObjectAt"),d[38],d[46]).then(function(a){return a=a,d[47]=a[0],d[48]=a[1],a})},function(a){return d[49]=(d[43].push(c.i64.to_string(d[47])),d[43])}])}):c.resolve()},function(a){return d[45]=d[43].join(","),d[40]=d[45]}])},function(a){return d[38].some(function(a){return c.i64.eq(d[31],a)})?d[41]=d[31]:d[41]=void 0,c.i64.neq(d[41],void 0)?d[42]=d[41]:d[42]=void 0,d[28]=d[42]}])},function(a){return c.i64.neq(d[28],void 0)?d[29]=d[28]:d[29]=c.i64.cast([0,0]),c.i64.neq(d[32],void 0)?d[30]=d[32]:d[30]=c.i64.cast([0,1]),a=[e.backupId,e.deviceId,e.mailboxRootKeyBlob,e.ocmfClientStateBlob,void 0,void 0,d[29],d[30],e.epochAuthPublicKeyBlob,e.epochAuthPrivateKeyBlob,e.devicePublicKeyBlob,e.devicePrivateKeyBlob,e.epochStoragePublicKeyBlob,e.epochStoragePrivateKeyBlob,e.obliviousValidationTokenBlob,e.authorityLevel,void 0,void 0],d[2]=a[0],d[3]=a[1],d[4]=a[2],d[5]=a[3],d[6]=a[4],d[7]=a[5],d[8]=a[6],d[9]=a[7],d[10]=a[8],d[11]=a[9],d[12]=a[10],d[13]=a[11],d[14]=a[12],d[15]=a[13],d[16]=a[14],d[17]=a[15],d[18]=a[16],d[19]=a[17],a}]))})},function(e){return c.blobs.neq(d[5],void 0)?c.i64.neq(d[3],void 0)?(d[27]=c.i64.to_string(d[2]),d[27]!==void 0?c.sequence([function(e){return d[28]=c.createArray(),d[63]=(d[28].push(d[27]),d[28]),d[63]=(d[28].push(a[0]),d[28]),d[29]=c.toJSON(d[28]),c.nativeOperation(b("LSGetBlobFromString.nop"),d[29]).then(function(a){return a=a,d[30]=a[0],a})},function(a){return c.nativeOperation(b("LSHmac_v2.nop"),c.blob("YXR0YWNobWVudF9rZXlfc2NvcGU="),d[30]).then(function(a){return a=a,d[31]=a[0],a})},function(a){return c.nativeOperation(b("LSOcmfClientMap.nop"),d[5],d[31]).then(function(a){return a=a,d[32]=a[0],a})},function(a){return c.nativeOperation(b("LSBase64Encode.nop"),d[32]).then(function(a){return a=a,d[33]=a[0],a})},function(a){return c.nativeOperation(b("LSGetBlobFromString.nop"),d[27]).then(function(a){return a=a,d[34]=a[0],a})},function(a){return c.nativeOperation(b("LSHmac_v2.nop"),c.blob("bWFpbGJveF9pZF9zY29wZQ=="),d[34]).then(function(a){return a=a,d[35]=a[0],a})},function(a){return c.nativeOperation(b("LSOcmfClientMap.nop"),d[5],d[35]).then(function(a){return a=a,d[36]=a[0],a})},function(a){return c.nativeOperation(b("LSBase64Encode.nop"),d[36]).then(function(a){return a=a,d[37]=a[0],a})},function(e){return d[38]=new c.Map(),d[38].set("attachment_index_key",d[33]==null?"":d[33]),d[38].set("delivery_object_id",a[0]),d[38].set("device_id",d[3]),d[38].set("mailbox_partial_id",d[37]==null?"":d[37]),d[38].set("media_type",a[2]),d[38].set("product_type",a[1]),d[38].set("trace_id",d[1]),d[39]=new c.Map(),d[39].set("otid",a[3]),d[39].set("server_thread_key",a[6]),d[39].set("timestamp",void 0),c.nativeOperation(b("LSGetBlobFromString.nop"),a[3]).then(function(a){return a=a,d[40]=a[0],a})},function(a){return c.nativeOperation(b("LSHmac_v2.nop"),c.blob("YXR0YWNobWVudF90b2tlbl9zYWx0X3Njb3Bl"),d[40]).then(function(a){return a=a,d[41]=a[0],a})},function(a){return c.nativeOperation(b("LSOcmfClientMap.nop"),d[5],d[41]).then(function(a){return a=a,d[42]=a[0],a})},function(a){return c.nativeOperation(b("LSBase64Encode.nop"),d[42]).then(function(a){return a=a,d[43]=a[0],a})},function(e){return d[38].set("salt_partial_id",d[43]==null?"":d[43]),c.nativeOperation(b("LSGetBlobFromString.nop"),a[4]).then(function(a){return a=a,d[44]=a[0],a})},function(a){return c.nativeOperation(b("LSHmac_v2.nop"),c.blob("dGhyZWFkX2lkX3Njb3Bl"),d[44]).then(function(a){return a=a,d[45]=a[0],a})},function(a){return c.nativeOperation(b("LSOcmfClientMap.nop"),d[5],d[45]).then(function(a){return a=a,d[46]=a[0],a})},function(a){return c.nativeOperation(b("LSBase64Encode.nop"),d[46]).then(function(a){return a=a,d[47]=a[0],a})},function(e){return d[38].set("thread_partial_id",d[47]==null?"":d[47]),c.nativeOperation(b("LSGetBlobFromString.nop"),a[3]).then(function(a){return a=a,d[48]=a[0],a})},function(a){return c.nativeOperation(b("LSHmac_v2.nop"),c.blob("bWVzc2FnZV9pZF9zY29wZQ=="),d[48]).then(function(a){return a=a,d[49]=a[0],a})},function(a){return c.nativeOperation(b("LSOcmfClientMap.nop"),d[5],d[49]).then(function(a){return a=a,d[50]=a[0],a})},function(a){return c.nativeOperation(b("LSBase64Encode.nop"),d[50]).then(function(a){return a=a,d[51]=a[0],a})},function(e){return d[38].set("message_partial_id",d[51]==null?"":d[51]),d[52]=c.createArray(),d[63]=(d[52].push(a[3]),d[52]),d[63]=(d[52].push(a[0]),d[52]),d[53]=c.toJSON(d[52]),d[38].set("raw_identifiers",d[39]),d[38].set("backup_id",d[27]),d[38].set("thread_id",a[4]),c.nativeOperation(b("LSGetBlobFromString.nop"),d[53]).then(function(a){return a=a,d[54]=a[0],a})},function(a){return c.nativeOperation(b("LSHmac_v2.nop"),c.blob("YXR0YWNobWVudF90b2tlbl9zYWx0X3Njb3Bl"),d[54]).then(function(a){return a=a,d[55]=a[0],a})},function(a){return c.nativeOperation(b("LSOcmfClientMap.nop"),d[5],d[55]).then(function(a){return a=a,d[56]=a[0],a})},function(a){return c.nativeOperation(b("LSBase64Encode.nop"),d[56]).then(function(a){return a=a,d[57]=a[0],a})},function(a){return d[38].set("salt_partial_access_token",d[57]==null?"":d[57]),c.nativeOperation(b("LSGetBlobFromString.nop"),d[29]).then(function(a){return a=a,d[58]=a[0],a})},function(a){return c.nativeOperation(b("LSHmac_v2.nop"),c.blob("YXR0YWNobWVudF9rZXlfc2FsdF9zY29wZQ=="),d[58]).then(function(a){return a=a,d[59]=a[0],a})},function(a){return c.nativeOperation(b("LSOcmfClientMap.nop"),d[5],d[59]).then(function(a){return a=a,d[60]=a[0],a})},function(a){return c.nativeOperation(b("LSBase64Encode.nop"),d[60]).then(function(a){return a=a,d[61]=a[0],a})},function(a){return d[38].set("salt_partial_attachment_index_key",d[61]==null?"":d[61]),d[62]=c.toJSON(d[38]),c.storedProcedure(b("LSIssueNewTask"),"encrypted_backups_attachment_upload",c.i64.cast([0,50012]),d[62],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))}]):(d[29]="Missing backup_id for IssueAttachmentBackupTask",function(a){c.logger(a).mustfix(a)}(["[EncryptedBackups][LSEncryptedBackupsIssueAttachmentBackupTaskStoredProcedure] ","",d[29]].join("")),d[28]=c.createArray(),void 0!==void 0?d[30]=(d[28].push(void 0),d[28]):0,c.nativeOperation(b("LSLogMebClientEvent.nop"),"LSEncryptedBackupsIssueAttachmentBackupTaskStoredProcedure",d[29],d[28],c.i64.cast([0,3])))):(d[28]="Missing device_id for IssueAttachmentBackupTask",function(a){c.logger(a).mustfix(a)}(["[EncryptedBackups][LSEncryptedBackupsIssueAttachmentBackupTaskStoredProcedure] ","",d[28]].join("")),d[27]=c.createArray(),void 0!==void 0?d[29]=(d[27].push(void 0),d[27]):0,c.nativeOperation(b("LSLogMebClientEvent.nop"),"LSEncryptedBackupsIssueAttachmentBackupTaskStoredProcedure",d[28],d[27],c.i64.cast([0,3]))):(d[28]="Missing ocmf_client_state_blob for IssueAttachmentBackupTask",function(a){c.logger(a).mustfix(a)}(["[EncryptedBackups][LSEncryptedBackupsIssueAttachmentBackupTaskStoredProcedure] ","",d[28]].join("")),d[27]=c.createArray(),void 0!==void 0?d[29]=(d[27].push(void 0),d[27]):0,c.nativeOperation(b("LSLogMebClientEvent.nop"),"LSEncryptedBackupsIssueAttachmentBackupTaskStoredProcedure",d[28],d[27],c.i64.cast([0,3])))},function(a){return d[21]=c.i64.of_float(Date.now()),d[22]=c.i64.random(),d[24]="Finishing execution. Execution time: ",d[25]=c.i64.to_string(c.i64.sub(d[21],d[0])),d[26]=" ms",d[23]="",function(a){c.logger(a).info(a)}(["[EncryptedBackups][LSEncryptedBackupsIssueAttachmentBackupTaskStoredProcedure] ",d[23],d[24],d[23],d[25],d[23],d[26]].join("")),c.i64.eq(c.i64.mod_(d[22],c.i64.cast([0,1e3])),c.i64.cast([0,0]))?(d[27]=",",d[28]=c.createArray(),void 0!==void 0?d[29]=(d[28].push(void 0),d[28]):0,c.nativeOperation(b("LSLogMebClientEvent.nop"),"LSEncryptedBackupsIssueAttachmentBackupTaskStoredProcedure",[d[24],d[27],d[25],d[27],d[26]].join(""),d[28],c.i64.cast([0,4]))):c.resolve()}])},function(a){return c.resolve(e)}])}a.__sproc_name__="LSEncryptedBackupsIssueAttachmentBackupTaskStoredProcedure";a.__tables__=["secure_encrypted_backups_client_state"];e.exports=a}),null);
__d("LSIssueAttachmentBackupTaskStoredProcedure",["LSIssueAttachmentBackupTask","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSIssueAttachmentBackupTask"),e.deliveryObjectId,e.productType,e.mediaType,e.messageId,e.threadId,e.traceId,e.serverThreadKey);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("LSIssueThreadRemove",["LSArrayGetObjectAt","LSIsEncryptionVersionSecure","LSIssueNewTaskAndGetTaskID","LSLogMebClientEvent.nop"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(f){return c.sequence([function(a){return d[0]=c.i64.of_float(Date.now()),function(a){c.logger(a).info(a)}("[EncryptedBackups][issueThreadRemove] Beginning execution."),d[1]=c.i64.of_float(Date.now()),c.islc(c.filter(c.db.table(168).fetch(),function(a){return c.i64.eq(a.authorityLevel,c.i64.cast([0,80]))||!1}),0,c.i64.to_float(c.i64.cast([0,1]))).next().then(function(a,b){var e=a.done;a=a.value;return e?(d[15]=new c.Map(),d[15].set("error_msg","Expected a nonempty query result"),d[15].set("code",c.i64.cast([0,3])),d[15].set("src_line","MEBClientStateUtils.php:247 MEBClientStateUtils::maybeClientStateFromDatabaseDirectly()"),d[16]=c.createArray(),d[17]=(d[16].push(d[15]),d[16]),e=[void 0,d[16]],d[2]=e[0],d[3]=e[1],e):(b=a.item,d[15]=new c.Map(),d[15].set("backup_id",b.backupId),d[15].set("device_public_key_blob",b.devicePublicKeyBlob),d[15].set("device_private_key_blob",b.devicePrivateKeyBlob),d[15].set("epoch_storage_public_key_blob",b.epochStoragePublicKeyBlob),d[15].set("epoch_storage_private_key_blob",b.epochStoragePrivateKeyBlob),d[15].set("epoch_auth_public_key_blob",b.epochAuthPublicKeyBlob),d[15].set("epoch_auth_private_key_blob",b.epochAuthPrivateKeyBlob),d[15].set("mailbox_root_key_blob",b.mailboxRootKeyBlob),d[15].set("ocmf_client_state_blob",b.ocmfClientStateBlob),d[15].set("orf_client_state_v2_blob",void 0),d[15].set("orf_v2_authority_level",void 0),d[15].set("oblivious_validation_token_blob",b.obliviousValidationTokenBlob),d[15].set("device_id",b.deviceId),d[15].set("backup_tenancy",b.backupTenancy),d[15].set("encryption_version",b.encryptionVersion),d[15].set("revision_version",b.revisionVersion),d[15].set("initialize_restore_result",void 0),d[15].set("device_creation_timestamp_sec",void 0),d[15].set("authority_level",b.authorityLevel),e=[d[15],void 0],d[2]=e[0],d[3]=e[1],e)})},function(e){return d[5]=new c.Map(),d[5].set("value",d[2]),d[5].set("errors",d[3]),d[6]=d[5].get("value"),d[6]!==void 0?c.sequence([function(e){return d[15]=d[6].get("backup_id"),d[16]=d[6].get("device_public_key_blob"),d[17]=d[6].get("device_private_key_blob"),d[18]=d[6].get("epoch_storage_public_key_blob"),d[19]=d[6].get("epoch_storage_private_key_blob"),d[20]=d[6].get("epoch_auth_public_key_blob"),d[21]=d[6].get("epoch_auth_private_key_blob"),d[22]=d[6].get("mailbox_root_key_blob"),d[23]=d[6].get("ocmf_client_state_blob"),d[24]=d[6].get("orf_client_state_v2_blob"),d[25]=d[6].get("orf_v2_authority_level"),d[26]=d[6].get("oblivious_validation_token_blob"),d[27]=d[6].get("device_id"),d[28]=d[6].get("backup_tenancy"),d[29]=d[6].get("encryption_version"),d[30]=d[6].get("revision_version"),d[31]=d[6].get("initialize_restore_result"),d[32]=d[6].get("device_creation_timestamp_sec"),d[33]=d[6].get("authority_level"),c.i64.neq(d[27],void 0)?c.sequence([function(a){return d[36]=void 0,c.i64.neq(d[36],void 0)?c.resolve(d[37]=d[36]):c.sequence([function(a){return d[43]=c.createArray(),c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,0])).then(function(a){return a=a,d[44]=a[0],a})},function(a){return d[44]?d[53]=(d[43].push(c.i64.cast([0,0])),d[43]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,2])).then(function(a){return a=a,d[45]=a[0],a})},function(a){return d[45]?d[53]=(d[43].push(c.i64.cast([0,2])),d[43]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,3])).then(function(a){return a=a,d[46]=a[0],a})},function(a){return d[46]?d[53]=(d[43].push(c.i64.cast([0,3])),d[43]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,4])).then(function(a){return a=a,d[47]=a[0],a})},function(a){return d[47]?d[53]=(d[43].push(c.i64.cast([0,4])),d[43]):0,d[48]=c.createArray(),d[49]=c.i64.of_int32(d[43].length),c.i64.gt(d[49],c.i64.cast([0,0]))?c.loopAsync(d[49],function(a){return d[53]=a,c.sequence([function(a){return c.nativeTypeOperation("Array",b("LSArrayGetObjectAt"),d[43],d[53]).then(function(a){return a=a,d[54]=a[0],d[55]=a[1],a})},function(a){return d[56]=(d[48].push(d[54]),d[48])}])}):c.resolve()},function(a){return c.sequence([function(a){return d[53]=c.createArray(),d[54]=c.i64.of_int32(d[48].length),c.i64.gt(d[54],c.i64.cast([0,0]))?c.loopAsync(d[54],function(a){return d[56]=a,c.sequence([function(a){return c.nativeTypeOperation("Array",b("LSArrayGetObjectAt"),d[48],d[56]).then(function(a){return a=a,d[57]=a[0],d[58]=a[1],a})},function(a){return d[59]=(d[53].push(c.i64.to_string(d[57])),d[53])}])}):c.resolve()},function(a){return d[55]=d[53].join(","),d[50]=d[55]}])},function(a){return d[48].some(function(a){return c.i64.eq(d[29],a)})?d[51]=d[29]:d[51]=void 0,c.i64.neq(d[51],void 0)?d[52]=d[51]:d[52]=void 0,d[37]=d[52]}])},function(e){return c.i64.neq(d[37],void 0)?d[38]=d[37]:d[38]=c.i64.cast([0,0]),c.i64.neq(d[28],void 0)?d[39]=d[28]:d[39]=c.i64.cast([0,1]),d[40]=c.i64.of_float(Date.now()),c.storedProcedure(b("LSIssueNewTaskAndGetTaskID"),["eb_upload",":",a[0]].join(""),c.i64.cast([0,50026]),"",void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),c.i64.cast([0,93]),void 0,c.i64.le(c.i64.cast([0,0]),c.i64.cast([0,0]))?c.i64.cast([0,0]):c.i64.add(d[40],c.i64.sub(c.i64.cast([0,0]),c.i64.mod_(d[40],c.i64.cast([0,0])))),c.i64.cast([0,0])).then(function(a){return a=a,d[41]=a[0],a})},function(b){return c.db.table(177).add({pk:void 0,pendingBackupTaskId:d[41],actionType:c.i64.cast([0,2]),contentType:c.i64.cast([0,2]),backupStatus:c.i64.cast([0,1]),listKey:a[0],uniqueKey:a[0],sortKey:a[1],persistentId:"",isInstamadillo:!1,lsTraceId:a[3],error:a[4]})},function(b){return c.i64.eq(c.i64.cast([0,2]),c.i64.cast([0,1]))?c.i64.eq(c.i64.cast([0,2]),c.i64.cast([0,1]))?(d[43]=c.createArray(),d[45]=(d[43].push("Successfully issued message backup task. act thread id = "),d[43]),d[45]=(d[43].push(a[0]),d[43]),d[45]=(d[43].push(", otid = "),d[43]),d[45]=(d[43].push(a[0]),d[43]),d[44]=d[43].join(void 0||""),function(a){c.logger(a).info(a)}(["[EncryptedBackups][issueThreadRemove] ","",d[44]].join("")),c.resolve()):(d[43]=c.createArray(),d[45]=(d[43].push("Successfully issued message remove task. act thread id = "),d[43]),d[45]=(d[43].push(a[0]),d[43]),d[45]=(d[43].push(", otid = "),d[43]),d[45]=(d[43].push(a[0]),d[43]),d[44]=d[43].join(void 0||""),function(a){c.logger(a).info(a)}(["[EncryptedBackups][issueThreadRemove] ","",d[44]].join("")),c.resolve()):(d[43]=c.createArray(),d[45]=(d[43].push("Successfully issued thread remove task. act thread id = "),d[43]),d[45]=(d[43].push(a[0]),d[43]),d[44]=d[43].join(void 0||""),function(a){c.logger(a).info(a)}(["[EncryptedBackups][issueThreadRemove] ","",d[44]].join("")),c.resolve())},function(a){return d[42]=new c.Map(),d[42].set("task_id",d[41]),a=[d[42],void 0],d[34]=a[0],d[35]=a[1],a}]):c.sequence([function(b){return d[36]=c.createArray(),d[39]=(d[36].push("Skipping thread remove since EB is disabled. act thread id = "),d[36]),d[39]=(d[36].push(a[0]),d[36]),d[37]=d[36].join(void 0||""),function(a){c.logger(a).info(a)}(["[EncryptedBackups][issueThreadRemove] ","",d[37]].join("")),c.resolve()},function(a){return d[38]=new c.Map(),d[38].set("error_code",c.i64.cast([0,24])),d[38].set("stored_procedure","LSEncryptedBackupsIssueThreadRemoveStoredProcedure"),d[38].set("error_message",""),a=[void 0,d[38]],d[34]=a[0],d[35]=a[1],a}])},function(a){return a=[d[34],d[35]],d[7]=a[0],d[8]=a[1],a}]):c.sequence([function(b){return d[15]=d[5].get("errors"),d[16]=d[5].get("errors"),d[17]=c.createArray(),d[20]=(d[17].push("Skipping thread remove since EB is disabled. act thread id = "),d[17]),d[20]=(d[17].push(a[0]),d[17]),d[18]=d[17].join(void 0||""),function(a){c.logger(a).info(a)}(["[EncryptedBackups][issueThreadRemove] ","",d[18]].join("")),c.resolve()},function(a){return d[19]=new c.Map(),d[19].set("error_code",c.i64.cast([0,1])),d[19].set("stored_procedure","LSEncryptedBackupsIssueThreadRemoveStoredProcedure"),d[19].set("error_message",""),a=[void 0,d[19]],d[7]=a[0],d[8]=a[1],a}])},function(a){return d[9]=c.i64.of_float(Date.now()),d[10]=c.i64.random(),d[12]="Finishing execution. Execution time: ",d[13]=c.i64.to_string(c.i64.sub(d[9],d[0])),d[14]=" ms",d[11]="",function(a){c.logger(a).info(a)}(["[EncryptedBackups][issueThreadRemove] ",d[11],d[12],d[11],d[13],d[11],d[14]].join("")),c.i64.eq(c.i64.mod_(d[10],c.i64.cast([0,1e3])),c.i64.cast([0,0]))?(d[15]=",",d[16]=c.createArray(),void 0!==void 0?d[17]=(d[16].push(void 0),d[16]):0,c.nativeOperation(b("LSLogMebClientEvent.nop"),"issueThreadRemove",[d[12],d[15],d[13],d[15],d[14]].join(""),d[16],c.i64.cast([0,4]))):c.resolve()},function(a){return a=[d[7],d[8]],e[0]=a[0],e[1]=a[1],a}])},function(a){return c.resolve(e)}])}a.__sproc_name__="LSEncryptedBackupsIssueThreadRemoveStoredProcedure";a.__tables__=["secure_encrypted_backups_client_state","pending_backups_context_v2"];e.exports=a}),null);
__d("LSIssueThreadRemoveStoredProcedure",["LSIssueThreadRemove","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSIssueThreadRemove"),e.actThreadId,e.sortKey,e.source,e.traceId,e.error);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("MAWAsyncEBMediaDownloadPromise",[],(function(a,b,c,d,e,f){"use strict";var g=new Map();function a(a){var b=a.messageId,c=a.objectId,d=a.plaintextHash;a=a.threadId;var e=g.get(c);g.set(c,e!=null?[].concat(e,[{messageId:b,plaintextHash:d,threadId:a}]):[{messageId:b,plaintextHash:d,threadId:a}])}function b(a){return(a=g.get(a))==null?void 0:a.pop()}f.setMediaDownloadPayload=a;f.retrieveMediaDownloadPayload=b}),66);
__d("MAWBridgeDeleteMessagesOfThreadHandler",["EBIsEbEnabled","LSFactory","LSIntEnum","LSIssueThreadRemoveStoredProcedure","LSMEBTaskCreationSource","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){if(b.actionType===2){if(!(yield d("EBIsEbEnabled").isEbEnabledLS(a)))return;yield c("LSIssueThreadRemoveStoredProcedure")(c("LSFactory")(a),{actThreadId:b.threadId,sortKey:b.ts.toString(),source:(h||(h=d("LSIntEnum"))).ofNumber(c("LSMEBTaskCreationSource").TAM_WEB)})}});return i.apply(this,arguments)}g.call=a}),98);
__d("MAWBridgeEphemeralSettingsUpdatedForUIHandler",["MAWChatJid","MAWEphemeralSettingsUpdate","Promise","asyncToGeneratorRuntime","cr:17100"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){var e=(yield d("MAWChatJid").toThreadKeyMaybe(a,c.chatJid));return e!=null?d("MAWEphemeralSettingsUpdate").updateEphemeralSettingsWithTxn(a,c.ephemeralSettingArgs.ephemeralExpirationInSec,yield b("cr:17100").castUnixTimeToMillisTime(c.ephemeralSettingArgs.ephemeralLastUpdatedOrSetTimestamp),c.isEphemeralSettingReset,e,!1,c.author):(h||(h=b("Promise"))).resolve()});return i.apply(this,arguments)}g.call=a}),98);
__d("MAWBridgeResignAttachmentCDNUrlHandler",["I64","LSFactory","LSIssueAttachmentRestoreTaskStoredProcedure","MAWAsyncEBMediaDownloadPromise","MAWBridgeFireAndForget","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";var h,i="-8";function a(a,b){var e,f=b.traceId,g=(e=b.skipLegacyMediaDownloadFlow)!=null?e:!1;function j(a,b,c){!g&&f!=null&&d("MAWBridgeFireAndForget").fireAndForget("backend","addMediaDownloadQpl",{annotations:c!=null?c:void 0,pointName:b,qplInstanceKey:f,qplType:a})}j("point","resign_cdn_url_bridge_handler_start");return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,b.threadJid,"MAWBridgeResignAttachmentCDNUrlHandler",function(a,e){e={backupEntFbid:(h||(h=d("I64"))).of_string(i),deliveryObjectId:b.deliveryObjectId,mediaType:b.mediaType,messageId:b.messageId,productType:b.productType,serverThreadKey:h.to_string(e),sortOrder:h.of_float(b.sortOrder),threadId:b.threadId,traceId:f.toString()};b.skipLegacyMediaDownloadFlow!==!0&&d("MAWAsyncEBMediaDownloadPromise").setMediaDownloadPayload({messageId:b.msgId,objectId:b.deliveryObjectId,plaintextHash:b.plaintextHash,threadId:b.threadId});j("point","resign_cdn_url_issue_attachment_task",{string:{taskTraceId:e.traceId}});return c("LSIssueAttachmentRestoreTaskStoredProcedure")(c("LSFactory")(a),e).then(function(){j("point","resign_cdn_url_issue_attachment_task_end")})["catch"](function(a){j("fail","resign_cdn_url_issue_attachment_task_fail",{string:{error:a.toString()}})})})}g.call=a}),98);
__d("MAWBridgeTraceUtils",["I64","Promise","ReQL"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,c,e){return d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.data_trace_meta).filter(function(a){var b=a.contextThree;if(b!=null&&c===b&&(i||(i=d("I64"))).equal(a.traceType,e))return a.shouldFlush===!1;else return!1})).then(function(a){if(a.length!==0)return(h||(h=b("Promise"))).resolve(a[0].traceId);else return(h||(h=b("Promise"))).resolve()})}g.getTraceId=a}),98);
__d("MAWBridgeStartTraceHandler",["ArmadilloDataTraceCheckPoint","ArmadilloDataTraceType","I64","LSAppendDataTraceAddonStoredProcedure","LSDataTraceTag","LSFactory","LSRequestId","MAWAckLevel","MAWBridgeTraceUtils","MAWMsg","MAWTimeUtils","Promise","gkx","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a,b,e){return a.data_trace_meta.add({contextOne:void 0,contextThree:b.externalId,contextTwo:void 0,foregroundTimestampMs:(i||(i=d("I64"))).zero,initTimestampMs:d("MAWTimeUtils").toTimestamp(b.ts),predefinedId:void 0,shouldFlush:!1,traceId:e,traceType:b.traceType}).then(function(){return c("LSAppendDataTraceAddonStoredProcedure")(c("LSFactory")(a),{checkPointId:d("ArmadilloDataTraceCheckPoint").traceCreated,dataTraceId:e,syncChannel:(i||(i=d("I64"))).neg_one})}).then(function(){var f;if(b.threadKey==null||!c("gkx")("23995"))return;return c("LSAppendDataTraceAddonStoredProcedure")(c("LSFactory")(a),{checkPointId:d("ArmadilloDataTraceCheckPoint").armadilloTlcControlOpenThread,dataTraceId:e,syncChannel:(i||(i=d("I64"))).neg_one,tags:(f=b.threadKey)!=null?f:void 0})})}function a(a,e){var f=e.ack,g=e.traceType;return f===d("MAWAckLevel").ACK.clock?d("MAWBridgeTraceUtils").getTraceId(a,e.externalId,e.traceType).then(function(f){if(f!=null)return;var k=c("LSRequestId").generate();if((i||(i=d("I64"))).equal(g,d("ArmadilloDataTraceType").armadilloMessageSend))return j(a,e,k).then(function(){var b=e.threadType;if(b==="User")return;return c("LSAppendDataTraceAddonStoredProcedure")(c("LSFactory")(a),{checkPointId:d("ArmadilloDataTraceCheckPoint").traceGroupMessage,dataTraceId:k,syncChannel:(i||(i=d("I64"))).neg_one})}).then(function(){if(e.isFirstMsg)return c("LSAppendDataTraceAddonStoredProcedure")(c("LSFactory")(a),{checkPointId:d("ArmadilloDataTraceCheckPoint").armadilloActMessageSend,dataTraceId:k,syncChannel:(i||(i=d("I64"))).neg_one,tags:"first_msg=1"})}).then(function(){var f=e.type_;f=f===d("MAWMsg").text?d("ArmadilloDataTraceCheckPoint").traceTextMessage:f===d("MAWMsg").ptt||f===d("MAWMsg").image||f===d("MAWMsg").video||f===d("MAWMsg").gif?d("ArmadilloDataTraceCheckPoint").traceMediaMessage:void 0;if(f==null)return(h||(h=b("Promise"))).resolve(f);c("promiseDone")(c("LSAppendDataTraceAddonStoredProcedure")(c("LSFactory")(a),{checkPointId:f,dataTraceId:k,syncChannel:(i||(i=d("I64"))).neg_one}));return(h||(h=b("Promise"))).resolve(f)}).then(function(b){if(b==null)return;if(!(i||(i=d("I64"))).equal(b,d("ArmadilloDataTraceCheckPoint").traceMediaMessage))return;b=e.type_;b=b===d("MAWMsg").image?d("LSDataTraceTag").image:b===d("MAWMsg").ptt?d("LSDataTraceTag").audio:b===d("MAWMsg").video?d("LSDataTraceTag").video:b===d("MAWMsg").gif?d("LSDataTraceTag").animatedImage:d("LSDataTraceTag").none;return c("LSAppendDataTraceAddonStoredProcedure")(c("LSFactory")(a),{checkPointId:d("ArmadilloDataTraceCheckPoint").richMediaMediaSendAttachmentType,dataTraceId:k,syncChannel:(i||(i=d("I64"))).neg_one,tags:String(b)})});else return j(a,e,k)}):(h||(h=b("Promise"))).resolve()}g.call=a}),98);
__d("MAWBridgeStartTraceWithTraceIdHandler",["MAWMainTraceUtils","MAWTimeUtils","WATimeUtils"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){return d("MAWMainTraceUtils").startTraceWithTxn(a,d("MAWTimeUtils").toTimestamp(d("WATimeUtils").unixTime()),b.traceId,b.traceType,void 0,void 0,b.traceContext)}g.call=a}),98);
__d("MAWBridgeUpdateTraceHandler",["ArmadilloDataTraceCheckPoint","I64","LSAppendDataTraceAddonStoredProcedure","LSDataTraceCheckPoint","LSFactory","LSIntEnum","MAWBridgeTraceEvent","Promise","ReQL","emptyFunction","updateAndFlushLSDataTrace"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(a,e,f){return d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.data_trace_meta).filter(function(a){var b=a.contextThree;if(b!=null&&e.externalIds.includes(b)&&(j||(j=d("I64"))).equal(a.traceType,e.traceType))return a.shouldFlush===!1;else return!1})).then(function(g){var i=[];return g.reduce(function(b,g){return b.then(function(){i.push(g.traceId);var b=e.errorMessage;if(b!=null)return c("LSAppendDataTraceAddonStoredProcedure")(c("LSFactory")(a),{checkPointId:f,dataTraceId:g.traceId,errorMessage:b,syncChannel:(j||(j=d("I64"))).neg_one});else return c("LSAppendDataTraceAddonStoredProcedure")(c("LSFactory")(a),{checkPointId:f,dataTraceId:g.traceId,syncChannel:(j||(j=d("I64"))).neg_one})})},(h||(h=b("Promise"))).resolve()).then(function(){return i})})}function l(a,d){return d.reduce(function(b,d){return b.then(function(){return c("updateAndFlushLSDataTrace")(a,d)})},(h||(h=b("Promise"))).resolve())}function a(a,e){var f=e.event;if(f===c("MAWBridgeTraceEvent").ReceivedReceipt)return k(a,e,d("ArmadilloDataTraceCheckPoint").deliveryReceiptReceived).then(function(b){return l(a,b)});else if(f===c("MAWBridgeTraceEvent").ReceivedSent)return k(a,e,d("ArmadilloDataTraceCheckPoint").advancedCryptoMEMSendTaskComplete).then(c("emptyFunction"));else if(f===c("MAWBridgeTraceEvent").FlowEndForFailure)return k(a,e,(i||(i=d("LSIntEnum"))).ofNumber(c("LSDataTraceCheckPoint").FLOW_END_FOR_FAILURE)).then(function(b){return l(a,b)});else if(f===c("MAWBridgeTraceEvent").FlowEndForSuccess)return k(a,e,(i||(i=d("LSIntEnum"))).ofNumber(c("LSDataTraceCheckPoint").FLOW_END_AND_FLUSH)).then(function(b){return l(a,b)});else return(h||(h=b("Promise"))).resolve()}g.call=a}),98);
__d("MAWBridgeUploadAttachmentHandler",["I64","LSDataTraceCheckPoint","LSFactory","LSIntEnum","LSIssueAttachmentBackupTaskStoredProcedure","LSRequestId","MAWEBFrontendQPLLogger","MAWEncryptedBackupUtils","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWTimeUtils","MAWTraceUtils","Promise","isInstamadilloEB"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function a(a,e){var f;if(c("isInstamadilloEB")())return(j||(j=b("Promise"))).resolve();var g=(f=e.traceId)!=null?f:c("LSRequestId").generate();f=e.mediaType;if(f==="document"){d("MAWTraceUtils").recordInvalidUploadRequestAndFlushTrace(g,(h||(h=d("LSIntEnum"))).ofNumber(c("LSDataTraceCheckPoint").LABYRINTH_WEB_ECHO_MESSAGE_UPLOAD_INVALID_REQUEST),[]);return(j||(j=b("Promise"))).resolve()}return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.threadJid,"MAWBridgeUploadAttachmentHandler",function(a,f){var k=(i||(i=d("I64"))).to_string(f),l=d("MAWTimeUtils").isTimestampNewerThanXDays(e.ts,30);return d("MAWEncryptedBackupUtils").getBackupTenancy(a,g).then(function(f){var i=e.productType;if(f!=null&&l)if(g!=null){d("MAWEBFrontendQPLLogger").startFlowUploadAttachmentBackup({deliveryObjectId:e.deliveryObjectId,flow:"old",mediaType:e.mediaType,messageId:e.messageId,threadId:e.threadId,traceId:g});return c("LSIssueAttachmentBackupTaskStoredProcedure")(c("LSFactory")(a),{deliveryObjectId:e.deliveryObjectId,mediaType:e.mediaType,messageId:e.messageId,productType:i,serverThreadKey:k,threadId:e.threadId,traceId:g})}else return c("LSIssueAttachmentBackupTaskStoredProcedure")(c("LSFactory")(a),{deliveryObjectId:e.deliveryObjectId,mediaType:e.mediaType,messageId:e.messageId,productType:i,serverThreadKey:k,threadId:e.threadId});else{d("MAWTraceUtils").recordInvalidUploadRequestAndFlushTrace(g,(h||(h=d("LSIntEnum"))).ofNumber(c("LSDataTraceCheckPoint").LABYRINTH_WEB_ECHO_MESSAGE_UPLOAD_INVALID_REQUEST),[]);d("MAWEBFrontendQPLLogger").endFlowUploadAttachmentBackup(g,!1,"old");return(j||(j=b("Promise"))).resolve()}})})}g.call=a}),98);
__d("MAWBridgeUploadMessageByBatchHandler",["MAWBridgeTraceRecordCheckpointHandler","MAWBridgeUploadMessageHandler","MAWEBFrontendQPLLogger","Promise","QPLUserFlow","asyncToGeneratorRuntime","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a,b){a.forEach(function(a){a.uploadTrackingInstanceKey!=null&&d("MAWEBFrontendQPLLogger").addPointEBUploadTracking(a.uploadTrackingInstanceKey,b)})}function a(a,b){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){i(e.messages,"upload_tracking_bridge_batch_handler");e.qpl.forEach(function(a){return c("QPLUserFlow").addPoint(a.event,a.action.name,{instanceKey:a.instanceKey,timestamp:a.timestamp})});e.checkPoint.forEach(function(b){return c("promiseDone")(d("MAWBridgeTraceRecordCheckpointHandler").call(a,b))});for(e of e.messages)yield d("MAWBridgeUploadMessageHandler").call(a,e);return(h||(h=b("Promise"))).resolve()});return j.apply(this,arguments)}g.call=a}),98);
__d("MAWFTSMergeCutoverSearchResults",["fbt","I64"],(function(a,b,c,d,e,f,g,h){"use strict";var i;function a(a,b){var c,e;c=(i||(i=d("I64"))).add((c=a.matchCount)!=null?c:(i||(i=d("I64"))).of_string("1"),(c=b.matchCount)!=null?c:(i||(i=d("I64"))).of_string("1"));b=i.max((e=a.messageTimestampMs)!=null?e:(i||(i=d("I64"))).minus_one,(e=b.messageTimestampMs)!=null?e:(i||(i=d("I64"))).minus_one);return babelHelpers["extends"]({},a,{contextLine:h._(/*BTDS*/"_j{\"*\":\"{number} matched messages\",\"_1\":\"1 matched message\"}",[h._plural(i.to_int32(c),"number")]).toString(),matchCount:c,matchLengths:void 0,matchOffsets:void 0,messageId:void 0,messageTimestampMs:(i||(i=d("I64"))).equal(b,(i||(i=d("I64"))).minus_one)?void 0:b})}g.mergeCutoverSearchResults=a}),226);
__d("MAWFTSUniversalSearchQPLLogger",["QPLUserFlow","emptyFunction","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h={hasStarted:!1,instanceKey:5e3},i=c("qpl")._(25301522,"2409");function a(){h.hasStarted&&j();h.instanceKey++;h.hasStarted=!0;c("QPLUserFlow").start(i,{annotations:{bool:{isInThread:!1}},instanceKey:h.instanceKey});return h.instanceKey}function j(a){if(!h.hasStarted||a!=null&&h.instanceKey!==a)return;h.hasStarted=!1;c("QPLUserFlow").endCancel(i,{instanceKey:h.instanceKey})}function b(a){var b=a.annotations,d=a.instanceKey;a=a.point;if(!h.hasStarted||d!=null&&h.instanceKey!==d)return;c("QPLUserFlow").addPoint(i,a,{instanceKey:h.instanceKey});b!=null&&k({annotations:b,instanceKey:h.instanceKey})}function k(a){var b=a.annotations;a=a.instanceKey;if(!h.hasStarted||a!=null&&h.instanceKey!==a)return;c("QPLUserFlow").addAnnotations(i,b,{instanceKey:h.instanceKey})}function d(a){if(!h.hasStarted||a!=null&&h.instanceKey!==a)return;h.hasStarted=!1;c("QPLUserFlow").endSuccess(i,{annotations:{"int":{result_count:h.currentResultCount}},instanceKey:h.instanceKey})}e={getCurrentInstanceKey:function(){return h.instanceKey},qplAddAnnotations:k,qplAddPoint:b,qplCancel:j,qplMarkFirstResultShowUp:c("emptyFunction"),qplStart:a,qplSuccess:d,updateCurrentResultCount:c("emptyFunction")};f=e;g["default"]=f}),98);
__d("MAWMiActOnActThreadReadyDeferred",["requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h=c("requireDeferred")("MAWMiActOnActThreadReady").__setRef("MAWMiActOnActThreadReadyDeferred");function a(a,b,c,d){return h.load().then(function(e){e=e.onActThreadReady;return e(a.tables,b,c,d)})}g["default"]=a}),98);
__d("MAWOfflineQueueState",["MAWOfflineQueueThreadStatus","MAWSharedProtocolQueueConst"],(function(a,b,c,d,e,f,g){"use strict";var h=[];f={offlineQueueCount:0,offlineQueueProgressDownloaded:0,offlineQueueProgressProcessed:0,offlineQueueSyncState:d("MAWSharedProtocolQueueConst").OfflineConsumerStatus.Initializing,offlineQueueThreadStatus:{}};var i=babelHelpers["extends"]({},f),j={getSnapshot:function(){return i},subscribe:function(a){h.push(a);return function(){h=h.filter(function(b){return b!==a})}}};function a(a){i.offlineQueueCount!==a&&(i=babelHelpers["extends"]({},i,{offlineQueueCount:a}),h.forEach(function(a){return a()}))}function b(a){i.offlineQueueProgressDownloaded!==a&&(i=babelHelpers["extends"]({},i,{offlineQueueProgressDownloaded:a}),h.forEach(function(a){return a()}))}function c(a,b){b=d("MAWOfflineQueueThreadStatus").calculateOfflineQueueThreadStatus(b,i.offlineQueueThreadStatus);if(i.offlineQueueThreadStatus===b&&i.offlineQueueProgressProcessed===a)return;i=babelHelpers["extends"]({},i,{offlineQueueProgressProcessed:a,offlineQueueThreadStatus:b});h.forEach(function(a){return a()})}function e(a){a=d("MAWOfflineQueueThreadStatus").calculateOfflineQueueThreadStatus(a,i.offlineQueueThreadStatus);if(i.offlineQueueThreadStatus==a&&i.offlineQueueSyncState===d("MAWSharedProtocolQueueConst").OfflineConsumerStatus.Complete)return;i=babelHelpers["extends"]({},i,{offlineQueueSyncState:d("MAWSharedProtocolQueueConst").OfflineConsumerStatus.Complete,offlineQueueThreadStatus:a});h.forEach(function(a){return a()})}g.EMPTY_STATE=f;g.offlineQueueState=j;g.initOfflineQueueSyncStart=a;g.initOfflineQueueSyncProgress=b;g.initOfflineQueueConsumerSyncProgress=c;g.initOfflineQueueSyncComplete=e}),98);
__d("escapeRegExp",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}f["default"]=a}),66);
__d("MWContentSearchUtils",["fbt","I64","LSIntEnum","LSMessageSearchType","MAWVault","escapeRegExp","gkx"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=c("gkx")("1798");function l(a){return a.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}function m(a,b){a=l(a).split(" ");var d=l(b),e=[],f=[];a.forEach(function(a){var b=new RegExp(c("escapeRegExp")(a),"gi");b=d.matchAll(b);b=Array.from(b);b=b.map(function(a){return a.index});var g=b.map(function(b){return a.length});f.push.apply(f,b);e.push.apply(e,g)});return{matchLengths:e.join(","),matchOffsets:f.join(",")}}function a(a,b,e,f){if(e!=null&&(b===1||(i||(i=d("I64"))).equal(f,(j||(j=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").MESSAGE)))){f=d("MAWVault").isVaulted(e)?d("MAWVault").unvault(e):e;e=m(a,f);return babelHelpers["extends"]({contextLine:f},e)}else return{contextLine:h._(/*BTDS*/"_j{\"*\":\"{number} matched messages\",\"_1\":\"1 matched message\"}",[h._plural(b,"number")]).toString(),matchLengths:void 0,matchOffsets:void 0}}function b(a,b){return a===1&&b!=null?b:void 0}function e(a,b){return a===1&&b!=null?b:void 0}function f(a){return a!=null?(i||(i=d("I64"))).of_float(a):void 0}function n(a,b){a=(i||(i=d("I64"))).of_float(-a);return b==null?a:i.min(a,b)}function o(a,b){if(!k||a.messageTimestampMs==null||b.messageTimestampMs==null)if((i||(i=d("I64"))).gt(a.globalIndex,b.globalIndex))return 1;else if((i||(i=d("I64"))).lt(a.globalIndex,b.globalIndex))return-1;else return 0;else if((i||(i=d("I64"))).lt(a.messageTimestampMs,b.messageTimestampMs))return 1;else if((i||(i=d("I64"))).gt(a.messageTimestampMs,b.messageTimestampMs))return-1;else return 0}g.getContextLineAndMatches=a;g.getMessageOtid=b;g.getMessageId=e;g.getMessageTimestamp=f;g.getGlobalIndex=n;g.searchResultsCompareFn=o}),226);
__d("MAWUniversalMessageSearch",["FBLogger","I64","JSResourceForInteraction","LSIntEnum","LSMessageSearchType","LSMessagingThreadTypeUtil","LSSearchRequestStatusType","MAWBridgeSearchMsg","MAWBridgeSendAndReceive","MAWFTSMergeCutoverSearchResults","MAWFTSUniversalSearchQPLLogger","MWContentSearchUtils","MWSearchThreadUtils","Promise","ReQL","asyncToGeneratorRuntime","groupBy","promiseDone","waitForMiActMappingForUniversalSearch"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").THREAD),l=new Map(),m=c("JSResourceForInteraction")("MAWAsyncEBIssueBulkMessagesPointQuery").__setRef("MAWUniversalMessageSearch");function n(a,b,c){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){yield a.message_search_queries.put(babelHelpers["extends"]({hasNextPage:!1,query:b,threadKeyV2:(j||(j=d("I64"))).one,type_:k},c))});return o.apply(this,arguments)}function p(a,b){return q.apply(this,arguments)}function q(){q=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=(j||(j=d("I64"))).to_string(b.threadKey);b.matchCount!=null&&l.set(c,(j||(j=d("I64"))).to_float(b.matchCount));c=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.message_search_results).getKeyRange(k,b.query,b.threadKey)));if(c==null)yield a.message_search_results.put(babelHelpers["extends"]({},b));else{var e=d("MAWFTSMergeCutoverSearchResults").mergeCutoverSearchResults(c,b);yield a.message_search_results.upsert([k,c.query,c.threadKey,c.globalIndex],babelHelpers["extends"]({},e,{globalIndex:b.globalIndex}))}});return q.apply(this,arguments)}function a(a,b){var c;c=(c=l.get((j||(j=d("I64"))).to_string(a)))!=null?c:0;c+=b;l.set((j||(j=d("I64"))).to_string(a),c)}function e(){l.clear()}function r(a,b){return!d("LSMessagingThreadTypeUtil").isArmadilloSecure(b)?null:(b=l.get((j||(j=d("I64"))).to_string(a)))!=null?b:null}function s(a,b,c){return t.apply(this,arguments)}function t(){t=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,g){l.clear();yield a.runInTransaction(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){yield n(a,g,{resultCount:(j||(j=d("I64"))).zero,status:(i||(i=d("LSIntEnum"))).ofNumber(c("LSSearchRequestStatusType").PENDING)})});return function(b){return a.apply(this,arguments)}}(),"readwrite",void 0,void 0,f.id+":142");c("MAWFTSUniversalSearchQPLLogger").qplAddPoint({point:"search_local_result_start"});var o=(yield d("MAWBridgeSendAndReceive").sendAndReceive("backend","search",{query:g,searchMode:d("MAWBridgeSearchMsg").MESSAGE_CONTENT_SEARCH}));o=o.map(function(a){if(a.recordType==="msg"){a.matchedTerms;a.recordType;a=babelHelpers.objectWithoutPropertiesLoose(a,["matchedTerms","recordType"]);return babelHelpers["extends"]({},a)}}).filter(Boolean);o=c("groupBy")(o,function(a){return a.chatJid});var q=Object.values(o);c("MAWFTSUniversalSearchQPLLogger").qplAddPoint({annotations:{"int":{numResult:q.length}},point:"search_local_result_end"});c("promiseDone")((h||(h=b("Promise"))).all(q.map(function(){var h=b("asyncToGeneratorRuntime").asyncToGenerator(function*(h,i){var l=h[0].chatJid;yield c("waitForMiActMappingForUniversalSearch")(a,l);var n=(yield d("MWSearchThreadUtils").getThreadDataForSearchResult(a,l,e,k));if(n==null)return;var o=n.profilePicUrl,q=n.secondaryProfilePicUrl,r=n.threadDisplayName,s=n.threadKey;n=n.threadType;var t=h.sort(function(a,b){return b.sortOrderMs-a.sortOrderMs})[0],u=h.length===1&&t.msg==null?{id:t.externalId,sortOrderMs:t.sortOrderMs}:null,v={displayName:r,globalIndex:d("MWContentSearchUtils").getGlobalIndex(t.sortOrderMs),matchCount:(j||(j=d("I64"))).of_int32(h.length),profilePicUrl:o,query:g,secondaryProfilePicUrl:q,threadKey:s,threadType:n,type_:k};if(u==null)yield a.runInTransaction(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){yield p(a,babelHelpers["extends"]({},v,{},d("MWContentSearchUtils").getContextLineAndMatches(g,h.length,t==null?void 0:(a=t.msg)==null?void 0:a.content,k),{messageId:d("MWContentSearchUtils").getMessageId(h.length,t==null?void 0:(a=t.msg)==null?void 0:a.msgId),messageOtid:d("MWContentSearchUtils").getMessageOtid(h.length,t==null?void 0:t.externalId),messageTimestampMs:d("MWContentSearchUtils").getMessageTimestamp(t==null?void 0:t.sortOrderMs)}))});return function(b){return a.apply(this,arguments)}}(),"readwrite",void 0,void 0,f.id+":243");else{c("MAWFTSUniversalSearchQPLLogger").qplAddPoint({point:"fetch_missing_message_"+i+"_start"});r=(yield m.load().then(function(b){b=b.issueQueryAsPromiseForSearchResult;return b(a,l,[u],"viewerId")}));if(r!=null&&r.length!==0){var w=r[0];c("MAWFTSUniversalSearchQPLLogger").qplAddPoint({point:"fetch_missing_message_"+i+"_end"});yield a.runInTransaction(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){yield p(a,babelHelpers["extends"]({},v,{},d("MWContentSearchUtils").getContextLineAndMatches(g,h.length,t==null?void 0:(a=t.msg)==null?void 0:a.content,k),{messageOtid:w.externalId,messageTimestampMs:w.sortOrderMs!=null?(j||(j=d("I64"))).of_float(w.sortOrderMs):void 0}))});return function(b){return a.apply(this,arguments)}}(),"readwrite",void 0,void 0,f.id+":285")}else c("MAWFTSUniversalSearchQPLLogger").qplAddPoint({annotations:{bool:{failedFetchingMissingMessage:!0}},point:"fetch_missing_message_"+i+"_end"})}});return function(a,b){return h.apply(this,arguments)}}())),function(){c("MAWFTSUniversalSearchQPLLogger").qplAddPoint({annotations:{"int":{numResult:q.length}},point:"issue_secure_search_query_end"}),c("promiseDone")(a.runInTransaction(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){yield n(a,g,{hasNextPage:!1,resultCount:(j||(j=d("I64"))).of_int32(q.length),status:(i||(i=d("LSIntEnum"))).ofNumber(c("LSSearchRequestStatusType").COMPLETE)})});return function(b){return a.apply(this,arguments)}}(),"readwrite",void 0,void 0,f.id+":325"))},function(a){c("FBLogger")("maw_fts_search").mustfix("Error while searching in secure threads. Err: %s",a==null?void 0:a.message)})});return t.apply(this,arguments)}g.updateSecureMsgMatchCountForThread=a;g.clearSecureMsgMatchCountForThread=e;g.getSecureMsgsMatchCountForThread=r;g.search=s}),98);
__d("isEBSearchEnabled",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("4741")}g["default"]=a}),98);
__d("MWInboxSecureContentSearch",["I64","JSResourceForInteraction","LSIntEnum","LSMessageSearchType","LSSearchRequestStatusType","MAWBridgeSearchMsg","MAWBridgeSendAndReceive","MAWChatJid","MAWFTSMergeCutoverSearchResults","MAWMiActOnActThreadReadyDeferred","MAWUniversalMessageSearch","MWContentSearchUtils","MWSearchThreadUtils","Promise","ReQL","asyncToGeneratorRuntime","emptyFunction","groupBy","isEBSearchEnabled","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=c("JSResourceForInteraction")("MAWAsyncEBIssueBulkMessagesPointQuery").__setRef("MWInboxSecureContentSearch"),l=c("justknobx")._("2605");function a(a,c,e){return(j||(j=b("Promise"))).all([d("ReQL").mergeJoin(d("ReQL").fromTableAscending(e.tables.participants,[]).getKeyRange(a),d("ReQL").fromTableAscending(e.tables.contacts)),d("ReQL").firstAsync(d("ReQL").fromTableAscending(e.tables.threads).getKeyRange(a))]).then(function(a){var b=a[0];a[1];a=b.filter(function(a){a[0];a=a[1];a=(h||(h=d("I64"))).equal(a.id,c);return!a}).map(function(a){a[0];a=a[1];return a});return d("ReQL").firstAsync(a)})}function m(a,c){return d("MAWChatJid").toThreadKeyMaybe(c,a.chatJid).then(function(e){return e==null?(j||(j=b("Promise"))).resolve([null,a]):(j||(j=b("Promise"))).all([d("ReQL").firstAsync(d("ReQL").fromTableAscending(c.threads).getKeyRange(e)),j.resolve(a)])})}function n(a,b){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){a=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.tables.contacts).getKeyRange((h||(h=d("I64"))).of_string(b))));return a});return o.apply(this,arguments)}function p(a,b){var e=b.query;b=b.threadKey;return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.message_search_results).getKeyRange((i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").THREAD),e,b))}function q(a,e,g,k,l,o,q,r){r===void 0&&(r=c("LSSearchRequestStatusType").COMPLETE);var s=function(f){return m(a,f).then(function(a){var m=a[0];if(m==null)return;var q=a[1],s=q.msg;if(s==null)return;if(q!=null)return(j||(j=b("Promise"))).all([d("MWSearchThreadUtils").getThreadPics(m,l,o),d("MWSearchThreadUtils").getThreadParticipants(m,o),(h||(h=d("I64"))).equal(k,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").MESSAGE))?n(o,s.sender):(j||(j=b("Promise"))).resolve(null),(h||(h=d("I64"))).equal(k,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").THREAD))?p(f,{query:g,threadKey:m.threadKey}):(j||(j=b("Promise"))).resolve(null)]).then(function(a){var n=a[0],o=n[0];n=n[1];var p=a[1],t=a[2];a=a[3];(h||(h=d("I64"))).equal(k,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").THREAD))&&d("MAWUniversalMessageSearch").updateSecureMsgMatchCountForThread(m.threadKey,e);p=babelHelpers["extends"]({},d("MWContentSearchUtils").getContextLineAndMatches(g,e,s.content,k),{displayName:d("MWSearchThreadUtils").getThreadDisplayName(l,m,p,t,k),globalIndex:(h||(h=d("I64"))).of_float(-q.sortOrderMs),matchCount:h.of_int32(e),messageId:s.msgId,messageOtid:q.externalId,messageTimestampMs:(h||(h=d("I64"))).equal(k,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").THREAD))&&e>1?void 0:(h||(h=d("I64"))).of_string(""+q.sortOrderMs),profilePicUrl:(h||(h=d("I64"))).equal(k,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").MESSAGE))&&t!=null?t.profilePictureUrl:o,query:g,secondaryProfilePicUrl:(h||(h=d("I64"))).equal(k,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").MESSAGE))&&t!=null?t.profilePictureFallbackUrl:n,threadKey:m.threadKey,threadType:m.threadType,type_:k});if((h||(h=d("I64"))).equal(k,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").THREAD))||a==null)o=f.message_search_results.put(p);else{t=d("MAWFTSMergeCutoverSearchResults").mergeCutoverSearchResults(a,p);o=f.message_search_results.upsert([k,a.query,a.threadKey,a.globalIndex],babelHelpers["extends"]({},t,{globalIndex:p.globalIndex}))}return(j||(j=b("Promise"))).all([f.message_search_queries.put({hasNextPage:void 0,nextPageCursor:void 0,query:g,resultCount:h.of_int32(e),status:(i||(i=d("LSIntEnum"))).ofNumber(r),threadKeyV2:m.threadKey,type_:k}),o]).then(c("emptyFunction"))})})};return q!=null?s(q):o.runInTransaction(function(a){return s(a)},"readwrite",void 0,void 0,f.id+":284")}function r(a,b,c,d){return s.apply(this,arguments)}function s(){s=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e,f){f!=null&&f.flow.qplAddPoint({instanceKey:f.instanceKey,point:"search_local_result_start"});c=(yield d("MAWBridgeSendAndReceive").sendAndReceive("backend","search",{query:c,searchMode:d("MAWBridgeSearchMsg").MESSAGE_CONTENT_SEARCH}));return(j||(j=b("Promise"))).all(c.map(function(b){if(b.recordType==="msg"){b.matchedTerms;b.recordType;var c=babelHelpers.objectWithoutPropertiesLoose(b,["matchedTerms","recordType"]);return d("MAWChatJid").toThreadKeyMaybe(a.tables,c.chatJid).then(function(a){return[c,a]})}}).filter(Boolean)).then(function(a){a=e!=null?a.filter(function(a){a[0];a=a[1];return a!=null&&(h||(h=d("I64"))).equal(a,e)}):a.filter(function(a){a[0];a=a[1];return a!=null});var b=null;e!=null&&(b={"int":{numResult:a.length}});f!=null&&f.flow.qplAddPoint({annotations:babelHelpers["extends"]({},b),instanceKey:f.instanceKey,point:"search_local_result_end"});return a})});return s.apply(this,arguments)}function e(a,e,f,g){d("MAWUniversalMessageSearch").clearSecureMsgMatchCountForThread();return r(f,a,void 0,g).then(function(k){k=Object.values(c("groupBy")(k,function(a){a[0];a=a[1];return a==null?"":(h||(h=d("I64"))).to_string(a)}));g!=null&&g.flow.qplAddAnnotations({annotations:{"int":{numResult:k.length}}});return k.map(function(b){var g=b[0];if(g!==null){g=g[0];return q(g,b.length,a,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").THREAD),e,f)}}).reduce(function(a,d){return(j||(j=b("Promise"))).all([a,d]).then(c("emptyFunction"))},(j||(j=b("Promise"))).resolve())})}function t(a,e,f,g,h,k){return(j||(j=b("Promise"))).all(g.map(function(b){return q(b,h,f,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").MESSAGE),k,a,e)})).then(c("emptyFunction"))}function u(a,e,f,g,h){return(j||(j=b("Promise"))).all(f.map(function(b){return q(b,g,e,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").MESSAGE),h,a)})).then(c("emptyFunction"))}function v(a,e,g,m,n){return m.runInTransaction(function(b){return b.message_search_queries.put({hasNextPage:void 0,nextPageCursor:void 0,query:a,resultCount:(h||(h=d("I64"))).of_int32(0),status:(i||(i=d("LSIntEnum"))).ofNumber(c("LSSearchRequestStatusType").PENDING),threadKeyV2:e,type_:i.ofNumber(c("LSMessageSearchType").MESSAGE)})},"readwrite",void 0,void 0,f.id+":463").then(function(b){return r(m,a,e,n)}).then(function(o){var p=o.length,r=o.filter(function(a){var b=a[0];a[1];return b.msg!=null}).length,s=r,t=function(){var f=o.map(function(a){a=a[0];return a}).filter(function(a){return a.msg==null});if(f.length===0)return(j||(j=b("Promise"))).resolve();f=f.slice(0,l);n.flow.qplAddPoint({annotations:{"int":{numRequestedMessages:f.length}},instanceKey:n.instanceKey,point:"fetch_missing_messages_start"});return c("MAWMiActOnActThreadReadyDeferred")(m,e,"MWMessageSearchInput.react",function(c,e){var i=f.map(function(a){return{id:a.externalId,sortOrderMs:a.sortOrderMs}});return k.load().then(function(c){var f=c.issueQueryAsPromiseForSearchResult,k=(h||(h=d("I64"))).to_string(g);c=f(m,e,i,k);return c==null?(j||(j=b("Promise"))).resolve():c.then(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){var c=b;if(b.length!==i.length){n.flow.qplAddPoint({annotations:{"int":{numRemainingMessages:i.length-b.length}},instanceKey:n.instanceKey,point:"fetch_missing_messages_partial"});var d=i.filter(function(a){return!c.some(function(b){return b.externalId===a.id})});if(d.length>0){d=(yield f(m,e,d,k));d!=null&&(c=[].concat(b,d))}}s=r+c.length;n.flow.qplAddPoint({annotations:{"int":{numRespondedMessages:c.length}},instanceKey:n.instanceKey,point:"fetch_missing_messages_end"});return u(m,a,c,s,g)});return function(a){return c.apply(this,arguments)}}())})})};return(j||(j=b("Promise"))).all([o.map(function(b){b=b[0];return q(b,p,a,(i||(i=d("LSIntEnum"))).ofNumber(c("LSMessageSearchType").MESSAGE),g,m,void 0,c("LSSearchRequestStatusType").PENDING)}).reduce(function(a,d){return(j||(j=b("Promise"))).all([a,d]).then(c("emptyFunction"))},j.resolve()),c("isEBSearchEnabled")()?t():(j||(j=b("Promise"))).resolve()]).then(function(b){return m.runInTransaction(function(b){return b.message_search_queries.put({hasNextPage:void 0,nextPageCursor:void 0,query:a,resultCount:(h||(h=d("I64"))).of_int32(s),status:(i||(i=d("LSIntEnum"))).ofNumber(c("LSSearchRequestStatusType").COMPLETE),threadKeyV2:e,type_:i.ofNumber(c("LSMessageSearchType").MESSAGE)})},"readwrite",void 0,void 0,f.id+":618")}).then(function(a){return(j||(j=b("Promise"))).resolve()})})}g.getThreadOtherParticipantContact=a;g.searchSecureDatabaseThreads=e;g.addBridgeSearchMsgsToSearchResult=t;g.addMessagesToSearchResult=u;g.searchSecureDatabaseMessages=v}),98);
__d("addMAWBridgeSearchMessagesToUniversalSearch",["I64","LSIntEnum","LSMessageSearchType","MAWCurrentUser","MAWUniversalMessageSearch","MWContentSearchUtils","MWSearchThreadUtils","ReQL","asyncToGeneratorRuntime","gkx","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(h||d("LSIntEnum")).ofNumber(c("LSMessageSearchType").THREAD);function a(a,b,c,d,e,f){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,g,h,k,l){var m;d("MAWUniversalMessageSearch").updateSecureMsgMatchCountForThread(g,k.length);var n=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.tables.message_search_results).getKeyRange(j,h,g))),o=n==null;m=(i||(i=d("I64"))).to_int32((m=n==null?void 0:n.matchCount)!=null?m:(i||(i=d("I64"))).zero);var p=m+k.length;m=(yield d("MWSearchThreadUtils").getThreadDataByThreadKey(a,g,i.of_string(d("MAWCurrentUser").getID()),j));if(m==null)return;var q=m.profilePicUrl,r=m.secondaryProfilePicUrl,s=m.threadDisplayName,t=m.threadType,u=n==null?void 0:n.globalIndex,v=d("MWContentSearchUtils").getGlobalIndex(k[0].sortOrderMs,n==null?void 0:n.globalIndex),w=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b;yield a.message_search_results.upsert([j,h,g,u!=null?u:v],babelHelpers["extends"]({},d("MWContentSearchUtils").getContextLineAndMatches(h,p,(b=k[0].msg)==null?void 0:b.content,j),{displayName:s,globalIndex:v,matchCount:(i||(i=d("I64"))).of_int32(p),messageTimestampMs:d("MWContentSearchUtils").getMessageTimestamp(k[0].sortOrderMs),profilePicUrl:q,query:h,secondaryProfilePicUrl:r,threadKey:g,threadType:t,type_:j}));o&&(yield a.message_search_queries.put(babelHelpers["extends"]({},l,{resultCount:(i||(i=d("I64"))).add(l.resultCount,i.one)})))});return function(b){return a.apply(this,arguments)}}();c("gkx")("11537")?c("promiseDone")(a.runInTransaction(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){yield w(a)});return function(b){return a.apply(this,arguments)}}(),"readwrite",void 0,void 0,f.id+":116")):e!=null&&(yield w(e))});return k.apply(this,arguments)}g["default"]=a}),98);