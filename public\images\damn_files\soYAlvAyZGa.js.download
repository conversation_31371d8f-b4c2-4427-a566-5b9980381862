;/*FB_PKG_DELIM*/

__d("DedupMiThreadCreationCache",["justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h=new Set();a=function(a){var b=c("justknobx")._("4749");b&&h.add(a)};g.DedupMiThreadCreationCache=h;g.addToDedupMiThreadCreationCache=a}),98);
__d("DoubleKeyMap",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.$1=new Map()}var b=a.prototype;b.set=function(a,b,c){var d=this.$1.get(a);d||this.$1.set(a,d=new Map());d.set(b,c)};b.get=function(a,b){return(a=this.$1.get(a))==null?void 0:a.get(b)};b["delete"]=function(a,b){var c=this.$1.get(a);c&&(c["delete"](b),c.size===0&&this.$1["delete"](a))};b.getAll=function(a){return this.$1.get(a)};b.deleteAll=function(a){this.$1["delete"](a)};return a}();f.DoubleKeyMap=a}),66);
__d("EBDeps",["FBLogger","err"],(function(a,b,c,d,e,f,g){"use strict";var h=null;function a(){if(h==null)throw c("err")("EBDeps is not initialized");return h}function b(a,b){if(h!=null)return;h=new Proxy(a,{get:function(a,d){var e=a[d];if(e instanceof Function)return function(){c("FBLogger")("labyrinth_web").info("EBDeps: function %s, reason %s",d,b);for(var f=arguments.length,g=new Array(f),h=0;h<f;h++)g[h]=arguments[h];return e.apply(a,g)};c("FBLogger")("labyrinth_web").info("EBDeps: %s is not function",d);return e}})}g.getDeps=a;g.setDeps=b}),98);
__d("EBGating",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("15195")}g.isEBSMv2Phase1Enabled=a}),98);
__d("LSEncryptedBackupsBackupTenancy",[],(function(a,b,c,d,e,f){a=Object.freeze({PRODUCTION:1,SHADOW:2,OPEN:3});f["default"]=a}),66);
__d("WAPubSub",["err"],(function(a,b,c,d,e,f,g){"use strict";var h=function(){function a(){this.$1=new Set()}var b=a.prototype;b.publish=function(a){this.$1.forEach(function(b){return b(a)})};b.subscribe=function(a){var b=this;this.$1.add(a);return function(){b.$1["delete"](a)}};return a}();b=function(){function a(){this.$1=new Map()}var b=a.prototype;b.publish=function(a,b){a=this.$2(a);a.forEach(function(a){return a(b)})};b.$2=function(a){(!this.$1.has(a)||this.$1.get(a)==null)&&this.$1.set(a,new Set());a=this.$1.get(a);if(a==null)throw c("err")("Key space should not be null");return a};b.subscribe=function(a,b){var c=this.$2(a);c.add(b);return function(){c["delete"](b)}};return a}();function a(){return new h()}g.WAPubSub=h;g.WAKeyedPubSub=b;g.simplePubSub=a}),98);
__d("MAWGenericStateManager",["Promise","WAPubSub"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(){function a(a){this.$2=d("WAPubSub").simplePubSub(),this.$1=a}var c=a.prototype;c.onSet=function(a){return this.$2.subscribe(a)};c.set=function(a){this.$1=a;this.$2.publish(a);return this.$1};c.get=function(){return this.$1};c.waitForValue=function(a){var c=this;return this.$1===a?(h||(h=b("Promise"))).resolve():new(h||(h=b("Promise")))(function(b){var d=c.onSet(function(c){c===a&&(d(),b())})})};return a}();g.MAWGenericStateManager=a}),98);
__d("MAWEBEnabledStateManager",["MAWGenericStateManager"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.call(this,!1)||this}var c=b.prototype;c.isEnabled=function(){return this.get()};c.waitForEBEnabled=function(){return this.waitForValue(!0)};return b}(d("MAWGenericStateManager").MAWGenericStateManager);g.MAWEBEnabledStateManager=a}),98);
__d("MAWEBSwitch",["EBGating","ExecutionEnvironment","MAWEBEnabledStateManager","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h;a=c("requireDeferred")("EBIsEbEnabledSubscriber").__setRef("MAWEBSwitch");var i=new(d("MAWEBEnabledStateManager").MAWEBEnabledStateManager)();((h||(h=c("ExecutionEnvironment"))).isInMainThread===!0||d("EBGating").isEBSMv2Phase1Enabled())&&void a.load().then(function(a){return a.subscribeToEbChanges(function(a){i.set(a)})});b=i;g["default"]=b}),98);
__d("LSDataTraceCheckPoint",[],(function(a,b,c,d,e,f){a=Object.freeze({CLIENT_DB_OUTBOUND:1,CLIENT_NETWORK_OUTBOUND:2,CLIENT_NETWORK_INBOUND:10,CLIENT_DB_INBOUND:11,CLIENT_EVALUATE_DASM:12,CLIENT_APP_STATE_CHANGE:13,CLIENT_CREATE_TASK:20,CLIENT_RECIPIENT_PRESENCE:21,CLIENT_EVALUATE_DASM_BEGIN:22,MQTT_PUBLISH:30,MQTT_PUBLISH_ACK:31,MQTT_DISCONNECTED:32,MQTT_CONNECTING:33,MQTT_CONNECTED:34,MQTT_CONNECTION_ACKED:35,TINCAN_RECEIVE_MESSAGE:36,TINCAN_RECEIVE_DECRYPTION_COMPLETE:37,TINCAN_RECEIVE_DECRYPTION_FAILED:38,TINCAN_RECEIVE_DR_QUEUED:39,TINCAN_RECEIVE_DR_SENT_TO_NETWORK:40,TINCAN_RECEIVE_DR_PUBLISH_TO_MQTT:41,TINCAN_RECEIVE_DRMQTT_PUBACK:42,TINCAN_RECEIVE_BOUNCE_TASK_QUEUED:43,TINCAN_RECEIVE_BOUNCE_TASK_SENT_TO_NETWORK:44,TINCAN_RECEIVE_BOUNCE_TASK_PUBLISH_TO_MQTT:45,RICH_MEDIA_DOWNLOAD_START:50,RICH_MEDIA_DOWNLOAD_END:51,RICH_MEDIA_DOWNLOAD_ERROR:52,RICH_MEDIA_UPLOAD_START:53,ARMADILLO_ACT:60,ARMADILLO_ACT_TASK_PICK:61,ARMADILLO_ACT_SERIALIZE:62,ARMADILLO_ACT_GROUP_SEND:63,ARMADILLO_ACT_SEND_FAILURE:64,ARMADILLO_ACT_MESSAGE_SEND:65,NETWORK_DISCONNECTED:70,NETWORK_CONNECTED:71,NETWORK_UNKNOWN:72,FLOW_CREAT:80,FLOW_END:81,FLOW_INTERRUPTED:82,FLOW_CONNECT_ID:83,FLOW_END_AND_FLUSH:84,FLOW_END_FOR_FAILURE:85,TRACE_CREATED:98,CLIENT_READY_TO_FLUSH:99,PENDING_DELTA:100,NO_PENDING_DELTA:101,INIT_SYNC:102,RESNAPSHOT:103,CATCH_UP:104,PERIODIC_SYNC:105,CATCH_UP_FROM_REVISION:106,RESNAPSHOT_CONTINUE:107,RESNAPSHOT_END:108,PERIODIC_SYNC_CONTINUE:109,PERIODIC_SYNC_END:110,CONTACT_SYNC_OPTIMISTIC:111,CONTACT_SYNC_FROM_OMNISTORE:112,CONTACT_SYNC_TAM_OPTIMISTC:113,CONTACT_SYNC_TAM_FROM_OMNISTORE:114,CONTACT_SYNC_TAM_ERROR:115,VERIFY_USER_FOR_SYNC:116,SNAPSHOT_START:117,SNAPSHOT_CONTINUE:118,SNAPSHOT_END:119,FETCH_DELTA:120,DELTA_SUMMARY:130,RECEIVE_DELIVERY_RECEIPT:200,TASK_DESERIALIZATION_ERROR:201,OPTIMISIC_MESSAGE_FAILED:202,SEND_SERVER_AUTHORITATIVE:203,THREAD_RANGE_QUERY_COMPLETE:204,THREAD_RANGE_QUERY_FAILURE:205,THREAD_POINT_QUERY_COMPLETE:206,SEND_SERVER_PARTIAL:207,MARK_AS_OPTIMISTIC_SENT:208,XMS_TRANSPORT_MESSAGE_SEND_START:300,XMS_TRANSPORT_MESSAGE_SEND_FAIL:301,SKIP_TASK_DISPATCH:900,ADVANCED_CRYPTO_MEM_ENCRYPT_TASK_START:1200,ADVANCED_CRYPTO_MEM_ENCRYPT_TASK_COMPLETE:1201,ADVANCED_CRYPTO_MEM_WA_PUBLISH:1202,ADVANCED_CRYPTO_MEM_PUBLISH_ACK:1203,ADVANCED_CRYPTO_MEM_SEND_TASK_COMPLETE:1204,ADVANCED_CRYPTO_MEM_WA_CONNECTION_DISCONNECTED:1205,ADVANCED_CRYPTO_MEM_WA_CONNECTION_CONNECTING:1206,ADVANCED_CRYPTO_MEM_WA_CONNECTION_CONNECTED:1207,ADVANCED_CRYPTO_MEM_FAILURE:1208,ADVANCED_CRYPTO_MEM_SEND_SUCCESS_WITH_RETRY:1209,ADVANCED_CRYPTO_MEM_DELIVERY_RECEIPT_RECEIVED:1210,ADVANCED_CRYPTO_REGISTRATION_CAT_DISK_FETCH_START:1211,ADVANCED_CRYPTO_REGISTRATION_CAT_DISK_FETCH_END:1212,ADVANCED_CRYPTO_REGISTRATION_CAT_NETWORK_FETCH_START:1213,ADVANCED_CRYPTO_REGISTRATION_CAT_NETWORK_FETCH_RETRYSTART:1214,ADVANCED_CRYPTO_REGISTRATION_CAT_NETWORK_FETCH_END:1215,ADVANCED_CRYPTO_REGISTRATION_WA_REQUEST_SEND_START:1216,ADVANCED_CRYPTO_REGISTRATION_WA_REQUEST_SEND_END:1217,ADVANCED_CRYPTO_REGISTRATION_WCR_REGISTER:1218,ADVANCED_CRYPTO_REGISTRATION_SAVE_WA_DEVICE_ID_ERROR:1219,ADVANCED_CRYPTO_MEM_DEVICE_FETCH_START:1220,ADVANCED_CRYPTO_MEM_DEVICE_FETCH_END:1221,ADVANCED_CRYPTO_MEM_ENCRYPTION_BEGIN:1222,ADVANCED_CRYPTO_MEM_ENCRYPTION_END:1223,ADVANCED_CRYPTO_MEM_ENCRYPTION_DEVICE_COUNT:1224,ADVANCED_CRYPTO_DUAL_SEND:1225,ADVANCED_CRYPTO_REGISTRATION_SERVER_SUCCESS:1226,ADVANCED_CRYPTO_REGISTRATION_RETRY_REGISTER:1227,ADVANCED_CRYPTO_REGISTRATION_CAT_NULL:1228,ADVANCED_CRYPTO_REGISTRATION_WCR_MANAGER_NULL:1229,ADVANCED_CRYPTO_REGISTRATION_RESPONSE_NULL:1230,ADVANCED_CRYPTO_REGISTRATION_SELF_NULL:1231,ADVANCED_CRYPTO_REGISTRATION_EMPTY_DEVICE_ID:1232,ADVANCED_CRYPTO_REGISTRATIONRETRY_REGISTER_FAILURE:1233,ADVANCED_CRYPTO_GROUP_DUAL_SEND:1234,RAVEN_IMAGE_MESSAGE:1235,RAVEN_VIDEO_MESSAGE:1236,ADVANCED_CRYPTO_REGISTRATION_RETRY_REGISTER_CAT:1237,ADVANCED_CRYPTO_REGISTRATION_RETRY_REGISTER_NETWORK:1238,ADVANCED_CRYPTO_CAT_DISK_FETCH_CAT_INVALID:1239,ADVANCED_CRYPTO_RECONNECT_GENERIC_ERROR:1240,ADVANCED_CRYPTO_RECONNECT_CAT_INVALID:1241,ADVANCED_CRYPTO_RECONNECT_CAT_EXPIRED:1242,ADVANCED_CRYPTO_CAT_MEMORY_FETCH_NULL:1243,ADVANCED_CRYPTO_FIRST_CONNECTION_START:1244,LABYRINTH_INITIALIZE_BACKUP_SPROC:1400,LABYRINTH_INITIALIZE_BACKUP_SUCCESS_SPROC:1401,LABYRINTH_INITIALIZE_BACKUP_FAILURE:1402,LABYRINTH_CREATE_BACKUP_MAILBOX_SPROC:1403,LABYRINTH_HANDLE_CREATE_BACKUP_MAILBOX_SUCCESS_SPROC:1404,LABYRINTH_OPEN_EPOCH_SPROC:1405,LABYRINTH_HANDLER_OPEN_EPOCH_SUCCESS_SPROC:1406,LABYRINTH_HANDLER_OPEN_EPOCH_FAILURE_SPROC:1407,LABYRINTH_CREATE_VIRTUAL_DEVICE_SPROC:1410,LABYRINTH_HANDLE_CREATE_VIRTUAL_DEVICE_SUCCESS_SPROC:1411,LABYRINTH_HANDLE_CREATE_VIRTUAL_DEVICE_FAILURE_SPROC:1412,LABYRINTH_ADD_DEVICE_SPROC:1413,LABYRINTH_ADD_DEVICE_FROM_VIRTUAL_SPROC:1414,LABYRINTH_HANDOLE_WRONG_RECOVERY_CODE_SPROC:1415,LABYRINTH_HANDLE_ADD_DEVICE_SUCCESS_SPROC:1416,LABYRINTH_HANDLE_ADD_DEVICE_FAILURE_SPROC:1417,LABYRINTH_ADD_DEVICE_SPROC_ADD_DEVICE_ALREADY_PENDING:1600,LABYRINTH_ADD_DEVICE_SPROC_UPDATE_RECOVERY_DATA_TABLE_START:1601,LABYRINTH_ADD_DEVICE_SPROC_ADD_DEVICE_NO_VD_TABLE:1602,LABYRINTH_ADD_DEVICE_SPROC_GENERATE_RECOVERY_SECRETS_START:1603,LABYRINTH_ADD_DEVICE_SPROC_WRONG_RC:1604,LABYRINTH_ADD_DEVICE_SPROC_NULL_VD:1605,LABYRINTH_ADD_DEVICE_SPROC_NO_RC_STATUS_TABLE_FOUND:1606,LABYRINTH_ADD_DEVICE_SPROC_ISSUE_FETCH_VD_TASK:1607,LABYRINTH_ADD_DEVICE_SPROC_SUCCESS:1608,LABYRINTH_INITIALIZE_RESTORE_SPROC:1418,LABYRINTH_DECRYPT_THREADS_SPROC:1419,LABYRINTH_REQUEST_MESSAGES_FROM_THREADS_SPROC:1420,LABYRINTH_REQUEST_MESSAGES_FROM_RANGE_QUERY_SPROC:1421,LABYRINTH_DECRYPT_MESSAGES_SPROC:1422,LABYRINTH_DELETE_BACKUP_ON_CLIENT_SPROC:1423,LABYRINTH_DELETE_BACKUPS_SPROC:1424,LABYRINTH_HANDLE_DELETE_MAILBOX_SPROC:1425,LABYRINTH_ISSUE_DELETE_BACKUP_TASK_SPROC:1426,LABYRINTH_BACKUP_UPLOAD_BEGIN:1427,LABYRINTH_BACKUP_UPLOAD_SUCCESS_SPROC:1428,LABYRINTH_CREATE_EXTRA_VIRTUAL_DEVICE_SPROC:1429,LABYRINTH_QR_INIT_ADD_DEVICE_SPROC:1430,LABYRINTH_QR_INIT_ADD_DEVICE_SUCCESS_SPROC:1431,LABYRINTH_QR_INIT_GET_SECRETS_SPROC:1433,LABYRINTH_QR_FINISH_ADD_PEER_DEVICE_SPROC:1434,LABYRINTH_SYNC_EPOCHS_STORED_PROCEDURE_SPROC:1436,LABYRINTH_SYNC_EPOCHS_SUCCESS_SPROC:1437,LABYRINTH_SYNC_EPOCHS_FORWARD_DERIVATION_END:1438,LABYRINTH_SYNC_EPOCHS_BACKWARD_DERIVATION_END:1439,LABYRINTH_SYNC_EPOCHS_OPEN_NEW_EPOCH:1440,LABYRINTH_PREPARE_OPEN_EPOCH_SPROC:1441,LABYRINTH_PREPARE_OPEN_EPOCH_SUCCESS_SPROC:1442,LABYRINTH_MEB_MANAGER_ADD_DEVICE:1443,LABYRINTH_BACKUP_UPLOAD_FAIL:1450,LABYRINTH_DECRYPT_SINGLE_MESSAGE_SPROC:1498,LABYRINTH_DECRYPT_SINGLE_MESSAGE_SUCCESS:1499,LABYRINTH_MEDIA_BACKUP_START:1455,LABYRINTH_MEDIA_BACKUP_SUCCESS:1456,LABYRINTH_MEDIA_BACKUP_FAILURE:1458,LABYRINTH_MEDIA_RESTORE_START:1459,LABYRINTH_MEDIA_RESTORE_SUCCESS:1460,LABYRINTH_MEDIA_RESTORE_FAILURE:1462,LABYRINTH_REMOVE_VIRTUAL_DEVICE_SPROC:1484,LABYRINTH_HANDLE_REMOVE_VIRTUAL_DEVICE_RESULT_SUCCESS:1485,LABYRINTH_HANDLE_REMOVE_VIRTUAL_DEVICE_RESULT_FAILURE:1486,LABYRINTH_REMOVE_VIRTUAL_DEVICE_BACKUP_NOT_FOUND:1493,LABYRINTH_REMOVE_VIRTUAL_DEVICE_WRITE_TASK:1494,LABYRINTH_BACKUP_UPLOAD_ENQUEUE_TASK_EXECUTER:1495,LABYRINTH_BACKUP_UPLOAD_ENQUEUE_SERIALIZER:1496,LABYRINTH_BACKUP_UPLOAD_SERIALIZE:1451,LABYRINTH_BACKUP_UPLOAD_SERIALIZE_SUCCEED:1463,LABYRINTH_BACKUP_UPLOAD_SERIALIZE_FAIL:1464,LABYRINTH_UPDATE_BACKUP_TABLE_SPROC:1467,LABYRINTH_FETCH_BACKUP_IDS_SPROC:1468,LABYRINTH_PROCESS_BACKUP_IDS_SPROC:1487,LABYRINTH_BACKUP_STATUS_NO_INFO:1488,LABYRINTH_BACKUP_STATUS_FETCH_ERROR:1489,LABYRINTH_BACKUP_STATUS_NOT_ONBOARDED:1490,LABYRINTH_BACKUP_STATUS_ONBOARDED:1491,LABYRINTH_BACKUP_STATUS_NO_BACKUPS_PRESENT:1492,LABYRINTH_OPT_OUT_OF_BACKUP_SPROC:1469,LABYRINTH_TRIGGER_THIRD_PARTY_ID_GENERATION_SPROC:1470,LABYRINTH_HANDLE_TRIGGER_THIRD_PARTY_ID_GENERATION_SPROC:1471,LABYRINTH_FETCH_BACKUPS_METADATA_SPROC:1478,LABYRINTH_HANDLE_BACKUPS_METADATA_RESULT_SPROC:1480,LABYRINTH_RESTORE_THREADS_SUCCESS:1481,LABYRINTH_RESTORE_MESSAGES_SUCCESS:1482,LABYRINTH_POINT_QUERY_MESSAGE_RESTORE:2511,LABYRINTH_RESTORE_MESSAGE_NEWER_DIRECTION:2514,LABYRINTH_RESTORE_SURROUNDING_MESSAGES:2515,LABYRINTH_BACKUP_UPLOAD_GET_ECHO_DOC:1497,LABYRINTH_MEBMANAGER_ADD_DEVICE_FETCH_BACKUP_STATE:1800,LABYRINTH_MEBMANAGER_ADD_DEVICE_FETCH_BACKUP_STATE_SUCCESS:1801,LABYRINTH_MEBMANAGER_ADD_DEVICE_FETCH_BACKUP_STATE_FAILURE:1802,LABYRINTH_MEBMANAGER_ADD_DEVICE_ADD_DEVICE_SPROC_CALLED:1803,LABYRINTH_MEBMANAGER_ADD_DEVICE_ADD_DEVICE_SPROC_RETURN_SUCCESS:1804,LABYRINTH_MEBMANAGER_ADD_DEVICE_ADD_DEVICE_SPROC_RETURN_FAILURE:1805,LABYRINTH_MEBMANAGER_ADD_DEVICE_NOTOFICATION_TRIGGERED:1806,LABYRINTH_MEBMANAGER_ADD_DEVICE_FETCH_ADD_DEVICE_STATE:1807,LABYRINTH_MEBMANAGER_ADD_DEVICE_FETCH_ADD_DEVICE_STATEFAILURE:1808,LABYRINTH_MEBMANAGER_ADD_DEVICE_INIT_RESTORE_SPROC_CALLED:1809,LABYRINTH_MEBMANAGER_ADD_DEVICE_INIT_RESTORE_SPROC_RETURN_SUCCESS:1810,LABYRINTH_MEBMANAGER_ADD_DEVICE_INIT_RESTORE_SPROC_RETURN_FAILURE:1811,LABYRINTH_MEBMANAGER_CREATE_EXTRA_VIRTUAL_DEVICE_SPROC_CALLED:1812,LABYRINTH_MEBMANAGER_CREATE_EXTRA_VIRTUAL_DEVICE_SPROC_RETURN_SUCCESS:1813,LABYRINTH_MEBMANAGER_CREATE_EXTRA_VIRTUAL_DEVICE_SPROC_RETURN_FAILURE:1814,LABYRINTH_MEBMANAGER_CREATE_EXTRA_VIRTUAL_DEVICE_NOTIFICATION_TRIGGERRED:1815,LABYRINTH_MEBMANAGER_CREATE_EXTRA_VIRTUAL_DEVICE_FETCH_RECOVERY_CODE_STATUS:1816,LABYRINTH_MEBMANAGER_CREATE_EXTRA_VIRTUAL_DEVICE_FETCH_RECOVERY_CODE_STATUS_FAILURE:1817,LABYRINTH_MEBMANAGER_CREATE_EXTRA_VIRTUAL_DEVICE_CREATE_EXTRA_VIRTUAL_DEVICE_SUCCESS:1818,LABYRINTH_MEBMANAGER_CREATE_EXTRA_VIRTUAL_DEVICE_CREATE_EXTRA_VIRTUAL_DEVICE_FAILURE:1819,LABYRINTH_MEBMANAGER_WRONG_RECOVERY_CODE:1823,LABYRINTH_ENCRYPT_MESSAGE_FAIL:1828,LABYRINTH_ENCRYPT_MESSAGE_GENERATED_PAYLOAD:1824,LABYRINTH_ENCODE_DOCUMENT:1820,LABYRINTH_ENCODE_DOCUMENT_FAIL:1821,LABYRINTH_ENCODE_DOCUMENT_SUCCESS:1822,LABYRINTH_UPLOAD_CANCEL:1850,LABYRINTH_DELETE_BACKUP_ON_CLIENT_STARTING_CANCEL:1851,LABYRINTH_DELETE_BACKUP_ON_CLIENT_DID_CANCEL:1852,LABYRINTH_DELETE_BACKUP_ON_CLIENT_FAILED_CANCEL:1853,LABYRINTH_BACKUP_RETRY_DELETE_MESSAGE_BACKUP_STATUS_ENTRY:1865,LABYRINTH_MESSAGE_INTEGRITY_CHECKER_BEGIN:1881,LABYRINTH_MESSAGE_INTEGRITY_CHECKER_GOT_MESSAGE_IDS_FROM_ACT:1882,LABYRINTH_MESSAGE_INTEGRITY_CHECKER_BEGIN_OCMF_OPE_OPERATIONS:1883,LABYRINTH_MESSAGE_INTEGRITY_CHECKER_END_OCMF_OPE_OPERATIONS:1884,LABYRINTH_MESSAGE_INTEGRITY_CHECKER_FETCH_MESSAGE_RANGE_ERROR:1885,LABYRINTH_MESSAGE_INTEGRITY_CHECKER_ISSUE_COMPARATOR_TASK:1886,LABYRINTH_CLEAR_BACKUP_TABLE_ON_CLIENT:1891,LABYRINTH_CLEAR_VIRTUAL_DEVICES_TABLE_ON_CLIENT:1892,LABYRINTH_CLEAR_BACKUP_METADATA_TABLE_ON_CLIENT:1893,SAP_VESTA_FETCH_VESTA_KEY_AND_SECRET_BEGIN:2400,LABYRINTH_PLUGIN_CALLING_GENERATE_RECOVERY_CODE:2401,SAP_FESTA_FETCH_KEY_AND_SECRET_COMPLETION:2402,SAP_VESTA_CALLING_VESTA_REGISTRATION:2403,SAP_VESTA_REGISTRATION_COMPLETION:2404,LABYRINTH_PLUGIN_CALLING_CREATE_EXTRA_VIRTUAL_DEVICE:2405,SAP_VESTA_NOTIFY_SECRET_STORED_COMPLETION:2406,SAP_VESTA_NOTIFY_CLIENT_ABOUT_VESTA_REGISTRATION:2407,SAP_VESTA_CALLING_VESTA_LOGIN:2411,SAP_VESTA_LOGIN_COMPLETION:2412,LABYRINTH_PLUGIN_CALLING_ADD_DEVICE:2413,LABYRINTH_PLUGIN_CALLING_INITIALIZE_BACKUP:2416,LABYRINTH_PIN_SETUP_BEGIN:2417,LABYRINTH_PIN_SETUP_SUCCESS:2418,LABYRINTH_PIN_SETUP_FAILURE:2419,LABYRINTH_PIN_RESTORE_BEGIN:2420,LABYRINTH_PIN_RESTORE_SUCCESS:2421,LABYRINTH_PIN_RESTORE_FAILURE:2422,LABYRINTH_PLUGIN_CALLING_INITIALIZE_RESTORE:2423,LABYRINTH_FETCH_THREAD_IDS_SPROC:2500,LABYRINTH_FETCH_THREAD_IDS_NATIVE_OP_SUCCESS:2501,LABYRINTH_FETCH_THREAD_IDS_NATIVE_OP_FAILURE:2502,LABYRINTH_REQUEST_MESSAGES_FROM_FETCH_THREAD_IDS_SPROC:2503,LABYRINTH_REQUEST_MESSAGES_FROM_INIT_RESTORE_SPROC:2506,LABYRINTH_POINT_QUERY_RESTORE_TASK_SPROC_START:2512,LABYRINTH_POINT_QUERY_RESTORE_TASK_SPROC_END:2513,LABYRINTH_VERSIONING_UPSERT_DEVICE_ENCRYPTION_VERSIONS_TO_CLIENT_SPROC_START:2800,LABYRINTH_VERSIONING_UPSERT_DEVICE_ENCRYPTION_VERSIONS_TO_CLIENT_SPROC_END:2801,LABYRINTH_VERSIONING_DELETE_AND_SYNC_DEVICE_ENCRYPTION_VERSION_DATA_SPROC:2802,LABYRINTH_VERSIONING_UPSERT_DEVICE_DELETE_DEVICE_ENCRYPTION_VERSION_DATA:2803,LABYRINTH_VERSIONING_SYNC_ENCRYPTION_VERSION_DATA_SPROC:2804,LABYRINTH_VERSIONING_VERSION_SUPPORTED:2805,LABYRINTH_VERSIONING_SYNC_UPDATE_VERSION_DATA:2806,LABYRINTH_VERSIONING_SYNC_NO_VERSION_DATA:2807,LABYRINTH_VERSIONING_VERSION_SELECTED:2808,LABYRINTH_VERSIONING_DELETE_AND_SYNC_DEVICE_DELETE_DEVICE_ENCRYPTION_VERSION_DATA:2809,LABYRINTH_VERSIONING_VERSION_NOT_SUPPORTED:2810,LABYRINTH_VERSIONING_UPSERT_DEVICE_INVALID_SIGNATURE:2811,LABYRINTH_PREPARE_OPEN_EPOCH_SPROC_ISSUE_REQUEST_ASSOC_DEVICES:2821,LABYRINTH_PREPARE_OPEN_EPOCH_FAILURE_NO_BACKUP:2822,LABYRINTH_PREPARE_OPEN_EPOCH_FAILURE_FLOW_ALREADY_IN_PROGRES:2823,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC:2824,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_NUM_EDGES:2825,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_PROCESS_EDGE_FROM:2826,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_PROCESS_EDGE_TO:2827,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_FORWARD_EDGE_SOURCE:2828,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_FORWARD_SOURCE_EPOCH_LOADED:2829,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_DERIVE_CHAINING_KEY_PSK:2830,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_DECRYPT_FORWARD_EDGE_ENTROPY:2831,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_FORWARD_EDGE_DECRYPTION_SUCCESS:2832,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_FORWARD_EDGE_DECRYPTION_FAIL:2833,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_FORWARD_FAIL_TO_LOAD_SOURCE_EPOCH:2834,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_BACKWARD_EDGE_SOURCE:2835,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_BACKWARD_SOURCE_EPOCH_LOADED:2836,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_DECRYPT_BACKWARD_EDGE:2837,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_BACKWARD_EDGE_DECRYPTION_SUCCESS:2838,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_BACKWARD_EDGE_DERIVATION_FAIL:2839,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_BACKWARD_FAIL_TO_LOAD_SOURCE_EPOCH:2840,LABYRINTH_DERIVE_AND_STORE_EPOCHS_SPROC_FAILURE:2841,LABYRINTH_GET_OPEN_EPOCH_KEYS_SPROC:2842,LABYRINTH_GET_OPEN_EPOCH_KEYS_NO_EPOCHS_ON_CLIENT:2843,LABYRINTH_GET_OPEN_EPOCH_KEYS_GET_ALL_EPOCH_ANON_IDS:2844,LABYRINTH_GET_OPEN_EPOCH_KEYS_NUM_ANON_IDS:2845,LABYRINTH_GET_OPEN_EPOCHS_KEYS_EPOCH_ANON_ID_LIST_EMPTY:2846,LABYRINTH_GET_OPEN_EPOCH_KEYS_GET_KEYS_FOR_ANON_ID:2847,LABYRINTH_GET_OPEN_EPOCH_KEYS_ANON_ID_NOT_FOUND:2848,LABYRINTH_SYNC_EPOCHS_SPROC_DOWNLOAD_EPOCHS_V2_WRITE_TASK:2849,LABYRINTH_OPEN_EPOCH_SPROC_OPEN_INITIAL_EPOCH:2850,LABYRINTH_OPEN_EPOCH_SPROC_OPEN_EPOCH_WITH_LAST_EPOCH_METADATA:2851,LABYRINTH_OPEN_EPOCH_SPROC_OPEN_AFTER_FIRST_EPOCH:2852,LABYRINTH_OPEN_EPOCH_SPROC_FAILURE_LAST_EPOCH_NOT_FOUND:2853,LABYRINTH_OPEN_EPOCH_SPROC_ERROR:2854,LABYRINTH_PREPARE_OPEN_EPOCH_SPROC_CLOSE_LAST_EPOCH:2855,LABYRINTH_PREPARE_OPEN_EPOCH_SPROC_DERIVE_AND_STORE_EPOCH:2856,LABYRINTH_HANDLER_OPEN_EPOCH_SUCCESS_SPROC_DERIVE_AND_STORE:2857,LABYRINTH_HANDLER_OPEN_EPOCH_SUCCESS_SPROC_UPDATE_AUTHORITY:2858,LABYRINTH_HANDLER_OPEN_EPOCH_SUCCESS_SPROC_UPDATE_EPOCH_ID:2859,LABYRINTH_INCLUDE_FALLBACK_KEY_DURING_ENCRYPTION:2860,LABYRINTH_DERIVE_FALLBACK_KEY:2861,LABYRINTH_FAILED_TO_INCLUDE_FALLBACK_KEY:2862,LABYRINTH_GET_OPEN_EPOCH_KEYS_ANON_ID_VALUE:2863,LABYRINTH_GET_OPEN_EPOCH_KEYS_ANON_ID_NULL_VALUE:2864,LABYRINTH_MESSAGE_RESTORE_EPOCH_ANON_ID_NULL:2865,LABYRINTH_WEB_EPOCH_ROTATION_ENABLED:2866,LABYRINTH_WEB_EPOCH_ROTATION_DISABLED:2867,LABYRINTH_MOBILE_EPOCH_ROTATION_ENABLED:2868,LABYRINTH_MOBILE_EPOCH_ROTATION_DISABLED:2869,LABYRINTH_DECRYPT_MESSAGE_SPROC_SINGLE_EPOCH_KEY:2870,LABYRINTH_DECRYPT_MESSAGE_SPROC_EPOCH_NOT_FOUND_ERROR:2871,LABYRINTH_DECRYPT_MESSAGE_SPROC_MULTIPLE_EPOCHS_ERROR:2872,LABYRINTH_WEB_ECHO_MESSAGE_RESTORE_BEGIN:3600,LABYRINTH_WEB_ISSUE_RESTORE_JOB_DONE:3601,LABYRINTH_WEB_INSERT_TO_BAKCUP_MESSAGE_TABLE_DONE:3602,LABYRINTH_WEB_RESTORE_WITH_SERIALIZER_START:3603,LABYRINTH_WEB_ISSUE_RESTORE_JOB_STARTED:3604,LABYRINTH_WEB_INSERT_TO_RESTORE_PAYLOAD_CONTEXT_TABLE_DONE:3605,LABYRINTH_WEB_XMA_RESTORE_STARTED:3606,LABYRINTH_WEB_XMA_RESTORE_ENDED:3607,LABYRINTH_WEB_XMA_RESTORE_MEDIAID_NULL:3608,LABYRINTH_WEB_XMA_RESTORE_DBXMA_NULL:3609,LABYRINTH_WEB_ISSUE_MESSAGE_RANGE_QUERY:3610,LABYRINTH_WEB_ECHO_MESSAGE_UPLOAD_INVALID_REQUEST:3700,LABYRINTH_WEB_ECHO_MESSAGE_UPLOAD_BEGIN:3701,LABYRINTH_WEB_ECHO_MESSAGE_UPLOAD_FAILURE:3702,LABYRINTH_WEB_UPLOAD_THREAD_NOT_PRESENT:3703,LABYRINTH_WEB_UPLOAD_THREAD_PRESENT:3704,LABYRINTH_WEB_UPLOAD_GET_SENDER_INFO_UNSUCCESSFUL:3705,LABYRINTH_WEB_UPLOAD_GET_SENDER_INFO_SUCCESSFUL:3706,LABYRINTH_WEB_UPLOAD_CONTACT_NAME_NULL:3707,LABYRINTH_WEB_UPLOAD_CONTACT_NAME_NOT_NULL:3708,LABYRINTH_WEB_UPLOAD_GET_RECEIPT_STATUS_LIST_SUCCESSFUL:3709,LABYRINTH_WEB_UPLOAD_GET_SENDER_DISPLAY_NAME_FOR_QUOTED_MSG_NULL:3710,LABYRINTH_WEB_UPLOAD_GET_SENDER_DISPLAY_NAME_FOR_QUOTED_MSG_NOT_NULL:3711,LABYRINTH_WEB_UPLOAD_XMA_DATA_UPLOAD_SUCCESSFUL:3712,LABYRINTH_WEB_UPLOAD_XMA_DATA_UPLOAD_UNSUCCESSFUL:3713,LABYRINTH_WEB_UPLOAD_INVALID_MSG_TYPE_FOR_MEDIA_TYPE:3714,LABYRINTH_WEB_UPLOAD_IS_MEDIA_MSG_TRUE:3715,LABYRINTH_WEB_UPLOAD_IS_MEDIA_MSG_FALSE:3716,LABYRINTH_WEB_UPLOAD_UNSUPPORTED_MEDIA_TYPE:3717,LABYRINTH_WEB_UPLOAD_NO_MEDIA_ID_FOUND_IN_MEDIA_MSG:3718,LABYRINTH_WEB_UPLOAD_NO_MEDIA_ENTRY_FOR_MEDIA_ID:3719,LABYRINTH_WEB_UPLOAD_GET_REACTION_DATA_SUCCESSFUL:3720,LABYRINTH_WEB_UPLOAD_GET_QUOTE_MSG_SUCCESSFUL:3721,LABYRINTH_WEB_UPLOAD_GET_QUOTE_SENDER_NAME_SUCCESSFUL:3722,LABYRINTH_WEB_UPLOAD_FB_ID_NULL_FOR_XMA_MSG:3723,LABYRINTH_WEB_UPLOAD_GET_XMA_DATA_SUCCESSFUL:3724,LABYRINTH_WEB_UPLOAD_NO_XMA_DB_ENTRY_FOR_EXTERNAL_ID:3725,LABYRINTH_WEB_UPLOAD_ECHO_MESSAGE_CREATION_SUCCESSFUL:3726,LABYRINTH_WEB_UPLOAD_MISSING_MEDIA_DATA_FOR_MEDIA_MSG:3727,LABYRINTH_WEB_UPLOAD_INVOKE_MESSAGE_UPLOAD_SPROC:3728,LABYRINTH_WEB_UPLOAD_ISSUE_MESSAGE_UPLOAD_UI_EVENT:3729,LABYRINTH_WEB_UPLOAD_USER_DEVICE_NOT_ENROLLED:3730,LABYRINTH_WEB_GET_BACKUP_TENANCY:3731,LABYRINTH_WEB_UPLOAD_ERROR_OCCURRED_ON_ISSUE_MESSAGE_UPLOAD_UI_EVENT:3739,LABYRINTH_WEB_UPLOAD_XMA_NOT_FOUND_IN_IDB:3740,LABYRINTH_WEB_UPLOAD_MEDIA_NOT_FOUND_FOR_XMA_IN_IDB:3741,LABYRINTH_WEB_UPLOAD_INVALID_ECHO_MESSAGE_SERIALIZATION_RESPONSE:3742,LABYRINTH_WEB_UPLOAD_DUPLICATE_REQUEST:3743,LABYRINTH_WEB_RETROACTIVE_BACKUPS_START_UPLOAD_MSG:3744,LABYRINTH_WEB_RETROACTIVE_BACKUPS_START_UPLOAD_MEDIA:3745,LABYRINTH_WEB_RETROACTIVE_BACKUPS_UPLOAD_MSG_ERROR:3746,LABYRINTH_WEB_RETROACTIVE_BACKUPS_UPLOAD_MEDIA_ERROR:3747,LABYRINTH_WEB_UPLOAD_XMA_DATA_IS_NULL:3748,LABYRINTH_WEB_UPLOAD_REPLIED_MESSAGE_NOT_IN_DB:3749,LABYRINTH_WEB_UPLOAD_REPLIED_MESSAGE_IN_DB:3750,MQTT_STATE_DISCONNECTED_EVENT:********,MQTT_STATE_CONNECTED_EVENT:********,APP_STATEBACKGROUND:********,APP_STATEFOREGROUND:********,MQTT_STATE_CONNECTING_EVENT:********,GLOBAL_CONNECTIVITY_BANNER_DISPLAY:********,GLOBAL_CONNECTIVITY_BANNER_VANISH:********,SWITCH_ACCOUNT_EVENT:********,ADVANCED_CRYPTO_MEM_WA_CONNECTION_DISCONNECTED_EVENT:********,ADVANCED_CRYPTO_MEM_WA_CONNECTION_CONNECTING_EVENT:********,ADVANCED_CRYPTO_MEM_WA_CONNECTION_CONNECTED_EVENT:********});f["default"]=a}),66);
__d("LSDataTraceTag",[],(function(a,b,c,d,e,f){"use strict";a=0;b=1;c=2;d=3;e=4;var g=5,h=6,i=7,j=20,k=21,l=22,m=23,n=24,o=25,p=26,q=27,r=28,s=29,t=30,u=31,v=32,w=33,x=34,y=35,z=36,A=37,B=38,C=39,D=40,E=41,F=42;f.none=a;f.sticker=b;f.image=c;f.animatedImage=d;f.video=e;f.audio=g;f.file=h;f.xma=i;f.messageTypeNone=j;f.messageTypeText=k;f.messageTypeFutureproof=l;f.messageTypeCiphertext=m;f.messageTypeUnavailable=n;f.messageTypeImage=o;f.messageTypeVideo=p;f.messageTypePtt=q;f.messageTypeAdmin=r;f.messageTypeRevoked=s;f.messageTypeGif=t;f.messageTypeSticker=u;f.messageTypeExpiredEphemeral=v;f.messageTypeEphemeralSettingAdmin=w;f.messageTypeEphemeralSyncResponse=x;f.messageTypeEphemeralScreenshotAction=y;f.messageTypeDeleteForMe=z;f.messageTypeEphemeralSettingChangeFromCurrentDevice=A;f.messageTypeAlertICDC=B;f.messageTypeGroupInvite=C;f.messageTypeXMA=D;f.messageTypeReaction=E;f.messageTypeSenderKeyDistribution=F}),66);
__d("LSRequestId",["uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h="0123456789abcdef",i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function a(){var a=c("uuidv4")().replace(/[^0-9a-f]/gi,""),b=0,d=["#"];while(b<33){var e=h.indexOf(a.charAt(b))<<8,f=h.indexOf(a.charAt(b+1))<<4,g=h.indexOf(a.charAt(b+2));g=g|(f|e);f=g>>6;d.push(i.charAt(f));d.push(i.charAt(g&63));b+=3}return d.join("")}b={generate:a};g["default"]=b}),98);
__d("MAWBridgeTrace",["WAJids"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,e,f,g,h,i){var j=a.ack,k=a.externalId,l=a.ts;a=a.type;var m=d("WAJids").switchOnMsgrChatJidType(b,{group:function(a){return"Group"},user:function(a){return"User"}});return{ack:j,externalId:k,isFirstMsg:!!f,openMessageOtid:e,openMessageParticipantCount:g,participantCount:h,threadJid:b,threadKey:i,threadType:m,traceType:c,ts:l,type_:a}}function b(a,b,c){return{traceContext:c,traceId:b,traceType:a}}function c(a,b,c,d){return{errorMessage:d,event:b,externalIds:a,traceType:c}}function e(a,b,c,d,e,f){return{checkPointId:a,dataTraceId:b,errorMessage:c,shouldFlush:d,syncChannel:e,tags:f}}g.createBridgeStartTraceData=a;g.createBridgeStartTraceWithTraceIdData=b;g.createBridgeUpdateTraceData=c;g.createBridgeTraceRecordCheckpointData=e}),98);
__d("WAMsgType",[],(function(a,b,c,d,e,f){"use strict";a="Text";b="Futureproof";c="Ciphertext";d="Unavailable";e="Image";var g="Video",h="Ptt",i="Admin",j="Revoked",k="Gif",l="Sticker",m="DocumentFile",n="ExpiredEphemeral",o="EphemeralSettingAdmin",p="EphemeralSyncResponse",q="DeleteForMe",r="EphemeralSettingChangeFromCurrentDevice",s="AlertICDC",t="GroupInvite",u="NoteReply",v="Reaction",w="SenderKeyDistribution",x="EditAction",y="ReceiverFetch",z="Raven",A="GroupPollCreate",B="GroupPollUpdate",C={ADMIN:i,CIPHERTEXT:c,DELETE_FOR_ME:q,DOCUMENT_FILE:m,EDIT_ACTION:x,EPHEMERAL_SETTING_ADMIN:o,EPHEMERAL_SETTING_CHANGE_FROM_CURRENT_DEVICE:r,EPHEMERAL_SYNC_RESPONSE:p,EXPIRED_EPHEMERAL:n,FUTUREPROOF:b,GIF:k,GROUP_INVITE:t,ICDC_ALERT:s,IMAGE:e,PTT:h,REACTION:v,REVOKED:j,SK_DISTRIBUTION:w,STICKER:l,TEXT:a,UNAVAILABLE:d,VIDEO:g,RECEIVER_FETCH:y,RAVEN:z,GROUP_POLL_CREATE:A,GROUP_POLL_UPDATE:B};f.TEXT=a;f.FUTUREPROOF=b;f.CIPHERTEXT=c;f.UNAVAILABLE=d;f.IMAGE=e;f.VIDEO=g;f.PTT=h;f.ADMIN=i;f.REVOKED=j;f.GIF=k;f.STICKER=l;f.DOCUMENT_FILE=m;f.EXPIRED_EPHEMERAL=n;f.EPHEMERAL_SETTING_ADMIN=o;f.EPHEMERAL_SYNC_RESPONSE=p;f.DELETE_FOR_ME=q;f.EPHEMERAL_SETTING_CHANGE_FROM_CURRENT_DEVICE=r;f.ICDC_ALERT=s;f.GROUP_INVITE=t;f.NOTE_REPLY=u;f.REACTION=v;f.SK_DISTRIBUTION=w;f.EDIT_ACTION=x;f.RECEIVER_FETCH=y;f.RAVEN=z;f.GROUP_POLL_CREATE=A;f.GROUP_POLL_UPDATE=B;f.MSG_TYPE=C}),66);
__d("MAWMsgType",["WAMsgType"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){return k.has(a)};var h=function(a){return l.has(a)||k.has(a)};b=function(a){return h(a)||m.has(a)};c="EphemeralScreenshotAction";e="Raven";f="RavenAction";var i="XMA",j="BumpExistingMessage";d=babelHelpers["extends"]({},d("WAMsgType").MSG_TYPE,{BUMP_EXISTING_MESSAGE:j,EPHEMERAL_SCREENSHOT_ACTION:c,RAVEN:e,RAVEN_ACTION:f,XMA:i});var k=new Set([d.IMAGE,d.VIDEO,d.PTT,d.GIF,d.STICKER,d.XMA,d.RAVEN,d.DOCUMENT_FILE,d.RECEIVER_FETCH]),l=new Set([d.TEXT,d.REVOKED,d.XMA,d.BUMP_EXISTING_MESSAGE,d.GROUP_POLL_CREATE]),m=new Set([d.FUTUREPROOF]),n=new Set([d.TEXT,d.IMAGE,d.VIDEO,d.PTT,d.GIF,d.STICKER,d.DOCUMENT_FILE,d.UNAVAILABLE,d.EXPIRED_EPHEMERAL,d.REVOKED,d.XMA,d.RAVEN,d.RECEIVER_FETCH]);j=function(a){return n.has(a)};g.isMAWSupportedMediaType=a;g.isEBSupportedMsgType=h;g.isProtobufUploadSupportedMsgType=b;g.EPHEMERAL_SCREENSHOT_ACTION=c;g.MSG_TYPE=d;g.mawSupportedMediaTypes=k;g.isQuotedMsgType=j}),98);
__d("MAWTraceUtils",["LSDataTraceCheckPoint","LSDataTraceTag","LSIntEnum","LSRequestId","MAWBridge","MAWBridgeTrace","MAWMsgType"],(function(a,b,c,d,e,f,g){"use strict";var h,i="backup_upload",j="media_backup",k="vesta_registration",l="vesta_login";function m(){return c("LSRequestId").generate()}function a(a){return a}function b(a,b,c){c=c!=null?c:m();d("MAWBridge").getBridge().fireAndForget("event","uiUpdate",{events:[{tag:"StartTraceWithTraceId",value:d("MAWBridgeTrace").createBridgeStartTraceWithTraceIdData(a,c,b)}]},!0);return c}function n(a,b,c,e,f){if(a!=null){c=c.length>0?c.map(function(a){return a.toString()}).join(","):void 0;d("MAWBridge").getBridge().fireAndForget("event","uiUpdate",{events:[{tag:"TraceRecordCheckpoint",value:d("MAWBridgeTrace").createBridgeTraceRecordCheckpointData(b,a,f,e,-1,c)}]},!0)}}function e(a,b,e,f){n(a,b,e,!1),n(a,(h||(h=d("LSIntEnum"))).ofNumber(c("LSDataTraceCheckPoint").FLOW_END_FOR_FAILURE),e,!0,f)}function f(a,b,e){n(a,b,e,!1),n(a,(h||(h=d("LSIntEnum"))).ofNumber(c("LSDataTraceCheckPoint").FLOW_END_AND_FLUSH),e,!0)}function o(a,b,e){n(a,b,e,!1),n(a,(h||(h=d("LSIntEnum"))).ofNumber(c("LSDataTraceCheckPoint").FLOW_END_AND_FLUSH),e,!0)}function p(a){switch(a){case d("MAWMsgType").MSG_TYPE.TEXT:return d("LSDataTraceTag").messageTypeText;case d("MAWMsgType").MSG_TYPE.FUTUREPROOF:return d("LSDataTraceTag").messageTypeFutureproof;case d("MAWMsgType").MSG_TYPE.CIPHERTEXT:return d("LSDataTraceTag").messageTypeCiphertext;case d("MAWMsgType").MSG_TYPE.UNAVAILABLE:return d("LSDataTraceTag").messageTypeUnavailable;case d("MAWMsgType").MSG_TYPE.IMAGE:return d("LSDataTraceTag").messageTypeImage;case d("MAWMsgType").MSG_TYPE.VIDEO:return d("LSDataTraceTag").messageTypeVideo;case d("MAWMsgType").MSG_TYPE.PTT:return d("LSDataTraceTag").messageTypePtt;case d("MAWMsgType").MSG_TYPE.ADMIN:return d("LSDataTraceTag").messageTypeAdmin;case d("MAWMsgType").MSG_TYPE.REVOKED:return d("LSDataTraceTag").messageTypeRevoked;case d("MAWMsgType").MSG_TYPE.GIF:return d("LSDataTraceTag").messageTypeGif;case d("MAWMsgType").MSG_TYPE.STICKER:return d("LSDataTraceTag").messageTypeSticker;case d("MAWMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:return d("LSDataTraceTag").messageTypeExpiredEphemeral;case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_ADMIN:return d("LSDataTraceTag").messageTypeEphemeralSettingAdmin;case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SYNC_RESPONSE:return d("LSDataTraceTag").messageTypeEphemeralSyncResponse;case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SCREENSHOT_ACTION:return d("LSDataTraceTag").messageTypeEphemeralScreenshotAction;case d("MAWMsgType").MSG_TYPE.DELETE_FOR_ME:return d("LSDataTraceTag").messageTypeDeleteForMe;case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_CHANGE_FROM_CURRENT_DEVICE:return d("LSDataTraceTag").messageTypeEphemeralSettingChangeFromCurrentDevice;case d("MAWMsgType").MSG_TYPE.ICDC_ALERT:return d("LSDataTraceTag").messageTypeAlertICDC;case d("MAWMsgType").MSG_TYPE.GROUP_INVITE:return d("LSDataTraceTag").messageTypeGroupInvite;case d("MAWMsgType").MSG_TYPE.XMA:return d("LSDataTraceTag").messageTypeXMA;case d("MAWMsgType").MSG_TYPE.REACTION:return d("LSDataTraceTag").messageTypeReaction;case d("MAWMsgType").MSG_TYPE.SK_DISTRIBUTION:return d("LSDataTraceTag").messageTypeSenderKeyDistribution;default:return d("LSDataTraceTag").messageTypeNone}}g.CONTEXT_BACKUP_UPLOAD=i;g.CONTEXT_MEDIA_BACKUP=j;g.CONTEXT_VESTA_REGISTRATION=k;g.CONTEXT_VESTA_LOGIN=l;g.createTraceId=m;g.stringToTraceId=a;g.startNewTrace=b;g.recordCheckpointForTrace=n;g.recordFailureAndFlushForTraceId=e;g.recordSuccessAndFlushForTraceId=f;g.recordInvalidUploadRequestAndFlushTrace=o;g.getTagForMsgType=p}),98);
__d("MAWEncryptedBackupUtils",["FBLogger","I64","LSAuthorityLevel","LSDataTraceCheckPoint","LSDict","LSIntEnum","LSShape","LSVec","MAWTraceUtils","ReQL"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a,b,c,e,f){d("MAWTraceUtils").recordCheckpointForTrace(a,b,c,e,f)}function a(a,b,e){j(b,(h||(h=d("LSIntEnum"))).ofNumber(c("LSDataTraceCheckPoint").LABYRINTH_WEB_GET_BACKUP_TENANCY),[],!1);return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.secure_encrypted_backups_client_state)).then(function(a){return k(a,e)})}function k(a,b){var e;if(a==null){b!=null&&b.addPoint("get_backup_tenancy_null_client_state_row");return}e=(e=a.backupTenancy)!=null?e:(i||(i=d("I64"))).one;a=a.authorityLevel;b!=null&&b.addPoint("get_backup_tenancy",{"int":{authorityLevel:(i||(i=d("I64"))).to_int32(a!=null?a:(i||(i=d("I64"))).zero),backupTenancy:i.to_int32(e!=null?e:(i||(i=d("I64"))).zero)}});if(e!=null&&(i||(i=d("I64"))).equal((h||(h=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").AUTHORITATIVE),a))return e}function b(a,b){var d=new Set(),e=a.length;for(a of a.entries()){var f=a[0],g=a[1];d.has(g)&&c("FBLogger")("labyrinth_web").warn("Duplicate messages returned from server for restoreType: %s, index: %s of batchSize: %s",b,f,e);d.add(g)}}function e(a){a=a.map(function(a){var b=a.toplevelProtobuf;b=d("LSShape").ofRecord({decrypted_protobuf:b.decryptedProtobuf,protobuf_timestamp_ms:(i||(i=d("I64"))).of_float(b.protobufTimestampMS)});var e=new(c("LSDict"))();a.supplementalProtobufs.forEach(function(a,b){e.set(b,d("LSShape").ofRecord({decrypted_protobuf:a.decryptedProtobuf,protobuf_timestamp_ms:(i||(i=d("I64"))).of_float(a.protobufTimestampMS)}))});return d("LSShape").ofRecord({otid:a.otid,supplemental_protobufs:e,toplevel_protobuf:b})});return c("LSVec").ofArray(a)}function f(a){a=c("LSVec").toArray(a);a=a.map(function(a){return d("LSShape").toRecord(a)});return a.map(function(a){var b=d("LSShape").toRecord(a.toplevel_protobuf);b={decryptedProtobuf:b.decrypted_protobuf,protobufTimestampMS:(i||(i=d("I64"))).to_float(b.protobuf_timestamp_ms)};var c=new Map(),e=a.supplemental_protobufs;e.forEach(function(a,b){a=d("LSShape").toRecord(a);c.set(b,{decryptedProtobuf:a.decrypted_protobuf,protobufTimestampMS:(i||(i=d("I64"))).to_float(a.protobuf_timestamp_ms)})});return{otid:a.otid,supplementalProtobufs:c,toplevelProtobuf:b}})}g.getBackupTenancy=a;g.getBackupTenancyFromClientStateRow=k;g.logIfDuplicateMessagesFoundInRestore=b;g.convertJSTypesToLSTypesForRestoreJob=e;g.convertLSTypesToJSTypesForRestoreJob=f}),98);
__d("EBIsEbEnabled",["EBDeps","EBReadyNotifier","FBLogger","I64","LSEncryptedBackupsBackupTenancy","LSIntEnum","MAWEBSwitch","MAWEncryptedBackupUtils","WAResultOrError","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a,b,e){return d("EBReadyNotifier").waitForEBReady().then(function(){return d("MAWEncryptedBackupUtils").getBackupTenancy(a,b,e)}).then(function(a){return a!=null&&(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(c("LSEncryptedBackupsBackupTenancy").PRODUCTION))})}function a(){try{return d("WAResultOrError").makeResult(c("MAWEBSwitch").isEnabled())}catch(a){c("FBLogger")("labyrinth_web").catching(a).mustfix("Failed to check EB enabled");return d("WAResultOrError").makeResult(!1)}}function e(){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=(yield d("EBDeps").getDeps().getLSDB());return j(a.tables)});return k.apply(this,arguments)}g.isEbEnabledLS=j;g.isEbEnabledEbSwitch=a;g.isEBEnabled=e}),98);
__d("EBIsEbEnabledSubscriber",["EBDeps","EBIsEbEnabled","I64","LSEncryptedBackupsBackupTenancy","LSIntEnum","Promise","ReQL","WAPubSub","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=function(){function a(a){this.$1=d("WAPubSub").simplePubSub(),this.$4=a,this.$2=!1}var e=a.prototype;e.initialize=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){this.$2=(yield d("EBIsEbEnabled").isEbEnabledLS(this.$4.tables)),this.$5(this.$4)});function c(){return a.apply(this,arguments)}return c}();e.$6=function(a){this.$2=a,this.$1.publish(a)};e.$5=function(a){var b=this;this.$3==null&&(this.$3=d("ReQL").fromTableAscending(a.tables.secure_encrypted_backups_client_state).take(1).subscribe(function(a,e){e.operation==="delete"&&b.$6(!1),(e.operation==="add"||e.operation==="put")&&(e.value.backupTenancy!=null&&(i||(i=d("I64"))).equal(e.value.backupTenancy,(j||(j=d("LSIntEnum"))).ofNumber(c("LSEncryptedBackupsBackupTenancy").PRODUCTION))?b.$6(!0):b.$6(!1))}));return this.$3};e.subscribe=function(a){return this.$1.subscribe(a)};e.isEnabled=function(){return this.$2};return a}(),l;function m(a){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){if(l!=null)return(h||(h=b("Promise"))).resolve();l=new k(a);yield l.initialize()});return n.apply(this,arguments)}function a(){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){if(l==null){var a=(yield d("EBDeps").getDeps().getLSDB());yield m(a)}return l});return o.apply(this,arguments)}function e(a){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){if(l==null){var b=(yield d("EBDeps").getDeps().getLSDB());yield m(b)}a(l.isEnabled());b=l.subscribe(a);return b});return p.apply(this,arguments)}function f(){return q.apply(this,arguments)}function q(){q=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){if(l==null){var a=(yield d("EBDeps").getDeps().getLSDB());yield m(a)}return l.isEnabled()});return q.apply(this,arguments)}g.initIsEbEnabledPubSub=m;g.isEbEnabledSubscriber=a;g.subscribeToEbChanges=e;g.isEBEnabledValueFromSubscriber=f}),98);
__d("WAProtoConst",[],(function(a,b,c,d,e,f){"use strict";a={REPEATED:1<<6,PACKED:1<<7,REQUIRED:1<<8};b=31;c={INT32:1,INT64:2,UINT32:3,UINT64:4,SINT32:5,SINT64:6,BOOL:7,ENUM:8,FIXED64:9,SFIXED64:10,DOUBLE:11,STRING:12,BYTES:13,MESSAGE:14,FIXED32:15,SFIXED32:16,FLOAT:17,MAP:18};d={VARINT:0,BIT64:1,BINARY:2,BIT32:5};e={ONEOF:"__oneofs__",RESERVED:"__reserved__",RESERVED_TAGS:"tags",RESERVED_FIELDS:"fields"};f.FLAGS=a;f.TYPE_MASK=b;f.TYPES=c;f.ENC=d;f.KEYS=e}),66);
__d("WACommon.pb",["WAProtoConst"],(function(a,b,c,d,e,f,g){a={EVERYONE:1,SILENT:2,AI:3,AI_IMAGINE:4};b={PROFILE:0};c={PLACEHOLDER:0,NO_PLACEHOLDER:1,IGNORE:2};e={};f={};var h={},i={},j={};e.name="MessageKey";e.internalSpec={remoteJid:[1,(d=d("WAProtoConst")).TYPES.STRING],fromMe:[2,d.TYPES.BOOL],id:[3,d.TYPES.STRING],participant:[4,d.TYPES.STRING]};f.name="Command";f.internalSpec={commandType:[1,d.TYPES.ENUM,a],offset:[2,d.TYPES.UINT32],length:[3,d.TYPES.UINT32],validationToken:[4,d.TYPES.STRING]};h.name="Mention";h.internalSpec={mentionType:[1,d.TYPES.ENUM,b],mentionedJid:[2,d.TYPES.STRING],offset:[3,d.TYPES.UINT32],length:[4,d.TYPES.UINT32]};i.name="MessageText";i.internalSpec={text:[1,d.TYPES.STRING],mentionedJid:[2,d.FLAGS.REPEATED|d.TYPES.STRING],commands:[3,d.FLAGS.REPEATED|d.TYPES.MESSAGE,f],mentions:[4,d.FLAGS.REPEATED|d.TYPES.MESSAGE,h]};j.name="SubProtocol";j.internalSpec={payload:[1,d.TYPES.BYTES],version:[2,d.TYPES.INT32]};g.COMMAND_COMMAND_TYPE=a;g.MENTION_MENTION_TYPE=b;g.FUTURE_PROOF_BEHAVIOR=c;g.MessageKeySpec=e;g.CommandSpec=f;g.MentionSpec=h;g.MessageTextSpec=i;g.SubProtocolSpec=j}),98);
__d("WAArmadilloXMA.pb",["WACommon.pb","WAProtoConst"],(function(a,b,c,d,e,f,g){var h;a={INFO:0,EYE_OFF:1,NEWS_OFF:2,WARNING:3,PRIVATE:4,NONE:5,MEDIA_LABEL:6,POST_COVER:7,POST_LABEL:8,WARNING_SCREENS:9};b={OPEN_NATIVE:11};c={SINGLE:0,HSCROLL:1,PORTRAIT:3,STANDARD_DXMA:12,LIST_DXMA:15,GRID:16};e={UNSUPPORTED:-1,IG_STORY_PHOTO_MENTION:4,IG_SINGLE_IMAGE_POST_SHARE:9,IG_MULTIPOST_SHARE:10,IG_SINGLE_VIDEO_POST_SHARE:11,IG_STORY_PHOTO_SHARE:12,IG_STORY_VIDEO_SHARE:13,IG_CLIPS_SHARE:14,IG_IGTV_SHARE:15,IG_SHOP_SHARE:16,IG_PROFILE_SHARE:19,IG_STORY_PHOTO_HIGHLIGHT_SHARE:20,IG_STORY_VIDEO_HIGHLIGHT_SHARE:21,IG_STORY_REPLY:22,IG_STORY_REACTION:23,IG_STORY_VIDEO_MENTION:24,IG_STORY_HIGHLIGHT_REPLY:25,IG_STORY_HIGHLIGHT_REACTION:26,IG_EXTERNAL_LINK:27,IG_RECEIVER_FETCH:28,FB_FEED_SHARE:1e3,FB_STORY_REPLY:1001,FB_STORY_SHARE:1002,FB_STORY_MENTION:1003,FB_FEED_VIDEO_SHARE:1004,FB_GAMING_CUSTOM_UPDATE:1005,FB_PRODUCER_STORY_REPLY:1006,FB_EVENT:1007,FB_FEED_POST_PRIVATE_REPLY:1008,FB_SHORT:1009,FB_COMMENT_MENTION_SHARE:1010,FB_POST_MENTION:1011,FB_PROFILE_DIRECTORY_ITEM:1013,MSG_EXTERNAL_LINK_SHARE:2e3,MSG_P2P_PAYMENT:2001,MSG_LOCATION_SHARING:2002,MSG_LOCATION_SHARING_V2:2003,MSG_HIGHLIGHTS_TAB_FRIEND_UPDATES_REPLY:2004,MSG_HIGHLIGHTS_TAB_LOCAL_EVENT_REPLY:2005,MSG_RECEIVER_FETCH:2006,MSG_IG_MEDIA_SHARE:2007,MSG_GEN_AI_SEARCH_PLUGIN_RESPONSE:2008,MSG_REELS_LIST:2009,MSG_CONTACT:2010,MSG_THREADS_POST_SHARE:2011,MSG_FILE:2012,MSG_AVATAR_DETAILS:2013,MSG_AI_CONTACT:2014,MSG_MEMORIES_SHARE:2015,MSG_SHARED_ALBUM_REPLY:2016,MSG_SHARED_ALBUM:2017,MSG_OCCAMADILLO_XMA:2018,MSG_GEN_AI_SUBSCRIPTION:2021,MSG_GEN_AI_REMINDER:2022,MSG_GEN_AI_MEMU_ONBOARDING_RESPONSE:2023,MSG_NOTE_REPLY:2024,MSG_NOTE_MENTION:2025,GEN_AI_ENTITY:2026,RTC_AUDIO_CALL:3e3,RTC_VIDEO_CALL:3001,RTC_MISSED_AUDIO_CALL:3002,RTC_MISSED_VIDEO_CALL:3003,RTC_GROUP_AUDIO_CALL:3004,RTC_GROUP_VIDEO_CALL:3005,RTC_MISSED_GROUP_AUDIO_CALL:3006,RTC_MISSED_GROUP_VIDEO_CALL:3007,RTC_ONGOING_AUDIO_CALL:3008,RTC_ONGOING_VIDEO_CALL:3009,MSG_RECEIVER_FETCH_FALLBACK:3025,DATACLASS_SENDER_COPY:4e3};f={};var i={};f.name="ExtendedContentMessage";f.internalSpec={associatedMessage:[1,(h=d("WAProtoConst")).TYPES.MESSAGE,(d=d("WACommon.pb")).SubProtocolSpec],targetType:[2,h.TYPES.ENUM,e],targetUsername:[3,h.TYPES.STRING],targetId:[4,h.TYPES.STRING],targetExpiringAtSec:[5,h.TYPES.INT64],xmaLayoutType:[6,h.TYPES.ENUM,c],ctas:[7,h.FLAGS.REPEATED|h.TYPES.MESSAGE,i],previews:[8,h.FLAGS.REPEATED|h.TYPES.MESSAGE,d.SubProtocolSpec],titleText:[9,h.TYPES.STRING],subtitleText:[10,h.TYPES.STRING],maxTitleNumOfLines:[11,h.TYPES.UINT32],maxSubtitleNumOfLines:[12,h.TYPES.UINT32],favicon:[13,h.TYPES.MESSAGE,d.SubProtocolSpec],headerImage:[14,h.TYPES.MESSAGE,d.SubProtocolSpec],headerTitle:[15,h.TYPES.STRING],overlayIconGlyph:[16,h.TYPES.ENUM,a],overlayTitle:[17,h.TYPES.STRING],overlayDescription:[18,h.TYPES.STRING],sentWithMessageId:[19,h.TYPES.STRING],messageText:[20,h.TYPES.STRING],headerSubtitle:[21,h.TYPES.STRING],xmaDataclass:[22,h.TYPES.STRING],contentRef:[23,h.TYPES.STRING],mentionedJid:[24,h.FLAGS.REPEATED|h.TYPES.STRING],commands:[25,h.FLAGS.REPEATED|h.TYPES.MESSAGE,d.CommandSpec],mentions:[26,h.FLAGS.REPEATED|h.TYPES.MESSAGE,d.MentionSpec]};i.name="ExtendedContentMessage$CTA";i.internalSpec={buttonType:[1,h.TYPES.ENUM,b],title:[2,h.TYPES.STRING],actionUrl:[3,h.TYPES.STRING],nativeUrl:[4,h.TYPES.STRING],ctaType:[5,h.TYPES.STRING],actionContentBlob:[6,h.TYPES.STRING]};g.EXTENDED_CONTENT_MESSAGE_OVERLAY_ICON_GLYPH=a;g.EXTENDED_CONTENT_MESSAGE_CTA_BUTTON_TYPE=b;g.EXTENDED_CONTENT_MESSAGE_XMA_LAYOUT_TYPE=c;g.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE=e;g.ExtendedContentMessageSpec=f;g.ExtendedContentMessage$CTASpec=i}),98);
__d("MAWJobDefinitions",["MAWExternalId","MWPBumpEntityKey","WAArmadilloXMA.pb"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return a}function b(a){return a}e={acceptGroupInvite:function(a,b,c,d,e,f){f=i(f);return{args:{chatJid:a,inviteCode:d,invitedParticipantUserId:c,inviteExpirationTs:e,inviterUserId:b},scheduleConfig:f!=null?f:void 0,type:"acceptGroupInvite"}},addGroupParticipants:function(a,b,c,d){d=i(d);return{args:{group:a,s2sInstanceKey:c,users:b},scheduleConfig:d!=null?d:void 0,type:"addGroupParticipants"}},deleteMsgsForMe:function(a,b){b=i(b);return{args:{msgIds:a},scheduleConfig:b!=null?b:void 0,type:"deleteMsgsForMe"}},demoteGroupParticipants:function(a,b,c){c=i(c);return{args:{group:a,users:b},scheduleConfig:c!=null?c:void 0,type:"demoteGroupParticipants"}},downloadAndHandleMedia:function(a,b,c,d,e,f,g,h){f=i(h);return{args:{downloadType:e,hash:b,metricInstanceKeys:g,msgType:d,protocolMsgId:a,qplEntryPoint:c},scheduleConfig:f!=null?f:void 0,type:"downloadAndHandleMedia"}},forwardMsg:function(a,b,c,e,f,g,h){h=i(h);c=c!=null?c:{};var j=c.s2sInstanceKey;c.s2sUserFlowQPLEvent;c=babelHelpers.objectWithoutPropertiesLoose(c,["s2sInstanceKey","s2sUserFlowQPLEvent"]);return{args:{args:c,chatJid:a,ephemeralSetting:e,externalId:f!=null?f:d("MAWExternalId").generateExternalId(),isFirstMsg:g,protocolMsgId:b,s2sInstanceKey:j},scheduleConfig:h!=null?h:void 0,type:"forwardMsg"}},getSpams:function(a){a=i(a);return{args:void 0,scheduleConfig:a!=null?a:void 0,type:"getSpams"}},handleFutureproofMsg:function(a){a=i(a);return{args:{},scheduleConfig:a!=null?a:void 0,type:"handleFutureproofMsg"}},handleSpamMsg:function(a){a=i(a);return{args:void 0,scheduleConfig:a!=null?a:void 0,type:"handleSpamMsg"}},igdReportUserSpam:function(a,b){var c=a.chatJid,d=a.context,e=a.frxParams,f=a.frxTags,g=a.msgs,h=a.reportedMessageId,j=a.spamFlow;a=a.userJid;b=i(b);return{args:{chatJid:c,context:d,frxParams:e,frxTags:f,msgs:g,reportedMessageId:h,spamFlow:j,userJid:a},scheduleConfig:b!=null?b:void 0,type:"igdReportUserSpam"}},igdSendMsg:function(a,b){var c=a.actorID;a=a.taskID;b=i(b);return{args:{actorID:c,taskID:a},scheduleConfig:b!=null?b:void 0,type:"igdSendMsg"}},leaveGroups:function(a,b){b=i(b);return{args:{groups:a},scheduleConfig:b!=null?b:void 0,type:"leaveGroups"}},markThreadAsRead:function(a,b,c,d,e,f){f=i(f);return{args:{chatJid:a,isBlocked:c,isReadReceiptsDisabled:e,isRestricted:d,relationship:b},scheduleConfig:f!=null?f:void 0,type:"markThreadAsRead"}},promoteGroupParticipants:function(a,b,c){c=i(c);return{args:{group:a,users:b},scheduleConfig:c!=null?c:void 0,type:"promoteGroupParticipants"}},removeGroupParticipants:function(a,b,c){c=i(c);return{args:{group:a,users:b},scheduleConfig:c!=null?c:void 0,type:"removeGroupParticipants"}},reportUserSpam:function(a,b,c,d,e,f,g,h,j,k){j=i(j);return{args:{chatJid:c,context:f,frxParams:g,frxTags:e,isIgd:d,openMsgs:k,reportedMessageId:h,spamFlow:b,userJid:a},scheduleConfig:j!=null?j:void 0,type:"reportUserSpam"}},resendWrittenAppData:function(a,b){b=i(b);return{args:{appDataExternalId:a},scheduleConfig:b!=null?b:void 0,type:"resendWrittenAppData"}},restoreMessagesFromEncryptedBackups:function(a,b,c,d,e,f,g,h,j,k,l,m,n,o,p){n=i(n);return{args:{deanonApiPayload:o,hasMoreAfter:e,hasMoreBefore:d,instanceKey:k,isMediaGalleryRestore:p,isPointQuery:f,messages:c,minMsgId:g,optimisticMsgs:l,startSortKey:j,taskSource:m,threadId:b,traceId:h,transportKey:a},scheduleConfig:n!=null?n:void 0,type:"restoreMessagesFromEncryptedBackups"}},restoreProtobufsFromEncryptedBackups:function(a,b,c,d,e,f,g,h,j,k,l,m,n,o,p,q){m=i(m);return{args:{chatJid:q,deanonApiPayload:o,echoMessages:a,hasMoreAfter:f,hasMoreBefore:e,instanceKey:l,isMediaGalleryRestore:p,isPointQuery:g,messages:d,minMsgId:h,referenceTimestamp:k,requestId:j,taskSource:n,threadId:c,transportKey:b},scheduleConfig:m!=null?m:void 0,type:"restoreProtobufsFromEncryptedBackups"}},revokeMsgs:function(a,b){b=i(b);return{args:a,scheduleConfig:b!=null?b:void 0,type:"revokeMsgs"}},sendAppData:function(a,b,c){c=i(c);return{args:{args:a,externalId:b!=null?b:d("MAWExternalId").generateExternalId()},scheduleConfig:c!=null?c:void 0,type:"sendAppData"}},sendBumpMsg:function(a,b,c,e){e=i(e);return{args:{args:b,chatJid:a,externalId:c!=null?c:d("MAWExternalId").generateExternalId()},scheduleConfig:e!=null?e:void 0,type:"sendBumpMsg"}},sendGroupInviteMsg:function(a,b,c){c=i(c);return{args:{args:a,externalId:b!=null?b:d("MAWExternalId").generateExternalId()},scheduleConfig:c!=null?c:void 0,type:"sendGroupInviteMsg"}},sendMediaMsg:function(a,b,c,e,f,g,h,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x){s=i(s);return{args:{args:{duration:k,ephemeralSetting:m,filename:u,height:g,isFirstMsg:q,isForwarded:p,isPtt:l,jpegThumbnail:e,jpegThumbnailHeight:j,jpegThumbnailWidth:h,mediaGroupMetadata:x,offlineAttachmentId:v,openMessageOtid:o,quote:n,s2sInstanceKey:c,source:t,width:f},attachmentType:w,chatJid:a,externalId:r!=null?r:d("MAWExternalId").generateExternalId(),file:b},scheduleConfig:s!=null?s:void 0,type:"sendMediaMsg"}},sendMediaMsgV2:function(a,b,c,e,f,g,h,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y){s=i(s);return{args:{args:{duration:k,ephemeralSetting:m,filename:u,height:g,isFirstMsg:q,isForwarded:p,isPtt:l,jpegThumbnail:e,jpegThumbnailHeight:j,jpegThumbnailWidth:h,mediaGroupMetadata:y,offlineAttachmentId:v,openMessageOtid:o,quote:n,s2sInstanceKey:w,source:t,width:f},attachmentType:x,chatJid:a,externalId:r!=null?r:d("MAWExternalId").generateExternalId(),mimeType:b,plaintext:c},scheduleConfig:s!=null?s:void 0,type:"sendMediaMsgV2"}},sendWrittenAppData:function(a,b){b=i(b);return{args:{appDataExternalId:a},scheduleConfig:b!=null?b:void 0,type:"sendWrittenAppData"}},sendXMAShareMsg:function(a,b,c,e,f,g,h,j,k,l){g.xmaMessageType===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_RECEIVER_FETCH&&k.contentRef==null&&d("MWPBumpEntityKey").bumpEntityKey("maw.sendXMAShareMsg","content_ref_null_for_rf_xma");l=i(l);return{args:{args:g,associatedMsgContent:f,associatedMsgContentExternalId:j!=null?j:d("MAWExternalId").generateExternalId(),chatJid:a,faviconFile:e,headerFile:c,previewFile:b,xmaArgs:k,xmaMsgExternalId:h!=null?h:d("MAWExternalId").generateExternalId()},scheduleConfig:l!=null?l:void 0,type:"sendXMAShareMsg"}},setGroupMemberAddMode:function(a,b,c){c=i(c);return{args:{groupJid:a,memberAddMode:b},scheduleConfig:c!=null?c:void 0,type:"setGroupMemberAddMode"}},setGroupSubject:function(a,b,c){c=i(c);return{args:{group:a,subject:b},scheduleConfig:c!=null?c:void 0,type:"setGroupSubject"}},startInstamadilloDYI:function(a,b){b=i(b);return{args:{qplInstanceKey:a},scheduleConfig:b!=null?b:void 0,type:"startInstamadilloDYI"}},updateBackupEntFbidForMedia:function(a,b,c,d){d=i(d);return{args:{backupEntFbid:b,deliveryObjectId:a,traceId:c},scheduleConfig:d!=null?d:void 0,type:"updateBackupEntFbidForMedia"}}};function c(a,b){return h(a,b)}function h(a,b){var c=b.scheduleConfig,e=babelHelpers.objectWithoutPropertiesLoose(b,["scheduleConfig"]);c={args:{},scheduleConfig:c,type:a};b.externalIdJob&&(c.args.externalId=d("MAWExternalId").generateExternalId());c.args=babelHelpers["extends"]({},c.args,{},e);b.uniqueJob&&(c.uniqKey=a);return c}function i(a){if(a==null)return;return{jobId:a.jobId,maxTimeoutMs:a.maxTimeoutMs,priority:a.priority}}g.toThreadJidPrimaryId=a;g.toEncodedEchoMessage=b;g.jobSerializers=e;g.createStartJobInfo=c;g.createStartJobInfoGeneric=h;g.serializeJobScheduledConfig=i}),98);
__d("WACustomError",[],(function(a,b,c,d,e,f){"use strict";var g=Object.create(Error.prototype,{constructor:{value:void 0,writable:!0,configurable:!0}});function a(a,b,c){b===void 0&&(b=!0);function d(c){var d=Error.call(this,c);this.message=c;d.name=a;this.name=a;b&&(this.stack=d.stack)}d.prototype=Object.create(c?c.prototype:g);d.prototype.constructor=d;return d}function h(a){if(a.length===0)return"No errors";return a.length===1?a[0].message:a.map(function(a){return"- "+a.message}).join("\n")}e=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){c=c!=null&&c.length>0?c:h(b);c=a.call(this,c)||this;c.errors=b;return c}return b}(a("AggregateError",!0));var i=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}return a}(a("TimeoutError",!1)),j=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c){a=b.call(this,a)||this;a.code=c;return a}return a}(a("HttpError",!1)),k=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}return a}(a("UnimplementedMethod")),l=!1;function b(a){if(a==null||a.name!=="QuotaExceededError")throw a;l=!0}function c(){return l}function d(a){return JSON.stringify(a,Object.getOwnPropertyNames(a))}var m=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c){a=b.call(this,a)||this;a.inner=c;return a}return a}(babelHelpers.wrapNativeSuper(Error));f.customError=a;f.AggregateError=e;f.TimeoutError=i;f.HttpError=j;f.UnimplementedMethod=k;f.supressQuotaExceededError=b;f.hasSupressedQuotaExceededError=c;f.obtainErrorSummary=d;f.WrappedError=m}),66);
__d("EBMessagePointQueryWithPersistenceQuery_facebookRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9266310850161351"}),null);
__d("EBMessagePointQueryWithPersistenceQuery.graphql",["EBMessagePointQueryWithPersistenceQuery_facebookRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={defaultValue:null,kind:"LocalArgument",name:"app_id"},c={defaultValue:null,kind:"LocalArgument",name:"restore_payload_string"},d={defaultValue:null,kind:"LocalArgument",name:"restore_type"},e=[{kind:"Variable",name:"app_id",variableName:"app_id"},{kind:"Variable",name:"restore_payload_string",variableName:"restore_payload_string"},{kind:"Variable",name:"restore_type",variableName:"restore_type"}],f={alias:null,args:null,kind:"ScalarField",name:"encryption_version",storageKey:null},g={alias:null,args:null,kind:"ScalarField",name:"epoch_anon_id",storageKey:null},h={alias:null,args:null,kind:"ScalarField",name:"epoch_id",storageKey:null},i={alias:null,args:null,kind:"ScalarField",name:"encrypted_protobuf_stanza",storageKey:null},j={alias:null,args:null,kind:"ScalarField",name:"protobuf_timestamp_ms",storageKey:null},k={alias:null,args:null,kind:"ScalarField",name:"sk_ciphertext",storageKey:null},l=[g,h];return{fragment:{argumentDefinitions:[a,c,d],kind:"Fragment",metadata:null,name:"EBMessagePointQueryWithPersistenceQuery",selections:[{alias:null,args:null,concreteType:"Viewer",kind:"LinkedField",name:"viewer",plural:!1,selections:[{alias:null,args:null,concreteType:"XFBEncryptedBackup",kind:"LinkedField",name:"encrypted_backup",plural:!1,selections:[{alias:null,args:null,concreteType:"XFBEncryptedBackupMailbox",kind:"LinkedField",name:"mailbox",plural:!1,selections:[{alias:null,args:e,concreteType:"XFBEncryptedBackupMessages",kind:"LinkedField",name:"messages_point_restore",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"handleRestoreMessagesGraphQLResponse_XFBEncryptedBackupMessages"}],storageKey:null}],storageKey:null}],storageKey:null}],storageKey:null}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[c,d,a],kind:"Operation",name:"EBMessagePointQueryWithPersistenceQuery",selections:[{alias:null,args:null,concreteType:"Viewer",kind:"LinkedField",name:"viewer",plural:!1,selections:[{alias:null,args:null,concreteType:"XFBEncryptedBackup",kind:"LinkedField",name:"encrypted_backup",plural:!1,selections:[{alias:null,args:null,concreteType:"XFBEncryptedBackupMailbox",kind:"LinkedField",name:"mailbox",plural:!1,selections:[{alias:null,args:e,concreteType:"XFBEncryptedBackupMessages",kind:"LinkedField",name:"messages_point_restore",plural:!1,selections:[{alias:null,args:null,concreteType:"XFBEncryptedBackupMessage",kind:"LinkedField",name:"encrypted_messages",plural:!0,selections:[{alias:null,args:null,concreteType:"XFBEchoDocument",kind:"LinkedField",name:"echo_document",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"echo_document_string",storageKey:null},f,g,h,{alias:null,args:null,kind:"ScalarField",name:"epoch_fingerprint",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"otid",storageKey:null},{alias:null,args:null,concreteType:"XFBProtobufStanzas",kind:"LinkedField",name:"protobuf_stanzas",plural:!1,selections:[{alias:null,args:null,concreteType:"XFBProtobufStanza",kind:"LinkedField",name:"top_level_protobuf",plural:!1,selections:[i,f,g,h,j,k],storageKey:null},{alias:null,args:null,concreteType:"XFBProtobufStanza",kind:"LinkedField",name:"supplemental_protobufs",plural:!0,selections:[i,f,g,h,j,k,{alias:null,args:null,kind:"ScalarField",name:"supplemental_key",storageKey:null}],storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"backup_id",storageKey:null},{alias:null,args:null,concreteType:"XFBMessageRangeInfo",kind:"LinkedField",name:"message_range_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"has_more_before",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_more_after",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"next_message_timestamp_ms_before",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"next_message_timestamp_ms_after",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"should_delete_mailbox",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"exception_string",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"thread_not_found",storageKey:null},{alias:null,args:null,concreteType:"XFBEpochDerivationSet",kind:"LinkedField",name:"epoch_derivation_set",plural:!1,selections:[{alias:null,args:null,concreteType:"XFBEncryptedBackupsEpochEdge",kind:"LinkedField",name:"epoch_edges",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"backward_edge",storageKey:null},{alias:null,args:null,concreteType:"XFBEpochForwardEdge",kind:"LinkedField",name:"forward_edge",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"auth_public_key",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"encrypted_entropy",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"entropy_fingerprint",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"epoch_storage_public_key",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"psk_fingerprint",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XFBEpochIds",kind:"LinkedField",name:"from_epoch",plural:!1,selections:l,storageKey:null},{alias:null,args:null,concreteType:"XFBEpochIds",kind:"LinkedField",name:"to_epoch",plural:!1,selections:l,storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"from_epoch_fingerprint",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"to_epoch_fingerprint",storageKey:null}],storageKey:null}],storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null}],storageKey:null}],storageKey:null}]},params:{id:b("EBMessagePointQueryWithPersistenceQuery_facebookRelayOperation"),metadata:{},name:"EBMessagePointQueryWithPersistenceQuery",operationKind:"query",text:null}}}();e.exports=a}),null);
__d("MAWReliabilityMonitor",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum").Mirrored(["PENDING","OK","ERROR","UNKNOWN"]);c=function(){function a(){this.$1=new Map(),this.$2=new Map(),this.$3=[]}var b=a.prototype;b.startMonitoring=function(a,b){b=new h(a,b,this);this.$1.set(a,b);this.recordReport(b);return b};b.stopMonitoring=function(a){this.$1["delete"](a.identifier),this.$2["delete"](a.identifier)};b.recordReport=function(a){this.$2.set(a.identifier,babelHelpers["extends"]({},a.selfAssessmentFn(),{identifier:a.identifier,timestamp:Date.now()})),this.$4()};b.onAggregatedReportChange=function(a){this.$3.push(a)};b.$4=function(){var a=this.getAggregatedReportEntity();this.$3.forEach(function(b){return b(a)})};b.getAggregatedReportEntity=function(){return new g(this.$2)};b.triggerAllSelfAssessments=function(){var a=this;this.$1.forEach(function(b){a.recordReport(b)})};b.getAggregatedReport=function(){this.triggerAllSelfAssessments();return this.$2};b.TEST_ONLY_reset=function(){this.$1.clear(),this.$2.clear(),this.$3=[]};return a}();var g=function(){function a(a){this.$1=a}var b=a.prototype;b.toAckPayloadFormat=function(){return Array.from(this.$1.values())};b.toString=function(){var a=Object.fromEntries(this.$1);return JSON.stringify(a)};return a}(),h=function(){function a(a,b,c){this.identifier=a,this.selfAssessmentFn=b,this.$1=c}var b=a.prototype;b.stopMonitoring=function(){this.$1.stopMonitoring(this)};b.updateState=function(){this.$1.recordReport(this)};return a}();d=new c();f.HealthReportState=a;f.ReliabilityAggregatedReport=g;f.ReportableModule=h;f.MAWReliabilityMonitorSingleton=d}),66);
__d("getMWEncryptedBackupsIsLocalStorageSupported",["FBLogger","WebStorage"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(){if((h||(h=c("WebStorage"))).getLocalStorage()==null){c("FBLogger")("labyrinth_web").warn("Local storage not supported");return!1}if((h||(h=c("WebStorage"))).isLocalStorageQuotaExceeded()){c("FBLogger")("labyrinth_web").warn("Local storage quota exceeded");return!1}return!0}g.getMWEncryptedBackupsIsLocalStorageSupported=a}),98);
__d("MWEncryptedBackupsLocalStorageEntryEnum",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum")({INTERSTITIAL_DIALOG_VERIFY_DEVICE_NUX_DISMISSED:"maw_eb_interstitial_dialog_verify_device_nux_dismissed",VERIFY_DEVICE_NUX_DISMISSED:"maw_eb_verify_device_in_thread_nux_dismissed",ENCRYPTED_BACKUPS_OPT_OUT:"maw_encrypted_backups_opt_out",MW_ENCRYPTED_BACKUPS_USER_CLICKED_ON_SETTINGS:"mw_encrypted_backups_user_clicked_on_settings",MW_ENCRYPTED_BACKUPS_HIGH_FRICTION_RESTORE_DISMISSED:"mw_encrypted_backups_high_friction_restore__dismissed",ENCRYPTED_BACKUPS_TURN_OFF_SECURE_STORAGE:"mw_encrypted_backups_turn_off_secure_storage",ENCRYPTED_BACKUPS_REMEMBER_BROWSER_CONSENT:"mw_encrypted_backups_remember_browser_consent",ENCRYPTED_BACKUPS_RESTORED_SUCCESSFULLY:"maw_eb_restored_successfully",MW_EB_SHOULD_NOT_DISPLAY_SOFTBLOCK:"mw_eb_should_not_display_softblock",MW_EB_HAS_RESTORED_IN_CURRENT_SESSION:"maw_eb_has_restored_in_current_session",MW_EB_HAS_RESTORED_IN_CURRENT_SESSION_2:"maw_eb_has_restored_in_current_session_temp",MW_EB_HAS_SET_UP_IN_CURRENT_SESSION:"maw_eb_has_set_up_in_current_session",ENCRYPTED_BACKUPS_ONBOARDED_SUCCESSFULLY:"maw_eb_onboarded_successfully",EB_ONBOARDING_DEFER_UPSELL_UNTIL_TIME:"mw_eb_onboarding_defer_upsell_until_time",EB_ONBOARDING_QP_DISMISSED:"mw_eb_onboarding_qp_dismissed",EB_ONBOARDING_CHAT_TAB_QP_DISMISSED:"mw_eb_onboarding_chat_tab_qp_dismissed",EB_POST_DECISION_WEB_ONLY_ONBOARDING:"mw_eb_post_decision_web_only_onboarding",EB_MOBILE_ONBOARDING_QP_DISMISSED:"mw_eb_mobile_onboarding_qp_dismissed",EB_MOBILE_ONBOARDING_PERSISTENT_BANNER_DISMISSED:"mw_eb_mobile_onboarding_persistent_banner_dismissed",EBSM_CORRUPTION_WIPE:"ebsm_corruption_wipe",MW_EB_HAS_OPTED_OUT_OF_AUTO_RESTORE:"mw_eb_has_opted_out_of_auto_restore",MW_EB_PRECUTOVER_BANNER_ON_OPEN_THREAD_DISMISSED:"mw_eb_precutover_banner_on_open_thread_dismissed",MW_EB_POSTCUTOVER_BANNER_ON_SECURE_THREAD_DISMISSED:"mw_eb_postcutover_banner_on_secure_thread_dismissed",VERIFY_KEYS_IN_CHAT_ENABLED:"mw_eb_verify_keys_in_chat_enabled"});c=a;f["default"]=c}),66);
__d("EBMessagePointQueryWithPersistence",["Base64Utils","EBAPIQPLPoints","EBAPISharedUtils","EBAPIWorkerCheck","EBIsEbEnabled","EBMessagePointQueryWithPersistenceQuery.graphql","EBSMDBAPI","EBSMHydrationUtils","I64","LSDecryptMessageProtobufsStoredProcedure","LSDecryptMessagesStoredProcedure","LSDeleteAndReenrollDeviceStateStoredProcedure","LSEncryptedBackupsBackupTenancy","LSFactory","LSIntEnum","LSMEBDeviceReenrollmentReason","LSMEBTaskCreationSource","LSShape","LSVec","MAWBridge","MAWCurrentUser","MAWEBRestoreTrackingUtils","MAWEncryptedBackupUtils","MAWJobDefinitions","MAWMainTraceUtils","QPLUserFlow","WAHashStringToNumber","WALogger","WAResultOrError","WorkerRelay","WorkerRelayNetwork","asyncToGeneratorRuntime","cr:14165","gkx","handleRestoreMessagesGraphQLResponse","promiseDone","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] graphQL point restore error ",""]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["[protobuf-only] decryptMessageProtobufs failed with error: ",""]);l=function(){return a};return a}function m(){var a=babelHelpers.taggedTemplateLiteralLoose(["[protobuf-only] decryptMessageProtobufs failed with an error"]);m=function(){return a};return a}function n(){var a=babelHelpers.taggedTemplateLiteralLoose(["[echo-only][echo] decryptMessageEcho failed with error: ",""]);n=function(){return a};return a}function o(){var a=babelHelpers.taggedTemplateLiteralLoose(["[echo-only][echo] decryptMessageEcho failed with an error."]);o=function(){return a};return a}function p(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] message range info is not complete - cannot proceed with graphQL restore"]);p=function(){return a};return a}function q(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] backup_id is null during graphQL restore"]);q=function(){return a};return a}function r(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] message range info is null - cannot proceed with graphQL restore"]);r=function(){return a};return a}function s(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] server side exception during graphql restore - ",""]);s=function(){return a};return a}function t(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] GQL worker point restore query returned null for message response"]);t=function(){return a};return a}function u(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] mandatory params of client state are null"]);u=function(){return a};return a}function v(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] No client state found - unable to restore"]);v=function(){return a};return a}function w(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] No client state found - unable to restore"]);w=function(){return a};return a}function x(){var a=babelHelpers.taggedTemplateLiteralLoose(["[labyrinth_web] Calling EBLS without EBLS being available for message point query"]);x=function(){return a};return a}var y=h!==void 0?h:h=b("EBMessagePointQueryWithPersistenceQuery.graphql");function a(a){return z.apply(this,arguments)}function z(){z=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var e=a.jid,g=a.messageId,h=a.sortOrderMs,z=a.source,A=a.threadId;a=a.traceId;if(!d("EBAPIWorkerCheck").runningInWorker())return d("WAResultOrError").makeError("unsupported-context");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql");var B=a!=null?a:d("MAWMainTraceUtils").createTraceId(),C=d("WAHashStringToNumber").hashStringToNumber(B),D=c("qpl")._(521474469,"2612");c("QPLUserFlow").start(D,{annotations:{bool:{mainThreadRestore:!1,workerThreadRestore:!0},"int":{source:z!=null?z:c("LSMEBTaskCreationSource").UNKNOWN}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestorePoint(z,B,"GQL_WORKER",!0);try{var E;if(b("cr:14165")==null){d("WALogger").ERROR(x());c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.EBLS_NOT_INITIALIZED,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.EBLS_NOT_INITIALIZED}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,"ebls_not_initialized");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("ebls-not-initialized")}c("QPLUserFlow").addPoint(D,"before_ebls_initialization",{instanceKey:C});yield b("cr:14165").genLSClient();c("QPLUserFlow").addPoint(D,"after_ebls_initialization",{instanceKey:C});var F=(yield b("cr:14165").getLSStorage()),G=(yield d("EBIsEbEnabled").isEbEnabledLS(F.tables));if(c("gkx")("2489")){var H=d("EBSMDBAPI").getEBLSDB();H=(yield d("EBSMHydrationUtils").loadEBdataToEphemeralRestore(H));var I=d("EBAPISharedUtils").getClientStateFromPersistedEBSM(H);H=d("EBAPISharedUtils").getAvailableEpochsFromPersistedEBSM(H);if(I===null){d("WALogger").ERROR(w());c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.NO_CLIENT_STATE,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.NO_CLIENT_STATE}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,"no_client_state");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("no-client-state")}var J=G!=null?G:d("EBAPISharedUtils").isBackupTenancyAuthoritative(I);if(!J){c("QPLUserFlow").addPoint(D,"eb_not_enabled",{instanceKey:C});c("QPLUserFlow").endSuccess(D,{instanceKey:C});d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"eb_not_enabled",traceId:B});d("MAWEBRestoreTrackingUtils").markEBRestoreSuccess(B,!0);d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.success");return d("WAResultOrError").makeResult()}J=d("EBAPISharedUtils").getDeviceAndKeyMetadata(I,H)}else{I=(yield d("MAWEncryptedBackupUtils").getBackupTenancy(F.tables));H=G!=null?G:I!=null&&(i||(i=d("I64"))).equal(I,(j||(j=d("LSIntEnum"))).ofNumber(c("LSEncryptedBackupsBackupTenancy").PRODUCTION));if(!H){c("QPLUserFlow").addPoint(D,"eb_not_enabled",{instanceKey:C});c("QPLUserFlow").endSuccess(D,{instanceKey:C});d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"eb_not_enabled",traceId:B});d("MAWEBRestoreTrackingUtils").markEBRestoreSuccess(B,!0);d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.success");return d("WAResultOrError").makeResult()}d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"lsdb_singleton_retrieved",traceId:B});c("QPLUserFlow").addPoint(D,"lsdb_singleton_retrieved",{instanceKey:C});J=(yield d("handleRestoreMessagesGraphQLResponse").getClientState(F))}if(J==null){d("WALogger").ERROR(v());c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.NO_CLIENT_STATE,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.NO_CLIENT_STATE}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,"no_client_state");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("no-client-state")}G=J;I=G.backupId;var K=G.device_id;H=G.locally_available_epochs;var L=G.mailboxRootKey;G=G.ocmfClientStateBlob;if(I==null||K==null||L==null||G==null){d("WALogger").ERROR(u());c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.INVALID_CLIENT_STATE,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.INVALID_CLIENT_STATE}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.INVALID_CLIENT_STATE);d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("invalid-client-state")}d("MAWEBRestoreTrackingUtils").addQPLRestorePointWithAnnotationsWorker({annotations:{string:{device_id:(i||(i=d("I64"))).to_string((E=(E=J)==null?void 0:E.device_id)!=null?E:(i||(i=d("I64"))).zero)}},pointName:"client_state_retrieved",traceId:B});c("QPLUserFlow").addPoint(D,"client_state_retrieved",{data:{string:{device_id:i.to_string((J=(E=J)==null?void 0:E.device_id)!=null?J:(i||(i=d("I64"))).zero)}},instanceKey:C});E=d("MAWCurrentUser").getAppID();d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"app_id_retrieved",traceId:B});c("QPLUserFlow").addPoint(D,"app_id_retrieved",{instanceKey:C});J={restore_context:{act_thread_id:d("MAWJobDefinitions").toThreadJidPrimaryId(A),site:"www",tam_thread_subtype:0},success:babelHelpers["extends"]({device_context:{device_id:i.to_string(K),locally_available_epochs:H,raw_tokens:{mailbox_root_key:d("Base64Utils").fromArrayBuffer(L),ocmf_client_state_blob:d("Base64Utils").fromArrayBuffer(G)}},message_id:g},h!=null?{reference_timestamp:h}:{})};d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"graphql_query_start",traceId:B});c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.GRAPHQL_QUERY_START,{instanceKey:C});yield d("WorkerRelayNetwork").createWorkerNetworkExecute();H=(yield d("WorkerRelay").createWorkerQuery(y,{app_id:String(E!=null?E:"0"),restore_payload_string:JSON.stringify(J),restore_type:"POINT_QUERY_RESTORE"}));d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"graphql_query_end",traceId:B});c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.GRAPHQL_QUERY_END,{instanceKey:C});if(H==null||(H==null?void 0:(L=H.viewer)==null?void 0:(G=L.encrypted_backup)==null?void 0:G.mailbox)==null){c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.INVALID_GRAPHQL_RESPONSE,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.INVALID_GRAPHQL_RESPONSE}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,"gql_point_restore_exception");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("invalid-graphql-response")}L=H==null?void 0:(g=H.viewer)==null?void 0:(E=g.encrypted_backup)==null?void 0:(J=E.mailbox)==null?void 0:J.messages_point_restore;if(L==null){d("WALogger").ERROR(t());c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.INVALID_GRAPHQL_RESPONSE,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.INVALID_GRAPHQL_RESPONSE}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,"gql_point_restore_exception");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("invalid-graphql-response")}G=L;if((G==null?void 0:G.encrypted_messages)==null){c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.NULL_MESSAGES,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.NULL_MESSAGES}},instanceKey:C});d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"null_messages",traceId:B});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,"null_messages");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("invalid-graphql-response")}H=G.backup_id;g=G.encrypted_messages;E=G.epoch_derivation_set;J=G.exception_string;L=G.message_range_info;G=G.should_delete_mailbox;if(J!=null){d("WALogger").ERROR(s(),J);G===!0&&(c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.DELETE_MAILBOX,{instanceKey:C}),yield F.runInTransaction(function(a){return c("LSDeleteAndReenrollDeviceStateStoredProcedure")(c("LSFactory")(a),{reenrollmentReason:(j||(j=d("LSIntEnum"))).ofNumber(c("LSMEBDeviceReenrollmentReason").RESTORE_FAILED_WITH_NO_DEVICE_FOUND),targetDeviceId:K})},"readwrite",void 0,void 0,f.id+":540"),d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"delete_mailbox",traceId:B}));c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.SERVER_SIDE_EXCEPTION,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.SERVER_SIDE_EXCEPTION,exception:J}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.SERVER_SIDE_EXCEPTION,{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.SERVER_SIDE_EXCEPTION,exception:J}});d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("server-side-exception")}d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"derive_and_store_epochs_start",traceId:B});c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.DERIVE_AND_STORE_EPOCHS_START,{instanceKey:C});c("QPLUserFlow").addAnnotations(D,{bool:{hasEpochs:E!=null&&E.epoch_edges.length>0}},{instanceKey:C});yield d("handleRestoreMessagesGraphQLResponse").deriveAndStoreEpochs(E,F,K,B,!0);d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"derive_and_store_epochs_end",traceId:B});c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.DERIVE_AND_STORE_EPOCHS_END,{instanceKey:C});g.length===0&&(c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.NO_MESSAGES_TO_RESTORE,{instanceKey:C}),d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"no_messages",traceId:B}));if(L==null){d("WALogger").ERROR(r());c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.MISSING_RANGE_INFO,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.MISSING_RANGE_INFO}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,"missing_message_range_info");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("missing-message-range-info")}H==null&&(d("WALogger").WARN(q()),c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.BACKUP_ID_NULL,{instanceKey:C}),d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"backup_id_null",traceId:B}));G=L.has_more_after;J=L.has_more_before;E=L.next_message_timestamp_ms_after;L=L.next_message_timestamp_ms_before;if(J==null||G==null||E==null||L==null){d("WALogger").ERROR(p());c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.MISSING_RANGE_INFO,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.MISSING_RANGE_INFO}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,"message_range_info_null");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("missing-message-range-info")}g=d("handleRestoreMessagesGraphQLResponse").processMessageArray(g,H!=null?H:(i||(i=d("I64"))).to_string(I),D,C);var M=g.encryptedLSEchoMessages,N=g.encryptedMessagesWithProtobufs;if(N.length===0){d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"decrypt_messages_echo_start",traceId:B});c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.DECRYPT_MESSAGES_ECHO_START,{instanceKey:C});H=(yield F.runInTransaction(function(a){return c("LSDecryptMessagesStoredProcedure")(c("LSFactory")(a),{actThreadId:A,encryptedMessages:c("LSVec").ofArray(M)})},"readwrite",void 0,void 0,f.id+":754"));I=H[0];g=H[1];if(g!=null){H=d("LSShape").toRecord(g);d("WALogger").ERROR(o());d("WALogger").WARN(n(),H.error_message);c("QPLUserFlow").endFailure(D,"ECHO_DECRYPTION_FAILURE",{annotations:{string:{error_description:H.error_message}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(a,!1,"ECHO_DECRYPTION_FAILURE",{string:{error_description:H.error_message}});d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("echo-decryption-failure")}d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"decrypt_messages_echo_end",traceId:B});c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.DECRYPT_MESSAGES_ECHO_END,{instanceKey:C});g=I;if(g!=null){H=c("LSVec").toArray(g).map(function(a){return d("LSShape").toRecord(a).value});c("promiseDone")(d("MAWBridge").getBridge().sendAndReceive("event","uiUpdate",{events:[{tag:"RestoreNativeOp",value:{chatJid:e,echoMessages:c("LSVec").ofArray(H),hasMoreAfter:G,hasMoreBefore:J,isPointQuery:!0,nextMessageTimestampMsAfter:(i||(i=d("I64"))).of_string(E),nextMessageTimestampMsBefore:i.of_string(L),referenceTimestamp:h!=null?(i||(i=d("I64"))).to_string(h):void 0,requestId:B,taskSource:(j||(j=d("LSIntEnum"))).ofNumber(z!=null?z:c("LSMEBTaskCreationSource").UNKNOWN),threadId:A}}]}))}}else{d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"decrypt_messages_protobuf_start",traceId:B});c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.DECRYPT_MESSAGES_PROTOBUF_START,{instanceKey:C});I=(yield F.runInTransaction(function(a){return c("LSDecryptMessageProtobufsStoredProcedure")(c("LSFactory")(a),{actThreadId:A,encryptedMessagesWithProtobufs:c("LSVec").ofArray(N)})},"readwrite",void 0,void 0,f.id+":858"));g=I[0];H=I[1];if(H!=null){F=d("LSShape").toRecord(H);d("WALogger").ERROR(m());d("WALogger").WARN(l(),F.error_message);c("QPLUserFlow").endFailure(D,"PROTOBUF_DECRYPTION_FAILURE",{annotations:{string:{error_description:F.error_message}},instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(a,!1,"PROTOBUF_DECRYPTION_FAILURE",{string:{error_description:F.error_message}});d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("protobuf-decryption-failure")}d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"decrypt_messages_protobuf_end",traceId:B});c("QPLUserFlow").addPoint(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.DECRYPT_MESSAGES_PROTOBUF_END,{instanceKey:C});if(g!=null){I=d("MAWEncryptedBackupUtils").convertLSTypesToJSTypesForRestoreJob(g);c("promiseDone")(d("MAWBridge").getBridge().sendAndReceive("event","uiUpdate",{events:[{tag:"RestoreNativeOp",value:{chatJid:e,decryptedMessagesProtobufs:I,hasMoreAfter:G,hasMoreBefore:J,isPointQuery:!0,nextMessageTimestampMsAfter:(i||(i=d("I64"))).of_string(E),nextMessageTimestampMsBefore:i.of_string(L),referenceTimestamp:h!=null?(i||(i=d("I64"))).to_string(h):void 0,requestId:B,taskSource:(j||(j=d("LSIntEnum"))).ofNumber(z!=null?z:c("LSMEBTaskCreationSource").UNKNOWN),threadId:A}}]}))}else{c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.DECRYPTED_PROTOBUFS_NULL,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.DECRYPTED_PROTOBUFS_NULL}},instanceKey:C});d("MAWEBRestoreTrackingUtils").addQPLRestorePointWorker({pointName:"decrypted_protobufs_null",traceId:B});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,"decrypted_protobufs_null");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").makeError("invalid-graphql-response")}}c("QPLUserFlow").endSuccess(D,{annotations:{bool:{isEcho:N.length===0,isProtobuf:N.length>0}},instanceKey:C});d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.success");return d("WAResultOrError").makeResult()}catch(a){d("WALogger").ERROR(k(),a);c("QPLUserFlow").endFailure(D,d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.RUNTIME_ERROR,{annotations:{string:{error_description:d("EBAPIQPLPoints").EBMessageRestoreQueryQPLPoints.RUNTIME_ERROR,errorStackTrace:(H=a.stack)!=null?H:""}},error:a,instanceKey:C});d("MAWEBRestoreTrackingUtils").markEBRestoreFail(B,!0,"gql_point_restore_exception");d("EBAPISharedUtils").bumpOdsRestoreKey("restore.request.point.gql.error");return d("WAResultOrError").DEPRECATED_makeError("runtime-error",a)}});return z.apply(this,arguments)}g.pointRestoreQuery=y;g.messagePointQueryWithPersistence=a}),98);
__d("FuncChannel",["FBLogger","Promise"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(){function a(a){var d=this,e;this.$2=[];this.callMessageHandler=function(a){return new(h||(h=b("Promise")))(function(b,c){a.result={resolveFunc:b,rejectFunc:c},d.$2.push(a)})};this.messageToCall=function(a){var b,e,f=d.proxyMethods[a.method];if(typeof f!=="function"){c("FBLogger")("worker").mustfix("proxyMethods[%s] is not a functions",a.method);return}b=(b=a.result)==null?void 0:b.resolveFunc;e=(e=a.result)==null?void 0:e.rejectFunc;if(typeof b==="function"&&typeof e==="function")try{var g=f.apply(a.thisArg,a.argList);b(g)}catch(a){e(a)}else return f.apply(a.thisArg,a.argList)};var f=this,g={},i=(e=a==null?void 0:a(g))!=null?e:{},j=babelHelpers["extends"]({},g);function k(a){j[a]=function(){var b={type:"call",method:a,thisArg:null,argList:Array.from(arguments)};return f.callMessageHandler(b)},g[a]=function(){var b=f.$1[a];if(typeof b!=="function"){c("FBLogger")("worker").mustfix("_backend[%s] is not a functions",a);return}return b.apply(f.$1,arguments)},i[a]==null&&(i[a]=g[a])}this.$1=j;if(a!=null){e=Object.keys(i);e.forEach(k);this.proxyMethods=i}else this.proxyMethods=new Proxy(i,{get:function(a,b){a=b;i[a]==null&&k(a);return i[a]}})}var d=a.prototype;d.flushBuffer=function(){var a=this.$2;this.$2=[];a.forEach(this.messageToCall)};d.setBackend=function(a){this.$1!==a&&(this.$1=a,this.flushBuffer());return this};d.setCallMessageHandler=function(a){this.callMessageHandler=a,this.flushBuffer()};return a}();g["default"]=a}),98);
__d("IGDInteractionTraceAnnotations",["I64","IGDInstamadilloUtils","IGDRouteProvider.react","IGDThreadTTLCUtils","Int64Hooks","InteractionTracing","LSIntEnum","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(h||d("react")).useEffect;function a(a){return babelHelpers["extends"]({},d("IGDThreadTTLCUtils").getTTLCBooleanAnnotations(a),{is_dm:d("IGDInstamadilloUtils").isIGDDisappearingModeEnabled(a),is_instamadillo:d("IGDInstamadilloUtils").isInstamadilloTransportEnabled(a),is_instamadillo_tlc:d("IGDInstamadilloUtils").isInstamadilloCutover(a)})}function l(a,b){var c=d("IGDInstamadilloUtils").isInstamadilloCutover(a),e=d("IGDInstamadilloUtils").isInstamadilloTransportEnabled(a),f=d("IGDInstamadilloUtils").isIGDDisappearingModeEnabled(a);b.addAnnotationBoolean("is_instamadillo",e);b.addAnnotationBoolean("is_instamadillo_tlc",c);b.addAnnotationBoolean("is_dm",f);b.addAnnotationBoolean("is_instamadillo_ttlc",(e=d("IGDThreadTTLCUtils")).isIGDTTLCEnabledForThread(a));b.addAnnotationBoolean("is_instamadillo_ttlc_audio",e.isInstamadilloTTLCAudioEnabled(a));b.addAnnotationBoolean("is_instamadillo_ttlc_clip",e.isInstamadilloTTLCClipEnabled(a));b.addAnnotationBoolean("is_instamadillo_ttlc_generic_xma",e.isInstamadilloTTLCGenericXmaEnabled(a));b.addAnnotationBoolean("is_instamadillo_ttlc_image",e.isInstamadilloTTLCImageEnabled(a));b.addAnnotationBoolean("is_instamadillo_ttlc_link",e.isInstamadilloTTLCLinkEnabled(a));b.addAnnotationBoolean("is_instamadillo_ttlc_media_share",e.isInstamadilloTTLCMediaShareEnabled(a));b.addAnnotationBoolean("is_instamadillo_ttlc_profile",e.isInstamadilloTTLCProfileEnabled(a));b.addAnnotationBoolean("is_instamadillo_ttlc_reel_share",e.isInstamadilloTTLCReelShareEnabled(a));b.addAnnotationBoolean("is_instamadillo_ttlc_story_share",e.isInstamadilloTTLCStoryShareEnabled(a));b.addAnnotationBoolean("is_instamadillo_ttlc_text",e.isInstamadilloTTLCTextEnabled(a));b.addAnnotationBoolean("is_instamadillo_ttlc_video",e.isInstamadilloTTLCVideoEnabled(a))}function b(a){var b=d("react-compiler-runtime").c(3),e,f;b[0]!==a?(e=function(){c("InteractionTracing").getPendingInteractions().forEach(function(b){l(a,b)})},f=[a],b[0]=a,b[1]=e,b[2]=f):(e=b[1],f=b[2]);d("Int64Hooks").useEffectInt64(e,f)}var m=new Set();function e(a){var b=d("react-compiler-runtime").c(7),e;b[0]!==a.consistentThreadFbid||b[1]!==a.threadKey||b[2]!==a.threadSubtype||b[3]!==a.threadType?(e=function(){var b=(i||(i=d("I64"))).to_string(a.threadKey);c("InteractionTracing").getPendingInteractions().forEach(function(c){var e=m.has(b);c.addAnnotationBoolean("is_thread_visited",e);c.addAnnotation("thread_key",(i||(i=d("I64"))).to_string(a.threadKey));a.consistentThreadFbid!=null&&c.addAnnotation("consistent_thread_fbid",(i||(i=d("I64"))).to_string(a.consistentThreadFbid));c.addAnnotationInt("thread_type",(j||(j=d("LSIntEnum"))).toNumber(a.threadType));a.threadSubtype!=null&&c.addAnnotation("thread_subtype",(i||(i=d("I64"))).to_string(a.threadSubtype))});return function(){m.add(b)}},b[0]=a.consistentThreadFbid,b[1]=a.threadKey,b[2]=a.threadSubtype,b[3]=a.threadType,b[4]=e):e=b[4];var f;b[5]!==a?(f=[a],b[5]=a,b[6]=f):f=b[6];d("Int64Hooks").useEffectInt64(e,f)}function f(){var a=d("react-compiler-runtime").c(5),b=d("IGDRouteProvider.react").useThreadKeyFromCurrentRoute(),e;a[0]!==b?(e=b!=null?(i||(i=d("I64"))).to_string(b):null,a[0]=b,a[1]=e):e=a[1];var f=e;a[2]!==f?(b=function(){c("InteractionTracing").getPendingInteractions().forEach(function(a){if(f==null){a.addAnnotationInt("rendered_direct_null_state",1);a.addAnnotationInt("has_thread_key",0);return}a.addAnnotationInt("has_thread_key",1);a.addAnnotationInt("rendered_direct_null_state",0)})},e=[f],a[2]=f,a[3]=b,a[4]=e):(b=a[3],e=a[4]);k(b,e)}g.getInstamadilloBooleanAnnotations=a;g.addInstamadilloAnnotationsToInteractionTrace=l;g.useAddInstamadilloAnnotationsToInteractionTraces=b;g.useAddThreadAnnotationToInteractionTraces=e;g.useAddThreadRouteAnnotations=f}),98);
__d("LSCheckIfAuthoritativeThreadMissingMiActMapping",["gkx"],(function(a,b,c,d,e,f,g){function a(){var a=arguments,b=a[a.length-1],d=[],e=[];return b.sequence([function(f){return b.sequence([function(e){return c("gkx")("4792")?b.sequence([function(c){return b.db.table(173).fetch([[[a[0]]]]).next().then(function(c,e){e=c.done;c=c.value;return e?b.sequence([function(c){return b.db.table(9).fetch([[[a[0]]]]).next().then(function(a,c){var e=a.done;a=a.value;return e?d[3]=!1:(c=a.item,d[3]=b.i64.eq(c.authorityLevel,b.i64.cast([0,80])))})},function(a){return d[1]=d[3]}]):(c.item,d[1]=!1)})},function(a){return d[0]=d[1]}]):b.resolve(d[0]=!1)},function(a){return e[0]=d[0]}])},function(a){return b.resolve(e)}])}a.__sproc_name__="LSE2EEMessagingMetadataMailboxCheckIfAuthoritativeThreadMissingMiActMappingStoredProcedure";a.__tables__=["mi_act_mapping_table","threads"];b=a;g["default"]=b}),98);
__d("LSContactGender",[],(function(a,b,c,d,e,f){a=Object.freeze({UNKNOWN:0,FEMALE:1,MALE:2,NEUTER:3});f["default"]=a}),66);
__d("LSContactIdType",[],(function(a,b,c,d,e,f){a=Object.freeze({UNKNOWN:0,FBID:1,SMS_LOCAL_ID:2});f["default"]=a}),66);
__d("LSContactUserBlockAction",[],(function(a,b,c,d,e,f){a=Object.freeze({MN_UNBLOCK:0,MN_BLOCK:1,FB_UNBLOCK:2,FB_BLOCK:3});f["default"]=a}),66);
__d("LSContactWorkForeignEntityType",[],(function(a,b,c,d,e,f){a=Object.freeze({UNKNOWN:0,FOREIGN:1,NOT_FOREIGN:2,NOT_FOREIGN_LIMITED:3});f["default"]=a}),66);
__d("LSCoreClientFolderType",[],(function(a,b,c,d,e,f){a=Object.freeze({INBOX:0,PENDING:1,OTHER:2,SPAM:3,ARCHIVED:4,HIDDEN:5,MARKETPLACE:12,RESTRICTED:15,INTEROP:19});f["default"]=a}),66);
__d("LSCreateE2EEMetadataThread",["LSArrayGetObjectAt","LSBase64Encode.nop","LSIsEncryptionVersionSecure","LSIssueNewTask","LSLogMebClientEvent.nop"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.sequence([function(a){return d[0]=c.i64.of_float(Date.now()),function(a){c.logger(a).info(a)}("[EncryptedBackups][createE2EEMetadataThread] Beginning execution."),d[1]=c.i64.of_float(Date.now()),c.islc(c.filter(c.db.table(168).fetch(),function(a){return c.i64.eq(a.authorityLevel,c.i64.cast([0,80]))||!1}),0,c.i64.to_float(c.i64.cast([0,1]))).next().then(function(a,b){var e=a.done;a=a.value;return e?(d[19]=new c.Map(),d[19].set("error_msg","Expected a nonempty query result"),d[19].set("code",c.i64.cast([0,3])),d[19].set("src_line","MEBClientStateUtils.php:247 MEBClientStateUtils::maybeClientStateFromDatabaseDirectly()"),d[20]=c.createArray(),d[21]=(d[20].push(d[19]),d[20]),e=[void 0,d[20]],d[2]=e[0],d[3]=e[1],e):(b=a.item,d[19]=new c.Map(),d[19].set("backup_id",b.backupId),d[19].set("device_public_key_blob",b.devicePublicKeyBlob),d[19].set("device_private_key_blob",b.devicePrivateKeyBlob),d[19].set("epoch_storage_public_key_blob",b.epochStoragePublicKeyBlob),d[19].set("epoch_storage_private_key_blob",b.epochStoragePrivateKeyBlob),d[19].set("epoch_auth_public_key_blob",b.epochAuthPublicKeyBlob),d[19].set("epoch_auth_private_key_blob",b.epochAuthPrivateKeyBlob),d[19].set("mailbox_root_key_blob",b.mailboxRootKeyBlob),d[19].set("ocmf_client_state_blob",b.ocmfClientStateBlob),d[19].set("orf_client_state_v2_blob",void 0),d[19].set("orf_v2_authority_level",void 0),d[19].set("oblivious_validation_token_blob",b.obliviousValidationTokenBlob),d[19].set("device_id",b.deviceId),d[19].set("backup_tenancy",b.backupTenancy),d[19].set("encryption_version",b.encryptionVersion),d[19].set("revision_version",b.revisionVersion),d[19].set("initialize_restore_result",void 0),d[19].set("device_creation_timestamp_sec",void 0),d[19].set("authority_level",b.authorityLevel),e=[d[19],void 0],d[2]=e[0],d[3]=e[1],e)})},function(a){return d[5]=new c.Map(),d[5].set("value",d[2]),d[5].set("errors",d[3]),d[6]=d[5].get("value"),d[6]!==void 0?c.sequence([function(a){return d[19]=d[6].get("backup_id"),d[20]=d[6].get("device_public_key_blob"),d[21]=d[6].get("device_private_key_blob"),d[22]=d[6].get("epoch_storage_public_key_blob"),d[23]=d[6].get("epoch_storage_private_key_blob"),d[24]=d[6].get("epoch_auth_public_key_blob"),d[25]=d[6].get("epoch_auth_private_key_blob"),d[26]=d[6].get("mailbox_root_key_blob"),d[27]=d[6].get("ocmf_client_state_blob"),d[28]=d[6].get("orf_client_state_v2_blob"),d[29]=d[6].get("orf_v2_authority_level"),d[30]=d[6].get("oblivious_validation_token_blob"),d[31]=d[6].get("device_id"),d[32]=d[6].get("backup_tenancy"),d[33]=d[6].get("encryption_version"),d[34]=d[6].get("revision_version"),d[35]=d[6].get("initialize_restore_result"),d[36]=d[6].get("device_creation_timestamp_sec"),d[37]=d[6].get("authority_level"),c.i64.neq(d[31],void 0)?c.sequence([function(a){return d[39]=void 0,c.i64.neq(d[39],void 0)?c.resolve(d[40]=d[39]):c.sequence([function(a){return d[52]=c.createArray(),c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,0])).then(function(a){return a=a,d[53]=a[0],a})},function(a){return d[53]?d[62]=(d[52].push(c.i64.cast([0,0])),d[52]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,2])).then(function(a){return a=a,d[54]=a[0],a})},function(a){return d[54]?d[62]=(d[52].push(c.i64.cast([0,2])),d[52]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,3])).then(function(a){return a=a,d[55]=a[0],a})},function(a){return d[55]?d[62]=(d[52].push(c.i64.cast([0,3])),d[52]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,4])).then(function(a){return a=a,d[56]=a[0],a})},function(a){return d[56]?d[62]=(d[52].push(c.i64.cast([0,4])),d[52]):0,d[57]=c.createArray(),d[58]=c.i64.of_int32(d[52].length),c.i64.gt(d[58],c.i64.cast([0,0]))?c.loopAsync(d[58],function(a){return d[62]=a,c.sequence([function(a){return c.nativeTypeOperation("Array",b("LSArrayGetObjectAt"),d[52],d[62]).then(function(a){return a=a,d[63]=a[0],d[64]=a[1],a})},function(a){return d[65]=(d[57].push(d[63]),d[57])}])}):c.resolve()},function(a){return c.sequence([function(a){return d[62]=c.createArray(),d[63]=c.i64.of_int32(d[57].length),c.i64.gt(d[63],c.i64.cast([0,0]))?c.loopAsync(d[63],function(a){return d[65]=a,c.sequence([function(a){return c.nativeTypeOperation("Array",b("LSArrayGetObjectAt"),d[57],d[65]).then(function(a){return a=a,d[66]=a[0],d[67]=a[1],a})},function(a){return d[68]=(d[62].push(c.i64.to_string(d[66])),d[62])}])}):c.resolve()},function(a){return d[64]=d[62].join(","),d[59]=d[64]}])},function(a){return d[57].some(function(a){return c.i64.eq(d[33],a)})?d[60]=d[33]:d[60]=void 0,c.i64.neq(d[60],void 0)?d[61]=d[60]:d[61]=void 0,d[40]=d[61]}])},function(a){return c.i64.neq(d[40],void 0)?d[41]=d[40]:d[41]=c.i64.cast([0,0]),c.i64.neq(d[32],void 0)?d[42]=d[32]:d[42]=c.i64.cast([0,1]),c.nativeOperation(b("LSBase64Encode.nop"),d[27]).then(function(a){return a=a,d[43]=a[0],a})},function(a){return c.nativeOperation(b("LSBase64Encode.nop"),d[26]).then(function(a){return a=a,d[44]=a[0],a})},function(a){return d[45]=new c.Map(),d[45].set("ocmf_client_state_blob",d[43]==null?"":d[43]),d[45].set("mailbox_root_key",d[44]==null?"":d[44]),d[46]=c.createArray(),c.forEach(c.filter(c.db.table(169).fetch(),function(a){return c.i64.eq(a.authorityLevel,c.i64.cast([0,80]))}),function(a){a=a.item;return d[52]=(d[46].push(a.epochId),d[46])})},function(a){return d[50]="Queried locally available epochs. Count: ",d[47]=c.i64.of_int32(d[46].length),d[51]=c.i64.to_string(d[47]),d[48]="",function(a){c.logger(a).info(a)}(["[EncryptedBackups][MEBEpochClientUtils] ",d[48],d[50],d[48],d[51]].join("")),c.resolve()},function(a){return d[49]=new c.Map(),d[49].set("device_id",d[31]),d[49].set("raw_tokens",d[45]),d[49].set("locally_available_epochs",d[46]),d[38]=d[49]}]):c.resolve(d[38]=void 0)},function(a){return d[7]=d[38]}]):c.resolve((d[19]=d[5].get("errors"),d[20]=d[5].get("errors"),d[7]=void 0))},function(e){return d[8]=c.i64.random(),d[9]=c.i64.to_string(d[8]),d[10]=new c.Map(),d[10].set("wa_jid",a[0]),d[10].set("offline_thread_key",a[1]),d[10].set("thread_type",a[2]),d[10].set("folder_type",a[3]),d[10].set("bump_timestamp_ms",a[4]),d[10].set("tam_thread_subtype",c.i64.cast([0,0])),d[10].set("created_by_local_device",a[5]),d[10].set("device_context",d[7]),d[10].set("qpl_instance_key",d[9]),d[11]=d[10].get("offline_thread_key"),d[12]=c.toJSON(d[10]),c.storedProcedure(b("LSIssueNewTask"),c.i64.to_string(d[11]),c.i64.cast([0,388]),d[12],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))},function(a){return d[13]=c.i64.of_float(Date.now()),d[14]=c.i64.random(),d[16]="Finishing execution. Execution time: ",d[17]=c.i64.to_string(c.i64.sub(d[13],d[0])),d[18]=" ms",d[15]="",function(a){c.logger(a).info(a)}(["[EncryptedBackups][createE2EEMetadataThread] ",d[15],d[16],d[15],d[17],d[15],d[18]].join("")),c.i64.eq(c.i64.mod_(d[14],c.i64.cast([0,1e3])),c.i64.cast([0,0]))?(d[19]=",",d[20]=c.createArray(),void 0!==void 0?d[21]=(d[20].push(void 0),d[20]):0,c.nativeOperation(b("LSLogMebClientEvent.nop"),"createE2EEMetadataThread",[d[16],d[19],d[17],d[19],d[18]].join(""),d[20],c.i64.cast([0,4]))):c.resolve()}])},function(a){return c.resolve(e)}])}a.__sproc_name__="LSE2EEMessagingMetadataMailboxCreateE2EEMetadataThreadStoredProcedure";a.__tables__=["secure_encrypted_backups_client_state","secure_encrypted_backups_epochs"];e.exports=a}),null);
__d("LSCreateE2EEMetadataThreadStoredProcedure",["LSCreateE2EEMetadataThread","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSCreateE2EEMetadataThread"),e.waJid,e.offlineThreadKey,e.threadType,e.folderType,e.bumpTimestampMs,e.createdByLocalDevice);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("LSCreateOfflineThreadingID",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[],d=[];return c[0]=b.i64.random(),d[0]=b.i64.and_(b.i64.or_(b.i64.lsl_(a[0],b.i64.to_int32(b.i64.cast([0,22]))),b.i64.and_(c[0],b.i64.cast([0,4194303]))),b.i64.cast([2147483647,4294967295])),b.resolve(d)}a.__sproc_name__="LSMailboxCreateOfflineThreadingIDStoredProcedure";a.__tables__=[];e.exports=a}),null);
__d("LSCreateOfflineThreadingIDStoredProcedure",["LSCreateOfflineThreadingID","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSCreateOfflineThreadingID"),e.timestampMs);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("LSGroupParticipantJoinState",[],(function(a,b,c,d,e,f){a=Object.freeze({MEMBER:0,PENDING:1,INVITED:2});f["default"]=a}),66);
__d("LSHotEmojiSize",[],(function(a,b,c,d,e,f){a=Object.freeze({NONE:0,SMALL:1,MEDIUM:2,LARGE:3});f["default"]=a}),66);
__d("LSInitSyncCompleteSubscription",["I64","LSIntEnum","Promise","ReQL"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(a,c){return new(j||(j=b("Promise")))(function(b,e){var f=d("ReQL").fromTableAscending(a.tables.sync_groups).getKeyRange(c).subscribe(function(a,c){if(c.operation==="delete")return;if((h||(h=d("I64"))).equal(c.value.syncStatus,(i||(i=d("LSIntEnum"))).ofNumber(2))){f();return b()}})})}function a(a,b){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.tables.sync_groups).getKeyRange(b)).then(function(c){if(c!=null&&(h||(h=d("I64"))).equal(c.syncStatus,(i||(i=d("LSIntEnum"))).ofNumber(2)))return;return k(a,b)})}function c(a){return k(a,(i||(i=d("LSIntEnum"))).ofNumber(1))}g.maybeWaitForSyncGroup=a;g.use=c}),98);
__d("LSRefetchContactRowIfExists",["LSIssueNewTask"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.filter(c.db.table(7).fetch([[[a[0]]]]),function(b){return c.i64.eq(b.id,a[0])&&c.i64.eq(c.i64.cast([0,1]),c.i64.cast([0,1]))}).next().then(function(e,f){var g=e.done;e=e.value;return g?0:(f=e.item,c.i64.le(f.authorityLevel,c.i64.cast([0,80]))?(d[0]=new c.Map(),d[0].set("contact_id",a[0]),d[1]=c.toJSON(d[0]),c.storedProcedure(b("LSIssueNewTask"),"cpq_v2",c.i64.cast([0,207]),d[1],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))):c.resolve())})},function(a){return c.resolve(e)}])}a.__sproc_name__="LSContactRefetchContactRowIfExistsStoredProcedure";a.__tables__=["contacts"];e.exports=a}),null);
__d("LSRefetchContactRowIfExistsStoredProcedure",["LSRefetchContactRowIfExists","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSRefetchContactRowIfExists"),e.id);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("LSRtcCallState",[],(function(a,b,c,d,e,f){a=Object.freeze({NO_ONGOING_CALL:0,AUDIO_GROUP_CALL:1,VIDEO_GROUP_CALL:2,VIDEO_1TO1_CALL:3,AUDIO_1TO1_CALL:4});f["default"]=a}),66);
__d("LSThreadPointQueryAndRestoreMessagesWithJID",["LSArrayGetObjectAt","LSBase64Encode.nop","LSIsEncryptionVersionSecure","LSIssueNewTask","LSLogMebClientEvent.nop"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.sequence([function(a){return d[0]=c.i64.of_float(Date.now()),function(a){c.logger(a).info(a)}("[EncryptedBackups][ThreadPointQueryAndRestoreMessagesWithJID] Beginning execution."),d[1]=c.i64.of_float(Date.now()),c.islc(c.filter(c.db.table(168).fetch(),function(a){return c.i64.eq(a.authorityLevel,c.i64.cast([0,80]))||!1}),0,c.i64.to_float(c.i64.cast([0,1]))).next().then(function(a,b){var e=a.done;a=a.value;return e?(d[19]=new c.Map(),d[19].set("error_msg","Expected a nonempty query result"),d[19].set("code",c.i64.cast([0,3])),d[19].set("src_line","MEBClientStateUtils.php:247 MEBClientStateUtils::maybeClientStateFromDatabaseDirectly()"),d[20]=c.createArray(),d[21]=(d[20].push(d[19]),d[20]),e=[void 0,d[20]],d[2]=e[0],d[3]=e[1],e):(b=a.item,d[19]=new c.Map(),d[19].set("backup_id",b.backupId),d[19].set("device_public_key_blob",b.devicePublicKeyBlob),d[19].set("device_private_key_blob",b.devicePrivateKeyBlob),d[19].set("epoch_storage_public_key_blob",b.epochStoragePublicKeyBlob),d[19].set("epoch_storage_private_key_blob",b.epochStoragePrivateKeyBlob),d[19].set("epoch_auth_public_key_blob",b.epochAuthPublicKeyBlob),d[19].set("epoch_auth_private_key_blob",b.epochAuthPrivateKeyBlob),d[19].set("mailbox_root_key_blob",b.mailboxRootKeyBlob),d[19].set("ocmf_client_state_blob",b.ocmfClientStateBlob),d[19].set("orf_client_state_v2_blob",void 0),d[19].set("orf_v2_authority_level",void 0),d[19].set("oblivious_validation_token_blob",b.obliviousValidationTokenBlob),d[19].set("device_id",b.deviceId),d[19].set("backup_tenancy",b.backupTenancy),d[19].set("encryption_version",b.encryptionVersion),d[19].set("revision_version",b.revisionVersion),d[19].set("initialize_restore_result",void 0),d[19].set("device_creation_timestamp_sec",void 0),d[19].set("authority_level",b.authorityLevel),e=[d[19],void 0],d[2]=e[0],d[3]=e[1],e)})},function(a){return d[5]=new c.Map(),d[5].set("value",d[2]),d[5].set("errors",d[3]),d[6]=d[5].get("value"),d[6]!==void 0?c.sequence([function(a){return d[19]=d[6].get("backup_id"),d[20]=d[6].get("device_public_key_blob"),d[21]=d[6].get("device_private_key_blob"),d[22]=d[6].get("epoch_storage_public_key_blob"),d[23]=d[6].get("epoch_storage_private_key_blob"),d[24]=d[6].get("epoch_auth_public_key_blob"),d[25]=d[6].get("epoch_auth_private_key_blob"),d[26]=d[6].get("mailbox_root_key_blob"),d[27]=d[6].get("ocmf_client_state_blob"),d[28]=d[6].get("orf_client_state_v2_blob"),d[29]=d[6].get("orf_v2_authority_level"),d[30]=d[6].get("oblivious_validation_token_blob"),d[31]=d[6].get("device_id"),d[32]=d[6].get("backup_tenancy"),d[33]=d[6].get("encryption_version"),d[34]=d[6].get("revision_version"),d[35]=d[6].get("initialize_restore_result"),d[36]=d[6].get("device_creation_timestamp_sec"),d[37]=d[6].get("authority_level"),c.i64.neq(d[31],void 0)?c.sequence([function(a){return d[39]=void 0,c.i64.neq(d[39],void 0)?c.resolve(d[40]=d[39]):c.sequence([function(a){return d[52]=c.createArray(),c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,0])).then(function(a){return a=a,d[53]=a[0],a})},function(a){return d[53]?d[62]=(d[52].push(c.i64.cast([0,0])),d[52]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,2])).then(function(a){return a=a,d[54]=a[0],a})},function(a){return d[54]?d[62]=(d[52].push(c.i64.cast([0,2])),d[52]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,3])).then(function(a){return a=a,d[55]=a[0],a})},function(a){return d[55]?d[62]=(d[52].push(c.i64.cast([0,3])),d[52]):0,c.storedProcedure(b("LSIsEncryptionVersionSecure"),c.i64.cast([0,4])).then(function(a){return a=a,d[56]=a[0],a})},function(a){return d[56]?d[62]=(d[52].push(c.i64.cast([0,4])),d[52]):0,d[57]=c.createArray(),d[58]=c.i64.of_int32(d[52].length),c.i64.gt(d[58],c.i64.cast([0,0]))?c.loopAsync(d[58],function(a){return d[62]=a,c.sequence([function(a){return c.nativeTypeOperation("Array",b("LSArrayGetObjectAt"),d[52],d[62]).then(function(a){return a=a,d[63]=a[0],d[64]=a[1],a})},function(a){return d[65]=(d[57].push(d[63]),d[57])}])}):c.resolve()},function(a){return c.sequence([function(a){return d[62]=c.createArray(),d[63]=c.i64.of_int32(d[57].length),c.i64.gt(d[63],c.i64.cast([0,0]))?c.loopAsync(d[63],function(a){return d[65]=a,c.sequence([function(a){return c.nativeTypeOperation("Array",b("LSArrayGetObjectAt"),d[57],d[65]).then(function(a){return a=a,d[66]=a[0],d[67]=a[1],a})},function(a){return d[68]=(d[62].push(c.i64.to_string(d[66])),d[62])}])}):c.resolve()},function(a){return d[64]=d[62].join(","),d[59]=d[64]}])},function(a){return d[57].some(function(a){return c.i64.eq(d[33],a)})?d[60]=d[33]:d[60]=void 0,c.i64.neq(d[60],void 0)?d[61]=d[60]:d[61]=void 0,d[40]=d[61]}])},function(a){return c.i64.neq(d[40],void 0)?d[41]=d[40]:d[41]=c.i64.cast([0,0]),c.i64.neq(d[32],void 0)?d[42]=d[32]:d[42]=c.i64.cast([0,1]),c.nativeOperation(b("LSBase64Encode.nop"),d[27]).then(function(a){return a=a,d[43]=a[0],a})},function(a){return c.nativeOperation(b("LSBase64Encode.nop"),d[26]).then(function(a){return a=a,d[44]=a[0],a})},function(a){return d[45]=new c.Map(),d[45].set("ocmf_client_state_blob",d[43]==null?"":d[43]),d[45].set("mailbox_root_key",d[44]==null?"":d[44]),d[46]=c.createArray(),c.forEach(c.filter(c.db.table(169).fetch(),function(a){return c.i64.eq(a.authorityLevel,c.i64.cast([0,80]))}),function(a){a=a.item;return d[52]=(d[46].push(a.epochId),d[46])})},function(a){return d[50]="Queried locally available epochs. Count: ",d[47]=c.i64.of_int32(d[46].length),d[51]=c.i64.to_string(d[47]),d[48]="",function(a){c.logger(a).info(a)}(["[EncryptedBackups][MEBEpochClientUtils] ",d[48],d[50],d[48],d[51]].join("")),c.resolve()},function(a){return d[49]=new c.Map(),d[49].set("device_id",d[31]),d[49].set("raw_tokens",d[45]),d[49].set("locally_available_epochs",d[46]),d[38]=d[49]}]):c.resolve(d[38]=void 0)},function(a){return d[7]=d[38]}]):c.resolve((d[19]=d[5].get("errors"),d[20]=d[5].get("errors"),d[7]=void 0))},function(e){return d[8]=c.i64.random(),d[9]=c.i64.to_string(d[8]),d[10]=new c.Map(),d[10].set("wa_jid",a[0]),d[10].set("tam_thread_subtype",c.i64.cast([0,0])),d[10].set("enable_logging_for_tpq",!1),d[10].set("device_context",d[7]),d[10].set("qpl_instance_key",d[9]),d[11]=d[10].get("wa_jid"),d[12]=c.toJSON(d[10]),c.storedProcedure(b("LSIssueNewTask"),c.i64.to_string(d[11]),c.i64.cast([0,556]),d[12],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))},function(a){return d[13]=c.i64.of_float(Date.now()),d[14]=c.i64.random(),d[16]="Finishing execution. Execution time: ",d[17]=c.i64.to_string(c.i64.sub(d[13],d[0])),d[18]=" ms",d[15]="",function(a){c.logger(a).info(a)}(["[EncryptedBackups][ThreadPointQueryAndRestoreMessagesWithJID] ",d[15],d[16],d[15],d[17],d[15],d[18]].join("")),c.i64.eq(c.i64.mod_(d[14],c.i64.cast([0,1e3])),c.i64.cast([0,0]))?(d[19]=",",d[20]=c.createArray(),void 0!==void 0?d[21]=(d[20].push(void 0),d[20]):0,c.nativeOperation(b("LSLogMebClientEvent.nop"),"ThreadPointQueryAndRestoreMessagesWithJID",[d[16],d[19],d[17],d[19],d[18]].join(""),d[20],c.i64.cast([0,4]))):c.resolve()}])},function(a){return c.resolve(e)}])}a.__sproc_name__="LSE2EEMessagingMetadataMailboxThreadPointQueryAndRestoreMessagesWithJIDStoredProcedure";a.__tables__=["secure_encrypted_backups_client_state","secure_encrypted_backups_epochs"];e.exports=a}),null);
__d("LSThreadPointQueryAndRestoreMessagesWithJIDStoredProcedure",["LSSynchronousPromise","LSThreadPointQueryAndRestoreMessagesWithJID","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSThreadPointQueryAndRestoreMessagesWithJID"),e.waJid);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("LSVerifyContactRowExistsStoredProcedure",["LSSynchronousPromise","LSVerifyContactRowExists","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSVerifyContactRowExists"),e.id,e.contactIdType,e.profilePictureUrl,e.name,e.contactType,e.profilePictureFallbackUrl,e.profilePictureUrlExpirationTimestampMs,e.urlExpirationTimestampMs,e.normalizedNameForSearch,e.isMemorialized,e.isBlocked,e.blockedByViewerStatus,e.canViewerMessage,e.isSelf,e.authorityLevel,e.capabilities,e.capabilities2,e.workForeignEntityType,e.gender,e.contactViewerRelationship,e.secondaryName,e.firstName,e.friendshipStatus);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("LSVerifyE2EEMetadataThreadExistsV2",["LSCheckIfAuthoritativeThreadMissingMiActMapping","LSCreateOfflineThreadingID","LSIssueNewTask"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(f){return c.sequence([function(b){return c.i64.neq(a[1],void 0)?c.sequence([function(b){return c.db.table(9).fetch([[[a[1]]]]).next().then(function(b,c){var e=b.done;b=b.value;return e?(e=[void 0,void 0],d[3]=e[0],d[4]=e[1],e):(c=b.item,e=[a[1],c.authorityLevel],d[3]=e[0],d[4]=e[1],e)})},function(a){return a=[d[3],d[4]],d[0]=a[0],d[1]=a[1],a}]):c.resolve((b=[void 0,void 0],d[0]=b[0],d[1]=b[1],b))},function(e){return c.i64.neq(d[0],void 0)?c.sequence([function(e){return c.i64.neq(d[1],void 0)?c.sequence([function(a){return c.storedProcedure(b("LSCheckIfAuthoritativeThreadMissingMiActMapping"),d[0]).then(function(a){return a=a,d[3]=a[0],a})},function(b){return c.i64.gt(a[2],d[1])?c.forEach(c.db.table(9).fetch([[[d[0]]]]),function(b){var c=b.update;b.item;return c({authorityLevel:a[2]})}):c.resolve()},function(e){return d[3]||c.i64.gt(a[2],d[1])?(d[4]=new c.Map(),d[4].set("thread_fbid",d[0]),d[4].set("force_upsert",!1),d[4].set("use_open_messenger_transport",!1),d[4].set("sync_group",c.i64.cast([0,95])),d[4].set("metadata_only",!1),d[4].set("preview_only",!1),d[5]=d[4].get("thread_fbid"),d[6]=c.toJSON(d[4]),c.storedProcedure(b("LSIssueNewTask"),c.i64.to_string(d[5]),c.i64.cast([0,209]),d[6],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))):c.resolve()}]):c.resolve()},function(a){return d[2]=d[0]}]):c.sequence([function(e){return c.i64.neq(a[1],void 0)?c.sequence([function(e){return c.i64.neq(a[2],c.i64.cast([0,20]))?(d[11]=new c.Map(),d[11].set("thread_fbid",a[1]),d[11].set("force_upsert",!1),d[11].set("use_open_messenger_transport",!1),d[11].set("sync_group",c.i64.cast([0,95])),d[11].set("metadata_only",!1),d[11].set("preview_only",!1),d[12]=d[11].get("thread_fbid"),d[13]=c.toJSON(d[11]),c.storedProcedure(b("LSIssueNewTask"),c.i64.to_string(d[12]),c.i64.cast([0,209]),d[13],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]))):c.resolve()},function(b){return d[3]=a[1]}]):c.sequence([function(a){return d[11]=c.i64.of_float(Date.now()),c.storedProcedure(b("LSCreateOfflineThreadingID"),d[11]).then(function(a){return a=a,d[12]=a[0],a})},function(a){return d[3]=d[12]}])},function(b){return c.i64.eq(c.i64.cast([0,0]),c.i64.cast([0,0]))?(b=["inbox",c.i64.cast([0,0])],d[4]=b[0],d[5]=b[1],b):(c.i64.eq(c.i64.cast([0,0]),c.i64.cast([0,1]))?(b=["pending",c.i64.cast([-1,4294967295])],d[11]=b[0],d[12]=b[1],b):(c.i64.eq(c.i64.cast([0,0]),c.i64.cast([0,2]))?(b=["other",c.i64.cast([-1,4294967294])],d[13]=b[0],d[14]=b[1],b):(c.i64.eq(c.i64.cast([0,0]),c.i64.cast([0,3]))?(b=["spam",c.i64.cast([-1,4294967293])],d[15]=b[0],d[16]=b[1],b):(c.i64.eq(c.i64.cast([0,0]),c.i64.cast([0,5]))?(b=["hidden",c.i64.cast([-1,4294967292])],d[17]=b[0],d[18]=b[1],b):(c.i64.eq(c.i64.cast([0,0]),c.i64.cast([0,4]))?(b=["archived",c.i64.cast([-1,4294967286])],d[19]=b[0],d[20]=b[1],b):(c.i64.eq(c.i64.cast([0,0]),c.i64.cast([0,15]))?(b=["restricted",c.i64.cast([-1,4294967281])],d[21]=b[0],d[22]=b[1],b):(b=["inbox",c.i64.cast([0,0])],d[21]=b[0],d[22]=b[1],b),b=[d[21],d[22]],d[19]=b[0],d[20]=b[1],b),b=[d[19],d[20]],d[17]=b[0],d[18]=b[1],b),b=[d[17],d[18]],d[15]=b[0],d[16]=b[1],b),b=[d[15],d[16]],d[13]=b[0],d[14]=b[1],b),b=[d[13],d[14]],d[11]=b[0],d[12]=b[1],b),b=[d[11],d[12]],d[4]=b[0],d[5]=b[1],b),d[6]=c.i64.cast([0,1083495711]),d[7]=c.i64.cast([0,1024]),d[8]=c.i64.cast([0,0]),d[9]=c.i64.cast([0,0]),d[10]=c.i64.cast([0,0]),c.forEach(c.filter(c.db.table(9).fetch([[[d[3]]]]),function(b){return c.i64.eq(b.threadKey,d[3])&&c.i64.lt(b.authorityLevel,a[2])}),function(a){return a["delete"]()})},function(b){return c.db.table(9).add({threadKey:d[3],mailboxType:c.i64.cast([0,4096]),threadType:a[0],folderName:d[4],lastActivityTimestampMs:c.i64.cast([-1,4294967295]),lastReadWatermarkTimestampMs:c.i64.cast([-1,4294967295]),removeWatermarkTimestampMs:c.i64.cast([-1,4294967295]),ongoingCallState:c.i64.cast([0,0]),parentThreadKey:d[5],authorityLevel:a[2],capabilities:d[6],capabilities2:d[7],capabilities3:d[8],capabilities4:d[9],capabilities5:d[10],unsendLimitMs:c.i64.cast([-1,4294967295]),syncGroup:c.i64.cast([0,95]),isHidden:!0,clientThreadKey:d[3]})},function(a){return d[2]=d[3]}])},function(a){return e[0]=d[2]}])},function(a){return c.resolve(e)}])}a.__sproc_name__="LSE2EEMessagingMetadataMailboxVerifyE2EEMetadataThreadExistsV2StoredProcedure";a.__tables__=["threads"];e.exports=a}),null);
__d("LSVerifyE2EEMetadataThreadExistsV2StoredProcedure",["LSSynchronousPromise","LSVerifyE2EEMetadataThreadExistsV2","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSVerifyE2EEMetadataThreadExistsV2"),e.threadType,e.threadKey,e.authorityLevel);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("MAWMiActMappingTableAPI",["I64","MAWJids","Promise","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a,c){return c==null?(i||(i=b("Promise"))).resolve(null):d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.mi_act_mapping_table).getKeyRange(c))}function a(a,b){return l(a,b).then(function(a){return a==null?void 0:a.serverThreadKey})}function k(a,c){return c==null?(i||(i=b("Promise"))).resolve(null):d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.mi_act_mapping_table.index("jid")).getKeyRange(c))}function l(a,b){return k(a,d("MAWJids").convertChatJidToIntJid(b))}function c(a,b){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=b.intJid;b=b.threadKey;b=(yield j(a,b));return b!=null?b:k(a,c)});return m.apply(this,arguments)}function n(a){return(h||(h=d("I64"))).le(a,h.zero)}function e(a,b){return a.tables.mi_act_mapping_table.subscribe(function(a,c){var d=a[0],e=a[1];a[2];a=c.operation;a==="delete"&&!n(e)&&b(e,d)})}g.getMappingRowForThreadKey=j;g.getThreadKeyForChatJid=a;g.getMappingRowForIntJid=k;g.getMappingRowForChatJid=l;g.getMappingRowForThreadKeyOrJid=c;g.subscribeToMappingDeletion=e}),98);
__d("MAWBridgeGroupInviteLoadedHandler",["fbt","I64","LSMessagingThreadTypeUtil","MAWMiActMappingTableAPI","Promise","ReQL","asyncToGeneratorRuntime","justknobx"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j;function a(a,b,c){return l(a,c,b.actorId)}function k(a,c,e){return d("MAWMiActMappingTableAPI").getThreadKeyForChatJid(a,c).then(function(c){return c==null?(j||(j=b("Promise"))).resolve():l(a,c,e)})}function l(a,b,c){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){var f=a.threads,g=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(f).getKeyRange(b)));if(g==null)return;var j=d("LSMessagingThreadTypeUtil").isGroup(g.threadType);if(!j)return;j=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.group_invites).getKeyRange(b)));if(j==null){c("justknobx")._("1162")&&(yield f.put(babelHelpers["extends"]({},g,{additionalThreadContext:void 0})));return}b=(i||(i=d("I64"))).equal((i||(i=d("I64"))).of_string(e),j.inviterId)?null:yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.contacts,["name"]).getKeyRange(j.inviterId));if(b==null)return;e=h._(/*BTDS*/"{Inviter Name} invited you to join",[h._param("Inviter Name",b.name)]).toString();a=g.folderName==="inbox"?h._(/*BTDS*/"Message request").toString():g.additionalThreadContext;yield f.put(babelHelpers["extends"]({},g,{additionalThreadContext:a,snippet:e}))});return m.apply(this,arguments)}function e(a,b){return k(a,b.threadJid,b.actorId)}function f(a,b,c){return k(a,b,c)}g.callForOneDb=a;g.call=e;g.callFromMainThread=f}),226);
__d("WADbContact",[],(function(a,b,c,d,e,f){"use strict";a=0;b=1;c=2;d=3;f.NON_CONTACT=a;f.ONE_WAY_CONTACT=b;f.REVERSED_ONE_WAY_CONTACT=c;f.TWO_WAY_CONTACT=d}),66);
__d("MAWContactRelationshipType",["I64","LSContactBitOffset","WADbContact"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var b=d("LSContactBitOffset").hasWithDefault(64,a,!0);a=(h||(h=d("I64"))).ge(a.contactViewerRelationship,h.of_string("2"));if(a&&b)return d("WADbContact").ONE_WAY_CONTACT;else if(!a&&!b)return d("WADbContact").REVERSED_ONE_WAY_CONTACT;else if(a&&!b)return d("WADbContact").TWO_WAY_CONTACT;return d("WADbContact").NON_CONTACT}g.getContactRelationshipType=a}),98);
__d("MAWMessageRequestUtil",["I64","LSContactBitOffset","LSContactViewerRelationship","LSIntEnum","LSMessagingThreadTypeUtil","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a,b,e){if(a==="pending"||a==="other")return!0;var f=d("LSContactBitOffset").hasWithDefault(64,e,!0);return a==="inbox"&&(h||(h=d("I64"))).equal(e.contactViewerRelationship,(i||(i=d("LSIntEnum"))).ofNumber(c("LSContactViewerRelationship").NOT_CONTACT))&&!f?b:!1}function k(a){return a==="pending"||a==="other"}function a(a){return d("LSMessagingThreadTypeUtil").isOneToOne(a.threadType)?k(a.folderName):!1}function l(a,b){return b!=null?j(a.folderName,c("gkx")("13333")?(h||(h=d("I64"))).gt(a.lastActivityTimestampMs,(h||(h=d("I64"))).zero):!(h||(h=d("I64"))).equal(a.lastActivityTimestampMs,(h||(h=d("I64"))).zero),b[1]):!1}function b(a,b){return d("LSMessagingThreadTypeUtil").isOneToOne(a.threadType)?l(a,b):!1}function e(a){return d("LSMessagingThreadTypeUtil").isGroup(a.threadType)?k(a.folderName):!1}f=[8,18,19,34,24,25,122];var m=[21,48];g.isMessageRequestV2=j;g.isOneOnOneMessageRequest=a;g.isOneOnOneMessageRequestV2=b;g.isGroupMessageRequest=e;g.disabledThreadCapabilitiesForIncomingRequest=f;g.enabledThreadCapabilitiesForIncomingRequest=m}),98);
__d("MAWBridgeOneToOneMessageRequestLoadedHandler",["fbt","I64","LSAuthorityLevel","LSIntEnum","LSMessagingThreadTypeUtil","LSThreadBitOffset","MAWContactRelationshipType","MAWJids","MAWMessageRequestUtil","MAWMiActMappingTableAPI","ODS","Promise","ReQL","WADbContact","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k,l;function a(a,b){b=b.threadJid;return m(a,b)}function e(a,b){return m(a,b)}function m(a,b){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){var e=d("MAWJids").convertChatJidToIntJid(c);c=(yield d("MAWMiActMappingTableAPI").getThreadKeyForChatJid(a,c));if(c==null)return;c=(yield (l||(l=b("Promise"))).all([d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(c)),d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.contacts).getKeyRange(e))]));e=c[0];c=c[1];if(e==null||c==null||!d("LSMessagingThreadTypeUtil").isOneToOne(e.threadType))return;c=o(e,c);c=e.folderName==="pending"||e.folderName==="other"||e.folderName==="spam"||c;if(!c)return;c=e.folderName==="inbox"?h._(/*BTDS*/"Message request").toString():void 0;c=babelHelpers["extends"]({},e,{additionalThreadContext:c},p(e));yield a.threads.put(c)});return n.apply(this,arguments)}function o(a,b){var e=d("MAWContactRelationshipType").getContactRelationshipType(b);e=a.folderName==="inbox"&&e===d("WADbContact").REVERSED_ONE_WAY_CONTACT&&!(i||(i=d("I64"))).equal(a.lastActivityTimestampMs,(i||(i=d("I64"))).zero);a=(i||(i=d("I64"))).lt(b.authorityLevel,(j||(j=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").AUTHORITATIVE));e&&!a&&(k||(k=d("ODS"))).bumpEntityKey(3185,"e2ee.message_request_loaded_failure","contacts_not_yet_loaded",1);return e&&a}function p(a){a=d("LSThreadBitOffset").clear(d("MAWMessageRequestUtil").disabledThreadCapabilitiesForIncomingRequest,a.capabilities,a.capabilities2,a.capabilities3,a.capabilities4,a.capabilities5);a=d("LSThreadBitOffset").set(d("MAWMessageRequestUtil").enabledThreadCapabilitiesForIncomingRequest,a[0],a[1],a[2],a[3],a[4]);return{capabilities:a[0],capabilities2:a[1],capabilities3:a[2],capabilities4:a[3],capabilities5:a[4]}}g.call=a;g.callFromMainThread=e}),226);
__d("MAWSetupWorkerAuxStateForLogging",["nullthrows"],(function(a,b,c,d,e,f,g){"use strict";var h=[],i=3,j={getWorkerAge:function(){if(j.workerStartTime!=null)return Math.floor((Date.now()-c("nullthrows")(j.workerStartTime))/1e3)},restartMessageTypes:[],restartReasons:[],workerStartTime:null,workerTerminatedPermanently:!1};function a(){j.workerStartTime=Date.now()}function b(a){h.push(a),h.length>i&&h.shift()}function d(){return h.join(",")}g.HEART_BEAT_HISTORY=h;g.WorkerLifeCycleState=j;g.resetWorkerCreationTime=a;g.addWorkerHeartbeatToHistory=b;g.getHeartbeatHistoryAsString=d}),98);
__d("MAWInitError",[],(function(a,b,c,d,e,f){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;d=a.call(this,b)||this;d.error=void 0;d.name="MAWInitError";d.message=b;d.error=c;return d}return b}(babelHelpers.wrapNativeSuper(Error));f.MAWInitError=a}),66);
__d("MAWInit",["MAWCurrentUser","MAWInitError","MAWMIC","MWQPLJoinId","MultipleTabsLogger","QPLUserFlow","QuickPerformanceLogger","WAGetStorageQplAnnotations","asyncToGeneratorRuntime","gkx","qpl","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("qpl")._(25310776,"6155"),j=!1,k=[],l=[],m=!1;function n(){if(m)return;m=!0;c("QPLUserFlow").addAnnotations(i,{bool:{armadillo_init_sync_api_improvements:c("gkx")("24025"),isArmadilloPublicLaunchUser:c("gkx")("23405"),isTlcPublicUser:d("MAWCurrentUser").isTlcPublicUser(),useSharedWorker:d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker()}})}var o=!1,p=function(){return document.visibilityState!==void 0?document.visibilityState==="hidden":document.hidden},q=function(){r("visibility_change_"+(p()?"hidden":"visible"))};function a(){if(o)return;d("MAWMIC").addPoint("maw_init_start");o=!0;c("QPLUserFlow").start(i,babelHelpers["extends"]({annotations:{bool:{initiallyHidden:p()},string:{hostname:window.location.hostname}},cancelOnUnload:!0},d("MWQPLJoinId").allowJoinId?{joinOptions:{joinId:d("MWQPLJoinId").MWQPLJoinId}}:{}));c("QPLUserFlow").addAlignmentPointForJoin(i,0,{requestId:d("MWQPLJoinId").MWQPLJoinId});d("MultipleTabsLogger").addAnnotationToQPLEvent(i);void d("WAGetStorageQplAnnotations").getStorageQplAnnotations().then(function(a){c("QPLUserFlow").addAnnotations(i,a)});n();document.addEventListener("visibilitychange",q);k.forEach(function(a){return a()});l.forEach(function(a){return a()});k.splice(0,k.length);l.splice(0,l.length)}function r(a,b){if(!o||j){if(!o&&(b==null?void 0:b.allowEarlyLogPoints)){var d=(h||(h=c("QuickPerformanceLogger"))).currentTimestamp();l.push(function(){c("QPLUserFlow").addPoint(i,a,{timestamp:d})})}return}n();c("QPLUserFlow").addPoint(i,a)}function e(a,b,d){if(!o||j){!o&&(d==null?void 0:d.allowEarlyAnnotations)&&k.push(function(){var d;c("QPLUserFlow").addAnnotations(i,{bool:(d={},d[a]=b,d)})});return}c("QPLUserFlow").addAnnotations(i,{bool:(d={},d[a]=b,d)})}function f(a,b,d){if(!o||j){!o&&(d==null?void 0:d.allowEarlyAnnotations)&&k.push(function(){var d;c("QPLUserFlow").addAnnotations(i,{string:(d={},d[a]=b,d)})});return}c("QPLUserFlow").addAnnotations(i,{string:(d={},d[a]=b,d)})}function s(a,b,d){if(!o||j){!o&&(d==null?void 0:d.allowEarlyAnnotations)&&k.push(function(){var d;c("QPLUserFlow").addAnnotations(i,{string_array:(d={},d[a]=b,d)})});return}c("QPLUserFlow").addAnnotations(i,{string_array:(d={},d[a]=b,d)})}function t(a,b,d){if(!o||j){!o&&(d==null?void 0:d.allowEarlyAnnotations)&&k.push(function(){var d;c("QPLUserFlow").addAnnotations(i,{"int":(d={},d[a]=b,d)})});return}c("QPLUserFlow").addAnnotations(i,{"int":(d={},d[a]=b,d)})}function u(){n(),j=!0,m=!1,document.removeEventListener("visibilitychange",q)}function v(a,b){u();b=b instanceof d("MAWInitError").MAWInitError&&b.error?b.error:b;c("QPLUserFlow").addAnnotations(i,{string:{errorDescription:b==null?void 0:b.message}});c("QPLUserFlow").endFailure(i,a,{error:b});d("MAWMIC").fail(a,b)}function w(a,b){u(),c("QPLUserFlow").addAnnotations(i,{string:{cancelReason:a}}),b&&c("QPLUserFlow").markError(i,"maw_init_cancel_with_error",{error:b}),c("QPLUserFlow").endCancel(i),d("MAWMIC").addStringAnnotation("mawInitCancelReason",a),d("MAWMIC").cancel("maw_init_cancel",b),c("QPLUserFlow").addAnnotations(c("qpl")._(29818881,"6702"),{string:{maw_cancel_reason:a,maw_error_message:b==null?void 0:b.message}})}function x(a){if(j)return;u();d("MAWMIC").addStringAnnotation("mawInitDropReason",a);(h||(h=c("QuickPerformanceLogger"))).markerDrop(i)}function y(){u(),c("QPLUserFlow").endSuccess(i),d("MAWMIC").addPoint("maw_init_end")}function z(a,b){return A.apply(this,arguments)}function A(){A=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=a+"_start";a=a+"_end";r(c);c=(yield b());r(a);return c});return A.apply(this,arguments)}var B={isCompleted:function(){return j}};e={addBoolAnnotation:e,addIntAnnotation:t,addStringAnnotation:f,addStringArrayAnnotation:s,cancel:w,drop:x,endSuccess:y,fail:v,logPoint:r,measurePerformance:z,start:a};g.MAWInitState=B;g.MAWInit=e}),98);
__d("MAWStartupLoggingUtils",["FBLogger","MAWInit","MAWMIC","getErrorSafe"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,e){var f;f=(f=b==null?void 0:b.message)!=null?f:"";f.indexOf("Failed to execute 'importScripts'")>=0?d("MAWInit").MAWInit.fail("import_scripts_failure",c("getErrorSafe")(b)):d("MAWInit").MAWInit.fail("worker_error",c("getErrorSafe")(b));c("FBLogger")("messenger_web").mustfix("[MAWWebWorkerSingletonAsync: "+a+"]: %s:%s:%s:%s:%s:%s:%s",JSON.stringify(b==null?void 0:b.message),b==null?void 0:b.lineno,b==null?void 0:b.colno,b==null?void 0:b.filename,JSON.stringify(b,window.Object.getOwnPropertyNames(b)),b==null?void 0:b.name,e)}function b(a){var b=function b(d){d=d==null?void 0:d.data;if(typeof d==="object")if((d==null?void 0:d.type)==="importScripts_error"){var e,f;a.removeEventListener("message",b);var g=c("FBLogger")("messenger_web");g=g.addMetadata("FBLOGGER","ERROR_STACK",(e=typeof (d==null?void 0:d.stack)==="string"?d==null?void 0:d.stack:JSON.stringify(d==null?void 0:d.stack))!=null?e:"no_stack");e=d!=null?d:{};var h=e.error_code,i=e.error_msg,j=e.error_name;e=e.source;f=(f=d==null?void 0:d.perfEntry)!=null?f:{};var k=f.duration,l=f.encodedBodySize,m=f.responseStatus;f=f.transferSize;g.mustfix("[MAWWebWorkerSingletonAsync Script Import Error Details]: %s:%s:%s || errCode:%s || respStatus:%s || encBodySize:%s || duration:%s || transferSize:%s",e!=null&&JSON.stringify(e)||"no_source",j!=null&&JSON.stringify(j)||"no_name",i!=null&&JSON.stringify(i)||"no_msg",h!=null&&JSON.stringify(h)||"none",(g=JSON.stringify(m))!=null?g:"none",(e=JSON.stringify(l))!=null?e:"none",(j=JSON.stringify(k))!=null?j:"none",(i=JSON.stringify(f))!=null?i:"none")}else(d==null?void 0:d.type)==="importScripts_success"&&a.removeEventListener("message",b)};a.addEventListener("message",b)}var h=new Set(["worker_connection_established","worker_init_point_hsdp_end","worker_init_point_hblp_end","get_worker_rev_ack_received","old_worker_shutdown_end","old_worker_shutdown_start"]);function e(a){var b=a.data.annotations;if(b!=null){var c={allowEarlyAnnotations:!0};Object.keys(b).forEach(function(a){var e;e=(e=b[a])!=null?e:{};Object.entries(e).forEach(function(b){var e=b[0];b=b[1];e="workerInit"+e.charAt(0).toUpperCase()+e.substring(1);a==="int"&&typeof b==="number"?(d("MAWInit").MAWInit.addIntAnnotation(e,b,c),d("MAWMIC").addIntAnnotation(e,b)):a==="string"&&typeof b==="string"?(d("MAWInit").MAWInit.addStringAnnotation(e,b,c),d("MAWMIC").addStringAnnotation(e,b)):a==="bool"&&typeof b==="boolean"&&(d("MAWInit").MAWInit.addBoolAnnotation(e,b,c),d("MAWMIC").addBoolAnnotation(e,b))})})}var e={allowEarlyLogPoints:!0};if(a.type==="point"){if(h.has(a.data.event)){var f="worker_"+a.data.event;d("MAWInit").MAWInit.logPoint(f,e);d("MAWMIC").addPoint(f)}}else a.type==="end"&&(a.data.cancel===!0?d("MAWInit").MAWInit.logPoint("worker_creation_cancel",e):a.data.errorName!=null&&(d("MAWInit").MAWInit.addStringAnnotation("errorDescription",a.data.errorName,{allowEarlyAnnotations:!0}),d("MAWInit").MAWInit.logPoint("worker_creation_fail",e)))}g.logWorkerError=a;g.setImportScriptsErrorListener=b;g.logSharedWorkerQPLEvents=e}),98);
__d("MAWWorkerWatchdogRecovery",[],(function(a,b,c,d,e,f){"use strict";var g=null;function a(a){g=a}function b(){return g}f.setRecoveryForWatchdog=a;f.getWorkerRecoveryForWatchdog=b}),66);
__d("SharedWorkerDevChangeManager",["gkx"],(function(a,b,c,d,e,f,g){"use strict";var h=!1,i=new Set();function a(a,b){var c=b.storageWorkerResource;b=b.tabResource;if(!h)return!0;if(i.has(a))return!0;if(new URLSearchParams(window.location.search).has("force_shared_worker_updates"))return!1;if(c.version===2&&b.version===2&&c.sandboxOnlyChecksum!=null)return c.sandboxOnlyChecksum===b.sandboxOnlyChecksum;return c.version===2&&c.rsrcBundleUrl==null||b.version===2&&b.getJSModuleBundleResource().url==null?!1:c.rsrcBundleUrl===b.getJSModuleBundleResource().url}function b(a){if(!h)return!1;if(i.has(a))return!1;return new URLSearchParams(window.location.search).has("force_shared_worker_updates")?!0:!1}function c(a){h&&i.add(a)}g.shouldUseStorageWorkerForDev=a;g.shouldUpgradeWorkerForDevExperimental=b;g.trackCreatedWorker=c}),98);
__d("SharedWorkerEventManager",["FBLogger"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b){var d=function(a){var d;if(typeof a.data==="object"&&((d=a.data)==null?void 0:d.type)==="console"&&typeof ((d=a.data)==null?void 0:d.response)==="object"&&((d=a.data)==null?void 0:d.response)!=null){d=a.data.response;a=d.method;d=d.args;if(typeof a!=="string"||!["log","error","info","debug","warn"].includes(a)){c("FBLogger")("worker").mustfix("Unexpected console method: %s",String(a));return}if(!Array.isArray(d)){c("FBLogger")("worker").mustfix('Expected console args "%s" to be an array',JSON.stringify(d));return}b({method:a,args:d})}},e=a.port;e.addEventListener("message",d);return function(){return e.removeEventListener("message",d)}}function i(a,b){var c=function(a){var c;if(typeof a.data==="object"&&((c=a.data)==null?void 0:c.type)==="sw-uncaught-error"){a=typeof ((c=a.data)==null?void 0:c.e)==="object"?(c=a.data.e)!=null?c:{}:{};c=a.error;var d=a.lineno,e=a.colno,f=a.filename;a=a.message;b({error:c instanceof Error?c:null,errorObject:{lineno:typeof d==="number"?d:null,colno:typeof e==="number"?e:null,filename:typeof f==="string"?f:null,message:typeof a==="string"?a:null}})}},d=a.port;d.addEventListener("message",c);return function(){return d.removeEventListener("message",c)}}function a(a,b,d){var e=function(a){var e;if(typeof a.data==="object"&&((e=a.data)==null?void 0:e.type)==="sw-get-rev"&&typeof a.data.response==="object"){b==null?void 0:b.markPoint("get_worker_rev_ack_received");a=typeof a.data==="object"&&typeof ((e=a.data)==null?void 0:e.response)==="object"?(e=a.data)==null?void 0:e.response:{};e=a==null?void 0:a.workerRevision;var f=a==null?void 0:a.spinTime;a=a==null?void 0:a.workerChecksum;if(e==null||typeof e!=="number"){b==null?void 0:b.addAnnotations({string:{malformedData:"rev is non-number"}});c("FBLogger")("worker").mustfix("Expected worker rev to be non-null number, but instead received: %s",String(e));return}if(f==null||typeof f!=="number"){b==null?void 0:b.addAnnotations({string:{malformedData:"spin_time is non-number"}});c("FBLogger")("worker").mustfix("Expected worker spin time to be non-null number, but instead received: %s",String(f));return}d({rev:e,spinTime:f,workerChecksum:typeof a==="string"?a:void 0})}},f=a.port;f.addEventListener("message",e);return function(){return f.removeEventListener("message",e)}}function b(a,b,c){var d=function d(e){var f;if(typeof e.data==="object"&&((f=e.data)==null?void 0:f.type)==="sw-shutdown"&&typeof e.data.response==="object"){var g,h;f=typeof ((f=e.data.response)==null?void 0:f.reason)==="string"?(f=e.data.response)==null?void 0:f.reason:null;g=typeof ((g=e.data.response)==null?void 0:g.workerID)==="string"?(g=e.data.response)==null?void 0:g.workerID:null;e=typeof ((h=e.data.response)==null?void 0:h.isInitialized)==="boolean"?(h=e.data.response)==null?void 0:h.isInitialized:null;b({reason:f,workerID:g,isInitialized:e});(c==null?void 0:c.once)===!0&&a.port.removeEventListener("message",d)}},e=a.port;e.addEventListener("message",d);return function(){return e.removeEventListener("message",d)}}function d(a,b){a.port.addEventListener("message",function(a){var c;typeof a.data==="object"&&((c=a.data)==null?void 0:c.type)==="sw-uptime-tracking"&&typeof ((c=a.data)==null?void 0:c.response)==="object"&&typeof ((c=a.data)==null?void 0:(c=c.response)==null?void 0:c.trackerID)==="number"&&b({trackerID:a.data.response.trackerID})})}function e(a,b,d){var e=function(a){var e;if(typeof a.data==="object"&&((e=a.data)==null?void 0:e.type)==="execute-worker-ack"&&typeof ((e=a.data)==null?void 0:e.response)==="object"){e=typeof a.data==="object"&&typeof ((e=a.data)==null?void 0:e.response)==="object"?(a=(e=a.data)==null?void 0:e.response)!=null?a:{}:{};a=e.workerRevision;var f=e.spinMode,g=e.spinTime,h=e.firstInit,i=e.errorGettingSiteData;e=e.error;if(typeof h!=="boolean"){b==null?void 0:b.addAnnotations({string:{malformedData:"firstInit is non-boolean"}});c("FBLogger")("worker").mustfix("Expected firstInit to be a boolean, but instead received: %s",String(h));return}i=i!=null?i:e;if(typeof i==="string")return d({err:i,isFirstInit:h});if(typeof a==="number"&&typeof f==="number"&&typeof g==="number")return d({workerRev:a,workerSpinTime:g,workerSpinMode:f,isFirstInit:h});else b==null?void 0:b.addAnnotations({string:{malformedData:"workerRevision, spinMode, and/or spinTime are non-numbers"}}),c("FBLogger")("worker").mustfix("Expected workerRevision, spinMode, and spinTime to be non-null number, but instead received: %s %s %s",String(a),String(f),String(g))}},f=a.port;f.addEventListener("message",e);return function(){return f.removeEventListener("message",e)}}function f(a,b,d,e){var f=function f(g){var h;if(typeof g.data==="object"&&((h=g.data)==null?void 0:h.type)==="execute-worker-imports"&&typeof ((h=g.data)==null?void 0:h.response)==="object"){(e==null?void 0:e.once)===!0&&a.port.removeEventListener("message",f);b==null?void 0:b.markPoint("bundle_imports_ack");h=typeof g.data==="object"&&typeof ((h=g.data)==null?void 0:h.response)==="object"?(g=(h=g.data)==null?void 0:h.response)!=null?g:{}:{};g=h.err;h=h.attempts;if(g!=null&&typeof g!=="string"){b==null?void 0:b.addAnnotations({string:{malformedData:"err is neither null or a string"}});c("FBLogger")("worker").mustfix("Expected err to be null or a string, but instead receieved: %s",String(g));return}if(h!=null&&typeof h!=="number"){b==null?void 0:b.addAnnotations({string:{malformedData:"attempts is neither null or a number"}});c("FBLogger")("worker").mustfix("Expected attempts to be a number or null, but instead receieved: %s",String(h));return}d({err:g,attempts:h})}},g=a.port;g.addEventListener("message",f);return function(){return g.removeEventListener("message",f)}}function j(a,b,d){var e=function(a){var e;if(typeof a.data==="object"&&((e=a.data)==null?void 0:e.type)==="ww-hrp-init"){b==null?void 0:b.markEventEnd("hrp_init");e=typeof a.data==="object"&&typeof ((e=a.data)==null?void 0:e.response)==="object"?(a=(e=a.data)==null?void 0:e.response)!=null?a:{}:{};a=e.err;if(a!=null&&typeof a!=="string"){b==null?void 0:b.addAnnotations({string:{malformedData:"err is neither null or a string"}});c("FBLogger")("worker").mustfix("Expected err to be null or a string, but instead receieved: %s",String(a));return}d({err:a})}},f=a.port;f.addEventListener("message",e);return function(){return f.removeEventListener("message",e)}}function k(a,b){var c=function(a){var c;if(typeof a.data==="object"&&((c=a.data)==null?void 0:c.type)==="worker-init-mark"&&typeof ((c=a.data)==null?void 0:c.response)==="object"&&typeof ((c=a.data)==null?void 0:(c=c.response)==null?void 0:c.point)==="string"){b((c=a.data)==null?void 0:(a=c.response)==null?void 0:a.point)}},d=a.port;d.addEventListener("message",c);return function(){return d.removeEventListener("message",c)}}function l(a,b,c){var d=function d(e){var f;if(typeof e.data==="object"&&((f=e.data)==null?void 0:f.type)==="self-terminate"&&typeof ((f=e.data)==null?void 0:f.response)==="object"&&typeof ((f=e.data)==null?void 0:(f=f.response)==null?void 0:f.from)==="string"){var g,h,i;f=(f=e.data)==null?void 0:(f=f.response)==null?void 0:f.from;g=typeof ((g=e.data)==null?void 0:(g=g.response)==null?void 0:g.reason)==="string"?(g=e.data)==null?void 0:(g=g.response)==null?void 0:g.reason:"unknown";h=typeof ((h=e.data)==null?void 0:(h=h.response)==null?void 0:h.workerID)==="string"?(h=e.data)==null?void 0:(h=h.response)==null?void 0:h.workerID:"unknown";i=typeof ((i=e.data)==null?void 0:(i=i.response)==null?void 0:i.error)==="string"?(i=e.data)==null?void 0:(e=i.response)==null?void 0:e.error:void 0;(c==null?void 0:c.once)===!0&&a.port.removeEventListener("message",d);b(f,g,h,i)}},e=a.port;e.addEventListener("message",d);return function(){return e.removeEventListener("message",d)}}function m(a,b){b=b!=null?b:{};var c=b.reason;b=b.upgrade;a.port.postMessage({type:"sw-shutdown",args:[{reason:c,upgrade:b}]})}function n(a){a.port.postMessage({type:"sw-get-rev",args:[]})}function o(a,b){b=b!=null?b:{};var c=b.jsModuleResource;b=b.isDev;a.port.postMessage({type:"execute-worker",args:[c!=null?c:{},b]})}var p=["no-hrp","initialized","processed"];function q(a,b,c){var d=function d(e){var f;if(typeof e.data==="object"&&((f=e.data)==null?void 0:f.type)==="connection-ack"&&typeof ((f=e.data)==null?void 0:f.response)==="object"&&typeof ((f=e.data)==null?void 0:(f=f.response)==null?void 0:f.from)==="string"){var g;f=typeof ((f=e.data)==null?void 0:(f=f.response)==null?void 0:f.workerID)==="string"?(f=e.data)==null?void 0:(f=f.response)==null?void 0:f.workerID:void 0;g=typeof ((g=e.data)==null?void 0:(g=g.response)==null?void 0:g.hrpStatus)==="string"?(g=e.data)==null?void 0:(g=g.response)==null?void 0:g.hrpStatus:void 0;b((e=e.data)==null?void 0:(e=e.response)==null?void 0:e.from,f,g!=null&&p.includes(g)?g:void 0);(c==null?void 0:c.once)===!0&&a.port.removeEventListener("message",d)}},e=a.port;e.addEventListener("message",d);return function(){return e.removeEventListener("message",d)}}function r(a,b){var c=function(a){b(a.data)},d=a.port;d.addEventListener("message",c);return function(){return d.removeEventListener("message",c)}}function s(a,b){a.port.postMessage({type:"ww-connection-ack"}),b==null?void 0:b.markPoint("worker_connection_ack_requested")}function t(a,b,c){c=b.isDev;var d=b.hrp;b=b.js_env;a.port.postMessage({type:"ww-hrp-init",hrp:d,is_dev:c,js_env:b,tiered:!0})}function u(a,b){b=b.trackerID;a.port.postMessage({type:"sw-uptime-tracking",args:{trackerID:b}})}function v(a){var b=h(a,function(a){var b,c=a.args;a=a.method;(b=console)[a].apply(b,c)}),d=i(a,function(a){var b=a.error;a=a.errorObject;b!=null?c("FBLogger")("worker").catching(b).mustfix("Shared Worker uncaught error."):c("FBLogger")("worker").mustfix("Shared Worker uncaught error with no Error object. %s:%d:%d:%s",a.message,a.lineno,a.colno,a.filename)});return function(){b(),d()}}g.registerLogFowardingListener=h;g.registerUncaughtErrorForwardingListener=i;g.registerGetWorkerRevListener=a;g.registerWorkerShutdownListener=b;g.registerUptimeTrackerListener=d;g.registerExecuteWorkerAckListener=e;g.registerExecuteWorkerImportsListener=f;g.registerHrpInitListener=j;g.registerWorkerInitPointsListener=k;g.registerWorkerSelfTerminationListener=l;g.emitWorkerShutdown=m;g.emitGetRev=n;g.emitExecuteWorker=o;g.registerConnectionAckListener=q;g.registerAnyMessageListener=r;g.emitConnectionAckRequest=s;g.emitHrpInit=t;g.emitUptimeTracking=u;g.registerForwardListeners=v}),98);
__d("supportsNativeWebLock",[],(function(a,b,c,d,e,f){"use strict";function a(){return self.LockManager!=null&&self.navigator.locks instanceof self.LockManager}f["default"]=a}),66);
__d("SharedWorkerLockManager",["Deferred","FBLogger","WebStorageLockManager","emptyFunction","pageID","promiseDone","setTimeout","supportsNativeWebLock"],(function(a,b,c,d,e,f,g){"use strict";var h="shared_worker_mutex",i=6e5;function j(a){var b;b=(b=window)==null?void 0:(b=b.navigator)==null?void 0:b.locks;if(b!=null)return b;var e=d("WebStorageLockManager").make(c("pageID"));if(e!=null){a==null?void 0:a.addAnnotations({bool:{fallback_lock_manager:!0}});return{request:function(a,b){return e.request(a,b)}}}return null}function k(a,b,d){c("setTimeout")(function(){a.isSettled()||(b==null?void 0:b.endFailure("lock_timeout"),c("FBLogger")("worker").warn("[SharedWorkerLockManager]: lock timed out in %s ms",i),a.resolve(),d==null?void 0:d())},i)}function a(a,b){var d=b.workerQPLLogger,e=b.onLockTimeout;b=b.onLockFail;var f=j(d);d==null?void 0:d.addAnnotations({bool:{native_locks:f!=null?c("supportsNativeWebLock")():null}});if(f==null){d==null?void 0:d.markPoint("no_lock_manager");c("FBLogger")("worker").warn("Unable to initialize lock manager");a(c("emptyFunction"));return}d==null?void 0:d.markPoint("lock_requested");var g=new(c("Deferred"))();c("promiseDone")(f.request(h,function(){d==null?void 0:d.markPoint("lock_obtained");k(g,d,e);a(function(){d==null?void 0:d.markPoint("lock_released"),g.resolve()});return g.getPromise()}),void 0,b)}g.withWorkerLock=a}),98);
__d("SharedWorkerLoggingUtils",["ErrorSerializer","FBLogger","ODS","QPLUserFlow","getErrorSafe","pageID","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b,c,e){(h||(h=d("ODS"))).bumpEntityKey(6653,"shared_worker_infra","worker_self_termination."+b+"."+a),i(a,b,c,e,!0)}function i(a,b,d,e,f){f===void 0&&(f=!1);var g=c("qpl")._(931600201,"1438");c("QPLUserFlow").start(g);c("QPLUserFlow").endSuccess(g,{annotations:{string:{workerName:a,workerID:e,reason:d,location:b,pageID:c("pageID")},bool:{isSelfTermination:f}}})}function b(a,b,c,d,e,f){a==null?void 0:a.markEventEnd("execute_worker"),a==null?void 0:a.addAnnotations({"int":{runningWorkerRev:e,runningWorkerSpinTime:b,runningWorkerSpinMode:f},string:{executeWorkerErr:c},bool:{isFirstInit:d}})}function e(a,b,d){var e;e=c("getErrorSafe")((e=b instanceof ErrorEvent&&(b==null?void 0:b.error))!=null?e:b);c("FBLogger")("worker").catching(e).warn("Worker %s onerror: %s type, %s message",d,String(b.type),c("ErrorSerializer").toReadableMessage(e));a==null?void 0:a.addAnnotations({string_array:{onErrorHandler:[(a=JSON.stringify((d=e==null?void 0:e.message)!=null?d:b==null?void 0:b.message))!=null?a:"undefined",String((d=b==null?void 0:b.lineno)!=null?d:e==null?void 0:e.lineno),String((a=b==null?void 0:b.colno)!=null?a:e==null?void 0:e.colno),String((d=b==null?void 0:b.filename)!=null?d:e==null?void 0:e.filename)]}})}g.logSelfTermination=a;g.logShutdown=i;g.logExecuteAck=b;g.logSharedWorkerError=e}),98);
__d("SharedWorkerStatusLock",[],(function(a,b,c,d,e,f){"use strict";var g="sw-lock",h="sw-ds-lock";function i(a){return g+"-"+a}function j(a){return h+"-"+a}function k(a,b){return g+"-"+a+"-"+b}function a(){var a=null,b;function c(c){var d=self,e=d.navigator.locks;d.LockManager!=null&&e instanceof d.LockManager&&(b==null&&(b=new d.Promise(function(b){a=b})),e.request(i(d.name),function(a){if(a!=null)return b}),c.requestDynamicStringLockToo===!0&&e.request(j(d.name),function(a){if(a!=null)return b}),c.requestWorkerIdStatusLockToo===!0&&e.request(k(d.name,d.worker_id),function(a){if(a!=null){c.onUniqueLockAckquired==null?void 0:c.onUniqueLockAckquired();return b}}))}function d(){a==null?void 0:a(),b=null,a=null}return{init:c,release:d}}f.getStatusLockName=i;f.getDynamicStringInitLockName=j;f.getWorkerIdStatusLockName=k;f.getStatusLock=a}),66);
__d("WebAsyncStorage",["Deferred","Promise","err","mergeDeepInto","mergeHelpers"],(function(a,b,c,d,e,f,g){"use strict";var h;a=typeof window!=="undefined"?window:self;var i=a.indexedDB,j=1,k="data",l=!1,m;d=Date.now();var n=new(c("Deferred"))(),o=3;function p(){var a=i.open("AsyncStorage",j);a.onerror=function(b){b=(b=a.error)!=null?b:new Error("Failed to open AsyncStorage IndexedDB instance.");if(b.name==="UnknownError"&&o>0){o--;return p()}n.reject(b)};a.onsuccess=function(a){l&&!1,m=a.target.result,n.resolve({db:m}),m.onerror=function(a){a=new Error("AsyncStorage error: "+a.target.error.message);n.reject(a)}};a.onupgradeneeded=function(a){a=a.currentTarget.result;if(a.objectStoreNames&&a.objectStoreNames.contains(k))return;a.createObjectStore(k,{keyPath:"key"})}}i&&p();function q(a,b){if(m)try{a()}catch(a){b&&b(a)}else n.getPromise().then(function(){l&&!1,q(a,b)},function(a){b&&b(a)})}e={setItem:function(a,b,c){this.multiSet([[a,b]],function(a){c(a&&a[0]||null)})},getItem:function(a,b){this.multiGet([a],function(a,c){c=c!==void 0&&c[0]!==void 0&&c[0][1]!==void 0?c[0][1]:null;b(a&&a[0]||null,c)})},removeItem:function(a,b){this.multiRemove([a],function(a){b(a&&a[0]||null)})},getOrSetItem:function(a,b,c){this.multiGetOrSet([[a,b]],function(a,b){b=b!==void 0&&b[0]!==void 0&&b[0][1]!==void 0?b[0][1]:null;c(a&&a[0]||null,b)})},upsertAndSaveAtomic:function(a,b,c){var d=null;this._multiOp([a],"readwrite",function(a){return c(a,d)},function(a){return a},function(a,c,e){if(c&&a===c.key){var f=b(c.value.value);if(f!=null){var g=c.value;g.value=f;d=f;c.update(g)}}else{f=b(null);f!=null&&(d=f,e.push([a,f]))}})},multiGet:function(a,b){var c=[];this._multiOp(a,"readonly",function(a){return b(a,c)},function(a){return a},function(a,b,d){b&&a===b.key?(l&&!1,c.push([a,b.value.value])):c.push([a,null])})},multiSet:function(a,b){if(this._persistentWritesDisabled){b(new Array(a.length).fill("writes disabled"));return}this._multiOp(a,"readwrite",b,function(a){return a[0]},function(a,b,c){if(b&&a[0]===b.key){var d=b.value;d.value=a[1];l&&!1;b.update(d)}else c.push(a)})},multiGetOrSet:function(a,b){if(this._persistentWritesDisabled){b(new Array(a.length).fill("writes disabled"));return}var c=[];this._multiOp(a,"readwrite",function(a){return b(a,c)},function(a){return a[0]},function(a,b,d){b&&a[0]===b.key?(c.push([a,b.value.value]),l&&!1):d.push(a)})},multiMerge:function(a,b){this._multiOp(a,"readwrite",b,function(a){return a[0]},function(a,b,d){if(b&&a[0]===b.key){var e=b.value,f=JSON.parse(e.value);c("mergeDeepInto")(f,JSON.parse(a[1]),c("mergeHelpers").ArrayStrategies.Clobber);l&&!1;e.value=JSON.stringify(f);b.update(e)}else l&&!1,d.push(a)})},multiRemove:function(a,b){this._multiOp(a,"readwrite",b,function(a){return a},function(a,b,c){b&&a===b.key&&(l&&!1,b["delete"]())})},getAllKeys:function(a){q(function(){var b=m.transaction([k],"readonly");b=b.objectStore(k).openCursor();var c=[];b.onsuccess=function(b){b=b.target.result;if(!b){a(null,c);return}c.push(b.key);b["continue"]()}},function(b){return a(null,[])})},clear:function(a){q(function(){var b=m.transaction([k],"readwrite");b=b.objectStore(k).openCursor();b.onsuccess=function(b){b=b.target.result;if(!b){a(null);return}b["delete"]();b["continue"]()}},function(b){return a(null)})},_multiOp:function(a,b,c,d,e){q(function(){var f=!1,g=a.slice().sort(function(a,b){a=d(a);b=d(b);if(a===b){var e=new Error("AsyncStorage._multiOp cannot process duplicate keys.");c&&c([e]);f=!0;return 0}return a<b?-1:1});if(f)return;var h=m.transaction([k],b),i=h.objectStore(k).openCursor(),j=[],n=0;i.onsuccess=function(a){a=a.target.result;if(!a){while(n<g.length)e(g[n],a,j),n++;o();return}var b=a.key;l&&!1;while(d(g[n])<=b){l&&!1;e(g[n],a,j);n++;if(n===g.length){o();return}}b=d(g[n]);a["continue"](b)};function o(){var a=h.objectStore(k);j.forEach(function(b){l&&!1,a.add({key:b[0],value:b[1]})})}h.oncomplete=function(){c&&c(null)};h.onerror=function(a){a=new Error("IndexedDB error: "+a.target.error.message);c&&c([a])};l&&!1},function(a){c&&c([a])})},_persistentWritesDisabled:!1,disablePersistentWrites:function(){this._persistentWritesDisabled=!0},isOpenPromiseSettled:function(){return n.isSettled()},isOperational:function(){return i==null?(h||(h=b("Promise"))).resolve({success:!1,error:c("err")("IDB interface not available")}):n.getPromise().then(function(){return{success:!0}})["catch"](function(a){return{success:!1,error:a}})}};f=e;g["default"]=f}),98);
__d("validateSharedWorkerReference",["ConstUriUtils","isRelativeURL","isSameOrigin","memoize"],(function(a,b,c,d,e,f,g){"use strict";var h=c("memoize")(function(){return d("ConstUriUtils").getUri(window.location.href)});function i(a){var b=h(),e=d("ConstUriUtils").getUri(a);e=b!=null&&e!=null?c("isSameOrigin")(e,b):!1;return e||c("isRelativeURL")(a)}function a(a){if(a==null||typeof a!=="object")return null;var b=a.rev,c=a.spin_time,d=a.url,e=a.version,f=a.rsrcBundleUrl;a=a.sandboxOnlyChecksum;return typeof b!=="number"||typeof c!=="number"||e!=null&&e!==1&&e!==2||!Number.isInteger(b)||!Number.isInteger(c)||typeof d!=="string"||!i(d)||f!=null&&typeof f!=="string"||a!=null&&typeof a!=="string"?null:{rev:b,spin_time:c,url:d,version:e!=null?e:1,rsrcBundleUrl:f,sandboxOnlyChecksum:a}}g["default"]=a}),98);
__d("SharedWorkerStorageManager",["Deferred","FBLogger","Promise","WebAsyncStorage","validateSharedWorkerReference"],(function(a,b,c,d,e,f,g){"use strict";var h,i="__swbundle__",j={};function k(a){return""+i+a}function l(a){var b=new(c("Deferred"))();c("WebAsyncStorage").removeItem(k(a),function(c){if(c)return b.reject(c);j[a]=null;b.resolve()});return b.getPromise()}function m(a,b){var d=new(c("Deferred"))();c("WebAsyncStorage").setItem(k(a),b,function(c){if(c!=null)return d.reject(c);j[a]=b;d.resolve()});return d.getPromise()}function n(a){var b=new(c("Deferred"))();c("WebAsyncStorage").getItem(k(a),function(c,d){if(c)return b.reject(c);j[a]=d;b.resolve(d)});return b.getPromise()}function a(){return j}function d(a,b){return n(a).then(function(a){a=c("validateSharedWorkerReference")(a);return a==null?null:a})["catch"](function(d){c("FBLogger")("worker").catching(d).mustfix("Failed to getSharedWorkerReference for bundleName %s",a);b==null?void 0:b.addAnnotations({bool:{failedToGetSharedWorkerResource:!0}});throw d})}function e(a,b){return l(a)["catch"](function(d){c("FBLogger")("worker").catching(d).mustfix("Failed to removeSharedWorkerReference for bundleName %s",a);b==null?void 0:b.addAnnotations({bool:{failedToDeleteSharedWorkerResource:!0}});throw d})}function f(a,d,e){var f=c("validateSharedWorkerReference")(d);if(f==null){c("FBLogger")("worker").mustfix('Failed to save worker reference for bundle "%s". Invalid reference: %s',a,JSON.stringify(d));return(h||(h=b("Promise"))).resolve()}return m(a,f)["catch"](function(b){c("FBLogger")("worker").catching(b).mustfix("Failed to saveSharedWorkerReference for bundleName %s",a);e==null?void 0:e.addAnnotations({bool:{failedToSaveSharedWorkerResource:!0}});throw b})}function o(a,b,d){var e=new(c("Deferred"))();c("WebAsyncStorage").upsertAndSaveAtomic(k(a),function(a){a=c("validateSharedWorkerReference")(a);return b(a)},function(b,f){if(b){d==null?void 0:d.addAnnotations({bool:{failedToGetOrUpdateSharedWorkerResource:!0}});c("FBLogger")("worker").catching(b).mustfix("Failed to getOrUpdateWorkerReference for bundleName %s",a);return e.reject(b)}j[a]=f;e.resolve(f)});return e.getPromise()}g.SHARED_WORKER_STORAGE_KEY_BASE=i;g.getDebugState=a;g.getSharedWorkerReference=d;g.removeSharedWorkerReference=e;g.saveSharedWorkerReference=f;g.getOrUpdateWorkerReference=o}),98);
__d("TrustedTypesWebWorkerScriptURLPolicy",["TrustedTypes"],(function(a,b,c,d,e,f,g){"use strict";a={createScriptURL:function(a){return a}};b=c("TrustedTypes").createPolicy("web-worker-url",a);d=b;g["default"]=d}),98);
__d("XCometFBMultiSiteWebWorkerV2InitScriptControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/static_resources/webworker/init_script/",Object.freeze({}),void 0);b=a;g["default"]=b}),98);
__d("supportsModuleWorker",["UserAgent"],(function(a,b,c,d,e,f,g){"use strict";var h=null;function a(a){if(h!=null)return h;if(h!=null)return h;if(c("UserAgent").isEngine("Blink")){h=a?c("UserAgent").isEngine_DEPRECATED_DANGEROUS("Blink > 83"):c("UserAgent").isEngine_DEPRECATED_DANGEROUS("Blink > 80.1");return h}if(c("UserAgent").isBrowser("Safari")){h=c("UserAgent").isBrowser("Safari > 14.9");return h}if(c("UserAgent").isBrowser("Firefox")){h=c("UserAgent").isBrowser("Firefox > 113");return h}if(c("UserAgent").isEngine("WebKit")){h=c("UserAgent").isEngine_DEPRECATED_DANGEROUS("WebKit > 606");return h}if(c("UserAgent").isBrowser("IE")){h=!1;return h}var b={get type(){h=!0}};try{a?new SharedWorker("blob://",b):new Worker("blob://",b)}finally{h=(a=h)!=null?a:!1;return h}}g["default"]=a}),98);
__d("createSharedWorkerV2BundleUrlExperimental",["XCometFBMultiSiteWebWorkerV2InitScriptControllerRouteBuilder","nullthrows","supportsModuleWorker"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=c("supportsModuleWorker")(!0);return c("nullthrows")((b=c("XCometFBMultiSiteWebWorkerV2InitScriptControllerRouteBuilder").buildUri({worker_type:b?"MODULE":"CLASSIC",name:a}))==null?void 0:b.toString())}g["default"]=a}),98);
__d("SharedWorkerMigrationUtils",["ConstUriUtils","Deferred","FBLogger","Promise","SharedWorkerEventManager","SharedWorkerStatusLock","SharedWorkerStorageManager","TrustedTypesWebWorkerScriptURLPolicy","asyncToGeneratorRuntime","clearTimeout","createSharedWorkerV2BundleUrlExperimental","err","justknobx","setTimeout","supportsModuleWorker","supportsNativeWebLock"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){if(!c("supportsNativeWebLock")())return!0;var b=(yield self.navigator.locks.query());return b.held.some(function(b){return b.name===d("SharedWorkerStatusLock").getDynamicStringInitLockName(a)})});return j.apply(this,arguments)}function a(){return c("supportsNativeWebLock")()}function k(a){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){if(!c("supportsNativeWebLock")())return!0;var b=(yield self.navigator.locks.query());return b.held.some(function(b){return b.name===d("SharedWorkerStatusLock").getStatusLockName(a)})});return l.apply(this,arguments)}function e(a,b){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){if(!c("supportsNativeWebLock")())return!0;var e=(yield self.navigator.locks.query());return e.held.some(function(c){return c.name===d("SharedWorkerStatusLock").getWorkerIdStatusLockName(a,b)})});return m.apply(this,arguments)}var n={SharedWorkerBundle:c("justknobx")._("2852"),MAWMainWebWorkerV2Bundle:c("justknobx")._("2920")};function f(a){return(a=n[a])!=null?a:!1}function o(a){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){if(!(yield i(a)))return!1;var b=new(c("Deferred"))(),e=c("createSharedWorkerV2BundleUrlExperimental")(a);e=c("TrustedTypesWebWorkerScriptURLPolicy").createScriptURL(e);e=c("supportsModuleWorker")(!0)?new SharedWorker(e,{name:a,type:"module"}):new SharedWorker(e,a);d("SharedWorkerEventManager").registerWorkerShutdownListener(e,function(a){b.resolve(!0)},{once:!0});d("SharedWorkerEventManager").emitWorkerShutdown(e,{upgrade:!1,reason:"dynamic_string_worker_rollback"});e.port.start();return b.getPromise()});return p.apply(this,arguments)}function q(a){a=d("ConstUriUtils").getUri(a);return a!=null&&a.getQueryParam("hash")!=null}function r(a){return s.apply(this,arguments)}function s(){s=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var e=a.name,f=a.logger;try{a=(yield (h||(h=b("Promise"))).all([k(e),i(e)]));var g=a[0];a=a[1];if(!g||c("supportsNativeWebLock")()&&a){f.markPoint("legacy_termination_skipped");f.addAnnotations({bool:{dynamicStringWorkerAlreadyRunning:a,workerAlreadyRunning:g,native_locks:c("supportsNativeWebLock")()}});return}a=(yield d("SharedWorkerStorageManager").getSharedWorkerReference(e));f.markPoint("storage_read");f.addAnnotations({bool:{storageResourceSet:a!=null}});if(a==null)return;try{yield d("SharedWorkerStorageManager").removeSharedWorkerReference(e),f.markPoint("storage_cleaned")}catch(a){f.markPoint("storage_error"),f.addAnnotations({string:{storageError:a.toString()}}),c("FBLogger")("worker").catching(a).mustfix("Failed to remove reference from storage.")}g=q(a.url);f.markPoint(g?"legacy_termination_start":"termination_skipped");if(g){var j=new(c("Deferred"))();g=c("TrustedTypesWebWorkerScriptURLPolicy").createScriptURL(a.url);a=c("supportsModuleWorker")(!0)?new SharedWorker(g,{name:e,type:"module"}):new SharedWorker(g,e);g=function(){f.markPoint("legacy_termination_end"),j.resolve(!0)};d("SharedWorkerEventManager").registerWorkerShutdownListener(a,g,{once:!0});d("SharedWorkerEventManager").registerWorkerSelfTerminationListener(a,g,{once:!0});a.port.start();d("SharedWorkerEventManager").emitWorkerShutdown(a,{upgrade:!1,reason:"maw-request-refresh"});e=c("setTimeout")(function(){f.markPoint("legacy_termination_timeout"),j.reject(c("err")("Failed to close Shared Web Worker properly. SelfTerminationTimeout was exceeded."))},1e3);yield j.getPromise();c("clearTimeout")(e)}}catch(a){f.markPoint("legacy_check_and_termination_error"),f.addAnnotations({string:{legacyCheckError:a.toString()}}),c("FBLogger")("worker").catching(a).mustfix("Failed to check storage and possibly terminate legacy worker.")}});return s.apply(this,arguments)}g.isDynamicStringWorkerRunning=i;g.supportsNativeWebLocks=a;g.isStatusLockHeld=k;g.isWorkerIdStatusLockHeld=e;g.shouldCheckForDynamicStringWorker=f;g.maybeTerminateDynamicStringWorker=o;g.isLegacyWorkerURL=q;g.checkStorageAndTerminateLegacyWorker=r}),98);
__d("SharedWorkerUptimeTracker",["FBLogger","ODS","SharedWorkerEventManager","clearInterval","performanceNow","setInterval","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=1e4,k=45e3,l=new Map();function m(a){return Math.floor(((i||(i=c("performanceNow")))()-a)/1e3)}function n(a,b){var e=0,f={};d("SharedWorkerEventManager").registerUptimeTrackerListener(a,function(a){a=a.trackerID;var c=f[a];if(c==null)return;(h||(h=d("ODS"))).bumpFraction(6653,"shared_worker_infra","worker_uptime."+b,1,0);h.bumpEntityKey(6653,"shared_worker_infra","worker_uptime_elapsed_time_to_response_sec."+m(c.createdMs)+"."+b);delete f[a]});return c("setInterval")(function(){var g=e++;c("setTimeout")(function(){var a=f[g];if(a==null)return;delete f[g]},k);f[g]={createdMs:(i||(i=c("performanceNow")))()};(h||(h=d("ODS"))).bumpFraction(6653,"shared_worker_infra","worker_uptime."+b,0,1);d("SharedWorkerEventManager").emitUptimeTracking(a,{trackerID:g})},j)}function a(a,b){if(l.has(b)){c("FBLogger")("worker").mustfix('Uptime tracking for worker "%s" already started.',b);return}l.set(b,{intervalID:n(a,b)})}function b(a){var b=l.get(a);b!=null&&(c("clearInterval")(b.intervalID),l["delete"](a))}g.startUptimeTracking=a;g.stopUptimeTracking=b}),98);
__d("XCometFBMultiSiteWebWorkerV2HasteResponseControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/static_resources/webworker/rsrc/",Object.freeze({}),void 0);b=a;g["default"]=b}),98);
__d("WebWorkerV2DynamicData",["FBLogger","Promise","XCometFBMultiSiteWebWorkerV2HasteResponseControllerRouteBuilder","cometAsyncFetchShared","err","gkx","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Map();function a(a,b){b===void 0&&(b=!1);var c=a.name,d=i.get(c);if(d==null||b){var e=Math.floor(+Date.now()/1e3);d=j(a,b).then(function(a){return{time:e,data:a}});i.set(c,d)}return d}function j(a,d){var e=a.name,f=a.v2HasteResponsePreloader;if(f==null||d)return k(e,d);var g=null,i=c("gkx")("14133");a=new(h||(h=b("Promise")))(function(a,b){f.onLoaded(function(d){d=d.data;(d==null||d.hrp==null)&&(i&&b(c("err")("Preloaded data for worker %s is missing haste response",e)),c("FBLogger")("worker").mustfix("Preloaded data for worker %s is missing haste response, preload data keys: %s",e,d==null?"null":Object.keys(d).join(", ")));g=d;a(d)}).onError(b)});if(i){var j,l,m=function(){l==null&&(l=k(e,d));return l};return(h||(h=b("Promise"))).race([a["catch"](function(){return m()}),new h(function(a,b){j=window.setTimeout(function(){c("promiseDone")(m(),a,b)},1e4)})]).then(function(a){window.clearTimeout(j);return a})}return h.race([a,new h(function(a,b){window.setTimeout(function(){g==null?c("promiseDone")(k(e,d),a,b):a(g)},1e4)})])}function k(a,b){var d=null;b&&(d=new FormData(),d.set("__rev","null"),d.set("__spin_r","null"));return c("cometAsyncFetchShared")(c("XCometFBMultiSiteWebWorkerV2HasteResponseControllerRouteBuilder").buildUri({worker_module:a}).toString(),{formData:(d=d)!=null?d:void 0,data:{},getFullPayload:!0,method:"POST",skipSRState:!0,retryCount:b?2:void 0}).then(function(b){if(b!=null&&typeof b==="object"&&Object.prototype.hasOwnProperty.call(b,"hrp")&&typeof b.hrp==="object")return b;throw c("err")("Unexpected data from WorkerInitResourceDeliveryController for worker %s",a)})}g.readDynamicDataForWorker=a}),98);
__d("checkForIndexedDbSupported",[],(function(a,b,c,d,e,f){"use strict";function a(){return!window.indexedDB?!1:!0}f["default"]=a}),66);
__d("buildSharedWorkerInitQPLLogger",["QPLUserFlow","SiteData","checkForIndexedDbSupported","pageID","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h=c("qpl")._(931595918,"2062"),i=0;function a(a){var b=!1,d=!1,e=i++;function f(f){var g=f.version,i=f.workerName,j=f.callReason,k=f.experimental_init;f=f.migrationEnabled;if(b||d)return;b=!0;g={"int":{version:g,clientRev:c("SiteData").client_revision},bool:{isDev:!1,supportsIDB:c("checkForIndexedDbSupported")(),experimental_init:k!=null?k:!1,migrationEnabled:f!=null?f:!1},string:{workerName:i,hostname:window.location.hostname,callReason:j,pageID:c("pageID")}};c("QPLUserFlow").start(h,{onFlowTimeout:function(){c("QPLUserFlow").addAnnotations(h,{bool:{setupTimeout:!0}},{instanceKey:e}),d=!0},cancelOnUnload:!0,annotations:g,timeoutInMs:66e4,instanceKey:e});a==null?void 0:a({type:"start",data:{annotations:g}})}function g(f,g){f=f+"_start";a==null?void 0:a({type:"point",data:{annotations:g,event:f}});if(!b||d)return;c("QPLUserFlow").addPoint(h,f,{data:g,instanceKey:e})}function j(f,g){f=f+"_end";a==null?void 0:a({type:"point",data:{annotations:g,event:f}});if(!b||d)return;c("QPLUserFlow").addPoint(h,f,{data:g,instanceKey:e});a==null?void 0:a({type:"point",data:{annotations:g,event:f}})}function k(f,g){a==null?void 0:a({type:"point",data:{annotations:g,event:f}});if(!b||d)return;c("QPLUserFlow").addPoint(h,f,{data:g,instanceKey:e})}function l(f){a==null?void 0:a({type:"end",data:{annotations:f}});if(!b||d)return;c("QPLUserFlow").endSuccess(h,{annotations:f,instanceKey:e});d=!0}function m(f,g){a==null?void 0:a({type:"end",data:{errorName:f,annotations:g}});if(!b||d)return;c("QPLUserFlow").endFailure(h,f,{annotations:g,instanceKey:e});d=!0}function n(f){a==null?void 0:a({type:"end",data:{annotations:f,cancel:!0}});if(!b||d)return;c("QPLUserFlow").endCancel(h,{annotations:f,instanceKey:e});d=!0}function o(f){a==null?void 0:a({type:"annotate",data:{annotations:f}});if(!b||d)return;c("QPLUserFlow").addAnnotations(h,f,{instanceKey:e})}return{start:f,endFailure:m,addAnnotations:o,endSuccess:l,endCancel:n,markEventStart:g,markEventEnd:j,markPoint:k}}g["default"]=a}),98);
__d("buildSharedWorkerTerminateQPLLogger",["QPLUserFlow","SiteData","pageID","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h=c("qpl")._(931600201,"1438"),i=0;function a(a){var b=!1,d=!1,e=i++;function f(f){var g=f.workerName;f=f.callReason;if(b||d)return;b=!0;g={"int":{clientRev:c("SiteData").client_revision},bool:{isDev:!1},string:{workerName:g,hostname:window.location.hostname,callReason:f,pageID:c("pageID")}};c("QPLUserFlow").start(h,{onFlowTimeout:function(){c("QPLUserFlow").addAnnotations(h,{bool:{setupTimeout:!0}},{instanceKey:e}),d=!0},cancelOnUnload:!0,annotations:g,timeoutInMs:3e4,instanceKey:e});a==null?void 0:a({type:"start",data:{annotations:g}})}function g(f,g){if(!b||d)return;f=f+"_start";c("QPLUserFlow").addPoint(h,f,{data:g,instanceKey:e});a==null?void 0:a({type:"point",data:{annotations:g,event:f}})}function j(f,g){if(!b||d)return;f=f+"_end";c("QPLUserFlow").addPoint(h,f,{data:g,instanceKey:e});a==null?void 0:a({type:"point",data:{annotations:g,event:f}})}function k(f,g){if(!b||d)return;c("QPLUserFlow").addPoint(h,f,{data:g,instanceKey:e});a==null?void 0:a({type:"point",data:{annotations:g,event:f}})}function l(f){if(!b||d)return;c("QPLUserFlow").endSuccess(h,{annotations:f,instanceKey:e});a==null?void 0:a({type:"end",data:{annotations:f}});d=!0}function m(f,g){if(!b||d)return;c("QPLUserFlow").endFailure(h,f,{annotations:g,instanceKey:e});a==null?void 0:a({type:"end",data:{errorName:f,annotations:g}});d=!0}function n(f){if(!b||d)return;c("QPLUserFlow").endCancel(h,{annotations:f,instanceKey:e});a==null?void 0:a({type:"end",data:{annotations:f,cancel:!0}});d=!0}function o(f){if(!b||d)return;c("QPLUserFlow").addAnnotations(h,f,{instanceKey:e});a==null?void 0:a({type:"annotate",data:{annotations:f}})}return{start:f,endFailure:m,addAnnotations:o,endSuccess:l,endCancel:n,markEventStart:g,markEventEnd:j,markPoint:k}}g["default"]=a}),98);
__d("XCometFBMultiSiteSharedWorkerStaticInitScriptControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/static_resources/sharedworker/init_script/",Object.freeze({}),void 0);b=a;g["default"]=b}),98);
__d("getSharedWorkerHash",["uuidv4"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("uuidv4")()}g["default"]=a}),98);
__d("getWorkerInitScriptSPINParams",["SiteData","StaticSiteData","objectEntries"],(function(a,b,c,d,e,f,g){"use strict";function a(){var a,b,d,e=(b=c("SiteData"))[(d=c("StaticSiteData")).spin_mhenv_key];d=babelHelpers["extends"]((a={},a[d.hs_key]=b.haste_session,a[d.spin_rev_key]=b[d.spin_rev_key],a[d.spin_branch_key]=b[d.spin_branch_key],a[d.spin_time_key]=b[d.spin_time_key],a),Boolean(e)?(b={},b[c("StaticSiteData").spin_mhenv_key]=e,b):null);return new Map(c("objectEntries")(d))}g["default"]=a}),98);
__d("createSharedWorkerBundleUrl",["XCometFBMultiSiteSharedWorkerStaticInitScriptControllerRouteBuilder","getSharedWorkerHash","getWorkerInitScriptSPINParams","nullthrows"],(function(a,b,c,d,e,f,g){"use strict";function a(){var a;return c("nullthrows")((a=c("XCometFBMultiSiteSharedWorkerStaticInitScriptControllerRouteBuilder").buildUri({hash:c("getSharedWorkerHash")()}).addQueryParams(c("getWorkerInitScriptSPINParams")()))==null?void 0:a.toString())}g["default"]=a}),98);
__d("createSharedWorkerV2BundleUrl",["BootloaderDocumentInserter","BootloaderPreloader","TrustedTypesWebWorkerScriptURLPolicy","XCometFBMultiSiteWebWorkerV2InitScriptControllerRouteBuilder","getSharedWorkerHash","getWorkerInitScriptSPINParams","gkx","justknobx","nullthrows","qex","supportsModuleWorker"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=c("supportsModuleWorker")(!0),b=c("getWorkerInitScriptSPINParams")();b.set("hash",c("getSharedWorkerHash")());b.set("use_broadcast",c("justknobx")._("4177"));b.set("worker_id_in_terminate",c("gkx")("12766"));b.set("with_worker_id_lock",c("qex")._("4412"));return c("nullthrows")((a=c("XCometFBMultiSiteWebWorkerV2InitScriptControllerRouteBuilder").buildUri({worker_type:a?"MODULE":"CLASSIC"}))==null?void 0:(a=a.addQueryParams(b))==null?void 0:a.toString())}var i=c("gkx")("13019")?h():null;a=function(){if(i==null)return;var a=c("TrustedTypesWebWorkerScriptURLPolicy").createScriptURL(i);d("BootloaderDocumentInserter").batchDOMInsert(function(b){d("BootloaderPreloader").preloadResource({type:"js",src:a.toString(),nc:1},b)})};b=function(){var a=i;if(a!=null){i=null;return a}return h()};g.maybePreloadSharedWorkerV2BundleUrl=a;g.createSharedWorkerV2BundleUrl=b}),98);
__d("handleWorkerWriteError",["FBLogger","WebStorageCleanupReason"],(function(a,b,c,d,e,f,g){"use strict";function a(a){if(a instanceof Error){c("FBLogger")("worker").catching(a).mustfix("Error occured checking in shared worker reference");return}if(a==="writes disabled"){var b;c("FBLogger")("worker").warn("Writes to WebAsyncStorage is disabled. Unable to save shared worker reference. Reason: %s",(b=d("WebStorageCleanupReason").getLastCleanupReason())!=null?b:"n/a");return}c("FBLogger")("worker").mustfix("Error occured checking in shared worker reference: %s",JSON.stringify(a))}g["default"]=a}),98);
__d("SharedWorkerInitFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("4266");b=d("FalcoLoggerInternal").create("shared_worker_init",a);e=b;g["default"]=e}),98);
__d("logSharedWorkerInitStep",["SharedWorkerInitFalcoEvent","SiteData","gkx","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h=c("uuidv4")();function a(a,b,d,e){if(!c("gkx")("21115"))return;c("SharedWorkerInitFalcoEvent").log(function(){return{client_id:h,bundle_name:a,client_rev:String(c("SiteData").client_revision),local_shared_worker_ref:b!=null?{bundle_url:b.url,rev:String(b.rev),spin_time:String(b.spin_time)}:null,new_shared_worker_ref:d!=null?{bundle_url:d}:null,init_step:e}})}g["default"]=a}),98);
__d("SharedWorkerBundleResource",["BootloaderPreloader","BrowserLockManager","Deferred","FBLogger","Promise","SharedWorkerDevChangeManager","SharedWorkerEventManager","SharedWorkerLockManager","SharedWorkerLoggingUtils","SharedWorkerMigrationUtils","SharedWorkerStatusLock","SharedWorkerStorageManager","SharedWorkerUptimeTracker","SharedWorkerV2ResourceExperimental","SiteData","StaticSiteData","TrustedTypesWebWorkerScriptURLPolicy","WebWorkerV2DynamicData","asyncToGeneratorRuntime","buildSharedWorkerInitQPLLogger","buildSharedWorkerTerminateQPLLogger","createSharedWorkerBundleUrl","createSharedWorkerV2BundleUrl","createSharedWorkerV2BundleUrlExperimental","err","getErrorSafe","gkx","handleWorkerWriteError","justknobx","logSharedWorkerInitStep","pageID","promiseDone","qex","setTimeout","supportsModuleWorker","supportsNativeWebLock"],(function(a,b,c,d,e,f,g){"use strict";var h,i=5e3,j="shared_worker_broadcast_channel";d("createSharedWorkerV2BundleUrl").maybePreloadSharedWorkerV2BundleUrl();var k=function(b){babelHelpers.inheritsLoose(a,b);function a(a){var c;c=b.call(this)||this;c.name="SelfTerminationError";c.message="Worker self terminated with the reason: "+a;c.reason=a;return c}return a}(babelHelpers.wrapNativeSuper(Error));function l(a,b,e,f){f==null?void 0:f.markEventStart("shared_worker_construct");var g=new SharedWorker(c("TrustedTypesWebWorkerScriptURLPolicy").createScriptURL(a),b);f==null?void 0:f.markEventEnd("shared_worker_construct");g.addEventListener("error",function(a){d("SharedWorkerLoggingUtils").logSharedWorkerError(f,a,b)});var h=new Set();d("SharedWorkerEventManager").registerWorkerSelfTerminationListener(g,function(a,c,e){d("SharedWorkerLoggingUtils").logSelfTermination(b,a,c,e)},{once:!0});var i=0;d("SharedWorkerEventManager").registerConnectionAckListener(g,function(a,b){i++;if(h.has("worker_connection_established")){f==null?void 0:f.addAnnotations({"int":{connectionAckNum:i}});return}h.add("worker_connection_established");f==null?void 0:f.addAnnotations({string:{connectedFrom:a,workerID:b}});f==null?void 0:f.markPoint("worker_connection_established")});d("SharedWorkerEventManager").registerWorkerInitPointsListener(g,function(a){if(h.has(a))return;h.add(a);f==null?void 0:f.markPoint("worker_init_point_"+a)});g.port.start();d("SharedWorkerEventManager").registerExecuteWorkerImportsListener(g,f,function(a){var b=a.err;a=a.attempts;f==null?void 0:f.addAnnotations({string:{importsError:b},"int":{importAttempts:a}});f==null?void 0:f.markPoint(b==null?"bundle_imports_success":"bundle_imports_failure")});var j=new(c("Deferred"))();d("SharedWorkerEventManager").registerExecuteWorkerAckListener(g,f,function(a){var b=a.workerSpinTime,e=a.err,g=a.isFirstInit,h=a.workerRev;a=a.workerSpinMode;d("SharedWorkerLoggingUtils").logExecuteAck(f,b,e,g,h,a);e!=null||b==null||h==null?j.reject(c("err")(e!=null?e:"no worker rev or spin time")):j.resolve({rev:h,spinTime:b})});f==null?void 0:f.markEventStart("execute_worker");d("SharedWorkerEventManager").emitExecuteWorker(g,{jsModuleResource:e,isDev:!1});return j.getPromise().then(function(a){var b=a.rev;a=a.spinTime;return{worker:g,rev:b,spinTime:a}})}function m(a,b,d){a=c("TrustedTypesWebWorkerScriptURLPolicy").createScriptURL(a);b=c("supportsModuleWorker")(!0)?new SharedWorker(a,{name:b,type:"module"}):new SharedWorker(a,b);d==null?void 0:d.markPoint("shared_worker_construct_done");d==null?void 0:d.addAnnotations({string:{workerUrl:a.toString()}});return b}function n(a,b,c){var e=[],f=new Set();e.push(d("SharedWorkerEventManager").registerWorkerInitPointsListener(a,function(a){if(f.has(a))return;f.add(a);c==null?void 0:c.markPoint("worker_init_point_"+a)}));e.push(d("SharedWorkerEventManager").registerHrpInitListener(a,c,function(){}));var g=function(a){d("SharedWorkerLoggingUtils").logSharedWorkerError(c,a,b)};a.addEventListener("error",g);e.push(function(){return a.removeEventListener("error",g)});return e}function o(a,b,e,f){d("SharedWorkerMigrationUtils").shouldCheckForDynamicStringWorker(b)&&(f==null?void 0:f.markEventStart("dynamic_string_worker_check"),c("promiseDone")(d("SharedWorkerMigrationUtils").maybeTerminateDynamicStringWorker(b),function(a){f==null?void 0:f.markEventEnd("dynamic_string_worker_check",{bool:{didTerminateDynamicStringWorker:a}})}));var g=m(a,b,f);d("SharedWorkerEventManager").registerConnectionAckListener(g,function(a,b,c){f==null?void 0:f.addAnnotations({string:{connectedFrom:a,workerID:b,hrpStatus:c}}),f==null?void 0:f.markPoint("worker_connection_established")});var h=new(c("Deferred"))();d("SharedWorkerEventManager").registerWorkerSelfTerminationListener(g,function(a,c,e,g){f==null?void 0:f.addAnnotations({string:{selfTerminationError:g}}),d("SharedWorkerLoggingUtils").logSelfTermination(b,a,c,e),h.reject(new k(c))},{once:!0});n(g,b,f);g.port.start();d("SharedWorkerEventManager").emitConnectionAckRequest(g,f);f==null?void 0:f.markPoint("read_dynamic_data");c("promiseDone")(H(e,!1,f).then(function(a){a=a.hrp;f==null?void 0:f.markEventStart("hrp_init");d("SharedWorkerEventManager").emitHrpInit(g,{hrp:a.hrp,js_env:a.js_env,isDev:!1},f)},function(a){a=c("getErrorSafe")(a);f==null?void 0:f.addAnnotations({string:{hrpInitErr:a.toString()}})}));d("SharedWorkerEventManager").registerExecuteWorkerAckListener(g,f,function(a){var b=a.workerSpinTime,e=a.err,g=a.isFirstInit,i=a.workerRev;a=a.workerSpinMode;d("SharedWorkerLoggingUtils").logExecuteAck(f,b,e,g,i,a);e!=null||i==null||b==null?h.reject(c("err")(e!=null?e:"no worker rev or spin time")):h.resolve({rev:i,spinTime:b})});f==null?void 0:f.markEventStart("execute_worker");d("SharedWorkerEventManager").emitExecuteWorker(g);return h.getPromise().then(function(a){var b=a.rev;a=a.spinTime;return{worker:g,rev:b,spinTime:a}})}function p(a,b,e,f,g,h){var i=new(c("Deferred"))();d("SharedWorkerEventManager").registerGetWorkerRevListener(a,h,function(a){var j=a.rev;a=a.spinTime;c("promiseDone")(d("SharedWorkerStorageManager").saveSharedWorkerReference(e,{url:b,rev:j,spin_time:a,version:g.version,rsrcBundleUrl:g.getJSModuleBundleResource().url,sandboxOnlyChecksum:g.sandboxOnlyChecksum},h).then(function(){return d("SharedWorkerStorageManager").getSharedWorkerReference(e,h)}).then(function(a){c("logSharedWorkerInitStep")(e,a,g.resourceUrlForLogging,"after_worker_reference_save")}),function(){h.markEventEnd("get_worker_rev",{bool:{successfullySavedWorkerRef:!0}}),i.resolve(),f()},function(a){h.markEventEnd("get_worker_rev",{bool:{successfullySavedWorkerRef:!1}}),i.reject(a),c("handleWorkerWriteError")(a),f()})});h.markEventStart("get_worker_rev");d("SharedWorkerEventManager").emitGetRev(a);return i.getPromise()}function q(a,b,e,f){f===void 0&&(f=null);var g=[],h=function(c,a,f){g.forEach(function(a){return a()}),b(c,a,f),d("SharedWorkerUptimeTracker").stopUptimeTracking(e)};g.push(d("SharedWorkerEventManager").registerForwardListeners(a));g.push(d("SharedWorkerEventManager").registerWorkerShutdownListener(a,function(a){var b=a.reason;a=a.workerID;d("SharedWorkerLoggingUtils").logShutdown(e,null,b,a);b=b==="dynamic_string_worker_rollback"?"requested-upgrade":b;h(b,a,"sw-shutdown")}));g.push(d("SharedWorkerEventManager").registerWorkerSelfTerminationListener(a,function(b,c,a){h(c,a,"self-terminate")}));c("qex")._("4410")&&c("supportsNativeWebLock")()&&f!=null&&g.push(L(e,f,function(){h("worker-id-lock-release",f,"self-terminate")}));c("qex")._("4411")&&self.BroadcastChannel!=null&&f!=null&&g.push(K(e,function(a){h(a,f,"broadcast")}));d("SharedWorkerDevChangeManager").trackCreatedWorker(e);d("SharedWorkerUptimeTracker").startUptimeTracking(a,e);c("logSharedWorkerInitStep")(e,null,null,"create_worker_end")}function r(a,b,c,d,e,f){return s.apply(this,arguments)}function s(){s=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f,g,h){h.addAnnotations({string:{initFlow:"use_existing_ref"}});c("logSharedWorkerInitStep")(a,null,null,"using_existing_worker_ref");var i=d("SharedWorkerMigrationUtils").isLegacyWorkerURL(b.url),j=(yield b.version===2?i?o(b.url,a,e.getJSModuleBundleResource(),h):d("SharedWorkerV2ResourceExperimental").startV2Worker(b.url,a,e.getJSModuleBundleResource(),h):l(b.url,a,e.getJSModuleBundleResource(),h));j=j.worker;f();b.version===2&&!i&&void p(j,b.url,a,f,e,h);q(j,g,a);return j});return s.apply(this,arguments)}function t(a,b,c,d){return u.apply(this,arguments)}function u(){u=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f){var g=new(c("Deferred"))(),h=d("SharedWorkerMigrationUtils").isStatusLockHeld(a);if((yield d("SharedWorkerMigrationUtils").isDynamicStringWorkerRunning(a))&&c("supportsNativeWebLock")()&&f===!1)g.resolve(),e.addAnnotations({string:{oldWorkerShutdown:"not_needed_for_dynamic_string"}});else if(self.BroadcastChannel!=null&&c("gkx")("13712")){var i=F(a);i.postMessage({type:"terminate",reason:"requested-upgrade",pageID:c("pageID")});g.resolve();e.addAnnotations({string:{oldWorkerShutdown:"broadcast"}})}else if(yield h){b.version===2&&c("supportsModuleWorker")(!0)?i=new SharedWorker(b.url,{name:a,type:"module"}):i=new SharedWorker(b.url,a);d("SharedWorkerEventManager").registerWorkerShutdownListener(i,function(a){var b=a.workerID,h=a.isInitialized;f?c("setTimeout")(function(){e.markEventEnd("old_worker_shutdown",{string:{oldWorkerID:b},bool:{oldWorkerWasInitialized:h}}),g.resolve()},d("SharedWorkerV2ResourceExperimental").DELAY_FOR_WORKER_SHUTDOWN_MS):(e.markEventEnd("old_worker_shutdown",{string:{oldWorkerID:b},bool:{oldWorkerWasInitialized:h}}),g.resolve());e.addAnnotations({string:{oldWorkerShutdown:"success"}})},{once:!0});e.markEventStart("old_worker_shutdown");i.port.start();d("SharedWorkerEventManager").emitWorkerShutdown(i,{upgrade:!0})}else g.resolve(),e.addAnnotations({string:{oldWorkerShutdown:"not_needed"}});f===!0?yield g.getPromise():g.resolve()});return u.apply(this,arguments)}function v(a,b,c,d,e,f,g){return w.apply(this,arguments)}function w(){w=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,d,e,f,g){f.addAnnotations({string:{initFlow:"upgrade_worker_ref"}});yield t(a,b,f,g);return y(a,c,d,e,f,"upgrading_worker_ref")});return w.apply(this,arguments)}function x(a,b,c,d,e){e.addAnnotations({string:{initFlow:"new_worker_ref"}});return y(a,b,c,d,e,"new_worker_ref")}function y(a,b,c,d,e,f){return z.apply(this,arguments)}function z(){z=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f,g,h){c("logSharedWorkerInitStep")(a,null,null,h);(yield d("SharedWorkerMigrationUtils").isStatusLockHeld(a))&&c("FBLogger")("worker").info("Trying to create a worker %s while old one is still running",a);var i=(yield b.createSharedWorker()),j=i[0];i=i[1].worker;h==="upgrading_worker_ref"&&(yield d("SharedWorkerStorageManager").removeSharedWorkerReference(a,g));yield p(i,j,a,e,b,g);q(i,f,a);return i});return z.apply(this,arguments)}function A(a,b){var e=d("SharedWorkerDevChangeManager").shouldUseStorageWorkerForDev(a.name,{storageWorkerResource:b,tabResource:a}),f=b.spin_time,g=b.version;b=b.rev;return b>=c("SiteData").client_revision&&(a.version===g||f>=c("SiteData")[c("StaticSiteData").spin_time_key])&&e}function B(a,b,c,d,e){return C.apply(this,arguments)}function C(){C=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f,g){f.markEventStart("create_worker");var h=a.name;d("SharedWorkerUptimeTracker").stopUptimeTracking(h);f.addAnnotations({bool:{workerAlreadyRunning:c("supportsNativeWebLock")()?yield d("SharedWorkerMigrationUtils").isStatusLockHeld(h):null}});var i=c("supportsNativeWebLock")()?yield d("SharedWorkerMigrationUtils").isStatusLockHeld(h):!0;if(!g&&a.version===2&&c("gkx")("13712"))return N(a,b,e,f);var j=(yield d("SharedWorkerStorageManager").getSharedWorkerReference(h,f));c("logSharedWorkerInitStep")(h,j,a.resourceUrlForLogging,"create_worker_start");var k=j!=null&&d("SharedWorkerMigrationUtils").isLegacyWorkerURL(j==null?void 0:j.url);i=g&&i===!1&&k;if(j==null||i)return x(h,a,b,e,f);k=j.spin_time;i=j.version;var l=j.rev;f.addAnnotations({"int":{storageWorkerSpinTime:k,storageWorkerVersion:i,storageWorkerRev:l}});if(A(a,j))return r(h,j,a,b,e,f);else return v(h,j,a,b,e,f,g)});return C.apply(this,arguments)}function a(a,e,f){f=f!=null?f:{};var g=f.onQPLEvent;f=f.reason;var h=a.name,i=c("buildSharedWorkerInitQPLLogger")(g);i.start({version:1,workerName:h,callReason:f});var j=new(c("Deferred"))();d("SharedWorkerLockManager").withWorkerLock(function(d){var f;c("promiseDone")(B({name:h,version:1,resourceUrlForLogging:(f=a.url)!=null?f:"",getJSModuleBundleResource:function(){return a},createSharedWorker:function(){var d=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var b=c("createSharedWorkerBundleUrl")();return[b,yield l(b,h,a,i)]});function e(){return d.apply(this,arguments)}return e}()},d,e,i,!1),function(a){i.markEventEnd("create_worker"),i.endSuccess(),j.resolve(a)},function(a){a=typeof a==="string"?c("err")(a):a;i.endFailure("worker_init_failure",{string:{workerInitFailureReason:a.message,errorName:a.name}});j.reject(a);d()})},{workerQPLLogger:i,onLockTimeout:function(){return j.reject(c("err")("Worker lock timeout"))}});return j.getPromise()}function D(a,e,f,g){g===void 0&&(g=0);var i=a.name,j=f!=null?f:{},l=j.onQPLEvent;j=j.reason;var m=c("buildSharedWorkerInitQPLLogger")(l);m.start({version:2,workerName:i,callReason:j,experimental_init:f==null?void 0:f.useDynamicStringInit});m.addAnnotations({bool:{usingModuleWorker:c("supportsModuleWorker")(!0)}});var n=new(c("Deferred"))();l=(f==null?void 0:f.useDynamicStringInit)!==!0&&c("qex")._("4050")===!0;j=function(g){c("promiseDone")(B({name:i,version:2,getJSModuleBundleResource:function(){return a},sandboxOnlyChecksum:a.sandboxOnlyChecksum,createSharedWorker:(f==null?void 0:f.useDynamicStringInit)===!0?b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var b=c("createSharedWorkerV2BundleUrlExperimental")(i);return[b,yield d("SharedWorkerV2ResourceExperimental").startV2Worker(b,i,a,m)]}):b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var b=d("createSharedWorkerV2BundleUrl").createSharedWorkerV2BundleUrl();return[b,yield o(b,i,a,m)]})},g,e,m,(f==null?void 0:f.useDynamicStringInit)===!0),function(a){m.markEventEnd("create_worker"),m.endSuccess(),n.resolve(a)},function(a){a=typeof a==="string"?c("err")(a):a;n.reject(a);g()})};l?j(function(){}):d("SharedWorkerLockManager").withWorkerLock(j,{workerQPLLogger:m,onLockTimeout:function(){return n.reject(c("err")("Worker lock timeout"))},onLockFail:function(a){n.reject(a)}});return n.getPromise()["catch"](function(d){if(d instanceof k&&g<2){m.endCancel({string:{workerInitCancelReason:d.message},"int":{workerInitRetry:g}});return new(h||(h=b("Promise")))(function(b,d){c("setTimeout")(function(){c("promiseDone")(D(a,e,f,g+1),b,d)},g===0?200:500)})}m.endFailure("worker_init_failure",{string:{workerInitFailureReason:d.message,errorName:d.name}});throw d})}function e(a,b,c){return E.apply(this,arguments)}function E(){E=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f){var g=e!=null?e:a.name,j=new(c("Deferred"))(),k=c("buildSharedWorkerTerminateQPLLogger")();k.start({workerName:g});c("promiseDone")(c("supportsNativeWebLock")()?d("SharedWorkerMigrationUtils").isStatusLockHeld(g):(h||(h=b("Promise"))).resolve(void 0),function(a){return k.addAnnotations({bool:{workerAlreadyRunning:a}})});k.addAnnotations({string:{reason:f,terminatingPageId:c("pageID")}});if(self.BroadcastChannel!=null&&c("gkx")("13712")){e=F(g);e.postMessage({type:"terminate",reason:f,pageID:c("pageID")});c("promiseDone")(d("SharedWorkerStorageManager").removeSharedWorkerReference(g,k).then(function(){k.endSuccess(),j.resolve()}));return j.getPromise()}d("SharedWorkerLockManager").withWorkerLock(function(e){c("promiseDone")(d("SharedWorkerStorageManager").getSharedWorkerReference(g,k).then(function(e){k.markPoint("read_worker_reference",{"int":{storageWorkerVersion:e==null?void 0:e.version},bool:{storageReferenceExists:e!=null}});if(e!=null){var f=d("SharedWorkerMigrationUtils").isLegacyWorkerURL(e.url);k.addAnnotations({bool:{isLegacy:f}});var i=e.version===2?f?o(e.url,g,a,void 0).then(function(a){a=a.worker;return a}):d("SharedWorkerV2ResourceExperimental").startV2Worker(e.url,g,a,void 0).then(function(a){a=a.worker;return a}):l(e.url,g,a).then(function(a){a=a.worker;return a});f=c("justknobx")._("3569")?(h||(h=b("Promise"))).resolve(null):d("SharedWorkerStorageManager").removeSharedWorkerReference(g,k);return f.then(function(){return i})}k.markPoint("storage_resource_empty");return null}),function(a){try{e();d("SharedWorkerUptimeTracker").stopUptimeTracking(g);if(a==null){k.endSuccess();j.resolve();return}d("SharedWorkerEventManager").registerWorkerShutdownListener(a,function(a){var e=a.workerID;a=a.isInitialized;k.markPoint("worker_shut_down",{string:{workerID:e},bool:{wasInitialized:a}});e=c("justknobx")._("3569")?d("SharedWorkerStorageManager").removeSharedWorkerReference(g,k):(h||(h=b("Promise"))).resolve(null);c("promiseDone")(e,function(){return k.endSuccess()},function(a){k.endFailure("failed_to_remove_worker_reference",{string:{errorDescription:a.toString()}})});j.resolve()},{once:!0});d("SharedWorkerEventManager").emitWorkerShutdown(a,{reason:f})}catch(a){k.endFailure("terminate_worker_failure",{string:{workerTerminateFailReason:a.message}}),j.reject(a.message)}},function(a){c("FBLogger")("worker").catching(c("getErrorSafe")(a)).mustfix("Failed to terminate worker"),k.endFailure("connect_to_worker_failure",{string:{workerTerminateFailReason:a.message}}),j.reject(a.message),e()})},{workerQPLLogger:k});c("setTimeout")(function(){return j.reject()},i);return j.getPromise()});return E.apply(this,arguments)}function F(a){return new self.BroadcastChannel(j+"_"+a)}function f(a){return G.apply(this,arguments)}function G(){G=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){a=(yield d("SharedWorkerStorageManager").getSharedWorkerReference(a.name));return a!=null});return G.apply(this,arguments)}function H(a,b,c){return I.apply(this,arguments)}function I(){I=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){b=(yield d("WebWorkerV2DynamicData").readDynamicDataForWorker(a,b));var f=b.data;b=b.time;try{var g=c("justknobx")._("1979");g&&d("BootloaderPreloader").preloadWorkerJSFromHRP(f.hrp);e==null?void 0:e.addAnnotations({bool:{prefetchWorkerScripts:g}})}catch(b){g=c("getErrorSafe")(b);e==null?void 0:e.addAnnotations({string:{hrpInitErr:g.toString()}});c("FBLogger")("worker").catching(b).warn("Failed to preload worker %s JS from HRP",a.name)}return{hrp:f,time:b,rev:(e=f.hrp.hsrp)==null?void 0:(g=e.hblp)==null?void 0:(a=g.consistency)==null?void 0:a.rev}});return I.apply(this,arguments)}function J(b,a,c){return d("SharedWorkerStorageManager").getOrUpdateWorkerReference(b.name,function(d){if(d==null)return a;if(A(b,d))return d;void t(b.name,d,c,!1);return a})}function K(a,b){if(self.BroadcastChannel!=null){var d=F(a),e=function(a){a=a.data;if(typeof a==="object"&&(a==null?void 0:a.type)==="terminate"){var d=typeof a.reason==="string"?a.reason:"unknown";a=typeof a.pageID==="string"?a.pageID:null;if(a!=null&&a===c("pageID")&&d==="requested-upgrade")return;b(d)}};d.addEventListener("message",e);return function(){return d.removeEventListener("message",e)}}return function(){}}function L(a,b,e){var f=new AbortController();void (c("BrowserLockManager")==null?void 0:c("BrowserLockManager").request(d("SharedWorkerStatusLock").getWorkerIdStatusLockName(a,b),{signal:f.signal},function(a){a!=null&&e()}));return function(){f.abort()}}function M(a,b,e,f){var g=new(c("Deferred"))();void d("SharedWorkerStorageManager").getOrUpdateWorkerReference(a,function(a){if(a==null||a.url!==b){g.resolve("url-was-changed");return a}return a.rev!==e||a.spin_time!==f?babelHelpers["extends"]({},a,{rev:e,spin_time:f}):a}).then(function(){g.resolve("success")});return g.getPromise()}function N(a,b,c,d){return O.apply(this,arguments)}function O(){O=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f,g){if(a.version!==2)throw c("err")("Worker version must be 2");var i=a.name,j=(yield H(a.getJSModuleBundleResource(),!1,g)),l=j.rev;if(l==null)throw c("err")("HRP rev or spin time is null");l={url:d("createSharedWorkerV2BundleUrl").createSharedWorkerV2BundleUrl(),rev:l,spin_time:j.time,version:a.version,rsrcBundleUrl:a.getJSModuleBundleResource().url,sandboxOnlyChecksum:a.sandboxOnlyChecksum};var o=new(c("Deferred"))(),p=new(c("Deferred"))(),r=[];try{var s,t=function(a){p.reject(a),o.reject(a)};r.push(K(i,function(a){t(new k("broadcast-"+a))}));var u=(yield J(a,l,g));g.addAnnotations({string:{initFlow:"connect_to_worker"}});var v=m(u.url,i,g);r.push((s=d("SharedWorkerEventManager")).registerConnectionAckListener(v,function(a,b,c){g==null?void 0:g.addAnnotations({string:{connectedFrom:a,workerID:b}}),g==null?void 0:g.markPoint("worker_connection_established"),o.resolve({hrpStatus:c!=null?c:"no-hrp",workerID:b})}));r.push(s.registerWorkerSelfTerminationListener(v,function(a,b,c,e){g==null?void 0:g.addAnnotations({string:{selfTerminationError:e}}),d("SharedWorkerLoggingUtils").logSelfTermination(i,a,b,c),t(new k(b))},{once:!0}));r.push(s.registerWorkerShutdownListener(v,function(a){var b=a.reason;a=a.workerID;d("SharedWorkerLoggingUtils").logShutdown(i,null,b,a);t(new k(b!=null?b:"sw-shutdown-unknown"))}));r.push.apply(r,n(v,i,g));v.port.start();s.emitConnectionAckRequest(v,g);s=(yield o.getPromise());var w=s.hrpStatus;s=s.workerID;c("qex")._("4410")&&c("supportsNativeWebLock")()&&s!=null&&r.push(L(i,s,function(){t(new k("worker-id-lock-release"))}));if(w==="no-hrp"){g==null?void 0:g.markPoint("read_dynamic_data");w=l.rev>=u.rev;l=w?(h||(h=b("Promise"))).resolve(j):H(a.getJSModuleBundleResource(),!0,g);c("promiseDone")(l.then(function(a){g==null?void 0:g.markEventStart("hrp_init"),d("SharedWorkerEventManager").emitHrpInit(v,{hrp:a.hrp.hrp,js_env:a.hrp.js_env,isDev:!1},g)},function(a){a=c("getErrorSafe")(a);g==null?void 0:g.addAnnotations({string:{hrpInitErr:a.toString()}});t(a)}))}r.push(d("SharedWorkerEventManager").registerExecuteWorkerAckListener(v,g,function(a){var b=a.workerSpinTime,e=a.err,f=a.isFirstInit,h=a.workerRev;a=a.workerSpinMode;d("SharedWorkerLoggingUtils").logExecuteAck(g,b,e,f,h,a);e!=null||b==null||h==null?p.reject(c("err")(e!=null?e:"no worker rev or spin time")):p.resolve({workerRev:h,workerSpinTime:b})}));g==null?void 0:g.markEventStart("execute_worker");d("SharedWorkerEventManager").emitExecuteWorker(v);w=(yield p.getPromise());j=w.workerRev;a=w.workerSpinTime;l=(yield M(i,u.url,j,a));if(l==="url-was-changed"){d("SharedWorkerEventManager").emitWorkerShutdown(v,{reason:"url-was-changed"});throw new k("url-was-changed")}e();q(v,f,i,s);return v}finally{r.forEach(function(a){return a()})}});return O.apply(this,arguments)}g.SHARED_WORKER_BROADCAST_CHANNEL=j;g.SelfTerminationError=k;g.createPushSafeSharedWebWorkerAsync=a;g.createPushSafeSharedWebWorkerV2Async=D;g.terminateSharedWorker=e;g.doesSharedWorkerReferenceExist=f}),98);
__d("SharedWorkerV2ResourceExperimental",["Deferred","Promise","SharedWorkerBundleResource","SharedWorkerDevChangeManager","SharedWorkerEventManager","SharedWorkerLoggingUtils","SharedWorkerMigrationUtils","SharedWorkerUptimeTracker","SiteData","StaticSiteData","TrustedTypesWebWorkerScriptURLPolicy","asyncToGeneratorRuntime","buildSharedWorkerInitQPLLogger","buildSharedWorkerTerminateQPLLogger","createSharedWorkerV2BundleUrlExperimental","justknobx","pageID","promiseDone","setTimeout","supportsModuleWorker","supportsNativeWebLock"],(function(a,b,c,d,e,f,g){"use strict";var h,i=50,j=5e3,k=2e3;function l(a,b,e,f){return d("SharedWorkerDevChangeManager").shouldUpgradeWorkerForDevExperimental(a)&&!f?!0:b<c("SiteData").client_revision&&e<c("SiteData")[c("StaticSiteData").spin_time_key]}function a(a,b,c){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f){var g=a.name,j=c("buildSharedWorkerInitQPLLogger")(f==null?void 0:f.onQPLEvent);j.start({version:2,workerName:g,experimental_init:!0,callReason:f==null?void 0:f.reason,migrationEnabled:!0});j.markEventStart("create_worker");c("supportsNativeWebLock")()&&c("promiseDone")(d("SharedWorkerMigrationUtils").isStatusLockHeld(g).then(function(a){j.addAnnotations({bool:{workerAlreadyRunning:a}})}));(f==null?void 0:f.migratedWorker)===!0&&(yield d("SharedWorkerMigrationUtils").checkStorageAndTerminateLegacyWorker({name:g,logger:j}));f=(yield n(a,j));var k=f.worker,l=f.needsUpgrade;return new(h||(h=b("Promise")))(function(b,f){var h=function(a){return b(p(a,g,j,e))};l?(d("SharedWorkerEventManager").registerWorkerShutdownListener(k,function(b){var d=b.workerID;b=b.isInitialized;j.markEventEnd("old_worker_shutdown",{string:{oldWorkerID:d},bool:{oldWorkerWasInitialized:b}});c("setTimeout")(function(){return n(a,j).then(function(a){a=a.worker;return h(a)})},i)},{once:!0}),j.markEventStart("old_worker_shutdown"),d("SharedWorkerEventManager").emitWorkerShutdown(k,{upgrade:!0})):h(k)})});return m.apply(this,arguments)}function n(a,b){var e=a.name,f=c("createSharedWorkerV2BundleUrlExperimental")(e);f=c("TrustedTypesWebWorkerScriptURLPolicy").createScriptURL(f);var g=c("supportsModuleWorker")(!0)?"module":"classic",h=new SharedWorker(f,{name:e,type:g}),i=new Set();d("SharedWorkerEventManager").registerWorkerInitPointsListener(h,function(a){if(i.has(a))return;i.add(a);b==null?void 0:b.markPoint("worker_init_point_"+a)});var j=new(c("Deferred"))();d("SharedWorkerEventManager").registerGetWorkerRevListener(h,b,function(c){var d=c.rev,f=c.spinTime;c=c.workerChecksum;c=l(e,d,f,a.sandboxOnlyChecksum===c);j.resolve(c);c?b.addAnnotations({"int":{oldWorkerRev:d,oldWorkerSpinTime:f}}):b.addAnnotations({"int":{workerRev:d,workerSpinTime:f}})});o(h,e,b);h.port.start();d("SharedWorkerEventManager").emitConnectionAckRequest(h,b);return j.getPromise().then(function(a){return{worker:h,needsUpgrade:a}})}function o(a,b,c,e){a.addEventListener("error",function(a){d("SharedWorkerLoggingUtils").logSharedWorkerError(c,a,b)});d("SharedWorkerEventManager").registerForwardListeners(a);d("SharedWorkerEventManager").registerWorkerSelfTerminationListener(a,function(a,b,d,e){c.endFailure("worker_init_failure",{string:{selfTerminationError:e,selfTerminationReason:b}})},{once:!0});d("SharedWorkerEventManager").registerConnectionAckListener(a,function(a,b){c==null?void 0:c.addAnnotations({string:{connectedFrom:a,workerID:b}}),c==null?void 0:c.markPoint("worker_connection_established")},{once:!0});return a}function p(a,b,e,f){d("SharedWorkerEventManager").registerWorkerShutdownListener(a,function(a){var e=a.reason,g=a.workerID;d("SharedWorkerLoggingUtils").logShutdown(b,null,e,g);e==="requested-upgrade"?c("setTimeout")(function(){return f==null?void 0:f(e,g)},1e3):e==="dynamic_string_worker_rollback"?f==null?void 0:f("maw_request_refresh",g):f==null?void 0:f(e,g);d("SharedWorkerUptimeTracker").stopUptimeTracking(b)});e.markEventEnd("create_worker");e.endSuccess();d("SharedWorkerUptimeTracker").startUptimeTracking(a,b);d("SharedWorkerDevChangeManager").trackCreatedWorker(b);return a}function q(a,e,f,g,i){i===void 0&&(i=0);var j=f.name,l=c("TrustedTypesWebWorkerScriptURLPolicy").createScriptURL(a),m=c("supportsModuleWorker")(!0)?"module":"classic",n=new SharedWorker(l,{name:j,type:m});g==null?void 0:g.markPoint("shared_worker_construct_done");g==null?void 0:g.addAnnotations({string:{workerUrl:l.toString()}});var o=new(c("Deferred"))(),p=[];p.push(d("SharedWorkerEventManager").registerConnectionAckListener(n,function(a,b){g==null?void 0:g.addAnnotations({string:{connectedFrom:a,workerID:b}}),g==null?void 0:g.markPoint("worker_connection_established"),o.resolve()},{once:!0}));p.push(d("SharedWorkerEventManager").registerWorkerInitPointsListener(n,function(a){g==null?void 0:g.markPoint("worker_init_point_"+a)}));p.push(d("SharedWorkerEventManager").registerForwardListeners(n));d("SharedWorkerEventManager").emitConnectionAckRequest(n,g);n.addEventListener("error",function(a){d("SharedWorkerLoggingUtils").logSharedWorkerError(g,a,e)});var r=new(c("Deferred"))();p.push(d("SharedWorkerEventManager").registerGetWorkerRevListener(n,g,function(a){var b=a.rev;a=a.spinTime;g==null?void 0:g.addAnnotations({"int":{initialWorkerRev:b,initialSpinTime:a}});r.resolve({worker:n,rev:b,spinTime:a})}));p.push(d("SharedWorkerEventManager").registerWorkerSelfTerminationListener(n,function(a,b,c,f){g==null?void 0:g.addAnnotations({string:{selfTerminationError:f}}),d("SharedWorkerLoggingUtils").logSelfTermination(e,a,b,c),r.reject(new(d("SharedWorkerBundleResource").SelfTerminationError)(b))},{once:!0}));p.push(d("SharedWorkerEventManager").registerWorkerShutdownListener(n,function(a){var b=a.reason;a=a.workerID;g==null?void 0:g.addAnnotations({string:{shutdownReason:b}});d("SharedWorkerLoggingUtils").logShutdown(e,null,b,a);r.reject(new(d("SharedWorkerBundleResource").SelfTerminationError)(b!=null?b:"unknownShutdown"))},{once:!0}));n.port.start();i===0&&c("justknobx")._("1707")&&void (h||(h=b("Promise"))).race([o.getPromise().then(function(){return"success"}),new h(function(a){c("setTimeout")(function(){a("timeout")},k)})]).then(function(b){if(b==="timeout"){b=n;var c=q(a,e,f,g,i+1);void c.then(function(a){return r.resolve(a)});p.forEach(function(a){return a()});b.port.close()}});return r.getPromise()}function e(a,b){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e=a.name,f=new(c("Deferred"))();a=(yield d("SharedWorkerMigrationUtils").isStatusLockHeld(e));var g=c("buildSharedWorkerTerminateQPLLogger")();if(!a){g.start({workerName:e});g.markPoint("no_worker_running");g.endSuccess();return}g.start({workerName:e});g.addAnnotations({string:{reason:b,terminatingPageId:c("pageID")}});a=c("createSharedWorkerV2BundleUrlExperimental")(e);a=c("TrustedTypesWebWorkerScriptURLPolicy").createScriptURL(a);var h=c("supportsModuleWorker")(!0)?"module":"classic";a=new SharedWorker(a,{workerName:e,type:h});g.markPoint("worker_created");d("SharedWorkerEventManager").registerWorkerShutdownListener(a,function(a){a=a.workerID;g.markPoint("worker_shut_down",{string:{workerID:a}});g.endSuccess();d("SharedWorkerUptimeTracker").stopUptimeTracking(e);f.resolve()},{once:!0});a.port.start();d("SharedWorkerEventManager").emitWorkerShutdown(a,{reason:b});c("setTimeout")(function(){return f.reject()},j);return f.getPromise()});return r.apply(this,arguments)}g.DELAY_FOR_WORKER_SHUTDOWN_MS=i;g.createPushSafeSharedWebWorkerV2Async=a;g.startV2Worker=q;g.terminateSharedWorker=e}),98);
__d("WebWorkerV2Resource",["TrustedTypesWebWorkerScriptURLPolicy","WebWorkerV2DynamicData","XCometFBMultiSiteWebWorkerV2InitScriptControllerRouteBuilder","forEachObject","getAsyncParamsFromCurrentPageURI","getWorkerInitScriptSPINParams","nullthrows","promiseDone","supportsModuleWorker"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,e){b=b!=null?b:a.name;var f=c("supportsModuleWorker")(!1)&&e!==!0?"module":"classic",g=c("getWorkerInitScriptSPINParams")();c("forEachObject")(c("getAsyncParamsFromCurrentPageURI")(),function(a,b){g.set(b,a)});e=c("nullthrows")(c("XCometFBMultiSiteWebWorkerV2InitScriptControllerRouteBuilder").buildUri({worker_type:c("supportsModuleWorker")(!1)&&e!==!0?"MODULE":"CLASSIC"}).addQueryParams(g)).toString();e=c("TrustedTypesWebWorkerScriptURLPolicy").createScriptURL(e);var h=new Worker(e,{name:b,type:f});c("promiseDone")(d("WebWorkerV2DynamicData").readDynamicDataForWorker(a).then(function(a){a=a.data;h.postMessage({type:"ww-hrp-init",hrp:a.hrp,js_env:a.js_env,is_dev:!1,tiered:!0})}));return{worker:h,url:e.toString()}}g.createDedicatedV2WebWorker=a}),98);
__d("MAWWebWorkerSingleton",["Deferred","MAWInit","MAWMIC","MAWStartupLoggingUtils","MAWWaitForBackendSetup","MAWWorkerWatchdogRecovery","Promise","SharedWorkerBundleResource","SharedWorkerMigrationUtils","SharedWorkerV2ResourceExperimental","WAResolvable","WebWorkerV2Resource","clearTimeout","cr:6600","err","gkx","promiseDone","setTimeout","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";var h,i=null,j=function(a){i=a},k=function(){return i},l=f,m=new(d("WAResolvable").Resolvable)(),n=null;function a(){return d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker()?d("SharedWorkerMigrationUtils").isStatusLockHeld(b("cr:6600").name):(h||(h=b("Promise"))).resolve(n!==null)}function e(){var a={isSharedWorker:d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker(),nativeLocksSupported:d("SharedWorkerMigrationUtils").supportsNativeWebLocks(),workerCreationPromiseExists:n!==null};if(a.isSharedWorker!==!0)return(h||(h=b("Promise"))).resolve(a.workerCreationPromiseExists===!0?{data:a,tag:"dedicated_exists"}:{data:a,tag:"dedicated_not_exists"});if(a.nativeLocksSupported!==!0)return(h||(h=b("Promise"))).resolve({data:a,tag:"shared_unknown_locks_not_supported"});var c=d("MAWWaitForBackendSetup").getCurrentWorkerID();return(h||(h=b("Promise"))).all([d("SharedWorkerMigrationUtils").isStatusLockHeld(b("cr:6600").name),c!=null?d("SharedWorkerMigrationUtils").isWorkerIdStatusLockHeld(b("cr:6600").name,c):(h||(h=b("Promise"))).resolve(!1)]).then(function(b){var c=b[0];b=b[1];a.sharedLockHeld=c;a.workerIdLockHeld=b;if(c&&b)return{data:a,tag:"shared_exists_and_connected"};if(c)return{data:a,tag:"shared_exists_not_connected"};return b?{data:a,tag:"shared_only_unique_lock_held"}:{data:a,tag:"shared_not_exists"}})}function f(){n=null}function o(a){if(n){d("MAWMIC").addPoint(a+"_wait_on_worker_creation");d("MAWInit").MAWInit.logPoint(a+"_wait_on_worker_creation");return n}d("MAWInit").MAWInit.logPoint(a+"_create_worker_start");d("MAWMIC").addPoint(a+"_create_worker_start");n=(d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker()?p(a):q().then(function(a){return{worker:a,workerID:"dedicated"}})).then(function(b){var c=b.worker;b=b.workerID;d("MAWMIC").addPoint(a+"_create_worker_end");d("MAWInit").MAWInit.logPoint(a+"_create_worker_end");return{worker:c,workerID:b}},function(a){n=null;throw a});return n}function p(a){var e=function(a,b,d){c("promiseDone")(m.promise.then(function(){return l(a,b,d)}))};d("MAWWorkerWatchdogRecovery").setRecoveryForWatchdog(e);var f=new(c("Deferred"))(),g=c("gkx")("7313");d("MAWMIC").addBoolAnnotation("migrationEnabled",g);g=g?d("SharedWorkerV2ResourceExperimental").createPushSafeSharedWebWorkerV2Async(b("cr:6600"),e,{migratedWorker:!0,onQPLEvent:d("MAWStartupLoggingUtils").logSharedWorkerQPLEvents,reason:a}):d("SharedWorkerBundleResource").createPushSafeSharedWebWorkerV2Async(b("cr:6600"),e,{onQPLEvent:d("MAWStartupLoggingUtils").logSharedWorkerQPLEvents,reason:a,useDynamicStringInit:c("gkx")("8737")});return g.then(function(a){j(a);d("MAWMIC").addPoint("shared_worker_bundle_created");var b=null;c("gkx")("12910")&&(b=c("setTimeout")(function(){d("MAWMIC").addPoint("shared_worker_connection_ack_timeout"),f.reject(c("err")("Worker connection ack timeout"))},1e4));var e=function(a){var e;if(typeof a.data==="object"&&((e=a.data)==null?void 0:e.type)==="connection-ack"&&typeof ((e=a.data)==null?void 0:e.response)==="object"&&typeof ((e=a.data)==null?void 0:(e=e.response)==null?void 0:e.from)==="string"){e=typeof ((e=a.data)==null?void 0:(e=e.response)==null?void 0:e.workerID)==="string"?(e=a.data)==null?void 0:(a=e.response)==null?void 0:a.workerID:void 0;e!=null?(d("MAWMIC").addPoint("shared_worker_workerID_received"),d("MAWMIC").addStringAnnotation("workerID",e),b!=null&&c("clearTimeout")(b),f.resolve(e)):(d("MAWMIC").addPoint("shared_worker_null_workerID_received"),d("MAWMIC").addStringAnnotation("workerID","unknown"))}};a.port.addEventListener("message",e);a.port.postMessage({type:"ww-connection-ack"});a.addEventListener("error",function(a){d("MAWStartupLoggingUtils").logWorkerError("Shared Worker",a,"")});return f.getPromise().then(function(b){return{worker:a.port,workerID:b}})})}function q(){return(h||(h=b("Promise"))).resolve(d("WebWorkerV2Resource").createDedicatedV2WebWorker(b("cr:6600"))).then(function(a){var b=a.url;a=a.worker;a.onerror=function(a){return d("MAWStartupLoggingUtils").logWorkerError("Dedicated Worker",a,b)};d("MAWStartupLoggingUtils").setImportScriptsErrorListener(a);return a})}function r(a){l=a,m.resolve()}g.getSharedWorkerInstance__TEST_ONLY=k;g.doesWorkerExist=a;g.getWorkerHealthStatus=e;g.resetWorkerCreation=f;g.createWorkerIfNone=o;g.setOnCloseForWorkerInstance=r}),98);
__d("MAWWaitForBackendSetup",["ErrorSerializer","MAWInit","MAWInitError","MAWMIC","MAWSetupWorkerAuxStateForLogging","MAWWebWorkerSingleton","Promise","err","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=new(h||(h=b("Promise")))(function(a,b){i=a,j=b}),l=!1,m=!1,n=!1,o=!1,p=null,q=i,r=j;function s(a){return new(h||(h=b("Promise")))(function(b,e){k.then(b)["catch"](function(b){var f=a+" rejected because backend setup failed: "+b.message+".";b=b instanceof d("MAWInitError").MAWInitError?b.error:null;b!=null&&(f=f.concat(" More error detail: "+c("ErrorSerializer").toReadableMessage(b)));e(c("err")(f))})})}function a(a,b){c("promiseDone")(s(b).then(function(){a()}))}function t(){if(!o)return;k=new(h||(h=b("Promise")))(function(a,b){i=a,j=b});o=!1;l=!1;q=i;r=j;m=!0;n=!1}function e(a){l=!0,o=!0,n=!1,q==null?void 0:q(a)}function f(a){l=!1,o=!0,n=!1,r==null?void 0:r(a)}function u(){return l}function v(){return o}function w(){return m}function x(a){a!=null&&p!==a&&d("MAWSetupWorkerAuxStateForLogging").resetWorkerCreationTime(),p=a}function y(a){if(p==null||p!==a)return;z()}function z(){p=null,d("MAWWebWorkerSingleton").resetWorkerCreation(),t(),d("MAWInit").MAWInit.logPoint("reset_current_worker"),d("MAWMIC").addPoint("reset_current_worker")}function A(){return p}function B(){n=!0}function C(){return n}g.waitForBackendSetup=s;g.runAfterBackendSetup=a;g.resetBackendSetup=t;g.resolveBackendSetup=e;g.rejectBackendSetup=f;g.isBackendSetupSuccessful=u;g.isBackendSetupSettled=v;g.wasBackendSetupReset=w;g.setCurrentWorker=x;g.resetCurrentWorkerWithId=y;g.resetCurrentWorkerSkipIdCheck=z;g.getCurrentWorkerID=A;g.markBackendSetupStarted=B;g.isBackendSetupInProgress=C}),98);
__d("MWLogSendToSentFailedError",["FBLogger","QuickPerformanceLogger"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b,d,e,f){var g=a.qplEventType;a=a.qplInstanceKey;g=(h||(h=c("QuickPerformanceLogger"))).getMarker(g,a);a=g==null?void 0:(a=g.annotations)==null?void 0:(a=a.bool)==null?void 0:a.is_secure;c("FBLogger")("messenger_web_messaging","send_to_sent_failed").blameToPreviousFrame().blameToPreviousFrame().warn("send_to_sent failed with reason: %s. Caught error message: %s. Additional description: %s. Secure? %s. Error Code: %s. Application Error: %s.",b,(b=d==null?void 0:d.message)!=null?b:"",(g=(d=g==null?void 0:(d=g.annotations)==null?void 0:(b=d.string)==null?void 0:b.error_description)!=null?d:g==null?void 0:(b=g.annotations)==null?void 0:(d=b.string)==null?void 0:d.errorType)!=null?g:"",a===!0?"secure":a===!1?"open":"unknown",(b=e==null?void 0:e.toString())!=null?b:"",(d=f==null?void 0:f.toString())!=null?d:"")}g["default"]=a}),98);
__d("MWMsgMediaTypeLogUtils",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";var g=b("$InternalEnum")({Application:"application",Audio:"audio",Avatar:"avatar",HotEmoji:"hot_emoji",File:"file",Gif:"gif",Image:"image",Link:"link",None:"none",Poll:"poll",Reaction:"reaction",Sticker:"sticker",Video:"video",Share:"share",NoteReply:"note_reply"});function a(a){var b=a.hasFile;b=b===void 0?!1:b;var c=a.hasGif;c=c===void 0?!1:c;var d=a.hasHotEmoji;d=d===void 0?!1:d;var e=a.hasImage;e=e===void 0?!1:e;var f=a.hasLinks;f=f===void 0?!1:f;var h=a.hasNoteReply;h=h===void 0?!1:h;var i=a.hasShare;i=i===void 0?!1:i;var j=a.hasSticker;j=j===void 0?!1:j;var k=a.hasVideo;k=k===void 0?!1:k;a=a.hasVoiceClip;a=a===void 0?!1:a;var l=g.None;e?l=g.Image:k?l=g.Video:d?l=g.HotEmoji:j?l=g.Sticker:c?l=g.Gif:a?l=g.Audio:b?l=g.File:i?l=g.Share:f?l=g.Link:h&&(l=g.NoteReply);return l}f.AttachmentType=g;f.getAttachmentType=a}),66);
__d("MWGetOriginalEntrypoint",["I64","InteractionTracingMetrics","MWChatInteraction"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var b,e=d("MWChatInteraction").get((h||(h=d("I64"))).to_string(a));e=e!=null?c("InteractionTracingMetrics").get(e):null;e=(b=e==null?void 0:(b=e.annotations.string)==null?void 0:b.inboxEntrypoint)!=null?b:e==null?void 0:(b=e.annotations.string)==null?void 0:b.entrypoint;if(e!=null&&e!=="unknown")return e;b=d("MWChatInteraction").get("inbox_init");if(b!=null&&(h||(h=d("I64"))).equal(a,(h||(h=d("I64"))).of_string(b)))return"initInbox";e=d("MWChatInteraction").get(d("MWChatInteraction").MW_AUTO_CHAT_TAB_OPEN);b=e!=null?(b=c("InteractionTracingMetrics").get(e))==null?void 0:(e=b.annotations.string)==null?void 0:e.thread_id:null;return b===(h||(h=d("I64"))).to_string(a)?"auto_open":"unknown"}g["default"]=a}),98);
__d("MWMsgActionLogUtils",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";var g=b("$InternalEnum")({Send:"send",Reaction:"reaction",Reply:"reply",Forward:"forward",Share:"share",StoryReply:"story-reply",Unsend:"unsend",GroupInvite:"group-invite",SenderKeyDistribution:"sender-key-distribution",EphemeralSetting:"ephemeral-setting",Edit:"edit",Bump:"bump",Retry:"retry"});function a(a){var b=a.hasEphemeralSetting;b=b===void 0?!1:b;var c=a.hasGroupInvite;c=c===void 0?!1:c;var d=a.hasReaction;d=d===void 0?!1:d;var e=a.hasReply;e=e===void 0?!1:e;var f=a.hasSenderKeyDistribution;f=f===void 0?!1:f;var h=a.hasStoryReply;h=h===void 0?!1:h;var i=a.isBump;i=i===void 0?!1:i;var j=a.isEdit;j=j===void 0?!1:j;var k=a.isForward;k=k===void 0?!1:k;var l=a.isRetry;l=l===void 0?!1:l;a=a.isUnsend;a=a===void 0?!1:a;var m=g.Send;e?m=g.Reply:h?m=g.StoryReply:a?m=g.Unsend:d?m=g.Reaction:c?m=g.GroupInvite:b?m=g.EphemeralSetting:f?m=g.SenderKeyDistribution:j?m=g.Edit:k?m=g.Forward:i?m=g.Bump:l&&(m=g.Retry);return m}f.ActionType=g;f.getActionType=a}),66);
__d("MWHasLinksUtil",["URLMatcher","isStringNullOrEmpty"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return c("isStringNullOrEmpty")(a)?!1:c("URLMatcher").match(a)!=null}g.getHasLinks=a}),98);
__d("MWVersion",["I64"],(function(a,b,c,d,e,f,g){"use strict";var h;a=(h||d("I64")).of_string("2");g.v2=a}),98);
__d("MWSharedS2SBaseAnnotations",["$InternalEnum","I64","LSMessagingThreadAttributionType","LSMessagingThreadTypeUtil","MWHasLinksUtil","MWMsgActionLogUtils","MWMsgMediaTypeLogUtils","MWPreloadableQueries","MWVersion","MultipleTabsLogger","ReQL","asyncToGeneratorRuntime","gkx","isStringNullOrEmpty","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";var h,i=b("$InternalEnum")({Text:"text",NonText:"non-text"});function a(a,b,c,d){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f){var g=a.hasAttachment;g=g===void 0?!1:g;var j=a.hasEphemeralSetting,l=a.hasGroupInvite,m=a.hasReaction,n=a.hasReply,o=a.hasSenderKeyDistribution,p=a.hasStoryReply,q=a.hasText;q=q===void 0?!1:q;var r=a.isBump,s=a.isEdit,t=a.isForward,u=a.isRetry,v=a.isUnsend;a=babelHelpers.objectWithoutPropertiesLoose(a,["hasAttachment","hasEphemeralSetting","hasGroupInvite","hasReaction","hasReply","hasSenderKeyDistribution","hasStoryReply","hasText","isBump","isEdit","isForward","isRetry","isUnsend"]);j=d("MWMsgActionLogUtils").getActionType({hasEphemeralSetting:j,hasGroupInvite:l,hasReaction:m,hasReply:n,hasSenderKeyDistribution:o,hasStoryReply:p,isBump:r,isEdit:s,isForward:t,isRetry:u,isUnsend:v});l=d("MWMsgMediaTypeLogUtils").getAttachmentType(a);m=q&&(!l||l===d("MWMsgMediaTypeLogUtils").AttachmentType.None)?i.Text:i.NonText;n=f!=null?parseInt(f,10):void 0;isNaN(n)&&(n=void 0);return{bool:{has_attachment:g,has_sticker:a.hasSticker,has_text:q,is_attachments_grouped:a.isAttachmentsGrouped,is_pdb:Boolean(c("gkx")("8483")),is_tlc_public_user:c("gkx")("24028"),use_maw_shared_worker:d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker()},"int":{number_of_attachments:a.numberOfAttachments,number_of_grouped_attachments:a.numberOfGroupedAttachments,offline_threading_id:n},string:{action_type:j,attachment_type:l,entrypoint:k(b),message_type:m,multipleTabs:yield d("MultipleTabsLogger").getMultipleTabsAnnotation(),mw_version:(h||(h=d("I64"))).to_string(d("MWVersion").v2),offline_threading_id:f,root_component:e}}});return j.apply(this,arguments)}function k(a){var b;return(b=Object.keys(c("LSMessagingThreadAttributionType")).find(function(b){return c("LSMessagingThreadAttributionType")[b]===a}))!=null?b:"UNKNOWN"}function e(a){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=a.db,c=a.threadKey;a=babelHelpers.objectWithoutPropertiesLoose(a,["db","threadKey"]);b=b!=null&&c!=null?yield d("ReQL").toArrayAsync(d("MWPreloadableQueries").getMediaStagingQuery(b,c)):[];return{mediaStagings:b,messageTypeParams:m(babelHelpers["extends"]({},a,{mediaStagings:b}))}});return l.apply(this,arguments)}function m(a){var b=a.externalAttachmentUrl,e=a.hotEmojiSize,f=a.isBump,g=a.isEdit,h=a.isEphemeralSetting,i=a.isForward,j=a.isGroupInvite,k=a.isReaction,l=a.isRetry,m=a.isSenderKeyDistribution,n=a.isUnsend,o=a.mediaGroupInfo,p=a.mediaStagings,q=a.messageText,r=a.reply,s=a.sentFromShareSheet,t=a.stickerId,u=a.threadType;a=a.voiceClip;var v=!c("isStringNullOrEmpty")(q);q=d("MWHasLinksUtil").getHasLinks(q);var w=!1;a=a!=null;a?w=!0:b!=null&&(w=!0);var x=!1,y=!1,z=!1,A=0,B=0,C;if(p!=null&&p.length>0){w=!0;for(var D of p){var E=D.mimeType;E.indexOf("image")!==-1&&(x=!0);E.indexOf("video")!==-1&&(y=!0);E.indexOf("application")!==-1&&(z=!0)}if(x&&u!=null){E=d("LSMessagingThreadTypeUtil").isArmadilloSecure(u);C=!E}A=p.length}o!=null&&(C=!0,B=o.messagesAndAttachments.length);D=e!=null;u=r!=null;E=r!=null;p=t!=null&&!D;o=!p&&b!=null;e=s===!0;r=k===!0;t=j===!0;b=h===!0;s=m===!0;return{hasAttachment:w,hasEphemeralSetting:b,hasFile:z,hasGif:o,hasGroupInvite:t,hasHotEmoji:D,hasImage:x,hasLinks:q,hasReaction:r,hasReply:u,hasSenderKeyDistribution:s,hasShare:e,hasSticker:p,hasStoryReply:E,hasText:v,hasVideo:y,hasVoiceClip:a,isAttachmentsGrouped:C,isBump:f!=null?f:!1,isEdit:g!=null?g:!1,isForward:i!=null?i:!1,isRetry:l!=null?l:!1,isUnsend:n!=null?n:!1,numberOfAttachments:A,numberOfGroupedAttachments:B}}g.MsgType=i;g.getSendToSentBaseAnnotations=a;g.getEntrypointAnnotation=k;g.queryMediaStagingAndGetMessageTypeParams=e;g.getMessageTypeParams=m}),98);
__d("MWSharedMsgLogUtils",["$InternalEnum","I64","IGDInteractionTraceAnnotations","LSIntEnum","LSMessagingThreadTypeUtil","MAWEBSwitch","MWGetOriginalEntrypoint","MWMsgActionLogUtils","MWMsgMediaTypeLogUtils","MWSharedS2SBaseAnnotations","MultipleTabsLogger","asyncToGeneratorRuntime","cr:6985","gkx","isArmadillo","promiseDone","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=b("$InternalEnum")({Text:"text",NonText:"non-text"});function a(a,b,c,d,e){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f,g,i){var j=b("cr:6985")!=null?yield b("cr:6985").isCutoverThreadKey(a.threadKey):!1;e=(yield d("MWSharedS2SBaseAnnotations").getSendToSentBaseAnnotations(e,f,g,i));return{bool:babelHelpers["extends"]({},e.bool,{},d("IGDInteractionTraceAnnotations").getInstamadilloBooleanAnnotations(a),{is_community_event:a.eventStartTimestampMs!=null,is_cutover:c("isArmadillo")()&&j,is_message_request:d("LSMessagingThreadTypeUtil").isMessageRequest(a),is_secure:d("LSMessagingThreadTypeUtil").isArmadilloSecure(a.threadType)}),"int":babelHelpers["extends"]({},e["int"]),string:babelHelpers["extends"]({},e.string,{consistent_thread_fbid:a.consistentThreadFbid!=null?(h||(h=d("I64"))).to_string(a.consistentThreadFbid):null,original_entrypoint:c("MWGetOriginalEntrypoint")(a.threadKey),thread_id:(h||(h=d("I64"))).to_string(a.threadKey),thread_subtype:a.threadSubtype!=null?(h||(h=d("I64"))).to_string(a.threadSubtype):null,thread_type:h.to_string(a.threadType)})}});return k.apply(this,arguments)}function e(a,b,e,f){l(a,b,c("MWGetOriginalEntrypoint")(e.threadKey),e.threadKey,e.consistentThreadFbid,e.threadType,e.threadSubtype,f),d("IGDInteractionTraceAnnotations").addInstamadilloAnnotationsToInteractionTrace(e,a)}function l(a,e,f,g,i,k,l,m){var n=e.hasEphemeralSetting;n=n===void 0?!1:n;var o=e.hasFile;o=o===void 0?!1:o;var p=e.hasGif;p=p===void 0?!1:p;var q=e.hasGroupInvite;q=q===void 0?!1:q;var r=e.hasHotEmoji;r=r===void 0?!1:r;var s=e.hasImage;s=s===void 0?!1:s;var t=e.hasLinks;t=t===void 0?!1:t;var u=e.hasReaction;u=u===void 0?!1:u;var v=e.hasReply;v=v===void 0?!1:v;var w=e.hasSenderKeyDistribution;w=w===void 0?!1:w;var x=e.hasShare;x=x===void 0?!1:x;var y=e.hasSticker;y=y===void 0?!1:y;var z=e.hasStoryReply;z=z===void 0?!1:z;var A=e.hasText;A=A===void 0?!1:A;var B=e.hasVideo;B=B===void 0?!1:B;var C=e.hasVoiceClip;C=C===void 0?!1:C;var D=e.isAttachmentsGrouped,E=e.isBump;E=E===void 0?!1:E;var F=e.isEdit;F=F===void 0?!1:F;var G=e.isForward;G=G===void 0?!1:G;var H=e.isRetry;H=H===void 0?!1:H;var I=e.isUnsend;I=I===void 0?!1:I;var J=e.numberOfAttachments;J=J===void 0?0:J;e=e.numberOfGroupedAttachments;e=e===void 0?0:e;n=d("MWMsgActionLogUtils").getActionType({hasEphemeralSetting:n,hasGroupInvite:q,hasReaction:u,hasReply:v,hasSenderKeyDistribution:w,hasStoryReply:z,isBump:E,isEdit:F,isForward:G,isRetry:H,isUnsend:I});q=d("MWMsgMediaTypeLogUtils").getAttachmentType({hasFile:o,hasGif:p,hasHotEmoji:r,hasImage:s,hasLinks:t,hasShare:x,hasSticker:y,hasVideo:B,hasVoiceClip:C,numberOfAttachments:J,numberOfGroupedAttachments:e});u=A&&(!q||q===d("MWMsgMediaTypeLogUtils").AttachmentType.None)?j.Text:j.NonText;d("MultipleTabsLogger").addAnnotationWithInteractionUuid(a.getTraceId());a.addAnnotation("attachment_type",q);a.addAnnotation("action_type",n);a.addAnnotation("message_type",u);a.addAnnotation("original_entrypoint",f);m!=null&&a.addAnnotation("root_component",m);a.addAnnotationInt("number_of_attachments",J);D!=null&&a.addAnnotationBoolean("is_attachments_grouped",D);a.addAnnotation("thread_id",(h||(h=d("I64"))).to_string(g));i!=null&&a.addAnnotation("consistent_thread_fbid",(h||(h=d("I64"))).to_string(i));a.addAnnotation("thread_type",h.to_string(k));l!=null&&a.addAnnotation("thread_subtype",(h||(h=d("I64"))).to_string(l));b("cr:6985")!=null&&c("promiseDone")(b("cr:6985").isCutoverThreadKey(g),function(b){a.addAnnotationBoolean("is_cutover",b)});a.addAnnotationBoolean("is_secure",d("LSMessagingThreadTypeUtil").isArmadilloSecure(k));a.addAnnotationBoolean("is_eb_enabled",c("MAWEBSwitch").isEnabled());a.addAnnotationBoolean("use_maw_shared_worker",d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker());a.addAnnotationBoolean("is_pdb",Boolean(c("gkx")("8483")))}function f(a){if((h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(2)))return"media";else if((h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(4)))return"audio";else if((h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(4096))||(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(134217728)))return"sticker";else if((h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(16384)))return"gif";else if((h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(64)))return"file";else if((h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(1)))return"text";else if((h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(1024))||(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(536870912)))return"XMA";else return"unknown"}g.MsgType=j;g.getSendToSentAnnotations=a;g.addSendMessageMetadataForInteractionTrace=e;g.addSendMessageMetadataForInteractionTraceWithThreadMetadata=l;g.getMessageTypeFromDisplayedContentType=f}),98);
__d("sendToSentQPLLogger",["LSDatabaseSingleton","LSIntEnum","MAWMIC","MAWSetupWorkerAuxStateForLogging","MAWWaitForBackendSetup","MWLogSendToSentFailedError","MWMsgMediaTypeLogUtils","MWSharedMsgLogUtils","MWSharedS2SBaseAnnotations","Network","QPLEvent","QPLUserFlow","QuickPerformanceLogger","Random","ReQL","WAExceededStorageQuota","WAGetSafeQPLError","WAGetStorageQplAnnotations","asyncToGeneratorRuntime","gkx","interaction-tracing","justknobx","mergeDeep","pageID","performance","promiseDone","qpl","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l,m=c("requireDeferred")("MAWWebWorkerSingleton").__setRef("sendToSentQPLLogger"),n=c("qpl")._(25313175,"1551"),o=new Map();function p(a,b){c("QPLUserFlow").addAnnotations(n,b,{instanceKey:a})}function a(a,b){c("QPLUserFlow").endCancel(n,{annotations:b!=null?b:{},instanceKey:a}),u(a)}function e(a,b,e,f){b=f!=null?f.type:b;c("MWLogSendToSentFailedError")({qplEventType:n,qplInstanceKey:a},b,e,f==null?void 0:f.errorCode,f==null?void 0:f.applicationErrorCode);c("QPLUserFlow").endFailure(n,b,{annotations:{bool:{exceededStorageQuota:d("WAExceededStorageQuota").getExceededStorageQuota(),useSentBytesCache:c("gkx")("33008"),wasBackendSetupReset:d("MAWWaitForBackendSetup").wasBackendSetupReset(),workerTerminatedPermanently:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.workerTerminatedPermanently},string:{errorDescription:d("WAGetSafeQPLError").getSafeQPLErrorMessage(e)},string_array:{worker_restart_msgs:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartMessageTypes,worker_restart_reasons:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartReasons}},error:e,instanceKey:a});u(a)}function q(a,b,d,e){a!=null&&c("QPLUserFlow").addPoint(n,b,{data:e,debugInfo:d,instanceKey:a})}function r(a){(k||(k=c("QuickPerformanceLogger"))).markerEnd(n,160,a),u(a)}function s(a,b){q(a,b?"network_connection_online":"network_connection_offline"),b||r(a)}function t(a){var b=c("interaction-tracing").NetworkStatusTracker.onChange(function(b){b=b.online;s(a,b)});o.set(a,b);s(a,c("interaction-tracing").NetworkStatusTracker.isOnline());if(c("Network").containsNetworkInformation()){b=c("Network").getRTT();b!=null&&p(a,{"int":{network_rtt:b}});b=c("Network").getEffectiveType();b!=null&&p(a,{string:{network_effective_type:String(b)}});var d=c("Network").getBandwidth();d!=null&&p(a,{"int":{network_bandwidth:d}});d=c("Network").getType();d!=null&&p(a,{string:{network_connectivity_type:String(b)}})}}function u(a){var b;(b=o.get(a))==null?void 0:b.remove();o["delete"](a)}function v(a){window.navigator&&window.navigator.hardwareConcurrency&&p(a,{"int":{hardware_concurrency:window.navigator.hardwareConcurrency}})}function w(a,b){a===void 0&&(a=!0);var e=Date.now()+(Math.round(d("Random").random()*1e4)+1e4);c("QPLUserFlow").start(x(b),{annotations:{bool:{backendSetupReady:d("MAWWaitForBackendSetup").isBackendSetupSuccessful(),wasBackendSetupReset:d("MAWWaitForBackendSetup").wasBackendSetupReset(),workerTerminatedPermanently:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.workerTerminatedPermanently},"int":{timeMsSinceSessionStart:(l||(l=c("performance"))).now()},string:{hostname:window.location.hostname,pageID:c("pageID"),workerHeartbeatsOnStart:d("MAWSetupWorkerAuxStateForLogging").getHeartbeatHistoryAsString(),workerID:d("MAWWaitForBackendSetup").getCurrentWorkerID()},string_array:{worker_restart_msgs:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartMessageTypes,worker_restart_reasons:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartReasons}},instanceKey:e});c("promiseDone")(d("MAWMIC").getState().then(function(a){p(e,{string:{mic_state:a}})}));a&&c("promiseDone")(d("WAGetStorageQplAnnotations").getStorageQplAnnotations().then(function(a){p(e,a)}));c("promiseDone")((h||(h=d("LSDatabaseSingleton"))).LSDatabaseSingleton.then(function(a){return d("ReQL").firstAsync(d("ReQL").fromTableDescending(a.tables.connectivity_status)).then(function(a){if(!a)return;p(e,{"int":{connection_state_ls_transport:(i||(i=d("LSIntEnum"))).toNumber(a.internetConnectionState)}})})}));c("promiseDone")(m.load().then(function(a){return a.getWorkerHealthStatus().then(function(a){p(e,{string:{workerHealthStatusOnStart:a.tag}})})}));t(e);v(e);return e}function x(a){if((j||(j=d("QPLEvent"))).getSamplingMethod(n)!==1)return n;if(!y(a))return n;a=(j||(j=d("QPLEvent"))).getSampleRate(n);var b=c("justknobx")._("3946");b=Math.max(1,Math.min(b,a));return{i:j.getMarkerId(n),m:j.getSamplingMethod(n),r:b}}function y(a){return a===d("MWMsgMediaTypeLogUtils").AttachmentType.Application||a===d("MWMsgMediaTypeLogUtils").AttachmentType.Audio||a===d("MWMsgMediaTypeLogUtils").AttachmentType.File||a===d("MWMsgMediaTypeLogUtils").AttachmentType.Gif||a===d("MWMsgMediaTypeLogUtils").AttachmentType.Image||a===d("MWMsgMediaTypeLogUtils").AttachmentType.Link||a===d("MWMsgMediaTypeLogUtils").AttachmentType.Sticker||a===d("MWMsgMediaTypeLogUtils").AttachmentType.Video||a===d("MWMsgMediaTypeLogUtils").AttachmentType.Share}function f(a){c("QPLUserFlow").endSuccess(n,{annotations:{bool:{exceededStorageQuota:d("WAExceededStorageQuota").getExceededStorageQuota()}},instanceKey:a}),u(a)}function z(a,b,e,f,g,h){g===void 0&&(g=!0);var i=w(g);if(a==null)return i;g=d("MWSharedS2SBaseAnnotations").getMessageTypeParams(babelHelpers["extends"]({},b,{threadType:a.threadType}));c("promiseDone")(d("MWSharedMsgLogUtils").getSendToSentAnnotations(a,g,e,void 0,h),function(a){a=c("mergeDeep")(a,f);p(i,a)});return i}var A=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,d){var e=a+"_start";a=a+"_end";c("QPLUserFlow").addPoint(n,e,{instanceKey:b});e=(yield d());c("QPLUserFlow").addPoint(n,a,{instanceKey:b});return e});return function(b,c,d){return a.apply(this,arguments)}}();g.addSendToSentAnnotations=p;g.markSendToSentCancel=a;g.markSendToSentFail=e;g.markSendToSentPoint=q;g.markSendToSentStart=w;g.markSendToSentSuccess=f;g.markSendToSentStartWithAnnotation=z;g.measureSendToSentPerformance=A}),98);
__d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE",["MAWThreadMappingState","MWFBLogger","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=babelHelpers.taggedTemplateLiteralLoose(["Executing ",", jid: ",""]);h=function(){return a};return a}function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["Executing ",", jid: ",""]);i=function(){return a};return a}var j=d("MWFBLogger").MWLogger.tags(["MiActMapping","UIActions","ThreadReady"]);function a(a,b,c,d){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e){var f=(yield d("MAWThreadMappingState").getThreadKeyIfMiThreadNotMissing(a,b));if(f==null)throw j.mustfixThrow("Missing thread key or MI mapping for jid: %s, thread action: %s, MI mapping state %s",c,b);j.DEBUG(i(),c,b);return e(a,f)});return k.apply(this,arguments)}function c(a,b,c,d){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e){var f=(yield d("MAWThreadMappingState").getThreadKeyIfMiThreadNotMissing(a,b));if(f!=null){j.DEBUG(h(),c,b);return e(a,f)}});return l.apply(this,arguments)}g.onMiThreadExistsForJid__DO_NOT_USE=a;g.onMiThreadExistsForJidNoThrow__DO_NOT_USE=c}),98);
__d("MAWBlockUtils",["I64","LSContactUserBlockAction","LSIntEnum","LSThreadBitOffset","ReQL","asyncToGeneratorRuntime","promiseDone","react","useReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=(k||d("react")).useCallback,m=[0,31,35,55,56,58,24,25,19,85],n=[0,31,35,55,56,58,24,25,19,85,8,22],o=[35,19,85,56,24,25];function a(){var a=(h||(h=c("useReStore")))();return l(function(e,g){c("promiseDone")(b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var b=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.tables.participants).getKeyRange(e)));if(b==null)return;var h=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.tables.threads).getKeyRange(b.threadKey).filter(function(a){return(i||(i=d("I64"))).equal(a.threadType,(j||(j=d("LSIntEnum"))).ofNumber(15))})));if(h==null)return;b=(i||(i=d("I64"))).equal(g,(j||(j=d("LSIntEnum"))).ofNumber(c("LSContactUserBlockAction").MN_BLOCK))?d("LSThreadBitOffset").clear(m,h.capabilities,h.capabilities2,h.capabilities3,h.capabilities4,h.capabilities5):(i||(i=d("I64"))).equal(g,(j||(j=d("LSIntEnum"))).ofNumber(c("LSContactUserBlockAction").FB_BLOCK))?d("LSThreadBitOffset").clear(n,h.capabilities,h.capabilities2,h.capabilities3,h.capabilities4,h.capabilities5):d("LSThreadBitOffset").set(o,h.capabilities,h.capabilities2,h.capabilities3,h.capabilities4,h.capabilities5);var k=b[0],l=b[1];yield a.runInTransaction(function(a){var b=babelHelpers["extends"]({},h,{capabilities:k,capabilities2:l});return a.threads.put(b)},"readwrite",void 0,void 0,f.id+":137")})())},[a])}g.clearedMnBlockCapabilities=m;g.clearedFbBlockCapabilities=n;g.useChangeThreadBlockCapabilities=a}),98);
__d("clearedMAWMnRestrictCapabilities",[],(function(a,b,c,d,e,f){"use strict";a=[0,31,35,55,56,58,24,25,35,19];b=a;f["default"]=b}),66);
__d("MAWUpdateLSThreadCapabilities",["fbt","I64","LSBitOffset","LSContactBitOffset","LSContactBlockedByViewerStatus","LSIntEnum","LSMessagingThreadTypeUtil","LSThreadBitOffset","MAWBlockUtils","MAWCurrentUser","MAWMessageRequestUtil","Promise","ReQL","asyncToGeneratorRuntime","clearedMAWMnRestrictCapabilities","emptyFunction"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k,l=d("MAWCurrentUser").getID(),m=(k||(k=d("I64"))).of_string(l);function a(a,e){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(e)).then(function(c){if(c==null)return(i||(i=b("Promise"))).resolve();var e=d("LSBitOffset").set((j||(j=d("LSIntEnum"))).ofNumber(27),c.capabilities);c=babelHelpers["extends"]({},c,{capabilities:e});return a.threads.put(c)}).then(c("emptyFunction"))}function e(a,e){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(e)).then(function(c){if(c==null)return(i||(i=b("Promise"))).resolve();var e=d("LSBitOffset").clear((j||(j=d("LSIntEnum"))).ofNumber(27),c.capabilities);c=babelHelpers["extends"]({},c,{capabilities:e});return a.threads.put(c)}).then(c("emptyFunction"))}function f(a,e){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(e)).then(function(c){if(c==null)return(i||(i=b("Promise"))).resolve([void 0,void 0]);var f=d("ReQL").firstAsync(d("LSMessagingThreadTypeUtil").isOneToOne(c.threadType)?d("ReQL").mergeJoin(d("ReQL").fromTableAscending(a.participants).getKeyRange(e),d("ReQL").fromTableAscending(a.contacts)).filter(function(a){a=a[1];return!(k||(k=d("I64"))).equal(a.id,m)}).map(function(a){a=a[1];return a}):d("ReQL").empty());return(i||(i=b("Promise"))).all([i.resolve(c),f])}).then(function(e){var f=e[0];if(f==null)return(i||(i=b("Promise"))).resolve();e=e[1];if(e==null)return(i||(i=b("Promise"))).resolve();var g=(k||(k=d("I64"))).equal(e.blockedByViewerStatus,(j||(j=d("LSIntEnum"))).ofNumber(c("LSContactBlockedByViewerStatus").MESSAGE_BLOCKED)),h=k.equal(e.blockedByViewerStatus,j.ofNumber(c("LSContactBlockedByViewerStatus").FULLY_BLOCKED));e=d("LSContactBitOffset").has(66,e);h=h?d("LSThreadBitOffset").clear(d("MAWBlockUtils").clearedFbBlockCapabilities,f.capabilities,f.capabilities2,f.capabilities3,f.capabilities4,f.capabilities5):g?d("LSThreadBitOffset").clear(d("MAWBlockUtils").clearedMnBlockCapabilities,f.capabilities,f.capabilities2,f.capabilities3,f.capabilities4,f.capabilities5):e?d("LSThreadBitOffset").clear(c("clearedMAWMnRestrictCapabilities"),f.capabilities,f.capabilities2,f.capabilities3,f.capabilities4,f.capabilities5):[f.capabilities,f.capabilities2,f.capabilities3,f.capabilities4,f.capabilities5];g=h[0];e=h[1];var l=h[2],m=h[3];h=h[4];return a.threads.put(babelHelpers["extends"]({},f,{capabilities:g,capabilities2:e,capabilities3:l,capabilities4:m,capabilities5:h})).then(c("emptyFunction"))})}function n(a,b){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){a=a.threads;c=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a).getKeyRange(c)));if(c==null)return(i||(i=b("Promise"))).resolve();var e=d("LSThreadBitOffset").set.apply(d("LSThreadBitOffset"),[d("MAWMessageRequestUtil").disabledThreadCapabilitiesForIncomingRequest].concat(d("LSThreadBitOffset").clear(d("MAWMessageRequestUtil").enabledThreadCapabilitiesForIncomingRequest,c.capabilities,c.capabilities2,c.capabilities3,c.capabilities4,c.capabilities5))),f=e[0],g=e[1],h=e[2],j=e[3];e=e[4];yield a.put(babelHelpers["extends"]({},c,{additionalThreadContext:void 0,capabilities:f,capabilities2:g,capabilities3:h,capabilities4:j,capabilities5:e}))});return o.apply(this,arguments)}function p(a,b,c){return q.apply(this,arguments)}function q(){q=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){b=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(b)));if(b==null)return;c=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.contacts,["name"]).getKeyRange(c)));if(c==null)return;c=h._(/*BTDS*/"{Inviter Name} invited you to join",[h._param("Inviter Name",c.name)]).toString();var e=d("LSThreadBitOffset").set([105],b.capabilities,b.capabilities2,b.capabilities3,b.capabilities4,b.capabilities5),f=e[0],g=e[1],i=e[2],j=e[3];e=e[4];yield a.threads.put(babelHelpers["extends"]({},b,{capabilities:f,capabilities2:g,capabilities3:i,capabilities4:j,capabilities5:e,snippet:c}))});return q.apply(this,arguments)}function r(a,b){return s.apply(this,arguments)}function s(){s=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){var e=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(c)));if(e==null)return;c=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.group_invites).getKeyRange(c)));var f=d("LSThreadBitOffset").clear([105],e.capabilities,e.capabilities2,e.capabilities3,e.capabilities4,e.capabilities5),g=f[0],h=f[1],i=f[2],j=f[3];f=f[4];yield a.threads.put(babelHelpers["extends"]({},e,{capabilities:g,capabilities2:h,capabilities3:i,capabilities4:j,capabilities5:f,snippet:void 0}));yield c.map(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){yield a.group_invites["delete"](b.threadKey,b.inviterId,b.inviteeId)});return function(a){return c.apply(this,arguments)}}())});return s.apply(this,arguments)}g.enableAddMembersTxn=a;g.disableAddMembersTxn=e;g.disableBlockerCapabilitiesTxn=f;g.updateNonMessageRequestThreadTxn=n;g.setGroupInviteTxn=p;g.clearGroupInviteTxn=r}),226);
__d("ReQLTable",["asyncToGeneratorRuntime"],(function(a,b,c,d,e,f){"use strict";function a(a,b,c){return g.apply(this,arguments)}function g(){g=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){var d=(yield a.get.apply(a,b));c=c(d);yield a.upsert(b,c)});return g.apply(this,arguments)}f.update=a}),66);
__d("uniqueArray",[],(function(a,b,c,d,e,f){"use strict";function g(a){return a}function a(a,b){var c=b!=null?b:g,d=[],e=new Set();a.forEach(function(a){var b=c(a);if(e.has(b))return;e.add(b);d.push(a)});return d}f["default"]=a}),66);
__d("MAWBridgeParticipantsUpdatedHandler",["fbt","I64","LSAuthorityLevel","LSContactBlockedByViewerStatus","LSContactGender","LSContactIdType","LSContactType","LSContactViewerRelationship","LSContactWorkForeignEntityType","LSFactory","LSGroupParticipantJoinState","LSIntEnum","LSVerifyContactRowExistsStoredProcedure","MAWCurrentUser","MAWJids","MAWMiActMappingTableAPI","MAWMiActOnMiThreadExistsForJid__DO_NOT_USE","MAWTimeUtils","MAWUpdateLSThreadCapabilities","Promise","ReQLTable","asyncToGeneratorRuntime","gkx","uniqueArray","vulture"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k,l=d("MAWCurrentUser").getID(),m=(k||(k=d("I64"))).of_string(l);function a(a,b){b=b.map(function(a){var b=a.chatJid,c=a.type;a=a.userJid;return babelHelpers["extends"]({chatJid:b},A(c),{userJid:a})});return n(a,b)}function e(a,b){if(!c("gkx")("13628"))return y(a,b);b=b.participants.map(function(a){a.deliveredWatermarkTs;var b=a.fbid;a.lastReadActionTs;a.readWatermarkTs;var c=a.threadJid;a=babelHelpers.objectWithoutPropertiesLoose(a,["deliveredWatermarkTs","fbid","lastReadActionTs","readWatermarkTs","threadJid"]);return babelHelpers["extends"]({chatJid:c,userJid:d("MAWJids").toUserJid(b)},a)});return n(a,b)}function n(a,b){return o.apply(this,arguments)}function o(){o=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c){c=(yield s(a,c));yield (i||(i=b("Promise"))).all(c.map(function(c){var e=c.mawParticipant,f=c.threadKey,g=d("MAWJids").convertChatJidToIntJid(e.userJid);return[a.participants.get(f,g).then(function(c){var d=w(c,e,f);return c!=null?a.participants.put(d):(i||(i=b("Promise"))).all([a.participants.add(d),q(a,g)])}),u(a,g,f,e.isAdmin)]}).flat(Infinity))});return o.apply(this,arguments)}function p(a,b){return(k||(k=d("I64"))).equal(a,(k||(k=d("I64"))).zero)?b:a}function q(a,b){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){yield c("LSVerifyContactRowExistsStoredProcedure")(c("LSFactory")(a),{authorityLevel:(j||(j=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").OPTIMISTIC),blockedByViewerStatus:j.ofNumber(c("LSContactBlockedByViewerStatus").UNBLOCKED),contactIdType:j.ofNumber(c("LSContactIdType").FBID),contactType:j.ofNumber(c("LSContactType").USER),contactViewerRelationship:j.ofNumber(c("LSContactViewerRelationship").UNKNOWN),gender:j.ofNumber(c("LSContactGender").UNKNOWN),id:b,isBlocked:!1,isMemorialized:!1,isSelf:l===(k||(k=d("I64"))).to_string(b),workForeignEntityType:j.ofNumber(c("LSContactWorkForeignEntityType").UNKNOWN)})});return r.apply(this,arguments)}function s(a,b){return t.apply(this,arguments)}function t(){t=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){var f=c("uniqueArray")(e.map(function(a){return a.chatJid})),g=new Map();yield (i||(i=b("Promise"))).all(f.map(function(b){return d("MAWMiActMappingTableAPI").getThreadKeyForChatJid(a,b).then(function(a){return a!=null&&g.set(b,a)})}));return e.map(function(a){var b=g.get(a.chatJid);return b==null?null:{mawParticipant:a,threadKey:b}}).filter(Boolean)});return t.apply(this,arguments)}function u(a,b,c,d){return v.apply(this,arguments)}function v(){v=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e){yield d("MAWUpdateLSThreadCapabilities").disableBlockerCapabilitiesTxn(a,c),(k||(k=d("I64"))).equal(m,b)&&(e?yield d("MAWUpdateLSThreadCapabilities").enableAddMembersTxn(a,c):yield d("MAWUpdateLSThreadCapabilities").disableAddMembersTxn(a,c))});return v.apply(this,arguments)}function w(a,b,c){return a==null?babelHelpers["extends"]({},x(b),{authorityLevel:(k||(k=d("I64"))).zero,contactId:d("MAWJids").convertChatJidToIntJid(b.userJid),deliveredWatermarkTimestampMs:k.zero,isSuperAdmin:b.isSuperAdmin,nickname:void 0,normalizedSearchTerms:void 0,participantCapabilities:void 0,readActionTimestampMs:k.zero,readWatermarkTimestampMs:k.zero,threadKey:c,threadRoles:k.zero}):babelHelpers["extends"]({},a,{},x(b))}function x(a){return{groupParticipantJoinState:a.isInvited?(j||(j=d("LSIntEnum"))).ofNumber(c("LSGroupParticipantJoinState").INVITED):(j||(j=d("LSIntEnum"))).ofNumber(c("LSGroupParticipantJoinState").MEMBER),isAdmin:a.isAdmin,isModerator:!1,subscribeSource:a.isSuperAdmin?h._(/*BTDS*/"Group creator").toString():void 0}}function y(a,b){return z.apply(this,arguments)}function z(){z=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){e=e.participants.filter(function(a){a=!a.fbid.startsWith("shadow_");a||c("vulture")("Yo_bP2RGeN3xZlA5MJbxDlcF808=");return a});yield (i||(i=b("Promise"))).all(e.map(function(e){return d("MAWMiActOnMiThreadExistsForJid__DO_NOT_USE").onMiThreadExistsForJidNoThrow__DO_NOT_USE(a,e.threadJid,"MAWBridgeParticipantsUpdatedHandler",function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var f=(k||(k=d("I64"))).of_string(e.fbid),g=[e.readWatermarkTs,e.deliveredWatermarkTs,e.lastReadActionTs].map(d("MAWTimeUtils").toTimestamp),i=g[0],l=g[1],m=g[2],n={groupParticipantJoinState:e.isInvited?(j||(j=d("LSIntEnum"))).ofNumber(c("LSGroupParticipantJoinState").INVITED):(j||(j=d("LSIntEnum"))).ofNumber(c("LSGroupParticipantJoinState").MEMBER),isAdmin:e.isAdmin,isModerator:!1,subscribeSource:e.isSuperAdmin?h._(/*BTDS*/"Group creator").toString():void 0};g=!1;var o=!1;yield d("ReQLTable").update(a.participants,[b,f],function(a){o=!0;if(a!=null){var c={deliveredWatermarkTimestampMs:p(l,a.deliveredWatermarkTimestampMs),readActionTimestampMs:p(m,a.readActionTimestampMs),readWatermarkTimestampMs:p(i,a.readWatermarkTimestampMs)};return babelHelpers["extends"]({},a,{},c,{},n)}else{g=!0;return babelHelpers["extends"]({},n,{authorityLevel:(k||(k=d("I64"))).zero,contactId:f,deliveredWatermarkTimestampMs:l,isSuperAdmin:e.isSuperAdmin,nickname:void 0,normalizedSearchTerms:void 0,participantCapabilities:void 0,readActionTimestampMs:k.zero,readWatermarkTimestampMs:i,threadKey:b})}});o&&(yield u(a,f,b,e.isAdmin));g&&(yield q(a,f))});return function(b,c){return a.apply(this,arguments)}}())}))});return z.apply(this,arguments)}function A(a){return{isAdmin:a==="superadmin"||a==="admin",isInvited:a==="invitedParticipant",isSuperAdmin:a==="superadmin"}}g.callFromMainThread=a;g.call=e;g.mawToLsParticipantTypeConversion=A}),226);
__d("MAWTrackPendingOccamadilloThreads",["Deferred","LSDatabaseSingleton","LSInitSyncCompleteSubscription","LSIntEnum","LSPlatformLsInitLog","MAWMIC","MAWMICSchema","QuickPerformanceLogger","asyncToGeneratorRuntime","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=(h={},h[d("MAWMICSchema").ANNOTATIONS.occamadilloBatchesSentToWorker]=0,h[d("MAWMICSchema").ANNOTATIONS.occamadilloPendingThreadsAfterInitSync]=0,h[d("MAWMICSchema").ANNOTATIONS.occamadilloDuplicateThreads]=0,h);(function(){Object.keys(l).forEach(function(a){d("MAWMIC").addIntAnnotation(a,0)})})();function m(a){d("MAWMIC").addIntAnnotation(a,++l[a])}function a(){return l.occamadilloBatchesSentToWorker}var n=new(c("Deferred"))(),o=new Set(),p=new Set(),q=new Set(),r=new Set();function e(a){a=a.callingSource;r.add((k||(k=d("LSIntEnum"))).toNumber(a));d("MAWMIC").addIntArrayAnnotation(d("MAWMICSchema").ANNOTATIONS.occamadilloCallingSources,Array.from(r))}function f(a){d("MAWMIC").addPoint(d("MAWMICSchema").POINTS.thread_mapping_start);if(o.has(a)){m(d("MAWMICSchema").ANNOTATIONS.occamadilloDuplicateThreads);return}p.add(a);d("LSPlatformLsInitLog").LsSync.isRunning()&&q.add(a);(i||(i=c("QuickPerformanceLogger"))).currentTimestamp();o.add(a);d("MAWMIC").addIntAnnotation("occamadilloThreadCount",o.size);d("LSPlatformLsInitLog").LsSync.hasEnded()&&m(d("MAWMICSchema").ANNOTATIONS.occamadilloPendingThreadsAfterInitSync)}function s(){d("MAWMIC").addBoolAnnotation("threadMappingAcked",!0)}function t(a){return u.apply(this,arguments)}function u(){u=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){if(c("gkx")("2661"))yield n.getPromise();else{var b=(yield (j||(j=d("LSDatabaseSingleton"))).LSDatabaseSingleton);yield d("LSInitSyncCompleteSubscription").maybeWaitForSyncGroup(b,(k||(k=d("LSIntEnum"))).ofNumber(95))}p["delete"](a);q["delete"](a);d("MAWMIC").addIntAnnotation("occamadilloCompleteThreadCount",o.size-p.size);w()});return u.apply(this,arguments)}function v(){n.resolve(),w()}function w(){d("MAWMIC").addIntAnnotation("occamadilloThreadCount",o.size);if(d("LSPlatformLsInitLog").LsSync.hasEnded()&&q.size===0){d("MAWMIC").addPoint(d("MAWMICSchema").POINTS.thread_mapping_end);return}p.size===0&&d("MAWMIC").addPoint(d("MAWMICSchema").POINTS.thread_mapping_end)}g.incrementCounter=m;g.getBatchesSentNumber=a;g.markThreadPassedToNativeOp=e;g.addPendingThread=f;g.markBridgeCallAcked=s;g.removePendingThread=t;g.completeSync=v;g.maybeEndOccamadilloThreadMapping=w}),98);
__d("MAWWorkerEvent",["FBLogger","MAWMIC","MAWReliabilityMonitor","MAWSetupWorkerAuxStateForLogging","MAWTrackPendingOccamadilloThreads","MAWWaitForBackendSetup","MultipleTabsLogger","QPLUserFlow","pageID","performanceNow","promiseDone","qpl","requireDeferred","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("requireDeferred")("MAWBridgeObserver").__setRef("MAWWorkerEvent"),j=c("requireDeferred")("MAWWebWorkerSingleton").__setRef("MAWWorkerEvent"),k=new Set(),l=new Set(["addEbUploadTrackingWorkerQPL","ackWithInstanceKey","addMediaDownloadQpl","searchGetFTSRestoreSessionId","none"]),m=new Set(["bulkCreateThread","bulkCreateOrUpdateThread","bulkMaybeCreateOrUpdateThread","createThread","createOrUpdateThread"]),n=new Map(),o=function(a){switch(a){case"sendAndReceive":return 1e4;case"fireAndForget":return 4e3;case"waitUntilCompleted":return 1e4;case"waitUntilPersisted":return 1e4;default:return 1e4}},p=function(a,b,d){d=window.setTimeout(function(){v(a,"ack_timeout"),c("promiseDone")(i.load().then(function(a){a.notifyUnackedEvent({route:b,timestamp:(h||(h=c("performanceNow")))()})}))},o(d));n.set(a,d);return d},q=function(a){var b=n.get(a);b!=null&&(window.clearTimeout(b),c("promiseDone")(i.load().then(function(a){a.resetUnackedMessages()})),n["delete"](a))};function a(a,b,e){if(k.has(e)||l.has(b))return;k.add(e);c("QPLUserFlow").start(c("qpl")._(**********,"807"),{annotations:{bool:{duringMIC:d("MAWMIC").duringMIC(),useSharedWorker:d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker(),workerTerminatedPermanently:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.workerTerminatedPermanently},"int":{key:e},string:{callType:a,event:b,pageID:c("pageID"),workerHeartbeatsOnStart:d("MAWSetupWorkerAuxStateForLogging").getHeartbeatHistoryAsString(),workerID:d("MAWWaitForBackendSetup").getCurrentWorkerID()},string_array:{worker_restart_msgs:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartMessageTypes,worker_restart_reasons:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartReasons}},instanceKey:e});void d("MultipleTabsLogger").hasMultipleTabs().then(function(a){c("QPLUserFlow").addAnnotations(c("qpl")._(**********,"807"),{bool:{isMultiTabs:a}},{instanceKey:e})});void j.load().then(function(a){return a.getWorkerHealthStatus().then(function(a){c("QPLUserFlow").addAnnotations(c("qpl")._(**********,"807"),{string:{workerHealthStatusOnStart:a.tag}})})})}function b(a){if(!k.has(a))return;k["delete"](a);c("QPLUserFlow").endSuccess(c("qpl")._(**********,"807"),{instanceKey:a})}function e(a,b){if(!k.has(a))return;k["delete"](a);c("QPLUserFlow").endFailure(c("qpl")._(**********,"807"),"fail",{annotations:{bool:{workerTerminatedPermanently:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.workerTerminatedPermanently},string:{error:b},string_array:{worker_restart_msgs:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartMessageTypes,worker_restart_reasons:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartReasons}},instanceKey:a})}function f(a,b,e){if(!k.has(a))return;k["delete"](a);c("QPLUserFlow").endFailure(c("qpl")._(**********,"807"),"timeout",{annotations:{bool:{workerTerminatedPermanently:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.workerTerminatedPermanently},string:{error:b,workerHealthStatus:e.tag},string_array:{worker_restart_msgs:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartMessageTypes,worker_restart_reasons:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartReasons}},instanceKey:a})}function r(a,b){if(!k.has(a))return;c("QPLUserFlow").addAnnotations(c("qpl")._(**********,"807"),b,{instanceKey:a})}function s(a,b){if(!k.has(a))return;try{if(Array.isArray(b)){var e=b.every(function(a){return typeof a==="object"&&a!=null&&"state"in a&&a.state===d("MAWReliabilityMonitor").HealthReportState.OK.valueOf()});b=b.reduce(function(a,b){typeof b==="object"&&b!=null&&"state"in b&&"identifier"in b&&"reason"in b&&b.state!==d("MAWReliabilityMonitor").HealthReportState.OK.valueOf()&&typeof b.state==="string"&&typeof b.reason==="string"&&typeof b.identifier==="string"&&a.push([b.identifier,b.state,b.reason].join(":"));return a},[]);r(a,{bool:{workerHealthStatus:e},string:{error:(a=b[0])!=null?a:void 0},string_array:{errors:b}})}}catch(a){c("FBLogger")("messenger_web").catching(a).mustfix("Failed to log ack result annotations");return!1}}function t(a,b,c){if(typeof c==="object"&&typeof (c==null?void 0:c.timestamp)==="number"){c=c.payload;q(a);v(a,"ack");s(a,c);m.has(b)&&d("MAWTrackPendingOccamadilloThreads").markBridgeCallAcked()}}function u(a,b,c){l.has(b)||p(a,b,c),v(a,"send")}function v(a,b,d){if(!k.has(a))return;c("QPLUserFlow").addPoint(c("qpl")._(**********,"807"),b,{instanceKey:a,timestamp:d})}g.getUnackedEventTimeout=o;g.logStart=a;g.logSuccess=b;g.logFailure=e;g.logTimeoutFailure=f;g.logAnnotations=r;g.logAck=t;g.logSend=u}),98);
__d("MAWBridgeLoggingUtils",["MAWWorkerEvent","ODS","Random","WAHashStringToNumber"],(function(a,b,c,d,e,f,g){"use strict";var h,i=3185,j="armadillo_worker_runtime",k="getWorkerHeartbeat",l=10,m=new Array(l);function n(a,b,c){(h||(h=d("ODS"))).bumpEntityKey(i,j,a+"_"+b+"_"+c)}function a(a){return d("WAHashStringToNumber").hashStringToNumber(a+Math.round(d("Random").random()*1e4)+1e4)}function b(a,b,c,e){n(b,c,"start");if(c===k)return;(h||(h=d("ODS"))).bumpEntityKey(i,j,"route_start");d("MAWWorkerEvent").logStart(a,c,e);m.length===l&&m.shift();m.push(b+"_"+c)}function o(a){m.filter(Boolean).forEach(function(b){(h||(h=d("ODS"))).bumpEntityKey(i,j,b+a)})}function c(a,b,c){n(a,b,"success");if(b===k)return;(h||(h=d("ODS"))).bumpEntityKey(i,j,"route_success");d("MAWWorkerEvent").logSuccess(c)}function e(a,b,c,e){n(a,b,"fail");if(b===k)return;(h||(h=d("ODS"))).bumpEntityKey(i,j,"route_fail");d("MAWWorkerEvent").logFailure(e,c)}function f(a,b,c,e){n(a,b,"timeout");if(b===k)return;(h||(h=d("ODS"))).bumpEntityKey(i,j,"route_timeout");d("MAWWorkerEvent").logTimeoutFailure(c,"Route timeout",e)}function p(){o("_before_unresponsive"),m.fill(null)}g.CAT_ID=i;g.ENT=j;g.HEARTBEAT_ROUTE=k;g.RECENT_ROUTES_NUM=l;g.getBridgeEventInstanceKey=a;g.routeStart=b;g.logRecentRoutes=o;g.routeSuccess=c;g.routeFail=e;g.routeTimeoutFail=f;g.logRecentBridgeEvents=p}),98);
__d("WAAbortError",["WACustomError"],(function(a,b,c,d,e,f,g){"use strict";b=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}return b}(d("WACustomError").customError("AbortError"));var h="AbortError";function a(a){return function(b){if(b.name===h)return a(b);else throw b}}g.AbortError=b;g.ABORT_ERROR=h;g.catchAbort=a}),98);
__d("WAPromiseDelays",["Promise","WAAbortError"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,c){return new(h||(h=b("Promise")))(function(b,e){if(c==null?void 0:c.aborted)throw new(d("WAAbortError").AbortError)();var f=setTimeout(g,a);c==null?void 0:c.addEventListener("abort",h);function g(){c==null?void 0:c.removeEventListener("abort",h),b()}function h(){c==null?void 0:c.removeEventListener("abort",h),clearTimeout(f),e(new(d("WAAbortError").AbortError)())}})}function c(a,c,d){return new(h||(h=b("Promise")))(function(b,e){var f=setTimeout(function(){try{b(d())}catch(a){e(a)}},c);a.then(function(a){clearTimeout(f),b(a)},function(a){clearTimeout(f),e(a)})})}g.delayMs=a;g.withTimeout=c}),98);
__d("MAWMaybeWithTimeout",["MAWODSProxy","WAOdsEnums","WAPromiseDelays"],(function(a,b,c,d,e,f,g){"use strict";function h(){return!0}function a(a,b,c,e){return h()?d("WAPromiseDelays").withTimeout(a.then(function(a){d("MAWODSProxy").odsBumpEntityKey({entity:d("WAOdsEnums").Entity.MAW_NON_TIMEOUT,key:e!=null?e:"unknown"});return a}),b,function(){d("MAWODSProxy").odsBumpEntityKey({entity:d("WAOdsEnums").Entity.MAW_TIMEOUT,key:e!=null?e:"unknown"});return c()}):a}g.maybeWithTimeout=a}),98);
__d("MAWTimedBridge",["MAWBridgeLoggingUtils","MAWMaybeWithTimeout","MAWWaitForBackendSetup","MAWWebWorkerSingleton","WALogger"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=babelHelpers.taggedTemplateLiteralLoose(["route.",""]);h=function(){return a};return a}var i=17e4;function a(a,b,c,e,f){f===void 0&&(f=i);return d("MAWWaitForBackendSetup").waitForBackendSetup(c).then(function(){return d("MAWMaybeWithTimeout").maybeWithTimeout(a(),f,function(){return d("MAWWebWorkerSingleton").getWorkerHealthStatus().then(function(a){d("MAWBridgeLoggingUtils").routeTimeoutFail(b,c,e,a);d("WALogger").COUNT(h(),c).tags("bridge_timeout");throw new j("Bridge route "+c+" timed out, WorkerHealthStatus: "+a.tag)})},c)})}var j=function(b){babelHelpers.inheritsLoose(a,b);function a(a){var c;c=b.call(this,a)||this;c.name="BridgeTimeoutError";c.message=a;return c}return a}(babelHelpers.wrapNativeSuper(Error));g.DEFAULT_MAW_BRIDGE_TIMEOUT_MS=i;g.bridgeTimeout=a;g.MAWBridgeTimeoutError=j}),98);
__d("MAWBridgeSendAndReceive",["MAWBridge","MAWBridgeLoggingUtils","MAWTimedBridge","MAWWorkerEvent"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c,e){var f=d("MAWBridgeLoggingUtils").getBridgeEventInstanceKey(b),g=(e==null?void 0:e.isLoggingDisabled)!==!0,h=d("MAWTimedBridge").bridgeTimeout(function(){g&&d("MAWBridgeLoggingUtils").routeStart("sendAndReceive",a,b,f);return d("MAWBridge").getBridge().sendAndReceive(a,b,c,e==null?void 0:e.isLoggingDisabled,{onAck:function(a){g&&d("MAWWorkerEvent").logAck(f,b,a)},onSend:function(a){var c=a.queueMsgs;a=a.queueSize;g&&(d("MAWWorkerEvent").logSend(f,b,"sendAndReceive"),d("MAWWorkerEvent").logAnnotations(f,{"int":{bridgeSize:a},string_array:{bridgeMsgs:c}}))}},{bridgeQPLInstanceKey:f}).then(function(c){g&&d("MAWBridgeLoggingUtils").routeSuccess(a,b,f);return c})["catch"](function(c){g&&d("MAWBridgeLoggingUtils").routeFail(a,b,c.message,f);throw c})},a,b,f,e==null?void 0:e.timeoutMs);return h}g.sendAndReceive=a}),98);
__d("MAWFolderTypes",[],(function(a,b,c,d,e,f){"use strict";a=0;b=1;c=2;d=3;e=10;var g=15,h={ARCHIVED:e,INBOX:a,OTHER:c,PENDING:b,RESTRICTED:g,SPAM:d};f.INBOX=a;f.PENDING=b;f.OTHER=c;f.SPAM=d;f.ARCHIVED=e;f.RESTRICTED=g;f.FOLDER_ID=h}),66);
__d("MessagingFolderTag",[],(function(a,b,c,d,e,f){a=Object.freeze({INBOX:"inbox",OTHER:"other",SPAM:"spam",PENDING:"pending",MONTAGE:"montage",HIDDEN:"hidden",LEGACY:"legacy",DISABLED:"disabled",PAGE_BACKGROUND:"page_background",PAGE_DONE:"page_done",BLOCKED:"blocked",COMMUNITY:"community",RESTRICTED:"restricted",BC_PARTNERSHIP:"bc_partnership",E2EE_CUTOVER:"e2ee_cutover",E2EE_CUTOVER_ARCHIVED:"e2ee_cutover_archived",E2EE_CUTOVER_PENDING:"e2ee_cutover_pending",E2EE_CUTOVER_OTHER:"e2ee_cutover_other",INTEROP:"interop",INTEROP_PENDING:"interop_pending",ARCHIVED:"archived",AI_ACTIVE:"ai_active",SALSA_RESTRICTED:"salsa_restricted",MESSENGER_MARKETING_MESSAGE:"messenger_marketing_message",SECRET_CONVERSATIONS:"secret_conversations",WEBCHAT_PLUGIN:"webchat_plugin",NFB_OVERFLOW:"nfb_overflow",UNKNOWN:""});f["default"]=a}),66);
__d("MAWFolderUtils",["FBLogger","I64","LSCoreClientFolderType","LSIntEnum","MAWFolderTypes","MessagingFolderTag"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a){switch(a){case d("MAWFolderTypes").PENDING:return c("MessagingFolderTag").PENDING;case d("MAWFolderTypes").OTHER:return c("MessagingFolderTag").OTHER;case d("MAWFolderTypes").SPAM:return c("MessagingFolderTag").SPAM;case d("MAWFolderTypes").ARCHIVED:return c("MessagingFolderTag").UNKNOWN;default:return c("MessagingFolderTag").INBOX}}function b(a){switch(a){case d("MAWFolderTypes").PENDING:return(h||(h=d("LSIntEnum"))).ofNumber(-101);case d("MAWFolderTypes").OTHER:return(h||(h=d("LSIntEnum"))).ofNumber(-102);case d("MAWFolderTypes").SPAM:return(h||(h=d("LSIntEnum"))).ofNumber(-103);case d("MAWFolderTypes").ARCHIVED:return(h||(h=d("LSIntEnum"))).ofNumber(-110);default:return(h||(h=d("LSIntEnum"))).ofNumber(-100)}}function e(a){switch(a){case d("MAWFolderTypes").ARCHIVED:return(h||(h=d("LSIntEnum"))).ofNumber(c("LSCoreClientFolderType").ARCHIVED);case d("MAWFolderTypes").INBOX:return(h||(h=d("LSIntEnum"))).ofNumber(c("LSCoreClientFolderType").INBOX);case d("MAWFolderTypes").OTHER:return(h||(h=d("LSIntEnum"))).ofNumber(c("LSCoreClientFolderType").SPAM);case d("MAWFolderTypes").PENDING:return(h||(h=d("LSIntEnum"))).ofNumber(c("LSCoreClientFolderType").OTHER);case d("MAWFolderTypes").RESTRICTED:return(h||(h=d("LSIntEnum"))).ofNumber(c("LSCoreClientFolderType").RESTRICTED);case d("MAWFolderTypes").SPAM:return(h||(h=d("LSIntEnum"))).ofNumber(c("LSCoreClientFolderType").PENDING);default:a;throw c("FBLogger")("messenger_web").mustfixThrow("Unhandled folder ID: "+a)}}function f(a){switch(a){case(h||(h=d("LSIntEnum"))).ofNumber(-1):return(h||(h=d("LSIntEnum"))).ofNumber(-101);case(h||(h=d("LSIntEnum"))).ofNumber(-2):return(h||(h=d("LSIntEnum"))).ofNumber(-102);case(h||(h=d("LSIntEnum"))).ofNumber(-3):return(h||(h=d("LSIntEnum"))).ofNumber(-103);case(h||(h=d("LSIntEnum"))).ofNumber(-10):return(h||(h=d("LSIntEnum"))).ofNumber(-110);default:return(h||(h=d("LSIntEnum"))).ofNumber(-100)}}function j(a){switch(a){case"inbox":return d("MAWFolderTypes").FOLDER_ID.INBOX;case"pending":return d("MAWFolderTypes").FOLDER_ID.PENDING;case"spam":return d("MAWFolderTypes").FOLDER_ID.SPAM;case"archived":return d("MAWFolderTypes").FOLDER_ID.ARCHIVED;case"other":return d("MAWFolderTypes").FOLDER_ID.OTHER;case"restricted":return d("MAWFolderTypes").FOLDER_ID.RESTRICTED;default:return null}}function k(a){switch((i||(i=d("I64"))).to_int32(a)){case d("MAWFolderTypes").FOLDER_ID.INBOX:return 0;case d("MAWFolderTypes").FOLDER_ID.PENDING:return-1;case d("MAWFolderTypes").FOLDER_ID.SPAM:return-3;case d("MAWFolderTypes").FOLDER_ID.ARCHIVED:return-10;case d("MAWFolderTypes").FOLDER_ID.OTHER:return-2;default:return null}}g.getMessagingFolderTag=a;g.getClientSystemFolder=b;g.systemFolderToLSCoreClientFolder=e;g.systemFolderToClientSystemFolder=f;g.folderTagToFolderId=j;g.parentThreadKeyToLsSystemFolder=k}),98);
__d("isOfflineThreadingId",["I64"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||(h=d("I64"))).to_string(h.max_int).length;function a(a){return(h||(h=d("I64"))).to_string(a).length===i}g["default"]=a}),98);
__d("MAWMiActOnActThreadReadyInstrumentation",["QPLUserFlow","Random","WAHashStringToNumber","isOfflineThreadingId","qpl"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return d("WAHashStringToNumber").hashStringToNumber(a+Math.round(d("Random").random()*1e4)+1e4)}function b(a,b,d,e){c("QPLUserFlow").start(c("qpl")._(1056846543,"1530"),{annotations:{bool:{is_otid:c("isOfflineThreadingId")(d)},string:{apiName:e,description:b}},instanceKey:a})}function e(a,b){c("QPLUserFlow").addPoint(c("qpl")._(1056846543,"1530"),b,{instanceKey:a})}function f(a,b){c("QPLUserFlow").addAnnotations(c("qpl")._(1056846543,"1530"),b,{instanceKey:a})}function h(a){c("QPLUserFlow").endSuccess(c("qpl")._(1056846543,"1530"),{instanceKey:a})}function i(a,b){c("QPLUserFlow").endFailure(c("qpl")._(1056846543,"1530"),b,{instanceKey:a})}g.getOnActThreadReadyInstanceKey=a;g.startQPL=b;g.addPointQPL=e;g.addAnnotationsQPL=f;g.endSuccessQPL=h;g.endFailureQPL=i}),98);
__d("MAWVerifyThreadExistsUtils",["FBLogger","I64","LSAuthorityLevel","LSIntEnum","LSVerifyE2EEMetadataThreadExistsV2StoredProcedure","isOfflineThreadingId"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=new Map(),k=6e4,l=1e4;function m(a){if(j.size>l){c("FBLogger")("messenger_web").mustfix("MAWVerifyThreadExistsUtils: requestedThreadKeys size exceeded limit");return}j.has(a)&&(window.clearTimeout(j.get(a)),j["delete"](a));j.set(a,window.setTimeout(function(){j["delete"](a)},k))}function a(a){return j.has(a)}function b(a,b,e){var f=b.threadKey;f!=null&&m((h||(h=d("I64"))).to_string(f));f!=null&&c("isOfflineThreadingId")(f)&&(h||(h=d("I64"))).equal(b.authorityLevel,(i||(i=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").AUTHORITATIVE))&&c("FBLogger")("messenger_web").warn("MAWVerifyThreadExistsUtils: runVerifyThreadExistsSproc called with offline thread key - callsite: %s",e);return c("LSVerifyE2EEMetadataThreadExistsV2StoredProcedure")(a,b)}g.isThreadRequested=a;g.runVerifyThreadExistsSproc=b}),98);
__d("MAWMiActOnActThreadReadyQueue",["ExecutionEnvironment","I64","LSPlatformLsInitLog","MAWInit","MAWMIC","MAWMiActOnActThreadReadyInstrumentation","MAWThreadMappingQPL","MAWVerifyThreadExistsUtils","MWChatInteraction","MWFBLogger","asyncToGeneratorRuntime","clearTimeout","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["Error executing callback on ACT thread ready: ",", original threadState: ",", threadKey: ",""]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["Executing queued "," for threadKey: ",""]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["Took longer than "," ms to execute: "," in threadState: "," for threadKey: ",", onActThreadReady"]);l=function(){return a};return a}var m=d("MWFBLogger").MWLogger.tags(["MiActMapping","UIActions","ThreadReady"]),n=new Map(),o=3e4;function a(a,b,e,f,g,j){var k;if(!(i||(i=c("ExecutionEnvironment"))).isInBrowser)return;d("MAWMiActOnActThreadReadyInstrumentation").addPointQPL(j,"enqueue_by_thread_key_start");var p=(h||(h=d("I64"))).to_string(a);k=(k=n.get(p))!=null?k:[];var q=c("setTimeout")(function(){var b=d("MWChatInteraction").getInteractionTraceForThreadKey((h||(h=d("I64"))).to_string(a));b==null?void 0:b.addMarkerPoint("timeout_when_waiting_for_act_thread","AppTiming");b==null?void 0:b.addAnnotation("callsite_waiting_for_act_thread",f);b==null?void 0:b.addAnnotation("initial_thread_state",g);b==null?void 0:b.addAnnotationBoolean("is_mi_thread_requested",d("MAWVerifyThreadExistsUtils").isThreadRequested(p));d("MAWMiActOnActThreadReadyInstrumentation").addAnnotationsQPL(j,{bool:{during_mic:d("MAWMIC").duringMIC(),is_thread_requested:d("MAWVerifyThreadExistsUtils").isThreadRequested(p),ls_sync_has_ended:d("LSPlatformLsInitLog").LsSync.hasEnded(),maw_init_completed:d("MAWInit").MAWInitState.isCompleted()}});d("MAWMiActOnActThreadReadyInstrumentation").endFailureQPL(j,"enqueue_by_thread_key_timeout");m.MUSTFIX(l(),o,f,g,p);e(g)},o);k.push({callback:b,description:f,telemetryInstanceKey:j,threadState:g,timeoutId:q});n.set(p,k)}function e(a,b,c,e){if(a==null&&b==null)return;var f=a!=null?b!=null?"both":"clientThreadKey":"authoritativeThreadKey";a=a==null?0:(a=(a=n.get(a))==null?void 0:a.length)!=null?a:0;b=b==null?0:(b=(b=n.get(b))==null?void 0:b.length)!=null?b:0;d("MAWThreadMappingQPL").addAnnotations({"int":{authorativeThreadKeyCallbacksNum:b,clientThreadKeyCallbacksNum:a},string:{flushBasedOnKey:f,flushReason:e}},c);d("MAWThreadMappingQPL").addPoint("flush_awaiting_callbacks",c)}function f(a,b,c,d){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e,f){var g=(h||(h=d("I64"))).to_string(a),i=n.get(g);if(i==null||i.length===0)return;while(i.length!==0){var l=i.shift();if(l==null)continue;var o=l.callback,p=l.description,q=l.telemetryInstanceKey,r=l.threadState;l=l.timeoutId;d("MAWMiActOnActThreadReadyInstrumentation").addPointQPL(q,"enqueue_by_thread_key_end");d("MAWMiActOnActThreadReadyInstrumentation").addAnnotationsQPL(q,{string:{caller_identifier:e,processed_based_on_key:f}});try{m.DEBUG(k(),p,g),d("MAWMiActOnActThreadReadyInstrumentation").endSuccessQPL(q),yield o(a,b),c("clearTimeout")(l)}catch(a){c("clearTimeout")(l),d("MAWMiActOnActThreadReadyInstrumentation").endFailureQPL(q,"process_queue_on_act_thread_ready_failure"),m.catching(a).MUSTFIX(j(),p,r,g)}}n["delete"](g)});return p.apply(this,arguments)}g.enqueueByThreadKey=a;g.logOnFlushQueue=e;g.processQueueOnActThreadReady=f}),98);
__d("MAWRemoveCorruptedParticipantsFromOneToOneChat",["I64","Promise","ReQL","WAJids","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,b,c,d){return j.apply(this,arguments)}function j(){j=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e,f){e=d("WAJids").extractUserId(e);var g=[e];f!==e&&g.push(f);e=(yield d("ReQL").toArrayAsync(d("ReQL").fromTableAscending(a.participants).getKeyRange(c)));f=e.filter(function(a){return!g.includes((i||(i=d("I64"))).to_string(a.contactId))}).map(function(a){return a.contactId});yield (h||(h=b("Promise"))).all(f.map(function(b){return a.participants["delete"](c,b)}))});return j.apply(this,arguments)}g["default"]=a}),98);
__d("MAWActThreadMapping",["I64","JSResourceForInteraction","MAWBridgeGroupInviteLoadedHandler","MAWBridgeOccamadilloVerifyThreadExistsHandler","MAWBridgeOneToOneMessageRequestLoadedHandler","MAWBridgeParticipantsUpdatedHandler","MAWBridgeSendAndReceive","MAWFolderTypes","MAWFolderUtils","MAWJids","MAWMiActMappingTableAPI","MAWMiActOnActThreadReadyQueue","MAWRemoveCorruptedParticipantsFromOneToOneChat","MAWThreadMappingQPL","MAWUserJidWrapper","MWFBLogger","Promise","ReQL","WAArrayZip","WAJids","asyncToGeneratorRuntime","emptyFunction","getErrorSafe","gkx","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=c("JSResourceForInteraction")("MAWSyncThreadDescriptionAdminMsg").__setRef("MAWActThreadMapping"),k=new Map(),l=null;function m(){return c("gkx")("14178")}function n(a,b,e,g){var h=g.then(function(g){c("promiseDone")(a.runInTransaction(function(a){return s(a,p(g),e)},"readwrite",void 0,void 0,f.id+":91")["catch"](function(a){b.forEach(function(a){return k["delete"](a)}),d("MWFBLogger").MWLogger.warn("MAWActThreadMapping","Post processing transaction not committed due to error "+a.message)}));for(var h of g)h.status==="rejected"&&k["delete"](h.jid);return g},function(a){b.forEach(function(a){return k["delete"](a)});return b.map(function(b){return{jid:b,reason:c("getErrorSafe")(a).message,status:"rejected"}})});b.forEach(function(a){return k.set(a,h.then(function(b){b=b.find(function(b){var c;return((c=b.value)==null?void 0:c.chatJid)===a||b.jid===a});if(b==null)throw d("MWFBLogger").MWLogger.tags(["MiActMapping","Occam"]).mustfixThrow("Should have thread creation result for every thread");return b}))})}function o(a){var b=new Map(),c=[];a.threads.forEach(function(a){var e=k.get(a.chatJid),f=a.instanceKey;e!=null?(f!=null&&(d("MAWThreadMappingQPL").addPoint("reuse_existing_promise_for_act_mapping",f),d("MAWThreadMappingQPL").endCancel("act_mapping_is_already_in_progress",f)),c.push(e)):b.set(a.chatJid,a)});return{cachedPromises:c,threadsToVerifyByJid:b}}function p(a){var b=new Map();a.forEach(function(a){if(a.status==="fulfilled"){b.set(a.value.chatJid,babelHelpers["extends"]({},a.value,{adminMsgParams:(a=a.value.adminMsgParams)!=null?a:null}))}});return b}function q(a,b){return r.apply(this,arguments)}function r(){r=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e=(yield d("MAWBridgeSendAndReceive").sendAndReceive("backend","bulkCreateOrUpdateThread",b));c("promiseDone")(a.runInTransaction(function(a){return s(a,p(e),b)},"readwrite",void 0,void 0,f.id+":209"));return e});return r.apply(this,arguments)}function a(a,c){if(!m())return q(a,c);l==null&&(l=u(a));c.withoutSnippetRecalculation;var e=babelHelpers.objectWithoutPropertiesLoose(c,["withoutSnippetRecalculation"]);c=o(c);var f=c.cachedPromises;c=c.threadsToVerifyByJid;if(c.size===0)return(i||(i=b("Promise"))).all(f).then(function(a){return a.map(function(a){if(a.status==="fulfilled"){var b=a.value;b.adminMsgParams;b=babelHelpers.objectWithoutPropertiesLoose(b,["adminMsgParams"]);return{status:a.status,value:b}}else return{reason:a.reason,status:a.status}})});var g=d("MAWBridgeSendAndReceive").sendAndReceive("backend","bulkMaybeCreateOrUpdateThread",babelHelpers["extends"]({},e,{threads:Array.from(c.values())}));n(a,Array.from(c.keys()),e,g);return(i||(i=b("Promise"))).all([g].concat(f)).then(function(a){return a.flat(1).map(function(a){if(a.status==="fulfilled"){var b=a.value;b.adminMsgParams;b=babelHelpers.objectWithoutPropertiesLoose(b,["adminMsgParams"]);return{status:a.status,value:b}}else return{reason:a.reason,status:a.status}})})}function e(a){var b=a.authoritativeThreadKey,e=a.description,f=a.instanceKey,g=a.jid;a=a.optimisticThreadKey;f!=null&&d("MAWMiActOnActThreadReadyQueue").logOnFlushQueue(a,b,f,e);a!=null&&c("promiseDone")(d("MAWMiActOnActThreadReadyQueue").processQueueOnActThreadReady((h||(h=d("I64"))).of_string(a),g,e,"clientThreadKey"));b!=null&&c("promiseDone")(d("MAWMiActOnActThreadReadyQueue").processQueueOnActThreadReady((h||(h=d("I64"))).of_string(b),g,e,"authoritativeThreadKey"))}function s(a,b,c){return t.apply(this,arguments)}function t(){t=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f){if(f.threads.length===0)return;var g=[],k=d("WAJids").extractUserId(d("MAWUserJidWrapper").getMyUserJid()),l=c("gkx")("14386"),n=m();yield (i||(i=b("Promise"))).all(f.threads.map(function(f){var o=f.authoritativeThreadKey,p=f.chatJid,q=f.instanceKey;f=e.get(p);if(f==null)return(i||(i=b("Promise"))).resolve();var r=[],s=f.adminMsgParams,t=f.clientThreadKey,u=f.folder,v=f.isCreated,x=f.lastActivityTs;f=f.participants;f.forEach(function(a){g.push(babelHelpers["extends"]({},a,{chatJid:p}))});var y=d("WAJids").switchOnMsgrChatJidType(p,{group:function(a){return!0},user:function(a){return!1}});l&&r.push(y?d("MAWBridgeGroupInviteLoadedHandler").callFromMainThread(a,p,k):d("MAWBridgeOneToOneMessageRequestLoadedHandler").callFromMainThread(a,p));f=d("WAJids").interpretAsUserJid(p);f!=null&&m()&&r.push(c("MAWRemoveCorruptedParticipantsFromOneToOneChat")(a,(h||(h=d("I64"))).of_string(o),f,k));!v&&n&&r.push(w(a,o,u!=null?u:d("MAWFolderTypes").FOLDER_ID.INBOX));s!=null&&n&&r.push(j.load().then(function(b){return b(a,p,o,s)}));return(i||(i=b("Promise"))).all(r).then(function(){return d("MAWBridgeOccamadilloVerifyThreadExistsHandler").callFromMainThread(a,{authoritativeThreadKey:o,clientThreadKey:t,description:"bulkCreateThreadWithoutAfterTxn",folder:u,instanceKey:q,isGroup:y,jid:p,lastActivityTs:x})})}));c("gkx")("13628")&&(yield d("MAWBridgeParticipantsUpdatedHandler").callFromMainThread(a,g))});return t.apply(this,arguments)}function u(a){return d("MAWMiActMappingTableAPI").subscribeToMappingDeletion(a,function(a,b){b=d("MAWJids").convertIntJidToOneToOneChatJid(a);k.has(b)&&k["delete"](b);b=d("MAWJids").convertIntJidToGroupJid(a);k.has(b)&&k["delete"](b)})}function v(a,c){k.clear(),d("WAArrayZip").zip(a,c).forEach(function(a){var c=a[0],d=a[1];return k.set(c,new(i||(i=b("Promise")))(function(a,b){return a(d)}))})}function w(a,b,e){return d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange((h||(h=d("I64"))).of_string(b))).then(function(b){if(b==null)return;var c=d("MAWFolderUtils").getMessagingFolderTag(e);c=c==="inbox"?void 0:b.cannotReplyReason;return a.threads.put(babelHelpers["extends"]({},b,{cannotReplyReason:c}))}).then(c("emptyFunction"))}g.isActThreadMappingDedupEnabled=m;g.bulkVerifyThreadExistsInWorker=a;g.executePromisesBlockedOnActThreadMapping=e;g.setCache_TEST_ONLY=v}),98);
__d("MAWBridgeFireAndForget",["MAWBridge","MAWBridgeLoggingUtils","MAWTimedBridge","MAWWaitForBackendSetup","MAWWorkerEvent","Promise","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("justknobx")._("1143");function a(a,c,e,f){return d("MAWWaitForBackendSetup").runAfterBackendSetup(function(){var g=d("MAWBridgeLoggingUtils").getBridgeEventInstanceKey(c);f!==!0&&d("MAWBridgeLoggingUtils").routeStart("fireAndForget",a,c,g);void d("MAWTimedBridge").bridgeTimeout(function(){return new(h||(h=b("Promise")))(function(b){d("MAWBridge").getBridge().fireAndForget(a,c,e,f,{onAck:function(e){f!==!0&&(d("MAWWorkerEvent").logAck(g,c,e),d("MAWBridgeLoggingUtils").routeSuccess(a,c,g)),b()},onSend:function(a){var b=a.queueMsgs;a=a.queueSize;f!==!0&&(d("MAWWorkerEvent").logSend(g,c,"fireAndForget"),d("MAWWorkerEvent").logAnnotations(g,{"int":{bridgeSize:a},string_array:{bridgeMsgs:b}}))}},{bridgeQPLInstanceKey:g})})},a,c,g,i)},c)}g.fireAndForget=a}),98);
__d("MAWCreateOptimisticThread",["I64","LSAuthorityLevel","LSCreateOfflineThreadingIDStoredProcedure","LSFactory","LSIntEnum","LSMailboxType","LSRtcCallState","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){a=(yield c("LSCreateOfflineThreadingIDStoredProcedure")(c("LSFactory")(a),{timestampMs:(h||(h=d("I64"))).of_float(Date.now())}));a=a[0];return a});return k.apply(this,arguments)}function l(a,b){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e={authorityLevel:(i||(i=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").OPTIMISTIC),folderName:"inbox",lastReadWatermarkTimestampMs:(h||(h=d("I64"))).of_float(0),mailboxType:i.ofNumber(c("LSMailboxType").MESSENGER),ongoingCallState:i.ofNumber(c("LSRtcCallState").NO_ONGOING_CALL),parentThreadKey:h.zero,threadKey:b,threadType:i.ofNumber(15)};yield a.threads.put(e);return b});return m.apply(this,arguments)}function a(a){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=(yield j(a));yield l(a,b);return b});return n.apply(this,arguments)}g.createOfflineThreadingId=j;g.createOptimisticThreadWithThreadKey=l;g.createOptimisticThread=a}),98);
__d("MAWThreadUpdateMiddlewareGatingUtil",["gkx","justknobx","qex"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("8147")}function b(){return c("justknobx")._("4591")}function d(){return c("justknobx")._("4818")}function h(){return c("gkx")("15723")}function e(){var a;return(a=c("qex")._("4760"))!=null?a:!1}function f(){var a=c("gkx")("15723");return h()&&a}function i(){return h()&&j("exposure_logging_enabled")}function j(a){var b,d=!1;switch(a){case"exposure_logging_disabled":return(b=c("qex")._("4722"))!=null?b:d;case"exposure_logging_enabled":return(b=c("qex")._("4723"))!=null?b:d;default:a;return!1}}g.isPeristedUnbumpEnabled=a;g.isThreadCreationOptimisationEnabled=b;g.isNewThreadActivityUpdateEnabled=d;g.isTcCentralizedV2Enabled=h;g.isReverseMsgOrderForThreadUpdateProcessingEnabled=e;g.isMAWCacheServiceEnabled=f;g.isTcCentralizedV2WithNewMiThreadCreationFlowEnabled=i;g.isNewMiThreadCreationFlowEnabled=j}),98);
__d("MAWCreateOptimisticMiThreadForActThread",["DateConsts","DedupMiThreadCreationCache","I64","LSCoreClientFolderType","LSCreateE2EEMetadataThreadStoredProcedure","LSFactory","LSIntEnum","LSThreadPointQueryAndRestoreMessagesWithJIDStoredProcedure","MAWCreateOptimisticThread","MAWFolderUtils","MAWThreadMappingQPL","MAWThreadMappingState","MAWThreadUpdateMiddlewareGatingUtil","MAWTimeUtils","MWFBLogger","Promise","WATimeUtils","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["Creating e2ee metadata optimistic thread. threadKey: ",", bumpTimestampMs: ",", description: ",""]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["upgradeOptimisticActThreadInMI has already been called for jid ",", early returning"]);l=function(){return a};return a}function m(){var a=babelHelpers.taggedTemplateLiteralLoose(["Notifying MI server about thread: int jid ",", state: ",". "]);m=function(){return a};return a}var n=d("MWFBLogger").MWLogger.tags(["Occam","MiActMapping","GetOrCreateThread"]),o=(j||(j=d("I64"))).of_int32(c("LSCoreClientFolderType").INBOX);function p(a){if(a==null){n.warn("No folder ID was supplied to Occamadillo verify thread exists handler");return o}return d("MAWFolderUtils").systemFolderToLSCoreClientFolder(a)}function a(a,b,c){return q.apply(this,arguments)}function q(){q=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f){var g=e.authoritativeThreadKey,i=e.chatJid,k=e.clientThreadKey,l=e.description,m=e.folder,n=e.instanceKey,o=e.intJid,p=e.isGroup;e=e.lastActivityTs;var q=d("MAWThreadUpdateMiddlewareGatingUtil").isNewMiThreadCreationFlowEnabled("exposure_logging_enabled");n!=null&&(d("MAWThreadMappingQPL").addAnnotations({bool:{isNewServerThreadCreationFlowEnabled:q}},n),q&&d("MAWThreadMappingQPL").allowToSkipUpgradingOptimisticThreadInMI(n));if(q){q=d("MAWThreadUpdateMiddlewareGatingUtil").isThreadCreationOptimisationEnabled()?g!=null?g:k:k;q!=null&&(yield d("MAWThreadMappingState").markActThreadMappingAsCompleted(a,{intJid:o,threadKey:(j||(j=d("I64"))).of_string(q)},n),yield d("MAWThreadMappingQPL").measurePerformanceForNullableInstanceKey("thread_point_query_sproc",n,function(){return c("LSThreadPointQueryAndRestoreMessagesWithJIDStoredProcedure")(c("LSFactory")(a),{waJid:o})}))}else{q=k!=null?(j||(j=d("I64"))).of_string(k):yield d("MAWCreateOptimisticThread").createOptimisticThread(a);yield (h||(h=b("Promise"))).all([yield s(a,{chatJid:i,i64ClientThreadKey:q,intJid:o,mawThread:{authoritativeThreadKey:g,description:l,folder:m,instanceKey:n,isGroup:p,lastActivityTs:e},threadLifecycleState:f}),yield d("MAWThreadMappingState").markActThreadMappingAsCompleted(a,{intJid:o,threadKey:q},n)])}});return q.apply(this,arguments)}function r(a){a=d("MAWTimeUtils").millisTimeToTimestamp(a);return(j||(j=d("I64"))).equal(a,(j||(j=d("I64"))).zero)?d("MAWTimeUtils").millisTimeToTimestamp(d("WATimeUtils").millisTime()):a}function s(a,b){return t.apply(this,arguments)}function t(){t=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e){var f=e.chatJid,g=e.i64ClientThreadKey,o=e.intJid,q=e.mawThread;e=e.threadLifecycleState;n.DEBUG(m(),(j||(j=d("I64"))).to_string(o),e==null?void 0:e.type);var s=(yield a.threads.get(g));if(s!=null&&d("DedupMiThreadCreationCache").DedupMiThreadCreationCache.has(f)){n.DEBUG(l(),f);return(h||(h=b("Promise"))).resolve()}var t=c("LSFactory")(a),u=r(q.lastActivityTs),v=p(q.folder),w=q.isGroup?(i||(i=d("LSIntEnum"))).ofNumber(16):(i||(i=d("LSIntEnum"))).ofNumber(15);s=j.to_string(g);a=q.description;n.DEBUG(k(),s,j.to_string(u),a);s=d("WATimeUtils").millisTime();var x=j.to_float(u);Math.abs(x-s)<d("DateConsts").MS_PER_SEC&&n.warn("bumpTimestampMs is too close to now. bumpTimestampMs: %s, lastActivityTs: %s, now: %s, description: %s, threadLifecycleState: %s, mawThread.authoritativeThreadKey is null: %s",x,q.lastActivityTs,s,a,e==null?void 0:e.type,q.authoritativeThreadKey==null?"true":"false");x=q.instanceKey;x!=null&&d("MAWThreadMappingQPL").pendingThreadCreationOnServer.set((j||(j=d("I64"))).to_string(o),x);return d("MAWThreadMappingQPL").measurePerformanceForNullableInstanceKey("create_e2ee_metadata_thread_sproc",x,function(){return c("LSCreateE2EEMetadataThreadStoredProcedure")(t,{bumpTimestampMs:u,createdByLocalDevice:null,folderType:v,offlineThreadKey:g,threadType:w,waJid:o}).then(function(){d("DedupMiThreadCreationCache").addToDedupMiThreadCreationCache(f)})})});return t.apply(this,arguments)}g.maybeUpdateMappingTableAndUpgradeOptimisticActThreadInMI=a;g.upgradeOptimisticActThreadInMI=s}),98);
__d("MAWMiActGetMappingWithMawTypes",["I64","MAWJids","WAJids"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){var c=(h||(h=d("I64"))).to_string(a.jid);b=b?d("WAJids").toGroupJid(c):d("MAWJids").toUserJid(c);return{clientThreadPk:a.clientThreadPk,jid:b,serverThreadKey:a.serverThreadKey}}g["default"]=a}),98);
__d("MAWMiActThreadLifecycleState",[],(function(a,b,c,d,e,f){"use strict";a={AUTHORITATIVE_THREAD_MISSING_MAPPING_ROW:"AUTHORITATIVE_THREAD_MISSING_MAPPING_ROW",AUTHORITATIVE_THREAD_ONLY:"AUTHORITATIVE_THREAD_ONLY",CLIENT_PARTIAL_MI_THREAD:"CLIENT_PARTIAL_MI_THREAD",CLIENT_PARTIAL_MI_THREAD_WITH_ACT:"CLIENT_PARTIAL_MI_THREAD_WITH_ACT",JID_MISSING_MAPPING_ROW:"JID_MISSING_MAPPING_ROW",JID_MISSING_MI_THREAD:"JID_MISSING_MI_THREAD",MI_AND_ACT_THREAD_COMPLETE:"MI_AND_ACT_THREAD_COMPLETE",OPTIMISTIC_THREAD_NO_ACT:"OPTIMISTIC_THREAD_NO_ACT",OPTIMISTIC_THREAD_WITH_ACT:"OPTIMISTIC_THREAD_WITH_ACT",SERVER_PARTIAL_MI_THREAD:"SERVER_PARTIAL_MI_THREAD",THREAD_KEY_ONLY:"THREAD_KEY_ONLY"};f.MiActThreadStatesEnum=a}),66);
__d("MAWMiActGetThreadLifecycleState",["FBLogger","I64","LSAuthorityLevel","LSIntEnum","LSMessagingThreadTypeUtil","MAWMiActGetMappingWithMawTypes","MAWMiActMappingTableAPI","MAWMiActThreadLifecycleState","MAWThreadMappingState","Promise","ReQL","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(a,b,e){var f=(i||(i=d("LSIntEnum"))).toNumber(a.authorityLevel);if(f===c("LSAuthorityLevel").OPTIMISTIC)if(b!=null)return babelHelpers["extends"]({},c("MAWMiActGetMappingWithMawTypes")(b,d("LSMessagingThreadTypeUtil").isGroup(a.threadType)),{type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.OPTIMISTIC_THREAD_WITH_ACT});else return{serverThreadKey:a.threadKey,thread:a,type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.OPTIMISTIC_THREAD_NO_ACT};if(f===c("LSAuthorityLevel").CLIENT_PARTIAL)return b!=null?babelHelpers["extends"]({},c("MAWMiActGetMappingWithMawTypes")(b,d("LSMessagingThreadTypeUtil").isGroup(a.threadType)),{type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.CLIENT_PARTIAL_MI_THREAD_WITH_ACT}):{serverThreadKey:a.threadKey,type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.CLIENT_PARTIAL_MI_THREAD};if(f===c("LSAuthorityLevel").SERVER_PARTIAL)return{serverThreadKey:a.threadKey,type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.SERVER_PARTIAL_MI_THREAD};if(f===c("LSAuthorityLevel").AUTHORITATIVE){if(b==null)return{serverThreadKey:a.threadKey,thread:a,type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.AUTHORITATIVE_THREAD_MISSING_MAPPING_ROW};var g=d("MAWThreadMappingState").getActState(b)===d("MAWThreadMappingState").ActState.IN_PROGRESS;if(!g)return babelHelpers["extends"]({},c("MAWMiActGetMappingWithMawTypes")(b,d("LSMessagingThreadTypeUtil").isGroup(a.threadType)),{type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.MI_AND_ACT_THREAD_COMPLETE});else if(g){g=c("MAWMiActGetMappingWithMawTypes")(b,d("LSMessagingThreadTypeUtil").isGroup(a.threadType));return{jid:g.jid,jidInt64:b.jid,serverThreadKey:g.serverThreadKey,thread:a,type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.AUTHORITATIVE_THREAD_ONLY}}}throw c("FBLogger")("messenger_web").mustfixThrow("[MiActMapping] Unknown thread lifecycle state for threadKey: %s, authorityLevel: %s, thread action: %s",(j||(j=d("I64"))).to_string(a.threadKey),f,e)}function l(a,b,c){return m.apply(this,arguments)}function m(){m=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){var e=(yield d("MAWMiActMappingTableAPI").getMappingRowForIntJid(a,b));if(e==null)return{jidInt64:b,type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.JID_MISSING_MAPPING_ROW};a=(yield d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(e.serverThreadKey)));return a==null?{jidInt64:b,type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.JID_MISSING_MI_THREAD}:k(a,e,c)});return m.apply(this,arguments)}function a(a,b,c){return n.apply(this,arguments)}function n(){n=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e){a=(yield (h||(h=b("Promise"))).all([d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(c)),d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.mi_act_mapping_table).getKeyRange(c))]));var f=a[0];a=a[1];return f==null?{mappingRow:a,miActThreadState:{serverThreadKey:c,type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.THREAD_KEY_ONLY},thread:f}:{mappingRow:a,miActThreadState:k(f,a,e),thread:f}});return n.apply(this,arguments)}function o(a,b,c){return p.apply(this,arguments)}function p(){p=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e){a=(yield (h||(h=b("Promise"))).all([d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.threads).getKeyRange(c)),d("ReQL").firstAsync(d("ReQL").fromTableAscending(a.mi_act_mapping_table).getKeyRange(c))]));var f=a[0];a=a[1];return f==null?{serverThreadKey:c,type:d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.THREAD_KEY_ONLY}:k(f,a,e)});return p.apply(this,arguments)}function e(a,b,c,d){return q.apply(this,arguments)}function q(){q=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e){b=(yield l(a,b,e));if(b.type===d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.JID_MISSING_MAPPING_ROW&&c!=null)return o(a,(j||(j=d("I64"))).of_string(c),"MAWBridgeOccamadilloVerifyThreadExistsHandler");else return b});return q.apply(this,arguments)}g.getThreadLifecycleStateByJid=l;g.getThreadLifecycleStateAndMappingRowAndThread=a;g.getThreadLifecycleStateByThreadKey=o;g.getThreadLifecycleStateByJidOrThreadKey=e}),98);
__d("MAWThreadDefaults",["fbt","I64","LSBitOffset","LSIntEnum","qex"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j;function k(){if(c("qex")._("327")===!0)return!0;else return c("qex")._("121")===!0}function a(a){if(k())return d("LSBitOffset").set((j||(j=d("LSIntEnum"))).ofNumber(42),a);else return a}function b(a){return d("LSBitOffset").set((j||(j=d("LSIntEnum"))).ofNumber(4),a)}e=d("LSBitOffset").set((j||(j=d("LSIntEnum"))).ofNumber(18),d("LSBitOffset").set(j.ofNumber(56),d("LSBitOffset").set(j.ofNumber(14),d("LSBitOffset").set(j.ofNumber(23),d("LSBitOffset").set(j.ofNumber(8),d("LSBitOffset").set(j.ofNumber(15),d("LSBitOffset").set(j.ofNumber(2),d("LSBitOffset").empty)))))));f=a(b(d("LSBitOffset").set(j.ofNumber(7),d("LSBitOffset").set(j.ofNumber(6),d("LSBitOffset").set(j.ofNumber(9),d("LSBitOffset").set(j.ofNumber(10),d("LSBitOffset").set(j.ofNumber(19),d("LSBitOffset").set(j.ofNumber(35),d("LSBitOffset").set(j.ofNumber(3),d("LSBitOffset").set(j.ofNumber(43),e))))))))));a=d("LSBitOffset").set(j.ofNumber(17),d("LSBitOffset").set(j.ofNumber(60),d("LSBitOffset").set(j.ofNumber(16),d("LSBitOffset").set(j.ofNumber(26),d("LSBitOffset").set(j.ofNumber(28),d("LSBitOffset").clear(j.ofNumber(27),f))))));b=d("LSBitOffset").set((i||(i=d("I64"))).sub(j.ofNumber(103),i.of_string("64")),d("LSBitOffset").set(i.sub(j.ofNumber(85),i.of_string("64")),d("LSBitOffset").set(i.sub(j.ofNumber(109),i.of_string("64")),d("LSBitOffset").set(i.sub(j.ofNumber(74),i.of_string("64")),d("LSBitOffset").set(i.sub(j.ofNumber(65),i.of_string("64")),d("LSBitOffset").set(i.sub(j.ofNumber(64),i.of_string("64")),d("LSBitOffset").set(i.sub(j.ofNumber(106),i.of_string("64")),d("LSBitOffset").empty)))))));e=h._(/*BTDS*/"Messages are secured with end-to-end encryption.").toString();h=d("LSBitOffset").set(i.sub(j.ofNumber(172),i.of_string("64")),d("LSBitOffset").set(i.sub(j.ofNumber(170),i.of_string("64")),d("LSBitOffset").empty));g.defaultThreadCapabilities=f;g.defaultGroupThreadCapabilities=a;g.defaultThreadCapabilities_2=b;g.nullstateDescriptionText1=e;g.defaultThreadCapabilities_3=h;g.defaultThreadCapabilities_4=d("LSBitOffset").empty;g.defaultThreadCapabilities_5=d("LSBitOffset").empty}),226);
__d("MAWBridgeOccamadilloVerifyThreadExistsHandler",["I64","LSAuthorityLevel","LSThreadBitOffset","MAWActThreadMapping","MAWBridgeFireAndForget","MAWCreateOptimisticMiThreadForActThread","MAWJids","MAWMiActGetThreadLifecycleState","MAWMiActThreadLifecycleState","MAWThreadDefaults","MAWThreadMappingQPL","MAWThreadMappingState","MAWTimeUtils","MWFBLogger","Promise","WATimeUtils","asyncToGeneratorRuntime","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["Thread loading complete: jid ",", threadKey ",", lastActivityTs: ",""]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["New authoritative thread: jid ",", threadKey: ","), state: ",", lastActivityTs: ",""]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["Authoritative thread not found for clientThreadKey: ",""]);l=function(){return a};return a}function m(){var a=babelHelpers.taggedTemplateLiteralLoose(["Non-positive last activity ts from MI: "," for thread: ",", using local state ts: ",""]);m=function(){return a};return a}var n=d("MWFBLogger").MWLogger.tags(["Occam","VerifyThreadExists"]);function o(a,b,c){a=a;(i||(i=d("I64"))).le(a,(i||(i=d("I64"))).zero)&&(n.WARN(m(),(i||(i=d("I64"))).to_string(a),i.to_string(c),i.to_string(b)),a=b);return a}var p=[105,17,25,24,61,267];function q(a,b){var c=p.filter(function(b){return d("LSThreadBitOffset").has(b,a)});return d("LSThreadBitOffset").set(c,b?d("MAWThreadDefaults").defaultGroupThreadCapabilities:d("MAWThreadDefaults").defaultThreadCapabilities,d("MAWThreadDefaults").defaultThreadCapabilities_2,d("MAWThreadDefaults").defaultThreadCapabilities_3,d("MAWThreadDefaults").defaultThreadCapabilities_4,d("MAWThreadDefaults").defaultThreadCapabilities_5)}function r(a,b,c){return s.apply(this,arguments)}function s(){s=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e){var f=(yield a.threads.get(c));if(f==null){n.mustfix("Thread missing in verify thread exists");return(h||(h=b("Promise"))).resolve()}var g=e.clientThreadKey!=null?(i||(i=d("I64"))).of_string(e.clientThreadKey):f.clientThreadKey,j=o(f.lastActivityTimestampMs,d("MAWTimeUtils").millisTimeToTimestamp(e.lastActivityTs),c);e=q(f,e.isGroup);return a.threads.upsert([c],babelHelpers["extends"]({},f,{capabilities:e[0],capabilities2:e[1],capabilities3:e[2],capabilities4:e[3],capabilities5:e[4],clientThreadKey:g,lastActivityTimestampMs:j}))});return s.apply(this,arguments)}function t(a,b,c,d){return u.apply(this,arguments)}function u(){u=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e,f){yield (h||(h=b("Promise"))).all([d("MAWThreadMappingState").markActThreadMappingAsCompleted(a,{intJid:e,threadKey:c},f.instanceKey),x(a,c,f)])});return u.apply(this,arguments)}function v(a,b,c){return w.apply(this,arguments)}function w(){w=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){(i||(i=d("I64"))).equal(b,c)||(yield a.threads["delete"](c))});return w.apply(this,arguments)}function x(a,b,c){return y.apply(this,arguments)}function y(){y=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e){var f=e.clientThreadKey,g=e.instanceKey,j=e.isGroup,k=e.lastActivityTs;yield d("MAWThreadMappingQPL").measurePerformanceForNullableInstanceKey("handle_existing_thread",g,function(){return(h||(h=b("Promise"))).all([r(a,c,{clientThreadKey:f,isGroup:j,lastActivityTs:k}),f!=null&&v(a,c,(i||(i=d("I64"))).of_string(f))])})});return y.apply(this,arguments)}function z(a,b,c){return A.apply(this,arguments)}function A(){A=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,e){var f=(yield a.threads.get(b));f!=null&&(i||(i=d("I64"))).to_int32(f.authorityLevel)===c("LSAuthorityLevel").AUTHORITATIVE?yield x(a,b,e):e.instanceKey!=null&&d("MAWThreadMappingQPL").handleExistingThreadFail(e.instanceKey,f);f==null&&n.DEBUG(l(),e.clientThreadKey)});return A.apply(this,arguments)}function B(a,b){return C.apply(this,arguments)}function C(){C=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=b.authoritativeThreadKey,e=b.clientThreadKey,f=b.description,g=b.folder,h=b.instanceKey,l=b.isGroup,m=b.jid;b=b.lastActivityTs;var o=d("MAWJids").convertChatJidToIntJid(m),p=(yield d("MAWMiActGetThreadLifecycleState").getThreadLifecycleStateByJidOrThreadKey(a,o,c,"MAWBridgeOccamadilloVerifyThreadExistsHandler"));h!=null&&d("MAWThreadMappingQPL").addAnnotations({string:{threadStateInOccamVTE:p.type.toString()}},h);switch(p.type){case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.JID_MISSING_MAPPING_ROW:case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.JID_MISSING_MI_THREAD:case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.OPTIMISTIC_THREAD_WITH_ACT:case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.CLIENT_PARTIAL_MI_THREAD:case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.CLIENT_PARTIAL_MI_THREAD_WITH_ACT:case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.SERVER_PARTIAL_MI_THREAD:case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.THREAD_KEY_ONLY:return d("MAWCreateOptimisticMiThreadForActThread").maybeUpdateMappingTableAndUpgradeOptimisticActThreadInMI(a,{authoritativeThreadKey:c,chatJid:m,clientThreadKey:e,description:f,folder:g,instanceKey:h,intJid:o,isGroup:l,lastActivityTs:b},p).then(function(){return{previousState:p.type}});case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.AUTHORITATIVE_THREAD_ONLY:case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.AUTHORITATIVE_THREAD_MISSING_MAPPING_ROW:c=(i||(i=d("I64"))).to_string(p.serverThreadKey);n.DEBUG(k(),m,c,p.type,b);return t(a,p.serverThreadKey,o,{clientThreadKey:e,instanceKey:h,isGroup:l,lastActivityTs:b}).then(function(){return{previousState:p.type}});case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.MI_AND_ACT_THREAD_COMPLETE:g=(i||(i=d("I64"))).to_string(p.serverThreadKey);n.DEBUG(j(),m,g,b);return z(a,p.serverThreadKey,{clientThreadKey:e,instanceKey:h,isGroup:l,lastActivityTs:b}).then(function(){return{previousState:p.type}});case d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.OPTIMISTIC_THREAD_NO_ACT:throw n.mustfixThrow("[Unimplemented VerifyThreadExists state: %s for JID: %s, from %s",p.type,m,f);default:p.type;throw n.mustfixThrow("Unhandled VerifyThreadExists state: %s for JID: %s, from %s",p.type,m,f)}});return C.apply(this,arguments)}function D(a,b,c,d){return E.apply(this,arguments)}function E(){E=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e,f){yield d("MAWThreadMappingQPL").measurePerformanceForNullableInstanceKey("insert_ephemeral_cache",f,b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var b=(yield a.threads.get(c));if(b!=null){var f;b={ephemeralExpirationInSec:(i||(i=d("I64"))).to_int32((f=b.disappearingSettingTtl)!=null?f:(i||(i=d("I64"))).zero),ephemeralLastUpdatedOrSetTimestamp:d("WATimeUtils").castMilliSecondsToUnixTime(i.to_float((f=b.disappearingSettingUpdatedTs)!=null?f:(i||(i=d("I64"))).zero))};d("MAWBridgeFireAndForget").fireAndForget("backend","setEphemeralSettingCache",{jid:e,settings:b})}}))});return E.apply(this,arguments)}function a(a,b){return F.apply(this,arguments)}function F(){F=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=b.authoritativeThreadKey,d=b.clientThreadKey,e=b.description,f=b.folder,g=b.instanceKey,h=b.isGroup,i=b.jid;b=b.lastActivityTs;yield H(a,{authoritativeThreadKey:c,clientThreadKey:d,description:e!=null?e:"unknown",folder:f,instanceKey:g,isGroup:h,jid:i,lastActivityTs:b})});return F.apply(this,arguments)}function e(a,b){return G.apply(this,arguments)}function G(){G=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=b.authoritativeThreadKey,d=b.clientThreadKey,e=b.description,f=b.folder,g=b.instanceKey,h=b.isGroup,i=b.jid;b=b.lastActivityTs;yield H(a,{authoritativeThreadKey:c,clientThreadKey:d,description:e,folder:f,instanceKey:g,isGroup:h,jid:i,lastActivityTs:b})});return G.apply(this,arguments)}function H(a,b){return I.apply(this,arguments)}function I(){I=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var e=b.authoritativeThreadKey,f=b.clientThreadKey,g=b.description,h=b.instanceKey,j=b.jid;h!=null&&d("MAWThreadMappingQPL").addPoint("occam_verify_thread_exists_start",h);var k=c("gkx")("11539");k&&d("MAWActThreadMapping").executePromisesBlockedOnActThreadMapping({authoritativeThreadKey:e,description:g,instanceKey:h,jid:j,optimisticThreadKey:f});var l=(yield B(a,b));l=l.previousState;k||d("MAWActThreadMapping").executePromisesBlockedOnActThreadMapping({authoritativeThreadKey:e,description:g,instanceKey:h,jid:j,optimisticThreadKey:f});k=e!=null?e:f;g=k!=null?(i||(i=d("I64"))).of_string(k):null;g!=null&&l!==d("MAWMiActThreadLifecycleState").MiActThreadStatesEnum.MI_AND_ACT_THREAD_COMPLETE&&(yield D(a,g,j,h));if(h!=null){d("MAWThreadMappingQPL").addPoint("occam_verify_thread_exists_end",h);e=d("MAWJids").convertChatJidToIntJid(b.jid);d("MAWThreadMappingQPL").pendingThreadCreationOnServer.has((i||(i=d("I64"))).to_string(e))||(yield d("MAWThreadMappingQPL").end({instanceKey:h,intJid:e,tables:a,threadKey:g}))}});return I.apply(this,arguments)}g.genUpdatesForAuthoritativeThread=x;g.insertEphemeralCache=D;g.call=a;g.callFromMainThread=e}),98);
__d("MAWAnimatedImageThumbnailStore",[],(function(a,b,c,d,e,f){"use strict";var g=new Map();function a(a){return(a=g.get(a))!=null?a:null}function b(a,b){g.set(a,b)}f.getThumbnailSpec=a;f.setThumbnailSpec=b}),66);
__d("MAWClientConsistencyRefresh",["ClientConsistencyEventEmitter","CurrentAppID"],(function(a,b,c,d,e,f,g){"use strict";function a(a){switch(d("CurrentAppID").getAppID()){case 772021112871879:c("ClientConsistencyEventEmitter").emit("hardRefresh",a);break;default:c("ClientConsistencyEventEmitter").emit("softRefresh",a)}}g["default"]=a}),98);
__d("WAShiftTimer",["err"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){var b=this;this.$2=0;this.$3=0;this.ts=0;this.$4=0;this.$5=0;this.$6=0;this.$7=void 0;this.$8=function(){var a=b.$7,c=b.$1;b.$3=0;b.$2=0;b.ts=0;b.$4=0;b.$5=0;b.$6=0;b.$7=void 0;c(a)};this.onOrBefore=function(a,c){h(a);var d=Date.now();d=d+a;var e=b.$5;if(e!==0&&e<d)return;e=b.$6;if(e!==0&&d<e)return;b.$5=d;!b.$3&&(!b.$2||d<b.ts)&&b.$9(d,a,c)};this.forceRunNow=function(a){b.$2&&clearTimeout(b.$2),b.$3&&cancelAnimationFrame(b.$3),b.$7=a,b.$8()};this.cancel=function(){b.$2&&clearTimeout(b.$2),b.$3&&cancelAnimationFrame(b.$3),b.$2=0,b.$3=0,b.ts=0,b.$4=0,b.$5=0,b.$6=0,b.$7=void 0};this.$1=a}var b=a.prototype;b.onOrBeforeRepaint=function(a){if(this.$3)return;var b=Date.now(),c=this.$6;if(c!==0&&b<c)return;c=this.$5;(c===0||b<c)&&(this.$5=b);this.$2&&(clearTimeout(this.$2),this.$2=0);(!this.ts||this.ts>b)&&(this.ts=b);this.$7=a;this.$3=requestAnimationFrame(this.$8)};b.onOrAfter=function(a,b){h(a);var c=Date.now();c=c+a;var d=this.$6;if(d!==0&&c<d)return;d=this.$5;if(d!==0&&d<c)return;this.$6=c;(!this.$2||this.ts<c)&&this.$9(c,a,b)};b.debounce=function(a,b){h(a),this.$10(Date.now(),a,b)};b.debounceAndCap=function(a,b,c){h(a);h(b);var d=Date.now();b=d+b;var e=this.$6,f=this.$5;(e===0||e<=b)&&(f===0||b<f)&&(this.$5=b);this.$10(d,a,c)};b.forceRunNowIfScheduled=function(){this.$2?(clearTimeout(this.$2),this.$8()):this.$3&&(cancelAnimationFrame(this.$3),this.$8())};b.isScheduled=function(){return this.$2!==0||this.$3!==0};b.$10=function(a,b,c){var d=a+b,e=this.$6;if(e!==0&&d<e)return;e=this.ts;var f=this.$5;f!==0&&f<d?e<f&&this.$9(f,f-a,c):e<d&&this.$9(d,b,c)};b.$9=function(a,b,c){this.$3&&(cancelAnimationFrame(this.$3),this.$3=0);this.$7=c;this.ts=a;if(this.$2){c=a-this.$4;if(-16<c&&c<16)return;clearTimeout(this.$2)}this.$2=setTimeout(this.$8,b);this.$4=a};return a}();function h(a){if(!(a>=0))throw c("err")("ShiftTimer must be given delayMs >= 0")}g.ShiftTimer=a}),98);
__d("WABridgeBatcher",["WAShiftTimer"],(function(a,b,c,d,e,f,g){"use strict";var h=20,i=1e3;a=function(){function a(a,b,c){var e=this;this.$1=null;this.$6=Date.now();this.$7=new(d("WAShiftTimer").ShiftTimer)(function(){var a=e.$1;if(a==null)return;e.$1=null;e.$2(a)});this.$2=b;this.$3=a;this.$5=c!=null?c:h;this.$4=this.$5}var b=a.prototype;b.addEvent=function(a){var b=this.$1;this.$1=b?this.$3(b,a):[a];if(this.$7.isScheduled())return;b=this.$4;if(b<1){a=Date.now();var c=Math.max(a-this.$6,0)/i;b=Math.min(b+c,this.$5);this.$4=b;this.$6=a}b>=1?(c=0,this.$4=b-1):(c=Math.ceil(i*(1-b)),this.$4=0);this.$7.onOrBefore(c)};b.cancel=function(){this.$1=null,this.$7.cancel()};return a}();g.BridgeBatcher=a}),98);
__d("WALruCache",["WALogger","WAShiftTimer","err"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=babelHelpers.taggedTemplateLiteralLoose(["Blob size is larger than the limit of the whole store."]);h=function(){return a};return a}var i=function(a,b){this.key=a,this.value=b,this.prev=null,this.next=null};a=function(){function a(a){var b=this,e;this.$2=new Map();this.purgeNow=function(){for(var a=b.$6.prev;b.$4>b.$3&&a!==b.$5;a=a?a.prev:null){if(a==null)throw c("err")("The linked list is not constructed properly.");var d=a,e=d.key;d=d.value;if(!b.$9(e,d))continue;b["delete"](e);b.$10&&b.$10(e,d)}b.$8&&b.$8(b.$2)};this.$3=a.sizeLimit;this.$7=a.getSize;this.$8=a.onPurge;this.$9=(e=a.shouldEvict)!=null?e:function(){return!0};this.$10=a.onEvict;this.$11=a.onAdd;this.$12=a.onDelete;this.$4=0;this.$5=new i("placeholder-head",null);this.$6=new i("placeholder-tail",null);this.$5.next=this.$6;this.$6.prev=this.$5;this.$1=new(d("WAShiftTimer").ShiftTimer)(function(){return b.purgeNow()})}var b=a.prototype;b.get=function(a){if(!this.$2.has(a))return null;a=this.$2.get(a);if(a==null)return null;var b=a.prev,d=a.next;b&&(b.next=d);d&&(d.prev=b);d=this.$5.next;if(d==null)throw c("err")("The linked list is not constructed properly.");this.$5.next=a;d.prev=a;a.prev=this.$5;a.next=d;return a.value};b.has=function(a){return this.$2.has(a)};b.touch=function(a){this.get(a)};b.put=function(a,b){if(this.$3===0)return;if(this.$7(b)>this.$3){d("WALogger").WARN(h());return}this["delete"](a);this.$14(a,b);this.schedulePurge()};b.$14=function(a,b){var d=new i(a,b);this.$2.set(a,d);var e=this.$5.next;if(e==null)throw c("err")("The linked list is not constructed properly.");this.$5.next=d;e.prev=d;d.prev=this.$5;d.next=e;this.$4+=this.$7(b);this.$11&&this.$11(a,b)};b["delete"]=function(a){if(!this.$2.has(a))return;var b=this.$2.get(a);if(b==null)return;this.$4-=this.$7(b.value);var c=b.prev,d=b.next;c&&(c.next=d);d&&(d.prev=c);this.$2["delete"](a);this.$12&&this.$12(a,b.value)};b.clear=function(){this.$2.clear(),this.$4=0,this.$5.next=this.$6,this.$6.prev=this.$5,this.$13&&this.$13()};b.schedulePurge=function(){this.$1.onOrBefore(1e3)};b.getCurrentSize=function(){return this.$4};b.getPlaceholderHead=function(){return this.$5};b.forEach=function(a){if(this.$4===0)return;var b=this.$6.prev;if(b==null)throw c("err")("The linked list is not constructed properly.");while(b!=null&&b!==this.$5)a(b.value),b=b.prev};return a}();g.LruCache=a}),98);
__d("WACrossWorkerPortal",["Promise","WABridgeBatcher","WALogger","WALruCache","WAPromiseManagement","err"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["absorbPortal: transferring request #",""]);i=function(){return a};return a}function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["Failed to execute onAck: ",""]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["Bridge unexpected result to cast: ",""]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["Bridge unrecognized result ",""]);l=function(){return a};return a}function m(){var a=babelHelpers.taggedTemplateLiteralLoose(["Bridge "," response of ",""]);m=function(){return a};return a}function n(){var a=babelHelpers.taggedTemplateLiteralLoose(["Bridge "," not intended for client ",""]);n=function(){return a};return a}function o(){var a=babelHelpers.taggedTemplateLiteralLoose(["Bridge "," request for ",""]);o=function(){return a};return a}function p(){var a=babelHelpers.taggedTemplateLiteralLoose(["Bridge queuing "," msgs"]);p=function(){return a};return a}function q(){var a=babelHelpers.taggedTemplateLiteralLoose(["Bridge sending "," across (delayed)"]);q=function(){return a};return a}function r(){var a=babelHelpers.taggedTemplateLiteralLoose(["Bridge received msg on old port"]);r=function(){return a};return a}function s(){var a=babelHelpers.taggedTemplateLiteralLoose(["Bridge updating port"]);s=function(){return a};return a}var t=function(){function a(a,b,c,e,f){var g=this;this.port=null;this.$2=1;this.openRequests=new Map();this.listeners=new Set();if((f==null?void 0:f.useLRUCache)===!0){var h,i=new(d("WALruCache").LruCache)({sizeLimit:(h=f.portalCacheSize)!=null?h:100,getSize:function(){return 1}});this.processedRequests={has:function(a){return i.has(a)},add:function(a){i.put(a,!0)}}}else{var j=new Set();this.processedRequests={has:function(a){return j.has(a)},add:function(a){j.add(a)}}}this.$1=a;this.clientId=e;this.config=f;this.sendStream=new(d("WABridgeBatcher").BridgeBatcher)(b,function(a){g.$3(a)},(h=this.config)==null?void 0:h.bridgeBatcherMaxTokens);c.forEach(function(b){a.setNamespaceHandler(b,g.$4(b))})}var e=a.prototype;e.$5=function(){var a=this.clientId!=null?this.clientId+"_":"";return""+a+this.$2++};e.$6=function(a){a=a.split("_");return a.length!==2?null:a[0]};e.addListener=function(a){var b=this;this.listeners.add(a);return function(){return b.listeners["delete"](a)}};e.setPort=function(a){var b=this;d("WALogger").LOG(s());this.port=a;this.sendStream.cancel();a.onmessage=function(c){if(b.port!==a){d("WALogger").WARN(r()).devConsole(c.data);return}c=c.data;if(!c||!Array.isArray(c))return;c.forEach(function(c){b.$7(c,"incoming"),b.$8(a,c)})};this.openRequests.forEach(function(a){a=a.request;d("WALogger").DEV(q(),a.name);b.sendStream.addEvent({type:"request",content:a})})};e.getBridge=function(){return this.$1};e.$4=function(a){var b=this;return function(c,d,e,f,g,h){b.$9(a,c,d,e,f,g,h)}};e.$9=function(a,b,c,d,e,f,g){var h,i=this.$5();h=babelHelpers["extends"]({fromClientId:(h=this.clientId)!=null?h:void 0},g);g={requestId:i,expectsResponse:!!d,namespace:a,name:b,arg:c,silentLog:e,opts:h};this.openRequests.set(i,{request:g,resolver:d,eventCallbacks:f});this.sendStream.addEvent({type:"request",content:g})};e.$10=function(a,b){if(a!==this.port)return;this.sendStream.addEvent({type:"result",content:b})};e.$3=function(a){var b=this,c=this.port;if(!c){d("WALogger").DEV(p(),a.length);return}c.postMessage(a);var e=a.map(function(a){var b;b=(b=(b=a.content.arg)==null?void 0:b.type)!=null?b:"";return""+((a=a.content.name)!=null?a:"")+(b?":"+b:"")}).filter(Boolean);a.forEach(function(c){var d;b.$7(c,"outgoing");c=b.openRequests.get(c.content.requestId);(c==null?void 0:(d=c.eventCallbacks)==null?void 0:d.onSend)!=null&&c.eventCallbacks.onSend({queueSize:a.length,queueMsgs:e})})};e.$7=function(a,b){this.listeners.forEach(function(c){return c(a,b)})};e.$8=function(a,e){var f=this;if(e.type==="request"){var g=e.content,i=g.namespace,p=g.name,q=g.requestId,r=g.arg,s=g.expectsResponse,t=g.silentLog,u=g.opts;t||d("WALogger").LOG(o(),q,p);if((u==null?void 0:u.toClientId)!=null&&u.toClientId!==this.clientId){t||d("WALogger").LOG(n(),q,this.clientId);return}g=this.$1;var v={type:"result",content:{requestId:q,type:"handled",result:{payload:g.getAckPayoad(),timestamp:Date.now()},silentLog:t,opts:u}};this.$7(v,"outgoing");a.postMessage([v]);if(s){v=g.sendAndReceive(i,p,r,t,void 0,u).then(function(b){f.$10(a,{requestId:q,type:"success",result:b,silentLog:t,opts:u})},function(b){f.$10(a,{requestId:q,type:"error",result:String(b),silentLog:t,opts:u})});d("WAPromiseManagement").preventGarbageCollection(v)}else g.fireAndForget(i,p,r,t,void 0,u)}else{e.type;s=e.content;v=s.requestId;g=s.type;i=s.result;p=s.silentLog;p||d("WALogger").DEV(m(),v,g);r=this.$6(v);if(r!==this.clientId||this.processedRequests.has(v))return;e=this.openRequests.get(v);if(!e){d("WALogger").WARN(l(),v);return}switch(g){case"success":case"error":this.openRequests["delete"](v);this.processedRequests.add(v);s=e.resolver;s?s(g==="success"?i:(h||(h=b("Promise"))).reject(c("err")(i!=null?i:""))):d("WALogger").ERROR(k(),i);break;case"handled":try{(p=e.eventCallbacks)==null?void 0:p.onAck==null?void 0:p.onAck(i)}catch(a){d("WALogger").ERROR(j(),a.message)}e.resolver||(this.openRequests["delete"](v),this.processedRequests.add(v));break}}};return a}();function a(a,b,c,d,e){return new t(a,c,b,d,e)}function e(a){var b;(b=a.port)==null?void 0:b.close();a.port=null;a.sendStream.cancel();b=a.openRequests;a.openRequests=new Map();var c=a.getBridge();b.forEach(function(a){var b=a.request;a=a.resolver;var e=b.namespace,f=b.name,g=b.arg,h=b.silentLog;d("WALogger").LOG(i(),b.requestId);a?a(c.sendAndReceive(e,f,g,h)):c.fireAndForget(e,f,g,h)})}g.CrossWorkerPortal=t;g.attachPortal=a;g.killPortalAndSendPendingToBridge=e}),98);
__d("MAWCrossWorkerPortal",["FBLogger","WACrossWorkerPortal","pageID"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b,e,f){if(h!=null)return h;h=d("WACrossWorkerPortal").attachPortal(a,b,e,c("pageID"),f);return h}function b(){if(h==null){c("FBLogger")("messenger_web").warn("Trying to kill portal before its setup");return}d("WACrossWorkerPortal").killPortalAndSendPendingToBridge(h)}g.getOrCreateCrossWorkerPortal=a;g.killPortalWhenWorkerTerminating=b}),98);
__d("MAWUnrecoverableDbErrors",[],(function(a,b,c,d,e,f){"use strict";var g=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}return a}(babelHelpers.wrapNativeSuper(Error));e=function(a){babelHelpers.inheritsLoose(b,a);function b(){var b,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(b=c=a.call.apply(a,[this].concat(e))||this,c.name="indexed_db_access_error",b)||babelHelpers.assertThisInitialized(c)}return b}(g);var h=function(b){babelHelpers.inheritsLoose(a,b);function a(){var a,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(a=c=b.call.apply(b,[this].concat(e))||this,c.name="ear_init_error",a)||babelHelpers.assertThisInitialized(c)}return a}(g),i=function(b){babelHelpers.inheritsLoose(a,b);function a(){var a,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(a=c=b.call.apply(b,[this].concat(e))||this,c.name="ear_runtime_error",a)||babelHelpers.assertThisInitialized(c)}return a}(g),j=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,d,e){a=b.call(this,a)||this;a.name="ebsm_hydration_error";a.fullDeletion=c;a.qplInstanceKey=d;a.tableName=e;return a}return a}(g),k=function(b){babelHelpers.inheritsLoose(a,b);function a(){var a,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(a=c=b.call.apply(b,[this].concat(e))||this,c.name="wa_exceeded_storage_quota_error",a)||babelHelpers.assertThisInitialized(c)}return a}(g);function a(a){return a==null?!1:a instanceof g||l(a.name)}function l(a){return a==="indexed_db_access_error"||a==="ear_init_error"||a==="ear_runtime_error"||a==="ebsm_hydration_error"||a==="wa_exceeded_storage_quota_error"}var m=null,n=new Set();function b(){return m}function c(a,b){m=a,b==null?void 0:b(),n.forEach(function(b){return b(a)})}function d(a){n.add(a);return function(){return n["delete"](a)}}f.UnrecoverableDbError=g;f.IndexedDbAccessError=e;f.EarInitError=h;f.EarRuntimeError=i;f.EbsmHydrationError=j;f.WAExceededStorageQuotaError=k;f.isUnrecoverableDbError=a;f.isUnrecoverableDbErrorByName=l;f.getError=b;f.setError=c;f.subscribe=d}),66);
__d("MAWWorkerOnClose",["MAWSetupWorkerAuxStateForLogging","MAWWaitForBackendSetup","MAWWebWorkerSingleton","QPLUserFlow","pageID","promiseDone","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h=c("qpl")._(1056841865,"2862");function a(a,b,e){c("QPLUserFlow").start(h,{annotations:{string:{currentWorkerId:d("MAWWaitForBackendSetup").getCurrentWorkerID(),pageID:c("pageID"),terminationMsgType:b,terminationReason:a,workerID:e},string_array:{pastWorkerTerminationMsgTypes:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartMessageTypes,pastWorkerTerminationReasons:d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartReasons}}})}function b(a){c("promiseDone")(d("MAWWebWorkerSingleton").getWorkerHealthStatus(),function(b){return c("QPLUserFlow").endSuccess(h,{annotations:{bool:babelHelpers["extends"]({},b.data),string:{healthStatusAfter:b.tag,outcome:a}}})})}function e(a,b){c("promiseDone")(d("MAWWebWorkerSingleton").getWorkerHealthStatus(),function(d){return c("QPLUserFlow").endFailure(h,a,{annotations:{bool:babelHelpers["extends"]({},d.data),string:{healthStatusAfter:d.tag}},error:b})})}g.ON_WORKER_CLOSED_HANDLER_QPL=h;g.startQPL=a;g.success=b;g.fail=e}),98);
__d("MAWWorkerWatchdog",["MAWBridgeLoggingUtils","MAWBridgeSendAndReceive","MAWSetupWorkerAuxStateForLogging","MAWWaitForBackendSetup","MAWWebWorkerSingleton","MAWWorkerWatchdogRecovery","Promise","QPLUserFlow","clearTimeout","err","gkx","justknobx","promiseDone","qpl","setInterval","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("gkx")("12962"),j=c("justknobx")._("4240"),k=c("gkx")("15053"),l=j/2,m=c("qpl")._(**********,"2909"),n=!1,o=!1,p=null,q=["shared_not_exists","shared_exists_not_connected"];function r(){var a;c("QPLUserFlow").start(m,{annotations:{bool:{workerTerminatedPermanently:(a=d("MAWSetupWorkerAuxStateForLogging")).WorkerLifeCycleState.workerTerminatedPermanently},"int":{workerAge:a.WorkerLifeCycleState.getWorkerAge()},string:{currentWorkerId:d("MAWWaitForBackendSetup").getCurrentWorkerID()},string_array:{pastWorkerTerminationMsgTypes:a.WorkerLifeCycleState.restartMessageTypes,pastWorkerTerminationReasons:a.WorkerLifeCycleState.restartReasons}}});return(h||(h=b("Promise"))).race([d("MAWBridgeSendAndReceive").sendAndReceive("backend",d("MAWBridgeLoggingUtils").HEARTBEAT_ROUTE),new h(function(a,b){c("setTimeout")(function(){b(c("err")("Armadillo web worker heartbeat timed out."))},l)})]).then(function(){p!=null&&(c("clearTimeout")(p),p=null,c("QPLUserFlow").addPoint(m,"clear-lock-based-recovery")),c("QPLUserFlow").endSuccess(m),d("MAWSetupWorkerAuxStateForLogging").addWorkerHeartbeatToHistory("OK")},function(a){c("gkx")("13566")&&(k?s():p==null&&(p=c("setTimeout")(function(){s(),p=null},j*2))),d("MAWSetupWorkerAuxStateForLogging").addWorkerHeartbeatToHistory("ERROR"),c("promiseDone")(d("MAWWebWorkerSingleton").getWorkerHealthStatus(),function(b){return c("QPLUserFlow").endFailure(m,"heartbeat_timeout",{annotations:{bool:babelHelpers["extends"]({},b.data),string:{workerHealthStatus:b.tag}},error:a})})})}function s(){c("QPLUserFlow").addPoint(m,"starting-lock-based-recovery"),c("promiseDone")(d("MAWWebWorkerSingleton").getWorkerHealthStatus().then(function(a){var b=d("MAWWorkerWatchdogRecovery").getWorkerRecoveryForWatchdog(),c=d("MAWWaitForBackendSetup").getCurrentWorkerID();if(q.includes(a.tag)&&b!=null)return b("locks_based_recovery",c,"locks_based_recovery")}))}function a(){if(o||!i)return;c("setInterval")(function(){if(!n)return;c("promiseDone")(r())},j);d("MAWWaitForBackendSetup").runAfterBackendSetup(function(){n=!0},d("MAWBridgeLoggingUtils").HEARTBEAT_ROUTE);o=!0}g.HEARTBEAT_TIMEOUT_INTERVAL=l;g.MAW_WORKER_HEARTBEAT_QPL=m;g.startWatchdog=a}),98);
__d("MAWSetupWorker",["CurrentAppID","Deferred","IGDWebUtils","MAWBridgeObserver","MAWClientConsistencyRefresh","MAWCommonSetupWorker","MAWCrossWorkerPortal","MAWInit","MAWLoggerUtils","MAWMIC","MAWSetupWorkerAuxStateForLogging","MAWUnrecoverableDbErrors","MAWWaitForBackendSetup","MAWWebWorkerSingleton","MAWWorkerOnClose","MAWWorkerWatchdog","MWFBLogger","QPLUserFlow","SharedWorkerBundleResource","SharedWorkerV2ResourceExperimental","asyncToGeneratorRuntime","cr:6600","gkx","promiseDone","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=babelHelpers.taggedTemplateLiteralLoose(["Shared worker responded to termination request: ",""]);h=function(){return a};return a}function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["worker terminating: for ",""]);i=function(){return a};return a}function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["worker doesn't exist, no need to terminate"]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["backend setup successful"]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["bridge promise resolved"]);l=function(){return a};return a}function m(){var a=babelHelpers.taggedTemplateLiteralLoose(["worker setting up with reason: ",", use shared worker: ",""]);m=function(){return a};return a}var n=function(){return c("gkx")("8512")},o=d("MWFBLogger").MWLogger.tags([d("MAWLoggerUtils").Tag.WorkerSetup,d("MAWLoggerUtils").Tag.MAWInit]),p=d("IGDWebUtils").isInstagramWebSupportedApp(Number(d("CurrentAppID").getAppID())),q=p?"IGDAWMainWorker":"MAWMainWorker",r=null,s=!1;function t(a){s=a,d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.workerTerminatedPermanently=a}function u(){return s}function v(a){switch(a){case"mawInit":return!0;case"mawReinitSetupMutex":case"mawReinitReregDevice":case"bridgeRecovery":case"workerUpgraded":case"rebootRequired":case"workerTerminated":default:return!1}}function w(a,b,c,d,e,f){return x.apply(this,arguments)}function x(){x=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,f,g,h,i){if(u())throw o.mustfixThrow("Worker is permanently killed.");d("MAWWaitForBackendSetup").markBackendSetupStarted();var j=function(b){return w(a,e,f,g,b,i)};(h==="bridgeRecovery"||h==="backendSetupFailure")&&c("QPLUserFlow").addPoint(d("MAWBridgeObserver").BAD_STATE_RECOVERY_QPL,"create_worker_start");d("MAWWebWorkerSingleton").setOnCloseForWorkerInstance(function(a,b,e){d("MAWWorkerOnClose").startQPL(a,e,b);var f=a!=null?a:"unknown",g=G(f)&&d("MAWWaitForBackendSetup").isBackendSetupSettled();d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartReasons.push(f);d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartMessageTypes.push(e!=null?e:"unknown");if(d("MAWWaitForBackendSetup").getCurrentWorkerID()==null||b!==d("MAWWaitForBackendSetup").getCurrentWorkerID()){d("MAWInit").MAWInit.logPoint(f+"_worker_termination_skipped");d("MAWMIC").addPoint(f+"_worker_termination_skipped");d("MAWWorkerOnClose").success(f+"_worker_restart_skipped");o.warn("Worker termination skipped %s. Terminate reason: %s, Worker: %s, current worker: %s",h,a,b,d("MAWWaitForBackendSetup").getCurrentWorkerID());return}d("MAWWaitForBackendSetup").resetCurrentWorkerSkipIdCheck();o.warn("Worker termination started %s. Terminate reason: %s, Worker: %s",h,a,b);d("MAWInit").MAWInit.logPoint(f+"_worker_terminated");d("MAWMIC").addPoint(f+"_worker_terminated");d("MAWInit").MAWInit.addStringArrayAnnotation("workerTerminationReason",d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartReasons);d("MAWMIC").addStringArrayAnnotation("workerTerminationReason",d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartReasons);d("MAWInit").MAWInit.addStringArrayAnnotation("workerTerminationMsgType",d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartMessageTypes);d("MAWMIC").addStringArrayAnnotation("workerTerminationMsgType",d("MAWSetupWorkerAuxStateForLogging").WorkerLifeCycleState.restartMessageTypes);var i=a==="requested-upgrade"?"workerUpgraded":f+"_worker_terminated";a==="requested-upgrade"||n()?c("promiseDone")(E(),function(){if(g){t(!0);o.warn("Tab displays soft refresh. Flow: %s",f);d("MAWInit").MAWInit.logPoint(f+"_soft_refresh");d("MAWMIC").addPoint(f+"_soft_refresh");c("MAWClientConsistencyRefresh")("maw_request_refresh");d("MAWWorkerOnClose").success("softRefresh");return}if(f!=="logout")return j(i).then(function(){return d("MAWWorkerOnClose").success(i)})["catch"](function(a){d("MAWInit").MAWInit.fail("worker_restart_failed",a),d("MAWWorkerOnClose").fail("workerUpgradeFailed",a),d("MAWWaitForBackendSetup").rejectBackendSetup(a)});else d("MAWWorkerOnClose").success("skip_logout")}):d("MAWWorkerOnClose").success("skip_workerLifeCycleCrossTabsDisabledGK")});var p=(yield d("MAWWebWorkerSingleton").createWorkerIfNone(h)),s=p.worker;p=p.workerID;d("MAWWaitForBackendSetup").setCurrentWorker(p);p=new(c("Deferred"))();r=p.getPromise();(h==="bridgeRecovery"||h==="backendSetupFailure")&&c("QPLUserFlow").addPoint(d("MAWBridgeObserver").BAD_STATE_RECOVERY_QPL,"create_worker_end");var v=d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker();o.DEBUG(m(),h,v);v&&s.addEventListener("message",function(a){var b;typeof a.data==="object"&&((b=a.data)==null?void 0:b.type)==="sw-shutdown"&&typeof a.data.response==="object"&&((b=a.data.response)==null?void 0:b.reason)==="maw-request-refresh"&&c("MAWClientConsistencyRefresh")("maw_request_refresh")});var x=new(d("MAWCommonSetupWorker").BridgeAdapter)(s,q);yield e(x);p.resolve(x);o.DEBUG(l());d("MAWMIC").addPoint(h+"_connect_to_backend");yield d("MAWInit").MAWInit.measurePerformance(h+"-init_backend",b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var b=function(){return d("MAWCommonSetupWorker").initWorker({bridge:x,onWorkerAlreadySetup:f,onWorkerRestart:j,reason:h,vaultMaterials:a})};b=v?yield b():yield x.fullyConnected.then(b);d("MAWWaitForBackendSetup").resolveBackendSetup(b);o.DEBUG(k())}));return d("MAWWaitForBackendSetup").waitForBackendSetup("setup-worker")});return x.apply(this,arguments)}function a(a,b,e,f,g,h){var i=w(a,b,e,f,g,h);v(g)&&c("promiseDone")(d("MAWWaitForBackendSetup").waitForBackendSetup("init bridge recovery observer").then(function(){d("MAWBridgeObserver").initUnackedMessagesObserver({onConnectToExistingWorker:e,onTerminateWorker:f,onWorkerConnect:b,updateWorkerState:h,vaultMaterials:a}),d("MAWWorkerWatchdog").startWatchdog()}));return i}function e(){return r}function y(a){return z.apply(this,arguments)}function z(){z=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){o.debug("Terminating dedicated worker: %s. Bridge Promise: %s",a,!!r);if(!r){o.DEBUG(j());return!1}o.DEBUG(i(),a);yield E();return!0});return z.apply(this,arguments)}function f(){return A.apply(this,arguments)}function A(){A=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){yield y("terminateWorkerPermanently"),t(!0)});return A.apply(this,arguments)}function B(a,e){var f=c("gkx")("7313");return f?d("SharedWorkerV2ResourceExperimental").terminateSharedWorker(b("cr:6600"),a?"maw-request-refresh":e!=null?e:"unknown"):d("SharedWorkerBundleResource").terminateSharedWorker(b("cr:6600"),null,a?"maw-request-refresh":e!=null?e:"unknown")}function C(a){return D.apply(this,arguments)}function D(){D=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker();if(b){o.debug("Terminating shared worker: %s",a);b=(yield B(!1,a).then(function(){return!0},function(){return!1}));o.DEBUG(h(),b);n()||(yield E())}else yield y(a)});return D.apply(this,arguments)}function E(){return F.apply(this,arguments)}function F(){F=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a;o.debug("Updating worker state on termination");(a=(yield r))==null?void 0:a.close();r=null;d("MAWWaitForBackendSetup").resetBackendSetup();d("MAWCrossWorkerPortal").killPortalWhenWorkerTerminating();d("MAWWebWorkerSingleton").resetWorkerCreation()});return F.apply(this,arguments)}function G(a){if(a==="MAWEARKeychainDecryptionError"||d("MAWUnrecoverableDbErrors").isUnrecoverableDbErrorByName(a))return!0;return a==="update-required-fbid_change"||a==="update-required-vault_materials_change"?!0:!1}g.setPreventWorkerRestart=t;g.getOrSetupWorker=a;g.waitForWorkerSetup=e;g.terminateDedicatedWorker=y;g.terminateWorkerPermanently=f;g.killSharedWorker=B;g.terminateWorker=C}),98);
__d("MAWWorkerReboot",["FBLogger","MAWBridgeObserver","MAWSetupWorker","MAWWaitForBackendSetup","ODS","Promise","QPLUserFlow","pageID","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a){(i||(i=d("ODS"))).bumpEntityKey(3185,"armadillo_shared_worker_reboot","shared_worker_reboot_"+a)}var k=d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker(),l=function(a){switch(a){case"bridge_recovery":return"bridgeRecovery";case"backend_setup_failure":return"backendSetupFailure"}};function a(a,e,f,g,i,m){c("QPLUserFlow").start(d("MAWBridgeObserver").BAD_STATE_RECOVERY_QPL,{annotations:{bool:{isSharedWorker:k},string:{trigger:i,workerInitPageID:c("pageID")}}});c("FBLogger")("messenger_web").info("Rebooting worker started.");j("start");d("MAWWaitForBackendSetup").resetBackendSetup();var n=l(i);c("QPLUserFlow").addPoint(d("MAWBridgeObserver").BAD_STATE_RECOVERY_QPL,"terminate_worker_start");return new(h||(h=b("Promise")))(function(b,h){return d("MAWSetupWorker").terminateWorker(n).then(function(){c("QPLUserFlow").addPoint(d("MAWBridgeObserver").BAD_STATE_RECOVERY_QPL,"terminate_worker_end");c("FBLogger")("messenger_web").info("Reboot process killed existing worker successfully.");return d("MAWSetupWorker").getOrSetupWorker(a,e,f,g,n,m).then(function(a){c("FBLogger")("messenger_web").info("Rebooting worker success. Reason for reboot: %s",n),j("success"),c("QPLUserFlow").endSuccess(d("MAWBridgeObserver").BAD_STATE_RECOVERY_QPL),e(a),b()})})["catch"](function(a){c("FBLogger")("messenger_web").info("Rebooting worker failed. Error message: %s",a.message),j("fail"),c("QPLUserFlow").endFailure(d("MAWBridgeObserver").BAD_STATE_RECOVERY_QPL,a.message),d("MAWWaitForBackendSetup").rejectBackendSetup(a),h(a)})})}g.rebootWorker=a}),98);
__d("MAWBridgeObserver",["FBLogger","MAWWaitForBackendSetup","MAWWorkerReboot","promiseDone","qex","qpl"],(function(a,b,c,d,e,f,g){"use strict";a=c("qpl")._(1056840252,"808");var h=5,i=[],j=!1,k,l=function(){i=[]};b=function(a){if(!o())return;if(d("MAWWaitForBackendSetup").isBackendSetupInProgress()){l();return}if(n().length>=h&&!d("MAWWaitForBackendSetup").isBackendSetupInProgress()){c("FBLogger")("messenger_web").info("Minimum threshold of unacked messages reached: %s. Unacked messages: %s",h,JSON.stringify(n()));var b=c("qex")._("440"),e=c("qex")._("468")||h;b===!0&&n().length>=e?(c("FBLogger")("messenger_web").info("QE threshold of unacked messages reached: %s. Unacked messages: %s",h,JSON.stringify(n())),l(),c("promiseDone")(m())):(c("FBLogger")("messenger_web").info("Bridge recovery attempt skipped. Reason for skipped: maw_bridge_recovery_v2_enabled: %s, maw_bridge_recovery_threshold: %s",b,e),i.push(a))}else i.push(a)};var m=function(){c("FBLogger")("messenger_web").info("Starting bridge recovery attempt.");return d("MAWWorkerReboot").rebootWorker(k.vaultMaterials,k.onWorkerConnect,k.onConnectToExistingWorker,k.onTerminateWorker,"bridge_recovery",k.updateWorkerState).then(function(){c("FBLogger")("messenger_web").info("Bridge recovery attempt successful. Worker has restarted.")},function(a){c("FBLogger")("messenger_web").info("Bridge recovery attempt failed. Worker has failed to restart. Error: %s",a.message);throw a})};e=function(a){j=!0,k=a};var n=function(){return i},o=function(){return j};f=function(){return k};g.BAD_STATE_RECOVERY_QPL=a;g.MIN_UNACKED_EVENTS_THRESHOLD=h;g.resetUnackedMessages=l;g.notifyUnackedEvent=b;g.attemptRecovery=m;g.initUnackedMessagesObserver=e;g.getUnackedMessages=n;g.getIsObserverActive=o;g.getProps=f}),98);
__d("MAWGetDBVersionForTest",["justknobx"],(function(a,b,c,d,e,f,g){"use strict";function a(){if(!c("justknobx")._("1495"))return;var a=new URL(document.location.href);a=a.searchParams.get("db_version");if(a==null)return;if(a==="current"||a==="latest")return a;a=parseInt(a,10);if(Number.isInteger(a))return Number(a)}g.getDbVersionForTest=a}),98);
__d("WADevToolMessageChannel",[],(function(a,b,c,d,e,f){"use strict";var g=new MessageChannel(),h;a=function(){g=new MessageChannel();h(g);return g};b=function(){return g};c=function(a){h=a,h(g)};f.createNewChannel=a;f.getMostRecentChannel=b;f.onCreateNewChannel=c}),66);
__d("MainPageUrl",["ExecutionEnvironment","FBLogger","SimpleHook"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new URL(function(){var a=(typeof self!=="undefined"?self:typeof window!=="undefined"?window:{location:{href:"http://undefined"}}).location.href;(h||(h=c("ExecutionEnvironment"))).isInWorker&&a&&a.startsWith("blob:")&&(a=a.substring(5,a.length));return a}()),j=new URL(i.href),k=new(d("SimpleHook").SimpleHook)();function l(){var a;return(h||(h=c("ExecutionEnvironment"))).isInBrowser&&(j==null?void 0:(a=j.searchParams)==null?void 0:a.get("workerlog"))==="debug"}function a(a){try{l()&&c("FBLogger")("worker").debug("received url "+a);var b=new URL(a,j.href||i.href);j.href!==b.href?(j.href=b.href,k.call(j)):l()&&c("FBLogger")("worker").debug("ignoring url: path didn't change in "+a)}catch(a){c("FBLogger")("worker").catching(a).warn("invalid url")}}typeof window==="object"&&typeof window.location==="object"&&typeof window.location.href==="string"&&a(window.location.href);g.mainPageUrl=j;g.onMainPageUrlChange=k;g.isWorkerLogEnabled=l;g.updateMainPageUrl=a}),98);
__d("TimedOnceFunc",["ExecutionEnvironment"],(function(a,b,c,d,e,f,g){var h;a=function(){function a(a,b){this.$2=!1,this.$4=a,this.$3=b,(h||(h=c("ExecutionEnvironment"))).isInBrowser&&this.$5()}var b=a.prototype;b.$6=function(){this.$1!=null&&clearTimeout(this.$1),this.$1=null};b.$5=function(){var a=this;this.isDone()||(this.$6(),this.$1=setTimeout(function(){a.$2=!0,a.run()},this.$3))};b.isDone=function(){return this.$4===null};b.isCancelled=function(){return this.$1===null&&this.$4!==null};b.run=function(){this.$6();if(this.$4!=null){var a=this.$4;this.$4=null;a(this.$2)}};b.getDelay=function(){return this.$3};b.delay=function(a){this.$3=a!=null?a:this.$3,this.$5()};b.cancel=function(){this.$6()};return a}();g.TimedOnceFunc=a}),98);
__d("VirtualMessageChannel",["invariant","Promise","SimpleHook","nullthrows","promiseDone"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=function(){function a(a){a===void 0&&(a=!1),this.$2=new(d("SimpleHook").SimpleHook)(),this.onmessage=null,this.onmessageerror=null,this.$3=a}var e=a.prototype;e.setRemotePort=function(a){this.$1=a};e.addEventListener=function(a,b,c){(a==="message"||a==="error")&&c==null||h(0,55320,a);if(a==="message")if(typeof b==="function")this.$2.add(b);else{c=b.handleEvent.bind(b);this.$2.add(c);b.__handler=c}};e.removeEventListener=function(a,b,c){if(typeof b==="function")this.$2.remove(b);else{a=b.__handler;this.$2.remove(a)}};e.postMessage=function(a,d){var e=this,f=function(){var b=c("nullthrows")(e.$1,"By now remote port must have value!");b.$2.call({data:a,ports:d})};this.$3?f():c("promiseDone")((i||(i=b("Promise"))).resolve(),f)};e.start=function(){};return a}();a=function(a,b){this.port1=new j(a),this.port2=new j(b),this.port1.setRemotePort(this.port2),this.port2.setRemotePort(this.port1)};g.VirtualMessagePort=j;g.VirtualMessageChannel=a}),98);
__d("NestedTimeRange",["SimpleHook","performance","performanceAbsoluteNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=new(d("SimpleHook").SimpleHook)(),k=new(d("SimpleHook").SimpleHook)(),l="/";function m(a,b){return""+a+l+b}b=function(){function a(a,b){b===void 0&&(b=null),this.$3=0,this.$1=a,this.$2=b}var b=a.prototype;b.isRunning=function(){return this.$3>0};b.fullname=function(){return this.$2!==null?m(this.$2.fullname(),this.$1):this.$1};b.start=function(a){a===void 0&&(a=(h||(h=c("performanceAbsoluteNow")))());if(this.$3===0){var b;(b=this.$2)==null?void 0:b.start(a);this.__onStart(a)}++this.$3;return a};b.__onStart=function(a){j.call(this,a)};b.stop=function(a){a===void 0&&(a=(h||(h=c("performanceAbsoluteNow")))());--this.$3;if(this.$3===0){var b;this.__onStop(a);(b=this.$2)==null?void 0:b.stop(a)}return a};b.__onStop=function(a){k.call(this,a)};return a}();function a(){(i||(i=c("performance"))).mark&&(i||(i=c("performance"))).measure&&(j.add(function(a,b){(i||(i=c("performance"))).mark(a.fullname()+"_start")}),k.add(function(a,b){(i||(i=c("performance"))).measure(a.fullname(),a.fullname()+"_start")}))}g.OnRangeStart=j;g.OnRangeStop=k;g.TIME_RANGE_LEVEL_SEPARATOR=l;g.rangeFullName=m;g.NestedTimeRange=b;g.enableDevConsoleTimeline=a}),98);
__d("QLogEvent",["DoubleKeyMap","FBLogger","NestedTimeRange","QPLEvent","SimpleHook","performanceNavigationStart"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new(d("DoubleKeyMap").DoubleKeyMap)(),j=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,d){a=b.call(this,a,d)||this;a.$QPLRange$p_1=c;i.set(c,a.fullname(),babelHelpers.assertThisInitialized(a));return a}var e=a.prototype;e.__onStart=function(a){b.prototype.__onStart.call(this,a),this.$QPLRange$p_1.point(this.fullname()+"_START",{timestamp:a})};e.__onStop=function(a){b.prototype.__onStop.call(this,a),this.$QPLRange$p_1.point(this.fullname()+"_END",{timestamp:a}),i["delete"](this.$QPLRange$p_1,this.fullname())};e.range=function(b){this.fullname().startsWith(d("NestedTimeRange").TIME_RANGE_LEVEL_SEPARATOR)||c("FBLogger")("qpl").warn("Range %s cannot have subrange since root name does not start with /",this.fullname());var e=d("NestedTimeRange").rangeFullName(this.fullname(),b);return(e=i.get(this.$QPLRange$p_1,e))!=null?e:new a(b,this.$QPLRange$p_1,this)};return a}(d("NestedTimeRange").NestedTimeRange),k=new(d("DoubleKeyMap").DoubleKeyMap)(),l=new((b=d("SimpleHook")).SimpleHook)(),m=new b.SimpleHook(),n=new b.SimpleHook(),o=new b.SimpleHook(),p=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,e,f,g){var i;c===void 0&&(c=0);e===void 0&&(e=2);f===void 0&&(f=!1);var j=(h||(h=d("QPLEvent"))).getMarkerId(a);i=b.call(this,"event_"+j)||this;i.event=a;i.instanceKey=c;i.$QPLEvent$p_1=e;i.isUserFlow=f;i.$QPLEvent$p_2=g;k.set(j,c,babelHelpers.assertThisInitialized(i));return i}var e=a.prototype;e.action=function(a){this.$QPLEvent$p_1=a;return this};e.getAction=function(){return this.$QPLEvent$p_1};e.setIsUserFlow=function(a){this.isUserFlow=a;return this};e.startFromNavStart=function(){this.start(c("performanceNavigationStart")());return this};e.startFromTime=function(a){this.start(a);return this};e.__onStart=function(a){var b;(b=this.$QPLEvent$p_2)==null?void 0:b.onStart(this,a);l.call(this,a)};e.__onStop=function(a){var b;(b=this.$QPLEvent$p_2)==null?void 0:b.onStop(this,a);m.call(this,a);i.deleteAll(this);k["delete"]((h||(h=d("QPLEvent"))).getMarkerId(this.event),this.instanceKey)};e.range=function(a){var b;return(b=i.get(this,a))!=null?b:new j(a,this)};e.point=function(a,b){var c;b===void 0&&(b={});(c=this.$QPLEvent$p_2)==null?void 0:c.onPoint(this,a,b);n.call(this,a,b);return this};e.annotate=function(a){var b;(b=this.$QPLEvent$p_2)==null?void 0:b.onAnnotate(this,a);o.call(this,a);return this};return a}(d("NestedTimeRange").NestedTimeRange);function a(a,b,c){var e;b===void 0&&(b=0);return(e=k.get((h||(h=d("QPLEvent"))).getMarkerId(a),b))!=null?e:new p(a,b,2,!1,c)}g.OnEventStart=l;g.OnEventStop=m;g.OnEventPoint=n;g.OnEventAnnotate=o;g.QPLEvent=p;g.event=a}),98);
__d("WorkerMessagePortLogging",["MainPageUrl","QLogEvent","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h=Math.random()<.1,i=20,j=100,k=50;function a(){return d("MainPageUrl").isWorkerLogEnabled()||d("MainPageUrl").mainPageUrl.searchParams.get("worker_log")===1}var l=0;function m(){return d("QLogEvent").event(c("qpl")._(41497718,"106"),l++)}function b(a,b,c){c===void 0&&(c=!1);if(b==null||!h)return;var d=b.sendDelayHighPrecision,e=b.sendTimestamp,f=b.sendDateTime,g=b.receiveTimestamp,l=b.receiveDateTime,n=m();n.startFromTime();var o=n.range("SEND_DELAY");o.start(e);o.stop(g);o=n.range("SEND_DELAY_LOW_PRECISION");o.start(f);o.stop(l);o=Math.abs(e-f);e=Math.abs(g-l);n.annotate({string:{port_name:a},"int":{send_delay:d,send_drift:o,receive_drift:e},bool:{send_delay_above_max:d!=null&&d>i,send_drift_above_max:o>j,receive_drift_above_max:e>j}});if(c){f=n.range("ROUND_TRIP");f.start(b.sendTimestamp);f.stop(b.receiveTimestamp);g=b.receiveTimestamp-b.sendTimestamp;n.annotate({"int":{round_trip:g},bool:{round_trip_above_max:g>k}})}n.stop()}g.isWorkerLogEnabled=a;g.logMessageTiming=b}),98);
__d("WorkerMessagePort",["Deferred","FBLogger","MainPageUrl","Promise","PromiseAnnotate","SimpleHook","VirtualMessageChannel","WorkerMessagePortLogging","performanceAbsoluteNow","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(a){return a}var l=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}return a}(d("SimpleHook").SimpleHook);function m(a,b){a=k(a);a.__timing=b}function n(a){return a.__timing}a=function(){var a=e.prototype;a.onMessageHandler=function(a){try{this.onMessage.call(a);var b=this.$1[a.type];!b?this.onUnhandledMessage.call(a):b.call(a)}catch(a){this.onError.call(a)}};function e(a,b){var e=this;this.$1={};this.onUnhandledMessage=new l();this.onMessage=new l();this.onPostMessage=new l();this.onError=new l();this.$2=a;this.name=b;this.$2.addEventListener("message",function(a){return e.onMessageHandler(k(a.data))});this.$2.addEventListener("error",function(a){return e.onError.call(a)});this.onPostMessage.add(function(a){m(a,{receiveDateTime:-1,receiveTimestamp:-1,sendDateTime:Date.now(),sendDelayHighPrecision:0,sendDelayLowPrecision:0,sendTimestamp:(j||(j=c("performanceAbsoluteNow")))()})});this.onMessage.add(function(a){a=n(a);if(a!=null){var b=a.sendDateTime,f=a.sendTimestamp,g=(j||(j=c("performanceAbsoluteNow")))(),h=Date.now();a.receiveTimestamp=g;a.receiveDateTime=h;a.sendDelayHighPrecision=g-f;a.sendDelayLowPrecision=h-b;d("WorkerMessagePortLogging").logMessageTiming(e.name,a)}})}a.postMessage=function(a,b){this.onPostMessage.call(a),b?this.$2.postMessage(a,b):this.$2.postMessage(a)};a.addMessageListener=function(a,b){var c=this.$1[a];c||(c=new l(),this.$1[a]=c);return c.add(b)};a.removeMessageListener=function(a,b){a=this.$1[a];return!!a&&a.remove(b)};a.removeAllMessageListeners=function(a){a=this.$1[a];a&&a.clear()};a.onMessageOnce=function(a,c){var d=this;return new(i||(i=b("Promise")))(function(b){var e=d.addMessageListener(a,function(f){c(f)&&(d.removeMessageListener(a,e),b(f))})})};a.close=function(){if(this.$2 instanceof MessagePort)this.$2.close();else if(this.$2 instanceof Worker)this.$2.terminate();else if(this.$2 instanceof DedicatedWorkerGlobalScope)this.$2.close();else{var a;(a=(a=this.$2.close)!=null?a:this.$2.terminate)==null?void 0:a.call(this.$2)}};a.isWrappingVirtualMessagePort=function(){return this.$2 instanceof d("VirtualMessageChannel").VirtualMessagePort};return e}();function o(a){return k(a)}e=function(a){babelHelpers.inheritsLoose(e,a);function e(e,f){var g;g=a.call(this,e,f)||this;var j=o(babelHelpers.assertThisInitialized(g)),k=new(c("Deferred"))();e=k.getPromise();j.addMessageListener("endpoint_started",function(a){k.resolve(a);var b=n(a);j.postMessage({endpoint:a.endpoint,startSendTimestamp:b==null?void 0:b.sendTimestamp,targetEndpoint:g.name,type:"endpoint_started_received"})});f=j.onMessageOnce("endpoint_started_received",function(){return!0});void (h||(h=d("PromiseAnnotate"))).setDisplayName(e,"endpoint_started");void h.setDisplayName(f,"endpoint_started_received");j.postMessage({endpoint:g.name,type:"endpoint_started"});c("promiseDone")(e,function(a){a=n(a);d("WorkerMessagePortLogging").logMessageTiming(g.name,a)});c("promiseDone")(f,function(a){a=n(a);a!=null&&d("WorkerMessagePortLogging").logMessageTiming(g.name,a,!0)});g.fullyConnected=(i||(i=b("Promise"))).race([e,f]).then(function(){return babelHelpers.assertThisInitialized(g)});return g}return e}(a);g.getMessageTiming=n;g.WorkerMessagePort=a;g.CastWorkerMessagePort=o;g.WorkerSyncedMessagePort=e}),98);
__d("WorkerFuncChannel",["invariant","DateConsts","ExecutionEnvironment","FBLogger","FuncChannel","MainPageUrl","Promise","TimedOnceFunc","VirtualMessageChannel","WorkerMessagePort","err","promiseDone"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k={serialize:function(a,b){b.push(a);return a},deserialize:function(a){return a}},l=1e4,m=0,n=5,o=n+1,p=function(){function a(a){var b=self.WeakRef;b&&(this.$1=new b(a))}var b=a.prototype;b.isDead=function(){return this.$1!=null&&this.$1.deref()==null};return a}(),q=function(e){babelHelpers.inheritsLoose(a,e);function a(a,b,f,g){var i;f===void 0&&(f=null);g===void 0&&(g={});i=e.call(this,a)||this;i.$WorkerFuncChannel$p_1=new Map();i.$WorkerFuncChannel$p_2=new Map();i.$WorkerFuncChannel$p_5={"function":{serialize:function(a,b){b=o++;i.$WorkerFuncChannel$p_1.set(b,a);a={type:"function",value:b};return a},deserialize:function(a){var b=babelHelpers.assertThisInitialized(i),c=a.value;a=function(){var a={type:"call",id:c,method:"__anonymous",thisArg:null,argList:Array.from(arguments)};return b.callMessageHandler(a)};i.$WorkerFuncChannel$p_2.set(c,new p(a));return a}},custom:{serialize:function(a,b){var c=a.constructor.name,d=i.$WorkerFuncChannel$p_4[c];d!=null||h(0,63779,c);d=d.serialize(a,b);return{type:"custom",custom:c,value:d}},deserialize:function(a){var b=a.custom,c=i.$WorkerFuncChannel$p_4[b];c!=null||h(0,63779,b);return c.deserialize(a.value)}},error:{serialize:function(a,b){b={name:a.name,message:a.message,stack:a.stack};return{type:"error",value:b}},deserialize:function(a){var b=c("err")(a.value.message);b.name=a.value.name;b.stack=a.value.stack;return b}},object:{serialize:function(a,b){b=a.constructor;if(b!==Object)throw new Error("Cannot clone class object");return{type:"object",value:a}},deserialize:function(a){return a.value}},raw:{serialize:function(a,b){return{type:"raw",value:a}},deserialize:function(a){return a.value}}};i.name=b;i.$WorkerFuncChannel$p_3=f;i.$WorkerFuncChannel$p_4=g;a=function(a){d("MainPageUrl").isWorkerLogEnabled()&&c("FBLogger")("adsworker").debug("[Channel] removing callback ids: %s",a.join()),a.forEach(function(a){return i.$WorkerFuncChannel$p_1["delete"](a)})};var k=i.__remoteInternalFunc(m,a);typeof jest===typeof void 0&&(j||(j=c("ExecutionEnvironment"))).isInBrowser&&setInterval(function(){var a=[];i.$WorkerFuncChannel$p_2.forEach(function(b,c){b.isDead()&&(a.push(c),i.$WorkerFuncChannel$p_2["delete"](c))});a.length>0?k(a):i.$WorkerFuncChannel$p_2.size>5e3&&c("FBLogger")("adsworker").warn("[%s] has too many remaining refs %s",i.name,i.$WorkerFuncChannel$p_2.size)},l);return i}var f=a.prototype;f.__remoteInternalFunc=function(a,b){(a>n||this.$WorkerFuncChannel$p_1.get(a)!=null)&&c("FBLogger")("adsworker").mustfix("invalid internal func id %s",a);this.$WorkerFuncChannel$p_1.set(a,b);b=this.$WorkerFuncChannel$p_5["function"].deserialize({type:"function",value:a});return b};f.$WorkerFuncChannel$p_6=function(a){return a instanceof Int8Array||a instanceof Int16Array||a instanceof Int32Array||a instanceof Uint8Array||a instanceof Uint8ClampedArray||a instanceof Uint16Array||a instanceof Uint32Array||a instanceof Float32Array||a instanceof Float64Array};f.$WorkerFuncChannel$p_7=function(a){if(typeof a==="function")return"function";if(a===null)return"raw";if(typeof a!=="object")return"raw";if(Array.isArray(a))return"raw";if(this.$WorkerFuncChannel$p_6(a))return"raw";var b=a.constructor.name;if(this.$WorkerFuncChannel$p_4[b])return"custom";return a instanceof Error?"error":"object"};f.$WorkerFuncChannel$p_8=function(a,b){var c=this.$WorkerFuncChannel$p_7(a);return this.$WorkerFuncChannel$p_5[c].serialize(a,b)};f.$WorkerFuncChannel$p_9=function(a){var b=a;if(a!==null&&typeof a==="object"&&typeof a.type==="string"){var c=this.$WorkerFuncChannel$p_5[a.type];if(c)return c.deserialize(a)}return b};f.__onPostOutMessage=function(a){return a};f.__callCallback=function(a,b){a.apply(null,b.argList)};f.setOutMessagePort=function(a,d){var e=this,f=[];this.setCallMessageHandler(function(d){d.thisArg=e.name;var g=[];d.argList=d.argList.map(function(a){return e.$WorkerFuncChannel$p_8(a,g)});var h=new(i||(i=b("Promise")))(function(a,b){d.result={resolveFunc:e.$WorkerFuncChannel$p_8(a,g),rejectFunc:e.$WorkerFuncChannel$p_8(b,g)}}),j=e.__onPostOutMessage(d);j!=null&&(f!=null?f.push({message:d,transferList:g}):a.postMessage(d,g));h["catch"](function(a){c("FBLogger")("worker").catching(a).mustfix("Error: channel %s, remote call of %s returned error %s",e.name,d.method,a.message)});return h});c("promiseDone")(d||x.waitForRemote(this.name),function(){var b=f;f=null;b==null?void 0:b.forEach(function(b){a.postMessage(b.message,b.transferList)});e.$WorkerFuncChannel$p_3==null?void 0:e.$WorkerFuncChannel$p_3()})};f.__messageToCall=function(a){var b=this;a.argList=a.argList.map(function(a){return b.$WorkerFuncChannel$p_9(a)});if(a.result){var d=a.result,e=d.resolveFunc;d=d.rejectFunc;e=this.$WorkerFuncChannel$p_9(e);d=this.$WorkerFuncChannel$p_9(d);a.result={resolveFunc:e,rejectFunc:d}}e=a.id;if(e===void 0)this.messageToCall(a);else{d=this.$WorkerFuncChannel$p_1.get(e);d?this.__callCallback(d,a):c("FBLogger")("adsworker").mustfix("Error: callback is called but no longer available")}};f.setInMessagePort=function(a){var b=this;a.addMessageListener("call",function(a){typeof a.thisArg==="string"&&a.thisArg===b.name&&b.__messageToCall(a)});x.notifyReady(this.name)};f.setMessagePort=function(a){this.setInMessagePort(a);this.setOutMessagePort(a);return this};return a}(c("FuncChannel")),r=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}return a}(d("WorkerMessagePort").WorkerMessagePort);function s(a,b,d,e){x.exportChannel(a,function(e){var f=e.syncPort;e=e.remoteNativePort;var g=b();a===g.name||h(0,54247,a,g.name);var i;e?(i=new r(e,f.name+"("+g.name+")"),e.start(),i.onError.add(function(a){c("FBLogger")("worker").catching(a).mustfix("error caught in remotePort");throw a})):i=f;g.setMessagePort(i).setBackend(d);c("promiseDone")(f.fullyConnected,function(){f.postMessage({type:"channelReady",channelName:g.name}),x.logState("EXPORTED",a)})})}function a(a,b,c,d){d===void 0&&(d={});return s(c,function(){return new q(function(b){return a},c,null,d)},a,b)}var t=function(b){babelHelpers.inheritsLoose(a,b);function a(a,e){e===void 0&&(e="");var f=d("DateConsts").MS_PER_MIN*1;return b.call(this,function(){c("FBLogger")("worker").warn("Channel %s did not establish eventually. %s",a,e)},f)||this}return a}(d("TimedOnceFunc").TimedOnceFunc);function u(a,b){var e,f=null,g,h;b.isWrappingVirtualMessagePort()?(h=new(d("VirtualMessageChannel").VirtualMessageChannel)(!0,!0),e="on virtual dedicated channel"):(h=new MessageChannel(),e="on native dedicated channel",g=[h.port2]);var i=h.port1;f=h.port2;i.start();var j=new r(i,b.name+"("+a.name+")");c("promiseDone")(b.fullyConnected,function(){x.logState("IMPORTING "+e+" ",a.name);a.setInMessagePort(j);var d=new t(a.name,e);c("promiseDone")(b.onMessageOnce("channelReady",function(b){return b.channelName===a.name}),function(b){a.setOutMessagePort(j),d.cancel(),x.logState("ESTABLISHED",a.name)});b.postMessage({type:"channelImport",channelName:a.name,port:f},g)});return a.proxyMethods}function v(a,b,c,d){c===void 0&&(c=null);d===void 0&&(d={});b=new q(function(b){return a},b,c,d);return b}function e(a,b,c,d,e){d===void 0&&(d=null);e===void 0&&(e={});return u(v(a,c,d,e),b)}var w=function(){function a(){this.name=self.name+"_P"+Math.round(10*Math.random()),this.$1=new Map(),this.$2=new Set()}var e=a.prototype;e.logState=function(a,b){d("MainPageUrl").isWorkerLogEnabled()&&c("FBLogger")("worker").debug("%s-%s %s",this.name,a,b)};e.$3=function(a){var b=this.$1.get(a);b==null&&(b={pendingPorts:null,onImportRequest:null},this.$1.set(a,b));return b};e.exportChannel=function(a,b){var d=this,e=this.$3(a);e.onImportRequest!=null&&c("FBLogger")("worker").debug("Re-exporting channel %s",a);e.onImportRequest=b;this.logState("REGISTERED",a);a=e.pendingPorts;if(a!=null){var f=a.filter(function(a){return d.$2.has(a.syncPort)});f.forEach(b);f.length<a.length?e.pendingPorts=a.filter(function(a){return!d.$2.has(a.syncPort)}):e.pendingPorts=null}return e};e.activate=function(a,b,d){var e=this;if(this.$2.has(a)){c("FBLogger")("worker").debug("Port %sis already duplicated ",a.name);return}this.$2.add(a);a.addMessageListener("channelImport",function(b){var c=b.channelName,d=e.$3(c);b={syncPort:a,remoteNativePort:b.port};d.onImportRequest!=null?d.onImportRequest(b):d.pendingPorts!=null?d.pendingPorts.push(b):d.pendingPorts=[b];e.logState("IMPORT REQUEST",c)})};e.notifyReady=function(a){};e.waitForRemote=function(a){return(i||(i=b("Promise"))).resolve()};return a}(),x=new w();function f(a,b,c){x.activate(a,b,c)}g.TransferableTransformer=k;g.WorkerFuncChannel=q;g.exportChannelOnPort=s;g.exportChannel=a;g.importChannelOnPort=u;g.makeChannel=v;g.importChannel=e;g.activateChannels=f}),98);
__d("WorkerBanzaiLazyQueueChannelClient",["BanzaiLazyQueue","WorkerFuncChannel"],(function(a,b,c,d,e,f,g){"use strict";function a(a){d("WorkerFuncChannel").exportChannel(c("BanzaiLazyQueue"),a,"banzai_lazyqueue_channel")}g.init=a}),98);
__d("WorkerQPLChannel",["WorkerFuncChannel","performanceAbsoluteNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new(d("WorkerFuncChannel").WorkerFuncChannel)(function(a){return{markerStartFromNavStart:null,markerStart:function(b,d,e){e===void 0&&(e=(h||(h=c("performanceAbsoluteNow")))());return a.markerStart(b,d,e)},markerAnnotate:function(b,c,d){return a.markerAnnotate(b,c,d)},markerPoint:function(b,d,e){e=(e==null?void 0:e.timestamp)===void 0?babelHelpers["extends"]({},e,{timestamp:(h||(h=c("performanceAbsoluteNow")))()}):e;return a.markerPoint(b,d,e)},markerEnd:function(b,d,e,f){f===void 0&&(f=(h||(h=c("performanceAbsoluteNow")))());return a.markerEnd(b,d,e,f)}}},"qpl");function a(a){i.setMessagePort(a)}function b(a){i.setBackend(a)}g.setMessagePort=a;g.initQPL=b}),98);
__d("WorkerClient",["WorkerBanzaiLazyQueueChannelClient","WorkerFuncChannel","WorkerQPLChannel"],(function(a,b,c,d,e,f,g){"use strict";function a(a){d("WorkerFuncChannel").activateChannels(a,"client","worker"),d("WorkerBanzaiLazyQueueChannelClient").init(a),d("WorkerQPLChannel").setMessagePort(a),e(["QuickPerformanceLogger"],function(a){return d("WorkerQPLChannel").initQPL(a)})}g.init=a}),98);
__d("MAWCommonSetupWorker",["Deferred","FBLogger","MAWCurrentUser","MAWGetDBVersionForTest","MAWInit","MAWMIC","MAWSetupWorker","MAWWaitForBackendSetup","MAWWebWorkerSingleton","MessengerWebInitData","Promise","WADevToolMessageChannel","WALogger","WorkerClient","WorkerMessagePort","err","gkx","pageID","promiseDone","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["Terminating worker and rebooting..."]);i=function(){return a};return a}var j=c("gkx")("3282");e=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var e;e=a.call(this,b,c)||this;e.onmessage=null;e.$BridgeAdapter$p_1=b;d("WorkerClient").init(babelHelpers.assertThisInitialized(e));e.onUnhandledMessage.add(function(a){e.onmessage&&e.onmessage({data:a})});e.addMessageListener("worker-setup",function(a){a.status==="worker-ready"&&d("MAWInit").MAWInit.addBoolAnnotation("workerReadyReceived",!0)});return e}var c=b.prototype;c.getBridgePort=function(){return this};return b}(d("WorkerMessagePort").WorkerSyncedMessagePort);function a(a){var b=a.bridge,e=a.reason,f=a.vaultMaterials,g=new(c("Deferred"))();b.addMessageListener("worker-setup",function(b){return k(a,g,b)});b.addMessageListener("worker-setup-ack",function(a){return l(a,e)});n(b,f);c("promiseDone")(d("MAWWebWorkerSingleton").doesWorkerExist(),function(a){return d("MAWInit").MAWInit.addBoolAnnotation("workerExistsOnSetup",a)});c("promiseDone")(d("MAWWebWorkerSingleton").getWorkerHealthStatus(),function(a){return d("MAWInit").MAWInit.addStringAnnotation("workerHealthStatus",a.tag)});return g.getPromise()}function k(a,b,e){var f;if(e.setupHash!==c("pageID"))return;switch(e.status){case"success":b.resolve(a.bridge.getBridgePort());break;case"failure":b.reject(c("err")((f=e.msg)!=null?f:"Error setting up worker"));break;case"already-setup":return m(a,b,e);default:d("MAWInit").MAWInit.addBoolAnnotation("invalidSetupMessageReceived",!0);throw c("FBLogger")("messenger_web").mustfixThrow("Invalid message for worker: "+e.status)}}function l(a,b){if(a.setupHash!==c("pageID"))return;d("MAWInit").MAWInit.logPoint(b+"_backend_setup_ack");d("MAWMIC").addPoint(b+"_backend_setup_ack");a=(b=a.backendState)!=null?b:"none";d("MAWInit").MAWInit.addStringAnnotation("backendAckState",a);d("MAWMIC").addStringAnnotation("backendAckState",a)}function m(a,e,f){var g=a.bridge,j=a.onWorkerAlreadySetup,k=a.onWorkerRestart;a=a.reason;switch(f.action){case"no-action":e.resolve(g.getBridgePort());j();d("MAWInit").MAWInit.logPoint(a+"_backend_already_setup");d("MAWMIC").addPoint(a+"_backend_already_setup");break;case"reboot-required":d("WALogger").DEV(i());d("MAWMIC").addPoint(a+"_worker_rebooting");d("MAWInit").MAWInit.logPoint(a+"_worker_rebooting");j=(g=f==null?void 0:f.reason)!=null?g:"none";d("MAWInit").MAWInit.addStringAnnotation("workerRebootReason",j);d("MAWMIC").addStringAnnotation("workerRebootReason",j);a=d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker()?(h||(h=b("Promise"))).resolve(d("MAWWaitForBackendSetup").resetCurrentWorkerWithId(d("MAWWaitForBackendSetup").getCurrentWorkerID())):d("MAWSetupWorker").terminateDedicatedWorker("reboot required");return a.then(function(){return k("rebootRequired").then(function(a){return e.resolve(a)})["catch"](function(a){return e.reject(a)})});default:f.action;throw c("FBLogger")("messenger_web").mustfixThrow("Invalid action for worker-setup:already-setup: "+f.action)}}function n(a,b){b={content:{createdAt:c("MessengerWebInitData").createdAt,fbId:d("MAWCurrentUser").getID(),sessionId:c("MessengerWebInitData").sessionId,testDbVersion:d("MAWGetDBVersionForTest").getDbVersionForTest(),userKeyBase:c("MessengerWebInitData").userKeyBase,vaultMaterials:b},setupHash:c("pageID"),type:"worker-setup"};if(j){d("MAWInit").MAWInit.addBoolAnnotation("devToolsEnabled",!0);var e=d("WADevToolMessageChannel").createNewChannel();b.content.devToolMessageChannelPort2=e.port2;a.postMessage(b,[e.port2])}else a.postMessage(b)}g.BridgeAdapter=e;g.initWorker=a}),98);
__d("MAWDbChatId__UNSAFE_DO_NOT_USE",["I64"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){return(h||(h=d("I64"))).of_float(a)}function b(a,b){return a+"_"+b}g.convertToChatId64__DEPRECATED=a;g.craftAltIndex__DEPRECATED=b}),98);
__d("WADirectPath",["WAResultOrError"],(function(a,b,c,d,e,f,g){"use strict";var h="0_0_0_.enc";function a(a){return a}function b(a){if(a==null)return d("WAResultOrError").makeError("direct-path-undefined");if(a==="")return d("WAResultOrError").makeError("direct-path-empty");return a.includes(h)?d("WAResultOrError").makeError("direct-path-corrupted"):d("WAResultOrError").makeResult(a)}g.unsafeCastToDirectPath=a;g.validateDirectPath=b}),98);
__d("WAMediaEntryData.pb",["WAProtoConst"],(function(a,b,c,d,e,f,g){a={};b={};c={};a.internalSpec={fileSha256:[1,(e=d("WAProtoConst")).TYPES.BYTES],mediaKey:[2,e.TYPES.BYTES],fileEncSha256:[3,e.TYPES.BYTES],directPath:[4,e.TYPES.STRING],mediaKeyTimestamp:[5,e.TYPES.INT64],serverMediaType:[6,e.TYPES.STRING],uploadToken:[7,e.TYPES.BYTES],validatedTimestamp:[8,e.TYPES.BYTES],sidecar:[9,e.TYPES.BYTES],objectId:[10,e.TYPES.STRING],fbid:[11,e.TYPES.STRING],downloadableThumbnail:[12,e.TYPES.MESSAGE,c],handle:[13,e.TYPES.STRING],filename:[14,e.TYPES.STRING],progressiveJpegDetails:[15,e.TYPES.MESSAGE,b],size:[16,e.TYPES.INT64],lastDownloadAttemptTimestamp:[17,e.TYPES.INT64]};b.internalSpec={scanLengths:[1,e.FLAGS.REPEATED|e.TYPES.UINT32],sidecar:[2,e.TYPES.BYTES]};c.internalSpec={fileSha256:[1,e.TYPES.BYTES],fileEncSha256:[2,e.TYPES.BYTES],directPath:[3,e.TYPES.STRING],mediaKey:[4,e.TYPES.BYTES],mediaKeyTimestamp:[5,e.TYPES.INT64],objectId:[6,e.TYPES.STRING]};g.MediaEntrySpec=a;g.MediaEntry$ProgressiveJpegDetailsSpec=b;g.MediaEntry$DownloadableThumbnailSpec=c}),98);
__d("WAProgressiveJpegMarkers",[],(function(a,b,c,d,e,f){"use strict";a=255;b=0;c={SOF0:192,SOF1:193,SOF2:194,SOF3:195,DHT:196,SOF5:197,SOF6:198,SOF7:199,JPG:200,SOF9:201,SOF10:202,SOF11:203,SOF13:205,SOF14:206,SOF15:207,RST0:208,RST1:209,RST2:210,RST3:211,RST4:212,RST5:213,RST6:214,RST7:215,SOI:216,EOI:217,SOS:218,DQT:219,DRI:221,APP0:224,APP1:225,APP2:226,APP3:227,APP4:228,APP5:229,APP6:230,APP7:231,APP8:232,APP9:233,APP10:234,APP11:235,APP12:236,APP13:237,APP14:238,APP15:239,COM:254,TEM:1};d=[b,c.RST0,c.RST1,c.RST2,c.RST3,c.RST4,c.RST5,c.RST6,c.RST7];f.MARKER_PREFIX=a;f.MARKER_ESCAPE_BYTE=b;f.MARKERS=c;f.SOS_STREAM=d}),66);
__d("WAProgressiveJpegGetScanOffsets",["WAProgressiveJpegMarkers","WATagsLogger"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=babelHelpers.taggedTemplateLiteralLoose(["Non-progressive JPEG marker found"]);h=function(){return a};return a}function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["Ending marker found before end of image"]);i=function(){return a};return a}function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["Scan found at offset ",""]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["Not a JPEG image"]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["starting"]);l=function(){return a};return a}var m=d("WATagsLogger").TAGS(["media","progressiveJpegUtils"]);function a(a){m.DEV(l());if(!n(a)){m.DEV(k());return[]}var b=[],c=2,e="READ_MARKER_FIRST_BYTE_OR_ENTROPY_DATA",f=null,g=0,r=!1,s=!1,t=!1;function u(){t=!1,m.DEV(j(),c-1),b.push(c-1)}while(c<a.byteLength){f=a[c];if(r){m.ERROR(i());return[]}switch(e){case"READ_MARKER_FIRST_BYTE_OR_ENTROPY_DATA":f===d("WAProgressiveJpegMarkers").MARKER_PREFIX&&(e="READ_MARKER_SECOND_BYTE");break;case"READ_MARKER_SECOND_BYTE":if(f===d("WAProgressiveJpegMarkers").MARKER_PREFIX)e="READ_MARKER_SECOND_BYTE";else if(f===d("WAProgressiveJpegMarkers").MARKER_ESCAPE_BYTE)e="READ_MARKER_FIRST_BYTE_OR_ENTROPY_DATA";else if(f===d("WAProgressiveJpegMarkers").MARKERS.EOI)r=!0,t&&u();else if(!s&&p(f)){m.LOG(h());return[]}else o(f)?(s=!0,e="READ_MARKER_FIRST_BYTE_OR_ENTROPY_DATA"):(t&&u(),f===d("WAProgressiveJpegMarkers").MARKERS.SOS&&(t=!0),q(f)?e="READ_SIZE_FIRST_BYTE":e="READ_MARKER_FIRST_BYTE_OR_ENTROPY_DATA");break;case"READ_SIZE_FIRST_BYTE":e="READ_SIZE_SECOND_BYTE";break;case"READ_SIZE_SECOND_BYTE":c+=g*256;c+=f;c-=2;e="READ_MARKER_FIRST_BYTE_OR_ENTROPY_DATA";break}c+=1;g=f}return b}function n(a){if(a.byteLength<=2)return!1;if(a[0]!==d("WAProgressiveJpegMarkers").MARKER_PREFIX)return!1;return a[1]!==d("WAProgressiveJpegMarkers").MARKERS.SOF2&&a[1]!==d("WAProgressiveJpegMarkers").MARKERS.SOI?!1:!0}function o(a){return a===d("WAProgressiveJpegMarkers").MARKERS.SOF2||a===d("WAProgressiveJpegMarkers").MARKERS.SOF6||a===d("WAProgressiveJpegMarkers").MARKERS.SOF10||a===d("WAProgressiveJpegMarkers").MARKERS.SOF14}function p(a){return a===d("WAProgressiveJpegMarkers").MARKERS.SOF0||a===d("WAProgressiveJpegMarkers").MARKERS.SOF1||a===d("WAProgressiveJpegMarkers").MARKERS.SOF3||a===d("WAProgressiveJpegMarkers").MARKERS.SOF5||a===d("WAProgressiveJpegMarkers").MARKERS.SOF7||a===d("WAProgressiveJpegMarkers").MARKERS.JPG||a===d("WAProgressiveJpegMarkers").MARKERS.SOF9||a===d("WAProgressiveJpegMarkers").MARKERS.SOF11||a===d("WAProgressiveJpegMarkers").MARKERS.SOF13||a===d("WAProgressiveJpegMarkers").MARKERS.SOF15}function q(a){if(a===d("WAProgressiveJpegMarkers").MARKERS.TEM)return!1;return a>=d("WAProgressiveJpegMarkers").MARKERS.RST0&&a<=d("WAProgressiveJpegMarkers").MARKERS.RST7?!1:a!==d("WAProgressiveJpegMarkers").MARKERS.EOI&&a!==d("WAProgressiveJpegMarkers").MARKERS.SOI}g.getProgressiveJpegScanOffsets=a}),98);
__d("WAProgressiveJpegGetScanLengths",["WAProgressiveJpegGetScanOffsets"],(function(a,b,c,d,e,f,g){"use strict";function a(a){a=d("WAProgressiveJpegGetScanOffsets").getProgressiveJpegScanOffsets(a);return h(a)}function b(a){return a}function h(a){var b=[],c=0;for(a of a)b.push(a-c),c=a;return b}g.getProgressiveJpegScanLengths=a;g.asProgressiveJpegScanLength=b}),98);
__d("WAServerMediaType",["WAAssertUnreachable"],(function(a,b,c,d,e,f,g){"use strict";d=["image","sticker","ptt","audio","document","video","gif","ppic","md-app-state","md-msg-hist","kyc-id","template","thumbnail-image","thumbnail-video","thumbnail-gif","thumbnail-document","thumbnail-link","payment-bg-image","novi-video","novi-image","product","product-catalog-image","xma-image","biz-cover-photo","preview","newsletter-audio","newsletter-document","newsletter-image","newsletter-gif","newsletter-ptt","newsletter-sticker","newsletter-thumbnail-link","newsletter-video","sticker-pack","thumbnail-sticker-pack","music-artwork"];function a(a){switch(a){case"image":case"sticker":case"ptt":case"audio":case"document":case"video":case"gif":case"ppic":case"md-app-state":case"md-msg-hist":case"kyc-id":case"thumbnail-image":case"thumbnail-video":case"thumbnail-gif":case"thumbnail-document":case"thumbnail-link":case"payment-bg-image":case"novi-video":case"novi-image":case"template":case"product":case"product-catalog-image":case"xma-image":case"biz-cover-photo":case"preview":case"newsletter-audio":case"newsletter-document":case"newsletter-image":case"newsletter-gif":case"newsletter-ptt":case"newsletter-sticker":case"newsletter-thumbnail-link":case"newsletter-video":case"sticker-pack":case"thumbnail-sticker-pack":case"music-artwork":return a;default:return null}}function b(a){switch(a){case"Image":return"image";case"Video":return"video";case"Ptt":return"ptt";case"Gif":return"gif";case"Sticker":return"sticker";case"DocumentFile":return"document";case"Text":case"Futureproof":case"Ciphertext":case"Unavailable":case"ExpiredEphemeral":case"Admin":case"Revoked":case"DeleteForMe":case"EphemeralSettingAdmin":case"EphemeralSyncResponse":case"EphemeralSettingChangeFromCurrentDevice":case"AlertICDC":case"GroupInvite":case"SenderKeyDistribution":case"Reaction":case"EditAction":case"ReceiverFetch":case"Raven":case"GroupPollCreate":case"GroupPollUpdate":return null;default:return c("WAAssertUnreachable")(a)}}g.SERVER_MEDIA=d;g.castToServerMediaType=a;g.getMediaType=b}),98);
__d("WAHasProperty",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){return Object.prototype.hasOwnProperty.call(a,b)}f["default"]=a}),66);
__d("WAProtoCompile",["WAProtoConst"],(function(a,b,c,d,e,f,g){"use strict";var h=function(a,b,c,d,e,f,g,h,i){this.names=a,this.fields=b,this.types=c,this.defaults=d,this.meta=e,this.oneofToFields=f,this.fieldToOneof=g,this.reservedTags=h?h.reduce(function(a,b){a[b]=!0;return a},{}):{},this.reservedFields=i?i.reduce(function(a,b){a[b]=!0;return a},{}):{}};function a(a){if(a.internalCompiledSpec)return a.internalCompiledSpec;var b=a.internalSpec;if(!b)throw new Error("Message Class "+String(a)+" does not have internalSpec");var c=a.internalDefaults||{},e=Object.keys(b).filter(function(a){return a!==d("WAProtoConst").KEYS.ONEOF}),f=new Array(e.length),g=[],j=[],k=new Array(e.length),l=b[d("WAProtoConst").KEYS.ONEOF]||{};e.sort(function(a,c){a=i(b,a);c=i(b,c);return a[0]-c[0]});for(var m=0;m<e.length;m++){var n=e[m],o=i(b,n);k[m]=c[n];var p=o[1],q=o[0];g.push(q);j.push(p);if((p&d("WAProtoConst").TYPE_MASK)===d("WAProtoConst").TYPES.MESSAGE)f[m]=o[2];else if((p&d("WAProtoConst").TYPE_MASK)===d("WAProtoConst").TYPES.ENUM){q=o[2];if(typeof q.cast==="function")f[m]=q;else{var r=!0,s=0;for(var t in q)r&&t!==s++&&(r=!1);t=void 0;if(r){t=[];for(r=0;r<s;r++)t.push(!0)}else{t={};for(r in q)t[q[r]]=!0}f[m]=t}}else if((p&d("WAProtoConst").TYPE_MASK)===d("WAProtoConst").TYPES.MAP){if(o.length!==3)throw new Error("Map field "+n+" should have exactly three elements in its internalSpec");f[m]=o[2]}else f[m]=null}var u={};s=function(a){l[a].forEach(function(b){u[b]||(u[b]=[]),u[b].push(a)})};for(r in l)s(r);q=b[d("WAProtoConst").KEYS.RESERVED]&&b[d("WAProtoConst").KEYS.RESERVED][d("WAProtoConst").KEYS.RESERVED_TAGS];t=b[d("WAProtoConst").KEYS.RESERVED]&&b[d("WAProtoConst").KEYS.RESERVED][d("WAProtoConst").KEYS.RESERVED_FIELDS];p=new h(e,g,j,k,f,l,u,q,t);a.internalCompiledSpec=p;return p}function i(a,b){a=a[b];if(a==null)throw new Error("fieldData of "+b+" is missing");return a}g.Spec=h;g.compileSpec=a}),98);
__d("WAProtoUtils",["WAProtoConst"],(function(a,b,c,d,e,f,g){"use strict";function a(a){if(a&d("WAProtoConst").FLAGS.PACKED)return d("WAProtoConst").ENC.BINARY;a=a&d("WAProtoConst").TYPE_MASK;if(a<=d("WAProtoConst").TYPES.ENUM)return d("WAProtoConst").ENC.VARINT;else if(a<=d("WAProtoConst").TYPES.DOUBLE)return d("WAProtoConst").ENC.BIT64;else if(a<=d("WAProtoConst").TYPES.MESSAGE)return d("WAProtoConst").ENC.BINARY;else if(a===d("WAProtoConst").TYPES.MAP)return d("WAProtoConst").ENC.BINARY;else return d("WAProtoConst").ENC.BIT32}g.typeToEncType=a}),98);
__d("WAProtoValidate",["WAHasProperty","WALogger","WAProtoCompile","WAProtoConst"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=babelHelpers.taggedTemplateLiteralLoose(['"','" is not an array']);h=function(){return a};return a}function i(){var a=babelHelpers.taggedTemplateLiteralLoose(['"','" is not ',""]);i=function(){return a};return a}function j(){var a=babelHelpers.taggedTemplateLiteralLoose(['"','" is out of range']);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(['"','" is not a valid int']);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(['"','" is not a valid long']);l=function(){return a};return a}var m=Number.MAX_SAFE_INTEGER;function n(a,b){a=o(a,b);if(a){a.reverse();throw new TypeError("Message missing required value "+a.join("."))}}function a(a,b){n(a,b);b=u(b,a);if(b){b.path.reverse();throw new TypeError("Invalid value at "+b.path.join(".")+": "+b.error)}}function o(a,b){a=d("WAProtoCompile").compileSpec(a);var e=a.names,f=a.types;a=a.meta;var g=void 0;for(var h=0;h<f.length&&!g;h++){var i=f[h],j=e[h],k=c("WAHasProperty")(b,j)?b[j]:void 0;if(i&d("WAProtoConst").FLAGS.REQUIRED&&k==null)g=[j];else if((i&d("WAProtoConst").TYPE_MASK)===d("WAProtoConst").TYPES.MESSAGE&&i&d("WAProtoConst").FLAGS.REPEATED&&k!=null){var l=a[h],m=void 0;for(m=0;m<k.length&&!g;m++)g=o(l,k[m]);g&&g.push(j+"["+m+"]")}else(i&d("WAProtoConst").TYPE_MASK)===d("WAProtoConst").TYPES.MESSAGE&&k!=null&&(g=o(a[h],k),g&&g.push(j))}return g}function p(a,b,c){if(typeof a==="string")if(/^-?0x[0-9a-f]{16}$/i.test(a))return!1;else{d("WALogger").LOG(l(),s(a)).color("red");return{path:[],error:"value must be a hex string of the form '0x123...' or '-0x123...' where the tail is always 16 characters long"}}else return q(a,b,c)}function q(a,b,c){if(typeof a!=="number"||a!==a||Math.floor(a)!==a){d("WALogger").LOG(k(),s(a)).color("red");return{path:[],error:"value must be an int"}}else if(a<b||a>=c){d("WALogger").LOG(j(),s(a)).color("red");return{path:[],error:"value is out of range"}}else return!1}function r(a,b,c){if(a)return void 0;else{d("WALogger").LOG(i(),s(c),b).color("red");return{path:[],error:"value is invalid"}}}function s(a){if(typeof a==="string")return'"'+a+'"';else if(Array.isArray(a))return"["+a.join(", ")+"]";else return""+a}var t=[void 0,function(a){return q(a,-2147483648,2147483648)},function(a){return p(a,-m,m+1)},function(a){return q(a,0,4294967296)},function(a){return p(a,0,m+1)},function(a){return q(a,-2147483648,2147483648)},function(a){return p(a,-m,m+1)},function(a){return r(typeof a==="boolean","boolean",a)},function(a,b){return r(typeof a==="number"&&(b[a]||b.cast(a)!==void 0),"in enum",a)},function(a){return p(a,0,m+1)},function(a){return p(a,-m,m+1)},function(a){return r(typeof a==="number","number",a)},function(a){return r(typeof a==="string","string",a)},function(a){return r(a instanceof ArrayBuffer||a instanceof Uint8Array,"ArrayBuffer or Uint8Array",a)},u,function(a){return q(a,0,4294967296)},function(a){return q(a,-2147483648,2147483648)},function(a){return r(typeof a==="number","number",a)}];function u(a,b){b=d("WAProtoCompile").compileSpec(b);var c=b.names,e=b.fields,f=b.types,g=b.meta,i=b.oneofToFields,j=b.fieldToOneof,k=b.reservedTags,l=b.reservedFields,m=void 0;b=function(b){var n=c[b],o=f[b],p=a[n],q=o&d("WAProtoConst").TYPE_MASK,r=t[q],u=(o&d("WAProtoConst").TYPE_MASK)===d("WAProtoConst").TYPES.MAP;if(r===void 0&&!u)throw new Error("Can not find the validator for type "+q);if(o&(d("WAProtoConst").FLAGS.PACKED|d("WAProtoConst").FLAGS.REPEATED)&&p!=null)if(!Array.isArray(p))d("WALogger").LOG(h(),s(p)).color("red"),m={path:[n],error:"repeated field must be array"};else{q=g[b];for(o=0;o<p.length&&!m;o++)m=r(p[o],q),m&&m.path.push(n+"["+o+"]")}else if(u&&p!=null){o=g[b];q=o[0];u=o[1];o=typeof u==="object"?d("WAProtoConst").TYPES.MESSAGE:u;var v=t[q],w=t[o];if(v===void 0||w===void 0)throw new Error("Can not find the validator for Map with key type "+q+", value type "+o);for(o of p){var x=o[0],y=o[1];m=v(x,q);if(m){m.path.push(n+"'s key ["+x+"]");return{v:m}}m=w(y,u);if(m){m.path.push(n+"["+x+"]'s value");return{v:m}}}}else if(p!=null){m=r(p,g[b]);m&&m.path.push(n);y=j[n];y&&y.forEach(function(b){var c=i[b].filter(function(a){return a!==n});c.forEach(function(c){typeof a[c]!=="undefined"&&(m={path:[b],error:"oneof '"+b+"' has fields '"+n+"' and '"+c+"' set"})})});k[e[b]]&&(m={path:[n],error:"tag "+e[b]+" is reserved"});l[n]&&(m={path:[n],error:"field "+n+" is reserved"})}};for(var n=0;n<c.length&&!m;n++){var o=b(n);if(typeof o==="object")return o.v}return m}g.checkRequirements=n;g.checkValid=a}),98);
__d("decodeProtobuf",["WABinary","WAHasProperty","WAHex","WAProtoCompile","WAProtoConst","WAProtoUtils","WAProtoValidate"],(function(a,b,c,d,e,f,g){"use strict";var h="__tu";function a(a,b){b=new(d("WABinary").Binary)(b);b=o(a,b,void 0,!1,!1);d("WAProtoValidate").checkRequirements(a,b);return b}function b(a,b){b=new(d("WABinary").Binary)(b);b=o(a,b,void 0,!1,!0);d("WAProtoValidate").checkRequirements(a,b);return b}function e(a,b){b=new(d("WABinary").Binary)(b);b=o(a,b,void 0,!0,!1);d("WAProtoValidate").checkRequirements(a,b);return b}function f(a){return c("WAHasProperty")(a,"$$unsafeUnknownFields")?a.$$unsafeUnknownFields:null}function i(a,b,c){if(a!==d("WAProtoUtils").typeToEncType(b))throw new Error("FormatError: "+c+" encoded with wire type "+a)}function j(a,b,c){switch(b){case d("WAProtoConst").TYPES.INT32:return k(c,-2147483648,2147483648,a,d("WABinary").parseInt64OrThrow);case d("WAProtoConst").TYPES.INT64:return c.readVarInt(l);case d("WAProtoConst").TYPES.UINT32:return k(c,0,4294967296,a,d("WABinary").parseUint64OrThrow);case d("WAProtoConst").TYPES.UINT64:return c.readVarInt(m);case d("WAProtoConst").TYPES.SINT32:b=k(c,0,4294967296,a,d("WABinary").parseInt64OrThrow);return b&1?~(b>>>1):b>>>1;case d("WAProtoConst").TYPES.SINT64:return c.readVarInt(n);case d("WAProtoConst").TYPES.BOOL:return!!k(c,0,2,a,d("WABinary").parseUint64OrThrow);case d("WAProtoConst").TYPES.ENUM:return c.readVarInt(d("WABinary").parseInt64OrThrow);case d("WAProtoConst").TYPES.FIXED64:return c.readLong(m,!0);case d("WAProtoConst").TYPES.SFIXED64:return c.readLong(l,!0);case d("WAProtoConst").TYPES.DOUBLE:return c.readFloat64(!0);case d("WAProtoConst").TYPES.STRING:return c.readString(c.readVarInt(d("WABinary").parseUint64OrThrow));case d("WAProtoConst").TYPES.BYTES:return c.readBuffer(c.readVarInt(d("WABinary").parseUint64OrThrow));case d("WAProtoConst").TYPES.FIXED32:return c.readUint32(!0);case d("WAProtoConst").TYPES.SFIXED32:return c.readInt32(!0);case d("WAProtoConst").TYPES.FLOAT:return c.readFloat32(!0)}}function k(a,b,c,d,e){a=a.readVarInt(e);if(a<b||a>=c)throw new Error("FormatError: "+d+" encoded with out-of-range value "+a);return a}function l(a,b){var c=d("WABinary").longFitsInDouble(!0,a,b);if(c){c=p(b);return a*4294967296+c}else{c=a<0;var e;c?e=b===0?-a:~a:e=a;a=c?-b:b;return d("WAHex").createHexLongFrom32Bits(e,a,c)}}function m(a,b){var c=d("WABinary").longFitsInDouble(!1,a,b);if(c){c=p(a);var e=p(b);return c*4294967296+e}else return d("WAHex").createHexLongFrom32Bits(a,b)}function n(a,b){var c=a>>>1;a=a<<31|b>>>1;b&1&&(c=~c,a=~a);return l(c,a)}function o(a,b,c,e,f){var g=d("WAProtoCompile").compileSpec(a),l=g.names,m=g.fields,n=g.types,p=g.meta,q=g.oneofToFields,r=g.fieldToOneof,s=g.reservedTags,t=g.reservedFields;g=a.internalDefaults;var u=c||babelHelpers["extends"]({},g)||{};u.$$unknownFieldCount=(a=c==null?void 0:c.$$unknownFieldCount)!=null?a:0;for(g=0;g<l.length;g++)n[g]&d("WAProtoConst").FLAGS.REPEATED?u[l[g]]=[]:n[g]===(d("WAProtoConst").TYPES.MAP&d("WAProtoConst").TYPE_MASK)&&(u[l[g]]=new Map());var v=0;c=m.length>0;a=m[0];while(b.size()){g=k(b,0,4294967296,"field and enc type",d("WABinary").parseInt64OrThrow);var w=g&7,x=g>>>3;if(c&&x!==a){g=v;do++v===m.length&&(v=0),a=m[v];while(x!==a&&v!==g)}if(c&&x===a)(function(){var a=l[v],c=n[v];i(w,c,a);var g=c&d("WAProtoConst").TYPE_MASK,m=p[v];if(c&d("WAProtoConst").FLAGS.PACKED){var y=b.readVarInt(d("WABinary").parseUint64OrThrow);y=b.readBinary(y);while(y.size()){var z=j(a,g,y);(g!==d("WAProtoConst").TYPES.ENUM||m[z]||(m.cast==null?void 0:m.cast(z))!==void 0)&&u[a].push(z)}}else if(g===d("WAProtoConst").TYPES.MESSAGE){z=b.readVarInt(d("WABinary").parseUint64OrThrow);y=b.readBinary(z);if(c&d("WAProtoConst").FLAGS.REPEATED)u[a].push(o(m,y,void 0,e,f));else{z=u[a];u[a]=o(m,y,z,e,f)}}else if(g===d("WAProtoConst").TYPES.MAP){y=b.readVarInt(d("WABinary").parseUint64OrThrow);z=b.readBinary(y);var A,B;for(y=0;y<p[v].length;y++){var C=k(z,0,4294967296,"map field and enc type",d("WABinary").parseInt64OrThrow),D=C&7;C=C>>>3;var E=void 0;switch(D){case d("WAProtoConst").ENC.VARINT:E=z.readVarInt(d("WABinary").parseInt64OrThrow);break;case d("WAProtoConst").ENC.BIT64:E=z.readBinary(8);break;case d("WAProtoConst").ENC.BINARY:D=m[y];if(D===d("WAProtoConst").TYPES.BYTES||D===d("WAProtoConst").TYPES.STRING)E=j(a,D,z);else{var F=z.readVarInt(d("WABinary").parseUint64OrThrow);F=z.readBinary(F);E=o(D,F,void 0,e,f)}break;case d("WAProtoConst").ENC.BIT32:E=z.readBinary(4);break}C===1?A=E:B=E}u[a].set(A,B)}else{D=j(a,g,b);(g!==d("WAProtoConst").TYPES.ENUM||m[D]||(m.cast==null?void 0:m.cast(D))!==void 0)&&(c&d("WAProtoConst").FLAGS.REPEATED?u[a].push(D):u[a]=D)}F=r[a];F&&typeof u[a]!=="undefined"&&F.forEach(function(b){var c=q[b].filter(function(b){return b!==a});c.forEach(function(a){delete u[a]});if(f){u[b]=(c={type:a,value:u[a]},c[h]=!0,c);delete u[a]}});(s[x]||t[a])&&delete u[a]})();else{u.$$unknownFieldCount++;if(e){u.$$unsafeUnknownFields||(u.$$unsafeUnknownFields={});g=void 0;switch(w){case d("WAProtoConst").ENC.VARINT:g=b.readVarInt(d("WABinary").parseInt64OrThrow);break;case d("WAProtoConst").ENC.BIT64:g=b.readBinary(8);break;case d("WAProtoConst").ENC.BINARY:g=b.readBinary(b.readVarInt(d("WABinary").parseUint64OrThrow));break;case d("WAProtoConst").ENC.BIT32:g=b.readBinary(4);break}u.$$unsafeUnknownFields[x]=g}else w===d("WAProtoConst").ENC.VARINT?b.readVarInt(d("WABinary").parseInt64OrThrow):w===d("WAProtoConst").ENC.BIT64?b.advance(8):w===d("WAProtoConst").ENC.BINARY?b.advance(b.readVarInt(d("WABinary").parseUint64OrThrow)):w===d("WAProtoConst").ENC.BIT32&&b.advance(4)}}return u}function p(a){return a>=0?a:4294967296+a}g.TAGGED_UNION_TAG=h;g.decodeProtobuf=a;g.decodeProtobufWithTaggedUnions=b;g.decodeProtobufWithUnknowns=e;g.getUnknownFields=f}),98);
__d("encodeProtobuf",["WABinary","WAHex","WAProtoCompile","WAProtoConst","WAProtoUtils","WAProtoValidate"],(function(a,b,c,d,e,f,g){"use strict";var h=void 0,i=128;function a(a,b,c){c===void 0&&(c=new(d("WABinary").Binary)());d("WAProtoValidate").checkValid(a,b);o(c,b,a);h=void 0;return c}function b(){return h!==void 0?"Last encoded value for "+h:"No information known"}function c(a,b){a.writeVarInt(b)}function e(a,b){if(typeof b==="number"&&b<4503599627370496&&b>=-4503599627370496)a.writeVarInt(b>=0?2*b:2*-b-1);else{var c=new(d("WABinary").Binary)(),e;typeof b==="number"?(e=b<0,c.writeVarInt(e?-b:b)):(e=d("WAHex").hexLongIsNegative(b),c.writeVarIntFromHexLong(e?d("WAHex").negateHexLong(b):b));b=c.peek(function(){return c.readByteArray()});var f=b.byteLength;if(e){var g=0,h;do h=b[g],b[g]=h&i|(h&127)-1&127,g++;while(b[g-1]===255)}h=e?1:0;for(g=0;g<f;g++){e=b[g];var j=e&i|(e&63)<<1|h;h=(e&64)>>6;b[g]=j}h===1&&(b[f-1]|=i,c.writeInt8(1));a.writeBinary(c)}}function f(a,b){typeof b==="number"?a.writeVarInt(b):a.writeVarIntFromHexLong(b)}function j(a,b){a.writeVarInt(d("WABinary").numUtf8Bytes(b)),a.writeString(b)}function k(a,b){a.writeVarInt(b.byteLength),a.writeBuffer(b)}function l(a,b,c){a.writeWithVarIntLength(function(a,b){return o(a,b,c)},b)}var m=[void 0,c,f,c,f,e,e,function(a,b){a.writeVarInt(b?1:0)},c,function(a,b){typeof b==="number"?a.writeUint64(b,!0):a.writeHexLong(b,!0)},function(a,b){typeof b==="number"?a.writeInt64(b,!0):a.writeHexLong(b,!0)},function(a,b){a.writeFloat64(b,!0)},j,k,l,function(a,b){a.writeUint32(b,!0)},function(a,b){a.writeInt32(b,!0)},function(a,b){a.writeFloat32(b,!0)}];f=function(a){if(a==null)return void 0;var b=a;function c(a,c){for(var d=0;d<c.length;d++)b(a,c[d])}return function(a,b){a.writeWithVarIntLength(c,b)}};var n=m.map(f);function o(a,b,c){var e=d("WAProtoCompile").compileSpec(c),f=e.names,g=e.fields,i=e.types;e=e.meta;c=c.internalDefaults;for(var j=0;j<f.length;j++){var k=f[j],l=b[k];l==null&&c&&(l=c[k]);if(l!=null){h=k;k=g[j];var o=i[j],p=o&d("WAProtoConst").TYPE_MASK,q=e[j];k=k*8|d("WAProtoUtils").typeToEncType(o);if(o&d("WAProtoConst").FLAGS.PACKED){if(l.length>0){a.writeVarInt(k);var r=n[p];r(a,l,q)}}else if(o&d("WAProtoConst").FLAGS.REPEATED)for(r=0;r<l.length;r++){a.writeVarInt(k);o=m[p];o(a,l[r],q)}else if(p===d("WAProtoConst").TYPES.MAP)for(o of l){r=o[0];var s=o[1];a.writeVarInt(k);var t=q[0],u=q[1],v=typeof u==="object"?d("WAProtoConst").TYPES.MESSAGE:u,w=m[t],x=m[v];if(w==null||x==null)throw new Error("Invalid encoder for map key/value");var y=new(d("WABinary").Binary)(),z=1*8|d("WAProtoUtils").typeToEncType(t);y.writeVarInt(z);w(y,r,t);z=2*8|d("WAProtoUtils").typeToEncType(v);y.writeVarInt(z);x(y,s,u);a.writeVarInt(y.size());a.writeBinary(y)}else{a.writeVarInt(k);w=m[p];w(a,l,q)}}}}g.encodeProtobuf=a;g.encodeErrorInfo=b}),98);
__d("WAMediaUtils",["WACryptoDependencies","WADirectPath","WAGlobals","WALongInt","WAMediaEntryData.pb","WAProgressiveJpegGetScanLengths","WAResultOrError","WAServerMediaType","WATimeUtils","decodeProtobuf","encodeProtobuf","err"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=new Uint8Array(32);d("WACryptoDependencies").getCrypto().getRandomValues(a);return a.buffer}function i(a){return a}function a(){return h()}function j(a){return a}function k(a){return a}function b(a){return a}function e(a){return a}function f(a,b){a=d("decodeProtobuf").decodeProtobuf(d("WAMediaEntryData.pb").MediaEntrySpec,b);b=a.fileSha256;var c=a.mediaKey,e=a.fileEncSha256,f=a.directPath,g=a.mediaKeyTimestamp,h=a.sidecar,i=a.objectId,j=a.fbid,l=a.serverMediaType,m=a.downloadableThumbnail,n=a.filename,o=a.progressiveJpegDetails;a=a.size;f=d("WADirectPath").validateDirectPath(f);return{fileSha256:b,mediaKey:c,fileEncSha256:e,directPath:f.success?f.value:null,mediaKeyTimestamp:d("WALongInt").maybeNumberOrThrowIfTooLarge(g),sidecar:h&&k(h),objectId:i,fbid:j,downloadableThumbnail:m==null?void 0:{directPath:m.directPath,fileEncSha256:m.fileEncSha256,fileSha256:m.fileSha256,mediaKey:m.mediaKey,mediaKeyTimestamp:d("WALongInt").maybeNumberOrThrowIfTooLarge(m.mediaKeyTimestamp),objectId:m.objectId},serverMediaType:l,filename:n,progressiveJpegDetails:o&&s(o),size:a==null?void 0:d("WALongInt").numberOrThrowIfTooLarge(a)}}function l(a){var b=a.fileSha256,c=a.fileEncSha256,e=a.mediaKey,f=a.directPath,g=a.mediaKeyTimestamp,h=a.serverMediaType,i=a.objectId,j=a.fbid,k=a.downloadableThumbnail,l=a.filename,m=a.progressiveJpegDetails;a=a.size;b=d("encodeProtobuf").encodeProtobuf(d("WAMediaEntryData.pb").MediaEntrySpec,{fileSha256:b,fileEncSha256:c,mediaKey:e,directPath:f,mediaKeyTimestamp:g,serverMediaType:h,objectId:i,fbid:j,downloadableThumbnail:k,filename:l,progressiveJpegDetails:m,size:a});return b.readByteArray()}function m(a){return d("encodeProtobuf").encodeProtobuf(d("WAMediaEntryData.pb").MediaEntrySpec,a).readByteArray()}function n(a,b){return d("encodeProtobuf").encodeProtobuf(d("WAMediaEntryData.pb").MediaEntrySpec,babelHelpers["extends"]({},a,{directPath:b})).readByteArray()}function o(a,b){return d("encodeProtobuf").encodeProtobuf(d("WAMediaEntryData.pb").MediaEntrySpec,babelHelpers["extends"]({},a,{downloadableThumbnail:babelHelpers["extends"]({},a.downloadableThumbnail,{directPath:b})})).readByteArray()}function p(a,b){a=babelHelpers["extends"]({},a,{fbid:b});return d("encodeProtobuf").encodeProtobuf(d("WAMediaEntryData.pb").MediaEntrySpec,a).readByteArray()}function q(a,b){a=babelHelpers["extends"]({},a,{objectId:b});return d("encodeProtobuf").encodeProtobuf(d("WAMediaEntryData.pb").MediaEntrySpec,a).readByteArray()}function r(a){if(a==null)return null;a.$$unknownFieldCount;var b=a.directPath,c=a.objectId,e=babelHelpers.objectWithoutPropertiesLoose(a,["$$unknownFieldCount","directPath","objectId"]);b=d("WADirectPath").validateDirectPath(b);return babelHelpers["extends"]({},e,{directPath:b.success?b.value:null,objectId:c,mediaKey:a.mediaKey&&i(a.mediaKey),mediaKeyTimestamp:a.mediaKeyTimestamp==null?null:d("WATimeUtils").castToUnixTime(d("WALongInt").numberOrThrowIfTooLarge(a.mediaKeyTimestamp))})}function s(a){if(a==null)return null;return a.scanLengths==null||a.sidecar==null?null:{sidecar:a.sidecar,scanLengths:a.scanLengths.map(function(a){return d("WAProgressiveJpegGetScanLengths").asProgressiveJpegScanLength(d("WALongInt").numberOrThrowIfTooLarge(a))})}}function t(a){a=d("decodeProtobuf").decodeProtobuf(d("WAMediaEntryData.pb").MediaEntrySpec,a);var b=a.serverMediaType,c=a.mediaKey,e=a.mediaKeyTimestamp,f=a.uploadToken,g=a.downloadableThumbnail,h=a.progressiveJpegDetails,k=a.size,l=a.lastDownloadAttemptTimestamp,m=a.sidecar,n=a.directPath,o=a.objectId;a=babelHelpers.objectWithoutPropertiesLoose(a,["serverMediaType","mediaKey","mediaKeyTimestamp","uploadToken","downloadableThumbnail","progressiveJpegDetails","size","lastDownloadAttemptTimestamp","sidecar","directPath","objectId"]);var p=null;h!=null&&h.scanLengths!=null&&h.sidecar!=null&&(p={sidecar:h.sidecar,scanLengths:h.scanLengths.map(function(a){return d("WAProgressiveJpegGetScanLengths").asProgressiveJpegScanLength(d("WALongInt").numberOrThrowIfTooLarge(a))})});h=d("WADirectPath").validateDirectPath(n);return babelHelpers["extends"]({},a,{objectId:o,directPath:h.success?h.value:void 0,sidecar:m,serverMediaType:b==null?null:d("WAServerMediaType").castToServerMediaType(b),mediaKey:c&&i(c),mediaKeyTimestamp:e==null?null:d("WATimeUtils").castToUnixTime(d("WALongInt").numberOrThrowIfTooLarge(e)),uploadToken:f&&j(f),progressiveJpegDetails:p,downloadableThumbnail:g&&r(g),size:d("WALongInt").maybeNumberOrThrowIfTooLarge(k),lastDownloadAttemptTimestamp:l==null?null:d("WATimeUtils").castToUnixTime(d("WALongInt").numberOrThrowIfTooLarge(l))})}function u(a){var b=a.directPath,c=a.fileEncSha256,e=a.fileSha256,f=a.mediaKey,g=a.serverMediaType;if(f==null)return d("WAResultOrError").makeError("media-entry-invalid-media-key");else if(b==null)return d("WAResultOrError").makeError("media-entry-invalid-direct-path");else if(c==null)return d("WAResultOrError").makeError("media-entry-invalid-file-enc-sha256");else if(e==null)return d("WAResultOrError").makeError("media-entry-invalid-file-sha256");else if(g==null)return d("WAResultOrError").makeError("media-entry-invalid-server-media-type");return d("WAResultOrError").makeResult(babelHelpers["extends"]({},a,{directPath:b,fileEncSha256:c,fileSha256:e,mediaKey:f,serverMediaType:g}))}function v(a){var b=a.directPath,c=a.fileEncSha256,e=a.fileSha256,f=a.mediaKey,g=[];f==null?g.push("media-entry-invalid-media-key"):b==null?g.push("media-entry-invalid-direct-path"):c==null?g.push("media-entry-invalid-file-enc-sha256"):e==null&&g.push("media-entry-invalid-file-sha256");return f!=null&&b!=null&&c!=null&&e!=null?d("WAResultOrError").makeResult(babelHelpers["extends"]({},a,{directPath:b,fileEncSha256:c,fileSha256:e,mediaKey:f})):d("WAResultOrError").makeError(g)}function w(a){var b="image/jpeg";switch(a){case"image":b="image/jpeg";break;case"ptt":b="audio/wav";break;case"video":b="video/mp4";break;case"gif":b="image/gif";break;case"sticker":b="image/webp";break;case"preview":b="image/jpeg";break;case"document":b=d("WAGlobals").getConfig().isOctetStreamAttachmentMimeTypeEnabled()===!0?"application/octet-stream":"application/pdf";break;case"xma-image":b="image/jpeg";break;default:throw c("err")("Unsupported server media type: "+a)}return b}var x=function(){return"TransformStream"in self};g.createMediaKey=h;g.castToMediaKey=i;g.createUploadToken=a;g.castToUploadToken=j;g.castToStreamingSidecar=k;g.stringToDeliveryObjectId=b;g.stringToBackupEntFbid=e;g.mediaEntryDataToRawData=f;g.rawDataToMediaEntry=l;g.encodeMediaEntryForUpload=m;g.encodeMediaEntryWithUpdatedPath=n;g.encodeMediaEntryDownloadableThumbnailWithUpdatedPath=o;g.encodeMediaEntryWithUpdatedFbid=p;g.encodeMediaEntryWithUpdatedObjectId=q;g.convertMediaDownloadableThumbnailToDownloadableThumbnail=r;g.decodeMediaEntryData=t;g.validateDecodedMediaEntryForDownload=u;g.validateDownloadableThumbnailForDownload=v;g.getMimeTypeFromServerMediaType=w;g.isTransformStreamSupported=x}),98);
__d("MAWDbMedia",["I64","WAMediaUtils","WAResultOrError"],(function(a,b,c,d,e,f,g){"use strict";var h,i="Image",j="Video",k="Ptt",l="Gif",m="Sticker",n="DocumentFile",o={DOCUMENT_FILE:n,GIF:l,IMAGE:i,PTT:k,STICKER:m,VIDEO:j};function a(a){return a}function b(a){return a}function c(a){return(h||(h=d("I64"))).of_float(a)}function e(a){return(h||(h=d("I64"))).to_float(a)}function f(a,b){a=a.mediaEntries.get(b);if(a==null)return d("WAResultOrError").makeError("missing-media-entry");b=d("WAMediaUtils").decodeMediaEntryData(a);a=b.serverMediaType;return a==null?d("WAResultOrError").makeError("missing-server-media-type"):d("WAResultOrError").makeResult(a)}g.IMAGE=i;g.VIDEO=j;g.PTT=k;g.GIF=l;g.STICKER=m;g.DOCUMENT_FILE=n;g.MEDIA_TYPE=o;g.convertNumberToMediaId=a;g.convertToMediaId64=b;g.convertMediaIdToMediaId64=c;g.convertMediaId64ToMediaId=e;g.getServerMediaType=f}),98);
__d("MAWHexUtils",["FBLogger"],(function(a,b,c,d,e,f,g){"use strict";function a(a){a=a.toString(16);return(a.length-1).toString(16)+a}function b(a){for(var b=1;b<a.length;b++){var d=a.substring(0,b),e=a.substring(b);if(e.length-1===parseInt(d,16))return parseInt(e,16)}throw c("FBLogger")("messenger_web").mustfixThrow("Cannot convert the order-preserving-hex back to number")}g.orderPreservingHex=a;g.reverseOrderPreservingHex=b}),98);
__d("WAMsg",["WAAssertUnreachable","WAJids","WAMsgType","err"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=a.author,c=a.chat;a=a.externalId;return c+"_"+a+"_"+b}function h(a){switch(a.type){case d("WAMsgType").MSG_TYPE.TEXT:case d("WAMsgType").MSG_TYPE.IMAGE:case d("WAMsgType").MSG_TYPE.VIDEO:case d("WAMsgType").MSG_TYPE.PTT:case d("WAMsgType").MSG_TYPE.GIF:case d("WAMsgType").MSG_TYPE.STICKER:case d("WAMsgType").MSG_TYPE.RECEIVER_FETCH:return{author:a.id.author,deleteTs:a.deleteTs,ephemeralSetting:a.ephemeralSetting,expirationTs:a.expirationTs,externalId:a.id.externalId,sentTs:a.sentTs,serverTs:a.serverTs,ts:a.ts};case d("WAMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:case d("WAMsgType").MSG_TYPE.UNAVAILABLE:return{author:a.id.author,externalId:a.id.externalId,sentTs:a.sentTs,ts:a.ts};default:a.type;return null}}function b(a){var b=h(a);if(b==null)return null;switch(a.type){case d("WAMsgType").MSG_TYPE.TEXT:return babelHelpers["extends"]({},b,{msgContent:a.msgContent,type:d("WAMsgType").MSG_TYPE.TEXT});case d("WAMsgType").MSG_TYPE.IMAGE:return babelHelpers["extends"]({},b,{mediaId:a.mediaId,type:d("WAMsgType").MSG_TYPE.IMAGE});case d("WAMsgType").MSG_TYPE.VIDEO:return babelHelpers["extends"]({},b,{mediaId:a.mediaId,type:d("WAMsgType").MSG_TYPE.VIDEO});case d("WAMsgType").MSG_TYPE.PTT:return babelHelpers["extends"]({},b,{mediaId:a.mediaId,type:d("WAMsgType").MSG_TYPE.PTT});case d("WAMsgType").MSG_TYPE.STICKER:return babelHelpers["extends"]({},b,{mediaId:a.mediaId,type:d("WAMsgType").MSG_TYPE.STICKER});case d("WAMsgType").MSG_TYPE.RECEIVER_FETCH:return babelHelpers["extends"]({},b,{type:d("WAMsgType").MSG_TYPE.RECEIVER_FETCH});case d("WAMsgType").MSG_TYPE.GIF:return babelHelpers["extends"]({},b,{mediaId:a.mediaId,type:d("WAMsgType").MSG_TYPE.PTT});case d("WAMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:return babelHelpers["extends"]({},b,{type:d("WAMsgType").MSG_TYPE.EXPIRED_EPHEMERAL});case d("WAMsgType").MSG_TYPE.UNAVAILABLE:return babelHelpers["extends"]({},b,{type:d("WAMsgType").MSG_TYPE.UNAVAILABLE});case d("WAMsgType").MSG_TYPE.REVOKED:case d("WAMsgType").MSG_TYPE.REACTION:case d("WAMsgType").MSG_TYPE.ICDC_ALERT:case d("WAMsgType").MSG_TYPE.ADMIN:case d("WAMsgType").MSG_TYPE.FUTUREPROOF:case d("WAMsgType").MSG_TYPE.CIPHERTEXT:case d("WAMsgType").MSG_TYPE.EPHEMERAL_SETTING_ADMIN:case d("WAMsgType").MSG_TYPE.EPHEMERAL_SYNC_RESPONSE:case d("WAMsgType").MSG_TYPE.EPHEMERAL_SETTING_CHANGE_FROM_CURRENT_DEVICE:case d("WAMsgType").MSG_TYPE.GROUP_INVITE:case d("WAMsgType").MSG_TYPE.DELETE_FOR_ME:case d("WAMsgType").MSG_TYPE.DOCUMENT_FILE:case d("WAMsgType").MSG_TYPE.EDIT_ACTION:return null;default:return c("WAAssertUnreachable")(a.type)}}function e(a){if(d("WAJids").isAuthorSystem(a.author))throw c("err")("Cannot send @system messages");else return{author:a.author,chat:a.chat,externalId:a.externalId}}g.craftWAMsgIdString=a;g.asQuotedMsg=b;g.asProtocolMsgId=e}),98);
__d("MAWDbMsg",["FBLogger","I64","MAWDbChatId__UNSAFE_DO_NOT_USE","MAWHexUtils","MAWMsgType","WAMsg","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h,i;i=[(i=d("MAWMsgType")).MSG_TYPE.REVOKED,i.MSG_TYPE.EPHEMERAL_SYNC_RESPONSE,i.MSG_TYPE.EPHEMERAL_SETTING_CHANGE_FROM_CURRENT_DEVICE,i.MSG_TYPE.GROUP_INVITE,i.MSG_TYPE.SK_DISTRIBUTION,i.MSG_TYPE.RAVEN_ACTION];var j=0,k=Number.MAX_SAFE_INTEGER;function a(a){return a}function b(a){return a}function e(a){return y(a)?a:null}function f(a){return a}function l(a){return a}var m="futureproof",n="futureproof_spam",o="spam",p="toBeRead",q="e2ee_admin_msg",r="cutover_admin_msg";function s(a,b){return d("MAWHexUtils").orderPreservingHex(a)+"_"+d("MAWHexUtils").orderPreservingHex(b)+"_m"}function t(a,b,e){a=d("MAWHexUtils").orderPreservingHex(a)+"_"+d("MAWHexUtils").orderPreservingHex(b)+"_m";return!c("justknobx")._("621")?a:a+"_"+e.externalId}function u(a,b){return!c("justknobx")._("1903")?s(a,b):d("MAWHexUtils").orderPreservingHex(a)+"_"+d("MAWHexUtils").orderPreservingHex(b)+"_um"}function v(a,b){return!c("justknobx")._("1903")?s(a,b):d("MAWHexUtils").orderPreservingHex(a)+"_"+d("MAWHexUtils").orderPreservingHex(b)+"_r"}function w(a,b,c){c=c!=null?c:d("MAWHexUtils").orderPreservingHex(Math.floor(Math.random()*1e4));return d("MAWHexUtils").orderPreservingHex(a)+"_"+d("MAWHexUtils").orderPreservingHex(b)+"_DEBUG_"+c+")}"}function x(a){a=a.split("_");return a.length>=3&&a[0]!==""&&a[1]!==""&&a[2]==="m"}function y(a){var b=/^(.*)\..*?$/;b=b.test(a);return x(a)||b}function z(a){return d("MAWDbChatId__UNSAFE_DO_NOT_USE").craftAltIndex__DEPRECATED(a,p)}function A(a){return d("MAWDbChatId__UNSAFE_DO_NOT_USE").craftAltIndex__DEPRECATED(a,q)}function B(a){return d("MAWDbChatId__UNSAFE_DO_NOT_USE").craftAltIndex__DEPRECATED(a,r)}function C(a){if(!x(a))return 0;a=a.split("_");a[0];a=a[1];return d("MAWHexUtils").reverseOrderPreservingHex(a)}function D(a){return d("MAWHexUtils").orderPreservingHex(a)+"_"}function E(a){return d("MAWHexUtils").orderPreservingHex(a)+"_z"}function F(a){switch(a.type){case d("MAWMsgType").MSG_TYPE.XMA:return!0;case d("MAWMsgType").MSG_TYPE.IMAGE:case d("MAWMsgType").MSG_TYPE.VIDEO:case d("MAWMsgType").MSG_TYPE.PTT:case d("MAWMsgType").MSG_TYPE.GIF:case d("MAWMsgType").MSG_TYPE.STICKER:case d("MAWMsgType").MSG_TYPE.DOCUMENT_FILE:case d("MAWMsgType").MSG_TYPE.TEXT:case d("MAWMsgType").MSG_TYPE.FUTUREPROOF:case d("MAWMsgType").MSG_TYPE.CIPHERTEXT:case d("MAWMsgType").MSG_TYPE.UNAVAILABLE:case d("MAWMsgType").MSG_TYPE.ADMIN:case d("MAWMsgType").MSG_TYPE.REVOKED:case d("MAWMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_ADMIN:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SYNC_RESPONSE:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SCREENSHOT_ACTION:case d("MAWMsgType").MSG_TYPE.ICDC_ALERT:case d("MAWMsgType").MSG_TYPE.GROUP_INVITE:case d("MAWMsgType").MSG_TYPE.RAVEN:case d("MAWMsgType").MSG_TYPE.BUMP_EXISTING_MESSAGE:case d("MAWMsgType").MSG_TYPE.RECEIVER_FETCH:case d("MAWMsgType").MSG_TYPE.GROUP_POLL_CREATE:case d("MAWMsgType").MSG_TYPE.GROUP_POLL_UPDATE:return!1}throw c("FBLogger")("messenger_web").mustfixThrow('Invalid msg type for isXMAMsg check: "%s"',a.type)}function G(a){switch(a.type){case d("MAWMsgType").MSG_TYPE.IMAGE:case d("MAWMsgType").MSG_TYPE.VIDEO:case d("MAWMsgType").MSG_TYPE.PTT:case d("MAWMsgType").MSG_TYPE.GIF:case d("MAWMsgType").MSG_TYPE.STICKER:case d("MAWMsgType").MSG_TYPE.DOCUMENT_FILE:case d("MAWMsgType").MSG_TYPE.RAVEN:return!0;case d("MAWMsgType").MSG_TYPE.TEXT:case d("MAWMsgType").MSG_TYPE.FUTUREPROOF:case d("MAWMsgType").MSG_TYPE.CIPHERTEXT:case d("MAWMsgType").MSG_TYPE.UNAVAILABLE:case d("MAWMsgType").MSG_TYPE.ADMIN:case d("MAWMsgType").MSG_TYPE.REVOKED:case d("MAWMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_ADMIN:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SYNC_RESPONSE:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SCREENSHOT_ACTION:case d("MAWMsgType").MSG_TYPE.ICDC_ALERT:case d("MAWMsgType").MSG_TYPE.GROUP_INVITE:case d("MAWMsgType").MSG_TYPE.XMA:case d("MAWMsgType").MSG_TYPE.BUMP_EXISTING_MESSAGE:case d("MAWMsgType").MSG_TYPE.RECEIVER_FETCH:case d("MAWMsgType").MSG_TYPE.GROUP_POLL_CREATE:case d("MAWMsgType").MSG_TYPE.GROUP_POLL_UPDATE:return!1}throw c("FBLogger")("messenger_web").mustfixThrow('Invalid msg type for isMediaMsg check: "%s"',a.type)}function H(a){return a.type===d("MAWMsgType").MSG_TYPE.RECEIVER_FETCH}function I(a){switch(a.type){case d("MAWMsgType").MSG_TYPE.IMAGE:case d("MAWMsgType").MSG_TYPE.VIDEO:return!0;case d("MAWMsgType").MSG_TYPE.PTT:case d("MAWMsgType").MSG_TYPE.GIF:case d("MAWMsgType").MSG_TYPE.STICKER:case d("MAWMsgType").MSG_TYPE.DOCUMENT_FILE:case d("MAWMsgType").MSG_TYPE.RAVEN:case d("MAWMsgType").MSG_TYPE.TEXT:case d("MAWMsgType").MSG_TYPE.FUTUREPROOF:case d("MAWMsgType").MSG_TYPE.CIPHERTEXT:case d("MAWMsgType").MSG_TYPE.UNAVAILABLE:case d("MAWMsgType").MSG_TYPE.ADMIN:case d("MAWMsgType").MSG_TYPE.REVOKED:case d("MAWMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_ADMIN:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SYNC_RESPONSE:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SCREENSHOT_ACTION:case d("MAWMsgType").MSG_TYPE.ICDC_ALERT:case d("MAWMsgType").MSG_TYPE.GROUP_INVITE:case d("MAWMsgType").MSG_TYPE.XMA:case d("MAWMsgType").MSG_TYPE.BUMP_EXISTING_MESSAGE:case d("MAWMsgType").MSG_TYPE.RECEIVER_FETCH:case d("MAWMsgType").MSG_TYPE.GROUP_POLL_CREATE:return!1;default:c("FBLogger")("messenger_web").mustfix('Invalid msg type for isPhotoOrVideoMediaMsg check: "%s"',a.type);return!1}}function J(a){switch(a.type){case d("MAWMsgType").MSG_TYPE.DOCUMENT_FILE:return!0;case d("MAWMsgType").MSG_TYPE.IMAGE:case d("MAWMsgType").MSG_TYPE.VIDEO:case d("MAWMsgType").MSG_TYPE.PTT:case d("MAWMsgType").MSG_TYPE.GIF:case d("MAWMsgType").MSG_TYPE.STICKER:case d("MAWMsgType").MSG_TYPE.RAVEN:case d("MAWMsgType").MSG_TYPE.TEXT:case d("MAWMsgType").MSG_TYPE.FUTUREPROOF:case d("MAWMsgType").MSG_TYPE.CIPHERTEXT:case d("MAWMsgType").MSG_TYPE.UNAVAILABLE:case d("MAWMsgType").MSG_TYPE.ADMIN:case d("MAWMsgType").MSG_TYPE.REVOKED:case d("MAWMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_ADMIN:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SYNC_RESPONSE:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SCREENSHOT_ACTION:case d("MAWMsgType").MSG_TYPE.ICDC_ALERT:case d("MAWMsgType").MSG_TYPE.GROUP_INVITE:case d("MAWMsgType").MSG_TYPE.XMA:case d("MAWMsgType").MSG_TYPE.BUMP_EXISTING_MESSAGE:case d("MAWMsgType").MSG_TYPE.RECEIVER_FETCH:case d("MAWMsgType").MSG_TYPE.GROUP_POLL_CREATE:return!1;default:c("FBLogger")("messenger_web").mustfix('Invalid msg type for isDocumentFileMediaMsg check: "%s"',a.type);return!1}}function K(a){switch(a.type){case d("MAWMsgType").MSG_TYPE.TEXT:case d("MAWMsgType").MSG_TYPE.IMAGE:case d("MAWMsgType").MSG_TYPE.VIDEO:case d("MAWMsgType").MSG_TYPE.PTT:case d("MAWMsgType").MSG_TYPE.GIF:case d("MAWMsgType").MSG_TYPE.STICKER:case d("MAWMsgType").MSG_TYPE.DOCUMENT_FILE:case d("MAWMsgType").MSG_TYPE.REVOKED:case d("MAWMsgType").MSG_TYPE.DELETE_FOR_ME:case d("MAWMsgType").MSG_TYPE.XMA:case d("MAWMsgType").MSG_TYPE.RAVEN:case d("MAWMsgType").MSG_TYPE.BUMP_EXISTING_MESSAGE:case d("MAWMsgType").MSG_TYPE.RECEIVER_FETCH:case d("MAWMsgType").MSG_TYPE.GROUP_POLL_CREATE:return!0;case d("MAWMsgType").MSG_TYPE.FUTUREPROOF:case d("MAWMsgType").MSG_TYPE.CIPHERTEXT:case d("MAWMsgType").MSG_TYPE.UNAVAILABLE:case d("MAWMsgType").MSG_TYPE.ADMIN:case d("MAWMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_ADMIN:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SYNC_RESPONSE:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SCREENSHOT_ACTION:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_CHANGE_FROM_CURRENT_DEVICE:case d("MAWMsgType").MSG_TYPE.ICDC_ALERT:case d("MAWMsgType").MSG_TYPE.GROUP_INVITE:case d("MAWMsgType").MSG_TYPE.SK_DISTRIBUTION:case d("MAWMsgType").MSG_TYPE.RAVEN_ACTION:case d("MAWMsgType").MSG_TYPE.EDIT_ACTION:return!1}throw c("FBLogger")("messenger_web").mustfixThrow('Invalid msg type for isContentMsg check: "%s"',a.type)}function L(a){switch(a.type){case d("MAWMsgType").MSG_TYPE.TEXT:case d("MAWMsgType").MSG_TYPE.IMAGE:case d("MAWMsgType").MSG_TYPE.VIDEO:case d("MAWMsgType").MSG_TYPE.PTT:case d("MAWMsgType").MSG_TYPE.GIF:case d("MAWMsgType").MSG_TYPE.STICKER:case d("MAWMsgType").MSG_TYPE.DOCUMENT_FILE:case d("MAWMsgType").MSG_TYPE.XMA:case d("MAWMsgType").MSG_TYPE.RAVEN:case d("MAWMsgType").MSG_TYPE.BUMP_EXISTING_MESSAGE:case d("MAWMsgType").MSG_TYPE.RECEIVER_FETCH:return!0;case d("MAWMsgType").MSG_TYPE.FUTUREPROOF:case d("MAWMsgType").MSG_TYPE.CIPHERTEXT:case d("MAWMsgType").MSG_TYPE.UNAVAILABLE:case d("MAWMsgType").MSG_TYPE.ADMIN:case d("MAWMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_ADMIN:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SYNC_RESPONSE:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SCREENSHOT_ACTION:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_CHANGE_FROM_CURRENT_DEVICE:case d("MAWMsgType").MSG_TYPE.ICDC_ALERT:case d("MAWMsgType").MSG_TYPE.GROUP_INVITE:case d("MAWMsgType").MSG_TYPE.REVOKED:case d("MAWMsgType").MSG_TYPE.DELETE_FOR_ME:case d("MAWMsgType").MSG_TYPE.SK_DISTRIBUTION:case d("MAWMsgType").MSG_TYPE.EDIT_ACTION:case d("MAWMsgType").MSG_TYPE.GROUP_POLL_CREATE:return!1}throw c("FBLogger")("messenger_web").mustfixThrow('Invalid msg type for isMsgEligibleForTriggeringEphemeralSyncResponse check: "%s"',a.type)}function M(a){return O(a.type)}function N(a){return a.type===d("MAWMsgType").MSG_TYPE.ADMIN}function O(a){return a==="EphemeralSyncResponse"||a==="EphemeralSettingChangeFromCurrentDevice"||a==="GroupInvite"||a==="SenderKeyDistribution"||a==="RavenAction"||a==="EditAction"}function P(a){return a==="RavenAction"}function Q(a){return a.originalTs!=null?a.originalTs:R(a)}function R(a){return a.serverTs!=null?a.serverTs:a.ts}function S(a){if(a.sortOrderMs!=null)return a.sortOrderMs;else return R(a)*1e3}function T(a,b){if(a.serverTs!=null)return Math.max(b+1,a.serverTs*1e3);else return b+1}function U(a){switch(a.type){case d("MAWMsgType").MSG_TYPE.TEXT:return a.msgContent;case d("MAWMsgType").MSG_TYPE.EDIT_ACTION:return a.editMsgContent;case d("MAWMsgType").MSG_TYPE.IMAGE:case d("MAWMsgType").MSG_TYPE.VIDEO:case d("MAWMsgType").MSG_TYPE.PTT:case d("MAWMsgType").MSG_TYPE.GIF:case d("MAWMsgType").MSG_TYPE.STICKER:case d("MAWMsgType").MSG_TYPE.DOCUMENT_FILE:case d("MAWMsgType").MSG_TYPE.REVOKED:case d("MAWMsgType").MSG_TYPE.DELETE_FOR_ME:case d("MAWMsgType").MSG_TYPE.FUTUREPROOF:case d("MAWMsgType").MSG_TYPE.CIPHERTEXT:case d("MAWMsgType").MSG_TYPE.UNAVAILABLE:case d("MAWMsgType").MSG_TYPE.ADMIN:case d("MAWMsgType").MSG_TYPE.EXPIRED_EPHEMERAL:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_ADMIN:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SYNC_RESPONSE:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SCREENSHOT_ACTION:case d("MAWMsgType").MSG_TYPE.EPHEMERAL_SETTING_CHANGE_FROM_CURRENT_DEVICE:case d("MAWMsgType").MSG_TYPE.ICDC_ALERT:case d("MAWMsgType").MSG_TYPE.GROUP_INVITE:case d("MAWMsgType").MSG_TYPE.XMA:case d("MAWMsgType").MSG_TYPE.RAVEN:case d("MAWMsgType").MSG_TYPE.BUMP_EXISTING_MESSAGE:case d("MAWMsgType").MSG_TYPE.RECEIVER_FETCH:case d("MAWMsgType").MSG_TYPE.GROUP_POLL_CREATE:return}throw c("FBLogger")("messenger_web").mustfixThrow('Invalid msg type for getMsgContent check: "%s"',a.type)}function V(a){return d("WAMsg").craftWAMsgIdString({author:a.author,chat:a.threadJid,externalId:a.externalId})}function W(a){return(h||(h=d("I64"))).to_float(a)}g.NO_CONTENT_MESSAGE_TYPES_ALLOWLIST=i;g.MIN_MSG_SORT_ORDER=j;g.MAX_MSG_SORT_ORDER=k;g.toCachedMsgId__UNSAFE_DO_NOT_USE=a;g.toMsgId__UNSAFE_DO_NOT_USE=b;g.toMsgId=e;g.stanzaIdToMsgId=f;g.instamadilloMsgIdToMsgId=l;g.FUTUREPROOF_ALT_INDEX=m;g.FUTUREPROOF_SPAM_ALT_INDEX=n;g.SPAM_ALT_INDEX=o;g.craftMsgId__DEPRECATED=s;g.craftMsgIdV2=t;g.craftUnrenderedMsgId=u;g.craftReactionId=v;g.craftDebugMsgId=w;g.isValidMsgId=y;g.craftToBeReadAltIndex=z;g.craftE2eeAdminMsgAltIndex=A;g.craftCutoverAdminMsgAltIndex=B;g.getInChatMsgIdFromMsgId=C;g.msgIdsInChatLowerBound=D;g.msgIdsInChatUpperBound=E;g.isXMAMsg=F;g.isMediaMsg=G;g.isReceiverFetchStickerMsg=H;g.isPhotoOrVideoMediaMsg=I;g.isDocumentFileMediaMsg=J;g.isContentMsg=K;g.isMsgEligibleForTriggeringEphemeralSyncResponse=L;g.isUnrenderedMsg=M;g.isAdminMsg=N;g.isUnrenderedMsgType=O;g.isRavenActionMsgType=P;g.getOriginalTsFromMsg=Q;g.getCanonicalTsFromMsg=R;g.getSortOrderWithFallback=S;g.getSortOrderFromPrevious=T;g.getMsgContent=U;g.getWAMsgId=V;g.convertMsgRowId64=W}),98);
__d("MAWDefaultE2eeOneToOneEligibility",["LSContactBitOffset"],(function(a,b,c,d,e,f,g){"use strict";function a(a){a=a.maybeContact;a=a!=null&&d("LSContactBitOffset").has(83,a);return a}g.isContactDefaultE2eeOneToOneEligible=a}),98);
__d("MAWFrontendMediaUtils",["MAWDbMedia"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c){if(a==="image/gif")return{mediaType:d("MAWDbMedia").MEDIA_TYPE.GIF,serverMediaType:"gif"};if(a==="image/webp"&&b!==!0)return{mediaType:d("MAWDbMedia").MEDIA_TYPE.STICKER,serverMediaType:"sticker"};c=c!=null?c:a.split("/")[0];switch(c){case"image":if(b===!0)return{mediaType:d("MAWDbMedia").MEDIA_TYPE.IMAGE,serverMediaType:"xma-image"};else return{mediaType:d("MAWDbMedia").MEDIA_TYPE.IMAGE,serverMediaType:"image"};case"video":return{mediaType:d("MAWDbMedia").MEDIA_TYPE.VIDEO,serverMediaType:"video"};case"audio":return{mediaType:d("MAWDbMedia").MEDIA_TYPE.PTT,serverMediaType:"ptt"};case"application":default:return{mediaType:d("MAWDbMedia").MEDIA_TYPE.DOCUMENT_FILE,serverMediaType:"document"}}}g.getMediaTypeAndServerMediaTypeFromBlob=a}),98);
__d("MAWMediaPreProcessQpl",["MAWSendQplToServer","Random","justknobx","qpl"],(function(a,b,c,d,e,f,g){"use strict";var h=typeof self.BigInt==="function",i=c("justknobx")._("3427")*1e3;function a(a,b){var e=Date.now()+(Math.round(d("Random").random()*1e4)+1e4);a=d("MAWSendQplToServer").startQplUserFlow(c("qpl")._(1056840460,"718"),{bool:{has_big_int_support:h},string:{preprocess_type:a}},e,i);b&&a.addAnnotations({"int":{input_file_size:b.size},string:{mimetype:b.type}});return a}g.startMediaPreProcessQpl=a}),98);
__d("WABlobToArrayBuffer",["Promise","err"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){return"arrayBuffer"in a&&typeof a.arrayBuffer==="function"?a.arrayBuffer():new(h||(h=b("Promise")))(function(b,d){var e=new FileReader();e.onload=function(){var a=e.result;a instanceof ArrayBuffer?b(a):d(c("err")("blobToArrayBuffer got error because result is not ArrayBuffer: "+typeof a))};e.onerror=function(){var a;d((a=e.error)!=null?a:c("err")("blobToArrayBuffer got unknown error"))};e.readAsArrayBuffer(a)})}g.blobToArrayBuffer=a}),98);
__d("WABlobToImage",["Promise"],(function(a,b,c,d,e,f){"use strict";var g;function a(a){var c;return new(g||(g=b("Promise")))(function(b,d){c=new Image();var e=URL.createObjectURL(a),f=function(){return URL.revokeObjectURL(e)};c.onload=function(){b({image:c,releaseImageMemory:f})};c.onerror=function(){f(),d("loadBlobToImage error")};c.src=e})}f.blobToImage=a}),66);
__d("WACalculateBoundOutputSize",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){return b!=null?g(a.width,a.height,b.width,b.height):{outputWidth:a.width,outputHeight:a.height}}function g(a,b,c,d){var e=a,f=b;a>b?a>c&&(f*=c/e,e=c):f>d&&(e*=d/f,f=d);return{outputWidth:Math.round(Math.max(e,1)),outputHeight:Math.round(Math.max(f,1))}}f.calculateBoundOutputSize=a}),66);
__d("WADecodeImage",["WABlobToImage","WACalculateBoundOutputSize","WATagsLogger","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a=babelHelpers.taggedTemplateLiteralLoose(["start decoding image with DOM: ",""]);h=function(){return a};return a}function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["start decoding image without DOM: ",""]);i=function(){return a};return a}var j=d("WATagsLogger").TAGS(["decodeImage"]);e=typeof self.createImageBitmap==="function"&&typeof self.OffscreenCanvas==="function";f=e?a:c;function a(a,b){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){j.DEV(i(),a.name);a=(yield self.createImageBitmap(a));var c=a.width,e=a.height;b=d("WACalculateBoundOutputSize").calculateBoundOutputSize({width:c,height:e},b);var f=b.outputWidth;b=b.outputHeight;var g=new self.OffscreenCanvas(f,b);g=m({canvas:g,inputWidth:c,inputHeight:e,outputWidth:f,outputHeight:b,image:a});return g});return k.apply(this,arguments)}function c(a,b){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){j.DEV(h(),a.name);a=(yield d("WABlobToImage").blobToImage(a));var c=a.image;a=a.releaseImageMemory;var e=c.width,f=c.height;b=d("WACalculateBoundOutputSize").calculateBoundOutputSize({width:e,height:f},b);var g=b.outputWidth;b=b.outputHeight;var i=document.createElement("canvas");i=m({canvas:i,inputWidth:e,inputHeight:f,outputWidth:g,outputHeight:b,image:c});a();return i});return l.apply(this,arguments)}function m(a){var b=a.canvas,c=a.image,d=a.inputWidth,e=a.inputHeight,f=a.outputWidth;a=a.outputHeight;b.width=f;b.height=a;var g=b.getContext("2d");g.fillStyle="rgb(255,255,255)";g.fillRect(0,0,b.width,b.height);g.drawImage(c,0,0,d,e,0,0,f,a);b=g.getImageData(0,0,f,a);return b}g.canDecodeWithoutDOM=e;g.decodeImage=f;g.decodeImageWithoutDOM=a;g.decodeImageWithDOM=c}),98);
__d("WAPromiseTimeout",["Promise","WACustomError"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(a,c,e){var f=null,g=new(h||(h=b("Promise")))(function(a,b){f=setTimeout(function(){b(new(d("WACustomError").TimeoutError)(e)),clearTimeout(f)},c)});return h.race([a,g])["finally"](function(){clearTimeout(f)})};g.promiseTimeout=a}),98);
__d("XCometFBMultiSiteWebWorkerInitScriptControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/static_resources/webworker_v1/init_script/",Object.freeze({}),void 0);b=a;g["default"]=b}),98);
__d("WorkerBundleResource",["TrustedTypesWebWorkerScriptURLPolicy","XCometFBMultiSiteWebWorkerInitScriptControllerRouteBuilder","getWorkerInitScriptSPINParams","nullthrows"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){var d,e,f,g,i=(d=b==null?void 0:b.logImportScriptsErrors)!=null?d:!1,j=(e=b==null?void 0:b.name)!=null?e:a.name,k=new window.URL(a.url,window.location.href).href;if(k==null){var l=new Error("Can't start up worker without a resource url.");l.stack;throw l}var m=c("nullthrows")((f=c("XCometFBMultiSiteWebWorkerInitScriptControllerRouteBuilder").buildUri({}).addQueryParams(c("getWorkerInitScriptSPINParams")()))==null?void 0:(g=f.addQueryParams(h()))==null?void 0:g.toString()),n=new Worker(c("TrustedTypesWebWorkerScriptURLPolicy").createScriptURL(m),{name:j});for(var o=arguments.length,p=new Array(o>2?o-2:0),q=2;q<o;q++)p[q-2]=arguments[q];n.postMessage({bundleUrl:k,initArgs:p,isDev:!1,resource:a,logImportScriptsErrors:i,type:"sr-init"});return n}function h(){var a=new URLSearchParams(window.location.search),b=new Map();a.forEach(function(a,c){["gk_enable","gk_enable[]","gk_disable","gk_disable[]"].includes(c)&&b.set(c,a)});return b}g.createDedicatedWebWorker=a}),98);
__d("WAMediaWasmWorkerClient",["Promise","WAByteArray","WACustomError","WAMediaWasmWorkerResource","WAPromiseTimeout","WAResultOrError","WATagsLogger","WorkerBundleResource","WorkerClient","WorkerMessagePort","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["media storage test failed for storageType: ",", error: ",""]);i=function(){return a};return a}function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["received storage results from worker, requestId: ",""]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["start media storage shadow test"]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["requestId: ",", error: ",""]);l=function(){return a};return a}function m(){var a=babelHelpers.taggedTemplateLiteralLoose(["received output from worker, requestId: ",", success: ",""]);m=function(){return a};return a}function n(){var a=babelHelpers.taggedTemplateLiteralLoose(["sending input to worker, requestId: ",""]);n=function(){return a};return a}function o(){var a=babelHelpers.taggedTemplateLiteralLoose(["requestId: ",", error: ",""]);o=function(){return a};return a}function p(){var a=babelHelpers.taggedTemplateLiteralLoose(["received output from worker, requestId: ",""]);p=function(){return a};return a}function q(){var a=babelHelpers.taggedTemplateLiteralLoose(["sending input bytes to worker, requestId: ",""]);q=function(){return a};return a}function r(){var a=babelHelpers.taggedTemplateLiteralLoose(["worker-connection-runtime-error: ",""]);r=function(){return a};return a}function s(){var a=babelHelpers.taggedTemplateLiteralLoose(["start prewarming media wasm worker, operation: ",""]);s=function(){return a};return a}function t(){var a=babelHelpers.taggedTemplateLiteralLoose(["mp4RepairMuxInWorker processing video"]);t=function(){return a};return a}function u(){var a=babelHelpers.taggedTemplateLiteralLoose(["mp4CheckAndRepairInWorker processing video"]);u=function(){return a};return a}function v(){var a=babelHelpers.taggedTemplateLiteralLoose(["worker error: ",""]);v=function(){return a};return a}function w(){var a=babelHelpers.taggedTemplateLiteralLoose(["from worker: ",""]);w=function(){return a};return a}function a(){var b=babelHelpers.taggedTemplateLiteralLoose(["initiated."]);a=function(){return b};return b}var x=d("WATagsLogger").TAGS(["WAMediaWasmWorkerClient"]);c=d("WorkerBundleResource").createDedicatedWebWorker(c("WAMediaWasmWorkerResource"));var y=new(d("WorkerMessagePort").WorkerSyncedMessagePort)(c,"WAMediaWasmWorker");d("WorkerClient").init(y);x.DEV(a());y.addMessageListener("log",function(a){var b=a.logType;a=a.message;switch(b){case"dev":x.DEV(w(),a);break;case"error":x.ERROR(v(),a);break;default:b}});function e(a){var b=a.input;a=a.eventFlow;return F({operation:"webpCheck",input:d("WAByteArray").uint8ArrayToBuffer(b)},a)}function f(a){var b=a.input;a=a.eventFlow;x.LOG(u());return F({operation:"mp4CheckAndRepair",input:d("WAByteArray").uint8ArrayToBuffer(b)},a)}function z(a){var b=a.input;a=a.eventFlow;x.LOG(t());return F({operation:"mp4RepairMux",input:d("WAByteArray").uint8ArrayToBuffer(b)},a)}function A(a){var b=a.imageData,c=a.quality,e=a.useHdScanConfig;a=a.eventFlow;return F({operation:"progressiveJpegEncode",input:d("WAByteArray").uint8ArrayToBuffer(b.data),height:b.height,width:b.width,quality:c,useHdScanConfig:e},a)}function B(a){return C.apply(this,arguments)}function C(){C=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=a.file,c=a.quality,d=a.useHdScanConfig,e=a.eventFlow;a=a.maxOutputResolution;var f=(yield b.arrayBuffer());return F({operation:"progressiveJpegEncodeWithFile",input:f,fileName:b.name,fileType:b.type,quality:c,useHdScanConfig:d,maxOutputWidth:a==null?void 0:a.width,maxOutputHeight:a==null?void 0:a.height},e)});return C.apply(this,arguments)}function D(a){x.LOG(s(),a),y.postMessage({type:"prewarm",operation:a})}var E=function(){var a=0;return function(){return++a}}();function F(a,b){return G.apply(this,arguments)}function G(){G=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){var c=E();x.LOG(q(),c);var e=(yield H(b));if(!e.success)return e;e=e.value;var f=e.onMessageOnce("mediaOperationResponse",function(a){return a.requestId===c}),g=a.input?[a.input]:void 0;e.postMessage(babelHelpers["extends"]({},a,{requestId:c,type:"mediaOperationRequest"}),g);b==null?void 0:b.addPoint("sent_request_to_media_worker_start");e=(yield f);a=e.output;if(a.success){x.LOG(p(),c);b==null?void 0:b.addPoint("sent_request_to_media_worker_end",{bool:{isOpfsSyncSupported:a.value.isOpfsSyncSupported}});return d("WAResultOrError").makeResult(a.value.bytes)}x.ERROR(o(),c,a.error.errorMessage);b==null?void 0:b.addPoint("sent_request_to_media_worker_fail");return d("WAResultOrError").makeError(a.error.errorType)});return G.apply(this,arguments)}function H(a){a==null?void 0:a.addPoint("get_connected_worker_port_start");var b=3e4;return d("WAPromiseTimeout").promiseTimeout(y.fullyConnected,b).then(function(b){a==null?void 0:a.addPoint("get_connected_worker_port_end");return d("WAResultOrError").makeResult(b)})["catch"](function(b){a==null?void 0:a.addPoint("get_connected_worker_port_fail",{string:{workerPortFailure:b instanceof d("WACustomError").TimeoutError?"timeout":"runtime-error"}});if(b instanceof d("WACustomError").TimeoutError)return d("WAResultOrError").makeError("worker-connection-timeout");x.ERROR(r(),b);return d("WAResultOrError").makeError("worker-connection-runtime-error")})}function I(a){return J.apply(this,arguments)}function J(){J=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var c=a.file,e=a.maxDimension,f=a.thumbnailBlobByteSizeLimitBytes;a=a.thumbnailQualityPercentageWhenAboveByteSizeLimit;var g=E();x.LOG(n(),g);var i=(yield H());if(!i.success)return i;i=i.value;var j=i.onMessageOnce("mediaGenerateImageThumbnailResponse",function(a){return a.requestId===g}),k=(yield c.arrayBuffer()),o=k?[k]:void 0;i.postMessage({input:k,fileName:c.name,fileType:c.type,maxDimension:e,requestId:g,thumbnailBlobByteSizeLimitBytes:f,thumbnailQualityPercentageWhenAboveByteSizeLimit:a,type:"mediaGenerateImageThumbnailRequest"},o);i=(yield j);x.LOG(m(),g,i.output.success);if(i.output.success)return d("WAResultOrError").makeResult(i.output.value);x.ERROR(l(),g,i.output.error.errorMessage);return(h||(h=b("Promise"))).reject(i.output.error.errorMessage)});return J.apply(this,arguments)}function K(a,b){return L.apply(this,arguments)}function L(){L=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){x.LOG(k());var c=E(),e=(yield H());if(!e.success)return d("WAResultOrError").makeError({errorName:"port-init",errorMessage:e.error});e=e.value;var f=e.onMessageOnce("mediaStorageShadowTestResponse",function(a){return a.requestId===c});e.postMessage({input:a,requestId:c,storageType:b,type:"mediaStorageShadowTestRequest"});e=(yield f);a=e.output;if(a.success){x.LOG(j(),c);return d("WAResultOrError").makeResult(a.value)}x.ERROR(i(),b,a.error);return d("WAResultOrError").makeError({errorName:a.error.errorName,errorMessage:a.error.errorMessage})});return L.apply(this,arguments)}g.webpCheckInWorker=e;g.mp4CheckAndRepairInWorker=f;g.mp4RepairMuxInWorker=z;g.progressiveJpegEncodeInWorker=A;g.progressiveJpegEncodeInWorkerWithFile=B;g.prewarmMediaWasmWorker=D;g.generateImageThumbnailInWorker=I;g.runMediaStorageShadowTestWasmWorker=K}),98);
__d("MAWImagePreProcess",["MAWGetImageSpec","MAWImageUtils","MAWMediaPreProcessQpl","MWFBLogger","Promise","WABlobToArrayBuffer","WACustomError","WADecodeImage","WAMediaWasmWorkerClient","WAPromiseTimeout","WAResultOrError","asyncToGeneratorRuntime","cometIsMimeTypeForMedia","gkx","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(){var a=babelHelpers.taggedTemplateLiteralLoose(["finished jpeg transcoding through mozjpeg: "," bytes"]);i=function(){return a};return a}function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["start process image through wasm, file name: ",""]);j=function(){return a};return a}function k(){var a=babelHelpers.taggedTemplateLiteralLoose(["Error while transcoding image through browser api: ",""]);k=function(){return a};return a}function l(){var a=babelHelpers.taggedTemplateLiteralLoose(["start image transcode ",""]);l=function(){return a};return a}function m(){var a=babelHelpers.taggedTemplateLiteralLoose(["Error while processing url to thumbnail in worker"]);m=function(){return a};return a}function n(){var a=babelHelpers.taggedTemplateLiteralLoose(["finished image transcoding through browser api: "," bytes"]);n=function(){return a};return a}function o(){var a=babelHelpers.taggedTemplateLiteralLoose(["start image transcoding through browser api"]);o=function(){return a};return a}var p=12e4,q=d("MWFBLogger").MWMediaLogger.tags(["ImagePreProcess"]),r=85,s=["image/heic"];function e(a,e,f,g){var i;c("gkx")("9576")&&(g===!0?i={height:c("justknobx")._("187"),width:c("justknobx")._("187")}:i={height:c("justknobx")._("2144"),width:c("justknobx")._("2144")});e?g=a.type==="image/webp"?(h||(h=b("Promise"))).resolve(a):v(a,"image/webp",e,f,i):g=a.type==="image/gif"?(h||(h=b("Promise"))).resolve(a):v(a,"image/jpeg",!1,f,i);f=a.type==="image/gif"&&c("gkx")("8554");return f?g.then(function(a){return(h||(h=b("Promise"))).all([d("MAWGetImageSpec").getImageSpec(a),a])}).then(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){var c=b[0];b=b[1];return{file:b,imageSpec:c,plaintext:yield d("WABlobToArrayBuffer").blobToArrayBuffer(b),thumbnailUrlAndSpec:null,type:u(a,e)}});return function(a){return c.apply(this,arguments)}}()):g.then(function(e){return(h||(h=b("Promise"))).all([B(a,c("justknobx")._("2033")),d("MAWGetImageSpec").getImageSpec(e),e])}).then(function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(c){var f=c[0],g=c[1];c=c[2];return(h||(h=b("Promise"))).resolve({file:c,imageSpec:g,plaintext:yield d("WABlobToArrayBuffer").blobToArrayBuffer(c),thumbnailUrlAndSpec:f,type:u(a,e)})});return function(a){return c.apply(this,arguments)}}())}function f(a,b){return t.apply(this,arguments)}function t(){t=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b,e){var f=a.URL.createObjectURL(b),g=e===!0?c("justknobx")._("2545"):c("justknobx")._("2033");g=(yield d("MAWImageUtils").urlToThumbnailSpec(f,g));var h={height:g.height,width:g.width};return{file:b,imageSpec:h,thumbnailSpec:g,type:u(b,e),url:f}});return t.apply(this,arguments)}function u(a,b){return b===!0?"sticker":a.type==="image/gif"?"animated_image":"image"}function v(a,b,c,d,e){return w.apply(this,arguments)}function w(){w=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,c,e,f,g){q.DEBUG(l(),a.name);var i=d("MAWMediaPreProcessQpl").startMediaPreProcessQpl("image",a);i.addAnnotations({"int":{total_number_of_files:f!=null?f:1},string:{targetMimetype:c}});var j=function(){return y(a,c,r,e,g).then(function(a){return a.success?a.value:(h||(h=b("Promise"))).reject("runtime-error")})["catch"](function(a){q.MUSTFIX(k(),String(a));i.addPoint("native_encode_failure",{string:{native_encode_error:a.toString()}});return null})};f=c==="image/jpeg"?yield d("WAPromiseTimeout").promiseTimeout(z(a,r,i,g).then(function(a){return a.success?a.value:(h||(h=b("Promise"))).reject(a.error)}),p)["catch"](function(a){a instanceof d("WACustomError").TimeoutError?i.addPoint("mozjpeg_encode_timeout"):i.addPoint("mozjpeg_encode_failure",{string:{mozjpeg_encode_error:a.toString()}});return j()}):yield j();if(f==null){i.endFail("unable_to_transcode_image");throw q.mustfixThrow("unable to transcode image")}i.endSuccess({"int":{compressed_file_size:f.size}});return f});return w.apply(this,arguments)}function x(b,c){b=a.URL.createObjectURL(b);return d("MAWImageUtils").urlToThumbnail(b,c)}function y(b,c,e,f,g){q.DEBUG(o());var h=a.URL.createObjectURL(b),i=b.name;return d("MAWImageUtils").urlToFile(h,c,e,f,g).then(function(a){a=a.blob;q.DEBUG(n(),a.size);return d("WAResultOrError").makeResult(new File([a],i,{type:c}))})}function z(a,b,c,d){return A.apply(this,arguments)}function A(){A=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c,e){q.DEBUG(j(),a.name);d("WAMediaWasmWorkerClient").prewarmMediaWasmWorker("progressiveJpegEncode");c.addPoint("prewarm_worker_started");var f;d("WADecodeImage").canDecodeWithoutDOM&&(f=(yield d("WAMediaWasmWorkerClient").progressiveJpegEncodeInWorkerWithFile({eventFlow:c,file:a,maxOutputResolution:e,quality:b,useHdScanConfig:!0})));if(f==null||!f.success&&f.error==="decode-image-error"){e=(yield d("WADecodeImage").decodeImageWithDOM(a,e)["catch"](function(a){c.addPoint("image_decode_failure");throw a}));c.addPoint("image_decode_success");f=(yield d("WAMediaWasmWorkerClient").progressiveJpegEncodeInWorker({eventFlow:c,imageData:e,quality:b,useHdScanConfig:!0}))}if(!f.success)return f;c.addPoint("pjpeg_encode_success");q.DEBUG(i(),f.value.byteLength);return d("WAResultOrError").makeResult(new File([f.value],a.name,{type:"image/jpeg"}))});return A.apply(this,arguments)}function B(e,f){var g=function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var b=a.URL.createObjectURL(e),c=(yield d("MAWImageUtils").urlToThumbnail(b,f));a.URL.revokeObjectURL(b);return c});return function(){return c.apply(this,arguments)}}();if(!d("WADecodeImage").canDecodeWithoutDOM)return g();var h=c("justknobx")._("2338")*1024,i=c("justknobx")._("2340");return d("WAPromiseTimeout").promiseTimeout(d("WAMediaWasmWorkerClient").generateImageThumbnailInWorker({file:e,maxDimension:f,thumbnailBlobByteSizeLimitBytes:h,thumbnailQualityPercentageWhenAboveByteSizeLimit:i}).then(function(a){if(!a.success)throw q.mustfixThrow("urlToFileInWorker error: "+a.error);return{height:a.value.height,jpegThumbnail:a.value.blob,width:a.value.width}})["catch"](function(a){q.catching(a).MUSTFIX(m());throw a}),p)["catch"](function(a){return g()})}function C(a){if(a==="image/gif"||a==="image/webp")return!0;return!d("cometIsMimeTypeForMedia").isMimeTypeForPhoto(a)?!1:!s.includes(a)}g.imagePreprocess=e;g.optimisticImagePreprocess=f;g.getJpegThumbnailInfo=x;g.generateImageThumbnailInWorkerWithFallback=B;g.isSupportedImageFileType=C}),98);
__d("MAWImageUtils",["FBLogger","MAWAnimatedImageThumbnailStore","MAWDbMedia","MAWFrontendMediaUtils","MAWImagePreProcess","MWPBumpEntityKey","Promise","WABlobToArrayBuffer","WACalculateBoundOutputSize","cr:8676","err","gkx","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h,i="image/jpeg",j=100,k=c("justknobx")._("2338")*1024,l=c("justknobx")._("2340"),m=c("justknobx")._("2545");function n(a,d){d===void 0&&(d=!0);var e;return new(h||(h=b("Promise")))(function(b,f){e=new Image();var g=/^(?:data|blob):/.test(a);d&&!g&&e.setAttribute("crossOrigin","anonymous");e.onload=b;e.onabort=function(){f(function(){throw c("FBLogger")("messenger_web").mustfixThrow("Error loading image from provided URL")})};e.onerror=function(){f(function(){throw c("FBLogger")("messenger_web").mustfixThrow("Wrong blob type")})};e.src=a}).then(function(){return e})}function o(a,c,e,f){e===void 0&&(e=!1);if(a.toBlob)return new(h||(h=b("Promise")))(function(b){var d=f==null?void 0:f/100;a.toBlob(b,c,d)}).then(function(d){var f=k,g=l/100;return e&&d.size>=f?new(h||(h=b("Promise")))(function(b){a.toBlob(b,c,g)}):d});else{var g;d("MWPBumpEntityKey").bumpEntityKey("maw","data_url_to_blob");var i=(g=b("cr:8676")==null?void 0:b("cr:8676").dataURLtoBlob)!=null?g:window.dataURLtoBlob;return(h||(h=b("Promise"))).resolve(i(a.toDataURL(c))).then(function(d){var f=k,g=l/100;return e&&d.size>=f?(h||(h=b("Promise"))).resolve(i(a.toDataURL(c,g))):d})}}function p(a,e,f,g,i){return n(a).then(function(a){var b=a.naturalWidth,c=a.naturalHeight,h=d("WACalculateBoundOutputSize").calculateBoundOutputSize({height:c,width:b},i),j=h.outputHeight,k=h.outputWidth;h=document.createElement("canvas");h.width=k;h.height=j;var l=h.getContext("2d");g!==!0&&r(h);l.drawImage(a,0,0,b,c,0,0,k,j);return o(h,e,!1,f).then(function(a){return{blob:a,height:j,width:k}})})["catch"](function(a){return(h||(h=b("Promise"))).reject(a!=null?a:c("err")("Error loading image from URL"))})}function a(a,b){return n(a).then(function(a){var c=document.createElement("canvas"),e=a.naturalWidth,f=a.naturalHeight,g=c.getContext("2d");f=s(f,e,b);c.width=f.width;c.height=f.height;var h=c.width,j=c.height;q(c);g.drawImage(a,0,0,h,j);return o(c,i,!0).then(function(a){return d("WABlobToArrayBuffer").blobToArrayBuffer(a).then(function(a){return{height:j,jpegThumbnail:a,width:h}})})})}function e(a,b){return n(a).then(function(a){a=s(a.naturalHeight,a.naturalWidth,b);return{height:a.height,width:a.width}})}function q(a){var b=a.getContext("2d");b.fillStyle="rgb(247,247,247)";b.fillRect(0,0,a.width,a.height)}function r(a){var b=a.getContext("2d");b.fillStyle="rgb(255,255,255)";b.fillRect(0,0,a.width,a.height)}function s(a,b,c){b=b!=null?b:c;a=a!=null?a:c;b>a?b>c&&(a*=c/b,b=c):a>c&&(b*=c/a,a=c);return{height:Math.round(Math.max(a,1)),width:Math.round(Math.max(b,1))}}function f(a){return n(a).then(function(a){return{height:a.naturalHeight,width:a.naturalWidth}})}function t(a,e,f){var g=d("MAWAnimatedImageThumbnailStore").getThumbnailSpec(a);if(g)return(h||(h=b("Promise"))).resolve(g);g=p(a,"image/gif");return g==null||e==null||f==null?null:g.then(function(b){b=new File([b.blob],"gif",{type:"image/gif"});return d("MAWImagePreProcess").generateImageThumbnailInWorkerWithFallback(b,Math.max(f,e)).then(function(b){d("MAWAnimatedImageThumbnailStore").setThumbnailSpec(a,b);return b})})["catch"](function(a){throw c("FBLogger")("messenger_web_media").mustfixThrow("Failed while generating thumbnail for GIF",a)})}function u(a){var e=a==null?void 0:a.file,f=a==null?void 0:a.width,g=a==null?void 0:a.height;if(e==null||f==null||g==null)return(h||(h=b("Promise"))).resolve(a);var i=d("MAWFrontendMediaUtils").getMediaTypeAndServerMediaTypeFromBlob(e.type,!0);i=i.mediaType;return i!==d("MAWDbMedia").MEDIA_TYPE.GIF?(h||(h=b("Promise"))).resolve(a):c("gkx")("12431")?d("MAWImagePreProcess").generateImageThumbnailInWorkerWithFallback(e,Math.max(f,g)).then(function(a){var b=new File([a.jpegThumbnail],"secure_threads_xma_preview_thumbnail",{type:"image/jpeg"});return{file:b,height:a.height,width:a.width}}):(h||(h=b("Promise"))).resolve(a)}g.TYPE_JPEG=i;g.IMG_THUMB_MAX_EDGE=j;g.STICKER_THUMBNAIL_MAX_SIZE=m;g.canvasToBlob=o;g.urlToFile=p;g.urlToThumbnail=a;g.urlToThumbnailSpec=e;g.boundHeightWidth=s;g.urlToImageSpec=f;g.generateGIFThumbnail=t;g.generatePreviewBlobForShare=u}),98);
__d("MAWGetImageSpec",["MAWImageUtils","asyncToGeneratorRuntime"],(function(a,b,c,d,e,f,g){"use strict";function c(b,c){var e=a.URL.createObjectURL(b);return d("MAWImageUtils").urlToFile(e,c!=null?c:b.type).then(function(a){var b=a.height;a=a.width;return{height:b,width:a}})}function e(a){return h.apply(this,arguments)}function h(){h=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){b=a.URL.createObjectURL(b);var c=(yield d("MAWImageUtils").urlToImageSpec(b));a.URL.revokeObjectURL(b);return c});return h.apply(this,arguments)}g.getImageSpec_DEPRECATED=c;g.getImageSpec=e}),98);
__d("MAWIsFbProfileDirectoryItemXmaEnabled",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("15740")}g.isFbProfileDirectoryItemXmaEnabled=a}),98);
__d("MAWIsLiveLocationXmaEnabled",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("3758")===!0}g.isLiveLocationXmaEnabled=a}),98);
__d("MAWIsMemoriesXmaEnabled",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("1063")}g.isMemoriesXmaEnabled=a}),98);
__d("MAWIsRTCCallXmaEnabled",["qex"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("qex")._("865")===!0||c("qex")._("675")===!0&&c("qex")._("871")===!0}g.isRTCCallXmaEnabled=a}),98);
__d("WALRUMap",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a){a=a.max;this.$1=new Map();this.$2=a}var b=a.prototype;b.has=function(a){return this.$1.has(a)};b.get=function(a){return this.$1.get(a)};b.set=function(a,b){this.$1["delete"](a);this.$1.set(a,b);if(this.$1.size>this.$2){a=this.$1.keys().next().value;a!=null&&this.$1["delete"](a)}};b["delete"]=function(a){this.$1["delete"](a)};b.clear=function(){this.$1.clear()};b.size=function(){return this.$1.size};return a}();f.LRUMap=a}),66);
__d("WARandomHex",["WACryptoDependencies","WAHex"],(function(a,b,c,d,e,f,g){"use strict";function a(a){a=new Uint8Array(a);d("WACryptoDependencies").getCrypto().getRandomValues(a);return d("WAHex").toHex(a)}g.randomHex=a}),98);
__d("MAWJobActionsV2",["MAWTransactionMode","Promise","WALRUMap","WARandomHex","WATimeUtils","emptyFunction"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a){return new(h||(h=b("Promise")))(function(b,c){a.onsuccess=b,a.onerror=c})}function j(a){return i(a).then(function(a){return a.target.result})}function k(a,c,e){return new(h||(h=b("Promise")))(function(b,f){var g=a(d("MAWTransactionMode").READONLY).index(c);g=g.openCursor(IDBKeyRange.only(e));var h=[];g.onsuccess=function(a){a=a.target.result;if(!a){b(h);return}h.push(a.value);a["continue"]()};g.onerror=function(a){return f(a)}})}var l=new(d("WALRUMap").LRUMap)({max:100});function a(a,c){var e=c.args,f=c.scheduleConfig,g=c.type,i=c.uniqKey;c=c.version;c=c===void 0?1:c;var l=JSON.stringify([g,i!=null?i:d("WARandomHex").randomHex(32)]),n={backedOffCount:0,current:e,original:e,scheduleConfig:f,startTime:d("WATimeUtils").unixTime(),step:"$unstarted",stepFirstStartTime:null,stepHardStartCountAfterTimeout:0,stepUnexpectedErrorCount:0,type:g,uniqKey:l,version:c,waitUntil:null},o=function(){var b,c=a().add(n),d=j(c);if(n.uniqKey==null&&typeof ((b=c.transaction)==null?void 0:b.commit)==="function")try{c.transaction.commit()}catch(a){}return d.then(function(a){return{id:a,newlyCreated:!0}})};if(i!=null)return k(a,"uniqKey",l).then(function(c){if(c.length===0)return o();var d=[],e=null;c.forEach(function(b){b.step!=="$finished"?e=b:d.push(m(a,b.jobId))});return(h||(h=b("Promise"))).all(d).then(function(){return e!=null?{id:e.jobId,newlyCreated:!1}:o()})});else return o()}function m(a,b){return j(a()["delete"](b))}function e(a,b){return j(a().get(b))}function f(a,b){return j(a().put(b))}function n(a){return j(a().getAll())}function o(a){try{return i(a().clear()).then(c("emptyFunction"))}catch(a){if(a.name==="NotFoundError")return(h||(h=b("Promise"))).resolve();throw a}}g.recentFinishedJobsCache=l;g.maybeCreateJob=a;g.deletePersistedJob=m;g.readPersistedJob=e;g.updatePersistedJob=f;g.readAllPersistedJobs=n;g.clearJobs=o}),98);
__d("MAWMessagesDirection",["I64","err"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a){return c("err")("Unhandled direction: "+a)}function a(a,b){return k(a,{asc:b.maxTimestampMs,desc:b.minTimestampMs})}function b(a,b){return k(a,{asc:b.maxMessageId,desc:b.minMessageId})}function e(a,b){return b==="desc"&&(h||(h=d("I64"))).equal(a.maxTimestampMs,(h||(h=d("I64"))).max_int)&&a.minMessageId===a.maxMessageId&&a.hasMoreBefore&&!a.hasMoreAfter}function f(a){return k(a,{asc:"after",desc:"before"})}function j(a){return l(a,{after:"asc",before:"desc"})}function k(a,b){var c=b.asc;b=b.desc;switch(a){case"asc":return c;case"desc":return b;default:a;throw i(a)}}function l(a,b){var c=b.after;b=b.before;switch(a){case"after":return c;case"before":return b;default:a;throw i(a)}}g.unhandledDirectionError=i;g.getI64RangeTimestampForDirection=a;g.getRangeMsgIdForDirection=b;g.isFirstPageRange=e;g.translateMwpDirectionToMawDirection=f;g.translateMawDirectionToMwpDirection=j;g.switchOnMWPMessagesDirection=k;g.switchOnMAWMessagesDirection=l}),98);
__d("MAWMessagesCompare",["MAWMessagesDirection"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return function(b,c){if(c.sortOrderMs==null||b.sortOrderMs==null)return 0;if(b.sortOrderMs===c.sortOrderMs){var d,e;d=(d=b.externalId)!=null?d:"";e=(e=c.externalId)!=null?e:"";if(a!=null){d=a.has(d);e=a.has(e);if(d&&!e)return-1;else if(!d&&e)return 1}return((d=c.externalId)!=null?d:"").localeCompare((e=b.externalId)!=null?e:"")}return c.sortOrderMs-b.sortOrderMs}}var i=h(),j=function(a,b){return-1*h()(a,b)};function a(a){return d("MAWMessagesDirection").switchOnMWPMessagesDirection(a,{asc:j,desc:i})}g.makeCompareMessageMetadataForDescOrderFn=h;g.getSortComparisonFunctionForDirection=a}),98);
__d("MWXMAV2IsDataclassLiveLocationXMA",[],(function(a,b,c,d,e,f){"use strict";function a(a){if(a==null)return!1;a=JSON.parse(a);if(a==null)return!1;a=a.content;if(a==null||(a==null?void 0:a.__typename)!=="XMSGXmaSingleContent")return!1;a=a.custom_template_data;if(a==null||(a==null?void 0:a.__typename)!=="XMSGXmaLocationTemplateData")return!1;a=a.location_metadata;return a==null||(a==null?void 0:a.__typename)!=="XMSGXmaLiveLocationMetadata"?!1:!0}f.isDataclassLiveLocationXMA=a}),66);
__d("MAWParseXMAFBConfig",["FBLogger","MAWIsFbProfileDirectoryItemXmaEnabled","MAWIsLiveLocationXmaEnabled","MAWIsMemoriesXmaEnabled","MAWIsRTCCallXmaEnabled","MWXMAV2IsDataclassLiveLocationXMA","WAArmadilloXMA.pb","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h=new Set([(b=d("WAArmadilloXMA.pb")).EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_FEED_SHARE,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_SHORT,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_STORY_SHARE,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_STORY_REPLY,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_GAMING_CUSTOM_UPDATE,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_EXTERNAL_LINK_SHARE,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_AUDIO_CALL,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_VIDEO_CALL,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_MISSED_AUDIO_CALL,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_MISSED_VIDEO_CALL,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_GROUP_AUDIO_CALL,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_GROUP_VIDEO_CALL,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_FEED_POST_PRIVATE_REPLY,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_HIGHLIGHTS_TAB_FRIEND_UPDATES_REPLY,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_HIGHLIGHTS_TAB_LOCAL_EVENT_REPLY,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_EVENT,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_PRODUCER_STORY_REPLY,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_STORY_MENTION,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_PRODUCER_STORY_REPLY,b.EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_RECEIVER_FETCH]);function a(a){var b=a.fbTargetType,e=a.silentExposure;a=a.xmaDataclass;if(b!=null&&h.has(b))return!0;e=e!=null?e:!1;switch(b){case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_CONTACT:return e?c("gkx")("5304"):c("gkx")("4902");case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_AI_CONTACT:return e?c("gkx")("9338"):c("gkx")("9339");case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_LOCATION_SHARING_V2:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_LOCATION_SHARING:return a!=null&&d("MWXMAV2IsDataclassLiveLocationXMA").isDataclassLiveLocationXMA(a)?d("MAWIsLiveLocationXmaEnabled").isLiveLocationXmaEnabled():e?c("gkx")("5320"):c("gkx")("6952");case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_POST_MENTION:return e?c("gkx")("2605"):c("gkx")("2722");case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_MEMORIES_SHARE:return d("MAWIsMemoriesXmaEnabled").isMemoriesXmaEnabled();case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_AUDIO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_VIDEO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_MISSED_AUDIO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_MISSED_VIDEO_CALL:return d("MAWIsRTCCallXmaEnabled").isRTCCallXmaEnabled();case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_PROFILE_DIRECTORY_ITEM:return d("MAWIsFbProfileDirectoryItemXmaEnabled").isFbProfileDirectoryItemXmaEnabled();default:c("FBLogger")("maw_db").mustfix("MAWParseXMAFBConfig encountered an unknown unsupported XMA type: %s",String(b));return!1}}g.FB_FULLY_SUPPORTED_TARGET_TYPES=h;g.isFBSupportedTargetType=a}),98);
__d("MAWVaultMaterials",["FBLogger","WAArrayBufferUtils","randomInt","tweetnacl"],(function(a,b,c,d,e,f,g){"use strict";var h=null,i=null,j=null;function k(a){i=a}function l(){return h}function m(a){h=a}function a(){h=null,i=null}function n(){return p()!=null&&l()!=null}function b(){return j}function o(){var a=1e9,b=9999999999;return c("randomInt")(a,b)+"##"+c("randomInt")(a,b)}function p(){return i}function q(){if(!n())throw c("FBLogger")("messenger_web").mustfixThrow("Vault materials are null");return{encryptionKey:h,prefixAndSuffix:i}}function e(a,b){var d=n();c("FBLogger")("messenger_web").debug("Setting vault materials -- vaultMaterials=%s, previouslySetup=%s",Boolean(a).toString(),d);d||(s(a!=null?a:r()),j=b!=null?b:a!=null?"existing":"newly-generated");return q()}function r(){return{encryptionKey:d("tweetnacl").randomBytes(32).buffer,prefixAndSuffix:o()}}function s(a){var b=a.encryptionKey;a=a.prefixAndSuffix;if(b==null||a==null)throw c("FBLogger")("messenger_web").mustfixThrow("Cannot set null values for encryption key and prefix");m(b);k(a)}function f(){return t(q())}function t(a){var b=a.encryptionKey;a=a.prefixAndSuffix;if(a!=null&&b!=null){b=d("WAArrayBufferUtils").arrayBufferToString(b);return JSON.stringify({encryptionKey:b,prefixAndSuffix:a})}else return""}function u(a){if(a==="")return null;else{a=JSON.parse(a);if(a.encryptionKey==null||a.prefixAndSuffix==null)throw c("FBLogger")("messenger_web").mustfixThrow("Vault materials were not in correct format");var b=d("WAArrayBufferUtils").stringToArrayBuffer(a.encryptionKey);a=a.prefixAndSuffix;return{encryptionKey:b,prefixAndSuffix:a}}}g.TEST_ONLY_clearVaultMaterials=a;g.hasVaultBeenSetup=n;g.getVaultMaterialsSource=b;g.getVaultPrefixAndSuffix=p;g.getVaultMaterials=q;g.initializeVaultMaterials=e;g.generateNewVaultMaterials=r;g.materialsToString=f;g.fromMaterialsToString=t;g.fromStringToMaterials=u}),98);
__d("MAWVaultMaterialsStorageStatus",[],(function(a,b,c,d,e,f){"use strict";var g=!1;function a(){return g}function b(){g=!0}f.isCurrentContextHoldingLock=a;f.setIsCurrentContextHoldingLock=b}),66);
__d("MAWVault",["ExecutionEnvironment","FBLogger","MAWKeychainCrypto","MAWKeychainNaClCrypto","MAWODSProxy","MAWVaultMaterials","MAWVaultMaterialsStorageStatus","WAOdsEnums","err","gkx","hasMultipleTabs","promiseDone","shouldUseMAWSharedWorker"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Map(),j=new Map();function k(a){return new RegExp("^[0-9]{10}##[0-9]{10}.*[0-9]{10}##[0-9]{10}$").test(a)}var l=c("gkx")("23909");function m(a){if(!l)return!1;var b=d("MAWVaultMaterials").getVaultPrefixAndSuffix();b=b==null?!1:new RegExp(b+".*"+b).test(a);return b}function n(a,b){b===void 0&&(b=!1);if(!l)return a;if(m(a)){var e=d("MAWVaultMaterials").getVaultMaterials(),f=e.encryptionKey;e=e.prefixAndSuffix;if(e==null){o("unvault","prefix_null");throw c("FBLogger")("messenger_web").mustfixThrow("Vault prefix and suffix should not be null when unvaulting")}var g=new RegExp(e+"(.*)"+e);g=a.match(g);if(g==null||g.length!=2){o("unvault","value_null");throw c("FBLogger")("messenger_web").mustfixThrow("Could not retrieve inner value to unvault")}if(f==null){o("unvault","key_null");throw c("FBLogger")("messenger_web").mustfixThrow("Vault encryption key was null before unvault")}g=g[1];e=""+e+g+e;var i=q(f,g);return a.replace(e,function(){return i})}if(k(a)){o("unvault","incorrect_key");if(b)throw c("FBLogger")("messenger_web").mustfixThrow("Encountered vaulted value with incorrect key.");else{var j=c("err")("Encountered vaulted value with incorrect key");c("promiseDone")(d("hasMultipleTabs").hasMultipleTabs().then(function(a){c("FBLogger")("messenger_web").catching(j).mustfix("Encountered vaulted value with incorrect key. Vault material source: %s, environment: %s, hasMultipleTabs: %s, isSharedWorker: %s, isCurrentContextHoldingLock: %s",d("MAWVaultMaterials").getVaultMaterialsSource(),(h||(h=c("ExecutionEnvironment"))).isInMainThread?"main":"worker",a==null?"unknown_multi_tab":a?"multi_tab":"single_tab",d("shouldUseMAWSharedWorker").shouldUseMAWSharedWorker()?"shared_worker":"dedicated_worker",d("MAWVaultMaterialsStorageStatus").isCurrentContextHoldingLock()?"holding_lock":"not_holding_lock")}))}}return a}function o(a,b){d("MAWODSProxy").odsBumpEntityKey({entity:d("WAOdsEnums").Entity.MAW_VAULTING,key:"failure."+a+"."+b})}function a(a){if(a==="")return a;if(!l)return a;if(!(h||(h=c("ExecutionEnvironment"))).isInBrowser)return a;if(!d("MAWVaultMaterials").hasVaultBeenSetup()){o("vault","has_not_been_setup");c("FBLogger")("messenger_web").mustfix("Attempted to vault value while materials were not setup");return a}if(m(a)){o("vault","already_vaulted");c("FBLogger")("messenger_web").warn("Attempted to vault value that's already been vaulted");return a}k(a)&&(o("vault","already_vaulted_with_different_key"),c("FBLogger")("messenger_web").mustfix("Attempted to vault value that's already been vaulted with a different key"));var b=d("MAWVaultMaterials").getVaultMaterials(),e=b.encryptionKey;b=b.prefixAndSuffix;if(b==null||e==null){o("vault","prefix_or_key_null");throw c("FBLogger")("messenger_web").mustfixThrow("Cannot vault before vault materials are set")}e=p(e,a);return""+b+e+b}function b(a){if(!l)return a;if(!m(a))throw c("FBLogger")("messenger_web").mustfixThrow("Value should be vaulted");return n(a)}function p(a,b){var c=i==null?void 0:i.get(b);if(c!=null)return c;c=new TextEncoder().encode(b).buffer;a=d("MAWKeychainNaClCrypto").encryptTweetNaCl(a,new Uint8Array(c));i==null?void 0:i.set(b,a);j==null?void 0:j.set(a,b);return a}function q(a,b){var c=j==null?void 0:j.get(b);if(c!=null)return c;c=new TextDecoder().decode(d("MAWKeychainNaClCrypto").decryptTweetNaCl(a,b,void 0,d("MAWKeychainCrypto").ADDITIONAL_DATA.byteLength));i==null?void 0:i.set(c,b);j==null?void 0:j.set(b,c);return c}g.isVaulted=m;g.unvault=n;g.vault=a;g.unvaultOrThrow=b}),98);
__d("MAWXMAUtils",["I64","LSIntEnum","LSXmaContentType","MAWMsgType","WAArmadilloXMA.pb","WALogger","WATimeUtils","gkx","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(){var a=babelHelpers.taggedTemplateLiteralLoose(["Hit Unreachable XMAType "," - checking isXMAShare"]);j=function(){return a};return a}function a(a){return!a||a.type!==d("MAWMsgType").MSG_TYPE.XMA?null:a}function b(a){if(a==null)return!1;switch(a){case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_FEED_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_STORY_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_CLIPS_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_MULTIPOST_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_SINGLE_IMAGE_POST_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_PHOTO_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_VIDEO_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_EXTERNAL_LINK_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_PHOTO_MENTION:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_VIDEO_MENTION:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_FEED_POST_PRIVATE_REPLY:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_HIGHLIGHTS_TAB_FRIEND_UPDATES_REPLY:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_HIGHLIGHTS_TAB_LOCAL_EVENT_REPLY:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_PHOTO_HIGHLIGHT_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_VIDEO_HIGHLIGHT_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_SHORT:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_MEMORIES_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_EVENT:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_PROFILE_DIRECTORY_ITEM:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_RECEIVER_FETCH:return!0;case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_POST_MENTION:return c("gkx")("2605");case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_STORY_MENTION:return!0;case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_LOCATION_SHARING_V2:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_LOCATION_SHARING:return c("gkx")("6952");case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_CONTACT:return c("gkx")("4902");case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_AI_CONTACT:return c("gkx")("9339");case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_PRODUCER_STORY_REPLY:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_SINGLE_VIDEO_POST_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_IGTV_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_SHOP_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_REACTION:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_REPLY:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_STORY_REPLY:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_FEED_VIDEO_SHARE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_GAMING_CUSTOM_UPDATE:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_AUDIO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_VIDEO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_MISSED_AUDIO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_MISSED_VIDEO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_GROUP_AUDIO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_GROUP_VIDEO_CALL:return!1;default:d("WALogger").ERROR(j(),a);return!1}}function e(a){return a!=null&&((h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(c("LSXmaContentType").FB_STORY_REPLY))||(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(c("LSXmaContentType").FB_STORY_SHARE))||(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(c("LSXmaContentType").FB_STORY_MENTION))||(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(c("LSXmaContentType").FB_PRODUCER_STORY_REPLY)))}function f(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_STORY_SHARE||a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_PHOTO_SHARE||a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_VIDEO_SHARE}function k(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_EXTERNAL_LINK_SHARE}function l(a){return a!=null&&(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(c("LSXmaContentType").MSG_CONTACT))}function m(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_RECEIVER_FETCH}function n(a){return a==null?!1:a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_PHOTO_SHARE||a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_VIDEO_SHARE}function o(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_FEED_POST_PRIVATE_REPLY}function p(a,b){return a!=null&&a||b!=null&&b<d("WATimeUtils").unixTime()}function q(a){a=a.ctas;a!=null&&(a=a.filter(function(a){return a.buttonType!==d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_CTA_BUTTON_TYPE.OPEN_NATIVE}));return a}function r(a){return babelHelpers["extends"]({},a,{ctas:void 0,defaultCTA:void 0,headerTitle:void 0,isTombstoned:!0,maxSubtitleNumOfLines:void 0,maxTitleNumOfLines:void 0,overlayDescription:void 0,overlayIconGlyph:void 0,overlayTitle:void 0,subtitleText:void 0,targetId:void 0,targetUsername:void 0,titleText:void 0})}function s(a){a=babelHelpers["extends"]({},a,{ctas:void 0,defaultCTA:void 0,defaultPreviewMediaId:void 0,defaultPreviewMediaPlaintextHash:void 0,faviconMediaId:void 0,faviconPlaintextHash:void 0,headerMediaId:void 0,headerMediaPlaintextHash:void 0,headerTitle:void 0,isTombstoned:!0,maxSubtitleNumOfLines:void 0,maxTitleNumOfLines:void 0,overlayDescription:void 0,overlayIconGlyph:void 0,overlayTitle:void 0,previewMediaIds:void 0,subtitleText:void 0,targetId:void 0,targetUsername:void 0,titleText:void 0});c("justknobx")._("3897")&&(a.targetExpiringAtSec=void 0);return a}function t(a){switch(a){case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_STORY_REPLY:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_REPLY:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_PRODUCER_STORY_REPLY:return!0;default:return!1}}function u(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_PRODUCER_STORY_REPLY}function v(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_REACTION}function w(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_STORY_MENTION?!0:!1}function x(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.FB_POST_MENTION}function y(a){switch(a){case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_PHOTO_MENTION:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_VIDEO_MENTION:return!0;default:return!1}}function z(a){return w(a)||y(a)}function A(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_SINGLE_IMAGE_POST_SHARE||a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_MULTIPOST_SHARE||a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_SINGLE_VIDEO_POST_SHARE}function B(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_PHOTO_HIGHLIGHT_SHARE||a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.IG_STORY_VIDEO_HIGHLIGHT_SHARE}function C(a){return a===d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.MSG_HIGHLIGHTS_TAB_FRIEND_UPDATES_REPLY}function D(a){switch(a){case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_AUDIO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_VIDEO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_GROUP_AUDIO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_GROUP_VIDEO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_MISSED_AUDIO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_MISSED_VIDEO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_MISSED_GROUP_AUDIO_CALL:case d("WAArmadilloXMA.pb").EXTENDED_CONTENT_MESSAGE_EXTENDED_CONTENT_TYPE.RTC_MISSED_GROUP_VIDEO_CALL:return!0;default:return!1}}g.maybeXMAMessage=a;g.isXMAShare_DO_NOT_USE=b;g.isXMAFBStory=e;g.isXMAStoryShare=f;g.isXMAExternalLinkShare=k;g.isXMAContactShare=l;g.isReceiverFetchXMA=m;g.isIGXMAStoryShare=n;g.isXMAPostPrivateReply=o;g.isXMAExpired=p;g.removeCTAsForStoryMentionXMA=q;g.buildUnstoredTombstonedXMA=r;g.buildTombstonedDbXMA=s;g.isXMAStoryReply=t;g.isXMAStoryProducerReply=u;g.isXMAStoryReaction=v;g.isFBXMAStoryMention=w;g.isFBXMAPostMention=x;g.isIGXMAStoryMention=y;g.isXMAStoryMention=z;g.isIGXMAPostShare=A;g.isXMAStoryHighlightShare=B;g.isXMAMsgHighlightsTabFriendUpdatesReply=C;g.isRtcXMA=D}),98);
__d("MSGDataclassTypes.flow",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";c=(a=b("$InternalEnum"))({HistoryRestore:"HISTORY_RESTORE",Reporting:"REPORTING",Fts:"FTS",NewMemberExport:"NEW_MEMBER_EXPORT"});d=a({Before:"BEFORE",After:"AFTER"});e=a({UpsertBackupItem:"UPSERT_BACKUP_ITEM",RemoveBackupItem:"REMOVE_BACKUP_ITEM"});b=a({Message:"MESSAGE",Thread:"THREAD"});var g=a({Photo:"PHOTO",Permanent:"PERMANENT",Ephemeral:"EPHEMERAL",Video:"VIDEO",Gif:"GIF",Audio:"AUDIO",File:"FILE",Raven:"RAVEN",UnreadRaven:"UNREAD_RAVEN",ShareIgMedia:"SHARE_IG_MEDIA",ShareLink:"SHARE_LINK",ShareIgClips:"SHARE_IG_CLIPS",Shh:"SHH",StoryReply:"STORY_REPLY",DisappearingMessage:"DISAPPEARING_MESSAGE",RavenSeen:"RAVEN_SEEN",RavenReplayed:"RAVEN_REPLAYED",RavenReadOnce:"RAVEN_READ_ONCE",RavenReplayable:"RAVEN_REPLAYABLE",RavenPermanent:"RAVEN_PERMANENT",ActionLog:"ACTION_LOG"}),h=a({Fbn:"FBN",Instamadillo:"INSTAMADILLO",Unknown:"UNKNOWN"}),i=a({Retroactive:"RETROACTIVE",Realtime:"REALTIME",InfiniteRetroactive:"INFINITE_RETROACTIVE"}),j=a({Success:"SUCCESS",RetryableException:"RETRYABLE_EXCEPTION",PermanentFailure:"PERMANENT_FAILURE"}),k=a({NotApplicable:"NOT_APPLICABLE",NeedsUpload:"NEEDS_UPLOAD",AlreadyUploaded:"ALREADY_UPLOADED"}),l=a({DeleteBackupStateOnClient:"DELETE_BACKUP_STATE_ON_CLIENT",IssueCheckBackupStateTask:"ISSUE_CHECK_BACKUP_STATE_TASK",UpsertStatusTrigger:"UPSERT_STATUS_TRIGGER",UpsertReenrollmentTrigger:"UPSERT_REENROLLMENT_TRIGGER",UpdateInitializeRestoreResult:"UPDATE_INITIALIZE_RESTORE_RESULT",InitializeRestore:"INITIALIZE_RESTORE",DeleteAndInsertPakeMessage:"DELETE_AND_INSERT_PAKE_MESSAGE",RotateDeviceKeysStart:"ROTATE_DEVICE_KEYS_START",LegacyRetroactiveBackup:"LEGACY_RETROACTIVE_BACKUP",UpdatePbqWithNeedsBackupUploadStatus:"UPDATE_PBQ_WITH_NEEDS_BACKUP_UPLOAD_STATUS",SyncDeviceCreationTimestamp:"SYNC_DEVICE_CREATION_TIMESTAMP"}),m=a({LlmOutput:"llm_output",SendQuery:"send_query",StartTyping:"start_typing",StopTyping:"stop_typing",VideoAboutToFinish:"video_about_to_finish"}),n=a({Text:"text",Image:"image",Voice:"voice",Link_1p:"link_1p",Link_3p:"link_3p"}),o=a({Reasoning:"REASONING",Responding:"RESPONDING"}),p=a({Keyword:"KEYWORD",Method:"METHOD",Str:"STR",Number:"NUMBER",Comment:"COMMENT",Default:"DEFAULT"}),q=a({RequestStart:"REQUEST_START",Searching:"SEARCHING",Imagining:"IMAGINING",UnderstandingImage:"UNDERSTANDING_IMAGE",Generating:"GENERATING",StreamingImage:"STREAMING_IMAGE"}),r=a({Love:"LOVE",Giftwrap:"GIFTWRAP",Celebration:"CELEBRATION",Fire:"FIRE",AvatarLove:"AVATAR_LOVE",AvatarAngry:"AVATAR_ANGRY",AvatarCry:"AVATAR_CRY",AvatarLaugh:"AVATAR_LAUGH",Halloween:"HALLOWEEN",NomNomDash:"NOM_NOM_DASH",NewYears:"NEW_YEARS",ValentinesDay:"VALENTINES_DAY",AntiValentines:"ANTI_VALENTINES"}),s=a({Reminder:"REMINDER",Subscription:"SUBSCRIPTION"}),t=a({Memu:"MEMU",Imagine:"IMAGINE",ImagineRestyle:"IMAGINE_RESTYLE"}),u=a({Unknown:"UNKNOWN",Memu:"MEMU",Synthetic:"SYNTHETIC",UserUploaded:"USER_UPLOADED"}),v=a({Simple:"SIMPLE",Retro:"RETRO",Messages:"MESSAGES"}),w=a({MagicModBackdrop:"MAGIC_MOD_BACKDROP",MagicModRestyle:"MAGIC_MOD_RESTYLE",Imagine:"IMAGINE",Memu:"MEMU"}),x=a({PaidPartnership:"PAID_PARTNERSHIP",NoBranding:"NO_BRANDING"}),y=a({AskMetaAi:"ASK_META_AI",Summarize:"SUMMARIZE"}),z=a({BanUser:"BAN_USER",RemoveUser:"REMOVE_USER",SuspendUser:"SUSPEND_USER",RemoveMessage:"REMOVE_MESSAGE",ReportMessage:"REPORT_MESSAGE",HideMessage:"HIDE_MESSAGE"}),A=a({MothersDay:"MOTHERS_DAY",FathersDay:"FATHERS_DAY",Graduation:"GRADUATION",NationalSelfie:"NATIONAL_SELFIE",PhIndependenceDay:"PH_INDEPENDENCE_DAY",JulyFourth:"JULY_FOURTH",FriendshipDay:"FRIENDSHIP_DAY"}),B=a({Birthday:"BIRTHDAY",Attribution:"ATTRIBUTION"}),C=a({XmaWebUrl:"XMA_WEB_URL"}),D=a({GenAiGetInfo:"GEN_AI_GET_INFO",GenAiReminder:"GEN_AI_REMINDER",GenAiThreadSurfing:"GEN_AI_THREAD_SURFING"}),E=a({Mention:"MENTION",Reply:"REPLY"}),F=a({Note:"NOTE",Prompt:"PROMPT",PromptResponse:"PROMPT_RESPONSE"}),G=a({Restaurant:"RESTAURANT",Movie:"MOVIE",TvShows:"TV_SHOWS",Celebrity:"CELEBRITY",SportsTeam:"SPORTS_TEAM"}),H=a({GetInfo:"GET_INFO",GetRestaurantInfo:"GET_RESTAURANT_INFO",Reviews:"REVIEWS",MenuHighlights:"MENU_HIGHLIGHTS",Address:"ADDRESS",WhereToWatch:"WHERE_TO_WATCH",LatestNews:"LATEST_NEWS",FindNextGame:"FIND_NEXT_GAME",ResponseCard:"RESPONSE_CARD"}),I=a({Untranscribed:"UNTRANSCRIBED",Transcribing:"TRANSCRIBING",Success:"SUCCESS",Error:"ERROR",UnsupportedLanguage:"UNSUPPORTED_LANGUAGE"}),J=a({Undefined:"UNDEFINED",Genmoji:"GENMOJI",Metamoji:"METAMOJI",AnimatedEmoji:"ANIMATED_EMOJI"}),K=a({Video:"VIDEO",Image:"IMAGE",Other:"OTHER"}),L=a({Unactioned:"UNACTIONED",Accepted:"ACCEPTED",Deleted:"DELETED"}),M=a({FbmInbox:"FBM_INBOX",Profile:"PROFILE",Feed:"FEED",FbSearch:"FB_SEARCH",Watch:"WATCH",Shorts:"SHORTS",Ufi:"UFI",Ctm:"CTM",Cts:"CTS",Psrib:"PSRIB",Srib:"SRIB",Rta:"RTA",Mp:"MP",CommentMentions:"COMMENT_MENTIONS",ZorroMessage:"ZORRO_MESSAGE",FbHelpCenterXsMetaAi:"FB_HELP_CENTER_XS_META_AI",Dating:"DATING",IgReels:"IG_REELS",IgStory:"IG_STORY",IgFeed:"IG_FEED",IgProfile:"IG_PROFILE",IgNotes:"IG_NOTES",IgLive:"IG_LIVE",IgExternalLink:"IG_EXTERNAL_LINK",IgComment:"IG_COMMENT",FbNotes:"FB_NOTES",FbGroups:"FB_GROUPS",GenAiSuggestedPrompts:"GEN_AI_SUGGESTED_PROMPTS",GenAiMultiModal:"GEN_AI_MULTI_MODAL",GenAiFlAshImagine:"GEN_AI_FlASH_IMAGINE",GenAiAssistant:"GEN_AI_ASSISTANT",GenAiLocation:"GEN_AI_LOCATION",Sharesheet:"SHARESHEET",ProductHighlights:"PRODUCT_HIGHLIGHTS",MagicMessenger:"MAGIC_MESSENGER"}),N=a({Unspecified:"UNSPECIFIED",QuickEmoji:"QUICK_EMOJI",Avatar:"AVATAR",SuggestedReply:"SUGGESTED_REPLY",PostLwrSuggestedReply:"POST_LWR_SUGGESTED_REPLY",WriteWithAi:"WRITE_WITH_AI",GifLightWeight:"GIF_LIGHT_WEIGHT",StickerLightWeight:"STICKER_LIGHT_WEIGHT",QuickEmojiPalette:"QUICK_EMOJI_PALETTE"}),O=a({Default:"DEFAULT",CriticalUserMessage:"CRITICAL_USER_MESSAGE",NonCriticalAdminXma:"NON_CRITICAL_ADMIN_XMA"}),P=a({ThreadviewSuggestedPrompt:"THREADVIEW_SUGGESTED_PROMPT",TypeaheadSuggestedPrompt:"TYPEAHEAD_SUGGESTED_PROMPT",SuggestedPrompt:"SUGGESTED_PROMPT",NullStatePrompt:"NULL_STATE_PROMPT",UserInputPrompt:"USER_INPUT_PROMPT",GenericPrompt:"GENERIC_PROMPT",ThreadviewUserInputPrompt:"THREADVIEW_USER_INPUT_PROMPT",Icebreakers:"ICEBREAKERS",ThreadSurfingPrompt:"THREAD_SURFING_PROMPT",MetaAiVoiceConversationStarter:"META_AI_VOICE_CONVERSATION_STARTER",MetaAiTextConversationStarter:"META_AI_TEXT_CONVERSATION_STARTER"}),Q=a({Mention:"MENTION",SuperReact:"SUPER_REACT",StoryReply:"STORY_REPLY",MessageReply:"MESSAGE_REPLY",ContentReply:"CONTENT_REPLY",ContentMention:"CONTENT_MENTION",ContentShare:"CONTENT_SHARE",OnPlatform:"ON_PLATFORM",OffPlatform:"OFF_PLATFORM",MessageForward:"MESSAGE_FORWARD",Avatar:"AVATAR",SocialSticker:"SOCIAL_STICKER",NoteReply:"NOTE_REPLY",InvokesGenAiResponse:"INVOKES_GEN_AI_RESPONSE",SentFromGenAiBot:"SENT_FROM_GEN_AI_BOT",DisappearingMessage:"DISAPPEARING_MESSAGE",IncludesOnPlatformContent:"INCLUDES_ON_PLATFORM_CONTENT",IncludesOffPlatformContent:"INCLUDES_OFF_PLATFORM_CONTENT",IncludesLink:"INCLUDES_LINK",MultitabEnv:"MULTITAB_ENV",PowerUp:"POWER_UP",MsysSend:"MSYS_SEND",EditMessage:"EDIT_MESSAGE",VanishMode:"VANISH_MODE",Superlative:"SUPERLATIVE",Raven:"RAVEN",Hotlike:"HOTLIKE",IsDialtone:"IS_DIALTONE",MessageReshare:"MESSAGE_RESHARE",PostReply:"POST_REPLY",IsNotDialtone:"IS_NOT_DIALTONE",ThirdPartyReshare:"THIRD_PARTY_RESHARE",ZeroBalanceDetected:"ZERO_BALANCE_DETECTED",ConversationStarter:"CONVERSATION_STARTER",AnimatedAiSticker:"ANIMATED_AI_STICKER",AiGeneratedSticker:"AI_GENERATED_STICKER",MagicModImage:"MAGIC_MOD_IMAGE",ShareOfOriginalUserContent:"SHARE_OF_ORIGINAL_USER_CONTENT",LightweightInteractionAsStoryReply:"LIGHTWEIGHT_INTERACTION_AS_STORY_REPLY",WriteWithMetaAi:"WRITE_WITH_META_AI",AvatarStyle_1:"AVATAR_STYLE_1",AvatarStyle_2:"AVATAR_STYLE_2",MetaAiEntitySuggestedPrompts:"META_AI_ENTITY_SUGGESTED_PROMPTS",ContentConversationStarter:"CONTENT_CONVERSATION_STARTER",MetaAiIntents:"META_AI_INTENTS"}),R=a({MsgrE2EeGenai:"MSGR_E2EE_GENAI"}),S=a({Imagine:"IMAGINE",Imaginev2:"IMAGINEV2",Imagineme:"IMAGINEME"}),T=a({Animate:"ANIMATE",AnimateImagineme:"ANIMATE_IMAGINEME"}),U=a({Share:"SHARE",Reply:"REPLY",Mention:"MENTION",Bump:"BUMP"}),V=a({Statusupdate:"STATUSUPDATE",Grouppost:"GROUPPOST",Shareurl:"SHAREURL"}),W=a({Single:"SINGLE",Hscroll:"HSCROLL",Xcenter:"XCENTER",Portrait:"PORTRAIT",Layered:"LAYERED",Placeholder:"PLACEHOLDER",ExpiredPlaceholder:"EXPIRED_PLACEHOLDER",PilePrompt:"PILE_PROMPT",Stack:"STACK",SharedStack:"SHARED_STACK",StackCard:"STACK_CARD",StandardDxma:"STANDARD_DXMA",DxmaPlaceholder:"DXMA_PLACEHOLDER",Grid:"GRID"}),X=a({Text:"TEXT",Media:"MEDIA"}),Y=a({Unknown:"UNKNOWN",TwoOptionCombined:"TWO_OPTION_COMBINED",IgTwoOptionCombined:"IG_TWO_OPTION_COMBINED",FiveOptionStarRating:"FIVE_OPTION_STAR_RATING"}),Z=a({Unknown:"UNKNOWN",Location:"LOCATION",People:"PEOPLE",Event:"EVENT",ResharedPost:"RESHARED_POST",ResharedContent:"RESHARED_CONTENT",Page:"PAGE",Fundraiser:"FUNDRAISER",Product:"PRODUCT",AntiBully:"ANTI_BULLY",Instagram:"INSTAGRAM"}),$=a({Grid:"GRID",Hscroll:"HSCROLL",Portrait:"PORTRAIT",Single:"SINGLE",Vstack:"VSTACK",SharedStack:"SHARED_STACK"}),aa=a({Left:"LEFT",Center:"CENTER"}),ba=a({Test:"TEST",None:"NONE",IgMediaShare:"IG_MEDIA_SHARE",IgMultipostShare:"IG_MULTIPOST_SHARE",IgStoryReply:"IG_STORY_REPLY",IgStoryPhotoShare:"IG_STORY_PHOTO_SHARE",IgStoryPhotoMention:"IG_STORY_PHOTO_MENTION",IgClipsShare:"IG_CLIPS_SHARE",IgSingleVideoPostShare:"IG_SINGLE_VIDEO_POST_SHARE",IgStoryVideoShare:"IG_STORY_VIDEO_SHARE",IgStoryReaction:"IG_STORY_REACTION",IgIgtvShare:"IG_IGTV_SHARE",IgShopShare:"IG_SHOP_SHARE",IgProfileShare:"IG_PROFILE_SHARE",IgStoryPhotoHighlightShare:"IG_STORY_PHOTO_HIGHLIGHT_SHARE",IgStoryVideoHighlightShare:"IG_STORY_VIDEO_HIGHLIGHT_SHARE",IgStoryVideoMention:"IG_STORY_VIDEO_MENTION",IgStoryHighlightReply:"IG_STORY_HIGHLIGHT_REPLY",IgStoryHighlightReaction:"IG_STORY_HIGHLIGHT_REACTION",IgExternalLink:"IG_EXTERNAL_LINK",IgReceiverFetch:"IG_RECEIVER_FETCH",IgTextPostAppShare:"IG_TEXT_POST_APP_SHARE",FbFeedShare:"FB_FEED_SHARE",FbStoryReply:"FB_STORY_REPLY",FbStoryShare:"FB_STORY_SHARE",FbStoryMention:"FB_STORY_MENTION",FbFeedVideoShare:"FB_FEED_VIDEO_SHARE",FbGamingCustomUpdate:"FB_GAMING_CUSTOM_UPDATE",FbProducerStoryReply:"FB_PRODUCER_STORY_REPLY",FbEvent:"FB_EVENT",FbFeedPostPrivateReply:"FB_FEED_POST_PRIVATE_REPLY",FbShort:"FB_SHORT",FbCommentMentionShare:"FB_COMMENT_MENTION_SHARE",FbPostMention:"FB_POST_MENTION",MsgExternalLinkShare:"MSG_EXTERNAL_LINK_SHARE",MsgP2PPayment:"MSG_P2P_PAYMENT",MsgOpgP2PPayment:"MSG_OPG_P2P_PAYMENT",MsgLocationSharingDeprecated:"MSG_LOCATION_SHARING_DEPRECATED",MsgLocationSharingV2:"MSG_LOCATION_SHARING_V2",MsgReceiverFetch:"MSG_RECEIVER_FETCH",MsgIgMediaShare:"MSG_IG_MEDIA_SHARE",MsgGenAiSearchPluginResponse:"MSG_GEN_AI_SEARCH_PLUGIN_RESPONSE",MsgReelsList:"MSG_REELS_LIST",MsgContact:"MSG_CONTACT",MsgFile:"MSG_FILE",MsgAiContact:"MSG_AI_CONTACT",MsgMemoriesShare:"MSG_MEMORIES_SHARE",MsgEarlyAccessFbShare:"MSG_EARLY_ACCESS_FB_SHARE",MsgSharedAlbum:"MSG_SHARED_ALBUM",MsgReceiverFetchFallback:"MSG_RECEIVER_FETCH_FALLBACK",MsgAiBotSearchResult:"MSG_AI_BOT_SEARCH_RESULT",MsgGenAiSubscription:"MSG_GEN_AI_SUBSCRIPTION",MsgGenAiReminder:"MSG_GEN_AI_REMINDER",MsgGenAiTask:"MSG_GEN_AI_TASK",MsgGenAi_1PSearchResult:"MSG_GEN_AI_1P_SEARCH_RESULT",MsgGenAiMemuOnboardingResponse:"MSG_GEN_AI_MEMU_ONBOARDING_RESPONSE",RtcAudioCall:"RTC_AUDIO_CALL",RtcVideoCall:"RTC_VIDEO_CALL",RtcMissedAudioCall:"RTC_MISSED_AUDIO_CALL",RtcMissedVideoCall:"RTC_MISSED_VIDEO_CALL",RtcGroupAudioCall:"RTC_GROUP_AUDIO_CALL",RtcGroupVideoCall:"RTC_GROUP_VIDEO_CALL",RtcMissedGroupAudioCall:"RTC_MISSED_GROUP_AUDIO_CALL",RtcMissedGroupVideoCall:"RTC_MISSED_GROUP_VIDEO_CALL",LiveLocation:"LIVE_LOCATION",OcAvatarStaticSticker:"OC_AVATAR_STATIC_STICKER"}),ca=a({XmaWebUrl:"XMA_WEB_URL",XmaOpenNative:"XMA_OPEN_NATIVE",XmaMsgExternalLinkShare:"XMA_MSG_EXTERNAL_LINK_SHARE",XmaGenAiSearchPluginResponseSources:"XMA_GEN_AI_SEARCH_PLUGIN_RESPONSE_SOURCES",XmaSharedAlbumViewer:"XMA_SHARED_ALBUM_VIEWER",XmaMetaAiSingleSubscription:"XMA_META_AI_SINGLE_SUBSCRIPTION",XmaMetaAiSubscriptions:"XMA_META_AI_SUBSCRIPTIONS",XmaMetaAiReminders:"XMA_META_AI_REMINDERS",XmaMetaAiTask:"XMA_META_AI_TASK",XmaGamePlay:"XMA_GAME_PLAY",ViewSingleAiTask:"VIEW_SINGLE_AI_TASK",ViewAllAiTasks:"VIEW_ALL_AI_TASKS",XmaMetaAiLocation:"XMA_META_AI_LOCATION",XmaMetaAiLookupEntity:"XMA_META_AI_LOOKUP_ENTITY",XmaP2POpgTransfer:"XMA_P2P_OPG_TRANSFER"}),da=a({Album:"ALBUM",Clips:"CLIPS",Video:"VIDEO"}),ea=a({None:"NONE",VodVideo:"VOD_VIDEO",LiveVideo:"LIVE_VIDEO",ShortsVideo:"SHORTS_VIDEO"}),fa=a({Default:"DEFAULT",ThreadsIcon:"THREADS_ICON",FacebookIcon:"FACEBOOK_ICON",ExternalLinkIcon:"EXTERNAL_LINK_ICON",MessengerIcon:"MESSENGER_ICON",InstagramIcon:"INSTAGRAM_ICON",OculusIcon:"OCULUS_ICON"}),ga=a({Default:"DEFAULT"}),ha=a({None:"NONE",Collage:"COLLAGE",Photo:"PHOTO",Video:"VIDEO",MultiPhoto:"MULTI_PHOTO",MessengerIcon:"MESSENGER_ICON",InstagramIcon:"INSTAGRAM_ICON",ReelsIcon:"REELS_ICON",Live:"LIVE",YoutubeVideo:"YOUTUBE_VIDEO",YoutubeShorts:"YOUTUBE_SHORTS",Blur:"BLUR",Pill:"PILL"}),ia=a({Default:"DEFAULT",P2PPreviewImageWithTextOverlay:"P2P_PREVIEW_IMAGE_WITH_TEXT_OVERLAY",Playable:"PLAYABLE",AutoPlayable:"AUTO_PLAYABLE",BlurredImage:"BLURRED_IMAGE",BlurredPlayable:"BLURRED_PLAYABLE",InformTreatmentWithLabel:"INFORM_TREATMENT_WITH_LABEL",InformTreatmentWithWarningScreen:"INFORM_TREATMENT_WITH_WARNING_SCREEN",YoutubePlayer:"YOUTUBE_PLAYER",ImageWithPlayButton:"IMAGE_WITH_PLAY_BUTTON",Placeholder:"PLACEHOLDER",AutoPlayableWithAspectRatio:"AUTO_PLAYABLE_WITH_ASPECT_RATIO"}),ja=a({Default:"DEFAULT",Rounded:"ROUNDED",BlurredCircle:"BLURRED_CIRCLE",TemplateAsset:"TEMPLATE_ASSET",DefaultWithGradient:"DEFAULT_WITH_GRADIENT"}),ka=a({MediaLabel:"MEDIA_LABEL",Misinformation:"MISINFORMATION",PostCover:"POST_COVER",PostLabel:"POST_LABEL",Private:"PRIVATE",Sensitive:"SENSITIVE",WarningScreens:"WARNING_SCREENS"}),la=a({None:"NONE",YoutubeVideo:"YOUTUBE_VIDEO",YoutubeShorts:"YOUTUBE_SHORTS"}),ma=a({None:"NONE",Fb:"FB",FbAlwaysWhite:"FB_ALWAYS_WHITE",DotsThreeHorizontal:"DOTS_THREE_HORIZONTAL",ChevronDown:"CHEVRON_DOWN",FbInfoSmall:"FB_INFO_SMALL"}),na=a({Simple:"SIMPLE",Retro:"RETRO",Messages:"MESSAGES"}),oa=a({Missed:"MISSED",Ended:"ENDED",Started:"STARTED"}),pa=a({Video:"VIDEO",Audio:"AUDIO"}),qa=a({Weekly:"WEEKLY",Daily:"DAILY"}),ra=a({Paypal:"PAYPAL",Venmo:"VENMO"}),sa=a({Default:"DEFAULT",Overlay:"OVERLAY"}),ta=a({Started:"STARTED",Ended:"ENDED",Active:"ACTIVE",Expired:"EXPIRED"}),ua=a({Keyword:"KEYWORD",Method:"METHOD",Str:"STR",Number:"NUMBER",Comment:"COMMENT",Default:"DEFAULT"}),va=a({Square:"SQUARE",Circle:"CIRCLE"});a=a({Chevron:"CHEVRON"});f.MpsQueryCaller=c;f.MpsMessageRangeQueryDirection=d;f.MpsBackupOperation=e;f.MpsBackupContentType=b;f.MpsMessageTag=g;f.MpsUploadType=h;f.MpsMessageBackupSource=i;f.MpsServerMessageUploadStatus=j;f.MpsServerAttachmentUploadStatus=k;f.MebSplitDatabaseExecutionType=l;f.AiBotEmbodimentTrigger=m;f.XmsgGenAiMessageType=n;f.GenAiUnifiedResponseSectionType=o;f.GenAiCodeBlockType=p;f.XmsgStreamStatusType=q;f.XmsgPowerUpStyle=r;f.XmsgGenAiBotSubscriptionProductType=s;f.XmsgGenAiBotResponseProductType=t;f.XmsgaiBotResponseImagineSource=u;f.XmsgMemoriesTemplateType=v;f.XmsgaiProduct=w;f.XmsgContentBrandingOption=x;f.XmsgaiInvocationOptions=y;f.XmsgSuggestedModerationActionType=z;f.XmsgHighlightsTabMoment=A;f.XmsgHighlightsTabReplySourceType=B;f.XmsgMustacheCtaType=C;f.XmsgInlineEntityType=D;f.XmsgNoteMessageType=E;f.XmsgNoteModelType=F;f.XmsgaiLookupEntityType=G;f.XmsgaiLookupActionType=H;f.XmsgTranscriptionStatus=I;f.XmsgImageGlyphType=J;f.XmsgOculusMediaType=K;f.XmsgMessageRequestStatus=L;f.XmsgMessageSendProductType=M;f.XmsgStoryReplyType=N;f.GenAiContextInjectionType=O;f.GenAiPromptType=P;f.XmsgFeatureTags=Q;f.XmsgMessageStreamingProductType=R;f.Xmsge2EeGenAiEncryptedImageProductType=S;f.Xmsge2EeGenAiEncryptedVideoProductType=T;f.XmsgMsgrXmaActionType=U;f.XmsgMsgrReceiverFetchContentType=V;f.XmsgMsgrReceiverFetchXmaLayoutType=W;f.XmsgReportedMessageType=X;f.XmsgStoryOverlayPollStyle=Y;f.XmsgStoryOverlayTagType=Z;f.XmsgXmaLayoutType=$;f.XmsgXmaVStackLayoutHeaderAlignmentType=aa;f.XmsgXmaContentType=ba;f.XmsgctaType=ca;f.XmsgXmaContentAttributionIcon=da;f.XmsgXmaOverlayContentType=ea;f.XmsgXmaSubtitleDecorationType=fa;f.XmsgXmaVerifiedType=ga;f.XmsgXmaImageDecorationType=ha;f.XmsgXmaCustomPreviewType=ia;f.XmsgXmaMessagingAttachmentFaviconStyle=ja;f.XmsgXmaGatingType=ka;f.XmsgXmaYoutubePreviewMediaType=la;f.XmsgXmaCtaIconType=ma;f.XmsgMemoriesE2EeSendTemplateType=na;f.XmsgXmaRtcCallState=oa;f.XmsgXmaRtcCallType=pa;f.XmsgXmaSubscriptionRecurrenceType=qa;f.XmsgXmaOpgP2pPaymentMethodType=ra;f.XmsgXmaStandardGenericBodyLayoutType=sa;f.XmsgXmaLiveLocationSessionState=ta;f.XmsgXmaCodeBlockType=ua;f.XmsgXmaCompactGenericTemplatePreviewStyle=va;f.XmsgXmaCompactGenericTemplateAccessoryType=a}),66);
__d("MWEncryptedBackupsSharedState",["ReQL","ReQLSuspense","WebStorage","getMWEncryptedBackupsIsLocalStorageSupported","promiseDone","react","useReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(j||d("react")).useCallback;function l(a){var b=a.db,c=a.stateKey;a=a.stateValue;var d=a===void 0?"true":a;return b.runInTransaction(function(a){return a.experiences_shared_state.upsert([c],{stateKey:c,stateValue:d})},"readwrite","background",void 0,f.id+":37")}function m(a){var b,e=a.db;a=a.stateKey;if(!d("getMWEncryptedBackupsIsLocalStorageSupported").getMWEncryptedBackupsIsLocalStorageSupported())return;b=(b=(h||(h=c("WebStorage"))).getLocalStorageForRead())==null?void 0:b.getItem(a);if(b!=null){var f;(f=(h||(h=c("WebStorage"))).getLocalStorage())==null?void 0:f.removeItem(a);c("promiseDone")(l({db:e,stateKey:a,stateValue:b}))}return b}function a(a){var b=a.db,c=a.sharedStateTable,d=a.stateKey;a=c.filter(function(a){return a.stateKey===d});if(a.length===0){c=m({db:b,stateKey:d});return c!=null}return!0}function b(a){var b=a.db,c=a.sharedStateTable,d=a.stateKey;a=c.filter(function(a){return a.stateKey===d});c=a[0];return(c==null?void 0:c.stateValue)!=null?c.stateValue:m({db:b,stateKey:d})}function e(a){var b=a.db,c=a.sharedStateRow;a=a.stateKey;return c==null?m({db:b,stateKey:a}):c.stateValue}function n(a){var b=a.db,c=a.stateKey;return b.runInTransaction(function(a){return a.experiences_shared_state["delete"](c)},"readwrite","background",void 0,f.id+":131")}function o(a){var b,e=(i||(i=c("useReStore")))();b=(b=d("ReQLSuspense").useFirst(function(){return d("ReQL").fromTableAscending(e.tables.experiences_shared_state).getKeyRange(a)},[e.tables.experiences_shared_state,a],f.id+":142"))==null?void 0:b.stateValue;var g=k(function(b){return l({db:e,stateKey:a,stateValue:b})},[e,a]);return[b,g]}function p(a){a=o(a);var b=a[0],c=a[1];a=k(function(){void c("true")},[c]);return[b==="true",a]}g.upsertEBSharedStateEntry=l;g.getEntryPresentInLocalStorage=m;g.getIsEntryPresentInSharedState=a;g.getEntryValueFromSharedState=b;g.getEntryValue=e;g.deleteEntry=n;g.useEBSharedStateEntry=o;g.useEBSharedStateEntryBoolean=p}),98);
__d("MWThreadListConsts",["I64"],(function(a,b,c,d,e,f,g){"use strict";var h;a=56;b=900;c=10;g.threadlistItemHeight=a;g.minimumPixelsNeededForMaxDisplay=b;g.DEFAULT_MIN_LAST_ACTIVITY_TIMESTAMP=(h||d("I64")).one;g.SNIPPET_MAX_NEWEST_INCOMING_MESSAGES_COUNT=c}),98);
__d("WAHashUtils",["WABase64"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){return d("WABase64").encodeB64UrlSafe(a,b)}function b(a,b){return d("WABase64").encodeB64UrlSafe(a,b)}function c(a){return a}function e(a){return a}function f(a){return""+a.slice(0,3)}g.toPlaintextHash=a;g.toCiphertextHash=b;g.stringToPlaintextHash=c;g.toHashedPlaintextHash=e;g.sanitisePlaintextHash=f}),98);
__d("WAJobOrchestratorTypes",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum")({SKIP:"SKIP_PRIORITIZATION",UI_ACTION:"UI_ACTION",LOW:"LOW",HIGH:"HIGH",OFFLINE:"OFFLINE",HISTORY_SYNC:"HISTORY_SYNC",BEST_EFFORT:"BEST_EFFORT"});c=1;d=a.LOW;e=10*1e3;f.JOB_PRIORITY=a;f.DEFAULT_CONCURRENCY=c;f.DEFAULT_JOB_PRIORITY=d;f.DEFAULT_JOB_TIMEOUT_MS=e}),66);