;/*FB_PKG_DELIM*/

__d("AsyncTypedRequest",["AsyncRequest"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){b=a.call(this,b)||this;b.setReplaceTransportMarkers();return b}var c=b.prototype;c.promisePayload=function(b){return a.prototype.promisePayload.call(this,b)};c.setPayloadHandler=function(b){a.prototype.setPayloadHandler.call(this,b);return this};return b}(c("AsyncRequest"));g["default"]=a}),98);
__d("BTManifestName",["$InternalEnum"],(function(a,b,c,d,e,f){a=b("$InternalEnum")({MAIN:"main",LONGTAIL:"longtail"});c=a;f["default"]=c}),66);
__d("BtLongtailHashFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("5779");b=d("FalcoLoggerInternal").create("bt_longtail_hash",a);e=b;g["default"]=e}),98);
__d("CometBTManifestLoader",["BootloaderEvents","BtLongtailHashFalcoEvent","ClientConsistencyEventEmitter","FBLogger","ODS","SiteData","TrustedTypes","XHRRequest","err","justknobx","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Set();async function j(a,b,d,e){var f=await new Promise(function(f,g){new(c("XHRRequest"))(a+"/btmanifest/"+d+"/"+b+"/"+e).setMethod("GET").setResponseHandler(function(a){return f(a.toString())}).setErrorHandler(function(a){return g(a)}).send()});if(typeof f!=="string")throw c("FBLogger")("binary_transparency","bt_invalid_manifest_response").mustfixThrow("Invalid response from BT manifest endpoint");return f}function k(a,b){(h||(h=d("ODS"))).bumpEntityKey(454,"obc.www.all","bt.comet_manifest_loader."+Number(d("SiteData").compose_bootloads)+"."+d("SiteData").pkg_cohort+"."+a+"."+b)}function l(a,b,d){var e;if(d instanceof Error)e=d;else if(typeof d==="object"){var f=JSON.stringify(d);e=c("err")("(XHRRequest): %s",f.slice(0,300)+(f.length>300?"...":""))}else e=c("err")(d);c("FBLogger")("binary_transparency","bt_download_manifest_error").catching(e).mustfix('Unable to download and inject BT manifest "%s" for version: %s',b,a)}var m=c("TrustedTypes").createPolicy("bt-manifest",{createScript:function(a){return a}});async function n(a,b){if(!d("SiteData").manifest_origin||d("SiteData").manifest_version_prefix==null||d("SiteData").manifest_base_uri==null)return;var e=a+"_"+b;a=""+d("SiteData").manifest_version_prefix+a;if(i.has(e))return;k(b,"start");i.add(e);try{var f=document.createElement("script"),g=await j(d("SiteData").manifest_base_uri,d("SiteData").manifest_origin,a,b);c("justknobx")._("3786")?f.innerText=m.createScript(g):f.innerText=g;f.type="application/json";f.setAttribute("name","binary-transparency-manifest");f.dataset.manifestRev=a;f.dataset.manifestType=b;(g=document.head)==null?void 0:g.appendChild(f);k(b,"complete")}catch(c){k(b,"failed"),l(a,b,c),i.delete(e)}}function a(){c("promiseDone")(n(d("SiteData").client_revision,"main")),d("BootloaderEvents").onResourceInLongTailBTManifest(function(a){c("promiseDone")(n(d("SiteData").client_revision,"longtail")),a.hashes.forEach(function(b){c("BtLongtailHashFalcoEvent").log(function(){return{client_revision:String(d("SiteData").client_revision),compose_bootloads:d("SiteData").compose_bootloads,ef_page:d("SiteData").ef_page||"",hash:b,pkg_cohort:d("SiteData").pkg_cohort,reference:a.source,rls_id:d("SiteData").hsi}})})}),c("ClientConsistencyEventEmitter").addListener("newRevision",function(a){c("promiseDone")(n(a,"main")),c("promiseDone")(n(a,"longtail"))})}g.init=a}),98);
__d("FDSCamcorderFilled16Icon",["cr:13962"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:13962")}),98);
__d("FDSCamcorderFilled16PNGIcon.react",["ix","fbicon"],(function(a,b,c,d,e,f,g,h){"use strict";a=d("fbicon")._(h("493173"),16);b=a;g["default"]=b}),98);
__d("FDSRefreshLeftFilled20Icon",["cr:14078"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:14078")}),98);
__d("FDSRefreshLeftFilled20PNGIcon.react",["ix","fbicon"],(function(a,b,c,d,e,f,g,h){"use strict";a=d("fbicon")._(h("534219"),20);b=a;g["default"]=b}),98);
__d("FDSWirelessFilled24Icon",["cr:15876"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:15876")}),98);
__d("FDSWirelessFilled24PNGIcon.react",["ix","fbicon"],(function(a,b,c,d,e,f,g,h){"use strict";a=d("fbicon")._(h("485124"),24);b=a;g["default"]=b}),98);
__d("LSCheckThreadConsistency",["LSIssueNewFireAndForgetTask","LSIssueNewTask","gkx"],(function(a,b,c,d,e,f,g){function a(){var a=arguments,d=a[a.length-1],e=[],f=[];return d.sequence([function(f){return c("gkx")("15730")?(e[0]=new d.Map(),e[0].set("threads",a[0]),e[0].set("metadata",a[1]),e[1]=d.toJSON(e[0]),d.storedProcedure(b("LSIssueNewFireAndForgetTask"),"fire_forget",d.i64.cast([0,18]),e[1])):(e[0]=new d.Map(),e[0].set("threads",a[0]),e[0].set("metadata",a[1]),e[1]=d.toJSON(e[0]),d.storedProcedure(b("LSIssueNewTask"),"check-thread-consistency",d.i64.cast([0,160019]),e[1],void 0,void 0,d.i64.cast([0,0]),d.i64.cast([0,0]),void 0,void 0,d.i64.cast([0,0]),d.i64.cast([0,0])))},function(a){return d.resolve(f)}])}a.__sproc_name__="LSE2EEMessagingMetadataMailboxCheckThreadConsistencyStoredProcedure";a.__tables__=[];d=a;g["default"]=d}),98);
__d("LSCheckThreadConsistencyStoredProcedure",["LSCheckThreadConsistency","LSSynchronousPromise","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSCheckThreadConsistency"),e.threads,e.metadata);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("MAWThreadConsistencyLogger",["FBLogger","I64","LSAuthorityLevel","LSCheckThreadConsistencyStoredProcedure","LSDatabaseSingleton","LSFactory","LSMessagingThreadTypeUtil","LSShape","LSVec","ODS","ReQL","WAArrayZip","clearInterval","gkx","justknobx","promiseDone","qex","setInterval"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=20,l=c("justknobx")._("3207"),m=!c("gkx")("5457"),n=!1,o=null;function a(){if(n||m)return;n=!0;c("clearInterval")(o);o=c("setInterval")(function(){if(document.visibilityState!=="visible")return;c("promiseDone")(p())},l*1e3)}async function p(){var a=await (h||(h=d("LSDatabaseSingleton"))).LSDatabaseSingleton;c("promiseDone")(a.runInTransaction(async function(a){(i||(i=d("ODS"))).bumpEntityKey(3185,"maw_tc_metric","initiate_snapshot");var b=await d("ReQL").toArrayAsync(d("ReQL").fromTableDescending(a.threads.index("lastActivityTimestampMs")).filter(q).take(k));if(b.length===0){(i||(i=d("ODS"))).bumpEntityKey(3185,"maw_tc_metric","no_e2ee_threads_fetched_on_client");return}var e;c("qex")._("18")?e=await Promise.all(b.map(function(b){return d("ReQL").firstAsync(d("ReQL").fromTableDescending(a.messages.index("messageDisplayOrderOfflineThreadingId")).getKeyRange(b.threadKey))})):e=await Promise.all(b.map(function(b){return d("ReQL").firstAsync(d("ReQL").fromTableDescending(a.messages.index("messageDisplayOrder")).getKeyRange(b.threadKey))}));b=d("WAArrayZip").zip(b,e).map(function(a){var b=a[0];a=a[1];return d("LSShape").ofRecord({is_last_activity_optimistic:r(a),last_activity_timestamp_ms:b.lastActivityTimestampMs,last_read_timestamp_ms:b.lastReadWatermarkTimestampMs,thread_key:b.threadKey})});return c("LSCheckThreadConsistencyStoredProcedure")(c("LSFactory")(a),{metadata:d("LSShape").ofRecord({client_snapshot_timestamp_ms:(j||(j=d("I64"))).of_float(Date.now()),session_timestamp_ms:j.of_int32(Math.round(window.performance.now()))}),threads:c("LSVec").ofArray(b)})},"readonly",void 0,void 0,f.id+":59"),function(){return(i||(i=d("ODS"))).bumpEntityKey(3185,"maw_tc_metric","send_snapshot")},function(a){c("FBLogger")("maw_tc_metric").catching(a).warn("failed to send tc metric snapshot")})}function q(a){return d("LSMessagingThreadTypeUtil").isArmadilloSecure(a.threadType)&&a.folderName==="inbox"&&(j||(j=d("I64"))).to_int32(a.authorityLevel)>=c("LSAuthorityLevel").AUTHORITATIVE}function r(a){return a==null?!1:(j||(j=d("I64"))).to_int32(a.authorityLevel)<c("LSAuthorityLevel").AUTHORITATIVE}g.startMAWThreadConsistencyLogger=a;g.MAWThreadConsistencyLoggerTakeSnapshot=p}),98);