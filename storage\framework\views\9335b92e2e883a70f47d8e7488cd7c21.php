<?php $__env->startSection('title', 'Today\'s Menu - Student Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="container">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">Today's Menu</h3>
                        <p class="mb-0 text-muted">Welcome, <?php echo e(Auth::user()->name ?? 'Student'); ?>!</p>
                    </div>
                    <div class="text-end">
                        <div id="currentTime" class="h4 mb-0 text-primary"></div>
                        <div id="currentDate" class="text-muted"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Quick Actions Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-lightning-fill me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <a href="/student/pre-order" class="btn btn-primary w-100">
                                <i class="bi bi-clipboard-check me-2"></i>Pre-Orders & Polls
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="/student/menu" class="btn btn-outline-primary w-100">
                                <i class="bi bi-calendar-week me-2"></i>View Menu
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="/student/feedback" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-chat-dots me-2"></i>Give Feedback
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Meal Attendance Polls Section -->
    <?php if(count($activeMealPolls ?? []) > 0): ?>
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i>Meal Attendance Polls</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <?php $__currentLoopData = $activeMealPolls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $poll): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="list-group-item <?php echo e(isset($pollResponses[$poll->id]) ? 'bg-light' : ''); ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h5 class="mb-1"><?php echo e($poll->title); ?></h5>
                                        <p class="mb-1"><?php echo e($poll->content); ?></p>
                                        <small class="text-muted">Expires on <?php echo e(date('M d, Y', strtotime($poll->expiry_date))); ?></small>
                                    </div>
                                    <?php if(isset($pollResponses[$poll->id])): ?>
                                        <span class="badge bg-success">Response Submitted: <?php echo e($pollResponses[$poll->id]); ?></span>
                                    <?php endif; ?>
                                </div>

                                <?php if(!isset($pollResponses[$poll->id])): ?>
                                <div class="mt-3">
                                    <form action="<?php echo e(route('student.poll-response.store')); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="announcement_id" value="<?php echo e($poll->id); ?>">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Will you attend this meal?</label>
                                            <div class="d-flex flex-wrap gap-2">
                                                <?php $__currentLoopData = json_decode($poll->poll_options); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="radio" name="response" value="<?php echo e($option); ?>" id="option<?php echo e($poll->id); ?>_<?php echo e($loop->index); ?>">
                                                        <label class="form-check-label" for="option<?php echo e($poll->id); ?>_<?php echo e($loop->index); ?>">
                                                            <?php echo e($option); ?>

                                                        </label>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary btn-sm">Submit Response</button>
                                    </form>
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>


    </div>

    <!-- Meal Polls Section -->
    <?php if(count($activeMealPolls ?? []) > 0): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card main-card">
                <div class="card-header">
                    <h5 class="card-title"><i class="bi bi-check2-square me-2"></i>Meal Polls</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-3">
                        <i class="bi bi-info-circle me-2"></i> Please participate in the meal polls below to help us plan our menu better. Your input helps us reduce food waste and improve meal options.
                    </div>

                    <div class="row">
                        <?php $__currentLoopData = $activeMealPolls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $poll): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0"><?php echo e($poll->title); ?></h6>
                                        <span class="badge bg-primary"><?php echo e(date('M d, Y', strtotime($poll->expiry_date))); ?></span>
                                    </div>
                                    <div class="card-body">
                                        <p><?php echo e($poll->content); ?></p>

                                        <?php
                                            $hasResponded = isset($pollResponses[$poll->id]);
                                        ?>

                                        <?php if($hasResponded): ?>
                                            <div class="alert alert-success">
                                                <i class="bi bi-check-circle me-2"></i> You've already responded to this poll. Thank you!
                                            </div>
                                        <?php else: ?>
                                            <form action="<?php echo e(route('student.poll-response.store')); ?>" method="POST">
                                                <?php echo csrf_field(); ?>
                                                <input type="hidden" name="announcement_id" value="<?php echo e($poll->id); ?>">

                                                <div class="mb-3">
                                                    <label class="form-label">Your response:</label>
                                                    <div class="list-group">
                                                        <?php $__currentLoopData = json_decode($poll->poll_options); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <label class="list-group-item">
                                                                <input class="form-check-input me-1" type="radio" name="response" value="<?php echo e($option); ?>" required>
                                                                <strong><?php echo e($option); ?></strong>
                                                            </label>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                    <label for="comment" class="form-label">Additional Comments (Optional):</label>
                                                    <textarea class="form-control" id="comment" name="comment" rows="2"></textarea>
                                                </div>

                                                <button type="submit" class="btn btn-primary">Submit Response</button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    #currentTime {
        font-size: 1.5rem;
        font-weight: bold;
    }

    #currentDate {
        font-size: 1rem;
    }

    .card {
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    /* Food Waste Prevention Styles */
    .impact-stat {
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    /* Meal Menu Styles */
    .meal-item {
        padding: 10px;
        border-radius: 5px;
        transition: all 0.2s ease;
    }

    .meal-item:hover {
        background-color: #f8f9fa;
    }

    .week-menu {
        transition: all 0.3s ease;
    }

    .badge {
        font-size: 0.7rem;
        font-weight: normal;
        padding: 0.3rem 0.5rem;
    }

    .impact-stat:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .impact-value {
        font-size: 2rem;
        font-weight: 700;
        color: #2e7d32;
        margin-bottom: 5px;
    }

    .impact-label {
        font-size: 0.9rem;
        color: #555;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Update date/time every second
    function updateDateTime() {
        const now = new Date();

        // Format time in 12-hour format with AM/PM
        const timeOptions = {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        };
        const timeString = now.toLocaleTimeString('en-US', timeOptions);

        // Format date
        const dateOptions = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
        const dateString = now.toLocaleDateString('en-US', dateOptions);

        // Update elements
        const currentTimeElement = document.getElementById('currentTime');
        const currentDateElement = document.getElementById('currentDate');

        if (currentTimeElement) {
            currentTimeElement.textContent = timeString;
        }
        if (currentDateElement) {
            currentDateElement.textContent = dateString;
        }
    }

    // Call immediately and then every second
    updateDateTime();
    setInterval(updateDateTime, 1000);

    // Week cycle toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const weekCycleSelect = document.getElementById('weekCycleSelect');
        const week1Menu = document.getElementById('week1Menu');
        const week2Menu = document.getElementById('week2Menu');

        if (weekCycleSelect && week1Menu && week2Menu) {
            weekCycleSelect.addEventListener('change', function() {
                const selectedCycle = this.value;

                if (selectedCycle === '1') {
                    week1Menu.style.display = 'block';
                    week2Menu.style.display = 'none';
                } else {
                    week1Menu.style.display = 'none';
                    week2Menu.style.display = 'block';
                }
            });
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('form[action*="poll-response"]').forEach(function(form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                const submitBtn = form.querySelector('button[type="submit"]');
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';
                const formData = new FormData(form);
                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Response submitted successfully!');
                        form.reset();
                    } else {
                        alert(data.message || 'Failed to submit response');
                    }
                })
                .catch(error => {
                    alert('An error occurred while submitting response');
                })
                .finally(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = 'Submit Response';
                });
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\setup\Capstone\Github\Capstone14\capstone\resources\views/student/dashboard.blade.php ENDPATH**/ ?>