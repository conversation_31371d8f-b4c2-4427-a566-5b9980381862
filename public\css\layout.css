/* Layout Styles
   Contains styles for the main layout structure, wrapper, and containers
*/

/* Main wrapper */
#wrapper {
    display: flex;
    width: 100%;
    min-height: 100vh;
    margin-left: 280px; /* Match sidebar width */
    transition: margin-left 0.3s ease;
}

/* Navbar styles */
.navbar {
    width: 100%;
    margin: 0;
    border-bottom: 1px solid #e5e7eb;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Page content wrapper */
#page-content-wrapper {
    width: 100%;
    padding: 20px;
    background-color: #f8f9fa;
    min-height: 100vh;
}

/* Container styles */
.container {
    padding: 0;
    width: 100%;
    max-width: 100%;
}

/* Card styles */
.card {
    border: none;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    margin: 0;
    border-radius: 8px;
    overflow: hidden;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Table container */
.table-responsive {
    margin: 0;
    padding: 0;
}

/* Button container */
.d-flex.align-items-center.justify-content-end {
    gap: 12px;
}

/* Form elements */
.form-select, .form-control {
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 0.9375rem;
    transition: all 0.2s ease;
}

.form-select:focus, .form-control:focus {
    border-color: #22bbea;
    box-shadow: 0 0 0 3px rgba(34, 187, 234, 0.1);
    outline: none;
}

/* Alert styles */
.alert {
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 15px 20px;
}

.alert-success {
    background-color: #d1fae5;
    border-color: #a7f3d0;
    color: #065f46;
}

.alert-danger {
    background-color: #fee2e2;
    border-color: #fecaca;
    color: #991b1b;
}

.error-list {
    margin: 0;
    padding-left: 20px;
}

/* Student Meal Consumption */

/* Dropdown Styling */
.form-select {
    border: 1px solid #ced4da;
    border-radius: 5px;
    padding: 8px 12px;
    font-size: 0.9rem;
    color: #495057;
    transition: all 0.3s ease-in-out;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.form-select, .form-control {
    padding: 6px 10px; 
    margin: 0; 
    box-shadow: none; 
    height: auto;
    font-size: 0.9rem;
}

.form-select:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
}

.form-select option {
    background-color: #fff;
    color: #495057;
}


.form-select:hover {
    background-color: #e9ecef;
    cursor: pointer;
}



.d-flex > .form-select, 
.d-flex > .form-control {
    margin-right: 8px; 
}

.card-header .d-flex {
    gap: 10px; 
    align-items: center; 
    justify-content: space-between;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #wrapper {
        margin-left: 0;
    }
    
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
}