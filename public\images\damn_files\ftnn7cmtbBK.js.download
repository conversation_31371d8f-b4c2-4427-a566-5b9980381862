;/*FB_PKG_DELIM*/

__d("BladerunnerHandlersPresencePresenceHandlerTypes",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";c=(a=b("$InternalEnum"))({BuddyListPolling:1,AdditionalContactsPolling:2,Disabled:3,EnableRealtimeUpdatesOnly:4});d=a({ThriftCompact:1,Json:2,Dasm:3});f=a({Stream:1,Timed:2});b=a({Full:1,Incremental:2});var g=a({AdditionalContacts:1,PollingMode:2,PresenceReporting:3,ContextualPresenceReporting:4});a=a({AdditionalContacts:"additionalContacts",PollingModeAmendment:"pollingModeAmendment",PresenceReportingAmendment:"presenceReportingAmendment",ContextualPresenceReportingAmendment:"contextualPresenceReportingAmendment"});e.exports={LifecycleMode:f,PollingMode:c,PresenceAmendmentTypes:g,PresenceStreamAmendment$Types:a,PublishEncoding:d,PublishType:b}}),null);
__d("IGDAvatarGlimmer.react",["IGDSGlimmer.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={glimmer:{borderStartStartRadius:"x1c9tyrk",borderStartEndRadius:"xeusxvb",borderEndEndRadius:"x1pahc9y",borderEndStartRadius:"x1ertn4p",height:"x5yr21d",width:"xh8yej3",$$css:!0}};function k(a,b){if(b==="tiny")return"32px";if(b==="small"||b==="medium")if(a)return"32px";else return"44px";else if(b==="XXL")if(a)return"56px";else return"96px";else if(b==="micro")return"14px";else return"56px"}function a(a){var b=d("react-compiler-runtime").c(9),e=a.isMobile;a=a.size;e=e===void 0?!1:e;var f;b[0]!==e||b[1]!==a?(f=k(e,a),b[0]=e,b[1]=a,b[2]=f):f=b[2];e=f;b[3]===Symbol["for"]("react.memo_cache_sentinel")?(a="x1n2onr6",b[3]=a):a=b[3];b[4]!==e?(f={height:e,width:e},b[4]=e,b[5]=f):f=b[5];b[6]===Symbol["for"]("react.memo_cache_sentinel")?(e=i.jsx(c("IGDSGlimmer.react"),{index:1,xstyle:j.glimmer}),b[6]=e):e=b[6];b[7]!==f?(a=i.jsx("div",{className:a,style:f,children:e}),b[7]=f,b[8]=a):a=b[8];return a}g["default"]=a}),98);
__d("IGDChatTabsQPLInstanceKeyContext.react",["emptyFunction","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));e=h;d=e.createContext;var j=e.useContext,k=e.useMemo,l=e.useState,m=d({setThreadListKey:c("emptyFunction"),setThreadViewKey:c("emptyFunction"),threadListKey:0,threadViewKeys:{}});function a(a){a=a.children;var b=l(0),c=b[0],d=b[1];b=l({});var e=b[0],f=b[1];b=k(function(){return{setThreadListKey:d,setThreadViewKey:function(a,b){var c;f(babelHelpers["extends"]({},e,(c={},c[a]=b,c)))},threadListKey:c,threadViewKeys:e}},[c,e]);return i.jsx(m.Provider,{value:b,children:a})}a.displayName=a.name+" [from "+f.id+"]";function b(){return j(m)}g.IGDChatTabsQPLInstanceKeyContextProvider=a;g.useIGDChatTabsQPLInstanceKeyContext=b}),98);
__d("IGDCheckboxBadge.react",["IGDSCheckPanoFilledIcon.react","PolarisGenericStrings","react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={XL:{borderTopWidth:"xmn4e3e",borderInlineEndWidth:"x1if355w",borderBottomWidth:"x2x41l1",borderInlineStartWidth:"xct1zlm",bottom:"x1ey2m1c",height:"xx3o462",insetInlineEnd:"xtijo5x",left:null,right:null,width:"x1a00udw",$$css:!0},XXL:{borderTopWidth:"xmn4e3e",borderInlineEndWidth:"x1if355w",borderBottomWidth:"x2x41l1",borderInlineStartWidth:"xct1zlm",bottom:"x1ey2m1c",height:"xmix8c7",insetInlineEnd:"xtijo5x",left:null,right:null,width:"x1xp8n7a",$$css:!0},badge:{alignItems:"x6s0dn4",backgroundColor:"x1tu34mt",borderTopColor:"xbe7ycp",borderInlineEndColor:"x1npm9xp",borderBottomColor:"x5p4dl0",borderInlineStartColor:"x179w5xb",borderStartStartRadius:"x1c9tyrk",borderStartEndRadius:"xeusxvb",borderEndEndRadius:"x1pahc9y",borderEndStartRadius:"x1ertn4p",borderTopStyle:"x13fuv20",borderInlineEndStyle:"x18b5jzi",borderBottomStyle:"x1q0q8m5",borderInlineStartStyle:"x1t7ytsu",display:"x78zum5",justifyContent:"xl56j7k",position:"x10l6tqk",$$css:!0},large:{borderTopWidth:"xmn4e3e",borderInlineEndWidth:"x1if355w",borderBottomWidth:"x2x41l1",borderInlineStartWidth:"xct1zlm",bottom:"x1ey2m1c",height:"x1kpxq89",insetInlineEnd:"xtijo5x",left:null,right:null,width:"xsmyaan",$$css:!0},medium:{borderTopWidth:"xamhcws",borderInlineEndWidth:"x1alpsbp",borderBottomWidth:"xlxy82",borderInlineStartWidth:"xyumdvf",bottom:"x1ey2m1c",height:"xegnrdp",insetInlineEnd:"xtijo5x",left:null,right:null,width:"x1wc42o8",$$css:!0},micro:{borderTopWidth:"x178xt8z",borderInlineEndWidth:"x1lun4ml",borderBottomWidth:"xso031l",borderInlineStartWidth:"xpilrb4",bottom:"x1ey2m1c",height:"xols6we",insetInlineEnd:"xtijo5x",left:null,right:null,width:"x1v4s8kt",$$css:!0},small:{borderTopWidth:"xamhcws",borderInlineEndWidth:"x1alpsbp",borderBottomWidth:"xlxy82",borderInlineStartWidth:"xyumdvf",bottom:"x1ey2m1c",height:"xols6we",insetInlineEnd:"xtijo5x",left:null,right:null,width:"x1v4s8kt",$$css:!0},tiny:{borderTopWidth:"xamhcws",borderInlineEndWidth:"x1alpsbp",borderBottomWidth:"xlxy82",borderInlineStartWidth:"xyumdvf",bottom:"x1ey2m1c",height:"xols6we",insetInlineStart:"x1o0tod",left:null,right:null,width:"x1v4s8kt",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(5);a=a.size;var e;b[0]!==a?(e=(h||(h=c("stylex"))).props(k.badge,k.large,k[a]),b[0]=a,b[1]=e):e=b[1];b[2]===Symbol["for"]("react.memo_cache_sentinel")?(a=j.jsx(c("IGDSCheckPanoFilledIcon.react"),{alt:d("PolarisGenericStrings").CHECKMARK_FILLED_ICON_ALT,color:"web-always-white",size:8}),b[2]=a):a=b[2];b[3]!==e?(a=j.jsx("div",babelHelpers["extends"]({},e,{children:a})),b[3]=e,b[4]=a):a=b[4];return a}g["default"]=a}),98);
__d("IGDListCellSelectedContext.react",["react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));c=h;e=c.createContext;var j=c.useContext,k=e(!1);function a(a){var b=d("react-compiler-runtime").c(3),c=a.children;a=a.selected;var e;b[0]!==c||b[1]!==a?(e=i.jsx(k.Provider,{value:a,children:c}),b[0]=c,b[1]=a,b[2]=e):e=b[2];return e}function b(){return j(k)}g.IGDListCellSelectedContextProvider=a;g.useIsListCellSelected=b}),98);
__d("IGDPresenceBadge.react",["fbt","CometScreenReaderText.react","IGDListCellSelectedContext.react","react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||d("react"),l={XL:{borderTopWidth:"xmn4e3e",borderInlineEndWidth:"x1if355w",borderBottomWidth:"x2x41l1",borderInlineStartWidth:"xct1zlm",bottom:"x1ey2m1c",height:"xx3o462",insetInlineEnd:"xtijo5x",left:null,right:null,width:"x1a00udw",$$css:!0},XXL:{borderTopWidth:"xmn4e3e",borderInlineEndWidth:"x1if355w",borderBottomWidth:"x2x41l1",borderInlineStartWidth:"xct1zlm",bottom:"x1ey2m1c",height:"xmix8c7",insetInlineEnd:"xtijo5x",left:null,right:null,width:"x1xp8n7a",$$css:!0},badge:{backgroundColor:"x1wyv8x2",borderTopColor:"x1yhmmig",borderInlineEndColor:"xpcrz0c",borderBottomColor:"xyb01ml",borderInlineStartColor:"x1vpptxl",borderStartStartRadius:"x1c9tyrk",borderStartEndRadius:"xeusxvb",borderEndEndRadius:"x1pahc9y",borderEndStartRadius:"x1ertn4p",borderTopStyle:"x13fuv20",borderInlineEndStyle:"x18b5jzi",borderBottomStyle:"x1q0q8m5",borderInlineStartStyle:"x1t7ytsu",position:"x10l6tqk",$$css:!0},large:{borderTopWidth:"xmn4e3e",borderInlineEndWidth:"x1if355w",borderBottomWidth:"x2x41l1",borderInlineStartWidth:"xct1zlm",bottom:"x1ey2m1c",height:"x1kpxq89",insetInlineEnd:"xtijo5x",left:null,right:null,width:"xsmyaan",$$css:!0},medium:{borderTopWidth:"xamhcws",borderInlineEndWidth:"x1alpsbp",borderBottomWidth:"xlxy82",borderInlineStartWidth:"xyumdvf",bottom:"x1ey2m1c",height:"xegnrdp",insetInlineEnd:"xtijo5x",left:null,right:null,width:"x1wc42o8",$$css:!0},micro:{borderTopWidth:"x178xt8z",borderInlineEndWidth:"x1lun4ml",borderBottomWidth:"xso031l",borderInlineStartWidth:"xpilrb4",bottom:"x1ey2m1c",height:"xols6we",insetInlineEnd:"xtijo5x",left:null,right:null,width:"x1v4s8kt",$$css:!0},selected:{borderTopColor:"xbhhrqq",borderInlineEndColor:"x14esgxp",borderBottomColor:"x1szfnsn",borderInlineStartColor:"xifc02g",$$css:!0},small:{borderTopWidth:"xamhcws",borderInlineEndWidth:"x1alpsbp",borderBottomWidth:"xlxy82",borderInlineStartWidth:"xyumdvf",bottom:"x1ey2m1c",height:"xols6we",insetInlineEnd:"xtijo5x",left:null,right:null,width:"x1v4s8kt",$$css:!0},tiny:{borderTopWidth:"xamhcws",borderInlineEndWidth:"x1alpsbp",borderBottomWidth:"xlxy82",borderInlineStartWidth:"xyumdvf",bottom:"x1ey2m1c",height:"xols6we",insetInlineStart:"x1o0tod",left:null,right:null,width:"x1v4s8kt",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(7);a=a.size;var e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e=h._(/*BTDS*/"Active"),b[0]=e):e=b[0];e=e;var f=d("IGDListCellSelectedContext.react").useIsListCellSelected(),g;b[1]!==f||b[2]!==a?(g=(i||(i=c("stylex"))).props(l.badge,l.large,l[a],f&&l.selected),b[1]=f,b[2]=a,b[3]=g):g=b[3];b[4]===Symbol["for"]("react.memo_cache_sentinel")?(f=k.jsx(c("CometScreenReaderText.react"),{text:e}),b[4]=f):f=b[4];b[5]!==g?(a=k.jsx("div",babelHelpers["extends"]({},g,{children:f})),b[5]=g,b[6]=a):a=b[6];return a}g["default"]=a}),226);
__d("IGDSAvatar.react",["CometImage.react","CometPressable.react","IGDSConstants","isStringNullOrEmpty","react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={button:{appearance:"xjyslct",backgroundColor:"xjbqb8w",borderTopStyle:"x1ejq31n",borderInlineEndStyle:"x18oe1m7",borderBottomStyle:"x1sy0etr",borderInlineStartStyle:"xstzfhl",cursor:"x1ypdohk",paddingTop:"xexx8yu",paddingInlineEnd:"xyri2b",paddingBottom:"x18d9i69",paddingInlineStart:"x1c1uobl",$$css:!0},container:{borderStartStartRadius:"x1c9tyrk",borderStartEndRadius:"xeusxvb",borderEndEndRadius:"x1pahc9y",borderEndStartRadius:"x1ertn4p",display:"x1lliihq",overflowX:"x6ikm8r",overflowY:"x10wlt62",position:"x1n2onr6","::after_borderTopColor":"xzfakq","::after_borderInlineEndColor":"xhihtb0","::after_borderBottomColor":"x1j8hi7x","::after_borderInlineStartColor":"x172hklt","::after_borderStartStartRadius":"xrw4ojt","::after_borderStartEndRadius":"xg6frx5","::after_borderEndEndRadius":"xw872ko","::after_borderEndStartRadius":"xhgbb2x","::after_borderTopStyle":"xynf4tj","::after_borderInlineEndStyle":"xdjs2zz","::after_borderBottomStyle":"x1r9ni5o","::after_borderInlineStartStyle":"xvsnedh","::after_borderTopWidth":"xoiy6we","::after_borderInlineEndWidth":"x16ouz9t","::after_borderBottomWidth":"x1qj619r","::after_borderInlineStartWidth":"xsrjr5h","::after_bottom":"x1xrz1ek","::after_content":"x1s928wv","::after_insetInlineEnd":"x1unh1gc","::after_pointerEvents":"x2q1x1w","::after_position":"x1j6awrg","::after_insetInlineStart":"x1iygr5g","::after_left":null,"::after_right":null,"::after_top":"x1m1drc7",$$css:!0},root:{height:"x5yr21d",objectFit:"xl1xv1r",width:"xh8yej3",$$css:!0}};function l(a){var b=d("react-compiler-runtime").c(15),e=a.children,f=a.href,g=a.onClick,h=a.role,i=a.size,l=a.target;a=a.xstyle;var m;b[0]!==f||b[1]!==l?(m=f!=null?{target:l,url:f}:void 0,b[0]=f,b[1]=l,b[2]=m):m=b[2];f=c("IGDSConstants").AVATAR_SIZES[i];l=c("IGDSConstants").AVATAR_SIZES[i];b[3]!==f||b[4]!==l?(i={height:f,width:l},b[3]=f,b[4]=l,b[5]=i):i=b[5];b[6]!==a?(f=[k.button,k.container,a],b[6]=a,b[7]=f):f=b[7];b[8]!==e||b[9]!==g||b[10]!==h||b[11]!==m||b[12]!==i||b[13]!==f?(l=j.jsx(c("CometPressable.react"),{linkProps:m,onPress:g,overlayDisabled:!0,role:h,style:i,xstyle:f,children:e}),b[8]=e,b[9]=g,b[10]=h,b[11]=m,b[12]=i,b[13]=f,b[14]=l):l=b[14];return l}function a(a){var b=d("react-compiler-runtime").c(27),e=a.alt,f=a.href,g=a.onClick,i=a.role,m=a.size,n=a.src,o=a.target;a=a.xstyle;m=m===void 0?"medium":m;var p=!c("isStringNullOrEmpty")(n);if(f!=null||g){var q;b[0]!==e||b[1]!==p||b[2]!==m||b[3]!==n?(q=p?j.jsx(c("CometImage.react"),{alt:e,height:c("IGDSConstants").AVATAR_SIZES[m],src:n,width:c("IGDSConstants").AVATAR_SIZES[m],xstyle:k.root}):null,b[0]=e,b[1]=p,b[2]=m,b[3]=n,b[4]=q):q=b[4];var r;b[5]!==f||b[6]!==g||b[7]!==i||b[8]!==m||b[9]!==q||b[10]!==o||b[11]!==a?(r=j.jsx(l,{href:f,onClick:g,role:i,size:m,target:o,xstyle:a,children:q}),b[5]=f,b[6]=g,b[7]=i,b[8]=m,b[9]=q,b[10]=o,b[11]=a,b[12]=r):r=b[12];return r}b[13]!==a?(f=(h||(h=c("stylex")))(k.container,a),b[13]=a,b[14]=f):f=b[14];g=c("IGDSConstants").AVATAR_SIZES[m];i=c("IGDSConstants").AVATAR_SIZES[m];b[15]!==g||b[16]!==i?(q={height:g,width:i},b[15]=g,b[16]=i,b[17]=q):q=b[17];b[18]!==e||b[19]!==p||b[20]!==m||b[21]!==n?(o=p?j.jsx(c("CometImage.react"),{alt:e,height:c("IGDSConstants").AVATAR_SIZES[m],src:n,width:c("IGDSConstants").AVATAR_SIZES[m],xstyle:k.root}):null,b[18]=e,b[19]=p,b[20]=m,b[21]=n,b[22]=o):o=b[22];b[23]!==f||b[24]!==q||b[25]!==o?(r=j.jsx("span",{className:f,style:q,children:o}),b[23]=f,b[24]=q,b[25]=o,b[26]=r):r=b[26];return r}g["default"]=a}),98);
__d("IGDFacepile.react",["IGDChatTabsEnv","IGDCheckboxBadge.react","IGDPresenceBadge.react","IGDSAvatar.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function j(a,b,c){if(b==="XL")return["large","74px"];else if(b==="medium"||b==="tiny"||b==="small"){if(c===!0&&b==="tiny")return["tiny","32px"];if(a)return["extraSmall","32px"];else return["small","44px"]}else if(b==="XXL"&&!a)return["XLFacepile","96px"];else return["facepile","56px"]}function k(a,b){if(b==="medium"||b==="tiny"||b==="small")if(a)return"small";else return"medium";else if(b==="XXL")return"XXL";else if(b==="XL")return"XL";else return"large"}function a(a){var b=d("react-compiler-runtime").c(35),e=a.firstParticipantURI,f=a.isActive,g=a.isMobile,h=a.isSelected,l=a.secondParticipantURI;a=a.size;f=f===void 0?!1:f;g=g===void 0?!1:g;h=h===void 0?!1:h;var m=d("IGDChatTabsEnv").useIsChatTabsDisplay(),n;b[0]!==m||b[1]!==g||b[2]!==a?(n=j(g,a,m),b[0]=m,b[1]=g,b[2]=a,b[3]=n):n=b[3];n=n;var o=n[0];n=n[1];var p;b[4]!==g||b[5]!==a?(p=k(g,a),b[4]=g,b[5]=a,b[6]=p):p=b[6];g=p;b[7]===Symbol["for"]("react.memo_cache_sentinel")?(p="x1n2onr6",b[7]=p):p=b[7];var q;b[8]!==n?(q={height:n,width:n},b[8]=n,b[9]=q):q=b[9];b[10]!==o||b[11]!==e?(n=i.jsx(c("IGDSAvatar.react"),{alt:"",size:o,src:e}),b[10]=o,b[11]=e,b[12]=n):n=b[12];b[13]!==m||b[14]!==a?(e={0:{className:"xgf5ljw x1c9tyrk xeusxvb x1pahc9y x1ertn4p x1ey2m1c xtijo5x x10l6tqk x1nn3v0j x14vy60q x1120s5i xyiysdx"},2:{className:"xgf5ljw x1c9tyrk xeusxvb x1pahc9y x1ertn4p x1ey2m1c xtijo5x x10l6tqk x4p5aij x1ccui7m x1j85h84 x18pi947"},1:{className:"xgf5ljw x1c9tyrk xeusxvb x1pahc9y x1ertn4p x10l6tqk x1nn3v0j x14vy60q x1120s5i xyiysdx x1t1qrwb x1d0qlrl"},3:{className:"xgf5ljw x1c9tyrk xeusxvb x1pahc9y x1ertn4p x10l6tqk x4p5aij x1ccui7m x1j85h84 x18pi947 x1t1qrwb x1d0qlrl"}}[!!(a==="medium")<<1|!!m<<0],b[13]=m,b[14]=a,b[15]=e):e=b[15];b[16]!==o||b[17]!==l?(m=i.jsx(c("IGDSAvatar.react"),{alt:"",size:o,src:l}),b[16]=o,b[17]=l,b[18]=m):m=b[18];b[19]!==m||b[20]!==e?(a=i.jsx("div",babelHelpers["extends"]({},e,{children:m})),b[19]=m,b[20]=e,b[21]=a):a=b[21];b[22]!==f||b[23]!==h||b[24]!==g?(o=f&&!h?i.jsx(c("IGDPresenceBadge.react"),{size:g}):null,b[22]=f,b[23]=h,b[24]=g,b[25]=o):o=b[25];b[26]!==h||b[27]!==g?(l=h?i.jsx(c("IGDCheckboxBadge.react"),{size:g}):null,b[26]=h,b[27]=g,b[28]=l):l=b[28];b[29]!==a||b[30]!==o||b[31]!==l||b[32]!==q||b[33]!==n?(m=i.jsxs("div",{className:p,style:q,children:[n,a,o,l]}),b[29]=a,b[30]=o,b[31]=l,b[32]=q,b[33]=n,b[34]=m):m=b[34];return m}g["default"]=a}),98);
__d("IGDFallbackProfileUrl",[],(function(a,b,c,d,e,f){"use strict";a="/static/images/profile/profile-pic-null_outline_56_light-4x.png/bc91e9cae98c.png";f.FALLBACK_PROFILE_PIC_URL=a}),66);
__d("IGDIsCompactModeContext.react",["IGDProfessionalIGFolderContext.react","react","react-compiler-runtime","useMatchMedia"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));e=h;f=e.createContext;var j=e.useContext,k="900px",l=f(!1);function a(a){var b=d("react-compiler-runtime").c(3);a=a.children;var e=c("useMatchMedia")("(max-width: "+k+")"),f=d("IGDProfessionalIGFolderContext.react").useIsProfessionalIGD();e=e&&!f;b[0]!==a||b[1]!==e?(f=i.jsx(l.Provider,{value:e,children:a}),b[0]=a,b[1]=e,b[2]=f):f=b[2];return f}function b(){return j(l)}g.IGDIsCompactModeContextProvider=a;g.useIsCompactMode=b}),98);
__d("IGDListCellPlaceholder.react",["IGDAvatarGlimmer.react","IGDChatTabsEnv","IGDIsCompactModeContext.react","IGDSGlimmer.react","react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={chatTabRoot:{display:"x78zum5",paddingTop:"x1y1aw1k",paddingBottom:"xwib8y2",paddingInlineStart:"xf7dkkf",paddingInlineEnd:"xv54qhq",width:"xi55695",$$css:!0},compactRoot:{display:"x78zum5",paddingTop:"x1y1aw1k",paddingBottom:"xwib8y2",paddingInlineStart:"x13jy36j",paddingInlineEnd:"x64bnmy",$$css:!0},root:{display:"x78zum5",paddingTop:"x1y1aw1k",paddingBottom:"xwib8y2",paddingInlineStart:"x13jy36j",paddingInlineEnd:"x64bnmy",width:"xxsgkw5",$$css:!0},subtitleGlimmer:{borderStartStartRadius:"x1obq294",borderStartEndRadius:"x5a5i1n",borderEndEndRadius:"xde0f50",borderEndStartRadius:"x15x8krk",height:"xlup9mm",width:"x1oux285",$$css:!0},textGlimmer:{borderStartStartRadius:"x1obq294",borderStartEndRadius:"x5a5i1n",borderEndEndRadius:"xde0f50",borderEndStartRadius:"x15x8krk",height:"xmix8c7",marginBottom:"x1e56ztr",width:"x1f9tj09",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(11);a=a.avatarSize;a=a===void 0?"large":a;var e=d("IGDIsCompactModeContext.react").useIsCompactMode(),f=d("IGDChatTabsEnv").useIsChatTabsDisplay(),g;b[0]!==f||b[1]!==e?(g=(h||(h=c("stylex"))).props(e?k.compactRoot:f?k.chatTabRoot:k.root),b[0]=f,b[1]=e,b[2]=g):g=b[2];b[3]!==a?(f=j.jsx(c("IGDAvatarGlimmer.react"),{size:a}),b[3]=a,b[4]=f):f=b[4];b[5]!==e?(a=!e&&j.jsxs("div",babelHelpers["extends"]({className:"x78zum5 xdt5ytf x1iyjqo2 xs83m0k xl56j7k x1g0dm76"},{children:[j.jsx(c("IGDSGlimmer.react"),{index:1,xstyle:k.textGlimmer}),j.jsx(c("IGDSGlimmer.react"),{index:1,xstyle:k.subtitleGlimmer})]})),b[5]=e,b[6]=a):a=b[6];b[7]!==g||b[8]!==f||b[9]!==a?(e=j.jsxs("div",babelHelpers["extends"]({},g,{children:[f,a]})),b[7]=g,b[8]=f,b[9]=a,b[10]=e):e=b[10];return e}g["default"]=a}),98);
__d("IGDSectionHeaderLayout.react",["react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={noBottomBorder:{borderBottomWidth:"x1qhh985",height:"x14z7g9a",minHeight:"xzbw6zd",$$css:!0},root:{alignItems:"x6s0dn4",borderBottomColor:"x1bs97v6",borderBottomStyle:"x1q0q8m5",borderBottomWidth:"xso031l",boxSizing:"x9f619",display:"x78zum5",flexDirection:"x1q0g3np",height:"xr931m4",marginBottom:"xat24cr",minHeight:"x4lt0of",paddingInlineStart:"xf7dkkf",paddingInlineEnd:"xv54qhq",width:"xh8yej3",$$css:!0},rootMobile:{height:"xn3w4p2",minHeight:"x1gg8mnh",paddingInlineStart:"xf7dkkf",paddingInlineEnd:"xv54qhq",paddingLeft:null,paddingRight:null,$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(7),e=a.children,f=a.isMobile,g=a.noBottomBorder;a=a.xstyle;f=f===void 0?!1:f;g=g===void 0?!1:g;var i;b[0]!==f||b[1]!==g||b[2]!==a?(i=(h||(h=c("stylex"))).props(k.root,g?k.noBottomBorder:!1,f?k.rootMobile:!1,a),b[0]=f,b[1]=g,b[2]=a,b[3]=i):i=b[3];b[4]!==e||b[5]!==i?(f=j.jsx("div",babelHelpers["extends"]({},i,{children:e})),b[4]=e,b[5]=i,b[6]=f):f=b[6];return f}function b(a){var b=d("react-compiler-runtime").c(3);a=a.children;var c;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(c={className:"x78zum5"},b[0]=c):c=b[0];b[1]!==a?(c=j.jsx("div",babelHelpers["extends"]({},c,{children:a})),b[1]=a,b[2]=c):c=b[2];return c}function e(a){var b=d("react-compiler-runtime").c(3);a=a.children;var c;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(c={className:"x78zum5 x13a6bvl x1g0dm76"},b[0]=c):c=b[0];b[1]!==a?(c=j.jsx("div",babelHelpers["extends"]({},c,{children:a})),b[1]=a,b[2]=c):c=b[2];return c}g.Container=a;g.StartAdornment=b;g.EndAdornment=e}),98);
__d("IGDInboxLeftColumnPlaceholder.react",["IGDListCellPlaceholder.react","IGDSGlimmer.react","IGDSectionHeaderLayout.react","react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={leftColumn:{backgroundColor:"xvbhtw8",borderInlineEndColor:"x6usi7g",borderInlineEndStyle:"x18b5jzi",borderInlineEndWidth:"x1lun4ml",height:"x5yr21d",overflowX:"x6ikm8r",overflowY:"x10wlt62",$$css:!0},mobile:{borderTopWidth:"x972fbf",borderInlineEndWidth:"x10w94by",borderBottomWidth:"x1qhh985",borderInlineStartWidth:"x14e42zd",height:"x1dr59a3",$$css:!0},sectionHeaderIconGlimmer:{borderStartStartRadius:"x1obq294",borderStartEndRadius:"x5a5i1n",borderEndEndRadius:"xde0f50",borderEndStartRadius:"x15x8krk",height:"x1fgtraw",marginTop:"x1xmf6yo",marginInlineEnd:"x1xegmmw",marginBottom:"x1e56ztr",marginInlineStart:"x13fj5qh",width:"xvy4d1p",$$css:!0},usernameGlimmer:{borderStartStartRadius:"x1obq294",borderStartEndRadius:"x5a5i1n",borderEndEndRadius:"xde0f50",borderEndStartRadius:"x15x8krk",height:"x1ta3ar0",width:"xh8yej3",$$css:!0}};function l(){var a=d("react-compiler-runtime").c(1),b;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=j.jsx(c("IGDSGlimmer.react"),{index:1,xstyle:k.usernameGlimmer}),a[0]=b):b=a[0];return b}function m(){var a=d("react-compiler-runtime").c(1);if(a[0]===Symbol["for"]("react.memo_cache_sentinel")){var b;b=j.jsxs(j.Fragment,{children:[j.jsx(b=c("IGDListCellPlaceholder.react"),{}),j.jsx(b,{}),j.jsx(b,{}),j.jsx(b,{}),j.jsx(b,{}),j.jsx(b,{}),j.jsx(b,{}),j.jsx(b,{}),j.jsx(b,{}),j.jsx(b,{}),j.jsx(b,{})]});a[0]=b}else b=a[0];return b}function a(a){var b=d("react-compiler-runtime").c(15);a=a.isMobile;a=a===void 0?!1:a;var e;b[0]!==a?(e=(h||(h=c("stylex"))).props(k.leftColumn,a?k.mobile:!1),b[0]=a,b[1]=e):e=b[1];var f;b[2]!==a?(f=a?j.jsx(c("IGDSGlimmer.react"),{index:1,xstyle:k.sectionHeaderIconGlimmer}):null,b[2]=a,b[3]=f):f=b[3];var g;b[4]!==f?(g=j.jsx(d("IGDSectionHeaderLayout.react").StartAdornment,{children:f}),b[4]=f,b[5]=g):g=b[5];b[6]===Symbol["for"]("react.memo_cache_sentinel")?(f=j.jsx(l,{}),b[6]=f):f=b[6];var i;b[7]===Symbol["for"]("react.memo_cache_sentinel")?(i=j.jsx(d("IGDSectionHeaderLayout.react").EndAdornment,{children:j.jsx(c("IGDSGlimmer.react"),{index:1,xstyle:k.sectionHeaderIconGlimmer})}),b[7]=i):i=b[7];b[8]!==a||b[9]!==g?(f=j.jsxs(d("IGDSectionHeaderLayout.react").Container,{isMobile:a,noBottomBorder:!0,children:[g,f,i]}),b[8]=a,b[9]=g,b[10]=f):f=b[10];b[11]===Symbol["for"]("react.memo_cache_sentinel")?(i=j.jsx(m,{}),b[11]=i):i=b[11];b[12]!==e||b[13]!==f?(a=j.jsxs("div",babelHelpers["extends"]({},e,{children:[f,i]})),b[12]=e,b[13]=f,b[14]=a):a=b[14];return a}g.IGDInboxLeftColumnPlaceholderUsernameGlimmer=l;g.IGDInboxLeftColumnPlaceholderThreadListLayout=m;g.IGDInboxLeftColumnPlaceholder=a}),98);
__d("PresenceCapabilityBit",["I64"],(function(a,b,c,d,e,f,g){"use strict";var h;b=(h||(h=d("I64"))).of_float(2);c=h.of_float(8);e=h.of_float(268435456);function a(){for(var a=arguments.length,b=new Array(a),c=0;c<a;c++)b[c]=arguments[c];return(h||(h=d("I64"))).to_string(b.reduce(function(a,b){return(h||(h=d("I64"))).or_(a,b)},h.zero))}g.VoiceIpEnabled=b;g.WebrtcEnabled=c;g.ReceivePublishWithEIMUID=e;g.combine=a}),98);
__d("PresenceCommonPresenceCommonTypes",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum")({Active:2,Inactive:0});c=b("$InternalEnum")({Facebook:1,Messenger:2,Instagram:3,Threads:4});d=b("$InternalEnum")({StreamControllerConnectionIds:"streamControllerConnectionIds"});e.exports={AppFamily:c,PresenceStatus:a,StreamControllerSessionId$Types:d}}),null);
__d("PresenceUnifiedClient",["BladerunnerHandlersPresencePresenceHandlerTypes","FBLogger","Promise","RealtimeNexusSessionDataTypes","RequestStreamCommonRequestStreamCommonTypes","asyncToGeneratorRuntime","clearInterval","debounce","gkx","promiseDone","requireDeferred","setInterval","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("requireDeferred")("TransportSelectingClientSingleton").__setRef("PresenceUnifiedClient"),j=2.5*60*1e3,k=1e3;a=function(){function a(a){var b=this;this.$1=c("gkx")("9391");this.$2=!1;this.$3=new Map();this.$8=new Map();this.$9=d("RealtimeNexusSessionDataTypes").PresenceAvailability.IDLE;this.$10=new Set();this.$20=c("debounce")(function(){return b.$18()},k);this.$4=a;this.$11=(a=a.presenceReportingRequest)==null?void 0:a.capabilities}var e=a.prototype;e.$12=function(){this.$3.clear(),this.$13(),this.$5=null,c("clearInterval")(this.$7),this.$2=!1};e.$14=function(a){this.$1&&c("FBLogger")("rti_presence").info("Presence stream status update: %s",Number(a)),this.$6=a,a===d("RequestStreamCommonRequestStreamCommonTypes").FlowStatus.Started&&c("promiseDone")(this.reportUserPresence(this.$9))};e.$15=function(a){this.$1&&c("FBLogger")("rti_presence").debug(a)};e.$16=function(a){this.$1&&c("FBLogger")("rti_presence").catching(a).info("Presence stream: stream terminated"),this.$12()};e.$17=function(a){var b=this;try{a=JSON.parse(a);this.$1&&c("FBLogger")("rti_presence").debug("Presence stream map updated: %s",JSON.stringify(a.presenceUpdates.map(function(a){var b=a.userId;a=a.presenceStatus;return{id:b,ps:a}})).trim());a.publishType===d("BladerunnerHandlersPresencePresenceHandlerTypes").PublishType.Full&&this.$3.clear();a.presenceUpdates.forEach(function(a){b.$3.set(a.userId,a)});this.$13()}catch(a){this.$1&&c("FBLogger")("rti_presence").catching(a).info("Presence stream: Presence publish could not be parsed");return}};e.$13=function(){for(var a of this.$8.values())a(this.$3)};e.startAdditionalContactsPolling=function(){var a=this;c("clearInterval")(this.$7);this.$7=c("setInterval")(function(){c("promiseDone")(a.$18())},j)};e.stopAdditionalContactsPolling=function(){c("clearInterval")(this.$7)};e.$19=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){var b=this.$5;if(b==null){this.$1&&c("FBLogger")("rti_presence").info("Presence: stream: You cannot ammend a stream that is not open");return!1}a.presenceReportingAmendment!=null&&(a.presenceReportingAmendment.reportingArguments.capabilities=this.$11);a={payload:babelHelpers["extends"]({},a)};try{return yield b.amendWithAck(JSON.stringify(a))}catch(a){this.$1&&c("FBLogger")("rti_presence").catching(a).info("Presence: stream: Failed to amend stream with ack");return!1}});function d(b){return a.apply(this,arguments)}return d}();e.$18=function(){var a=Array.from(this.$10);this.$1&&c("FBLogger")("rti_presence").info("Presence: stream: requestAdditonalContactsPresence %s",JSON.stringify(a));if(a.length===0)return(h||(h=b("Promise"))).resolve(!0);a={additionalContacts:a};return this.$19({additionalContacts:a})};e.closeStream=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){try{this.$5!=null&&(yield this.$5.cancel())}catch(a){this.$1&&c("FBLogger")("rti_presence").catching(a).info("Presence stream: error while closing stream")}finally{this.$12()}});function d(){return a.apply(this,arguments)}return d}();e.startStream=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){var a=this;if(this.$2===!0||this.$5!=null)return;var b={method:"PresenceUnifiedJSON"};this.$2=!0;var e=JSON.stringify(babelHelpers["extends"]({},this.$4,{publishEncoding:d("BladerunnerHandlersPresencePresenceHandlerTypes").PublishEncoding.Json}));try{var f=(yield i.load());this.$5=(yield f.requestStream(b,e,{onData:function(b){return a.$17(b)},onFlowStatus:function(b){return a.$14(b)},onLog:function(b){return a.$15(b)},onTermination:function(b){return a.$16(b)}}))}catch(a){this.$1&&c("FBLogger")("rti_presence").catching(a).warn("Presence: stream: Failed to start presence stream")}finally{this.$2=!1}});function e(){return a.apply(this,arguments)}return e}();e.changeActiveStatusSetting=function(a){a={reportingArguments:{makeUserAvailableWhenInForeground:a,mutationId:c("uuidv4")()}};return this.$19({presenceReportingAmendment:a})};e.reportUserPresence=function(a){this.$9=a;if(this.$5==null)return(h||(h=b("Promise"))).resolve(!1);this.$1&&c("FBLogger")("rti_presence").info("Presence stream: report user presence: %s",Number(a));a={reportingArguments:{availability:a,foregrounded:Boolean(a===d("RealtimeNexusSessionDataTypes").PresenceAvailability.ACTIVE),mutationId:c("uuidv4")()}};return this.$19({presenceReportingAmendment:a})};e.addAdditionalContacts=function(a){for(a of a)this.$10.add(a);this.$20()};e.getPresenceMap=function(){return this.$3};e.registerListener=function(a){var b=this,d=c("uuidv4")();this.$8.set(d,a);return function(){b.$8["delete"](d)}};return a}();g.POLLING_INTERVAL_MS=j;g.REQUEST_ADDITIONAL_CONTACTS_DEBOUNCE_MS=k;g.PresenceUnifiedClient=a}),98);
__d("IGDMobilePresenceUnifiedClientSingleton",["BladerunnerHandlersPresencePresenceHandlerTypes","PresenceCapabilityBit","PresenceCommonPresenceCommonTypes","PresenceUnifiedClient","RealtimeNexusSessionDataTypes","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";b=new(d("PresenceUnifiedClient").PresenceUnifiedClient)({appFamily:d("PresenceCommonPresenceCommonTypes").AppFamily.Instagram,pollingMode:d("BladerunnerHandlersPresencePresenceHandlerTypes").PollingMode.BuddyListPolling,appId:String(1217981644879628),presenceReportingRequest:{capabilities:(a=d("PresenceCapabilityBit")).combine(a.VoiceIpEnabled,a.WebrtcEnabled,a.ReceivePublishWithEIMUID),mutationId:c("uuidv4")(),availability:d("RealtimeNexusSessionDataTypes").PresenceAvailability.IDLE}});g.IGDMobilePresenceUnifiedClientSingleton=b}),98);
__d("IGDSingleAvatar.react",["fbt","IGDCheckboxBadge.react","IGDPresenceBadge.react","IGDSAvatar.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react"),k={disabled:{opacity:"xbyyjgo",$$css:!0}};function l(a,b){if(a)switch(b){case"micro":return["micro","14px"];case"tiny":return["tiny","24px"];case"medium":return["small","32px"];case"XL":return["XL","74px"];case"XXL":return["large","56px"];default:return["large","56px"]}else switch(b){case"micro":return["micro","14px"];case"tiny":return["tiny","24px"];case"small":return["small","32px"];case"medium":return["medium","44px"];case"XL":return["XL","74px"];case"largeXL":return["largeXL","84px"];case"XXL":return["XXL","96px"];default:return["large","56px"]}}function a(a){var b=d("react-compiler-runtime").c(25),e=a.alt,f=a.isActive,g=a.isDisabled,i=a.isMobile,m=a.isSelected,n=a.size;a=a.src;e=e===void 0?null:e;f=f===void 0?!1:f;g=g===void 0?!1:g;i=i===void 0?!1:i;m=m===void 0?!1:m;var o;b[0]!==i||b[1]!==n?(o=l(i,n),b[0]=i,b[1]=n,b[2]=o):o=b[2];i=o;n=i[0];o=i[1];b[3]===Symbol["for"]("react.memo_cache_sentinel")?(i="x1n2onr6",b[3]=i):i=b[3];var p;b[4]!==o?(p={height:o,width:o},b[4]=o,b[5]=p):p=b[5];b[6]!==e?(o=e!=null?e:h._(/*BTDS*/"User avatar"),b[6]=e,b[7]=o):o=b[7];e=g?k.disabled:void 0;b[8]!==n||b[9]!==a||b[10]!==e||b[11]!==o?(g=j.jsx(c("IGDSAvatar.react"),{alt:o,size:n,src:a,xstyle:e}),b[8]=n,b[9]=a,b[10]=e,b[11]=o,b[12]=g):g=b[12];b[13]!==n||b[14]!==f||b[15]!==m?(a=f&&!m&&n!=="largeXL"?j.jsx(c("IGDPresenceBadge.react"),{size:n}):null,b[13]=n,b[14]=f,b[15]=m,b[16]=a):a=b[16];b[17]!==n||b[18]!==m?(e=m&&n!=="largeXL"?j.jsx(c("IGDCheckboxBadge.react"),{size:n}):null,b[17]=n,b[18]=m,b[19]=e):e=b[19];b[20]!==g||b[21]!==a||b[22]!==e||b[23]!==p?(o=j.jsxs("div",{className:i,style:p,children:[g,a,e]}),b[20]=g,b[21]=a,b[22]=e,b[23]=p,b[24]=o):o=b[24];return o}g["default"]=a}),226);
__d("IGDTypingIndicatorContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({typingEvents:[]});g["default"]=b}),98);
__d("IGDTypingIndicatorUtils",["$InternalEnum","FBLogger","I64","ReQL","ReQLSuspense","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=b("$InternalEnum")({TYPING:1,STOPPED_TYPING:0});function a(a){var b;a=a==null?void 0:(a=a.xdt_direct_realtime_event)==null?void 0:(a=a.data)==null?void 0:a[0];b=(b=a==null?void 0:a.path)!=null?b:"";var d=(a=a==null?void 0:a.value)!=null?a:"{}";try{a=JSON.parse(d,function(a,b){if(a==="sender_id"&&typeof b==="number"){a=/\"sender_id\":(\d+)/;a=d.match(a);return a?a[1]:null}return b});var e=/threads\/(\d+)\/activity_indicator_id/;b=b.match(e);e=b?b[1]:null;b=Math.floor(Number(a.timestamp)/1e3);if(e!=null)return{activityStatus:a.activity_status,senderIgId:a.sender_id,threadIgId:e,timestamp:b,ttl:a.ttl}}catch(a){c("FBLogger")("igd_web").catching(a).warn("IGD typing indicator: typing event could not be parsed")}return null}function e(a,b){return(h||(h=d("I64"))).equal(a.threadKey,b)&&a.activityStatus===i.TYPING}function j(a,b){var c=d("react-compiler-runtime").c(4),e,g;c[0]!==a.tables.ig_thread_info||c[1]!==b?(e=function(){return d("ReQL").fromTableDescending(a.tables.ig_thread_info.index("igThreadID")).getKeyRange(b!=null?b:"")},g=[a.tables.ig_thread_info,b],c[0]=a.tables.ig_thread_info,c[1]=b,c[2]=e,c[3]=g):(e=c[2],g=c[3]);c=d("ReQLSuspense").useFirst(e,g,f.id+":99");return c!=null?c.threadKey:null}function k(a,b){var c=d("react-compiler-runtime").c(4),e,g;c[0]!==a.tables.ig_contact_info||c[1]!==b?(e=function(){return d("ReQL").fromTableAscending(a.tables.ig_contact_info).filter(function(a){return a.igId===b})},g=[a.tables.ig_contact_info,b],c[0]=a.tables.ig_contact_info,c[1]=b,c[2]=e,c[3]=g):(e=c[2],g=c[3]);c=d("ReQLSuspense").useFirst(e,g,f.id+":112");return c!=null?c.contactId:null}function l(a,b){var c=(h||(h=d("I64"))).equal(a.threadKey,b.threadKey),e=h.equal(a.senderId,b.senderId);a=a.timestamp<h.to_float(b.timestampMs);return c&&e&&a}function m(a,b){b=n(a,b);a=a.activityStatus===i.TYPING;return b&&a}function n(a,b){return(h||(h=d("I64"))).equal(a.threadKey,b.threadKey)&&(h||(h=d("I64"))).equal(a.senderId,b.senderId)}g.IGDTypingStatus=i;g.transformIGDTypingPayload=a;g.hasTypingOnThread=e;g.useThreadIdFromIGId=j;g.useContactIdFromSenderIGId=k;g.isNewMessageAfterTypingEvent=l;g.shouldTypingEventStop=m;g.isSameThreadSenderEvent=n}),98);
__d("react-relay",["react-relay/ReactRelayContext","react-relay/ReactRelayFragmentContainer","react-relay/ReactRelayLocalQueryRenderer","react-relay/ReactRelayPaginationContainer","react-relay/ReactRelayQueryRenderer","react-relay/ReactRelayRefetchContainer","react-relay/relay-hooks/EntryPointContainer.react","react-relay/relay-hooks/ProfilerContext","react-relay/relay-hooks/RelayEnvironmentProvider","react-relay/relay-hooks/loadEntryPoint","react-relay/relay-hooks/loadQuery","react-relay/relay-hooks/useClientQuery","react-relay/relay-hooks/useEntryPointLoader","react-relay/relay-hooks/useFragment","react-relay/relay-hooks/useLazyLoadQuery","react-relay/relay-hooks/useMutation","react-relay/relay-hooks/usePaginationFragment","react-relay/relay-hooks/usePrefetchableForwardPaginationFragment","react-relay/relay-hooks/usePreloadedQuery","react-relay/relay-hooks/useQueryLoader","react-relay/relay-hooks/useRefetchableFragment","react-relay/relay-hooks/useRelayEnvironment","react-relay/relay-hooks/useSubscribeToInvalidationState","react-relay/relay-hooks/useSubscription","relay-runtime"],(function(a,b,c,d,e,f){"use strict";a=b("react-relay/relay-hooks/loadQuery").loadQuery;e.exports={ConnectionHandler:(c=b("relay-runtime")).ConnectionHandler,QueryRenderer:b("react-relay/ReactRelayQueryRenderer"),LocalQueryRenderer:b("react-relay/ReactRelayLocalQueryRenderer"),MutationTypes:c.MutationTypes,RangeOperations:c.RangeOperations,ReactRelayContext:b("react-relay/ReactRelayContext"),applyOptimisticMutation:c.applyOptimisticMutation,commitLocalUpdate:c.commitLocalUpdate,commitMutation:c.commitMutation,createFragmentContainer:b("react-relay/ReactRelayFragmentContainer").createContainer,createPaginationContainer:b("react-relay/ReactRelayPaginationContainer").createContainer,createRefetchContainer:b("react-relay/ReactRelayRefetchContainer").createContainer,fetchQuery_DEPRECATED:c.fetchQuery_DEPRECATED,graphql:c.graphql,readInlineData:c.readInlineData,requestSubscription:c.requestSubscription,EntryPointContainer:b("react-relay/relay-hooks/EntryPointContainer.react"),RelayEnvironmentProvider:b("react-relay/relay-hooks/RelayEnvironmentProvider"),ProfilerContext:b("react-relay/relay-hooks/ProfilerContext"),fetchQuery:c.fetchQuery,loadQuery:a,loadEntryPoint:b("react-relay/relay-hooks/loadEntryPoint"),useClientQuery:b("react-relay/relay-hooks/useClientQuery"),useFragment:b("react-relay/relay-hooks/useFragment"),useLazyLoadQuery:b("react-relay/relay-hooks/useLazyLoadQuery"),useEntryPointLoader:b("react-relay/relay-hooks/useEntryPointLoader"),useQueryLoader:b("react-relay/relay-hooks/useQueryLoader"),useMutation:b("react-relay/relay-hooks/useMutation"),usePaginationFragment:b("react-relay/relay-hooks/usePaginationFragment"),usePreloadedQuery:b("react-relay/relay-hooks/usePreloadedQuery"),useRefetchableFragment:b("react-relay/relay-hooks/useRefetchableFragment"),usePrefetchableForwardPaginationFragment:b("react-relay/relay-hooks/usePrefetchableForwardPaginationFragment"),useRelayEnvironment:b("react-relay/relay-hooks/useRelayEnvironment"),useSubscribeToInvalidationState:b("react-relay/relay-hooks/useSubscribeToInvalidationState"),useSubscription:b("react-relay/relay-hooks/useSubscription")}}),null);
__d("useIGDTypingIndicatorSubscription_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9712315318850438"}),null);
__d("useIGDTypingIndicatorSubscription.graphql",["useIGDTypingIndicatorSubscription_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"input_data"}],c=[{alias:null,args:[{kind:"Variable",name:"input",variableName:"input_data"}],concreteType:"XDTDirectRealtimeEventResponse",kind:"LinkedField",name:"xdt_direct_realtime_event",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"event",storageKey:null},{alias:null,args:null,concreteType:"XDTDirectRealtimeOperation",kind:"LinkedField",name:"data",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"op",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"path",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"value",storageKey:null}],storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"useIGDTypingIndicatorSubscription",selections:c,type:"Subscription",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"useIGDTypingIndicatorSubscription",selections:c},params:{id:b("useIGDTypingIndicatorSubscription_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_direct_realtime_event"],subscriptionName:"xdt_direct_realtime_event"},name:"useIGDTypingIndicatorSubscription",operationKind:"subscription",text:null}}}();e.exports=a}),null);
__d("useIGDTypingIndicator",["FBLogger","IGDTypingIndicatorUtils","PolarisConfig","react","react-compiler-runtime","react-relay","useIGDTypingIndicatorSubscription.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;(i||d("react")).useMemo;var j=h!==void 0?h:h=b("useIGDTypingIndicatorSubscription.graphql");function a(a){var b=d("react-compiler-runtime").c(6);if(b[0]===Symbol["for"]("react.memo_cache_sentinel")){var c;c=(c=d("PolarisConfig").getViewerId())!=null?c:"";b[0]=c}else c=b[0];c=c;var e;b[1]!==a?(e=function(b){if(b!=null){b=d("IGDTypingIndicatorUtils").transformIGDTypingPayload(b);b!=null&&a(b)}},b[1]=a,b[2]=e):e=b[2];b[3]===Symbol["for"]("react.memo_cache_sentinel")?(c={input_data:{user_id:c}},b[3]=c):c=b[3];b[4]!==e?(c={onError:k,onNext:e,subscription:j,variables:c},b[4]=e,b[5]=c):c=b[5];e=c;b=e;d("react-relay").useSubscription(b)}function k(a){c("FBLogger")("igd_web").catching(a).warn("IGD typing indicator: Error with subscription setup")}g["default"]=a}),98);
__d("IGDTypingIndicatorProvider.react",["IGDTypingIndicatorContext","IGDTypingIndicatorUtils","ReQL","ReQLSuspense","clearInterval","react","react-compiler-runtime","setInterval","useIGDTypingIndicator","useReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));b=i;b.useCallback;var k=b.useEffect;b.useMemo;var l=b.useRef,m=b.useState,n=1e3,o=11e3;function a(a){var b=d("react-compiler-runtime").c(24);a=a.children;var e=(h||(h=c("useReStore")))(),g=m(),i=g[0],p=g[1];b[0]===Symbol["for"]("react.memo_cache_sentinel")?(g=[],b[0]=g):g=b[0];g=m(g);var q=g[0],r=g[1];b[1]===Symbol["for"]("react.memo_cache_sentinel")?(g=[],b[1]=g):g=b[1];g=m(g);var s=g[0],t=g[1],u=l(null),v=d("IGDTypingIndicatorUtils").useThreadIdFromIGId(e,i==null?void 0:i.threadIgId),w=d("IGDTypingIndicatorUtils").useContactIdFromSenderIGId(e,i==null?void 0:i.senderIgId),x;b[2]!==e.tables.messages||b[3]!==q?(g=function(){return d("ReQL").fromTableDescending(e.tables.messages).filter(function(a){var b=q.some(function(b){return d("IGDTypingIndicatorUtils").isNewMessageAfterTypingEvent(b,a)});return b})},x=[q,e.tables.messages],b[2]=e.tables.messages,b[3]=q,b[4]=g,b[5]=x):(g=b[4],x=b[5]);var y=d("ReQLSuspense").useArray(g,x,f.id+":58");b[6]!==y||b[7]!==q?(g=function(){var a=q.filter(function(a){return!y.some(function(b){return d("IGDTypingIndicatorUtils").isNewMessageAfterTypingEvent(a,b)})});t(function(){return a})},x=[q,y],b[6]=y,b[7]=q,b[8]=g,b[9]=x):(g=b[8],x=b[9]);k(g,x);b[10]!==i||b[11]!==w||b[12]!==v?(g=function(){if(i&&v!=null&&w!=null){i.senderIgId;i.threadIgId;var a=babelHelpers.objectWithoutPropertiesLoose(i,["senderIgId","threadIgId"]),b=babelHelpers["extends"]({},a,{senderId:w,threadKey:v});r(function(a){if(b.activityStatus===d("IGDTypingIndicatorUtils").IGDTypingStatus.STOPPED_TYPING)return a.filter(function(a){return!d("IGDTypingIndicatorUtils").shouldTypingEventStop(a,b)});var c=a.findIndex(function(a){return d("IGDTypingIndicatorUtils").isSameThreadSenderEvent(a,b)});return c===-1?[].concat(a,[b]):a.map(function(a,d){return d===c?babelHelpers["extends"]({},a,{timestamp:b.timestamp}):a})})}},x=[i,w,v],b[10]=i,b[11]=w,b[12]=v,b[13]=g,b[14]=x):(g=b[13],x=b[14]);k(g,x);b[15]!==q.length?(g=function(){q.length>0?u.current=c("setInterval")(function(){var a=Date.now();r(function(b){return b.filter(function(b){return b.timestamp+o>a})})},n):q.length===0&&u.current!=null&&(c("clearInterval")(u.current),u.current=null);return function(){u.current&&(c("clearInterval")(u.current),u.current=null)}},x=[q.length],b[15]=q.length,b[16]=x,b[17]=g):(x=b[16],g=b[17]);k(g,x);b[18]===Symbol["for"]("react.memo_cache_sentinel")?(g=function(a){p(a)},b[18]=g):g=b[18];x=g;c("useIGDTypingIndicator")(x);b[19]!==s?(g={typingEvents:s},b[19]=s,b[20]=g):g=b[20];x=g;s=x;b[21]!==a||b[22]!==s?(g=j.jsx(c("IGDTypingIndicatorContext").Provider,{value:s,children:a}),b[21]=a,b[22]=s,b[23]=g):g=b[23];return g}g["default"]=a}),98);
__d("usePresenceUnifiedClientContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");c=b.createContext;var i=b.useContext,j=c(null);function a(){return i(j)}g.PresenceUnifiedClientContext=j;g.usePresenceUnifiedClientContext=a}),98);
__d("usePresenceUnifiedMapContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");c=b.createContext;var i=b.useContext,j=c(new Map());function a(){return i(j)}g.PresenceUnifiedMapContext=j;g.usePresenceUnifiedMapContext=a}),98);
__d("IGPresenceUnifiedContext.react",["CometErrorBoundary.react","CometPlaceholder.react","HeroInteractionIgnoreWithDiv.react","deferredLoadComponent","react","requireDeferred","usePresenceUnifiedClientContext","usePresenceUnifiedMapContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));b=h;var j=b.useEffect,k=b.useState,l=c("deferredLoadComponent")(c("requireDeferred")("IGPresenceUnifiedSetup.react").__setRef("IGPresenceUnifiedContext.react"));function a(a){var b=a.children,e=a.client;a=a.presenceSetupQueryRef;var f=k(new Map()),g=f[0],h=f[1];j(function(){return e.registerListener(function(a){h(new Map(a))})},[e]);return i.jsx(d("usePresenceUnifiedClientContext").PresenceUnifiedClientContext.Provider,{value:e,children:i.jsxs(d("usePresenceUnifiedMapContext").PresenceUnifiedMapContext.Provider,{value:g,children:[i.jsx(c("HeroInteractionIgnoreWithDiv.react"),{children:i.jsx(c("CometErrorBoundary.react"),{fallback:function(){return i.jsx("span",{})},children:i.jsx(c("CometPlaceholder.react"),{fallback:null,children:i.jsx(l,{client:e,presenceSetupQueryRef:a})})})}),b]})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("MWPGetThreadTitle",["fbt","CurrentEnvironment","FBLogger","I64","LSContactBitOffset","LSContactType","LSIntEnum","LSMessagingThreadTypeUtil","intlList","recoverableViolation"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=(c("CurrentEnvironment").workplacedotcom?h._(/*BTDS*/"Workplace user"):c("CurrentEnvironment").instagramdotcom?h._(/*BTDS*/"Instagram User"):c("CurrentEnvironment").horizondotmetadotcom?h._(/*BTDS*/"Horizon user"):h._(/*BTDS*/"Facebook user")).toString();function a(a,b,e,f){a===void 0&&(a=c("intlList").CONJUNCTIONS.AND);e.length>8&&c("recoverableViolation")("You are overfetching participants needed to compute a thread title. Please use ->take(MWPGetThreadTitle.amountToFetchForTitle) to limit participants fetched. Length: "+e.length,"messenger_web_ia");if(e.length===0)return;if(e.length===1){var g=e[0],l=g[0];g=g[1];return l.nickname!=null?l.nickname:(l=g==null?void 0:g.name)!=null?l:k}if(d("LSMessagingThreadTypeUtil").isOneToOne(b)){g=e.filter(function(a){return!(i||(i=d("I64"))).equal(a[0].contactId,f)});if(g.length===0){c("recoverableViolation")("No participants in thread","messenger_comet");return}l=g.sort(function(a,b){return(i||(i=d("I64"))).compare(b[0].authorityLevel,a[0].authorityLevel)})[0];b=l[0].nickname;if(b!=null)return b;g=l[1];return(b=g==null?void 0:g.name)!=null?b:k}l=e.filter(function(a){return!(i||(i=d("I64"))).equal(a[0].contactId,f)}).slice(0,7).sort(function(a,b){b=b[1];a=a[1];if(a!=null)if(b!=null)return b.rank-a.rank;else return 0;else return 0}).map(function(a){var b,f=a[1];f!=null&&(i||(i=d("I64"))).equal(f.contactType,(j||(j=d("LSIntEnum"))).ofNumber(c("LSContactType").PAGE))&&!((b=d("LSContactBitOffset").has(77,f))!=null?b:!1)&&!((b=d("LSContactBitOffset").has(65,f))!=null?b:!1)&&c("FBLogger")("messenger_web").debug("page participant in group");b=a[0].nickname;if(b!=null)return b;if(f==null)return k;a=f.firstName;if(a!=null&&e.length>2)return a;else return f.name});e.length>7&&l.push(String(h._(/*BTDS*/"others")));return c("intlList")(l,a,c("intlList").DELIMITERS.COMMA).toString()}b=8;g.computeThreadTitle=a;g.amountToFetchForTitle=b}),226);
__d("MWPVisibleMessageContext.react",["ReQL","ReQLSuspense","emptyFunction","react","useReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));e=i;var k=e.createContext,l=e.useContext,m=e.useEffect,n=e.useMemo,o=e.useState;e={setVisibleMessage:c("emptyFunction"),visibleMessage:void 0};var p=k(e);function a(a){a=a.children;var b=o(),e=b[0],g=b[1],i=(h||(h=c("useReStore")))(),k=e==null?void 0:e.messageOtid,l=e==null?void 0:e.messageId,q=(b=d("ReQLSuspense").useFirst(function(){return k!=null?d("ReQL").fromTableAscending(i.tables.messages.index("optimistic")).getKeyRange(k):d("ReQL").empty()},[i,k],f.id+":64"))==null?void 0:b.messageId;m(function(){q!=null&&l==null&&g(function(a){return a==null?null:babelHelpers["extends"]({},a,{messageId:q})})},[g,l,q]);b=n(function(){return{setVisibleMessage:g,visibleMessage:e}},[e,g]);return j.jsx(p.Provider,{value:b,children:a})}a.displayName=a.name+" [from "+f.id+"]";function b(){return l(p)}g.MWPVisibleMessageContext=p;g.MWPVisibleMessageContextProvider=a;g.useMWPVisibleMessageContext=b}),98);
__d("MWXSpinner.react",["fbt","CometScreenReaderText.react","cr:5028","cr:8931","gkx","react"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react"),k=function(a){if(a<=12)return 12;else if(a<=16)return 16;else if(a<=24)return 24;else return 32};function a(a){var d=a.color,e=a.size;a=a.xstyle;d=c("gkx")("13045")&&d==="disabled"?"dark":d;if(b("cr:8931")!=null)return j.jsxs("div",{children:[j.jsx(c("CometScreenReaderText.react"),{text:h._(/*BTDS*/"Loading...")}),j.jsx(b("cr:8931"),{color:d==="disabled"?"disabled_DEPRECATED":d,isDecorative:!0,size:e,xstyle:a})]});return b("cr:5028")!=null?j.jsx(b("cr:5028"),{color:d==="blue"?"blue":"grey",size:k(16),xstyle:a}):null}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("PolarisViewerSettingsContext.react",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||(h=d("react"));var i=h.useContext,j=b.createContext({isReduceMotionSettingEnabled:!1});function a(){return i(j)}g.PolarisViewerSettingsContext=j;g.usePolarisViewerSettings=a}),98);
__d("PolarisViewerSettingsContextProviderQuery.graphql",["PolarisViewerSettingsContextProviderQuery_instagramRelayOperation","relay-runtime"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={alias:null,args:null,kind:"ScalarField",name:"is_reduce_motion_setting_enabled",storageKey:null};return{fragment:{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisViewerSettingsContextProviderQuery",selections:[{alias:null,args:null,concreteType:"Viewer",kind:"LinkedField",name:"viewer",plural:!1,selections:[{alias:null,args:null,concreteType:"User",kind:"LinkedField",name:"user",plural:!1,selections:[a],storageKey:null}],storageKey:null}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[],kind:"Operation",name:"PolarisViewerSettingsContextProviderQuery",selections:[{alias:null,args:null,concreteType:"Viewer",kind:"LinkedField",name:"viewer",plural:!1,selections:[{alias:null,args:null,concreteType:"User",kind:"LinkedField",name:"user",plural:!1,selections:[a,{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null}],storageKey:null}],storageKey:null}]},params:{id:b("PolarisViewerSettingsContextProviderQuery_instagramRelayOperation"),metadata:{},name:"PolarisViewerSettingsContextProviderQuery",operationKind:"query",text:null}}}();b("relay-runtime").PreloadableQueryRegistry.set(a.params.id,a);e.exports=a}),null);
__d("PolarisViewerSettingsContextProvider.react",["CometRelay","FBLogger","PolarisViewerSettingsContext.react","PolarisViewerSettingsContextProviderQuery.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));i.useMemo;function a(a){var b=d("react-compiler-runtime").c(3),e=a.children;a=a.viewerSettingsQueryRef;if(a==null){c("FBLogger")("PolarisViewerSettingsContextProvider").mustfix("viewerSettingsQueryRef is null but settings context is rendered");return e}var f;b[0]!==e||b[1]!==a?(f=j.jsx(k,{children:e,viewerSettingsQueryRef:a}),b[0]=e,b[1]=a,b[2]=f):f=b[2];return f}function k(a){var c=d("react-compiler-runtime").c(5),e=a.children;a=a.viewerSettingsQueryRef;a=d("CometRelay").usePreloadedQuery(h!==void 0?h:h=b("PolarisViewerSettingsContextProviderQuery.graphql"),a);a=(a=(a=a.viewer)==null?void 0:(a=a.user)==null?void 0:a.is_reduce_motion_setting_enabled)!=null?a:!1;var f;c[0]!==a?(f={isReduceMotionSettingEnabled:a},c[0]=a,c[1]=f):f=c[1];a=f;f=a;c[2]!==e||c[3]!==f?(a=j.jsx(d("PolarisViewerSettingsContext.react").PolarisViewerSettingsContext.Provider,{value:f,children:e}),c[2]=e,c[3]=f,c[4]=a):a=c[4];return a}g["default"]=a}),98);
__d("PolarisViewerSettingsContextProviderStub.react",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;h||d("react");function a(a){a=a.children;return a}g["default"]=a}),98);
__d("useGetContactsToShow",["FBLogger","I64","Int64Hooks","LSMessagingThreadTypeUtil","getLSMediaContactProfilePictureUrl"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b,e){var f=function(a){return a.filter(function(a){return!(h||(h=d("I64"))).equal(a.id,e)})},g=b.length,i=[],j=[];if(g!==0){var k=b[0],l=b[1];j=f(b.filter(Boolean).filter(function(a){return c("getLSMediaContactProfilePictureUrl")(a)!==""}));switch(g){case 1:k!=null&&c("getLSMediaContactProfilePictureUrl")(k)!==""&&(i=[k]);break;case 2:d("LSMessagingThreadTypeUtil").isOneToOne(a.threadType)&&j.length===1&&c("getLSMediaContactProfilePictureUrl")(j[0])!==""?i=[j[0]]:d("LSMessagingThreadTypeUtil").isGroup(a.threadType)&&k!=null&&l!=null&&c("getLSMediaContactProfilePictureUrl")(k)!==""&&c("getLSMediaContactProfilePictureUrl")(l)!==""&&(i=[k,l]);break;default:j.length>=2&&(i=j)}}var m=d("LSMessagingThreadTypeUtil").isOneToOne(a.threadType)&&j.length===0,n=b.find(function(a){return a!=null&&(h||(h=d("I64"))).equal(a.id,e)}),o=i.sort(function(a,b){return b.rank-a.rank}).slice(0,2);d("Int64Hooks").useEffectInt64(function(){var b;g===2&&!m&&d("LSMessagingThreadTypeUtil").isOneToOne(a.threadType)&&o.length===1&&(h||(h=d("I64"))).equal(o[0].id,(b=n==null?void 0:n.id)!=null?b:(h||(h=d("I64"))).minus_one)&&c("FBLogger")("messenger_web").mustfix("One to one thread with participants is showing self contact. Num contacts without self: %s",j.length)},[g,m,a.threadType,o,n,j.length]);return o}g["default"]=a}),98);
__d("useLSGetThreadTitle.react",["MWPActor.react","MWPGetThreadTitle","ReQL","ReQLSuspense","intlList","useReStore"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){var e=d("MWPActor.react").useActor(),g=(h||(h=c("useReStore")))(),i=d("ReQLSuspense").useArray(function(){return b===!0?d("ReQL").empty():d("ReQL").leftJoin(d("ReQL").fromTableAscending(g.tables.participants,["contactId","nickname","authorityLevel"]).getKeyRange(a.threadKey),d("ReQL").fromTableAscending(g.tables.contacts,["name","rank","contactType","firstName","capabilities","capabilities2","secondaryName","id","contactTypeExact","blockedByViewerStatus"])).take(8)},[a.threadKey],f.id+":65");if(b===!0)return{actorId:e,participantsAndContacts:i,threadTitle:null};var j=d("MWPGetThreadTitle").computeThreadTitle(c("intlList").CONJUNCTIONS.NONE,a.threadType,i,e);return{actorId:e,participantsAndContacts:i,threadTitle:j}}g["default"]=a}),98);
__d("useMWFacepileGetContactsToShow",["I64","LSAuthorityLevel","LSGroupParticipantJoinState","LSIntEnum","MWPActor.react","ReQL","ReQLSuspense","useGetContactsToShow","useReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function a(a,b,e){e===void 0&&(e=!1);var g=d("MWPActor.react").useActor(),k=(j||(j=c("useReStore")))(),l=d("ReQLSuspense").useArray(function(){return d("ReQL").leftJoin(d("ReQL").fromTableAscending(k.tables.participants,["groupParticipantJoinState"]).getKeyRange(a),d("ReQL").fromTableAscending(k.tables.contacts,["rank","profilePictureFallbackUrl","profilePictureUrl","profilePictureUrlExpirationTimestampMs","id","name","authorityLevel"])).filter(function(a){var b=a[0];a=a[1];return(h||(h=d("LSIntEnum"))).toNumber(b.groupParticipantJoinState)!==c("LSGroupParticipantJoinState").MEMBER?!1:!e||a!=null&&e&&(i||(i=d("I64"))).to_int32(a.authorityLevel)>=c("LSAuthorityLevel").AUTHORITATIVE}).take(3)},[k,a,e],f.id+":61").map(function(a){a[0];a=a[1];return a});return c("useGetContactsToShow")({threadKey:a,threadType:b},l,g)}g["default"]=a}),98);