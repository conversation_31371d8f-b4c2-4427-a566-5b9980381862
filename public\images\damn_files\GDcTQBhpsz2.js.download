;/*FB_PKG_DELIM*/

__d("IGDChatTabsTooltipWrapper.react",["IGDSTooltip.react","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=a.children,d=a.isVisible;a=a.tooltip;return a==null?b:i.jsx(c("IGDSTooltip.react"),{align:"middle",color:"primary",isVisible:d,position:"above",tooltip:a,children:b})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDChatTabsPOG.react",["CometPressable.react","I64","IGDChatTabsStateContext.react","IGDChatTabsStateTypes","IGDChatTabsTooltipWrapper.react","IGDFallbackProfileUrl","IGDSBox.react","IGDSText.react","IGDSingleAvatar.react","IGDThreadListNewMessageLoggingDataContext","MessengerWebUXLogger","getLSMediaContactProfilePictureUrl","getLSMediaThreadPictureUrl","react","useLSGetThreadTitle.react","useMWFacepileGetContactsToShow"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=h||(h=d("react"));b=h;var k=b.useCallback,l=b.useMemo,m=b.useState,n={pog:{borderTopColor:"xbe7ycp",borderInlineEndColor:"x1npm9xp",borderBottomColor:"x5p4dl0",borderInlineStartColor:"x179w5xb",borderStartStartRadius:"x1c9tyrk",borderStartEndRadius:"xeusxvb",borderEndEndRadius:"x1pahc9y",borderEndStartRadius:"x1ertn4p",borderTopStyle:"x13fuv20",borderInlineEndStyle:"x18b5jzi",borderBottomStyle:"x1q0q8m5",borderInlineStartStyle:"x1t7ytsu",borderTopWidth:"xamhcws",borderInlineEndWidth:"x1alpsbp",borderBottomWidth:"xlxy82",borderInlineStartWidth:"xyumdvf",marginInlineStart:"xw01apr",zIndex:"x1vjfegm",$$css:!0}};function a(a){var b=a.isUnreadPOG,e=a.thread,f=d("IGDChatTabsStateContext.react").useIGDChatTabsDispatch();a=c("getLSMediaThreadPictureUrl")(e);var g=c("useLSGetThreadTitle.react")(e),h=g.threadTitle;g=c("useMWFacepileGetContactsToShow")(e.threadKey,e.threadType);g=g[0]!=null?c("getLSMediaContactProfilePictureUrl")(g[0]):d("IGDFallbackProfileUrl").FALLBACK_PROFILE_PIC_URL;a=a!=null?a:g;var o=d("IGDThreadListNewMessageLoggingDataContext").useFlowInstanceIdContext(),p=c("MessengerWebUXLogger").useInteractionLogger();g=c("MessengerWebUXLogger").useImpressionLoggerRef({eventName:"igd_chat_tabs_pog",extraData:{isUnreadPOG:String(b)},flowInstanceId:o});var q=l(function(){var a;return j.jsx(c("IGDSBox.react"),{maxWidth:95,children:j.jsx(c("IGDSText.react"),{maxLines:1,size:"footnote",weight:"medium",zeroMargin:!0,children:(a=e.threadName)!=null?a:h})})},[e.threadName,h]),r=k(function(){f==null?void 0:f({source:d("IGDChatTabsStateTypes").IGDChatTabsMessagingInitiationSource.ChatTabsPOG,threadKey:(i||(i=d("I64"))).to_string(e.threadKey),type:"open_tab"}),p==null?void 0:p({eventName:"igd_chat_tabs_pog",extraData:{isUnreadPOG:String(b)},flowInstanceId:o})},[f,o,b,p,e.threadKey]),s=m(!1),t=s[0],u=s[1];return j.jsx(c("IGDChatTabsTooltipWrapper.react"),{isVisible:t,tooltip:q,children:j.jsx(c("CometPressable.react"),{onHoverIn:function(){u(!0)},onHoverOut:function(){u(!1)},onPress:r,ref:g,role:"button",xstyle:n.pog,children:j.jsx(c("IGDSingleAvatar.react"),{size:"tiny",src:a})})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDChatTabsUnreadOverflowPlaceholder.react",["IGDSBox.react","IGDSConstants","IGDSGlimmer.react","IGDSPopoverLoadingState.react","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={glimmer:{height:"x5yr21d",width:"xh8yej3",$$css:!0},root:{borderStartStartRadius:"x1ua1ujl",borderStartEndRadius:"xksyday",borderEndEndRadius:"xshg46c",borderEndStartRadius:"xlej2ay",marginBottom:"x1hq5gj4",$$css:!0}};function k(){return i.jsxs(c("IGDSBox.react"),{alignItems:"center",direction:"row",children:[i.jsx(c("IGDSBox.react"),{height:c("IGDSConstants").AVATAR_SIZES.tiny,marginEnd:2,overflow:"hidden",shape:"circle",width:c("IGDSConstants").AVATAR_SIZES.tiny,children:i.jsx(c("IGDSGlimmer.react"),{index:0,xstyle:j.glimmer})}),i.jsx(c("IGDSBox.react"),{height:12,overflow:"hidden",width:160,children:i.jsx(c("IGDSGlimmer.react"),{index:1,xstyle:j.glimmer})})]})}k.displayName=k.name+" [from "+f.id+"]";function a(){return i.jsx(c("IGDSPopoverLoadingState.react"),{popoverContent:i.jsxs(c("IGDSBox.react"),{alignItems:"start",direction:"column",maxWidth:300,padding:3,children:[i.jsx(k,{}),i.jsx(k,{}),i.jsx(k,{})]}),popoverXStyle:j.root})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDChatTabsUnreadOverflowPopover.react",["fbt","CometPressable.react","IGDChatTabsEnv","IGDChatTabsUnreadOverflowPlaceholder.react","IGDSLazyPopoverTrigger.react","IGDSMoreHorizontalPanoOutlineIcon.react","IGDThreadListNewMessageLoggingDataContext","JSResourceForInteraction","MessengerWebUXLogger","react"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||(i=d("react")),k=i.useCallback,l={moreUnreadIndicator:{alignItems:"x6s0dn4",backgroundColor:"x1gjpkn9",borderStartStartRadius:"x1c9tyrk",borderStartEndRadius:"xeusxvb",borderEndEndRadius:"x1pahc9y",borderEndStartRadius:"x1ertn4p",height:"xxk0z11",justifyContent:"xl56j7k",marginInlineStart:"xw01apr",width:"xvy4d1p",$$css:!0}},m=c("JSResourceForInteraction")("IGDChatTabsUnreadOverflow.react").__setRef("IGDChatTabsUnreadOverflowPopover.react");function a(a){var b=a.onChatTabJewelClick;a=a.unreadThreads;var e=d("IGDThreadListNewMessageLoggingDataContext").useFlowInstanceIdContext(),f=c("MessengerWebUXLogger").useInteractionLogger(),g=c("MessengerWebUXLogger").useImpressionLoggerRef({eventName:"igd_chat_tabs_pog_overflow_icon",flowInstanceId:e}),i=a.length,n=h._(/*BTDS*/"_j{\"*\":\"{number of unread chats} unread chats\",\"_1\":\"{number of unread chats} unread chat\"}",[h._plural(i),h._param("number of unread chats",i)]);i=a.slice(d("IGDChatTabsEnv").MAX_POGS);var o=k(function(){b(),f==null?void 0:f({eventName:"igd_chat_tabs_pog_overflow_icon",flowInstanceId:e})},[e,f,b]);return j.jsx(c("IGDSLazyPopoverTrigger.react"),{align:"end",fallback:j.jsx(c("IGDChatTabsUnreadOverflowPlaceholder.react"),{}),popoverProps:{unreadThreads:i},popoverResource:m,popoverType:"menu",position:"above",children:function(a,b,d){return j.jsx(c("CometPressable.react"),{onHoverIn:b,onHoverOut:d,onPress:o,ref:a,xstyle:l.moreUnreadIndicator,children:j.jsx("div",{ref:g,children:j.jsx(c("IGDSMoreHorizontalPanoOutlineIcon.react"),{alt:n,size:16})})})}})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("useIGDChatTabsGetTopThreads.react",["I64","LSIntEnum","LSThreadBitOffset","MWThreadListSupportedThreadTypes","ReQL","ReQLSuspense","useReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function a(a){var b=(j||(j=c("useReStore")))(),e=d("ReQLSuspense").useArray(function(){return d("ReQL").fromTableDescending(b.tables.threads.index("parentThreadKeyLastActivityTimestampMs"),["takedownState","threadType","lastReadWatermarkTimestampMs","lastActivityTimestampMs","muteExpireTimeMs","isHidden","threadKey","threadPictureUrl","threadPictureUrlExpirationTimestampMs","threadPictureUrlFallback","threadSubtype","threadName","consistentThreadFbid","memberCount","nullstateDescriptionText1","parentThreadKey","pauseThreadTimestamp"].concat(d("LSThreadBitOffset").threadCapabilityFields)).getKeyRange((h||(h=d("LSIntEnum"))).ofNumber(0)).filter(function(a){return c("MWThreadListSupportedThreadTypes").findIndex(function(b){return(i||(i=d("I64"))).equal(a.threadType,b)})!==-1}).take(a)},[b.tables.threads,a],f.id+":60");return e}g["default"]=a}),98);
__d("IGDChatTabsPOGs.react",["I64","IGDChatTabsEnv","IGDChatTabsPOG.react","IGDChatTabsUnreadOverflowPopover.react","IGDSBox.react","react","useIGDChatTabsGetTopThreads.react"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=h||d("react");function a(a){var b=a.onChatTabJewelClick;a=a.unreadThreads;var e=a.length,f=c("useIGDChatTabsGetTopThreads.react")(d("IGDChatTabsEnv").MAX_POGS),g=a.slice(0,d("IGDChatTabsEnv").MAX_POGS);g=e>0?g:f;return j.jsxs(c("IGDSBox.react"),{alignItems:"center",direction:"rowReverse",children:[e>d("IGDChatTabsEnv").MAX_POGS&&j.jsx(c("IGDChatTabsUnreadOverflowPopover.react"),{onChatTabJewelClick:b,unreadThreads:a}),g.toReversed().map(function(a){return j.jsx(c("IGDChatTabsPOG.react"),{isUnreadPOG:e>0,thread:a},(i||(i=d("I64"))).to_string(a.threadKey))})]})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("SoundPlayer",["ODS","URI","createArrayFromMixed"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=new Map();function k(a){var b=new(i||(i=c("URI")))(a);return b.getDomain()?a:new(i||(i=c("URI")))(window.location.href).setPath(b.getPath()).toString()}function l(a){a=new(i||(i=c("URI")))(a).getPath();if(/\.mp3$/.test(a))return"audio/mpeg";return/\.og[ga]$/.test(a)?"audio/ogg":""}var m=function(a,b){for(a of c("createArrayFromMixed")(a)){if(j.has(a))return;var d=document.createElement("audio");if(!d||!d.canPlayType||!d.canPlayType(l(a)))continue;d.preload="auto";d.src=k(a);document.body&&document.body.appendChild(d);j.set(a,d);(b==null?void 0:b.onPreload)!=null&&b.onPreload(d);return}};a=function(a,b){b===void 0&&(b={});for(a of c("createArrayFromMixed")(a)){j.has(a)||m(a,b.callbacks);var e=j.get(a);if(!e)continue;b.loop&&e.setAttribute("loop","");b.volume&&(e.volume=b.volume);e=e.play();e!=null&&typeof e.then==="function"?e.then(function(){(h||(h=d("ODS"))).bumpEntityKey(2966,"sound_player","play.success")})["catch"](function(a){(h||(h=d("ODS"))).bumpEntityKey(2966,"sound_player","play.error")}):(h||(h=d("ODS"))).bumpEntityKey(2966,"sound_player","non_promise");return}};b=function(a){for(a of c("createArrayFromMixed")(a)){var b=j.get(a);if(b){b.pause();return}}};e=function(a){for(a of c("createArrayFromMixed")(a)){var b=j.get(a);b&&(b.pause(),b.removeAttribute("src"),b.src=k(a))}};g.preload=m;g.play=a;g.pause=b;g.stop=e}),98);
__d("SoundSynchronizer",["SoundPlayer","WebStorage","createArrayFromMixed"],(function(a,b,c,d,e,f){var g,h="fb_sounds_playing3";function i(){var a=(g||(g=b("WebStorage"))).getLocalStorage();if(a)try{a=a[h];if(a){a=JSON.parse(a);if(Array.isArray(a))return a}}catch(a){}return[]}function j(a){var c=(g||(g=b("WebStorage"))).getLocalStorage();if(c){var d=i();d.push(a);while(d.length>5)d.shift();try{c[h]=JSON.stringify(d)}catch(a){}}}function k(a){return i().some(function(b){return b===a})}a={play:function(a,c,d,e){a=b("createArrayFromMixed")(a);c=c||a[0]+Math.floor(Date.now()/1e3);if(k(c))return;b("SoundPlayer").play(a,{loop:!!d,callbacks:e});j(c)},isSupported:function(){return!!(g||(g=b("WebStorage"))).getLocalStorage()}};e.exports=a}),null);
__d("SoundRPC",["FBJSON","SecurePostMessage","SoundSynchronizer","cr:950105"],(function(a,b,c,d,e,f,g){function h(a,b,c,e){d("SoundSynchronizer").play(a,b,c,e)}function a(a,b,c,e){b={name:"SoundRPC",data:{paths:b,sync:c,loop:e}};d("SecurePostMessage").sendMessageAllowAnyOrigin_UNSAFE(a,d("FBJSON").stringify(b))}function c(){return!!window.postMessage}function e(){var a=function(a){if(!/\.facebook.com$/.test(a.origin))return;var b={};try{a=a.data;typeof a==="string"&&(b=d("FBJSON").parse(a))}catch(a){}a=b;b=a.name;a=a.data;b==="SoundRPC"&&a!=null&&typeof a==="object"&&h(a.paths,a.sync,a.loop)};b("cr:950105")!=null?b("cr:950105").listen(window,"message",a):window.addEventListener("message",a)}g.playLocal=h;g.playRemote=a;g.supportsRPC=c;g._listen=e}),98);
__d("Sound",["SoundInitialData","SoundPlayer","SoundRPC","SoundSynchronizer","URI","UserAgent_DEPRECATED","Visibility","isFacebookURI"],(function(a,b,c,d,e,f,g){var h,i=null,j=!1;function a(a){}function k(a,b,c,e){i?d("SoundRPC").playRemote(i.contentWindow,a,b,!1):d("SoundRPC").playLocal(a,b,c,e),j=!0}function b(){return j}function e(a,b,d){if(!j&&c("Visibility").isHidden())return;k(a,b,d)}function f(a){i||d("SoundPlayer").stop(a)}var l=new(h||c("URI"))(location.href),m=new Set(["comet","www","business"]);l.getSubdomain()&&!m.has(l.getSubdomain())&&l.setSubdomain("www");m=l.getDomain();function n(){if(d("UserAgent_DEPRECATED").ie()<9)return!1;return c("SoundInitialData").RPC_DISABLED?!1:d("SoundSynchronizer").isSupported()&&d("SoundRPC").supportsRPC()}c("isFacebookURI")(l)&&location.host!==m&&n()&&(i=document.createElement("iframe"),i.setAttribute("src","//"+m+"/sound_iframe.php"),i.style.display="none",document.body&&document.body.appendChild(i));g.init=a;g.play=k;g.hasPlayedSoundBefore=b;g.playOnlyIfImmediate=e;g.stop=f}),98);