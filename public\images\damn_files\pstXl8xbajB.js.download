;/*FB_PKG_DELIM*/

__d("hyperionChannel",["hyperionHook"],(function(a,b,c,d,e,f,g){var h;a=function(){function a(){this.$1=new((h||(h=d("hyperionHook"))).Hook)()}var b=a.prototype;b.pipe=function(a,b){var c=this.$1.add(b?function(c){for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];b(function(){a.emit.apply(a,[c].concat(e))})}:function(b){for(var c=arguments.length,d=new Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];a.emit.apply(a,[b].concat(d))});c.$2=a;return a};b.unpipe=function(a){return this.$1.removeIf(function(b){return b.$2===a})};b.emit=function(a){var b;for(var c=arguments.length,d=new Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];(b=this.$1).call.apply(b,[a].concat(d))};return a}();b=function(a){babelHelpers.inheritsLoose(b,a);function b(){var b,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(b=c=a.call.apply(a,[this].concat(e))||this,c.$Channel1=Object.create(null),b)||babelHelpers.assertThisInitialized(c)}var c=b.prototype;c.$Channel2=function(a){var b=this.$Channel1[a];b||(b=this.$Channel1[a]=new((h||(h=d("hyperionHook"))).Hook)());return b};c.on=function(a){return this.$Channel2(a)};c.addListener=function(a,b){return this.on(a).add(b)};c.removeListener=function(a,b){this.on(a).remove(b);return b};c.emit=function(b){var c,d;for(var e=arguments.length,f=new Array(e>1?e-1:0),g=1;g<e;g++)f[g-1]=arguments[g];(c=this.$Channel2(b)).call.apply(c,f);(d=a.prototype.emit).call.apply(d,[this,b].concat(f))};return b}(a);c=function(a){babelHelpers.inheritsLoose(b,a);function b(){var b,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(b=c=a.call.apply(a,[this].concat(e))||this,c.$PausableChannel1=!1,b)||babelHelpers.assertThisInitialized(c)}var c=b.prototype;c.pause=function(){this.$PausableChannel1=!0};c.unpause=function(){this.$PausableChannel1=!1};c.isPaused=function(){return this.$PausableChannel1};c.emit=function(b){var c;if(this.$PausableChannel1)return;for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];(c=a.prototype.emit).call.apply(c,[this,b].concat(e))};return b}(b);g.Channel=b;g.PausableChannel=c;g.PipeableEmitter=a}),98);
__d("ALChannel",["hyperionChannel"],(function(a,b,c,d,e,f,g){"use strict";a=new(d("hyperionChannel").Channel)();b=a;g["default"]=b}),98);
__d("BanzaiUtils",["BanzaiConsts","FBLogger","cr:1172","cr:9985","cr:9986"],(function(a,b,c,d,e,f){"use strict";var g,h={canSend:function(a){return a[2]>=b("cr:9985")()-(g||(g=b("BanzaiConsts"))).EXPIRY},filterPost:function(a,c,d,e){if(e.overlimit)return!0;if(!e.sendMinimumOnePost&&a[4]+e.currentSize>(g||(g=b("BanzaiConsts"))).BATCH_SIZE_LIMIT)return!0;var f=a.__meta;if(f.status!=null&&f.status>=(g||(g=b("BanzaiConsts"))).POST_SENT||!h.canSend(a))return!1;if(f.status!=null&&f.status>=(g||(g=b("BanzaiConsts"))).POST_INFLIGHT)return!0;var i=f.compress!=null?f.compress:!0,j=(f.webSessionId!=null?f.webSessionId:"null")+(f.userID!=null?f.userID:"null")+(f.appID!=null?f.appID:"null")+(i?"compress":""),k=e.wadMap.get(j);k||(k={app_id:f.appID,needs_compression:i,posts:[],user:f.userID,webSessionId:f.webSessionId},e.wadMap.set(j,k),c.push(k));f.status=(g||(g=b("BanzaiConsts"))).POST_INFLIGHT;Array.isArray(k.posts)?k.posts.push(a):b("FBLogger")("banzai").mustfix("Posts were a string instead of array");d.push(a);e.currentSize+=a[4];e.currentSize>=(g||(g=b("BanzaiConsts"))).BATCH_SIZE_LIMIT&&(e.overlimit=!0);return e.keepRetryable&&Boolean(f.retry)},resetPostStatus:function(a){a.__meta.status=(g||(g=b("BanzaiConsts"))).POST_READY},retryPost:function(a,c,d){var e=a;e.__meta.status=(g||(g=b("BanzaiConsts"))).POST_READY;e[3]=(e[3]||0)+1;e.__meta.retry!==!0&&c>=400&&c<600&&d.push(a)},wrapData:function(a,c,d,e,f){a=[a,c,d,0,f!=null?f:c?JSON.stringify(c).length:0];a.__meta={appID:b("cr:9986").getAppID(),retry:e===!0,status:(g||(g=b("BanzaiConsts"))).POST_READY,userID:b("cr:9986").getPossiblyNonFacebookUserID(),webSessionId:b("cr:1172").getId()};return a}};e.exports=h}),null);
__d("NavigationMetrics",["cr:6016"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:6016")}),98);
__d("cancelIdleCallback",["cr:7384"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:7384")}),98);
__d("requestIdleCallbackAcrossTransitions",["IdleCallbackImplementation","TimeSlice"],(function(a,b,c,d,e,f,g){var h=a.requestIdleCallback||d("IdleCallbackImplementation").requestIdleCallback;function b(b,d){b=c("TimeSlice").guard(b,"requestIdleCallback",{propagationType:c("TimeSlice").PropagationType.CONTINUATION,registerCallStack:!0});return h.call(a,b,d)}g["default"]=b}),98);
__d("SetIdleTimeoutAcrossTransitions",["NavigationMetrics","cancelIdleCallback","clearTimeout","nullthrows","requestIdleCallbackAcrossTransitions","setTimeoutAcrossTransitions"],(function(a,b,c,d,e,f,g){"use strict";var h=!1,i=new Map();function b(a,b){if(h){var d=c("setTimeoutAcrossTransitions")(function(){var b=c("requestIdleCallbackAcrossTransitions")(function(){a(),i["delete"](b)});i.set(d,b)},b);return d}else return c("setTimeoutAcrossTransitions")(a,b)}function d(a){c("clearTimeout")(a),i.has(a)&&(c("cancelIdleCallback")(c("nullthrows")(i.get(a))),i["delete"](a))}c("NavigationMetrics").addRetroactiveListener(c("NavigationMetrics").Events.EVENT_OCCURRED,function(b,c){c.event==="all_pagelets_loaded"&&(h=!!a.requestIdleCallback)});g.start=b;g.clear=d}),98);
__d("BanzaiStorage",["BanzaiConsts","BanzaiUtils","CurrentUser","SetIdleTimeoutAcrossTransitions","WebSession","WebStorage","WebStorageMutex","cr:8958","isInIframe","performanceAbsoluteNow"],(function(a,b,c,d,e,f){"use strict";var g,h,i,j="bz:",k=b("isInIframe")(),l,m=!1,n=null;function o(){var a="check_quota";try{var b=p();if(!b)return!1;b.setItem(a,a);b.removeItem(a);return!0}catch(a){return!1}}function p(){m||(m=!0,l=(g||(g=b("WebStorage"))).getLocalStorage());return l}a={flush:function(a){if(k)return;var c=p();if(c){n==null&&(n=parseInt(c.getItem((h||(h=b("BanzaiConsts"))).LAST_STORAGE_FLUSH),10));var d=n&&(i||(i=b("performanceAbsoluteNow")))()-n>=(h||(h=b("BanzaiConsts"))).STORAGE_FLUSH_INTERVAL;d&&a();(d||!n)&&(n=(i||(i=b("performanceAbsoluteNow")))(),(g||(g=b("WebStorage"))).setItemGuarded(c,(h||(h=b("BanzaiConsts"))).LAST_STORAGE_FLUSH,n.toString()))}},restore:function(a){if(k)return;var c=p();if(!c)return;var d=function(d){var e=[];for(var f=0;f<c.length;f++){var g=c.key(f);typeof g==="string"&&g.indexOf(j)===0&&g.indexOf("bz:__")!==0&&e.push(g)}e.forEach(function(d){var e=c.getItem(d);c.removeItem(d);if(e==null||e==="")return;d=b("cr:8958").parse(e);d.forEach(function(c){if(!c)return;var d=c.__meta=c.pop(),e=b("BanzaiUtils").canSend(c);if(!e)return;e=b("CurrentUser").getPossiblyNonFacebookUserID();(d.userID===e||e==="0")&&(b("BanzaiUtils").resetPostStatus(c),a(c))})});d&&d.unlock()};o()?new(b("WebStorageMutex"))("banzai").lock(d):b("SetIdleTimeoutAcrossTransitions").start(d,0)},store:function(a){if(k)return;var c=p(),d=a.filter(function(a){return a.__meta.status!==(h||(h=b("BanzaiConsts"))).POST_SENT});if(!c||d.length<=0)return;d=d.map(function(a){return[a[0],a[1],a[2],a[3]||0,a[4],a.__meta]});a.splice(0,a.length);(g||(g=b("WebStorage"))).setItemGuarded(c,j+b("WebSession").getId()+"."+(i||(i=b("performanceAbsoluteNow")))(),b("cr:8958").stringify(d))}};e.exports=a}),null);
__d("QueryString",[],(function(a,b,c,d,e,f){function g(a){var b=[];Object.keys(a).sort().forEach(function(c){var d=a[c];if(d===void 0)return;if(d===null){b.push(c);return}b.push(encodeURIComponent(c)+"="+encodeURIComponent(String(d)))});return b.join("&")}function a(a,b){b===void 0&&(b=!1);var c={};if(a==="")return c;a=a.split("&");for(var d=0;d<a.length;d++){var e=a[d].split("=",2),f=decodeURIComponent(e[0]);if(b&&Object.prototype.hasOwnProperty.call(c,f))throw new URIError("Duplicate key: "+f);c[f]=e.length===2?decodeURIComponent(e[1]):null}return c}function b(a,b){return a+(a.indexOf("?")!==-1?"&":"?")+(typeof b==="string"?b:g(b))}c={encode:g,decode:a,appendToUrl:b};f["default"]=c}),66);
__d("BanzaiAdapterComet",["BanzaiConfig","BanzaiConsts","BanzaiStorage","BaseEventEmitter","ExecutionEnvironment","FBLogger","HasteBitMapName","JSScheduler","NetworkStatus","QueryString","Run","SiteData","StaticSiteData","URI","UserAgent","ZeroRewrites","getAsyncHeaders","getAsyncParams","gkx","justknobx","objectValues","once","unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=[],m=new(c("BaseEventEmitter"))(),n="/ajax/bz",o="POST",p={cleanup:function(){var a=l;l=[];a.forEach(function(a){a.readyState<4&&a.abort()})},config:c("BanzaiConfig"),getEndPointUrl:function(a){var b=c("getAsyncParams")(o);c("objectValues")(c("HasteBitMapName")).forEach(function(a){return delete b[a]});delete b[c("StaticSiteData").jsmod_key];b.ph=c("SiteData").push_phase;a=n;c("justknobx")._("55")&&c("gkx")("23403")?a="/a/bz":c("gkx")("23404")?a="/ajax/bnzai":c("gkx")("4070")&&(a="/a/fl");a=c("QueryString").appendToUrl(a,b);if(a.length>2e3)throw c("unrecoverableViolation")("url is too long: ${url}","comet_infra");return a},getStorage:function(){return c("BanzaiStorage")},inform:function(a){Array.isArray(a)?a.forEach(function(a){return m.emit(a)}):m.emit(a)},isOkToSendViaBeacon:function(){return!1},onUnload:function(a){d("Run").onAfterUnload(a)},preferredCompressionMethod:c("once")(function(){return"deflate"}),readyToSend:function(){return c("UserAgent").isBrowser("IE <= 8")||navigator.onLine},send:function(a,b,d,e){var f,g=p.getEndPointUrl(!1);g=c("ZeroRewrites").rewriteURI(new(h||(h=c("URI")))(g));(i||(i=c("ExecutionEnvironment"))).isInWorker&&(g=g.getQualifiedURI());var m=c("ZeroRewrites").getTransportBuilderForURI(g)();m.open(o,g.toString(),!0);c("justknobx")._("2233")&&Object.entries(c("getAsyncHeaders")(g)).forEach(function(a){var b=a[0];a=a[1];m.setRequestHeader(b,a)});e===!0?m.onreadystatechange=function(){if(m.readyState>=4){var a=l.indexOf(m);a>=0&&l.splice(a,1);try{a=m.status}catch(b){a=0}a===200?(b&&b(),c("NetworkStatus").reportSuccess()):(d&&d(a),c("NetworkStatus").reportError())}}:m.onreadystatechange=function(){(j||(j=c("JSScheduler"))).scheduleNormalPriCallback(function(){if(m.readyState>=4){var a=l.indexOf(m);a>=0&&l.splice(a,1);try{a=m.status}catch(b){a=0}a===200?(b&&b(),c("NetworkStatus").reportSuccess(),p.inform((k||(k=c("BanzaiConsts"))).OK)):(d&&d(a),c("NetworkStatus").reportError(),p.inform((k||(k=c("BanzaiConsts"))).ERROR))}})};l.push(m);c("NetworkStatus").isOnline()?m.send(a):f=c("NetworkStatus").onChange(function(b){b=b.online;b&&(m.send(a),f.remove())})},setHooks:function(){},setUnloadHook:function(a){d("Run").onAfterUnload(a._unload)},subscribe:function(a,b){if(Array.isArray(a)){var c=[];a.forEach(function(a){return c.push(m.addListener(a,b))});return{remove:function(){c.forEach(function(a){return a.remove()})}}}else return m.addListener(a,b)},useBeacon:!1,wrapInTimeSlice:function(a,b){c("FBLogger")("banzai").mustfix("wrapInTimeSlice is not implemented");return function(){}}};a=p;g["default"]=a}),98);
/**
 * License: https://www.facebook.com/legal/license/WRsJ32R7YJG/
 */
__d("SnappyCompress",[],(function(a,b,c,d,e,f){"use strict";function g(){return typeof process==="object"&&(typeof process.versions==="object"&&typeof process.versions.node!=="undefined")?!0:!1}function h(a){return a instanceof Uint8Array&&(!g()||!Buffer.isBuffer(a))}function i(a){return a instanceof ArrayBuffer}function j(a){return!g()?!1:Buffer.isBuffer(a)}var k="Argument compressed must be type of ArrayBuffer, Buffer, or Uint8Array";function a(a){if(!h(a)&&!i(a)&&!j(a))throw new TypeError(k);var b=!1,c=!1;h(a)?b=!0:i(a)&&(c=!0,a=new Uint8Array(a));a=new A(a);var d=a.readUncompressedLength();if(d===-1)throw new Error("Invalid Snappy bitstream");if(b){b=new Uint8Array(d);if(!a.uncompressToBuffer(b))throw new Error("Invalid Snappy bitstream")}else if(c){b=new ArrayBuffer(d);c=new Uint8Array(b);if(!a.uncompressToBuffer(c))throw new Error("Invalid Snappy bitstream")}else{b=Buffer.alloc(d);if(!a.uncompressToBuffer(b))throw new Error("Invalid Snappy bitstream")}return b}function b(a){if(!h(a)&&!i(a)&&!j(a))throw new TypeError(k);var b=!1,c=!1;h(a)?b=!0:i(a)&&(c=!0,a=new Uint8Array(a));a=new x(a);var d=a.maxCompressedLength(),e,f,g;b?(e=new Uint8Array(d),g=a.compressToBuffer(e)):c?(e=new ArrayBuffer(d),f=new Uint8Array(e),g=a.compressToBuffer(f)):(e=Buffer.alloc(d),g=a.compressToBuffer(e));if(!e.slice){f=new Uint8Array(Array.prototype.slice.call(e,0,g));if(b)return f;else if(c)return f.buffer;else throw new Error("not implemented")}return e.slice(0,g)}c=16;var l=1<<c,m=14,n=new Array(m+1);function o(a,b){return a*506832829>>>b}function p(a,b){return a[b]+(a[b+1]<<8)+(a[b+2]<<16)+(a[b+3]<<24)}function q(a,b,c){return a[b]===a[c]&&a[b+1]===a[c+1]&&a[b+2]===a[c+2]&&a[b+3]===a[c+3]}function r(a,b,c,d,e){var f;for(f=0;f<e;f++)c[d+f]=a[b+f]}function s(a,b,c,d,e){c<=60?(d[e]=c-1<<2,e+=1):c<256?(d[e]=60<<2,d[e+1]=c-1,e+=2):(d[e]=61<<2,d[e+1]=c-1&255,d[e+2]=c-1>>>8,e+=3);r(a,b,d,e,c);return e+c}function t(a,b,c,d){if(d<12&&c<2048){a[b]=1+(d-4<<2)+(c>>>8<<5);a[b+1]=c&255;return b+2}else{a[b]=2+(d-1<<2);a[b+1]=c&255;a[b+2]=c>>>8;return b+3}}function u(a,b,c,d){while(d>=68)b=t(a,b,c,64),d-=64;d>64&&(b=t(a,b,c,60),d-=60);return t(a,b,c,d)}function v(a,b,c,d,e){var f=1;while(1<<f<=c&&f<=m)f+=1;f-=1;var g=32-f;typeof n[f]==="undefined"&&(n[f]=new Uint16Array(1<<f));f=n[f];var h;for(h=0;h<f.length;h++)f[h]=0;h=b+c;var i=b,j=b,k,l,r,t,v,w=!0,x=15;if(c>=x){c=h-x;b+=1;x=o(p(a,b),g);while(w){t=32;l=b;do{b=l;k=x;v=t>>>5;t+=1;l=b+v;if(b>c){w=!1;break}x=o(p(a,l),g);r=i+f[k];f[k]=b-i}while(!q(a,b,r));if(!w)break;e=s(a,j,b-j,d,e);do{v=b;k=4;while(b+k<h&&a[b+k]===a[r+k])k+=1;b+=k;l=v-r;e=u(d,e,l,k);j=b;if(b>=c){w=!1;break}t=o(p(a,b-1),g);f[t]=b-1-i;v=o(p(a,b),g);r=i+f[v];f[v]=b-i}while(q(a,b,r));if(!w)break;b+=1;x=o(p(a,b),g)}}j<h&&(e=s(a,j,h-j,d,e));return e}function w(a,b,c){do b[c]=a&127,a=a>>>7,a>0&&(b[c]+=128),c+=1;while(a>0);return c}function x(a){this.array=a}x.prototype.maxCompressedLength=function(){var a=this.array.length;return 32+a+Math.floor(a/6)};x.prototype.compressToBuffer=function(a){var b=this.array,c=b.length,d=0,e=0,f;e=w(c,a,e);while(d<c)f=Math.min(c-d,l),e=v(b,d,f,a,e),d+=f;return e};var y=[0,255,65535,16777215,4294967295];function r(a,b,c,d,e){var f;for(f=0;f<e;f++)c[d+f]=a[b+f]}function z(a,b,c,d){var e;for(e=0;e<d;e++)a[b+e]=a[b-c+e]}function A(a){this.array=a,this.pos=0}A.prototype.readUncompressedLength=function(){var a=0,b=0,c,d;while(b<32&&this.pos<this.array.length){c=this.array[this.pos];this.pos+=1;d=c&127;if(d<<b>>>b!==d)return-1;a|=d<<b;if(c<128)return a;b+=7}return-1};A.prototype.uncompressToBuffer=function(a){var b=this.array,c=b.length,d=this.pos,e=0,f,g,h,i;while(d<b.length){f=b[d];d+=1;if((f&3)===0){g=(f>>>2)+1;if(g>60){if(d+3>=c)return!1;h=g-60;g=b[d]+(b[d+1]<<8)+(b[d+2]<<16)+(b[d+3]<<24);g=(g&y[h])+1;d+=h}if(d+g>c)return!1;r(b,d,a,e,g);d+=g;e+=g}else{switch(f&3){case 1:g=(f>>>2&7)+4;i=b[d]+(f>>>5<<8);d+=1;break;case 2:if(d+1>=c)return!1;g=(f>>>2)+1;i=b[d]+(b[d+1]<<8);d+=2;break;case 3:if(d+3>=c)return!1;g=(f>>>2)+1;i=b[d]+(b[d+1]<<8)+(b[d+2]<<16)+(b[d+3]<<24);d+=4;break;default:break}if(i===0||i>e)return!1;z(a,e,i,g);e+=g}}return!0};e.exports.uncompress=a;e.exports.compress=b}),null);
__d("SnappyCompressUtil",["SnappyCompress","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h=a.Uint8Array,i=a.btoa,j=a.TextEncoder;function k(a){if(a==null||i==null)return null;var b=null;try{b=c("SnappyCompress").compress(a)}catch(a){return null}a="";for(var d=0;d<b.length;d++)a+=String.fromCharCode(b[d]);return i(a)}function l(a){if(a==null||i==null)return null;var b=null;try{b=c("SnappyCompress").compress(a)}catch(a){return null}a=Array.from(b,function(a){return String.fromCharCode(a)}).join("");return i(a)}var m=k,n=!1;function b(a){n||(m=c("gkx")("4737")?l:k,n=!0);return m(a)}var o={compressUint8ArrayToSnappy:b,compressStringToSnappy:function(b){if(h==null||i==null)return null;var c=new a.Uint8Array(b.length);for(var d=0;d<b.length;d++){var e=b.charCodeAt(d);if(e>127)return null;c[d]=e}return o.compressUint8ArrayToSnappy(c)},compressStringToSnappyBinary:function(a){if(h==null)return null;var b=null;if(j!=null)b=new j().encode(a);else{b=new h(a.length);for(var d=0;d<a.length;d++){var e=a.charCodeAt(d);if(e>127)return null;b[d]=e}}e=null;try{e=c("SnappyCompress").compress(b)}catch(a){return null}return e}};f.exports=o}),34);
__d("BanzaiCompressionUtils",["FBLogger","Promise","SnappyCompressUtil","once","performanceNow"],(function(a,b,c,d,e,f){"use strict";var g,h,i=b("once")(function(){if(a.CompressionStream==null)return!1;if(a.Response==null)return!1;try{new a.CompressionStream("deflate")}catch(a){return!1}return!0}),j={compressWad:function(a,c){if(a.needs_compression!==!0){delete a.needs_compression;return}if(c==="deflate"){j.compressWad(a,"snappy");return}var d=(g||(g=b("performanceNow")))(),e=JSON.stringify(a.posts),f;switch(c){case"snappy":f=b("SnappyCompressUtil").compressStringToSnappyBinary(e);break;case"snappy_base64":f=b("SnappyCompressUtil").compressStringToSnappy(e);break;default:break}f!=null&&f.length<e.length?(a.posts=f,a.compression=c,a.snappy_ms=Math.ceil((g||(g=b("performanceNow")))()-d),a.snappy_ms<0&&b("FBLogger")("BanzaiCompressionUtils").warn("Expected positive snappy_ms but got %s",a.snappy_ms)):a.compression="";delete a.needs_compression},compressWadAsync:function(c,d){if(d!=="deflate"){j.compressWad(c,"snappy");return(h||(h=b("Promise"))).resolve()}if(!i())return j.compressWadAsync(c,"snappy");var e=(g||(g=b("performanceNow")))(),f=JSON.stringify(c.posts),k=new Response(f).body;if(!k){c.compression="";delete c.needs_compression;return(h||(h=b("Promise"))).resolve()}k=k.pipeThrough(new a.CompressionStream("deflate"));return new Response(k).arrayBuffer().then(function(a){a.byteLength<f.length?(c.posts=new Uint8Array(a),c.compression=d,c.snappy_ms=Math.ceil((g||(g=b("performanceNow")))()-e),c.snappy_ms<0&&b("FBLogger")("BanzaiCompressionUtils").warn("Expected positive snappy_ms but got %s",c.snappy_ms)):c.compression="",delete c.needs_compression})["catch"](function(){c.compression="",delete c.needs_compression})},outOfBandsPosts:function(a){var b=0,c={};for(a of a){var d=a.compression==="snappy"||a.compression==="deflate";if(d){d=new Blob([a.posts],{type:"application/octet-stream"});a.posts=String(b);c["post_"+String(b)]=d;b++}}return c}};e.exports=j}),null);
__d("setTimeoutCometLoggingPriWithFallback",["cr:1268629"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:1268629")}),98);
__d("setTimeoutCometSpeculativeWithFallback",["cr:1268630"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:1268630")}),98);
__d("BanzaiComet",["BanzaiAdapterComet","BanzaiCompressionUtils","BanzaiConsts","BanzaiLazyQueue","BanzaiUtils","CurrentAppID","CurrentUser","ErrorGuard","ExecutionEnvironment","FBLogger","Promise","Run","Visibility","WebSession","clearTimeout","performanceAbsoluteNow","recoverableViolation","setInterval","setTimeout","setTimeoutCometLoggingPriWithFallback","setTimeoutCometSpeculativeWithFallback"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l,m={basic:[],vital:[]},n=[],o={basic:null,vital:null},p={basic:null,vital:null},q=new Map(),r,s=null,t={_expiredBatchMap:function(){var a=(i||(i=c("performanceAbsoluteNow")))();for(var b of q.entries()){var d=b[1];if(d.expiryTime<=a){var e=d.posts[0];e=(e=e.__meta.priority)!=null?e:(j||(j=c("BanzaiConsts"))).BASIC;(e=t._getPostBuffer(e)).push.apply(e,d.posts);q["delete"](b[0])}}q.size>0&&(r=c("setTimeout")(t._expiredBatchMap,(j||(j=c("BanzaiConsts"))).BATCH_TIMEOUT))},_flushBatchMap:function(){c("clearTimeout")(r);r=null;for(var a of q.values()){var b=a.posts[0];b=(b=b.__meta.priority)!=null?b:(j||(j=c("BanzaiConsts"))).BASIC;(b=t._getPostBuffer(b)).push.apply(b,a.posts)}q.clear()},_flushLazyQueue:function(){c("BanzaiLazyQueue").flushQueue().forEach(function(a){return t.post.apply(t,a)})},_gatherWadsAndPostsFromBuffer:function(a,b,d,e,f,g){var h={currentSize:0,keepRetryable:d,overlimit:!1,sendMinimumOnePost:g,wadMap:new Map()};d=f[e].filter(function(d){return c("BanzaiUtils").filterPost(d,a,b,h)});!h.overlimit&&e==="vital"&&(f.basic=f.basic.filter(function(d){return c("BanzaiUtils").filterPost(d,a,b,h)}));return d},_getPostBuffer:function(a){return a==null?m.basic:m[a]||[]},_handleBatchPost:function(a,b,d){if(d==null)return!1;var e=a[2],f=a[0],g=q.get(f);if(g!=null&&g.expiryTime<=e){(b=t._getPostBuffer(b)).push.apply(b,g.posts);q["delete"](f);return!1}if(g!=null&&g.expiryTime>e){g.posts.push(a);return!0}b={expiryTime:e+d,posts:[a]};q.set(f,b);r||(r=c("setTimeout")(t._expiredBatchMap,(j||(j=c("BanzaiConsts"))).BATCH_TIMEOUT));return!0},_handlePostPreflightChecks:function(a,b,d){if(t.adapter.config.disabled===!0)return!0;if(!(k||(k=c("ExecutionEnvironment"))).canUseDOM&&!(k||(k=c("ExecutionEnvironment"))).isInWorker)return!0;if(c("BanzaiAdapterComet").config.disabled===!0)return!0;b=c("BanzaiAdapterComet").config.blacklist;return b!=null&&typeof b.indexOf==="function"&&b.indexOf(a)!==-1?!0:!1},_handleSignalPost:function(a,b,e){if(!e)return!1;var f=a;f.__meta.status=(j||(j=c("BanzaiConsts"))).POST_INFLIGHT;e=[{app_id:d("CurrentAppID").getAppID(),posts:[a],trigger:a[0],user:c("CurrentUser").getPossiblyNonFacebookUserID(),webSessionId:d("WebSession").getId()}];c("BanzaiAdapterComet").send(t._prepForTransit(e),function(){f.__meta.status=(j||(j=c("BanzaiConsts"))).POST_SENT,f.__meta.callback!=null&&f.__meta.callback()},function(d){c("BanzaiUtils").retryPost(a,d,m[b])},!0);return!f.__meta.retry},_initialize:function(){var a=[(j||(j=c("BanzaiConsts"))).VITAL,j.BASIC];if((k||(k=c("ExecutionEnvironment"))).canUseDOM){t.isEnabled("comet_flush_lazy_queue")&&c("setInterval")(function(){t._flushLazyQueue()},(j||(j=c("BanzaiConsts"))).ENSURE_LAZY_QUEUE_FLUSH_TIMEOUT);if(c("Visibility").isSupported()){var b;(b=c("Visibility")).addListener(b.HIDDEN,function(){t._flushLazyQueue(),a.forEach(function(a){t._getPostBuffer(a).length>0&&t._tryToSendViaBeacon(a)}),t._store()});b.addListener(b.VISIBLE,function(){t._flushLazyQueue(),a.forEach(function(a){t._tryToSendViaBeacon(a)}),t._restore()})}else t.adapter.setHooks(t);d("Run").onBeforeUnload(function(){t._flushLazyQueue(),t._flushBatchMap(),t._sendBeacon((j||(j=c("BanzaiConsts"))).VITAL),t._sendBeacon(j.BASIC)},!1);t.adapter.setUnloadHook(t);d("Run").onAfterLoad(function(){t._restore()})}else(k||(k=c("ExecutionEnvironment"))).isInWorker&&self.addEventListener("force-flush-logs",function(){t.flush(),t._flushLazyQueue(),t._flushBatchMap()})},_isShutdown:!1,_prepForTransit:function(a){var b=new FormData();b.append("ts",String(Date.now()));var d=c("BanzaiCompressionUtils").outOfBandsPosts(a);Object.keys(d).forEach(function(a){b.append(a,d[a])});b.append("q",JSON.stringify(a));return b},_prepWadForTransit:function(a){c("BanzaiCompressionUtils").compressWad(a,c("BanzaiAdapterComet").preferredCompressionMethod())},_prepWadForTransitAsync:function(a){return c("BanzaiCompressionUtils").compressWadAsync(a,c("BanzaiAdapterComet").preferredCompressionMethod())},_restore:function(){var a=function(a){var b=a.__meta;b=b.priority===(j||(j=c("BanzaiConsts"))).VITAL?(j||(j=c("BanzaiConsts"))).VITAL:(j||(j=c("BanzaiConsts"))).BASIC;t._getPostBuffer(b).push(a)},b=c("BanzaiAdapterComet").getStorage();(l||(l=c("ErrorGuard"))).applyWithGuard(b.restore,b,[a]);t._schedule((j||(j=c("BanzaiConsts"))).VITAL_WAIT,j.VITAL)},_schedule:function(a,b){if(b==null)return!1;var d=function(){p[b]=null,o[b]=null,t._sendWithCallbacks(b,null,null)},e=(i||(i=c("performanceAbsoluteNow")))()+a;if(o[b]==null||e<o[b]){o[b]=e;p[b]!==null&&c("clearTimeout")(p[b]);b===(j||(j=c("BanzaiConsts"))).VITAL?p.vital=c("setTimeoutCometLoggingPriWithFallback")(d,a):p.basic=c("setTimeoutCometSpeculativeWithFallback")(d,a);return!0}return!1},_sendBeacon:function(a){t._getPostBuffer(a).length>0&&t._tryToSendViaBeacon(a)},_sendWithCallbacks:function(a,d,e){m[a].length>0&&t._schedule(a==="vital"?(j||(j=c("BanzaiConsts"))).VITAL_WAIT:(j||(j=c("BanzaiConsts"))).BASIC_WAIT_COMET,a);if(!c("BanzaiAdapterComet").readyToSend()){e&&e();return}var f=c("BanzaiAdapterComet").getStorage();(l||(l=c("ErrorGuard"))).applyWithGuard(f.flush,f,[t._restore]);c("BanzaiAdapterComet").inform((j||(j=c("BanzaiConsts"))).SEND);var g=[],i=[];m[a]=t._gatherWadsAndPostsFromBuffer(g,i,!0,a,m,!0);if(g.length<=0){c("BanzaiAdapterComet").inform((j||(j=c("BanzaiConsts"))).OK);d&&d();return}g[0].trigger=s;s=null;g.forEach(function(a){return a.send_method="ajax"});n.push.apply(n,i);(h||(h=b("Promise"))).all(g.map(t._prepWadForTransitAsync))["finally"](function(){if(t._isShutdown)return;i.forEach(function(a){a=n.indexOf(a);if(a===-1){c("recoverableViolation")("inflight post not found in inPreparationPosts","comet_infra");return}n.splice(a,1)});c("BanzaiAdapterComet").send(t._prepForTransit(g),function(){i.forEach(function(a){a=a;a.__meta.status=(j||(j=c("BanzaiConsts"))).POST_SENT;typeof a.__meta.callback==="function"&&a.__meta.callback()}),d&&d()},function(b){i.forEach(function(d){c("BanzaiUtils").retryPost(d,b,m[a])}),t._store(),e&&e()})})},_store:function(){var a=c("BanzaiAdapterComet").getStorage();(l||(l=c("ErrorGuard"))).applyWithGuard(a.store,a,[m[(j||(j=c("BanzaiConsts"))).VITAL]]);l.applyWithGuard(a.store,a,[m[j.BASIC]])},_testState:function(){return{postBuffer:m.basic,triggerRoute:s}},_tryToSendViaBeacon:function(b){if(!(navigator&&navigator.sendBeacon))return!1;var d=!0,e=[],f=[];m[b]=t._gatherWadsAndPostsFromBuffer(e,f,!1,b,m,!1);if(e.length<=0)return!1;e.forEach(function(a){return a.send_method="beacon"});e.map(t._prepWadForTransit);e=t._prepForTransit(e);var g=t.adapter.getEndPointUrl(!0);g=a.navigator.sendBeacon(g,e);g||(d=!1,f.forEach(function(a){c("BanzaiUtils").resetPostStatus(a),t._getPostBuffer(b).push(a)}));return d},_unload:function(){t._flushLazyQueue(),t._flushBatchMap(),c("BanzaiAdapterComet").cleanup(),c("BanzaiAdapterComet").inform((j||(j=c("BanzaiConsts"))).SHUTDOWN),t._isShutdown=!0,n.forEach(function(a){var b=a;b=b.__meta.priority;c("BanzaiUtils").retryPost(a,444,t._getPostBuffer(b!=null?b:(j||(j=c("BanzaiConsts"))).VITAL))}),t._sendBeacon(j.VITAL),t._sendBeacon(j.BASIC),t._store()},_validateRouteAndSize:function(a,b){a||c("FBLogger")("banzai").blameToPreviousFrame().blameToPreviousFrame().mustfix("BanzaiComet.post called without specifying a route");return((a=JSON.stringify(b))!=null?a:"").length},BASIC:{delay:(j||(j=c("BanzaiConsts"))).BASIC_WAIT},BASIC_WAIT:j.BASIC_WAIT,ERROR:j.ERROR,EXPIRY:void 0,OK:j.OK,SEND:j.SEND,SHUTDOWN:j.SHUTDOWN,VITAL:{delay:j.VITAL_WAIT},VITAL_WAIT:j.VITAL_WAIT,adapter:c("BanzaiAdapterComet"),canUseNavigatorBeacon:function(){return!!(navigator&&navigator.sendBeacon&&c("BanzaiAdapterComet").isOkToSendViaBeacon())},flush:function(a,b){t.flushHelper((j||(j=c("BanzaiConsts"))).VITAL,a,b),t.flushHelper(j.BASIC,a,b)},flushHelper:function(a,b,d){o[a]=null,p[a]!==null&&(c("clearTimeout")(p[a]),p[a]=null),t._sendWithCallbacks(a,b,d)},isEnabled:function(a){return!!(c("BanzaiAdapterComet").config.gks&&c("BanzaiAdapterComet").config.gks[a])},post:function(a,b,d){var e;t._flushLazyQueue();if(t._handlePostPreflightChecks(a,b,d))return;var f=a.split(":");if((c("BanzaiAdapterComet").config.known_routes||[]).indexOf(f[0])===-1){c("BanzaiAdapterComet").config.should_log_unknown_routes===!0&&c("FBLogger")("banzai").blameToPreviousFrame().mustfix("Attempted to post to invalid Banzai route '"+a+"'. This call site should be cleaned up.");if(c("BanzaiAdapterComet").config.should_drop_unknown_routes===!0)return}f=t._validateRouteAndSize(a,b);d=d||{};b=c("BanzaiUtils").wrapData(a,b,(i||(i=c("performanceAbsoluteNow")))(),d.retry,f);f=b;d.callback&&(f.__meta.callback=d.callback);d.compress!=null&&(f.__meta.compress=d.compress);e=(e=d.delay)!=null?e:(j||(j=c("BanzaiConsts"))).BASIC_WAIT_COMET;var g=e>(j||(j=c("BanzaiConsts"))).VITAL_WAIT?(j||(j=c("BanzaiConsts"))).BASIC:(j||(j=c("BanzaiConsts"))).VITAL;f.__meta.priority=g;if(t._handleSignalPost(b,g,(f=d.signal)!=null?f:!1))return;if(t._handleBatchPost(b,g,d.batch))return;t._getPostBuffer(g).push(b);(t._schedule(e,g)||s==null)&&(s=a)},postsCount:new Map(),subscribe:c("BanzaiAdapterComet").subscribe};t._initialize();e=t;g["default"]=e}),98);
__d("BanzaiWWW",["cr:1642797"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:1642797")}),98);
__d("BigPipeInstance",[],(function(a,b,c,d,e,f){"use strict";var g=null;a={Events:{init:"BigPipe/init",tti:"tti_bigpipe",displayed:"all_pagelets_displayed",loaded:"all_pagelets_loaded"},setCurrentInstance_DO_NOT_USE:function(a){g=a},getCurrentInstance:function(){return g}};e.exports=a}),null);
__d("CometTextTypography",["UserAgent"],(function(a,b,c,d,e,f,g){"use strict";var h={apple:{MozOsxFontSmoothing:"xlh3980",WebkitFontSmoothing:"xvmahel",fontFamily:"x1n0sxbx",$$css:!0},"default":{fontFamily:"x10flsy6",$$css:!0},segoe:{fontFamily:"x1xmvt09",$$css:!0}},i={body1:{fontFamily:h["default"],fontSize:20,fontWeight:"normal",lineHeight:24,offsets:[4,5]},body2:{fontFamily:h["default"],fontSize:17,fontWeight:"normal",lineHeight:20,offsets:[3,5]},body3:{fontFamily:h["default"],fontSize:15,fontWeight:"normal",lineHeight:20,offsets:[4,5]},body4:{fontFamily:h["default"],fontSize:13,fontWeight:"normal",lineHeight:16,offsets:[3,4]},bodyLink1:{fontFamily:h["default"],fontSize:20,fontWeight:"semibold",lineHeight:24,offsets:[4,5]},bodyLink2:{fontFamily:h["default"],fontSize:17,fontWeight:"semibold",lineHeight:20,offsets:[3,5]},bodyLink3:{fontFamily:h["default"],fontSize:15,fontWeight:"semibold",lineHeight:20,offsets:[4,5]},bodyLink4:{fontFamily:h["default"],fontSize:13,fontWeight:"semibold",lineHeight:16,offsets:[3,4]},button1:{fontFamily:h["default"],fontSize:17,fontWeight:"semibold",lineHeight:20,offsets:[3,5]},button2:{fontFamily:h["default"],fontSize:15,fontWeight:"semibold",lineHeight:20,offsets:[4,5]},entityHeaderHeadline1:{fontFamily:h["default"],fontSize:32,fontWeight:"bold",lineHeight:38,offsets:[7,8]},entityHeaderHeadline2:{fontFamily:h["default"],fontSize:28,fontWeight:"bold",lineHeight:32,offsets:[5,7]},entityHeaderMeta1:{defaultColor:"secondary",fontFamily:h["default"],fontSize:15,fontWeight:"bold",lineHeight:20,offsets:[4,5]},entityHeaderMeta2:{defaultColor:"secondary",fontFamily:h["default"],fontSize:15,fontWeight:"bold",lineHeight:20,offsets:[4,5]},headline3:{fontFamily:h["default"],fontSize:17,fontWeight:"medium",lineHeight:20,offsets:[3,5]},headline4:{fontFamily:h["default"],fontSize:15,fontWeight:"medium",lineHeight:20,offsets:[4,5]},headlineDeemphasized3:{fontFamily:h["default"],fontSize:17,fontWeight:"normal",lineHeight:20,offsets:[3,5]},headlineDeemphasized4:{fontFamily:h["default"],fontSize:15,fontWeight:"normal",lineHeight:20,offsets:[4,5]},headlineEmphasized1:{fontFamily:h["default"],fontSize:24,fontWeight:"bold",lineHeight:28,offsets:[5,6]},headlineEmphasized2:{fontFamily:h["default"],fontSize:20,fontWeight:"bold",lineHeight:24,offsets:[4,5]},headlineEmphasized3:{fontFamily:h["default"],fontSize:17,fontWeight:"semibold",lineHeight:20,offsets:[3,4]},headlineEmphasized4:{fontFamily:h["default"],fontSize:15,fontWeight:"semibold",lineHeight:20,offsets:[4,5]},meta1:{defaultColor:"secondary",fontFamily:h["default"],fontSize:13,fontWeight:"semibold",lineHeight:16,offsets:[3,4]},meta2:{defaultColor:"secondary",fontFamily:h["default"],fontSize:13,fontWeight:"semibold",lineHeight:16,offsets:[3,4]},meta3:{defaultColor:"secondary",fontFamily:h["default"],fontSize:13,fontWeight:"normal",lineHeight:16,offsets:[3,4]},meta4:{defaultColor:"secondary",fontFamily:h["default"],fontSize:12,fontWeight:"normal",lineHeight:16,offsets:[3,4]}},j=[["body1",[5,5]],["body2",[4,4]],["body3",[4,4]],["body4",[4,3]],["bodyLink1",[5,5]],["bodyLink2",[4,4]],["bodyLink3",[4,4]],["bodyLink4",[4,3]],["button1",[4,4]],["button2",[4,4]],["entityHeaderHeadline1",[8,7]],["entityHeaderHeadline2",[6,6]],["entityHeaderMeta1",[4,4]],["entityHeaderMeta2",[4,4]],["headline3",[4,4]],["headline4",[4,4]],["headlineDeemphasized3",[4,4]],["headlineDeemphasized4",[4,4]],["headlineEmphasized1",[6,5]],["headlineEmphasized2",[5,5]],["headlineEmphasized3",[4,4]],["headlineEmphasized4",[4,4]],["meta1",[4,3]],["meta2",[4,3]],["meta3",[4,3]],["meta4",[3,3]]],k=[["body1",[5,5]],["body2",[4,4]],["body3",[5,4]],["body4",[4,3]],["bodyLink1",[6,4]],["bodyLink2",[4,3]],["bodyLink3",[5,4]],["bodyLink4",[4,3]],["button1",[4,3]],["button2",[5,4]],["entityHeaderHeadline1",[8,7]],["entityHeaderHeadline2",[7,5]],["entityHeaderMeta1",[5,4]],["entityHeaderMeta2",[5,4]],["headline3",[5,3]],["headline4",[5,4]],["headlineDeemphasized3",[5,3]],["headlineDeemphasized4",[5,4]],["headlineEmphasized1",[6,5]],["headlineEmphasized2",[6,4]],["headlineEmphasized3",[4,3]],["headlineEmphasized4",[5,4]],["meta1",[4,3]],["meta2",[4,3]],["meta3",[4,3]],["meta4",[4,3]]],l=[["body1",[6,4,1]],["body2",[5,3,1]],["body3",[5,4]],["body4",[4,3,1]],["bodyLink1",[6,4,1]],["bodyLink2",[5,3,1]],["bodyLink3",[5,4]],["bodyLink4",[4,3,1]],["button1",[5,3,1]],["button2",[5,4]],["entityHeaderHeadline1",[10,6,2]],["entityHeaderHeadline2",[8,5,3]],["entityHeaderMeta1",[5,4,1]],["entityHeaderMeta2",[5,4,1]],["headline3",[5,3,1]],["headline4",[5,4]],["headlineDeemphasized3",[5,3,1]],["headlineDeemphasized4",[5,4]],["headlineEmphasized1",[7,4,2]],["headlineEmphasized2",[6,4,2]],["headlineEmphasized3",[5,3,1]],["headlineEmphasized4",[5,4]],["meta1",[4,3,1]],["meta2",[4,3,1]],["meta3",[4,3,1]],["meta4",[4,3]]];function m(){if(c("UserAgent").isPlatform("Windows >= 6"))return{fontFamily:h.segoe,offsets:l};return c("UserAgent").isPlatform("Mac OS X >= 10.11")&&!c("UserAgent").isBrowser("Firefox < 55")||c("UserAgent").isPlatform("iOS >= 9")?{fontFamily:h.apple,offsets:c("UserAgent").isEngine("Gecko")?k:j}:null}function a(){var a=babelHelpers["extends"]({},i),b=m();if(b!=null){var c=b.fontFamily;b=b.offsets;b=new Map(b);b.forEach(function(b,d){a[d]=babelHelpers["extends"]({},a[d],{fontFamily:c,offsets:b})})}return a}b=a();g["default"]=b}),98);
__d("CometLinkUtils.react",["CometTextTypography"],(function(a,b,c,d,e,f,g){"use strict";var h={blueLink:{color:"x1fey0fg",$$css:!0},disabled:{color:"x1dntmbh",$$css:!0},highlight:{color:"x1qq9wsj",$$css:!0},inheritColor:{color:"x1heor9g",$$css:!0},linkOnMedia:{color:"x9kptjx",$$css:!0},negative:{color:"x1a1m0xk",$$css:!0},positive:{color:"x6u5lvz",$$css:!0},primary:{color:"xzsf02u",$$css:!0},secondary:{color:"xi81zsa",$$css:!0},tertiary:{color:"x12scifz",$$css:!0},white:{color:"x14ctfv",$$css:!0}},i={bold:{fontWeight:"x1xlr1w8",$$css:!0},inheritFontWeight:{fontWeight:"x1pd3egz",$$css:!0},medium:{fontWeight:"xk50ysn",$$css:!0},normal:{fontWeight:"xo1l8bm",$$css:!0},semibold:{fontWeight:"x1s688f",$$css:!0}};function a(a,b,c){a=a!=null?a:b!=null?l(b,c):"inherit";return a!=="inherit"&&h[a]}function b(a,b){b=m(b);a=n(a);return b||a?h.inheritColor:h.blueLink}function d(a,b,c){a=a!=null?a:b!=null?j(b,c):"inherit";return a!=="inherit"&&i[a]}function e(a){a=m(a);return a?i.inheritFontWeight:i.semibold}function j(a,b){if(!b){b=k(a);return c("CometTextTypography")[b].fontWeight}return"inherit"}function k(a){switch(a){case"headline3":return"headlineEmphasized3";case"headline4":return"headlineEmphasized4";case"body1":return"bodyLink1";case"body2":return"bodyLink2";case"body3":return"bodyLink3";case"body4":return"bodyLink4";default:return a}}function l(a,b){switch(a){case"headline3":case"headline4":case"body1":case"body2":case"body3":case"body4":return b?"blueLink":"primary";case"meta1":case"meta2":case"meta3":case"meta4":return b?"blueLink":"inherit";default:return"inherit"}}function m(a){return a!=null&&(a==="headlineDeemphasized3"||a==="headlineDeemphasized4"||a==="headlineEmphasized1"||a==="headlineEmphasized2"||a==="headlineEmphasized3"||a==="headlineEmphasized4"||a==="headline3"||a==="headline4"||a==="entityHeaderHeadline1"||a==="entityHeaderHeadline2")}function n(a){return a!=null&&(a==="negative"||a==="positive")}g.getLinkColorStyle=a;g.getLinkColorStyle__new=b;g.getLinkWeightStyle=d;g.getLinkWeightStyle__new=e}),98);
__d("CometLinkNewImpl.react",["BaseLink.react","CometDangerouslySuppressInteractiveElementsContext","CometLinkUtils.react","FDSTextContext","isCometRouterUrl","react","react-compiler-runtime","react-strict-dom"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useContext,k={disabled:{color:"x1dntmbh",textDecorationLine:"xkrqix3 x1ubmc1d",$$css:!0},root:{color:"x1heor9g",textDecorationLine:"xkrqix3 x1sur9pj",$$css:!0}},l={block:{display:"x1lliihq",$$css:!0},"inline-block":{display:"x1rg5ohu",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(36);if(b[0]!==a){var e=a.color_DEPRECATED,f=a.display_DEPRECATED,g=a.ref;a.weight_DEPRECATED;var h=a.xstyle_DEPRECATED,m=a.disabled,n=a.href,o=a.role,p=a.target,q=babelHelpers.objectWithoutPropertiesLoose(a,["color_DEPRECATED","display_DEPRECATED","ref","weight_DEPRECATED","xstyle_DEPRECATED","disabled","href","role","target"]);e=e;f=f;g=g;h=h;m=m;n=n;o=o;p=p;q=q;b[0]=a;b[1]=e;b[2]=n;b[3]=q;b[4]=g;b[5]=o;b[6]=f;b[7]=m;b[8]=p;b[9]=h}else e=b[1],n=b[2],q=b[3],g=b[4],o=b[5],f=b[6],m=b[7],p=b[8],h=b[9];a=f===void 0?"inline":f;f=m===void 0?!1:m;m=d("FDSTextContext").useFDSTextContext();var r=j(c("CometDangerouslySuppressInteractiveElementsContext")),s=p==="_blank"||p==null&&n!=null&&n!=="#"&&!c("isCometRouterUrl")(n);o=o==null&&(n==null||n==="#")?"button":o;var t=m==null?void 0:m.type,u;b[10]!==e||b[11]!==s||b[12]!==t?(u=d("CometLinkUtils.react").getLinkColorStyle(e,t,s),b[10]=e,b[11]=s,b[12]=t,b[13]=u):u=b[13];e=u;t=m==null?void 0:m.type;b[14]!==t?(u=d("CometLinkUtils.react").getLinkWeightStyle__new(t),b[14]=t,b[15]=u):u=b[15];m=u;t=f&&k.disabled;u=a!=="inline"&&l[a];b[16]!==e||b[17]!==m||b[18]!==t||b[19]!==u?(a=[t,e,m,u],b[16]=e,b[17]=m,b[18]=t,b[19]=u,b[20]=a):a=b[20];e=a;if(r){b[21]!==q.children||b[22]!==g||b[23]!==e?(m=i.jsx(d("react-strict-dom").html.span,{ref:g,style:e,children:q.children}),b[21]=q.children,b[22]=g,b[23]=e,b[24]=m):m=b[24];return m}t=s?"_blank":p;b[25]!==e||b[26]!==h?(u=[k.root].concat(e,[h]),b[25]=e,b[26]=h,b[27]=u):u=b[27];b[28]!==f||b[29]!==n||b[30]!==o||b[31]!==q||b[32]!==g||b[33]!==t||b[34]!==u?(a=i.jsx(c("BaseLink.react"),babelHelpers["extends"]({},q,{disabled:f,display:"inline",href:n,ref:g,role:o,target:t,xstyle:u})),b[28]=f,b[29]=n,b[30]=o,b[31]=q,b[32]=g,b[33]=t,b[34]=u,b[35]=a):a=b[35];return a}g["default"]=a}),98);
__d("CometLinkTrackingUtils.facebook",["ConstUriUtils","isFacebookURI"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,e){if(a!=null&&a!=="#"&&(e.length||b.length)){var f=d("ConstUriUtils").getUri(a);if(f!=null){if(!c("isFacebookURI")(f))return a;e.length&&(f=f.addQueryParam("__cft__",e));b.length&&f!=null&&(f=f.addQueryParam("__tn__",b.join("")));return f!=null?f.toString():a}}return a}g.decorateHrefWithTrackingInfo=a}),98);
__d("CometTailLoadLogger",["QuickLogActionType","QuickPerformanceLogger","SiteData","VisibilityListener","gkx","performanceNavigationStart","qpl","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h,i=3e3,j=c("gkx")("1326"),k=0,l=c("performanceNavigationStart")(),m=new Map(),n=new Map(),o=new Map(),p=new Set(),q=c("uuidv4")(),r=new Set();function a(){m.clear(),n.clear(),o.clear(),p.clear()}function s(a,b,d){k++;j&&(h||(h=c("QuickPerformanceLogger"))).markerStart(c("qpl")._(588713587,"4750"),k,d.current+l,{trackedForLoss:!0});d=a+"_"+b;o.set(d,k)}function b(a,b){n.set(a,b.current)}function e(a,b,c){a=a+"_"+b;m.set(a,c)}function t(a,b,e,f,g){var k=a+"_"+b,t=0,u=n.get(a)||e,v=m.get(k);v!=null&&(t=v,m["delete"](k));v=o.get(k);v==null&&(s(a,b,{current:u}),v=o.get(k));u=d("VisibilityListener").getHiddenTime(u+c("performanceNavigationStart")(),e+c("performanceNavigationStart")());j&&((h||(h=c("QuickPerformanceLogger"))).markerAnnotate(c("qpl")._(588713587,"4750"),{"int":{client_revision:c("SiteData").client_revision,hidden:Number(u!=null&&u>0),interruptedConsumption:t,position:b,windowHeight:Math.floor(window.innerHeight/100)},string:{client_push_phase:c("SiteData").push_phase,pageletName:a,sessionId:q,tracePolicy:g}},{instanceKey:v}),h.markerEnd(c("qpl")._(588713587,"4750"),f,v,e+l));if(f!==2||f===706&&t>=i){u=Object.keys(c("QuickLogActionType")).find(function(a){return c("QuickLogActionType")[a]===f})||"UNKNOWN";r.add({position:b,scrollingFNLType:u,tracePolicy:g})}p.add(k);o["delete"](k)}function f(a,b,c,d,e){var f=a+"_"+b,g=!p.has(f);g&&t(a,b,c,d,e);p["delete"](f)}function u(){return r}g.alreadyFinishedMarkerKeys=p;g.reset=a;g.startTailLoadTracing=s;g.setTailLoadStartTime=b;g.reportInterruptedConsumption=e;g.endTailloadTracing=t;g.logMarkerEnd=f;g.dump=u}),98);
__d("CometTextLangContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext(void 0);g["default"]=b}),98);
__d("EventListenerWWW",["cr:1353359"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:1353359")}),98);
__d("filterMap",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){var c=new Map();for(a of a){var d=a[0],e=a[1];b(e,d)&&c.set(d,e)}return c}f["default"]=a}),66);
__d("HeroBootloadPerfStore",["BootloaderEvents","InteractionTracingMetrics","ResourceTimingStore","filterMap","gkx","performanceNavigationStart"],(function(a,b,c,d,e,f,g){"use strict";var h=["encodedBodySize","transferSize","totalCount","cacheCount"],i=["t1","t2","t3"],j=["untiered"],k=["js","css"],l=new Map();d("BootloaderEvents").onBootload(function(a){a.components.forEach(function(b){l.set(b,a)})});function m(a){try{return new URL(a).pathname}catch(a){return"[invalid url]"}}function n(a,b){if(b==null){a.missingData++;return}a.urls&&a.urls.add(b.name);a.encodedBodySize+=b.encodedBodySize;a.decodedBodySize+=b.decodedBodySize;a.transferSize+=b.transferSize;a.totalCount+=1;b.transferSize===0&&(a.cacheCount+=1)}function o(a){return a.src.indexOf("data")===0}function p(a,b,e){var f=new Map();function g(a){var b;b=(b=f.get(a))!=null?b:{cacheCount:0,decodedBodySize:0,encodedBodySize:0,missingData:0,totalCount:0,transferSize:0,urls:c("gkx")("23413")?new Set():null};f.set(a,b);return b}function h(a,c){var f=d("ResourceTimingStore").getEntryForURL(c.src);n(g("used_"+c.type),f);n(g("used_"+a),f);n(g("used_"+a+"_"+c.type),f);f!=null&&f.responseEnd>=b&&f.responseEnd<=e&&(n(g("downloaded_"+a),f),n(g("downloaded_"+c.type),f),n(g("downloaded_"+a+"_"+c.type),f))}function i(a,b){a.decodedBodySize+=b.length,a.totalCount+=1}function j(a,b){i(g("inline_"+a),b.src),i(g("inline_"+b.type),b.src),i(g("inline_"+a+"_"+b.type),b.src)}var k=function(a){var b=a[0];a=a[1];a.forEach(function(a,c){if(!(a.type==="css"||a.type==="js"))return;o(a)?j(b,a):h(b,a)})};for(a of a)k(a);return f}function q(a,b,c){var d;d=(d=a.get(b))!=null?d:new Map();a.set(b,d);for(a of c)(a.type==="css"||a.type==="js")&&d.set(a.src,a)}function r(a,b,d,e){d=p(d,a,b);a={};for(b of e)for(e of k){var f="downloaded_"+b+"_"+e;for(var g of h){var i;a[f+"_"+g]=(i=(i=d.get(f))==null?void 0:i[g])!=null?i:0}if(c("gkx")("23413")){a[f+"_urls"]=Array.from((g=(i=d.get(f))==null?void 0:i.urls)!=null?g:[]).map(m)}f="inline_"+b+"_"+e;a[f+"_decodedBodySize"]=(g=(i=d.get(f))==null?void 0:i.decodedBodySize)!=null?g:0}return a}function s(a,b,d,e){var f;if(((f=a.annotations["int"])==null?void 0:f.hasExtraResourceMetadata)!==1)return;var g=p(b,a.start,(f=a.completed)!=null?f:a.start);[].concat(k,e).forEach(function(b){var e=g.get("downloaded_"+b);for(var f of h){var i;c("InteractionTracingMetrics").addMetadata(a.traceId,d+"_downloaded_"+b+"_"+f,(i=e==null?void 0:e[f])!=null?i:0)}i=g.get("inline_"+b);c("InteractionTracingMetrics").addMetadata(a.traceId,d+"_inline_"+b+"_decodedBodySize",(e=i==null?void 0:i.decodedBodySize)!=null?e:0)})}function t(a){var b=new Map();a.heroRelay.forEach(function(e){var f=e.pageletStack;e=e.queries;for(e of e){var g=e.hasteResponseLogEvents;for(g of g){var h,i=g.startTime-c("performanceNavigationStart")(),k=g.logTime-c("performanceNavigationStart")();if(k<a.start||i>((h=a.completed)!=null?h:Infinity))return;h=d("BootloaderEvents").flattenResourceMapSet(g.rsrcs);var l=new Map();q(b,"untiered",h.values());q(l,"untiered",h.values());h=r(i,k,l,j);c("InteractionTracingMetrics").addSubspan(a.traceId,"Relay3D: "+e.name,"HeroTracing",Math.max(a.start,i),Math.min((l=a.completed)!=null?l:Infinity,k),babelHelpers["extends"]({},{pagelet:f[f.length-1],pageletStack:f,spanType:"Relay3D"},{},h,{full_duration:(k-i)/1e3,is_preloaded:e.isPreloaded===!0}))}}});s(a,b,"relay3d",j)}function u(a){var b;b=Array.from(new Set((b=[]).concat.apply(b,a.heroBootloads.map(function(a){return a.moduleIDs}))));var e=new Map(),f=new Map();b.forEach(function(a){var b=l.get(a);b&&f.set(a,b)});for(b of f.values())q(e,"t1",d("BootloaderEvents").flattenResourceMapSet(b.tierOne).values()),q(e,"t2",d("BootloaderEvents").flattenResourceMapSet(b.tierTwo).values()),q(e,"t3",d("BootloaderEvents").flattenResourceMapSet(b.tierThree).values());s(a,e,"bootload",i);a.heroBootloads.forEach(function(b){var c=b.moduleIDs;b=b.pageletStack;c=new Set(c.map(function(a){return l.get(a)}).filter(Boolean));v(a,c,b,"Bootload")});if(c("gkx")("23414")){var g=a.heroBootloads.map(function(a){a=a.moduleIDs;return a}).flat();b=c("filterMap")(l,function(a,b){return!g.includes(b)});v(a,b,[],"BootloadOutside")}}function v(a,b,e,f){b.forEach(function(b){if(b.callbackEnd-c("performanceNavigationStart")()<a.start)return;var g=new Map();q(g,"t1",d("BootloaderEvents").flattenResourceMapSet(b.tierOne).values());q(g,"t2",d("BootloaderEvents").flattenResourceMapSet(b.tierTwo).values());q(g,"t3",d("BootloaderEvents").flattenResourceMapSet(b.tierThree).values());g=r(b.startTime-c("performanceNavigationStart")(),b.callbackEnd-c("performanceNavigationStart")(),g,i);var h=f+": "+b.components.join(),j=f==="Bootload"?"HeroTracing":"BootloadOutside";c("InteractionTracingMetrics").addSubspan(a.traceId,h,j,Math.max(a.start,b.startTime-c("performanceNavigationStart")()),Math.min((h=a.completed)!=null?h:Infinity,b.callbackStart-c("performanceNavigationStart")()),babelHelpers["extends"]({},{bootloadComponents:b.components,bootloadRef:b.ref,pagelet:e[e.length-1],pageletStack:e,spanType:f},{},g))})}function a(a){d("ResourceTimingStore").init(),u(a),t(a)}g.addStaticResourcesStats=a}),98);
__d("WebLoomBanzaiTransport",["Banzai"],(function(a,b,c,d,e,f,g){"use strict";a={post:function(a,b){c("Banzai").post("loom_trace",a,{callback:b.onComplete,delay:b.isHighPri?c("Banzai").VITAL_WAIT:c("Banzai").BASIC_WAIT})},flush:function(a,b){c("Banzai").flush(a,b)}};b=a;g["default"]=b}),98);
__d("WebLoomStaticResourceFileTypes",[],(function(a,b,c,d,e,f){"use strict";var g={js:"js",css:"css",wasm:"wasm",woff:"woff",woff2:"woff2",otf:"otf",eot:"eot",ttf:"ttf"};function a(a){return Object.prototype.hasOwnProperty.call(g,a)}f.WebLoomStaticResourceFileTypes=g;f.isStaticResourceFileType=a}),66);
__d("sanitizeURIStringForLoom",["WebLoomStaticResourceFileTypes"],(function(a,b,c,d,e,f,g){"use strict";var h=new Map(),i=0,j=/(\d{4,})/gm,k=/([a-f0-9]{8,})/gm;function l(a){a=a.replace(j,"{N}");return a.replace(k,"{N}")}function m(a){a=a.getPath();var b=a.lastIndexOf(".");return b===-1?"":a.substring(b+1)}function n(){return i++}function o(a){if(a.getProtocol()!=="http"&&a.getProtocol()!=="https")return!1;var b=Number(a.getPort());if(!!b&&b!==80&&b!==443)return!1;return a.isSubdomainOfDomain("fbcdn.net")?!0:!1}function p(a,b){b=new b.URI(a);a=m(b);b.setQueryString("");b.setFragment("");if(a===""||a==="php"||a==="ico"){var c=l(b.getPath());c!==b.getPath()&&b.setPath(c+"/sanitized-"+n())}else d("WebLoomStaticResourceFileTypes").isStaticResourceFileType(a)||b.setPath("/sanitized"+(o(b)?"-cdn":"")+"-"+n()+"."+a);return b.toString()}function a(a,b){if(b.isBrowser("IE"))return"";h.has(a)||h.set(a,p(a,b));return h.get(a)||""}g["default"]=a}),98);
__d("InteractionTracingLoomProvider",["interaction-tracing-metrics","sanitizeURIStringForLoom"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return a.substr(0,7)==="http://"||a.substr(0,8)==="https://"?!0:!1}var i=function(){var b=a.prototype;b.$4=function(a){return this.$3.sanitizeURIs&&h(a)?c("sanitizeURIStringForLoom")(a,this.$2):a};b.$5=function(a,b){var c=this.$1;if(!c)return;c.buffer.addEvent(a,b+this.$3.appStart)};b.$6=function(a,b){var c=this,d=a.subSpans,e=function(e){d[e].forEach(function(d,f){f={blockName:e+"_"+f,blockType:d.type,execUnitName:e,traceId:b,traceType:a.type};c.$5(babelHelpers["extends"]({type:"INTERACTION_TRACE_START"},f),d.start);c.$5(babelHelpers["extends"]({blockAnnotations:d.data,type:"INTERACTION_TRACE_END"},f),d.end)})};for(var f in d)e(f)};b.$7=function(a,b){var c=a.payloadResources;for(var d in c){var e=c[d],f=this.$4(d),g={blockName:f,blockType:"PayloadResources",execUnitName:e.initiator,traceId:b,traceType:a.type};this.$5(babelHelpers["extends"]({type:"INTERACTION_TRACE_START"},g),e.start);this.$5({blockName:f,execUnitName:e.initiator,pointAnnotations:{},pointName:"requestStart",traceId:b,type:"INTERACTION_TRACE_POINT"},e.requestStart);this.$5(babelHelpers["extends"]({blockAnnotations:{refs:e.refs.join(","),transferSize:e.transferSize,url:this.$4(e.url)},type:"INTERACTION_TRACE_END"},g),e.end)}};b.$8=function(a,b){var c=a.imagePreloaderTimings;for(var d in c){var e=c[d],f=this.$4(d),g={blockName:f,blockType:"ImagePreloaders",execUnitName:e.playloadName,traceId:b,traceType:a.type};this.$5(babelHelpers["extends"]({type:"INTERACTION_TRACE_START"},g),e.start);this.$5({blockName:f,execUnitName:e.playloadName,pointAnnotations:{},pointName:"requestStart",traceId:b,type:"INTERACTION_TRACE_POINT"},e.requestStart);this.$5(babelHelpers["extends"]({blockAnnotations:{url:f},type:"INTERACTION_TRACE_END"},g),e.end)}};b.$9=function(a,b){var c=this,d=a.payloadTimings,e=function(e){var f=d[e];if(f.start==null||f.end==null)return{v:void 0};var g={blockName:e,blockType:f.payloadType,execUnitName:e,traceId:b,traceType:a.type};c.$5(babelHelpers["extends"]({type:"INTERACTION_TRACE_START"},g),f.start);Object.keys(f.points).forEach(function(a){c.$5({blockName:e,execUnitName:e,pointAnnotations:{},pointName:a,traceId:b,type:"INTERACTION_TRACE_POINT"},f.points[a])});var h=babelHelpers["extends"]({},f.data);for(var i in f.pkgStat){var j=f.pkgStat[i];for(var k in j)h[i+"_"+k]=j[k]}c.$5(babelHelpers["extends"]({blockAnnotations:h,type:"INTERACTION_TRACE_END"},g),f.end)};for(var f in d){var g=e(f);if(typeof g==="object")return g.v}};b.$10=function(a,b){var c=a.markerPoints;for(var d in c){var e=c[d],f=e.timestamp,g=e.type;g={blockName:d,blockType:g,execUnitName:d,traceId:b,traceType:a.type};this.$5(babelHelpers["extends"]({type:"INTERACTION_TRACE_START"},g),f);this.$5(babelHelpers["extends"]({blockAnnotations:e.data||{},type:"INTERACTION_TRACE_END"},g),f)}};b.$11=function(a,b){var c=a.requireDeferreds;for(var d in c){var e=c[d],f={blockName:d,blockType:"RequireDeferreds",execUnitName:d,traceId:b,traceType:a.type},g=e.end;if(g==null)continue;this.$5(babelHelpers["extends"]({type:"INTERACTION_TRACE_START"},f),e.start);this.$5(babelHelpers["extends"]({blockAnnotations:{alreadyRequired:Boolean(e.alreadyRequired)},type:"INTERACTION_TRACE_END"},f),g)}};b.$12=function(a,b,c,d){for(var e=0;e<d.length;e++){var f={blockName:a+"_"+e,blockType:a,execUnitName:a+"_"+e,traceId:c,traceType:b.type};this.$5(babelHelpers["extends"]({type:"INTERACTION_TRACE_START"},f),d[e].start);this.$5(babelHelpers["extends"]({blockAnnotations:{},type:"INTERACTION_TRACE_END"},f),d[e].end)}};b.$13=function(a){var b=this;a.vcStateLog!=null&&a.vcStateLog.forEach(function(c,d){var e=c[0];c=c[1];d={blockName:d,blockType:"VCState",execUnitName:"VCState",traceId:a.traceId,traceType:a.type};b.$5(babelHelpers["extends"]({type:"INTERACTION_TRACE_START"},d),e);b.$5(babelHelpers["extends"]({blockAnnotations:{},type:"INTERACTION_TRACE_END"},d),c)});a.vcMutationLog!=null&&a.vcMutationLog.forEach(function(c){var d;d=(d=c.finalState)!=null?d:"";d={blockName:c.mutationType+"_"+d+"_"+c.veid,blockType:"VCMutation",execUnitName:(d=c.finalState)!=null?d:"mutation",traceId:a.traceId,traceType:a.type};b.$5(babelHelpers["extends"]({type:"INTERACTION_TRACE_START"},d),c.mutationTime);b.$5(babelHelpers["extends"]({blockAnnotations:{data:JSON.stringify(c)},type:"INTERACTION_TRACE_END"},d),c.paintTime)})};b.$14=function(a){var b=this;a.factoryTimings.forEach(function(c){var d={blockName:c.name,blockType:"Factories",execUnitName:"Factories",traceId:a.traceId,traceType:a.type};b.$5(babelHelpers["extends"]({type:"INTERACTION_TRACE_START"},d),c.start);b.$5(babelHelpers["extends"]({blockAnnotations:{},type:"INTERACTION_TRACE_END"},d),c.end)})};function a(a,b,c){this.$1=a,this.$2=b,this.$3=c}b.loomTraceWillEnd=function(){var a=this,b=this.$1;if(!b)return;var c=d("interaction-tracing-metrics").InteractionTracingMetricsCore.dump(),e=[];if(b.triggerInfo.type==="INTERACTION"&&b.triggerInfo.interaction_id!=null)e.push(b.triggerInfo.interaction_id);else for(var f in c){var g=c[f];g=g.completed!=null?this.$3.appStart+g.completed:null;(g==null||g>b.startTime)&&e.push(f)}e.forEach(function(b){var d=c[b];a.$6(d,b);a.$10(d,b);a.$11(d,b);a.$7(d,b);a.$8(d,b);a.$9(d,b);a.$12("hidden",d,b,d.hiddenTimings);a.$12("offline",d,b,d.offlineTimings);a.$14(d);a.$13(d)})};return a}();a={getInstance:function(a,b,c){return new i(a,b,c)},isSupported:function(){return!0},loomProviderId:"InteractionTracing"};g["default"]=a}),98);
__d("QPLLoomProvider",["QPLEvent"],(function(a,b,c,d,e,f,g){"use strict";var h,i=function(){function a(a,b){var c=this;this.$2={};this.$1=b.QuickPerformanceLogger.addListener({onMarkerStart:function(b,e,f){b=(h||(h=d("QPLEvent"))).getMarkerId(b);f>=a.startTime&&a.buffer.addEvent({type:"QPL_START",markerId:b,instanceKey:e},f);c.$2[b]||(c.$2[b]={});c.$2[b][e]=f},onMarkerEnd:function(b,c,e,f){c=(h||(h=d("QPLEvent"))).getMarkerId(c);f>=a.startTime&&a.buffer.addEvent({type:"QPL_END",action:b,markerId:c,instanceKey:e},f)},onMarkerPoint:function(b,c,e,f,g){b=(h||(h=d("QPLEvent"))).getMarkerId(b);if(g>=a.startTime){a.buffer.addEvent({type:"QPL_POINT",markerId:b,instanceKey:c,name:e,data:f==null?void 0:(b=f.string)==null?void 0:b.__key},g)}},onAnnotation:function(b,e,f,g){b=(h||(h=d("QPLEvent"))).getMarkerId(b);var i=c.$2[b];i=i==null?void 0:i[e];i!=null&&i>=a.startTime&&a.buffer.addEvent({type:"QPL_ANNOTATION",markerId:b,instanceKey:e,annotationKey:f,annotationValue:g},i)}})}var b=a.prototype;b.loomTraceWillEnd=function(){this.$1.dispose()};return a}();a={loomProviderId:"QPL",isSupported:function(){return!0},getInstance:function(a,b){return new i(a,b)}};g["default"]=a}),98);
__d("clamp",[],(function(a,b,c,d,e,f){function a(a,b,c){if(a<b)return b;return a>c?c:a}f["default"]=a}),66);
__d("ResourceTimingLoomProvider",["clamp","performanceNow","sanitizeURIStringForLoom"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a,b){b=b.substring(b.lastIndexOf(".")+1);if(b=="js"||b=="css")return b;else if(a=="img"||b=="png"||b=="jpg"||b=="ico")return"img";else return a}var j=function(){a.isSupported=function(){return window.performance&&window.performance.getEntriesByType&&window.performance.timing&&window.performance.timing.navigationStart};function a(a,b,c){this.$1=a,this.$2=b,this.$3=c}var b=a.prototype;b.$4=function(a){var b=this,d=a.entry,e=a.resourceName,f=a.resourceId,g=a.resourceType,h=a.startTime,i=a.endTime,j=this.$1;if(j!=null){a=d;j.buffer.addEvent({type:"RESOURCE_TIMING_START",resourceType:g,resourceId:f,resourceName:e,encodedSize:a!=null&&typeof a.encodedBodySize==="number"?a.encodedBodySize:0,decodedSize:a!=null&&typeof a.decodedBodySize==="number"?a.decodedBodySize:0,transferSize:a!=null&&typeof a.transferSize==="number"?a.transferSize:0,responseStatus:a!=null&&typeof a.responseStatus==="number"?a.responseStatus:-1},h);e=function(a,d){d=d+b.$3.appStart;j.buffer.addEvent({type:"RESOURCE_TIMING_POINT",resourceType:g,resourceId:f,pointName:a},c("clamp")(d,h,i))};e("requestStart",d.requestStart);e("responseStart",d.responseStart);j.buffer.addEvent({type:"RESOURCE_TIMING_END",resourceType:g,resourceId:f},i)}};b.$5=function(a){if(this.$3.sanitizeURIs)return c("sanitizeURIStringForLoom")(a,this.$2);var b=a.indexOf("?");return b==-1?a:a.substring(0,b)};b.loomTraceWillEnd=function(){var a=this,b=this.$1;if(b!=null){var d=b.startTime,e=(h||(h=c("performanceNow")))()+this.$3.appStart;window.performance.getEntriesByType("resource").filter(function(b){return b.startTime<b.responseEnd&&b.startTime+a.$3.appStart>=d&&b.responseEnd+a.$3.appStart<=e}).forEach(function(f,c){var d=f.startTime+a.$3.appStart,e=f.responseEnd+a.$3.appStart,g=a.$5(f.name),b=i(f.initiatorType,g);a.$4({entry:f,resourceName:g,resourceId:c,resourceType:b,startTime:d,endTime:e})});b=window.performance.getEntriesByType("navigation")[0];b=typeof PerformanceNavigationTiming!=="undefined"&&b instanceof PerformanceNavigationTiming?b:null;if(b!=null&&(b.responseEnd===0||b.responseEnd+this.$3.appStart>=d)){var f="document",g=-1,j=d>this.$3.appStart?d:this.$3.appStart,k=b.responseEnd===0?e:b.responseEnd+this.$3.appStart;this.$4({entry:b,resourceName:this.$5(location.href),resourceId:g,resourceType:f,startTime:j,endTime:k})}}this.$1=null};return a}();a={loomProviderId:"ResourceTiming",isSupported:function(){return j.isSupported()},getInstance:function(a,b,c){return new j(a,b,c)}};g["default"]=a}),98);
__d("VisualCompletionLoomProvider",["vc-tracker"],(function(a,b,c,d,e,f,g){"use strict";var h=function(){a.isSupported=function(){return!0};function a(a,b,d){var e=this;this.$3=function(a){var b=e.$1;if(b!=null&&a!=null&&a.startTime+e.$2.appStart>=b.startTime){var c=new Map();a.elements.forEach(function(a){var b;c.set(a.timestamp,((b=c.get(a.timestamp))!=null?b:0)+a.pixels)});Array.from(c.entries()).sort(function(a,b){return a[0]-b[0]}).reduce(function(c,d){var f=d[0];d=d[1];c=c+d;b.buffer.addEvent({progress:c/a.paintedPixels,type:"VISUAL_COMPLETION_PROGRESS"},f+e.$2.appStart);return c},0);e.$4(b,a,a.elements.filter(function(a){return a.parent==null}),0)}};this.$1=a;c("vc-tracker").VisualCompletionTraceObserver.subscribe(this.$3);this.$2=d}var b=a.prototype;b.$4=function(a,b,c,d,e){var f=this;e===void 0&&(e=null);c.slice().sort(function(a,b){return a.timestamp-b.timestamp}).forEach(function(c){var g=c.rectangle,h=c.type==="component"||e==null?c.timestamp:Math.max(c.timestamp,e.timestamp);a.buffer.addEvent({depth:d,elementType:c.type,height:Math.floor(g.bottom-g.top),mutationType:c.mutationType,lateMutationType:c.hadLateMutationUnexpected?"unexpected":c.hadLateMutationExpected?"expected":void 0,type:"VISUAL_COMPLETION_RECT",width:Math.floor(g.right-g.left),x:Math.floor(g.left),y:Math.floor(g.top)},h+f.$2.appStart);c.children.length&&f.$4(a,b,c.children,d+1,c)})};b.loomTraceWillEnd=function(){c("vc-tracker").VisualCompletionTraceObserver.unsubscribe(this.$3),this.$1=null};return a}();a={getInstance:function(a,b,c){return new h(a,b,c)},isSupported:function(){return h.isSupported()},loomProviderId:"VisualCompletion"};g["default"]=a}),98);
__d("WebLoomEventBuffer",["performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(){function a(){this.$1=[]}var b=a.prototype;b.addEvent=function(a,b){this.$1.push({event:a,timestamp:b!=null?b:(h||(h=c("performanceNow")))()})};b.flushEvents=function(){return this.$1};return a}();g.WebLoomEventBuffer=a}),98);
__d("WebLoomSampling",[],(function(a,b,c,d,e,f){"use strict";function a(a,b,c,d){b=b==="QPL"?a.samplingConfig.adaptive_config.qpl:a.samplingConfig.adaptive_config.interactions;a=d!=null?c+"."+d:""+c;d=b.events[a];if(d!=null)return d;d=(a=b.modules[c>>16&65535])!=null?a:0;return d}f.getSampleRate=a}),66);
__d("WebLoomSerializer",[],(function(a,b,c,d,e,f){"use strict";function g(){return!window.Uint8Array||!window.btoa?!1:!0}function a(a,b,c,d){c=c.flushEvents();if(!g())return null;var e=b.start_time_us,f=b.end_time_us,h=e/1e3,i=f/1e3;if(d!=null){var j=h-d.stats.timeOrigin,k=i-d.stats.timeOrigin;d.trace.samples=d.trace.samples.filter(function(a){return a.timestamp>=j&&a.timestamp<=k})}var l=JSON.stringify(b)+"\n{}\n"+JSON.stringify(d||null).replace(/[^\x00-\x7F]/g,"")+"\n",m=0;c.forEach(function(a){var b=Math.round(a.timestamp*1e3);if(b<e||b>f)return;var c=b-m;m=b;b=a.event;a=[c,b.type];switch(b.type){case"QPL_ANNOTATION":a.push(b.markerId);a.push(b.instanceKey);a.push(b.annotationKey);a.push(b.annotationValue);break;case"QPL_START":a.push(b.markerId);a.push(b.instanceKey);break;case"QPL_END":a.push(b.markerId);a.push(b.instanceKey);a.push(b.action);break;case"QPL_POINT":a.push(b.markerId);a.push(b.instanceKey);a.push(b.name);b.data!=null&&a.push(b.data);break;case"RESOURCE_TIMING_START":a.push(b.resourceType);a.push(b.resourceId);a.push(b.resourceName);a.push(b.encodedSize);a.push(b.decodedSize);a.push(b.transferSize);a.push(b.responseStatus);break;case"RESOURCE_TIMING_END":a.push(b.resourceType);a.push(b.resourceId);break;case"RESOURCE_TIMING_POINT":a.push(b.resourceType);a.push(b.resourceId);a.push(b.pointName);break;case"INTERACTION_TRACE_START":a.push(b.traceId);a.push(b.execUnitName);a.push(b.blockName);a.push(b.blockType);a.push(b.traceType);break;case"INTERACTION_TRACE_END":a.push(b.traceId);a.push(b.execUnitName);a.push(b.blockName);a.push(b.blockType);a.push(b.traceType);a.push(b.blockAnnotations);break;case"INTERACTION_TRACE_POINT":a.push(b.traceId);a.push(b.execUnitName);a.push(b.blockName);a.push(b.pointName);a.push(b.pointAnnotations);break;case"VISUAL_COMPLETION_RECT":a.push(b.elementType);a.push(b.depth);a.push(b.x);a.push(b.y);a.push(b.width);a.push(b.height);a.push(b.mutationType);a.push(b.lateMutationType);break;case"VISUAL_COMPLETION_PROGRESS":a.push(b.progress);break;case"JS_SCHEDULER_QUEUE":a.push(b.priority);a.push(b.queueSize);break}l+=JSON.stringify(a)+"\n"});return a.compressStringToSnappy(l)}f.isSupported=g;f.serialize=a}),66);
__d("WebLoomCore",["InteractionTracingLoomProvider","QPLEvent","QPLLoomProvider","ResourceTimingLoomProvider","VisualCompletionLoomProvider","WebLoomEventBuffer","WebLoomSampling","WebLoomSerializer","addAnnotations","fb-error","mapObject","one-trace","performanceNow","recoverableViolation","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=new Set(["InteractionTracing"]);a=function(){function a(a,b){var e=this;this.$1=a;this.$2=b;this.$7=[c("InteractionTracingLoomProvider"),c("VisualCompletionLoomProvider"),c("ResourceTimingLoomProvider"),c("QPLLoomProvider")];this.$5=new Set(this.$7.map(function(a){return a.loomProviderId}));this.$6=new Set(this.$7.map(function(a){return a.loomProviderId}));this.$3=new Map();this.$4=new Map();this.$8=d("WebLoomSerializer").isSupported();this.$9=!1;this.$10=(b=a.debugLogToConsole)!=null?b:!1;this.$11=1;this.$12=new Map();this.$13=c("uuidv4")();this.$14=1;this.addStatusListener(function(a,b){e.$10&&e.$16("[Web Loom] trace",b,{interactionId:a.triggerId,traceReferenceId:e.$17(a),qplMarkerId:a.triggerInfo.qpl_marker_id,tracePolicy:a.triggerInfo.trace_policy})})}var b=a.prototype;b.setDebugLogToConsole=function(a){this.$10=a};b.getNextSequenceNumber=function(){var a=this.$14;this.$14++;return a};b.getSessionId=function(){return this.$13};b.getActiveTraces=function(){return this.$3};b.addProvider=function(a,b){b===void 0&&(b=!0),this.$6.has(a.loomProviderId)||(this.$7.push(a),this.$6.add(a.loomProviderId)),b&&!this.$5.has(a.loomProviderId)&&this.$5.add(a.loomProviderId)};b.addStatusListener=function(a){var b=this,c=this.$11++;this.$12.set(c,a);return{dispose:function(){b.$12.delete(c)}}};b.maybeStartTraceForInteraction=function(a,b,c,e,f){c=(j||(j=d("QPLEvent"))).getMarkerId(c);var g=d("WebLoomSampling").getSampleRate(this.$1,"INTERACTION",c,e),h=this.$2.Random.coinflip(g);this.$10&&this.$16("[Web Loom] maybeStartTraceForInteraction",{interactionId:a,markerId:c,tracePolicy:e,sampleRate:g,passedSamplingCheck:h});if(!h)return null;h=f+this.$1.appStart;f={interaction_class:b,interaction_id:a,qpl_marker_id:""+c,sample_rate:g,trace_policy:e,type:"INTERACTION"};return this.startTrace(a,f,h,this.$1.useLiteTracing?k:void 0)};b.startTraceManually=function(a,b,c,e,f,g){b=(j||(j=d("QPLEvent"))).getMarkerId(b);c=c+this.$1.appStart;b={interaction_id:a,qpl_marker_id:""+b,sample_rate:e,type:"INTERACTION"};g!=null&&(b.interaction_class=g);f!=null&&(b.trace_policy=f);return this.startTrace(a,b,c,this.$1.useLiteTracing?k:void 0)};b.endTraceForInteraction=function(a,b,d){var e=a.traceId,f=babelHelpers.extends({},null);for(var g in a.annotations)for(var j in a.annotations[g])f[j]=a.annotations[g][j];f.qpl_action=b;j=(h||(h=c("mapObject")))(a.tagSet,function(a){return Array.from(a)});g=a.completed;b=(b=a.markerPoints.visuallyComplete)==null?void 0:b.timestamp;a=(a=a.markerPoints.logVC)==null?void 0:a.timestamp;g=Math.max(g!=null?g:0,b!=null?b:0,a!=null?a:0,d!=null?d:0);b=g>0?g:(i||(i=c("performanceNow")))();a=b+this.$1.appStart;return this.endTrace(e,a,f,j)};b.startTrace=function(a,b,e,f){var g=this;f===void 0&&(f=this.$5);if(!this.$8)return null;if(this.$3.has(a)){c("recoverableViolation")("Already running trace for triggerId: "+a,"web_loom");return null}var h=this.$14++,i={buffer:new(d("WebLoomEventBuffer").WebLoomEventBuffer)(),triggerId:a,triggerInfo:b,startTime:e,sequenceNumber:h},j=new Set(),k=[];this.$7.forEach(function(a){f.has(a.loomProviderId)&&a.isSupported()&&(k.push(a.getInstance(i,g.$2,g.$1)),j.add(a.loomProviderId))});var l="STARTED";this.$3.set(a,{traceContext:i,providerInstances:k,status:l,startURI:window.location.href});this.$12.forEach(function(a){return a(i,l)});var m=this.$17(i);c("one-trace")&&(this.$15=c("one-trace").subscribe("trace-start",function(a){(a.traceType==="LONGTASK"||a.traceType==="INP")&&c("addAnnotations")(a.annotations,{string:{loomRefId:m},string_array:{loomProviders:Array.from(j)}})}));return{traceReferenceId:m,loomProviders:j}};b.$17=function(a){return this.$13+"_"+a.sequenceNumber};b.endTrace=async function(a,b,e,f){var g=this,h=this.$3.get(a);if(!h){c("recoverableViolation")("No trace running for triggerId: "+a,"web_loom");return!1}var i=h.traceContext.sequenceNumber;this.$3.delete(a);this.$4.set(i,h);this.$18(h,"END_PENDING");var j=[];h.providerInstances.forEach(function(a){a=a.loomTraceWillEnd();a&&j.push(a)});this.$15&&this.$15();a=window.location.href;try{await Promise.all(j);var k=this.$1.perfXData,l=h.traceContext.triggerInfo;b={app_id:this.$1.appId,start_time_us:Math.round(h.traceContext.startTime*1e3),end_time_us:Math.round(b*1e3),trigger_id:h.traceContext.triggerId,trigger_info:l,trigger_metadata:e,trigger_metadata_sets:f,client_push_phase:k.client_push_phase,device_num_cores:k.num_cores!=null?Math.floor(k.num_cores):null,device_ram_bytes:k.ram_gb!=null?k.ram_gb***********:null,is_rtl:k.isRTL,locale:k.locale,network_effective_connection_type:k.effective_connection_type,network_downlink_bps:k.downlink_megabits!=null&&k.downlink_megabits*1e6<1e20?k.downlink_megabits*1e6:null,network_rtt_ms:k.rtt_ms,client_rev:this.$1.clientRev,server_rev:this.$1.serverRev,spin_mode:this.$1.spinMode,start_uri:h.startURI,end_uri:a};e=d("WebLoomSerializer").serialize(this.$2,b,h.traceContext.buffer,h.traceContext.jsSelfProfilerData);if(e!=null){f={trace:e,session_id:this.$13,sequence_number:h.traceContext.sequenceNumber,qpl_marker_id:l.qpl_marker_id,trace_policy:l.trace_policy,sample_rate:l.sample_rate};this.$2.Transport.post(f,{onComplete:function(){g.$18(h,"COMPLETE"),g.$4.delete(i)},isHighPri:this.$9});this.$18(h,"UPLOAD_PENDING")}else this.$18(h,"COMPLETE"),this.$4.delete(i),c("fb-error").FBLogger("webloom").warn("[Loom Trace]Failed to serialize trace, trace will be dropped. QPL marker id: %s",l.qpl_marker_id)}catch(a){this.$18(h,"ERROR"),this.$4.delete(i)}return!0};b.flush=function(a){var b=this,c=new Set(),d=new Set();this.$4.forEach(function(a){a.status==="END_PENDING"?c.add(a.traceContext.sequenceNumber):a.status==="UPLOAD_PENDING"&&d.add(a.traceContext.sequenceNumber)});if(c.size>0)var e=this.addStatusListener(function(d){c.delete(d.sequenceNumber),c.size===0&&(b.$2.Transport.flush(a,a),e.dispose())});else d.size>0?this.$2.Transport.flush(a,a):a&&a()};b.setIsDevToolsConnected=function(a){this.$9=a};b.$18=function(a,b){a.status=b,this.$12.forEach(function(b){return b(a.traceContext,a.status)})};b.$16=function(){var a=typeof console!=="undefined"?console:null;a&&a.log.apply(a,arguments)};return a}();g.default=a}),98);
__d("web-loom",["WebLoomCore"],(function(a,b,c,d,e,f,g){"use strict";g.WebLoomCore=c("WebLoomCore")}),98);
__d("WebLoom",["CurrentUser","Env","PerfXSharedFields","QuickPerformanceLogger","Random","SiteData","SnappyCompressUtil","URI","UserAgent","WebLoomBanzaiTransport","WebLoomConfig","cr:1094133","cr:955714","gkx","performanceNavigationStart","web-loom"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;a={QuickPerformanceLogger:h||c("QuickPerformanceLogger"),Random:c("Random"),Transport:c("WebLoomBanzaiTransport"),URI:i||(i=c("URI")),isBrowser:c("UserAgent").isBrowser,compressStringToSnappy:c("SnappyCompressUtil").compressStringToSnappy};e={appStart:c("performanceNavigationStart")(),appId:c("CurrentUser").getAppID(),sanitizeURIs:c("gkx")("20864"),samplingConfig:c("WebLoomConfig"),clientRev:c("SiteData").client_revision,serverRev:c("SiteData").server_revision,spinMode:c("SiteData").spin,useLiteTracing:c("gkx")("20865"),perfXData:c("PerfXSharedFields").getCommonData(),debugLogToConsole:c("gkx")("7170")};f=new(d("web-loom").WebLoomCore)(e,a);b("cr:1094133")&&f.addProvider(b("cr:1094133"));b("cr:955714")&&(j||(j=c("Env"))).jssp_header_sent&&f.addProvider(b("cr:955714"));d=f;g["default"]=d}),98);
__d("FBInteractionTracingDependencies",["HeroBootloadPerfStore","QuickPerformanceLogger","WebLoom","cr:3976","getReactComponentStackFromDOMElement_THIS_CAN_BREAK","vc-tracker"],(function(a,b,c,d,e,f,g){"use strict";var h;a={getReactComponentStackFromDOMElement:c("getReactComponentStackFromDOMElement_THIS_CAN_BREAK"),HeroBootloadPerfStore:{addStaticResourcesStats:d("HeroBootloadPerfStore").addStaticResourcesStats},QuickPerformanceLogger:h||(h=c("QuickPerformanceLogger")),UserTimingUtils:b("cr:3976"),VCTracker:c("vc-tracker"),WebLoom:c("WebLoom")};g["default"]=a}),98);
__d("FacebookAppIDs",[],(function(a,b,c,d,e,f){a=Object.freeze({ABRA_WEB:****************,ACCOUNT_QUALITY:***************,ADS_AMI_BRAZIL_ONBOARDING:***************,ADS_AMI_ONBOARDING:****************,ADS_EVENTS_MANAGER:****************,ADS_INCUBATOR:***************,ADS_PAYMENT:***************,ADS_POWER_EDITOR:***************,ADS_WHATSAPP_MARKETING_MESSAGE_APP:***************,ADS_WHATSAPP_MARKETING_MESSAGE_TEST_APP:***************,ARMADILLO_WEB_DEVSERVER_CLIENT:****************,AT_WORK_FOR_ANDROID:****************,AT_WORK_FOR_IOS:***************,BIG_COMMERCE_CATALOG_SYNC:***************,BIZWEB:***************,BOOSTED_INSTAGRAM_MEDIA_MOBILE:***************,BOOSTED_PAGE:****************,BUSINESS_ACCOUNTS:***************,BUSINESS_MANAGER_WORKPLACE_LOGIN:***************,CHATPROXY_WEB:***************,DOLLY:***************,EVENTS:**********,FACEBOOK_FOR_ANDROID:***********,FACEBOOK_FOR_ANDROID2:************,FACEBOOK_FOR_EMERGING_MARKET_ANDROID:***************,FACEBOOK_FOR_IPAD:***************,FACEBOOK_FOR_IPHONE:**********,FACEBOOK_MEDIA_EFFECTS:****************,FACEBOOK_MESSENGER_FOR_ANDROID:***************,FACEBOOK_SPACES_2:****************,FBPAGES:**********,GAMES_INSTANT_SOAP_MITIGATION:****************,HORIZON_WEB:***************,INSTAGRAM:***************,INSTAGRAM_CARBON:***************,INSTAGRAM_FOR_ANDROID_ANALYTICS_ONLY:***************,INSTAGRAM_WEB:****************,INSTAGRAM_WEB_DESKTOP:***************,INSTAGRAM_WEB_OCULUS_PWA:****************,INSTAGRAM_WEB_PWA_FOR_WINSTORE:***************,INTERN_ADS_PREVIEW_GENERATION_APP:****************,KADABRA_VOICE:***************,LIFT_STUDY_CREATION:***************,LIFT_STUDY_VIEW:****************,M_SITE:************,M_TOUCH_SITE:333176286769482,MAGENTO_CATALOG_SYNC:195311308289826,MARKETPLACE:1606854132932955,MARKETPLACE_SOAP_MITIGATION:359197717007187,MEDIA_MANAGER:2007914219485853,MESSENGER_DESKTOP:195376314393036,MESSENGER_DESKTOP_ARCHON_MACOS:451384735309667,MESSENGER_DESKTOP_ARCHON_WINDOWS:1931350367173590,MESSENGERDOTCOM:772021112871879,MOBILE_ADS_MANAGER_FOR_ANDROID:438142079694454,MOBILE_ADS_MANAGER_FOR_IOS:1479723375646806,MOST_RECENT_FEED:608920319153834,MQTT_WEB:219994525426954,OCULUS_ANDROID_TWILIGHT:624536201004543,OCULUS_IOS_TWILIGHT:147781309031234,OCULUS_IOS_TWILIGHT_DEV:1635404796768116,OCULUS_IOS_TWILIGHT_IN_HOUSE:1543576032349914,OCULUS_IOS_TWILIGHT_LOCAL:****************,OCULUS_MSITE:****************,OCULUS_WEBSITE:****************,PAGE_INBOX:***************,PHOTOS:**********,POSTS:****************,SAVED_FOR_LATER:***************,SFCC_CATALOG_SYNC:***************,SHOPIFY_API_INTEGRATION_2P:***************,SHOPIFY_CONFIGURATION:****************,SHOPIFY_PARTNER_INTEGRATION:***************,TRANSLATIONS:**********,TURDUCKEN:***************,UNIFIED_AMI_ONBOARDING:***************,WHATSAPP_ANDROID:***************,WHATSAPP_BUSINESS_ACCOUNT_MANAGER:***************,WHATSAPP_IPHONE:************,WHATSAPP_KAIOS:***************,WHATSAPP_MAC:***************,WHATSAPP_SMB_ANDROID:***************,WHATSAPP_SMB_IPHONE:***************,WHATSAPP_SMB_WEB:***************,WHATSAPP_VR:***************,WHATSAPP_WEB:***************,WHATSAPP_WINDOWS:***************,WHATSAPP_WINDOWS_PHONE:****************,WIT:***************,WORKPLACE_BILLING:****************,WORKPLACE_DESKTOP:***************,WORKPLACE_FOR_EVERY_PHONE:****************,WORKPLACE_FOR_WEB:****************,WORKROOMS:***************,WORKROOMS_WEB:***************,WWW:************,WWW_COMET:****************});f["default"]=a}),66);
__d("HeroTracingCoreConfigWWW",["gkx"],(function(a,b,c,d,e,f,g){"use strict";b={alwaysMarkMutationRootAsVisualChange:(a=c("gkx"))("20863"),enableCascadingRenderDetector:a("12285"),enableHeroLoggingVerbose:a("10725"),enableReactProfiling:a("12286"),enableResetCompletedFix:a("12288"),enableTrackName:!0,fixParentInteractionIdWhenCancel:a("12343"),observeMutationOnStart:a("12287"),removeHoldOnParentContextOnNewInteraction:a("12290")};g["default"]=b}),98);
__d("useCometTailLoadPageletTracker",["CometTailLoadLogger","intersectionObserverEntryIsIntersecting","react","useCometRouteTracePolicy"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useCallback,j=b.useRef;function a(a,b,e){var f=j(null),g=j(!1),h=j(null),k=c("useCometRouteTracePolicy")();return i(function(i){if(i==null){h.current==null?void 0:h.current();h.current=null;return}if(e===!0&&a!=null&&b!=null&&f.current!==i){f.current=i;var j=function(e){Array.prototype.forEach.call(e,function(e){c("intersectionObserverEntryIsIntersecting")(e)&&!g.current&&(g.current=!0,h.current==null?void 0:h.current(),h.current=null,d("CometTailLoadLogger").logMarkerEnd(a,b,e.time,2,k))})},l=new IntersectionObserver(j);l.observe(i);h.current=function(){f.current=null,l.disconnect()}}},[a,b,e])}g["default"]=a}),98);
__d("HeroTracingCoreDependenciesWWW",["cr:3798","useCometTailLoadPageletTracker"],(function(a,b,c,d,e,f,g){"use strict";a={useTailLoadPageletTracker:c("useCometTailLoadPageletTracker"),UserTimingUtils:b("cr:3798")};g["default"]=a}),98);
__d("InlineFbtResult",["cr:1183579"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:1183579")}),98);
__d("InlineFbtResultImplComet",["FbtHooks","FbtReactUtil","FbtResultBase","react","recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function k(a){var b=a.content,d=a.hash,e=a.inlineMode;a=a.translation;d==null&&c("recoverableViolation")('Fbt string hash should not be null for translated string "'+a+'" '+("[inlineMode="+e+"]"),"internationalization");return j.jsx("span",{"data-intl-hash":d,"data-intl-translation":a,"data-intl-trid":"",children:b})}k.displayName=k.name+" [from "+f.id+"]";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,e,f,g){var i;i=a.call(this,b,(h||(h=c("FbtHooks"))).getErrorListener({hash:g,translation:f}))||this;i.$$typeof=d("FbtReactUtil").REACT_ELEMENT_TYPE;i.key=null;i.ref=null;i.type=k;i.props={content:b,hash:g,inlineMode:e,translation:f};return i}return b}(c("FbtResultBase"));g["default"]=a}),98);
__d("NavigationMetricsCore",["mixInEventEmitter","pageID"],(function(a,b,c,d,e,f,g){var h={NAVIGATION_DONE:"NAVIGATION_DONE",EVENT_OCCURRED:"EVENT_OCCURRED"},i={tti:"tti",e2e:"e2e",all_pagelets_loaded:"all_pagelets_loaded",all_pagelets_displayed:"all_pagelets_displayed"},j=0,k={},l=function(){function a(){this.eventTimings={tti:null,e2e:null,all_pagelets_loaded:null,all_pagelets_displayed:null},this.lid=c("pageID")+":"+j++,this.extras={}}var b=a.prototype;b.getLID=function(){return this.lid};b.setRequestStart=function(a){this.start=a;return this};b.setTTI=function(a){this.eventTimings.tti=a;this.$1(i.tti,a);return this};b.setE2E=function(a){this.eventTimings.e2e=a;this.$1(i.e2e,a);return this};b.setExtra=function(a,b){this.extras[a]=b;return this};b.setDisplayDone=function(a){this.eventTimings.all_pagelets_displayed=a;this.setExtra("all_pagelets_displayed",a);this.$1(i.all_pagelets_displayed,a);return this};b.setAllPageletsLoaded=function(a){this.eventTimings.all_pagelets_loaded=a;this.setExtra("all_pagelets_loaded",a);this.$1(i.all_pagelets_loaded,a);return this};b.setServerLID=function(a){this.serverLID=a;return this};b.$1=function(a,b){var c={};k!=null&&this.serverLID!=null&&k[this.serverLID]!=null&&(c=k[this.serverLID]);c=babelHelpers["extends"]({},c,{event:a,timestamp:b});m.emitAndHold(h.EVENT_OCCURRED,this.serverLID,c);return this};b.doneNavigation=function(){var a=babelHelpers["extends"]({start:this.start,extras:this.extras},this.eventTimings);if(this.serverLID&&k[this.serverLID]){var b=this.serverLID;Object.assign(a,k[b]);delete k[b]}m.emitAndHold(h.NAVIGATION_DONE,this.lid,a)};return a}(),m={Events:h,postPagelet:function(a,b,c){},siteInit:function(a){a(l)},setPage:function(a){if(!a.serverLID)return;k[a.serverLID]={page:a.page,pageType:a.page_type,pageURI:a.page_uri,serverLID:a.serverLID}},getFullPageLoadLid:function(){throw new Error("getFullPageLoadLid is not implemented on this site")}};c("mixInEventEmitter")(m,h);a=m;g["default"]=a}),98);
__d("PageEvents",[],(function(a,b,c,d,e,f){a=Object.freeze({NATIVE_ONLOAD:"onload/onload",BIGPIPE_ONLOAD:"onload/onload_callback",AJAXPIPE_ONLOAD:"ajaxpipe/onload_callback",NATIVE_DOMREADY:"onload/dom_content_ready",BIGPIPE_DOMREADY:"onload/domcontent_callback",AJAXPIPE_DOMREADY:"ajaxpipe/domcontent_callback",NATIVE_ONBEFOREUNLOAD:"onload/beforeunload",NATIVE_ONUNLOAD:"onload/unload",AJAXPIPE_ONUNLOAD:"onload/exit",AJAXPIPE_SEND:"ajaxpipe/send",AJAXPIPE_FIRST_RESPONSE:"ajaxpipe/first_response",AJAXPIPE_ONBEFORECLEARCANVAS:"ajaxpipe/onbeforeclearcanvas"});f["default"]=a}),66);
__d("NavigationMetricsWWW",["Arbiter","BigPipeInstance","NavigationMetricsCore","PageEvents","performance"],(function(a,b,c,d,e,f,g){var h,i="0";c("NavigationMetricsCore").getFullPageLoadLid=function(){return i};c("NavigationMetricsCore").siteInit(function(a){var b=new a(),e=!0;c("Arbiter").subscribe(d("BigPipeInstance").Events.init,function(f,g){var h=e?b:new a();e&&(i=g.lid);e=!1;h.setServerLID(g.lid);f=g.arbiter;f.subscribe(d("BigPipeInstance").Events.tti,function(a,b){a=b.ts;h.setTTI(a)});f.subscribe(c("PageEvents").AJAXPIPE_SEND,function(a,b){a=b.ts;h.setRequestStart(a)});f.subscribe(c("PageEvents").AJAXPIPE_ONLOAD,function(a,b){a=b.ts;h.setE2E(a).doneNavigation()});f.subscribe(d("BigPipeInstance").Events.displayed,function(a,b){a=b.ts;h.setDisplayDone(a)});f.subscribe(d("BigPipeInstance").Events.loaded,function(a,b){a=b.ts;h.setAllPageletsLoaded(a)})});c("Arbiter").subscribe(c("PageEvents").BIGPIPE_ONLOAD,function(a,d){a=d.ts;e=!1;b.setRequestStart((h||(h=c("performance"))).timing&&(h||(h=c("performance"))).timing.navigationStart).setE2E(a).doneNavigation()})});g["default"]=c("NavigationMetricsCore")}),98);
__d("RSTAdsManagerUtilsMainThread",["requireWeak"],(function(a,b,c,d,e,f,g){"use strict";var h=null;c("requireWeak")("AdsAccountStore",function(a){h=a});var i=null;c("requireWeak")("AdsPERouterHelper",function(a){i=a});function a(){try{var a;a=(a=(a=i)==null?void 0:(a=a.getRouter())==null?void 0:a.getQueryParams())!=null?a:{};var b=a.selected_campaign_ids,c=a.selected_adset_ids;a=a.selected_ad_ids;a=(a=a==null?void 0:a.toArray())!=null?a:[];c=(c=c==null?void 0:c.toArray())!=null?c:[];b=(b=b==null?void 0:b.toArray())!=null?b:[];return{adIds:a.map(function(a){return String(a)}),adsetIds:c.map(function(a){return String(a)}),campaignIds:b.map(function(a){return String(a)})}}catch(a){return{adIds:[],adsetIds:[],campaignIds:[]}}}function b(){var a;return(a=(a=h)==null?void 0:a.getSelectedAccountID())!=null?a:"0"}function d(){var a;return(a=(a=h)==null?void 0:a.getSelectedBusinessID())!=null?a:"0"}function e(){return{}}g.getAdEntityIds=a;g.getAdAccountID=b;g.getBusinessID=d;g.getAdsAnnotations=e}),98);
__d("reactComponentNameSemanticsCheck",[],(function(a,b,c,d,e,f){"use strict";c=["Child","Cell","CometImage","ComponentLoader","FocusWithinHandler","LegacyHidden","Path","SearchCometGlobalTypeaheadWithBackButtonFocus","StrictMode","Svg","WebView","GeoBaseText","FocusGroup","FocusTable","MDSText",/LazyLoadEntryPointContainer/,/relay-hooks/,/CommandWrapper/,/ScrollView/,/Layout/,/Base$/,/Boundary$/,/LoadingState$/,/FocusManager/,/Column$/,/Typeahead.*Strategy/,/CometList_/,/CometSSR/,/^ActorHovercard/,/^Base/,/^CometHero/,/^FDS/,/^Hero/,/^XDS/,/^deferredLoadComponent/,/^html\./,/^t[d]$/,/Container$/,/Context$/,/Entities$/,/Factory$/,/Focus.*Region/,/Grid$/,/HideLayer/,/Icon$/,/Impl$/,/InputButton$/,/Item$/,/ItemSelectable/,/KeyCommands$/,/Link$/,/ListCell/,/Logger$/,/MitigationNode$/,/OnEscape/,/Overlay$/,/OverlayInternal$/,/Placeholder$/,/Pressable/,/Provider$/,/Provider_/,/ReactWarnings/,/Ref$/,/Refs$/,/Relay$/,/Renderer$/,/Resolver$/,/Root$/,/Routes$/,/Row$/,/SVG/,/ScrollableArea/,/Theme$/,/Tooltip/,/Trigger$/,/Typeahead/,/Virtualization$/,/Wrapper$/,/\.svg$/];var g=new Set(Array.from(c).filter(function(a){return typeof a==="string"})),h=Array.from(c).filter(function(a){return a instanceof RegExp});function a(a){var b=a;a.includes(" [")&&(b=a.split(" [")[0]);b=b.trim();return b.length<=2?!0:g.has(b)||h.some(function(a){return a.test(b)})}d=["MessageList","MWXText","MDSText","MWChatContact","MWRelayChatTabHeader"];var i=new Set(Array.from(d).filter(function(a){return typeof a==="string"})),j=Array.from(d).filter(function(a){return a instanceof RegExp});function b(a){var b=a.trim();return i.has(b)||j.some(function(a){return a.test(b)})}f.isReactComponentNameMeaningless=a;f.isReactComponentTextBlocked=b}),66);
__d("RSTConfig",["CurrentUserInitialData","WebResponsivenessConfig","gkx","justknobx","reactComponentNameSemanticsCheck"],(function(a,b,c,d,e,f,g){"use strict";var h,i={app_id:"default",first_heartbeat_threshold_ms:1e4,freeze_threshold_ms:500,heart_beat_interval_ms:200,main_thread_logging_interval_ms:5e3,encrypt_db:!0,start_rst_from_main_thread_extra_delay_in_ms:0};function a(){var a,b=c("WebResponsivenessConfig").rst.app_config;return b.length===0?i:(a=(a=b.find(function(a){return""+a.app_id===""+((a=(h||(h=c("CurrentUserInitialData"))).APP_ID)!=null?a:0)}))!=null?a:b.find(function(a){return a.app_id==="default"}))!=null?a:i}e=a();function b(){var a="__felab_context"in window;return j.RST_ANNOTATION_KILLSWITCH_ON&&j.SHOULD_COLLECT_RST_ANNOTATIONS&&!a}var j={PROJECT_NAME:"RST",PROJECT_ONCALL:"web_ecosystem",DEBUG:!1,ENCRYPT_DB:e.encrypt_db,FIRST_HEART_BEAT_THRESHOLD_MS:e.first_heartbeat_threshold_ms,FREEZE_THRESHOLD_MS:e.freeze_threshold_ms,HEART_BEAT_INTERVAL_MS:e.heart_beat_interval_ms,START_RST_FROM_MAIN_THREAD_EXTRA_DELAY_IN_MS:e.start_rst_from_main_thread_extra_delay_in_ms,INDEX_DB_NAME:"responsiveness-db",INDEX_DB_TABLE_NAME:"unresponsiveness-events-v2",PAST_INDEX_DB_TABLE_NAMES:["unresponsiveness-events"],INDEX_DB_VERSION:4,WAIT_MS_FOR_OTHER_SESSION_BEFORE_PROCESSING:30*1e3,WEB_WORKER_UPDATE_INCIDENTS_INTERVAL_MS:(f=c("justknobx"))._("3306"),UNRECOVER_LASTS_THRESHOLD_MS:f._("3307"),SHOULD_SAMPLE_RECOVERABLE:f._("3273"),SHOULD_CAPTURE_INTERACTION_DATA:f._("3709"),SHOULD_CAPTURE_INTERACTION_ELEMENT_TEXT:c("justknobx")._("4704")||c("gkx")("15466"),ELEMENT_TEXT_SIZE_LIMIT:20,RST_ANNOTATION_KILLSWITCH_ON:c("justknobx")._("3738"),SHOULD_COLLECT_RST_ANNOTATIONS:c("gkx")("10200"),OBSOLETE_PENDING_INCIDENT_THRESHOLD_MS:4*24*60*60*1e3,MAIN_THREAD_LOGGING_INTERVAL_MS:e.main_thread_logging_interval_ms,INCIDENT_PROPOSED_PROCESSOR_OWNERSHIP_EXPIRE_MS:3*e.main_thread_logging_interval_ms,MESSAGE_TYPE:"responsiveness",DEBUG_LOGGING_ENABLED:c("gkx")("6199"),MAX_DB_FAILED_COUNT:10,TRACE_STATS_WINDOW_SIZE_MS:2*60*1e3,TRACE_STATS_MAX_QUEUE_SIZE:200,DEFAULT_REACT_COMPONENT_CONTEXT_SIZE:3,heartBeatCheckOn:!0,shouldCollectAdditionalMetadata:b,isReactComponentNameBlocked:d("reactComponentNameSemanticsCheck").isReactComponentNameMeaningless,isReactComponentTextBlocked:d("reactComponentNameSemanticsCheck").isReactComponentTextBlocked};a=j;g["default"]=a}),98);
__d("RSTEvents",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";var g=b("$InternalEnum")({HEART_BEAT:0,BROWSER_TAB_FOREGROUND:1,BROWSER_TAB_BACKGROUND:2,TRACE_START:3,TRACE_END:4,TRACE_POLICY_SET:5,INTERACTION_DATA:6,SPAN_START:7,SPAN_END:8,LOG_FLUSH:9,RECOVERABLE_UNRESPONSIVENESS:1e5,UNRECOVERABLE_UNRESPONSIVENESS:100001,CHECKING_INDEXDB_FOR_EVENTS:100002,UNRESPONSIVENESS_DETECTED:-1,LOGGING_UNRESPONSIVENESS_TO_INDEXDB:-2,NO_HEART_BEAT_SINCE_ORIGIN:-3});function a(a){return a===g.LOG_FLUSH}f.RSTEvent=g;f.isRSTFlushEvent=a}),66);
__d("RSTEventsMessageQueue",[],(function(a,b,c,d,e,f){"use strict";var g=new Map();function a(a,b){g.has(a)||g.set(a,new Set());(a=g.get(a))==null?void 0:a.add(b)}function b(a,b){a=g.get(a);a!=null&&a.forEach(function(a){return a(b)})}f.subscribe=a;f.notify=b}),66);
__d("RSTUtils",["RSTConfig"],(function(a,b,c,d,e,f,g){"use strict";function a(){return typeof window!=="undefined"&&window.document!=null}a=a();a=a?"MAIN":"WORKER";var h=c(),i=b();function b(){return h&&typeof self!=="undefined"&&typeof self.setTimeout==="function"?self:window}function c(){return typeof WorkerGlobalScope!=="undefined"?!0:typeof document==="undefined"}function d(){}function e(){}function f(a){if(a==null)return"";a=new URL(a);return a.origin+a.pathname}async function j(){return typeof scheduler!=="undefined"&&typeof scheduler.yield==="function"?scheduler.yield():new Promise(function(a){return i.setTimeout(a,0)})}function k(){return"indexedDB"in i}function l(a){return a!=null?String(a):null}function m(a){var b=a.componentName;a=n(a.componentText);if(b==null&&a==null)return null;if(a==null)return b;else if(b==null)return' ["'+a+'"]';else return b+' ["'+a+'"]'}function n(a){return a==null?null:a.replace(/[\[\]\",\']/g,"")}g.getGlobalObject=b;g.isInWorker=c;g.debugLogImportant=d;g.debugLog=e;g.sanitizeURL=f;g.scheduleYield=j;g.isIndexedDBSupported=k;g.intToString=l;g.getInteractionDetail=m}),98);
__d("RSTUtilsMainThread",["CurrentUser","FacebookAppIDs","RSTConfig"],(function(a,b,c,d,e,f,g){"use strict";var h=new Map();function a(a){if(h.has(a)){var b;b=(b=h.get(a))!=null?b:0;if(b<=0){h["delete"](a);return!1}h.set(a,b-1);return!0}h.set(a,Math.floor(Math.random()*10));return!0}f=c("CurrentUser").getAppID();function b(a){switch(a){case""+c("FacebookAppIDs").ADS_POWER_EDITOR:return"Ads";case""+c("FacebookAppIDs").WWW_COMET:return"Comet";case""+c("FacebookAppIDs").ADS_EVENTS_MANAGER:return"Business";default:return"(Unset-in-RST)"}}var i=new Set([c("FacebookAppIDs").ADS_POWER_EDITOR,c("FacebookAppIDs").ADS_EVENTS_MANAGER,c("FacebookAppIDs").BIZWEB].map(function(a){return""+a}));i=f!=null&&i.has(f);b=b(f);function d(a){if(a==null)return null;for(a of a)if(!c("RSTConfig").isReactComponentNameBlocked(a))return a;return null}function e(a,b,d){d===void 0&&(d=c("RSTConfig").DEFAULT_REACT_COMPONENT_CONTEXT_SIZE);if(a==null)return null;var e=new Set(),f=[];for(a of a){if(f.length>=d)break;!e.has(a)&&!b(a)&&(f.push(a),e.add(a))}return f}g.shouldSkipProcessingIncident=a;g.isMonetizationApp=i;g.appName=b;g.getAllowListedComponentName=d;g.getReactComponentContext=e}),98);
__d("RSTIncidentLoggingMainThread",["InteractionTracingConfigDefault","QPLEvent","RSTConfig","RSTEvents","RSTEventsMessageQueue","RSTUtils","RSTUtilsMainThread","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("requireDeferred")("LogBrowserCrashReportsLowerRetentionFalcoEvent").__setRef("RSTIncidentLoggingMainThread"),j=new Set();function a(){return j}function k(a){var b,c,e,f,g=a.unresponsiveEvent,h=g!=null?Date.now()-(g==null?void 0:g.detectTime):0,i=g==null?void 0:g.lastHeartBeatMetadata,j=(i==null?void 0:i.brsid)!=null?String(i.brsid):null;b=(b=i==null?void 0:i.cpp)!=null?b:null;c=(c=i==null?void 0:i.cr)!=null?c:null;e=(e=i==null?void 0:i.annotations)!=null?e:{};f=(f=i==null?void 0:i.spans)!=null?f:{};a=a.isRecoverable?"recoverable-unresponsive":"unrecoverable-unresponsive";var k=g==null?void 0:g.traces;k=o(k);var m=k.qplMarkerId;k=k.tracePolicy;var p=n(g==null?void 0:g.lastTrace);return{application_name:d("RSTUtilsMainThread").appName,reason:a,url:d("RSTUtils").sanitizeURL(i==null?void 0:i.url),age:String(h),brsid:j,qpl_event_marker_id:d("RSTUtils").intToString(m),trace_policy:k,last_qpl_event_marker_id:d("RSTUtils").intToString(p.qplMarkerId),last_trace_policy:p.tracePolicy,ad_account_id:(a=i==null?void 0:i.i2)!=null?a:"0",business_id:(h=i==null?void 0:i.i3)!=null?h:"0",client_push_phase:b,client_revision:c,freeze_time_in_ms:String((j=g==null?void 0:g.unrecoverLastsForMs)!=null?j:0),normalized_component_stack:(m=i==null?void 0:i.rcs)!=null?m:[],session_start_timestamp_in_ms:String((k=i==null?void 0:i.st)!=null?k:""),rst_annotations:l(e,f,g)}}function l(a,b,c){var d=[];for(var e of Object.keys(a))d.push(e+":"+a[e]);a=c==null?void 0:c.detectTime;for(e of Object.keys(b)){var f=b[e],g=a!=null?a-f.start:0;d.push("span:"+f.name+":"+((f=f.annotation)!=null?f:"")+":"+g)}m(d,c);return d}function m(a,b){if(!c("RSTConfig").shouldCollectAdditionalMetadata())return a;var d=b==null?void 0:b.lastTrace;d=d==null?void 0:d.endTimestamp;b=b==null?void 0:b.detectTime;d!=null&&b!=null&&a.push("msSinceLastInteractionEnds:"+(b-d));return a}function n(a){if(a==null)return{qplMarkerId:null,tracePolicy:null};if(a.qplEvent==null)return{qplMarkerId:null,tracePolicy:null};var b=(h||(h=d("QPLEvent"))).getMarkerId(a.qplEvent);a=a.tracePolicy;return{qplMarkerId:b,tracePolicy:a}}function o(a){if(a==null)return{qplMarkerId:null,tracePolicy:null};var b=0,c=null;a.forEach(function(a){if((a==null?void 0:a.qplEvent)==null||a.tracePolicy===d("InteractionTracingConfigDefault").DEFAULT_TRACING_CONFIG.defaultTracePolicy)return;a.startTime>b&&(b=a.startTime,c=a)});return n(c)}function p(a,b){i.onReady(function(c){c=c.log;c(function(){return k({unresponsiveEvent:a,isRecoverable:b})})})}function b(){c("RSTConfig").SHOULD_SAMPLE_RECOVERABLE&&d("RSTEventsMessageQueue").subscribe(d("RSTEvents").RSTEvent.RECOVERABLE_UNRESPONSIVENESS,function(a){a=a.unresponsiveEventRecord;if(a==null||j.has(a.incidentID))return;j.add(a.incidentID);d("RSTUtils").debugLogImportant("logging recoverable unresponsiveness event");p(a,!0)}),d("RSTEventsMessageQueue").subscribe(d("RSTEvents").RSTEvent.UNRECOVERABLE_UNRESPONSIVENESS,function(a){a=a.unresponsiveEventRecord;if(a==null||j.has(a.incidentID))return;j.add(a.incidentID);d("RSTUtils").debugLogImportant("logging unrecoverable unresponsiveness event:");p(a,!1)})}g.getLoggedIncidentIDs=a;g.registerIncidentLogging=b}),98);
__d("RSTCrypt",["RSTConfig"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(){}var b=a.prototype;b.encrypt=function(a){return!c("RSTConfig").ENCRYPT_DB?a:btoa(a)};b.decrypt=function(a){return!c("RSTConfig").ENCRYPT_DB?a:atob(a)};return a}();b=new a();d=b;g["default"]=d}),98);
__d("RSTIndexedDB",["FBLogger","RSTConfig","RSTCrypt","RSTEvents","RSTEventsMessageQueue","RSTUtils"],(function(a,b,c,d,e,f,g){"use strict";var h;try{h=self.indexedDB}catch(a){}a=function(){function a(){}var b=a.prototype;b.initDB=function(){var a=this;return new Promise(function(b,d){if(h==null)return d("IndexedDB is not supported in this browser");if(a.$1!=null)return b(a.$1);var e=h.open(c("RSTConfig").INDEX_DB_NAME,c("RSTConfig").INDEX_DB_VERSION);e.onupgradeneeded=function(b){a.$1=b.target.result,a.$2(),a.$3(),a.$4(c("RSTConfig").INDEX_DB_TABLE_NAME,!0)};e.onsuccess=function(c){a.$1=c.target.result,a.$3(),b(c.target.result)};e.onerror=function(b){a.closeDBConnection(),d("Database error: "+b.target.errorCode)}})};b.$2=function(){var a=this.$1;if(a==null)return;for(var b of c("RSTConfig").PAST_INDEX_DB_TABLE_NAMES)a.objectStoreNames.contains(b)&&a.deleteObjectStore(b)};b.$3=function(){var a=this;if(this.$1==null||this.$1.onversionchange!=null)return;this.$1.onversionchange=function(){a.closeDBConnection()}};b.$4=function(a,b){b===void 0&&(b=!1);var c=this.$1;if(c==null||c.objectStoreNames.contains(a))return;if(c.objectStoreNames.contains(a)){if(!b)return;c.deleteObjectStore(a)}c.createObjectStore(a,{autoIncrement:!0})};b.$5=function(a){return c("RSTConfig").ENCRYPT_DB?c("RSTCrypt").encrypt(JSON.stringify(a)):a};b.$6=function(a){if(a==null)return null;var b;typeof a==="string"?b=JSON.parse(c("RSTCrypt").decrypt(a)):b=a;if(typeof b!=="object"||b==null||b.incidentID==null)throw c("FBLogger")("responsive-tracker").mustfixThrow("data parsing error");return b};b.$7=function(a){a===void 0&&(a=null),this.closeDBConnection(),d("RSTUtils").debugLogImportant("indexedDB error, clearing the object store"),c("FBLogger")("responsive-tracker").catching(a).warn("Failed to operate on IndexedDB, clearing the object store"),void this.clearObjectStore()};b.$8=function(a){var b=this,c=[];try{c=a.map(function(a){return b.$6(a)})}catch(a){this.$7(a)}return c};b.$9=function(a,b,c){var e=this;a.onerror=function(a){e.closeDBConnection();a=(a=a==null?void 0:(a=a.target)==null?void 0:a.errorCode)!=null?a:"";a="Failed to handle "+c+" request in IndexedDB."+a;d("RSTUtils").debugLogImportant(a);b(a)}};b.$10=function(a,b,c){var e=this;a.onerror=function(a){e.closeDBConnection();a=(a=a==null?void 0:(a=a.target)==null?void 0:a.errorCode)!=null?a:"";a="Failed to handle "+c+" transaction in IndexedDB."+a;d("RSTUtils").debugLogImportant(a);b(a)}};b.closeDBConnection=function(){var a=this.$1;if(a==null)return;try{a.close()}catch(a){}this.$1=null};b.persistLog=async function(a){try{await this.initDB(),await this.persistEventToDB(a)}catch(a){this.closeDBConnection(),c("FBLogger")("responsive-tracker").catching(a).warn("Failed to persist log")}};b.persistEventToDB=async function(a){var b=this;await this.initDB();var e=Date.now(),f=babelHelpers.extends({loggingTime:e},a);d("RSTEventsMessageQueue").notify(d("RSTEvents").RSTEvent.LOGGING_UNRESPONSIVENESS_TO_INDEXDB,{unresponsiveEventRecord:f});return new Promise(function(a,d){if(b.$1==null){d("indexed db instance is not initialized yet");return}var e=b.$1.transaction([c("RSTConfig").INDEX_DB_TABLE_NAME],"readwrite"),g=e.objectStore(c("RSTConfig").INDEX_DB_TABLE_NAME);g=g.add(b.$5(f));g.onsuccess=function(){a()};b.$9(g,d,"persistEventToDB");b.$10(e,d,"persistEventToDB")})};b.readEventsFromDB=async function(){var a=this;await this.initDB();return new Promise(function(b,d){if(a.$1==null){d("indexed db instance is not initialized yet");return}var e=a.$1.transaction([c("RSTConfig").INDEX_DB_TABLE_NAME],"readonly"),f=e.objectStore(c("RSTConfig").INDEX_DB_TABLE_NAME),g=f.getAll();g.onsuccess=function(){b(a.$8(g.result))};a.$9(g,d,"readEventsFromDB");a.$10(e,d,"readEventsFromDB")})};b.updateIncidentInDB=async function(a){var b=this;await this.initDB();return new Promise(function(e,f){if(b.$1==null){f("IndexedDB instance is not initialized yet");return}var g=b.$1.transaction([c("RSTConfig").INDEX_DB_TABLE_NAME],"readwrite"),h=g.objectStore(c("RSTConfig").INDEX_DB_TABLE_NAME);h=h.openCursor();h.onsuccess=function(c){c=c.target.result;if(c==null){d("RSTUtils").debugLog("Finished clearing incidents from indexDB");e();return}try{var f=b.$6(c.value);if(f==null){var g;d("RSTUtils").debugLog("Unable to parse, deleting incident "+(((g=f==null?void 0:f.incidentID)!=null?g:"")+" from indexDB"));c.delete()}else{g=a(f);if(g){d("RSTUtils").debugLog("Update incident "+((g=c.value)==null?void 0:g.incidentID)+" in indexDB");c.update(b.$5(f))}}}catch(a){c.delete()}c.continue()};b.$9(h,f,"updateIncidentInDB");b.$10(g,f,"updateIncidentInDB")})};b.clearIncidentFromDB=async function(a){var b=this;await this.initDB();return new Promise(function(e,f){if(b.$1==null){f("indexed db instance is not initialized yet");return}var g=b.$1.transaction([c("RSTConfig").INDEX_DB_TABLE_NAME],"readwrite"),h=g.objectStore(c("RSTConfig").INDEX_DB_TABLE_NAME);h=h.openCursor();h.onsuccess=function(c){c=c.target.result;if(c){try{var f=b.$6(c.value);if(f==null||a.has(f==null?void 0:f.incidentID)){d("RSTUtils").debugLog("Deleting incident "+((f=f==null?void 0:f.incidentID)!=null?f:"")+" from indexDB");c.delete()}}catch(a){c.delete()}c.continue()}else d("RSTUtils").debugLog("Finished clearing incidents from indexDB"),e()};b.$9(h,f,"clearIncidentFromDB");b.$10(g,f,"clearIncidentFromDB")})};b.clearObjectStore=async function(a){var b=this;a===void 0&&(a=c("RSTConfig").INDEX_DB_TABLE_NAME);await this.initDB();d("RSTUtils").debugLogImportant("clearing object store:",a);return new Promise(function(c,d){try{var e=b.$1;if(e==null){d("indexed db instance is not initialized yet");return}if(!e.objectStoreNames.contains(a)){c();return}e=e.transaction([a],"readwrite");var f=e.objectStore(a);f=f.clear();f.onsuccess=function(){c()};f.onerror=function(a){b.closeDBConnection(),d("Error clearing the store: "+a.target.errorCode)};b.$9(f,d,"clearObjectStore");b.$10(e,d,"clearObjectStore")}catch(a){c()}})};b.deleteEventFromDB=async function(a){var b=this;await this.initDB();return new Promise(function(d,e){if(b.$1==null){e("indexed db instance is not initialized yet");return}var f=b.$1.transaction([c("RSTConfig").INDEX_DB_TABLE_NAME],"readwrite"),g=f.objectStore(c("RSTConfig").INDEX_DB_TABLE_NAME);g=g.delete(a);g.onsuccess=function(){d()};b.$9(g,e,"deleteEventFromDB");b.$10(f,e,"deleteEventFromDB")})};return a}();g.RSTIndexedDB=a}),98);
__d("RSTIndexedDBSafe",["FBLogger","RSTConfig","RSTIndexedDB","RSTUtils"],(function(a,b,c,d,e,f,g){"use strict";var h=d("RSTUtils").isIndexedDBSupported();a=function(a){babelHelpers.inheritsLoose(b,a);function b(){var b,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(b=c=a.call.apply(a,[this].concat(e))||this,c.$RSTIndexedDBSafe$p_1=!h,c.$RSTIndexedDBSafe$p_2=0,c.$RSTIndexedDBSafe$p_3=["The database connection is closing","Can't start a transaction on a closed database","A version change transaction is running"],b)||babelHelpers.assertThisInitialized(c)}var e=b.prototype;e.guardDB=async function(a,b,e){if(this.$RSTIndexedDBSafe$p_1)return b();try{return await a()}catch(f){this.closeDBConnection();if(!this.$RSTIndexedDBSafe$p_4(f))return b();this.$RSTIndexedDBSafe$p_2<c("RSTConfig").MAX_DB_FAILED_COUNT&&this.$RSTIndexedDBSafe$p_2++;if(this.$RSTIndexedDBSafe$p_2>=c("RSTConfig").MAX_DB_FAILED_COUNT){this.$RSTIndexedDBSafe$p_1=!0;a="Failed to "+e+" "+this.$RSTIndexedDBSafe$p_2+" times disabling RST indexedDB";d("RSTUtils").debugLogImportant(a);c("FBLogger")("responsive-tracker").catching(f).warn(a)}return b()}};e.$RSTIndexedDBSafe$p_4=function(a){if(a==null)return!1;var b=typeof a==="string"?a:a==null?void 0:a.message;return typeof b!=="string"?!0:!this.$RSTIndexedDBSafe$p_3.some(function(a){return b.includes(a)})};e.initDB=function(){var b=this;return this.guardDB(function(){return a.prototype.initDB.call(b)},function(){return null},"initDB")};e.persistLog=function(b){var c=this;return this.guardDB(function(){return a.prototype.persistLog.call(c,b)},function(){},"persistLog")};e.persistEventToDB=function(b){var c=this;return this.guardDB(function(){return a.prototype.persistEventToDB.call(c,b)},function(){},"persistEventToDB")};e.readEventsFromDB=function(){var b=this;return this.guardDB(function(){return a.prototype.readEventsFromDB.call(b)},function(){return[]},"readEventsFromDB")};e.updateIncidentInDB=function(b){var c=this;return this.guardDB(function(){return a.prototype.updateIncidentInDB.call(c,b)},function(){},"updateIncidentInDB")};e.clearIncidentFromDB=function(b){var c=this;return this.guardDB(function(){return a.prototype.clearIncidentFromDB.call(c,b)},function(){},"clearIncidentFromDB")};e.clearObjectStore=function(b){var d=this;b===void 0&&(b=c("RSTConfig").INDEX_DB_TABLE_NAME);return this.guardDB(function(){return a.prototype.clearObjectStore.call(d,b)},function(){},"clearObjectStore")};e.deleteEventFromDB=function(b){var c=this;return this.guardDB(function(){return a.prototype.deleteEventFromDB.call(c,b)},function(){},"deleteEventFromDB")};e.maybeClearObsoleteIncidents=async function(){try{var a=await this.readEventsFromDB(),b=new Set();a.forEach(function(a){if(a==null)return;if(typeof a.incidentID!=="string")return;(typeof a.detectTime!=="number"||Date.now()-a.detectTime>(1+Math.random())*c("RSTConfig").OBSOLETE_PENDING_INCIDENT_THRESHOLD_MS)&&b.add(a.incidentID)});await this.clearIncidentFromDB(b)}catch(b){c("FBLogger")("responsive-tracker").catching(b).warn((a=b==null?void 0:b.message)!=null?a:"Failed to clear obsolete incidents incidents")}};return b}(d("RSTIndexedDB").RSTIndexedDB);b=new a();e=b;g.default=e}),98);
__d("RSTSessionID",["Env","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h,i=null;function a(){if(i!=null)return i;var a=(h||(h=c("Env"))).brsid!=null?""+(h||(h=c("Env"))).brsid:c("uuidv4")(),b="rst-"+Date.now();i=b+"-"+a;return i}g.getSessionID=a}),98);
__d("ResponsiveTrackerMainThread",["FBLogger","RSTConfig","RSTEvents","RSTEventsMessageQueue","RSTEventsRegisterUseCasesMainThread","RSTIncidentLoggingMainThread","RSTIndexedDBSafe","RSTMetadataCollectorMainThread","RSTMetadataMainThread","RSTSessionID","RSTUtils","RSTUtilsMainThread"],(function(a,b,c,d,e,f,g){"use strict";c("RSTEventsRegisterUseCasesMainThread")();a=function(){function a(){this.$1=null}var b=a.prototype;b.init=function(a){this.$1=a,this.startLoggingUnresponsiveEvents(),this.startSendingHeartBeatToWorker(),this.startMonitorPageHiding(),d("RSTMetadataCollectorMainThread").start(),void c("RSTIndexedDBSafe").maybeClearObsoleteIncidents()};b.startLoggingUnresponsiveEvents=function(){var a=this;void this.logAndDeleteUnresponsiveEvents();window.setInterval(function(){void a.logAndDeleteUnresponsiveEvents()},c("RSTConfig").MAIN_THREAD_LOGGING_INTERVAL_MS)};b.startSendingHeartBeatToWorker=function(){var a=this;window.setInterval(function(){a.postEvent(d("RSTEvents").RSTEvent.HEART_BEAT)},c("RSTConfig").HEART_BEAT_INTERVAL_MS)};b.startMonitorPageHiding=function(){var a=this;document.addEventListener("visibilitychange",function(){document.hidden?a.postEvent(d("RSTEvents").RSTEvent.BROWSER_TAB_BACKGROUND):a.postEvent(d("RSTEvents").RSTEvent.BROWSER_TAB_FOREGROUND)})};b.postEvent=function(a,b){b===void 0&&(b=d("RSTMetadataMainThread").getMainThreadMetadata());b={type:c("RSTConfig").MESSAGE_TYPE,event:a,eventTime:Date.now(),DOMData:{hidden:document.hidden},sessionID:d("RSTSessionID").getSessionID(),metadata:b};d("RSTEventsMessageQueue").notify(a,{mainToWorkerMessage:b});(a=this.$1)==null?void 0:a.postMessage(b)};b.$2=function(a,b){if(a==null)return;var e=a.incidentID;if(d("RSTIncidentLoggingMainThread").getLoggedIncidentIDs().has(e)){b.add(e);return}var f=a.sessionID===d("RSTSessionID").getSessionID();if(f){a.hasRecovered=!0;d("RSTEventsMessageQueue").notify(d("RSTEvents").RSTEvent.RECOVERABLE_UNRESPONSIVENESS,{unresponsiveEventRecord:a});b.add(e);return}f=Date.now()-a.lastHeartBeatTimeStamp;if(f<c("RSTConfig").WAIT_MS_FOR_OTHER_SESSION_BEFORE_PROCESSING)return;if(f>c("RSTConfig").OBSOLETE_PENDING_INCIDENT_THRESHOLD_MS){if(d("RSTUtilsMainThread").shouldSkipProcessingIncident(e))return;d("RSTUtils").debugLogImportant("pending incident "+e+" cleaned");b.add(e);return}f=a.remoteLoggerSessionID==null||Date.now()-((f=a.remoteLoggerProposeTime)!=null?f:0)>c("RSTConfig").INCIDENT_PROPOSED_PROCESSOR_OWNERSHIP_EXPIRE_MS;if(f){void c("RSTIndexedDBSafe").updateIncidentInDB(function(a){if(a.incidentID!==e)return!1;d("RSTUtils").debugLogImportant("propose as logger for incident "+a.incidentID);a.remoteLoggerSessionID=d("RSTSessionID").getSessionID();a.remoteLoggerProposeTime=Date.now();return!0});return}a.remoteLoggerSessionID===d("RSTSessionID").getSessionID()&&(d("RSTUtils").debugLogImportant("act as logger for incident "+e),a.hasRecovered?(d("RSTEventsMessageQueue").notify(d("RSTEvents").RSTEvent.RECOVERABLE_UNRESPONSIVENESS,{unresponsiveEventRecord:a}),b.add(e)):a.unrecoverLastsForMs>c("RSTConfig").UNRECOVER_LASTS_THRESHOLD_MS&&(d("RSTEventsMessageQueue").notify(d("RSTEvents").RSTEvent.UNRECOVERABLE_UNRESPONSIVENESS,{unresponsiveEventRecord:a}),b.add(e)))};b.logAndDeleteUnresponsiveEvents=async function(){try{d("RSTEventsMessageQueue").notify(d("RSTEvents").RSTEvent.CHECKING_INDEXDB_FOR_EVENTS,{});var a=await c("RSTIndexedDBSafe").readEventsFromDB(),b=new Set();for(a of a)try{await d("RSTUtils").scheduleYield(),this.$2(a,b)}catch(a){var e;c("FBLogger")("responsive-tracker").catching(a).warn((e=a==null?void 0:a.message)!=null?e:"Failed to process incident")}await c("RSTIndexedDBSafe").clearIncidentFromDB(b)}catch(a){c("FBLogger")("responsive-tracker").catching(a).warn((e=a==null?void 0:a.message)!=null?e:"Failed to log incidents")}};return a}();b=new a();e=b;g.default=e}),98);
__d("RSTLogForCrash",["$InternalEnum","FBLogger","RSTEvents","RSTExternalLoggingDataStoreMainThread","ResponsiveTrackerMainThread","justknobx"],(function(a,b,c,d,e,f,g){var h=c("justknobx")._("3831"),i=b("$InternalEnum")({VALID_UNTIL_NEXT_HEARTBEAT:0,VALID_UNTIL_NEXT_WRITE:1,VALID_FOR_5_SECONDS:2});function a(a){switch(a){case i.VALID_UNTIL_NEXT_HEARTBEAT:return null;case i.VALID_UNTIL_NEXT_WRITE:return null;case i.VALID_FOR_5_SECONDS:return Date.now()+5*1e3}return null}var j=0,k=500;function e(a,b,e,f){e===void 0&&(e=i.VALID_UNTIL_NEXT_HEARTBEAT);f===void 0&&(f={});if(!h)return;b=typeof b==="object"?l(b):b.toString();if(b==null)return;d("RSTExternalLoggingDataStoreMainThread").loggingDataStore.log(a,b,e);a=Date.now();if(f.immediateFlush!==!0||a-j<k)return;j=a;c("ResponsiveTrackerMainThread").postEvent(d("RSTEvents").RSTEvent.LOG_FLUSH)}function l(a){try{return JSON.stringify(a)}catch(a){c("FBLogger")("responsive-tracker").mustfix("Failed to stringify object for RST logging");return null}}function f(a,b){return!h?-1:d("RSTExternalLoggingDataStoreMainThread").loggingDataStore.logSpanStart(a,b)}function m(a){a!==-1&&d("RSTExternalLoggingDataStoreMainThread").loggingDataStore.logSpanEnd(a)}g.RSTLogMode=i;g.getExpirationTime=a;g.logForCrash=e;g.logSpanStart=f;g.logSpanEnd=m}),98);
__d("RSTDebugConsoleMainThread",["RSTEvents","RSTEventsMessageQueue","RSTUtils"],(function(a,b,c,d,e,f,g){"use strict";function a(){}g.registerDebugConsole=a}),98);
__d("RSTEventsRegisterUseCasesMainThread",["RSTClearExternalLoggingDataStoreMainThread","RSTClearInteractionDataMainThread","RSTDebugConsoleMainThread","RSTIncidentLoggingMainThread"],(function(a,b,c,d,e,f,g){"use strict";function a(){d("RSTDebugConsoleMainThread").registerDebugConsole(),d("RSTIncidentLoggingMainThread").registerIncidentLogging(),d("RSTClearInteractionDataMainThread").registerClearInteractionData(),d("RSTClearExternalLoggingDataStoreMainThread").registerClearExternalLoggingData()}g["default"]=a}),98);
__d("RSTClearExternalLoggingDataStoreMainThread",["RSTEvents","RSTEventsMessageQueue","RSTExternalLoggingDataStoreMainThread"],(function(a,b,c,d,e,f,g){"use strict";function h(){d("RSTExternalLoggingDataStoreMainThread").loggingDataStore.clearNextHeartbeatStore()}function a(){d("RSTEventsMessageQueue").subscribe(d("RSTEvents").RSTEvent.HEART_BEAT,h),d("RSTEventsMessageQueue").subscribe(d("RSTEvents").RSTEvent.BROWSER_TAB_BACKGROUND,h),d("RSTEventsMessageQueue").subscribe(d("RSTEvents").RSTEvent.BROWSER_TAB_FOREGROUND,h)}g.registerClearExternalLoggingData=a}),98);
__d("RSTClearInteractionDataMainThread",["RSTEvents","RSTEventsMessageQueue","RSTInteractionDataMainThread"],(function(a,b,c,d,e,f,g){"use strict";function a(){d("RSTEventsMessageQueue").subscribe(d("RSTEvents").RSTEvent.HEART_BEAT,d("RSTInteractionDataMainThread").clearInteractionData),d("RSTEventsMessageQueue").subscribe(d("RSTEvents").RSTEvent.BROWSER_TAB_BACKGROUND,d("RSTInteractionDataMainThread").clearInteractionData),d("RSTEventsMessageQueue").subscribe(d("RSTEvents").RSTEvent.BROWSER_TAB_FOREGROUND,d("RSTInteractionDataMainThread").clearInteractionData)}g.registerClearInteractionData=a}),98);
__d("RSTInteractionTrackingMainThread",["RSTConfig","RSTEvents","RSTMetadataMainThread","ResponsiveTrackerMainThread","one-trace"],(function(a,b,c,d,e,f,g){"use strict";var h=new Set(["RESPONSIVENESS","INP","LONGTASK","UNKNOWN"]);function a(){if(!c("RSTConfig").SHOULD_CAPTURE_INTERACTION_DATA)return;c("one-trace").subscribe("trace-policy-set",function(a){if(h.has(a.traceType))return;var b=d("RSTMetadataMainThread").getMainThreadMetadata();c("ResponsiveTrackerMainThread").postEvent(d("RSTEvents").RSTEvent.TRACE_POLICY_SET,babelHelpers["extends"]({trace:babelHelpers["extends"]({},a)},b))});c("one-trace").subscribe("trace-end",function(a){if(h.has(a.traceType))return;var b=d("RSTMetadataMainThread").getMainThreadMetadata();c("ResponsiveTrackerMainThread").postEvent(d("RSTEvents").RSTEvent.TRACE_END,babelHelpers["extends"]({trace:babelHelpers["extends"]({},a,{endTimestamp:Date.now()})},b))})}g.start=a}),98);
__d("RSTRecoverableUnresponsivenessTrackingMainThread",["RSTConfig","one-trace","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Set(["INP","LONGTASK"]);function a(){c("one-trace").subscribe("trace-end",function(a){i.has(a.traceType)&&j.getInstance().addEvent(babelHelpers["extends"]({},a))})}function b(){return j.getInstance().getStats()}var j=function(){function a(){this.WINDOW_MS=c("RSTConfig").TRACE_STATS_WINDOW_SIZE_MS,this.MAX_EVENTS=c("RSTConfig").TRACE_STATS_MAX_QUEUE_SIZE,this.eventQueue=[]}a.getInstance=function(){a.instance||(a.instance=new a());return a.instance};var b=a.prototype;b.addEvent=function(a){if(a.startTime==null||a.endTime==null)return;a={traceType:a.traceType,startTime:a.startTime,duration:a.endTime-a.startTime,endTime:a.endTime};this.eventQueue.push(a);this.cleanupOldEvents();this.eventQueue.length>=this.MAX_EVENTS&&this.eventQueue.shift()};b.getStats=function(){this.cleanupOldEvents();var a={};this.eventQueue.forEach(function(b){b.traceType in a?(a[b.traceType].count++,a[b.traceType].totalDurationMs+=b.duration):a[b.traceType]={count:1,totalDurationMs:b.duration,averageDurationMs:0}});for(var b of Object.keys(a)){var c=a[b].totalDurationMs/a[b].count;a[b].averageDurationMs=Math.floor(c)}return a};b.cleanupOldEvents=function(){var a=(h||(h=c("performanceNow")))();while(this.eventQueue.length>0){var b=this.eventQueue[0];if(b.endTime>=a-this.WINDOW_MS)break;this.eventQueue.shift()}};return a}();j.instance=null;g.start=a;g.getStats=b}),98);
__d("RSTMetadataCollectorMainThread",["FBLogger","RSTInteractionTrackingMainThread","RSTRecoverableUnresponsivenessTrackingMainThread"],(function(a,b,c,d,e,f,g){"use strict";var h=!1;function a(){if(h)return;try{d("RSTInteractionTrackingMainThread").start(),d("RSTRecoverableUnresponsivenessTrackingMainThread").start(),h=!0}catch(a){c("FBLogger")("responsive-tracker").catching(a).warn("Failed to start RST metadata collector")}}g.start=a}),98);
__d("RSTExternalLoggingDataStoreMainThread",["FBLogger","RSTLogForCrash"],(function(a,b,c,d,e,f,g){var h=500,i=5e3,j=500;a=function(){function a(){this.$1=new Map(),this.$2=new Map(),this.$3=new Map(),this.$4=new Map()}var b=a.prototype;b.logSpanEnd=function(a){this.$4["delete"](a)};b.logSpanStart=function(b,d){d===void 0&&(d=null);if(b.length>h||d!=null&&d.length>i){c("FBLogger")("responsive-tracker").warn("Invalid key or annotation for external logging data (ignored), key: %s, annotation: %s",b,d);return-1}if(this.totalStoredPairCount()>j){c("FBLogger")("responsive-tracker").warn("Too many key-value pairs for external span logging data (ignored)");return-1}var e=a.curSpanId++;this.$4.set(e,{id:e,name:b,start:Date.now(),annotation:d});return e};b.log=function(a,b,e){if(a.length>h||b.length>i){c("FBLogger")("responsive-tracker").warn("Invalid key or value for external logging data (ignored), key: %s, value: %s",a,b);return!1}if(this.totalStoredPairCount()>j){c("FBLogger")("responsive-tracker").warn("Too many key-value pairs for external logging data (ignored)");return!1}var f=!1;if(e===d("RSTLogForCrash").RSTLogMode.VALID_UNTIL_NEXT_HEARTBEAT)f=this.$5(this.$2,a,b),this.$2.set(a,b);else if(e===d("RSTLogForCrash").RSTLogMode.VALID_UNTIL_NEXT_WRITE)f=this.$5(this.$1,a,b),this.$1.set(a,b);else if(e===d("RSTLogForCrash").RSTLogMode.VALID_FOR_5_SECONDS){f=!0;this.$3.set(a,{value:b,createTime:Date.now(),expirationTime:(a=d("RSTLogForCrash").getExpirationTime(e))!=null?a:Date.now()})}else c("FBLogger")("responsive-tracker").warn("Invalid logMode");return f};b.$5=function(a,b,c){return!a.has(b)||a.get(b)!==c};b.totalStoredPairCount=function(){return this.$1.size+this.$2.size+this.$3.size+this.$4.size};b.get=function(a){if(this.$2.has(a))return this.$2.get(a)||null;if(this.$1.has(a))return this.$1.get(a)||null;var b=this.$3.get(a);if(b!=null)if(Date.now()<b.expirationTime)return b.value;else this.$3["delete"](a);return null};b.clear=function(){this.$1.clear(),this.clearNextHeartbeatStore()};b.clearNextHeartbeatStore=function(){this.$2.clear()};b.clearSpanStore=function(){this.$4.clear()};b.getAllSpanData=function(){var a={};this.$4.forEach(function(b,c){a[""+c]=b});return a};b.$6=function(){var a=Date.now();for(var b of this.$3){var c=b[0],d=b[1];a>=d.expirationTime&&this.$3["delete"](c)}};b.getTimedStoreData=function(){this.$6();return Object.fromEntries(Array.from(this.$3.entries()).map(function(a){var b=a[0];a=a[1];return[b,a.value]}))};b.getAll=function(){return babelHelpers["extends"]({},this.getTimedStoreData(),{},Object.fromEntries(this.$1),{},Object.fromEntries(this.$2))};return a}();a.curSpanId=0;b=new a();g.loggingDataStore=b}),98);
__d("RSTInteractionDataMainThread",["ALChannel","AdsALReactUtils","FBLogger","RSTConfig","RSTEvents","RSTMetadataMainThread","RSTUtilsMainThread","ResponsiveTrackerMainThread","getReactComponentStackFromDOMElement_THIS_CAN_BREAK"],(function(a,b,c,d,e,f,g){"use strict";var h=5,i=[],j=100,k=10,l=[];function m(a){i.push(a),i.length>h&&i.shift(),l.push(a),l.length>j&&l.shift()}function a(){i.length=0}function b(){var a=i;return a[a.length-1]}function e(){return[].concat(i)}function f(a){a===void 0&&(a=k);return l.slice(-1*a)}function n(a,b){a=a!=null?a:{};var e=a.name,f=a.stack;a=a.context;(e!=null||f!=null)&&m({componentName:e,componentStack:f,componentContext:a,eventType:"click",eventTime:Date.now(),componentText:b});try{e=d("RSTMetadataMainThread").getMainThreadMetadata();c("ResponsiveTrackerMainThread").postEvent(d("RSTEvents").RSTEvent.INTERACTION_DATA,babelHelpers["extends"]({},e))}catch(a){c("FBLogger")("responsive-tracker").catching(a).mustfix("Failed to send interaction data to worker")}}o();function o(){c("RSTConfig").SHOULD_CAPTURE_INTERACTION_DATA&&(d("RSTUtilsMainThread").isMonetizationApp?q():p())}function p(){window.addEventListener("click",function(a){a=a.target;if(!(a instanceof Element))return;try{var b=c("getReactComponentStackFromDOMElement_THIS_CAN_BREAK")(a);b={name:d("RSTUtilsMainThread").getAllowListedComponentName(b),stack:b!=null?b:[],context:d("RSTUtilsMainThread").getReactComponentContext(b,c("RSTConfig").isReactComponentNameBlocked)};a=r(a,b);n(b,a)}catch(a){c("FBLogger")("responsive-tracker").catching(a).mustfix("Failed to get react component stack from Shim")}},!0)}function q(){c("ALChannel").addListener("al_ui_event_capture",function(a){if(a.event!=="click")return;try{var b;b={name:a.reactComponentName,stack:(b=a.reactComponentStack)!=null?b:[],context:d("RSTUtilsMainThread").getReactComponentContext(a.reactComponentStack,d("AdsALReactUtils").isComponentNameBlocked)};a=a.targetElement;a=r(a,b);n(b,a)}catch(a){c("FBLogger")("responsive-tracker").catching(a).mustfix("Failed to get react component stack from AL")}})}function r(a,b){if(!c("RSTConfig").SHOULD_CAPTURE_INTERACTION_ELEMENT_TEXT)return null;if((b==null?void 0:b.stack)==null)return null;for(b of b.stack)if(c("RSTConfig").isReactComponentTextBlocked(b))return null;a=(b=a==null?void 0:a.textContent)!=null?b:null;if(typeof a==="string"){a=a.trim();b=c("RSTConfig").ELEMENT_TEXT_SIZE_LIMIT;a.length>b&&(a=a.substr(0,b)+("... ("+(a.length-b)+" code units omitted)"))}return a===""?null:a}g.clearInteractionData=a;g.getLastInteractionDataSinceLastMessageToWorker=b;g.getInteractionDataSinceLastMessageToWorker=e;g.getInteractionHistory=f}),98);
__d("RSTMetadataMainThread",["CurrentUser","Env","MemoryUtils","RSTAdsManagerUtilsMainThread","RSTConfig","RSTExternalLoggingDataStoreMainThread","RSTInteractionDataMainThread","RSTRecoverableUnresponsivenessTrackingMainThread","RSTUtils","SiteData","performance"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(){var a=Date.now(),b=babelHelpers["extends"]({},d("RSTAdsManagerUtilsMainThread").getAdsAnnotations());l(b);var c=Date.now();b.rstAnnotationsCollectedInMs=""+(c-a);return b}function k(){return c("RSTConfig").shouldCollectAdditionalMetadata()?d("RSTExternalLoggingDataStoreMainThread").loggingDataStore.getAllSpanData():{}}function l(a){c("RSTConfig").shouldCollectAdditionalMetadata()&&(p(a),s(a),t(a),r(a),m(a),q(a))}function m(a){var b=d("RSTAdsManagerUtilsMainThread").getAdEntityIds();a.selectedAdIds=b.adIds.join(",");a.selectedAdSetIds=b.adsetIds.join(",");a.selectedCampaignIds=b.campaignIds.join(",")}function n(){try{var a;a=(a=d("RSTInteractionDataMainThread").getLastInteractionDataSinceLastMessageToWorker())==null?void 0:a.componentStack;return a!=null?a:[]}catch(a){return[]}}function o(){try{var a;a=(a=d("RSTInteractionDataMainThread").getLastInteractionDataSinceLastMessageToWorker())==null?void 0:a.componentContext;return a!=null?a:[]}catch(a){return[]}}function p(a){var b=d("RSTInteractionDataMainThread").getInteractionDataSinceLastMessageToWorker().map(function(a){return a.componentName}).filter(function(a){return a!=null}).join(",");b!==""&&(a.interactedComponentsSinceLastResponsiveState=b);b=o();b.length>0&&(a.interactedComponentContextSinceLastResponsiveState=b.join(" < "));b=d("RSTInteractionDataMainThread").getInteractionHistory().map(function(a){return a.componentName}).filter(function(a){return a!=null}).join(",");b!==""&&(a.interactedComponentHistory=b);b=d("RSTInteractionDataMainThread").getInteractionHistory().map(function(a){return d("RSTUtils").getInteractionDetail(a)}).filter(function(a){return a!=null}).join(",");b!==""&&(a.interactionDetailHistory=b)}function q(a){a.crashDataOncall=c("RSTConfig").PROJECT_ONCALL,a.crashLogger=c("RSTConfig").PROJECT_NAME}function r(a){var b=d("RSTExternalLoggingDataStoreMainThread").loggingDataStore.getAll();for(var c of Object.keys(b))a["@"+c]=b[c]}function s(a){var b=d("MemoryUtils").getCurrentMemory({runGC:!1}),c=b.jsHeapSizeLimit,e=b.usedJSHeapSize;b=b.totalJSHeapSize;c!=null&&(a.jsHeapSizeLimit=c.toString());e!=null&&(a.usedJSHeapSize=e.toString());b!=null&&(a.totalJSHeapSize=b.toString())}function t(a){var b=d("RSTRecoverableUnresponsivenessTrackingMainThread").getStats();if(b==null)return;for(var c of Object.keys(b)){var e=b[c];if(e==null)continue;var f=c.toLowerCase();for(var g of Object.keys(e)){var h=e[g];if(h==null)continue;a[f+"-"+g]=h.toString()}}}function a(){var a=document.URL,b=(h||(h=c("Env"))).brsid;try{var e;return{url:a,brsid:b,i1:(e=c("CurrentUser").getID())!=null?e:"0",i2:(e=d("RSTAdsManagerUtilsMainThread").getAdAccountID())!=null?e:"0",i3:(e=d("RSTAdsManagerUtilsMainThread").getBusinessID())!=null?e:"0",cpp:(e=c("SiteData").push_phase)!=null?e:"unknown",cr:""+c("SiteData").client_revision,rcs:n(),st:Math.floor(Date.now()-(i||(i=c("performance"))).now()),annotations:j(),spans:k()}}catch(c){return{url:a,brsid:b,i1:"0",i2:"0",i3:"0"}}}g.getMainThreadMetadata=a}),98);
__d("ReactFeatureFlags",["gkx","qex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;a=c("gkx")("11557");b=c("gkx")("11685");d=!0;e=c("gkx")("10839");f=c("gkx")("21072")||((f=c("qex")._("104"))!=null?f:!1);h=(h=c("qex")._("128"))!=null?h:250;i=(i=c("qex")._("344"))!=null?i:5e3;j=(j=c("qex")._("388"))!=null?j:5e3;var k=!1,l=!1,m=c("gkx")("21063"),n=c("gkx")("9861"),o=!0,p=c("gkx")("2815"),q=c("gkx")("10850"),r=!1,s=!1,t=c("gkx")("11370"),u=!0,v=c("gkx")("33056"),w=!1;c=!q&&c("gkx")("21069")||c("gkx")("10211");g.alwaysThrottleRetries=a;g.enableNoCloningMemoCache=b;g.enableObjectFiber=d;g.enableHiddenSubtreeInsertionEffectCleanup=e;g.enableRetryLaneExpiration=f;g.syncLaneExpirationMs=h;g.transitionLaneExpirationMs=i;g.retryLaneExpirationMs=j;g.enableScrollEndPolyfill=k;g.enableInfiniteRenderLoopDetection=l;g.enableTrustedTypesIntegration=m;g.enableRenderableContext=n;g.enableFragmentRefs=o;g.enableViewTransition=p;g.enableComponentPerformanceTrack=q;g.enableTransitionTracing=r;g.renameElementSymbol=s;g.disableDefaultPropsExceptForClasses=t;g.enableDO_NOT_USE_disableStrictPassiveEffect=u;g.favorSafetyOverHydrationPerf=v;g.disableSchedulerTimeoutInWorkLoop=w;g.enableSchedulingProfiler=c}),98);
__d("React-prod.classic",["ReactFeatureFlags"],(function(a,b,c,d,e,f){"use strict";var g=(b=b("ReactFeatureFlags")).disableDefaultPropsExceptForClasses,h=b.enableRenderableContext,i=b.enableTransitionTracing,j=b.renameElementSymbol,k=b.enableViewTransition;b=Symbol["for"]("react.element");var l=j?Symbol["for"]("react.transitional.element"):b,m=Symbol["for"]("react.portal");j=Symbol["for"]("react.fragment");b=Symbol["for"]("react.strict_mode");var n=Symbol["for"]("react.profiler"),o=Symbol["for"]("react.provider"),p=Symbol["for"]("react.consumer"),q=Symbol["for"]("react.context"),r=Symbol["for"]("react.forward_ref"),s=Symbol["for"]("react.suspense"),t=Symbol["for"]("react.suspense_list"),u=Symbol["for"]("react.memo"),v=Symbol["for"]("react.lazy"),w=Symbol["for"]("react.scope"),x=Symbol["for"]("react.activity"),y=Symbol["for"]("react.legacy_hidden"),z=Symbol["for"]("react.tracing_marker"),A=Symbol["for"]("react.view_transition"),B=typeof Symbol==="function"?Symbol.iterator:"@@iterator";function C(a){if(null===a||"object"!==typeof a)return null;a=B&&a[B]||a["@@iterator"];return"function"===typeof a?a:null}var D={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},E=Object.assign,F={};function a(a,b,c){this.props=a,this.context=b,this.refs=F,this.updater=c||D}a.prototype.isReactComponent={};a.prototype.setState=function(a,b){if("object"!==typeof a&&"function"!==typeof a&&null!=a)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,a,b,"setState")};a.prototype.forceUpdate=function(a){this.updater.enqueueForceUpdate(this,a,"forceUpdate")};function c(){}c.prototype=a.prototype;function d(a,b,c){this.props=a,this.context=b,this.refs=F,this.updater=c||D}c=d.prototype=new c();c.constructor=d;E(c,a.prototype);c.isPureReactComponent=!0;var G=Array.isArray;function H(){}var I={H:null,A:null,T:null,S:null},J=Object.prototype.hasOwnProperty;function K(a,b,c,d,e,f){c=f.ref;return{$$typeof:l,type:a,key:b,ref:void 0!==c?c:null,props:f}}function e(a,b,c){var d=null;void 0!==c&&(d=""+c);void 0!==b.key&&(d=""+b.key);if("key"in b){c={};for(var e in b)"key"!==e&&(c[e]=b[e])}else c=b;if(!g&&a&&a.defaultProps){b=a.defaultProps;for(e in b)void 0===c[e]&&(c[e]=b[e])}return K(a,d,void 0,void 0,null,c)}function L(a,b){return K(a.type,b,void 0,void 0,void 0,a.props)}function M(a){return"object"===typeof a&&null!==a&&a.$$typeof===l}function N(a){var b={"=":"=0",":":"=2"};return"$"+a.replace(/[=:]/g,function(a){return b[a]})}var O=/\/+/g;function P(a,b){return"object"===typeof a&&null!==a&&null!=a.key?N(""+a.key):b.toString(36)}function Q(a){switch(a.status){case"fulfilled":return a.value;case"rejected":throw a.reason;default:switch("string"===typeof a.status?a.then(H,H):(a.status="pending",a.then(function(b){"pending"===a.status&&(a.status="fulfilled",a.value=b)},function(b){"pending"===a.status&&(a.status="rejected",a.reason=b)})),a.status){case"fulfilled":return a.value;case"rejected":throw a.reason}}throw a}function R(a,b,c,d,e){var f=typeof a;("undefined"===f||"boolean"===f)&&(a=null);var g=!1;if(null===a)g=!0;else switch(f){case"bigint":case"string":case"number":g=!0;break;case"object":switch(a.$$typeof){case l:case m:g=!0;break;case v:return g=a._init,R(g(a._payload),b,c,d,e)}}if(g)return e=e(a),g=""===d?"."+P(a,0):d,G(e)?(c="",null!=g&&(c=g.replace(O,"$&/")+"/"),R(e,b,c,"",function(a){return a})):null!=e&&(M(e)&&(e=L(e,c+(null==e.key||a&&a.key===e.key?"":(""+e.key).replace(O,"$&/")+"/")+g)),b.push(e)),1;g=0;var h=""===d?".":d+":";if(G(a))for(var i=0;i<a.length;i++)d=a[i],f=h+P(d,i),g+=R(d,b,c,f,e);else if(i=C(a),"function"===typeof i)for(a=i.call(a),i=0;!(d=a.next()).done;)d=d.value,f=h+P(d,i++),g+=R(d,b,c,f,e);else if("object"===f){if("function"===typeof a.then)return R(Q(a),b,c,d,e);b=String(a);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===b?"object with keys {"+Object.keys(a).join(", ")+"}":b)+"). If you meant to render a collection of children, use an array instead.")}return g}function S(a,b,c){if(null==a)return a;var d=[],e=0;R(a,d,"","",function(a){return b.call(c,a,e++)});return d}function T(a){if(-1===a._status){var b=a._result;b=b();b.then(function(b){(0===a._status||-1===a._status)&&(a._status=1,a._result=b)},function(b){(0===a._status||-1===a._status)&&(a._status=2,a._result=b)});-1===a._status&&(a._status=0,a._result=b)}if(1===a._status)return a._result["default"];throw a._result}function U(a){return I.H.useMemoCache(a)}var V="function"===typeof reportError?reportError:function(a){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var b=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof a&&null!==a&&"string"===typeof a.message?String(a.message):String(a),error:a});if(!window.dispatchEvent(b))return}else if("object"===typeof process&&"function"===typeof process.emit){process.emit("uncaughtException",a);return}};function W(a,b){var c=I.T,d={};k&&(d.types=null!==c?c.types:null);i&&(d.name=void 0!==b&&void 0!==b.name?b.name:null,d.startTime=-1);I.T=d;try{b=a();a=I.S;null!==a&&a(d,b);"object"===typeof b&&null!==b&&"function"===typeof b.then&&b.then(H,V)}catch(a){V(a)}finally{null!==c&&null!==d.types&&(c.types=d.types),I.T=c}}function X(a){if(k){var b=I.T;if(null!==b){var c=b.types;null===c?b.types=[a]:-1===c.indexOf(a)&&c.push(a)}else W(X.bind(null,a))}}c={__proto__:null,c:U};f.Children={map:S,forEach:function(a,b,c){S(a,function(){b.apply(this,arguments)},c)},count:function(a){var b=0;S(a,function(){b++});return b},toArray:function(a){return S(a,function(a){return a})||[]},only:function(a){if(!M(a))throw Error("React.Children.only expected to receive a single React element child.");return a}};f.Component=a;f.Fragment=j;f.Profiler=n;f.PureComponent=d;f.StrictMode=b;f.Suspense=s;f.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=I;f.__COMPILER_RUNTIME=c;f.act=function(){throw Error("act(...) is not supported in production builds of React.")};f.c=U;f.cache=function(a){return function(){return a.apply(null,arguments)}};f.captureOwnerStack=void 0;f.cloneElement=function(a,b,c){if(null===a||void 0===a)throw Error("The argument must be a React element, but you passed "+a+".");var d=E({},a.props),e=a.key,f=void 0;if(null!=b){void 0!==b.ref&&(f=void 0);void 0!==b.key&&(e=""+b.key);if(!g&&a.type&&a.type.defaultProps)var h=a.type.defaultProps;for(i in b)!J.call(b,i)||"key"===i||"__self"===i||"__source"===i||"ref"===i&&void 0===b.ref||(d[i]=g||void 0!==b[i]||void 0===h?b[i]:h[i])}var i=arguments.length-2;if(1===i)d.children=c;else if(1<i){h=Array(i);for(var j=0;j<i;j++)h[j]=arguments[j+2];d.children=h}return K(a.type,e,void 0,void 0,f,d)};f.createContext=function(a){a={$$typeof:q,_currentValue:a,_currentValue2:a,_threadCount:0,Provider:null,Consumer:null};h?(a.Provider=a,a.Consumer={$$typeof:p,_context:a}):(a.Provider={$$typeof:o,_context:a},a.Consumer=a);return a};f.createElement=function(a,b,c){var d,e={},f=null;if(null!=b)for(d in void 0!==b.key&&(f=""+b.key),b)J.call(b,d)&&"key"!==d&&"__self"!==d&&"__source"!==d&&(e[d]=b[d]);var g=arguments.length-2;if(1===g)e.children=c;else if(1<g){for(var h=Array(g),i=0;i<g;i++)h[i]=arguments[i+2];e.children=h}if(a&&a.defaultProps)for(d in g=a.defaultProps,g)void 0===e[d]&&(e[d]=g[d]);return K(a,f,void 0,void 0,null,e)};f.createRef=function(){return{current:null}};f.experimental_useEffectEvent=function(a){return I.H.useEffectEvent(a)};f.forwardRef=function(a){return{$$typeof:r,render:a}};f.isValidElement=M;f.jsx=e;f.jsxDEV=void 0;f.jsxs=e;f.lazy=function(a){return{$$typeof:v,_payload:{_status:-1,_result:a},_init:T}};f.memo=function(a,b){return{$$typeof:u,type:a,compare:void 0===b?null:b}};f.startTransition=W;f.unstable_Activity=x;f.unstable_LegacyHidden=y;f.unstable_Scope=w;f.unstable_SuspenseList=t;f.unstable_TracingMarker=z;f.unstable_ViewTransition=A;f.unstable_addTransitionType=X;f.unstable_getCacheForType=function(a){var b=I.A;return b?b.getCacheForType(a):a()};f.unstable_useCacheRefresh=function(){return I.H.useCacheRefresh()};f.unstable_useMemoCache=U;f.use=function(a){return I.H.use(a)};f.useActionState=function(a,b,c){return I.H.useActionState(a,b,c)};f.useCallback=function(a,b){return I.H.useCallback(a,b)};f.useContext=function(a){return I.H.useContext(a)};f.useDebugValue=function(){};f.useDeferredValue=function(a,b){return I.H.useDeferredValue(a,b)};f.useEffect=function(a,b){return I.H.useEffect(a,b)};f.useId=function(){return I.H.useId()};f.useImperativeHandle=function(a,b,c){return I.H.useImperativeHandle(a,b,c)};f.useInsertionEffect=function(a,b){return I.H.useInsertionEffect(a,b)};f.useLayoutEffect=function(a,b){return I.H.useLayoutEffect(a,b)};f.useMemo=function(a,b){return I.H.useMemo(a,b)};f.useOptimistic=function(a,b){return I.H.useOptimistic(a,b)};f.useReducer=function(a,b,c){return I.H.useReducer(a,b,c)};f.useRef=function(a){return I.H.useRef(a)};f.useState=function(a){return I.H.useState(a)};f.useSyncExternalStore=function(a,b,c){return I.H.useSyncExternalStore(a,b,c)};f.useTransition=function(){return I.H.useTransition()};f.version="19.2.0-www-classic-c38e2689-20250609"}),null);
__d("React.classic",["cr:1292365"],(function(a,b,c,d,e,f){e.exports=b("cr:1292365")}),null);
__d("ReactDOM.classic",["cr:13683","cr:5277"],(function(a,b,c,d,e,f){b("cr:13683")&&b("cr:13683")(),e.exports=b("cr:5277")}),null);
__d("ReactDOM.classic.prod-or-profiling",["cr:5278"],(function(a,b,c,d,e,f){e.exports=b("cr:5278")}),null);
__d("ReactDOMCompatibilityLayer",["ReactDOM"],(function(a,b,c,d,e,f,g){"use strict";var h=typeof WeakMap==="function"?new WeakMap():new Map();function a(a,b){var c=h.get(b);c==null&&(c=d("ReactDOM").createRoot(b),h.set(b,c));b=null;var e=a.props.ref;d("ReactDOM").flushSync(function(){var d;return(d=c)==null?void 0:d.render(typeof a.type==="string"||((d=a.type)==null?void 0:(d=d.prototype)==null?void 0:d.isReactComponent)?babelHelpers["extends"]({},a,{props:babelHelpers["extends"]({},a.props,{ref:function(a){typeof e==="function"?e(a):e!=null&&(e.current=a),b=a}})}):a)});return b}function b(a){if(a==null)return!1;var b=h.get(a);if(b){d("ReactDOM").flushSync(function(){b.unmount()});h["delete"](a);return!0}return!1}g.render_DEPRECATED=a;g.unmountComponentAtNode_DEPRECATED=b}),98);
__d("ReactFiberErrorDialog",["cr:8909"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return b("cr:8909").showErrorDialog(a)}g.showErrorDialog=a}),98);
__d("ReactFiberErrorDialogWWW",["ErrorNormalizeUtils","ErrorPubSub","LogHistory","getErrorSafe"],(function(a,b,c,d,e,f){"use strict";var g;function a(a){var c=a.componentStack,d=a.errorBoundary,e=b("getErrorSafe")(a.error);e.componentStack=a.componentStack;e.loggingSource="REACT_FIBER";if(d!=null&&d.suppressReactDefaultErrorLoggingIUnderstandThisWillMakeBugsHarderToFindAndFix)return!1;a=b("LogHistory").getInstance("react_fiber_error_logger");a.error("capturedError",JSON.stringify({componentStack:c,error:{name:e.name,message:e.message,stack:e.stack}}));d=b("ErrorNormalizeUtils").normalizeError(e);(g||(g=b("ErrorPubSub"))).reportNormalizedError(d);return!1}e.exports={showErrorDialog:a}}),null);
__d("TransAppInlineMode",[],(function(a,b,c,d,e,f){a=Object.freeze({STRING_MANAGER:"STRING_MANAGER",TRANSLATION:"TRANSLATION",APPROVE:"APPROVE",REPORT:"REPORT",NO_INLINE:"NO_INLINE"});f["default"]=a}),66);
__d("WebView.react",["LegacyHidden","react","stylex","testID"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={hidden:{display:"x1s85apg",$$css:!0},root:{boxSizing:"x9f619",position:"x1n2onr6",zIndex:"x1ja2u2z",$$css:!0}};function a(a){var b=a.ref;a=babelHelpers.objectWithoutPropertiesLoose(a,["ref"]);var d=a.children,e=a.suppressHydrationWarning,f=a.testid,g=a.xstyle;a=babelHelpers.objectWithoutPropertiesLoose(a,["children","suppressHydrationWarning","testid","xstyle"]);var i=a.hidden===!0;return j.jsx(c("LegacyHidden"),{htmlAttributes:babelHelpers["extends"]({},a,{},c("testID")(f),{},(h||(h=c("stylex"))).props(k.root,g,i&&k.hidden)),mode:i?"hidden":"visible",ref:b,suppressHydrationWarning:e,children:d})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("cancelIdleCallbackComet",["IdleCallbackImplementation"],(function(a,b,c,d,e,f,g){"use strict";var h=a.cancelIdleCallback||d("IdleCallbackImplementation").cancelIdleCallback;function b(b){h.call(a,b)}g["default"]=b}),98);
__d("cancelIdleCallbackWWW",["cr:692209"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:692209")}),98);
__d("getUnwrappedFbt",["FbtResultGK"],(function(a,b,c,d,e,f){function a(a){a=a.contents;var c=b("FbtResultGK").inlineMode,d=b("FbtResultGK").shouldReturnFbtResult;if(!d&&c!=="REPORT")return(a==null?void 0:a.length)===1&&typeof a[0]==="string"?a[0]:a}e.exports=a}),null);
__d("getFbtResult",["FbtResult","FbtResultGK","InlineFbtResult","getUnwrappedFbt","gkx","recoverableViolation"],(function(a,b,c,d,e,f,g){c("gkx")("20935")&&c("FbtResultGK").inlineMode==="TRANSLATION"&&c("recoverableViolation")("TransAppInlineMode=TRANSLATION should not happen on Comet yet. "+("[inlineMode="+(c("FbtResultGK").inlineMode!=null?c("FbtResultGK").inlineMode:"")+"]")+("[runtime_site_is_comet="+String(c("gkx")("20935"))+"]"),"internationalization");function a(a){var b=c("getUnwrappedFbt")(a);if(b!=null)return b;b=a.contents;var d=a.patternString,e=a.patternHash;return c("FbtResultGK").inlineMode!=null&&c("FbtResultGK").inlineMode!=="NO_INLINE"?new(c("InlineFbtResult"))(b,c("FbtResultGK").inlineMode,d,e):c("FbtResult").get(a)}g["default"]=a}),98);
__d("scheduler",["SchedulerFb-Internals_DO_NOT_USE"],(function(a,b,c,d,e,f){"use strict";e.exports=b("SchedulerFb-Internals_DO_NOT_USE")}),null);
__d("setTimeoutCometLoggingPriWWW",["JSScheduler","setTimeoutCometInternals"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){for(var c=arguments.length,e=new Array(c>2?c-2:0),f=2;f<c;f++)e[f-2]=arguments[f];return d("setTimeoutCometInternals").setTimeoutAtPriority_DO_NOT_USE.apply(d("setTimeoutCometInternals"),[(h||(h=d("JSScheduler"))).priorities.unstable_Low,a,b].concat(e))}g["default"]=a}),98);
__d("setTimeoutCometSpeculative",["JSScheduler","setTimeoutCometInternals"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){for(var c=arguments.length,e=new Array(c>2?c-2:0),f=2;f<c;f++)e[f-2]=arguments[f];return d("setTimeoutCometInternals").setTimeoutAtPriority_DO_NOT_USE.apply(d("setTimeoutCometInternals"),[(h||(h=d("JSScheduler"))).priorities.unstable_Idle,a,b].concat(e))}g["default"]=a}),98);
__d("useHeroBootloadedComponent",["CometHeroInteractionContext","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useContext,j=b.useEffect;function a(a){var b=d("react-compiler-runtime").c(4),e=i(c("CometHeroInteractionContext").Context),f,g;b[0]!==e||b[1]!==a?(f=function(){e.consumeBootload(a.getModuleId())},g=[e,a],b[0]=e,b[1]=a,b[2]=f,b[3]=g):(f=b[2],g=b[3]);j(f,g)}g["default"]=a}),98);
__d("warningComet",["SiteData","cr:1072546","cr:1072547","cr:1072549","cr:983844","err","fb-error"],(function(a,b,c,d,e,f,g){function a(a,b){return!1}b=a;c=b;g["default"]=c}),98);
__d("warningWWW",["WebDriverConfig","cr:1105154","cr:11202","cr:2682"],(function(a,b,c,d,e,f,g){a=b("cr:2682");c=a;g["default"]=c}),98);