;/*FB_PKG_DELIM*/

__d("ActorHovercard.react",["cr:7663"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:7663")}),98);
__d("BDHeaderConfig",[],(function(a,b,c,d,e,f){"use strict";a="359341";f.ASBD_ID=a}),66);
__d("Banzai",["cr:7383"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:7383")}),98);
__d("BanzaiConsts",[],(function(a,b,c,d,e,f){a={SEND:"Banzai:SEND",OK:"Banzai:OK",ERROR:"Banzai:ERROR",SHUTDOWN:"Banzai:SHUTDOWN",BASIC:"basic",VITAL:"vital",BASIC_WAIT:6e4,BASIC_WAIT_COMET:2e3,VITAL_WAIT:1e3,BATCH_SIZE_LIMIT:64e3,EXPIRY:864e5,BATCH_TIMEOUT:1e4,LAST_STORAGE_FLUSH:"banzai:last_storage_flush",STORAGE_FLUSH_INTERVAL:12*60*6e4,ENSURE_LAZY_QUEUE_FLUSH_TIMEOUT:3e4,POST_READY:0,POST_INFLIGHT:1,POST_SENT:2};b=a;f["default"]=b}),66);
__d("BanzaiLogger",["cr:9989"],(function(a,b,c,d,e,f,g){function h(a){return{log:function(c,d){b("cr:9989").post("logger:"+c,d,a)},create:h}}a=h();c=a;g["default"]=c}),98);
__d("Base64",[],(function(a,b,c,d,e,f){var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function h(a){a=a.charCodeAt(0)<<16|a.charCodeAt(1)<<8|a.charCodeAt(2);return String.fromCharCode(g.charCodeAt(a>>>18),g.charCodeAt(a>>>12&63),g.charCodeAt(a>>>6&63),g.charCodeAt(a&63))}var i=">___?456789:;<=_______\0\x01\x02\x03\x04\x05\x06\x07\b\t\n\v\f\r\x0e\x0f\x10\x11\x12\x13\x14\x15\x16\x17\x18\x19______\x1a\x1b\x1c\x1d\x1e\x1f !\"#$%&'()*+,-./0123";function j(a){a=i.charCodeAt(a.charCodeAt(0)-43)<<18|i.charCodeAt(a.charCodeAt(1)-43)<<12|i.charCodeAt(a.charCodeAt(2)-43)<<6|i.charCodeAt(a.charCodeAt(3)-43);return String.fromCharCode(a>>>16,a>>>8&255,a&255)}var k={encode:function(a){a=unescape(encodeURI(a));var b=(a.length+2)%3;a=(a+"\0\0".slice(b)).replace(/[\s\S]{3}/g,h);return a.slice(0,a.length+b-2)+"==".slice(b)},decode:function(a){a=a.replace(/[^A-Za-z0-9+\/]/g,"");var b=a.length+3&3;a=(a+"AAA".slice(b)).replace(/..../g,j);a=a.slice(0,a.length+b-3);try{return decodeURIComponent(escape(a))}catch(a){throw new Error("Not valid UTF-8")}},encodeObject:function(a){return k.encode(JSON.stringify(a))},decodeObject:function(a){return JSON.parse(k.decode(a))},encodeNums:function(a){return String.fromCharCode.apply(String,a.map(function(a){return g.charCodeAt((a|-(a>63?1:0))&-(a>0?1:0)&63)}))}};a=k;f["default"]=a}),66);
__d("react-compiler-runtime",[],(function(a,b,c,d,e,f){"use strict";var g=null;function a(a){return g.H.useMemoCache(a)}function b(a){g=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE}f.c=a;f.initReactCompilerRuntime=b}),66);
__d("CometAppKey",[],(function(a,b,c,d,e,f){a=Object.freeze({NONE:0,GENERIC_COMET_DO_NOT_USE:1,COMET_ON_MOBILE:2,COMET_ON_INSTAGRAM_DO_NOT_USE:3,FB_ACCOUNTS_CENTER:5,CANVAS:6,IG_WWW:7,FRL_ACCOUNTS_CENTER:8,NOVI_CHECKOUT:9,ENTERPRISE_CENTER:10,BIZ_WEB:11,BUSINESS_FB:12,HORIZON_WORLDS:14,FB_WEB:15,WHATSAPP:17,META_DOT_COM:18,OCULUS_DOT_COM:19,FRL_FAMILY_CENTER:20,WHATSAPP_FAQ:23,IG_ACCOUNTS_CENTER:24,ADS_MANAGER_ON_BLUE:25,MESSENGER_FAMILY_CENTER:26,META_WORK_PORTFOLIO:27,BARCELONA_WEB:29,FB_FAMILY_CENTER:30,CANDIDATE_PORTAL:31,META_HELP:32,FRL_AUTH:33,META_LLAMA:34,IG_GEN_AI_STUDIO:35,FB_GEN_AI_STUDIO:36,IG_FAMILY_CENTER:37,IG_PRIVACY_CENTER:38,IG_HELP_CENTER:39,ABOUT_META:40,IG_GEN_AI_IMAGINE:41,FB_GEN_AI_IMAGINE:42,INTERNALFB:43,COMMERCE_MANAGER:44,QUEST_DEV_CENTER:45,ABRA:46,META_BUG_BOUNTY:47,CTRL_VERSE_DATA_COLLECTION:48,META_CONTENT_LIBRARY_UI:49,SUPPORT_PORTAL:50,MSE_RATING_TOOL:51,MEDIA_PORTAL:52,COMMERCE_PERMISSION_WIZARD:53,SA_DEMO_BOOKING:55,COMMERCE_EXTENSION:56,FB_PRIVACY_CENTER:57,ADS_MANAGER_ON_COMET:58,FB_HELP_CENTER:59,MONETIZATION_DOGFOODING:61,AI_DEMOS:62,DEVELOPER_PLATFORM:63,PARTNERSHIP_ADS_HUB:64,INSTAGRAM_ABOUT:65,TRANSPARENCY:66,BUSINESS_USER_PROFILE_MANAGED_ACCOUNT:67,WHATSAPP_FLOWS:68,GENERATIVE_OFFENSIVE_ASSESSMENT_TOOL:69,SHOPIFY_APP:70,LLAMA_DEV_CENTER:71,KADABRA:72,THREADS_FAMILY_CENTER:73,PAYMENTS:74,HORIZON_MANAGED_SOLUTIONS:75,TEST_COMET:76,WHATSAPP_MINIAPPS:77,META_ACCOUNT_AUTH:78,DEV_HUB:79,THREADS_ACCOUNTS_CENTER:80,SHOPIFY_WHATSAPP_APP:81,WILDFLOWER:82,FAMILY_CENTER_MARKETING:83,CONSENT_TEMPLATES:84,FB_COMMUNITY_NOTES:85,WEARABLES_DEV_CENTER:86,AI_META:87,EMPLOYEE_HELPDESK:88,RESEARCH_TOOLS_MANAGER:89,SOCIAL_PLUGIN:90,INSTAGRAM_AIRWAVE:91});f["default"]=a}),66);
__d("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a}f["default"]=a}),66);
__d("react-forget-runtime",["invariant","CometAppKey","DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE","FBLogger","SiteData","err","gkx"],(function(a,b,c,d,e,f,g,h){var i=null,j=null,k=!1,l=a.console,m=[],n={},o="Jest-Only Fatal: ",p="";function q(a){if(!k){var b=c("err")(o+a);l.error(o+a+"\n"+b.stack);k=!0;c("FBLogger")("React").warn("React compiler guard functions used in production.")}}["useCallback","useContext","useEffect","useImperativeHandle","useInsertionEffect","useLayoutEffect","useMemo","useReducer","useRef","useState","useDebugValue","useDeferredValue","useTransition","useMutableSource","useSyncExternalStore","useId","useCacheRefresh","useOptimistic"].forEach(function(a){n[a]=function(){var a="[React] Unexpected React hook call {name} from a React Forget compiled function. Check that all hooks are called directly and named according to convention ('use[A-Z]') ";q(a)}});a=function(a){n[a]=function(){if(j==null)throw c("FBLogger")("React").mustfixThrow("React Forget internal error: unexpected null dispatcher");else{var b;return(b=j)[a].apply(b,arguments)}}};for(var r of["useMemoCache","unstable_useMemoCache","readContext","unstable_isNewReconciler","getCacheSignal","getCacheForType","use"])a(r);function b(a){i.H==null;var b=i.H;if(b==null)return;if(a===0){m.push(b);m.length===1&&(j=b);if(b===n){var d="[React] Unexpected call to custom hook or component from a React Forget compiled function. Check that (1) all hooks are called directly and named according to convention ('use[A-Z]') and (2) components are returned as JSX instead of being directly invoked.";q(d)}i.H=n}else if(a===1){d=m.pop();if(d==null)throw c("FBLogger")("React").mustfixThrow("React Forget internal error: unexpected null in guard stack");m.length===0&&(j=null);i.H=d}else if(a===2)m.push(b),j!=null&&(i.H=j);else if(a===3){d=m.pop();if(d==null)throw c("FBLogger")("React").mustfixThrow("React Forget internal error: unexpected null in guard stack");i.H=d}}function d(a){s=a.isValidElement,i=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,a.unstable_useContextWithBailout,a.useContext}a=!1;function e(a,b){l.log(a,b)}var s=null;function t(a){return(a=s==null?void 0:s(a))!=null?a:!1}var u=new Set();function f(a,b,d,e,f,g){function i(a,b,c,h){c=p+"Forget change detection: "+f+":"+g+" ["+e+"]: "+d+c+" changed from "+a+" to "+b+" (depth "+h+")";if(u.has(c))return;u.add(c);l.error(c)}var j=2;function k(a,b,d,e){if(e>j)return;else if(a===b)return;else if(typeof a!==typeof b)i("type "+typeof a,"type "+typeof b,d,e);else if(typeof a==="object"){typeof b==="object"||h(0,18576);if(a===null||b===null)(a!==null||b!==null)&&i("type "+(a===null?"null":"object"),"type "+(b===null?"null":"object"),d,e);else if(a instanceof Map)if(!(b instanceof Map))i("Map instance","other value",d,e);else if(a.size!==b.size)i("Map instance with size "+a.size,"Map instance with size "+b.size,d,e);else for(var f of a){var g=f[0],l=f[1];!b.has(g)?i("Map instance with key "+String(g),"Map instance without key "+String(l),d,e):k(l,b.get(g),d+".get("+String(g)+")",e+1)}else if(b instanceof Map)i("other value","Map instance",d,e);else if(a instanceof Set)if(!(b instanceof Set))i("Set instance","other value",d,e);else if(a.size!==b.size)i("Set instance with size "+a.size,"Set instance with size "+b.size,d,e);else for(l of b)a.has(l)||i("Set instance without element "+String(l),"Set instance with element "+String(l),d,e);else if(b instanceof Set)i("other value","Set instance",d,e);else if(Array.isArray(a)||Array.isArray(b))if(!Array.isArray(a)||!Array.isArray(b))i("type "+(Array.isArray(a)?"array":"object"),"type "+(Array.isArray(b)?"array":"object"),d,e);else if(a.length!==b.length)i("array with length "+a.length,"array with length "+b.length,d,e);else for(g=0;g<a.length;g++)k(a[g],b[g],d+"["+g+"]",e+1);else if(t(a)||t(b))!t(a)||!t(b)?i("type "+(t(a)?"React element":"object"),"type "+(t(b)?"React element":"object"),d,e):c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(a).type!==c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(b).type?i("React element of type "+String(c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(a).type.name),"React element of type "+String(c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(b).type.name),d,e):k(c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(a).props,c("DangerouslyAccessReactElementInternals_DO_NOT_USE_IN_NEW_CODE")(b).props,"[props of "+d+"]",e+1);else{for(f in b)f in a||i("object without key "+f,"object with key "+f,d,e);for(l in a)!(l in b)?i("object with key "+l,"object without key "+l,d,e):k(a[l],b[l],d+"."+l,e+1)}}else if(typeof a==="function")return;else isNaN(a)||isNaN(b)?isNaN(a)!==isNaN(b)&&i(""+(isNaN(a)?"NaN":"non-NaN value"),""+(isNaN(b)?"NaN":"non-NaN value"),d,e):a!==b&&i(String(a),String(b),d,e)}k(a,b,"",0)}g.$dispatcherGuard=b;g.initReactForgetRuntime=d;g.shouldLogRender=a;g.logRender=e;g.$structuralCheck=f}),98);
__d("setupReactRefresh",["cr:1108857"],(function(a,b,c,d,e,f,g){}),34);
__d("shimReactSecretInternals_DEPRECATED",["FBLogger","justknobx"],(function(a,b,c,d,e,f,g){"use strict";var h=new Set();function i(a,b){b===void 0&&(b="warn");if(c("justknobx")._("1806")){if(h.has(a))return;h.add(a);var d=c("FBLogger")("react","secret-internals-shim-"+a);b==="warn"?d.warn("Access to a renamed property of React's secret internals: %s. A library or hack needs to be updated.",a):d.mustfix("Access to a renamed property of React's secret internals: %s. A library or hack needs to be updated.",a)}}function a(a){var b=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;Object.assign(b,{ReactCurrentDispatcher:{get current(){i("ReactCurrentDispatcher-current-get");return b.H},set current(a){i("ReactCurrentDispatcher-current-set"),b.H=a}},ReactCurrentCache:{get current(){i("ReactCurrentCache-current-get");return b.A},set current(a){i("ReactCurrentCache-current-set","mustfix")}},ReactCurrentBatchConfig:{get transition(){i("ReactCurrentBatchConfig-transition-get");return b.T},set transition(a){i("ReactCurrentBatchConfig-transition-set","mustfix")}},ReactCurrentOwner:{get current(){i("ReactCurrentOwner-current-get");var a=b.A;return a===null?null:a.getOwner()},set current(a){i("ReactCurrentOwner-current-set","mustfix")}}});["ReactCurrentDispatcher","ReactCurrentCache","ReactCurrentBatchConfig","ReactCurrentOwner"].forEach(function(a){var c;((c=Object.getOwnPropertyDescriptor(b,a))==null?void 0:c.configurable)&&Object.defineProperty(b,a,{enumerable:!1})});a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=b}g["default"]=a}),98);
__d("VultureJSGating",["justknobx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("justknobx")._("2635")}function b(){return c("justknobx")._("3532")}g.isLoggingEnabled=a;g.isLowVolumeLoggingEnabled=b}),98);
__d("objectEntries",[],(function(a,b,c,d,e,f){function a(a){return Object.entries(a)}f["default"]=a}),66);
__d("RDRequireDeferredReference",["RequireDeferredReference"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}b.disableForSSR_DO_NOT_USE=function(){this.$RDRequireDeferredReference$p_1=!1};var c=b.prototype;c.isAvailableInSSR_DO_NOT_USE=function(){return this.constructor.$RDRequireDeferredReference$p_1};return b}(c("RequireDeferredReference"));a.$RDRequireDeferredReference$p_1=!0;g["default"]=a}),98);
__d("requireDeferred",["RDRequireDeferredReference"],(function(a,b,c,d,e,f,g){"use strict";var h={};function i(a,b){h[a]=b}function j(a){return h[a]}function a(a){var b=j(a);if(b)return b;b=new(c("RDRequireDeferredReference"))(a);b.setRequireDeferredForDisplay(!1);i(a,b);return b}g["default"]=a}),98);
__d("vulture",["ExecutionEnvironment","FBLogger","JSResource","VultureJSGating","clearTimeout","objectEntries","requireDeferred","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i=0,j=-1,k=null;c("requireDeferred")("bumpVultureJSHash").__setRef("vulture").onReadyImmediately(function(a){k=a,u()});var l=!1,m=!1,n=null,o=new Map(),p=[],q=12e4;function r(a){var b=o.get(a);if(b===i||k==null)return;b==null?k(a,1):b===j?(k(a,1),d("VultureJSGating").isLowVolumeLoggingEnabled()&&c("FBLogger")("vulture_js","low_volume_"+a).addToCategoryKey(a).addMetadata("VULTURE_JS","LOW_VOLUME_HASH",a).warn("Low volume vulture with hash %s hit",a)):Math.floor(Math.random()*b)===0&&k(a,b)}function s(a){p.push(a)}function t(){n!=null&&(c("clearTimeout")(n),n=null),m=!1,l=!0,u()}function u(){if(l&&k!=null)while(p.length>0){var a=p.pop();a!=null&&r(a)}}function v(){if(m)return;m=!0;(h||(h=c("ExecutionEnvironment"))).canUseDOM&&(n=c("setTimeout")(function(){t(),c("FBLogger")("vulture_js","sample_rate_load_timeout").warn("Timed out attemping to fetch VultureJS sample rates")},q));c("JSResource")("VultureJSSampleRatesLoader").__setRef("vulture").load().then(async function(a){a=await a.loadSampleRates();for(a of c("objectEntries")(a)){var b=a[0],d=a[1];o.set(b,d)}}).catch(function(a){c("FBLogger")("vulture_js","sample_rate_load_timeout").catching(a).mustfix("Failed to fetch sample rates:  %s",a.getMessage())}).finally(t)}function a(a){d("VultureJSGating").isLoggingEnabled()&&(l&&k!=null?r(a):(s(a),v()))}g.default=a}),98);
__d("React",["FBLogger","cr:1294158","cr:15957","react-compiler-runtime","react-forget-runtime","setupReactRefresh","shimReactSecretInternals_DEPRECATED","vulture"],(function(a,b,c,d,e,f){b("setupReactRefresh");a=b("cr:1294158");b("cr:15957");b("shimReactSecretInternals_DEPRECATED")(a);b("react-forget-runtime").initReactForgetRuntime(a);b("react-compiler-runtime").initReactCompilerRuntime(a);var g=a.createFactory;a.createFactory=function(){b("vulture")("pB3SJkIu2GS8TCgsuxQ3RWJ--gc=");b("FBLogger")("react","createfactory").mustfix("React.createFactory is not supported anymore");return g.apply(void 0,arguments)};e.exports=a}),null);
__d("BaseDialogLabelIDProvider",["HiddenSubtreeContext","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));f=h;var j=f.useContext,k=f.useId,l=i.createContext(void 0);function a(a){var b=d("react-compiler-runtime").c(3),c=a.children;a=a.disable;a=a===void 0?!1:a;var e=k();a=a?void 0:e;b[0]!==c||b[1]!==a?(e=i.jsx(l.Provider,{value:a,children:c}),b[0]=c,b[1]=a,b[2]=e):e=b[2];return e}function b(){var a=j(l),b=j(c("HiddenSubtreeContext"));b=b.hidden;return b?void 0:a}function e(){return j(l)}g.BaseDialogLabelIDContext=l;g.BaseDialogLabelIDProvider=a;g.useDialogHeaderID=b;g.useDialogLabelID=e}),98);
__d("BaseGlimmerCompatStyles",[],(function(a,b,c,d,e,f,g){"use strict";var h=200;a={animationDelay:function(a){return[{animationDelay:"calc("+a%10+" * var(--glimmer-stagger-time, "+h+"ms))"==null?null:"x1js0keu",$$css:!0},{"--animationDelay":function(a){return typeof a==="number"?a+"ms":a!=null?a:void 0}("calc("+a%10+" * var(--glimmer-stagger-time, "+h+"ms))")}]},animationIterationCount:function(a){return[{animationIterationCount:a==null?null:"x16nzaz0",$$css:!0},{"--animationIterationCount":a!=null?a:void 0}]},paused:{animationPlayState:"xorstpt",$$css:!0},root:{animationDirection:"x1iq0kzc",animationDuration:"x1i9sevy",animationIterationCount:"xa4qsjk",animationName:"x43zylw",animationTimingFunction:"x1ag7td9",opacity:"x11fwcs0",$$css:!0}};g.styles=a}),98);
__d("OdsWebBatchFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1838142");b=d("FalcoLoggerInternal").create("ods_web_batch",a);e=b;g["default"]=e}),98);
__d("FalcoConsentChecker",[],(function(a,b,c,d,e,f){"use strict";function g(a,b,c,d){var e;switch(typeof d){case"string":e=a[String(d)];return!e?!1:e<=b;case"number":return g(a,b,c,c[Number(d)]);default:e=!1;if(Array.isArray(d)){var f=d[0];for(var h=1;h<d.length;h++){e=g(a,b,c,d[h]);if(e){if(f==="or")return!0}else if(f==="and")return!1}}return e}}f["default"]=g}),66);
__d("pageID",["WebSession"],(function(a,b,c,d,e,f,g){"use strict";a=d("WebSession").getPageId_DO_NOT_USE();g["default"]=a}),98);
__d("WebStorageMutex",["WebStorage","clearTimeout","pageID","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i=null,j=!1,k=c("pageID");function l(){j||(j=!0,i=(h||(h=c("WebStorage"))).getLocalStorage());return i}a=function(){function a(a){this.name=a}a.testSetPageID=function(a){k=a};var b=a.prototype;b.$2=function(){var a,b=l();if(!b)return k;b=b.getItem("mutex_"+this.name);b=((a=b)!=null?a:"").split(":");return b&&parseInt(b[1],10)>=Date.now()?b[0]:null};b.$3=function(a){var b=l();if(!b)return;a=a==null?1e3:a;a=Date.now()+a;(h||(h=c("WebStorage"))).setItemGuarded(b,"mutex_"+this.name,k+":"+a)};b.hasLock=function(){return this.$2()===k};b.lock=function(a,b,d){var e=this;this.$1&&c("clearTimeout")(this.$1);k===(this.$2()||k)&&this.$3(d);this.$1=c("setTimeout")(function(){e.$1=null;var c=e.hasLock()?a:b;c&&c(e)},0)};b.unlock=function(){this.$1&&c("clearTimeout")(this.$1);var a=l();a&&this.hasLock()&&a.removeItem("mutex_"+this.name)};return a}();g["default"]=a}),98);
__d("guid",[],(function(a,b,c,d,e,f){function a(){if(typeof crypto==="object"&&typeof crypto.getRandomValues==="function"&&typeof String.prototype.padStart==="function"){var a=crypto.getRandomValues(new Uint32Array(2));return"f"+a[0].toString(16).padStart(8,"0")+a[1].toString(16).padStart(8,"0")}return"f"+(Math.random()*(1<<30)).toString(16).replace(".","")}f["default"]=a}),66);
__d("PersistedQueue",["AnalyticsCoreData","BaseEventEmitter","ExecutionEnvironment","Run","WebStorage","WebStorageMutex","cr:8958","err","guid","nullthrows","performanceAbsoluteNow","requestAnimationFrame"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=24*60*60*1e3,m=30*1e3,n=new(c("BaseEventEmitter"))(),o;function p(a){a===void 0&&(a=!1);if(o===void 0){var b;if(((b=(h||(h=c("AnalyticsCoreData"))).queue_activation_experiment)!=null?b:!1)&&a)try{return(i||(i=c("WebStorage"))).getLocalStorageForRead()}catch(a){return null}b="check_quota";try{a=(i||(i=c("WebStorage"))).getLocalStorage();a?(a.setItem(b,b),a.removeItem(b),o=a):o=null}catch(a){o=null}}return o}function q(a){var b=a.prev,c=a.next;c&&(c.prev=b);b&&(b.next=c);a.next=null;a.prev=null}function r(a){return{item:a,next:null,prev:null}}function s(a,b){return a+"^$"+((a=b==null?void 0:b.queueNameSuffix)!=null?a:"")}var t={},u={},v={},w=!1;a=function(){function a(a,b){var d,e=this;this.$7=0;this.$3=a;this.$5=(d=b==null?void 0:b.queueNameSuffix)!=null?d:"";this.$15=b==null?void 0:b.application;this.$4=s(a,b);this.$11=this.$4+"^$"+c("guid")();this.$14=!1;if(b){this.$8=(d=b.max_age_in_ms)!=null?d:l;this.$12=b.migrate;this.$13=b.onLoad}else this.$8=l;this.$9=[n.addListener("active",function(){(e.$10!=null||!e.$14)&&(e.$14=!0,e.$10=null,e.$16())}),n.addListener("inactive",function(){e.$10==null&&(e.$10=Date.now(),e.$17())})];((j||(j=c("ExecutionEnvironment"))).canUseDOM||(j||(j=c("ExecutionEnvironment"))).isInWorker)&&c("requestAnimationFrame")(function(){return e.$16()})}var d=a.prototype;d.isActive=function(){var a=this.$10;if(a==null)return!0;if(Date.now()-a>m){this.$10=null;n.emit("active",null);return!0}return!1};d.$16=function(){this.$18(),this.$19()};d.$17=function(){this.$20()};d.getFullName=function(){return this.$4};d.getQueueNameSuffix=function(){return this.$5};a.isQueueActivateExperiment=function(){return w};a.setOnQueueActivateExperiment=function(){w=!0};a.create=function(b,d){var e=s(b,d);if(e in t)throw c("err")("Duplicate queue created: "+b);d=new a(b,d);t[e]=d;v[b]?v[b].push(d):v[b]=[d];e=u[b];e&&d.setHandler(e);return d};a.setHandler=function(a,b){if(v[a]){var c=v[a];for(c of c)c.setHandler(b)}u[a]=b};d.destroy=function(){this.$9.forEach(function(a){return a.remove()})};a.destroy=function(a,b){a=s(a,b);t[a].destroy();delete t[a]};d.setHandler=function(a){this.$6=a;this.$19();return this};d.$19=function(){this.$7>0&&this.$6&&this.$6(this)};d.length=function(){return this.$7};d.enumeratedLength=function(){return this.$21().length};a.isPersistenceAllowed=function(){var a=p();return!a?!1:!0};a.getSuffixesForKey=function(a){var b=[];try{var c=p(!0);if(!c)return b;a=a+"^$";for(var d=0;d<c.length;d++){var e=c.key(d);if(typeof e==="string"&&e.startsWith("mutex_falco_"))c.removeItem(e);else if(typeof e==="string"&&e.startsWith(a)){e=e.split("^$");if(e.length>2){e=e[1];b.push(e)}else b.push("")}}}catch(a){}return b};d.$18=function(){var d,e=this,a=p(!0);if(!a)return;var f=this.$4+"^$";d=new(c("WebStorageMutex"))((d=this.$15)!=null?d:f);var g=this.$12,h=this.$13;d.lock(function(d){var i=Date.now()-e.$8;try{for(var j=0;j<a.length;j++){var k=a.key(j);if(typeof k==="string"&&k.startsWith(f)){var l=a.getItem(k);a.removeItem(k);if(l!=null&&l.startsWith("{")){k=b("cr:8958").parse(c("nullthrows")(l));if(k.ts>i)try{for(l of k.items){g!=null?g(l):l;k=h!=null?h(l):l;e.$22(k)}}catch(a){}}}}}catch(a){}d.unlock();e.$19()})};d.$20=function(){var a=p();if(!a)return;var d=this.$21();if(d.length===0){a.getItem(this.$11)!=null&&a.removeItem(this.$11);return}(i||(i=c("WebStorage"))).setItemGuarded(a,this.$11,b("cr:8958").stringify({items:d.map(function(a){return a}),ts:(k||(k=c("performanceAbsoluteNow")))()}))};d.$21=function(){var a=this.$1,b=[];if(!a)return b;do b.push(a.item);while(a=a.prev);return b.reverse()};d.markItemAsCompleted=function(a){var b=a.prev;q(a);this.$1===a&&(this.$1=b);this.$7--;this.isActive()||this.$20()};d.markItemAsFailed=function(a){q(a);var b=this.$2;if(b){var c=b.prev;c&&(c.next=a,a.prev=c);a.next=b;b.prev=a}this.$2=a;this.isActive()&&this.$19()};d.markItem=function(a,b){b?this.markItemAsCompleted(a):this.markItemAsFailed(a)};d.$22=function(a){a=r(a);var b=this.$1;b&&(b.next=a,a.prev=b);this.$1=a;this.$2||(this.$2=a);this.$7++};d.wrapAndEnqueueItem=function(a){this.$22(a),this.isActive()?this.$19():this.$20()};d.dequeueItem=function(){if(this.$10!=null)return null;var a=this.$2;if(!a)return null;this.$2=a.next;return a};return a}();a.eventEmitter=n;if((j||(j=c("ExecutionEnvironment"))).canUseDOM){var x=d("Run").maybeOnBeforeUnload(function(){n.emit("inactive",null),x==null?void 0:x.remove()},!1);if(!x)var y=d("Run").onUnload(function(){n.emit("inactive",null),y.remove()})}g["default"]=a}),98);
__d("ServerTime",["ServerTimeData"],(function(a,b,c,d,e,f,g){var h,i=0;f=0;var j=null;h=(h=(typeof window!=="undefined"?window:self).performance)==null?void 0:h.timing;if(h){var k=h.requestStart;h=h.domLoading;if(k&&h){var l=c("ServerTimeData").timeOfResponseStart-c("ServerTimeData").timeOfRequestStart;k=h-k-l;f=k/2;l=h-c("ServerTimeData").timeOfResponseStart-f;h=Math.max(50,k*.8);Math.abs(l)>h&&(i=l,j=Date.now())}}else d(c("ServerTimeData").serverTime);function a(){return Date.now()-i}function b(){return i}function d(a){a=Date.now()-a;Math.abs(i-a)>6e4&&(i=a,j=Date.now())}function e(){return j===null?null:Date.now()-j}f=a;k=b;g.getMillis=a;g.getOffsetMillis=b;g.update=d;g.getMillisSinceLastUpdate=e;g.get=f;g.getSkew=k}),98);
__d("FalcoLoggerInternal",["AnalyticsCoreData","BaseEventEmitter","FBLogger","FalcoConsentChecker","FalcoUtils","PersistedQueue","Promise","Random","ServerTime","WebSession","nullthrows","performanceAbsoluteNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(f=d("FalcoUtils")).getTaggedBitmap(33),l=f.getTaggedBitmap(0),m=f.getTaggedBitmap(37),n=500*1024*.6,o=new Map();function p(a){var b;a.tags=d("FalcoUtils").xorBitmap((b=a.tags)!=null?b:[0,0],k);return a}function a(a,b){var d;d=(d=c("PersistedQueue").getSuffixesForKey(a))!=null?d:[];d.push(b);for(d of d){var e,f=a+"^$"+d;if(o.has(f))continue;e=((e=(i||(i=c("AnalyticsCoreData"))).use_falco_as_mutex_key)!=null?e:!1)?c("PersistedQueue").create(a,{onLoad:p,queueNameSuffix:d,application:"falco"}):c("PersistedQueue").create(a,{onLoad:p,queueNameSuffix:d});o.set(f,e)}return c("nullthrows")(o.get(a+"^$"+b))}f=f.identityToString((i||(i=c("AnalyticsCoreData"))).identity);var q=a("falco_queue_log",f),r=a("falco_queue_immediately",f),s=a("falco_queue_critical",f),t=new(c("BaseEventEmitter"))(),u={};function v(a,b,e){var f=c("Random").coinflip(b.r);if(!f){d("FalcoUtils").bumpODSMetrics(e,"event.filters.sampling",1);return!1}f=b.c;if(f!=null&&(i||(i=c("AnalyticsCoreData"))).consents!=null){b=w(f,(i||(i=c("AnalyticsCoreData"))).consents,a);if(!b){d("FalcoUtils").bumpODSMetrics(e,"event.filters.consent",1);return!1}}return!0}function w(a,b,d){var e=u[a];e==null&&(e=u[a]=JSON.parse(a));return c("FalcoConsentChecker")(b,d,e,e[0])}function x(){return(j||(j=c("performanceAbsoluteNow")))()-d("ServerTime").getOffsetMillis()}function y(a,b,d,e,f,g){if((i||(i=c("AnalyticsCoreData"))).enable_observer){a=babelHelpers["extends"]({name:a,time:b,sampled:d,getData:f,policy:e},g&&{getPrivacyContext:g});t.emit("event",a)}}function z(a,b,e,f,g,h){var j;g=JSON.stringify(g);if(g.length>n){d("FalcoUtils").bumpODSMetrics(a,"event.filters.exceeded_size",1);c("FBLogger")("falco","oversized_message:"+a).warn('Dropping event "%s" due to exceeding the max size %s at %s',a,n,g.length);return}var k=d("FalcoUtils").xorBitmap([0,0],l);k=d("FalcoUtils").xorBitmap(k,m);((j=(i||(i=c("AnalyticsCoreData"))).enable_session_id_bug_fix)!=null?j:!1)?h.wrapAndEnqueueItem({name:a,policy:b,time:e,extra:g,privacyContext:f,tags:k,sessionId:d("WebSession").getId(),deviceId:(i||(i=c("AnalyticsCoreData"))).device_id}):(h.wrapAndEnqueueItem({name:a,policy:b,time:e,extra:g,privacyContext:f,tags:k}),d("FalcoUtils").bumpODSMetrics(a,"event.captured",1))}function A(a,b,c,e,f){try{var g=x();d("FalcoUtils").bumpODSMetrics(a,"event.logged",1);var h=v(g,b,a);if(h){var i=e(),j=c&&c();z(a,b,g,j,i,f)}y(a,g,h,b,e,c)}catch(a){C(a)}}function B(a,c,e,f,g){try{var i=x();d("FalcoUtils").bumpODSMetrics(a,"event.logged",1);var j=v(i,c,a);if(j){var k=f(),l=(h||(h=b("Promise"))).resolve(e&&e());return h.all([k,l]).then(function(b){var d=b[0],e=b[1];z(a,c,i,e,d,g);y(a,i,j,c,function(){return d},e&&function(){return e})})}else{y(a,i,j,c,f,e);return(h||(h=b("Promise"))).resolve()}}catch(a){return(h||(h=b("Promise"))).reject(a)}}function C(a){var b=c("FBLogger")("falco");a instanceof Error?b.catching(a).warn("Error while attempting to log to Falco"):b.warn("Caught non-error while attempting to log to Falco: %s",JSON.stringify(a))}function e(a,b){return{log:function(c,d){A(a,b,d,c,q)},logAsync:function(c,d){B(a,b,d,c,q)["catch"](C)},logImmediately:function(c,d){A(a,b,d,c,r)},logCritical:function(c,d){A(a,b,d,c,s)},logRealtimeEvent:function(c,d){A(a,b,d,c,s)}}}g.observable=t;g.create=e}),98);
__d("FalcoUtils",["AnalyticsCoreData","ODS"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j="ods_web_batch";function a(a){if(a){var b=a.fbIdentity,c=a.appScopedIdentity;a=a.claim;var d="";if(b){var e=b.accountId;b=b.actorId;d=e+"^#"+b+"^#"}else c!==void 0&&(d="^#^#"+c);return d+"^#"+a}return""}function b(a){return a>30?[0,1<<a-30]:[1<<a,0]}function e(a,b){return[a[0]|b[0],a[1]|b[1]]}function f(a,b,e){if(a===j)return;(i||(i=d("ODS"))).bumpEntityKey(7173,"entities.ff_js_web."+a+"."+(h||(h=c("AnalyticsCoreData"))).app_id+"."+((a=(h||(h=c("AnalyticsCoreData"))).app_version)!=null?a:"0").split(".")[0]+"."+h.push_phase,b,e)}g.identityToString=a;g.getTaggedBitmap=b;g.xorBitmap=e;g.bumpODSMetrics=f}),98);
__d("ODS",["ExecutionEnvironment","OdsWebBatchFalcoEvent","Random","Run","clearTimeout","gkx","setTimeout","unrecoverableViolation"],(function(a,b,c,d,e,f,g){var h,i,j=(h||(h=c("ExecutionEnvironment"))).canUseDOM||(h||c("ExecutionEnvironment")).isInWorker,k={};function l(a,b,c,d,e){var f;d===void 0&&(d=1);e===void 0&&(e=1);var g=(f=k[b])!=null?f:null;if(g!=null&&g<=0)return;i=i||{};var h=i[a]||(i[a]={}),j=h[b]||(h[b]={}),l=j[c]||(j[c]={n:0,d:null}),m=Number(d),o=Number(e);g>0&&(m/=g,o/=g);if(!isFinite(m)||!isFinite(o))return;l.n+=m;if(arguments.length>=5){var p=l.d;p==null&&(p=0);l.d=p+o}n()}var m;function n(){if(m!=null)return;m=c("setTimeout")(function(){o()},c("gkx")("20935")?13e3/2:5e3)}function a(a,b){if(!j)return;k[a]=d("Random").random()<b?b:0}function b(a,b,c,d){d===void 0&&(d=1);if(!j)return;l(a,b,c,d)}function e(a,b,c,d,e){d===void 0&&(d=1);e===void 0&&(e=1);if(!j)return;l(a,b,c,d,e)}function o(a){a===void 0&&(a="normal");if(!j)return;c("clearTimeout")(m);m=null;if(i==null)return;var b=i;i=null;function d(){return{batch:b}}a==="critical"?c("OdsWebBatchFalcoEvent").logCritical(d):c("OdsWebBatchFalcoEvent").log(d)}j&&d("Run").onUnload(function(){o("critical")});g.setEntitySample=a;g.bumpEntityKey=b;g.bumpFraction=e;g.flush=o}),98);
__d("InteractionTracingMetrics",["interaction-tracing-metrics"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=d("interaction-tracing-metrics").InteractionTracingMetricsCore}),98);
__d("setTimeoutCometLoggingPri",["cr:619"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:619")}),98);
__d("CometVisibilityUserActivityMonitor",["Visibility"],(function(a,b,c,d,e,f,g){"use strict";a={isUserActive:function(){return c("Visibility").isHidden()===!1},subscribe:function(a){var b,d=(b=c("Visibility")).addListener(b.HIDDEN,function(){return a&&a(!1)}),e=b.addListener(b.VISIBLE,function(){return a&&a(!0)});return function(){d&&d.remove(),e&&e.remove()}}};g["default"]=a}),98);
__d("getIntersectionMarginFromViewportMargin",[],(function(a,b,c,d,e,f){"use strict";var g=new Map();function a(a){var b="bottom:"+a.bottom+"|top:"+a.top+"|left:"+a.left+"|right:"+a.right,c=g.get(b);c==null&&(c={bottom:a.bottom*-1,left:a.left*-1,right:a.right*-1,top:a.top*-1},g.set(b,c));return c}f["default"]=a}),66);
__d("camelize",[],(function(a,b,c,d,e,f){var g=/-(.)/g;function a(a){return a.replace(g,function(a,b){return b.toUpperCase()})}f["default"]=a}),66);
__d("hyphenate",[],(function(a,b,c,d,e,f){var g=/([A-Z])/g;function a(a){return a.replace(g,"-$1").toLowerCase()}f["default"]=a}),66);
__d("getStyleProperty",["camelize","hyphenate"],(function(a,b,c,d,e,f,g){function h(a){return a==null?"":String(a)}function a(a,b){var d;if(window.getComputedStyle){d=window.getComputedStyle(a,null);if(d)return h(d.getPropertyValue(c("hyphenate")(b)))}if(document.defaultView&&document.defaultView.getComputedStyle){d=document.defaultView.getComputedStyle(a,null);if(d)return h(d.getPropertyValue(c("hyphenate")(b)));if(b==="display")return"none"}return a.currentStyle?b==="float"?h(a.currentStyle.cssFloat||a.currentStyle.styleFloat):h(a.currentStyle[c("camelize")(b)]):h(a.style&&a.style[c("camelize")(b)])}g["default"]=a}),98);
__d("intersectionObserverEntryIsIntersecting",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a.isIntersecting!=null?a.isIntersecting:a.intersectionRatio>0||a.intersectionRect&&(a.intersectionRect.height>0||a.intersectionRect.width>0)}f["default"]=a}),66);
__d("nullIntersectionObserverEntryLogger",["FBLogger","Random"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){a==null&&d("Random").coinflip(100)&&c("FBLogger")("comet_infra").warn(b)}g["default"]=a}),98);
__d("observeIntersection",["invariant","ErrorGuard"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=typeof WeakMap==="function",k={__noRoot:!0},l=j?new WeakMap():new Map();function m(a){var b=a.threshold;if(Array.isArray(b)){var c={};b.forEach(function(a){c[String(a)]=!0});b=Object.keys(c).sort()}var d=babelHelpers["extends"]({},a,{threshold:b}),e={};Object.keys(d).sort().forEach(function(a){e[a]=d[a]});return JSON.stringify(e)}function a(a,b,d){d===void 0&&(d={});var e=m({rootMargin:d.rootMargin,scrollMargin:d.scrollMargin,threshold:d.threshold}),f=d.root||k,g=l.get(f);g==null&&(g={},l.set(f,g));var n=g[e];if(n==null){var o=void 0;Array.isArray(d.threshold)?o=d.threshold.slice():typeof d.threshold==="number"&&(o=[d.threshold]);d=new IntersectionObserver(function(a){a.forEach(function(a){n!=null||h(0,2439);var b=n.targetToCallbacksMap.get(a.target);b&&b.length>0&&b.forEach(function(b){(i||(i=c("ErrorGuard"))).applyWithGuard(b,null,[a],{name:"observeIntersection"})})})},babelHelpers["extends"]({},d,{threshold:o}));n={intersectionObserver:d,referenceCount:0,targetToCallbacksMap:j?new WeakMap():new Map()};g[e]=n}o=n.targetToCallbacksMap.get(a);o==null&&(n.intersectionObserver.observe(a),n.referenceCount+=1,o=[],n.targetToCallbacksMap.set(a,o));o.push(b);var p=!1,q=function(){if(p)return;var c=n.targetToCallbacksMap.get(a);c!=null||h(0,2440);if(c.length===1)n.intersectionObserver.unobserve(a),n.targetToCallbacksMap["delete"](a),n.referenceCount-=1,a=null;else{var d=c.indexOf(b);d!==-1||h(0,2441);c.splice(d,1)}n.referenceCount===0&&(g!=null||h(0,2442),delete g[e],f&&Object.keys(g).length===0&&l["delete"](f));b=null;a=null;f=null;p=!0};return{remove:function(){q&&(q(),q=null)}}}f.exports=a}),34);
__d("useIntersectionObserver",["DOMRectReadOnly","ExecutionEnvironment","JSScheduler","observeIntersection","react","react-compiler-runtime","useDynamicCallbackDANGEROUS"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;i||(i=d("react"));e=i;e.useCallback;var k=e.useLayoutEffect,l=e.useRef,m={bottom:0,left:0,right:0,top:0},n=c("DOMRectReadOnly").fromRect(),o={bottom:0,height:0,left:0,right:0,top:0,width:0,x:0,y:0};function p(a){var b;if(a==null)b=m;else if(typeof a==="string")return a;else typeof a==="number"?b={bottom:a,left:a,right:a,top:a}:b=babelHelpers["extends"]({},m,{},a);a=b;b=a.bottom;var c=a.left,d=a.right;a=a.top;return a+"px "+d+"px "+b+"px "+c+"px"}function q(a,b,d,e){var f=b.root,g=b.rootMargin,h=b.scrollMargin,i=b.threshold;f=f===null?null:f();var j=a==null||a.element!==d||a.onIntersect!==e||a.observedRoot!==f||a.rootMargin!==g||a.scrollMargin!==h||a.threshold!==i;if(j){a&&a.subscription.remove();j=c("observeIntersection")(d,e,{root:f,rootMargin:p(g),scrollMargin:p(h),threshold:i});return babelHelpers["extends"]({},b,{element:d,observedRoot:f,onIntersect:e,subscription:j})}return a}function a(a,b){var e=d("react-compiler-runtime").c(16),f=b.root,g=b.rootMargin,h=b.scrollMargin,i=b.threshold;b=b.disabled;var m=b===void 0?!1:b,p=l(null),r=l(null),s=l(null),t=l(null),u=l(!1),v=c("useDynamicCallbackDANGEROUS")(a);e[0]!==v||e[1]!==f||e[2]!==g||e[3]!==h||e[4]!==i?(b=function(a){if(p.current===a)return;if(p.current!=null&&a==null){t.current!=null&&(j||(j=d("JSScheduler"))).cancelCallback(t.current);var b=p.current;t.current=(j||(j=d("JSScheduler"))).scheduleImmediatePriCallback(function(){p.current===null&&u.current===!1&&v({boundingClientRect:o,intersectionRatio:0,intersectionRect:o,isIntersecting:!1,isVisible:!1,rootBounds:n,target:b,time:Date.now()}),t.current=null})}p.current=a;r.current!=null&&(r.current.subscription.remove(),r.current=null);s.current!=null&&(j||(j=d("JSScheduler"))).cancelCallback(s.current);s.current=(j||(j=d("JSScheduler"))).scheduleImmediatePriCallback(function(){p.current!=null&&(r.current=q(r.current,{root:f,rootMargin:g,scrollMargin:h,threshold:i},p.current,v)),s.current=null})},e[0]=v,e[1]=f,e[2]=g,e[3]=h,e[4]=i,e[5]=b):b=e[5];a=b;var w;e[6]!==m||e[7]!==v||e[8]!==f||e[9]!==g||e[10]!==h||e[11]!==i?(b=function(){if(m)return;s.current!=null&&(j||(j=d("JSScheduler"))).cancelCallback(s.current);s.current=(j||(j=d("JSScheduler"))).scheduleImmediatePriCallback(function(){p.current!=null&&(r.current=q(r.current,{root:f,rootMargin:g,scrollMargin:h,threshold:i},p.current,v)),s.current=null});return function(){r.current!=null&&(r.current.subscription.remove(),r.current=null),s.current!=null&&((j||(j=d("JSScheduler"))).cancelCallback(s.current),s.current=null)}},w=[v,f,m,g,h,i],e[6]=m,e[7]=v,e[8]=f,e[9]=g,e[10]=h,e[11]=i,e[12]=b,e[13]=w):(b=e[12],w=e[13]);k(b,w);e[14]===Symbol["for"]("react.memo_cache_sentinel")?(b=function(){u.current=!1;return function(){u.current=!0}},w=[],e[14]=b,e[15]=w):(b=e[14],w=e[15]);k(b,w);return a}function b(a,b,c){return r}function r(a){}f=(h||(h=c("ExecutionEnvironment"))).canUseDOM?a:b;g["default"]=f}),98);
__d("useViewportDuration",["BaseViewportMarginsContext","CometVisibilityUserActivityMonitor","HiddenSubtreePassiveContext","Run","getIntersectionMarginFromViewportMargin","getStyleProperty","gkx","intersectionObserverEntryIsIntersecting","nullIntersectionObserverEntryLogger","react","useDoubleEffectHack_DO_NOT_USE_THIS_IS_TRACKED","useIntersectionObserver"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useCallback,j=b.useContext,k=b.useLayoutEffect,l=b.useMemo,m=b.useRef,n=function(a){if(a.target==null)return null;if(c("getStyleProperty")(a.target,"opacity")==="0")return"TARGET_TRANSPARENT";return c("getStyleProperty")(a.target,"visibility")==="hidden"?"TARGET_HIDDEN":null},o=function(a){return a.boundingClientRect&&(a.boundingClientRect.height===0||a.boundingClientRect.width===0)};function a(a){var b,e,f,g=arguments,h,p,q=a.onHidden,r=a.onIntersection,s=a.onVisibilityDurationUpdated,t=a.onVisible,u=a.options,v=u===void 0?{}:u,w=a.threshold,x=(b=v.hiddenWhenZeroArea)!=null?b:!1,y=(e=v.hiddenWhenCSSStyleHidden)!=null?e:!1,z=(f=v.isEntryInViewport)!=null?f:c("intersectionObserverEntryIsIntersecting"),A=m(null),B=m(!1),C=m(null),D=m(null),E=m(null),F=j(c("HiddenSubtreePassiveContext")),G=v.activityMonitorOverride!==void 0?v.activityMonitorOverride:c("CometVisibilityUserActivityMonitor"),H=i(function(a){if(G&&!G.isUserActive())return"USER_INACTIVE";var b=F.getCurrentState();if(b.hidden)return"PUSH_VIEW_HIDDEN";if(b.backgrounded)return"BACKGROUNDED";if(B.current===!1)return"NOT_IN_VIEWPORT";if(x===!0&&o(a))return"TARGET_SIZE_0";if(y===!0){b=n(a);if(b!==null)return b}return null},[G,F,y,x]),I=i(function(a){return H(a)===null},[H]),J=i(function(a,b,c){var d=A.current!=null;if(!d&&c){var e=Date.now();A.current=e;t!=null&&b!=null&&t({entry:b,visibleTime:e})}else if(d&&!c){d=(e=A.current)!=null?e:0;c=Date.now();if(q!=null){e=a||b&&H(b)||"UNKNOWN";q({entry:b,hiddenReason:e,hiddenTime:c,visibleDuration:c-d,visibleTime:d})}A.current=null}},[H,q,s,t]),K=m(J);k(function(){K.current=J},[J]);c("useDoubleEffectHack_DO_NOT_USE_THIS_IS_TRACKED")(function(){return function(){K.current("COMPONENT_UNMOUNTED",null,!1),C.current!=null&&(C.current(),C.current=null),E.current!=null&&(E.current.remove(),E.current=null),D.current!=null&&(D.current.remove(),D.current=null)}},[]);var L=i(function(a){c("nullIntersectionObserverEntryLogger")(a,"IntersectionObserverEntry is null. num_arguments="+g.length);var b=B.current=z(a);r&&r({entry:a,isElementVisible:I(a)});C.current==null?b&&(C.current=G&&G.subscribe(function(b){return K.current("USER_INACTIVE",a,I(a))}),E.current=F.subscribeToChanges(function(b){return K.current(b.hidden?"PUSH_VIEW_HIDDEN":"BACKGROUNDED",a,I(a))}),D.current!=null&&D.current.remove(),D.current=d("Run").onBeforeUnload(function(a){K.current("PAGE_UNLOAD",null,!1)},!1)):b||(C.current!=null&&(C.current(),C.current=null),E.current&&(E.current.remove(),E.current=null),D.current!=null&&(D.current.remove(),D.current=null));K.current(null,a,I(a))},[I,G,F,z,r]),M=j(c("BaseViewportMarginsContext")),N=l(function(){return{bottom:M.bottom+1,left:M.left+1,right:M.right+1,top:M.top+1}},[M.bottom,M.left,M.right,M.top]),O=(h=v.root)!=null?h:null,P=(p=v.rootMargin)!=null?p:c("getIntersectionMarginFromViewportMargin")(N);return c("useIntersectionObserver")(L,{root:O,rootMargin:P,scrollMargin:v.scrollMargin,threshold:w})}g["default"]=a}),98);
__d("useVisibilityObserver",["react-compiler-runtime","useViewportDuration"],(function(a,b,c,d,e,f,g){"use strict";b=0;e=[0,.25,.5,.75,1];f=[0,.05,.1,.15,.2,.25,.3,.35,.4,.45,.5,.55,.6,.65,.7,.75,.8,.85,.9,.95,1];var h={EXPENSIVE:f,LITE:b,MODERATE:e};function a(a){var b,e=d("react-compiler-runtime").c(7),f=a.onHidden,g=a.onIntersection,i=a.onVisibilityDurationUpdated,j=a.onVisible;a=a.options;b=h[(b=a==null?void 0:a.thresholdOverride)!=null?b:"LITE"];var k;e[0]!==f||e[1]!==g||e[2]!==i||e[3]!==j||e[4]!==a||e[5]!==b?(k={onHidden:f,onIntersection:g,onVisibilityDurationUpdated:i,onVisible:j,options:a,threshold:b},e[0]=f,e[1]=g,e[2]=i,e[3]=j,e[4]=a,e[5]=b,e[6]=k):k=e[6];return c("useViewportDuration")(k)}g["default"]=a}),98);
__d("useGlimmerPausedState",["react","react-compiler-runtime","usePartialViewImpression"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useCallback;var i=b.useState;function a(){var a=d("react-compiler-runtime").c(6),b=i(!0),e=b[0],f=b[1];a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=function(a){a=a.hiddenReason;a!=="COMPONENT_UNMOUNTED"&&f(!0)},a[0]=b):b=a[0];b=b;var g;a[1]===Symbol["for"]("react.memo_cache_sentinel")?(g=function(){f(!1)},a[1]=g):g=a[1];g=g;a[2]===Symbol["for"]("react.memo_cache_sentinel")?(b={onImpressionEnd:b,onImpressionStart:g},a[2]=b):b=a[2];g=c("usePartialViewImpression")(b);a[3]!==g||a[4]!==e?(b={paused:e,ref:g},a[3]=g,a[4]=e,a[5]=b):b=a[5];return b}g["default"]=a}),98);
__d("BaseGlimmer.react",["BaseGlimmerCompatStyles","BaseLoadingStateElement.react","react","useGlimmerPausedState"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=a.children,e=a.index,f=a.iteration,g=a.variant;a=a.xstyle;var h=c("useGlimmerPausedState")(),j=h.paused;h=h.ref;return i.jsx(c("BaseLoadingStateElement.react"),{ref:h,xstyle:g==null?[d("BaseGlimmerCompatStyles").styles.root,j&&d("BaseGlimmerCompatStyles").styles.paused,a,d("BaseGlimmerCompatStyles").styles.animationDelay(e),f!=null&&d("BaseGlimmerCompatStyles").styles.animationIterationCount(f)]:[g.xstyleConfig.container,g.xstyleConfig.animation,g.xstyleConfig.animationDelay,g.xstyleConfig.animationIteration],children:b})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("BaseHeadingContextWrapper.react",["BaseHeadingContext","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useContext;function a(a){var b=d("react-compiler-runtime").c(3);a=a.children;var e=j(c("BaseHeadingContext"));e=e+1;var f;b[0]!==a||b[1]!==e?(f=i.jsx(c("BaseHeadingContext").Provider,{value:e,children:a}),b[0]=a,b[1]=e,b[2]=f):f=b[2];return f}g["default"]=a}),98);
__d("BaseModal.react",["cr:17228","cr:20294","react"],(function(a,b,c,d,e,f,g){"use strict";var h;h||d("react");a=b("cr:20294")!=null?b("cr:20294"):b("cr:17228");g["default"]=a}),98);
__d("BaseMultiPageViewContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext(null);g["default"]=b}),98);
__d("BasePopoverReflowSheetContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b={isReflowSheet:!1};c=a.createContext(b);g["default"]=c}),98);
__d("CometAsyncFetchError",[],(function(a,b,c,d,e,f){"use strict";function g(a){if(a==null)return"";if(typeof a==="string")return a;try{return String.fromCharCode.apply(null,new Uint16Array(a))}catch(a){return"<error parsing ArrayBuffer>"}}a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d,e,f){var g;g=a.call(this,b)||this;g.errorMsg=b;g.errorCode=c;g.errorRawResponseHeaders=d;g.errorRawTransport=e;g.errorType=f;return g}var c=b.prototype;c.toString=function(){var a;a=((a=this.errorCode)!=null?a:"")+"."+g(this.errorMsg)+"."+((a=this.errorRawResponseHeaders)!=null?a:"")+"."+((a=this.errorRawTransport)!=null?a:"")+"."+((a=this.errorType)!=null?a:"")+"."+((a=this.errorRawTransportStatus)!=null?a:"");return"CometAyncFetchError: "+a};return b}(babelHelpers.wrapNativeSuper(Error));f["default"]=a}),66);
__d("CometAsyncFetchResponse",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.$1=a,this.$2=this.$3(b)}var b=a.prototype;b.getFullResponsePayload=function(){return this.$1};b.getResponsePayload=function(){var a;return(a=this.$1)==null?void 0:a.payload};b.getResponseHeader=function(a){var b;return(b=this.$2)==null?void 0:b.get(a.toLowerCase())};b.getAllResponseHeadersMap=function(){return new Map(this.$2)};b.$3=function(a){if(a==null||a.length===0)return null;var b=new Map();a=a.split("\r\n");for(a of a){var c=a.indexOf(": ");if(c<=0)continue;var d=a.substring(0,c).toLowerCase();c=a.substring(c+2);var e=b.get(d);e=e!=null?e+", "+c:c;b.set(d,e)}return b};return a}();f["default"]=a}),66);
__d("CometDebounce",["clearTimeout","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){b=b===void 0?{}:b;var d=b.leading,e=b.wait,f=!0,g;function h(){for(var b=arguments.length,i=new Array(b),j=0;j<b;j++)i[j]=arguments[j];var k;if(d===!0){k=function(){f=!0,g=null};if(!f){c("clearTimeout")(g);g=c("setTimeout")(k,e);return}f=!1;a.apply(void 0,i)}else h.reset(),k=function(){g=null,a.apply(void 0,i)};g=c("setTimeout")(k,e)}h.isPending=function(){return g!=null};h.reset=function(){c("clearTimeout")(g),g=null,f=!0};return h}g["default"]=a}),98);
__d("CometDialogTransitionTypes",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum")({Enter:"comet-dialog-enter",Exit:"comet-dialog-exit"});c=a;f["default"]=c}),66);
__d("CometEventListener",["unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b,d,e){if(a.addEventListener){a.addEventListener(b,d,e);return{remove:function(){a.removeEventListener(b,d,e)}}}else throw c("unrecoverableViolation")('Attempted to listen to eventType "'+b+'" on a target that does not have addEventListener.',"comet_ui")}a={addListenerWithOptions:function(a,b,c,d){return h(a,b,c,d)},bubbleWithPassiveFlag:function(a,b,c,d){return h(a,b,c,{capture:!1,passive:d})},capture:function(a,b,c){return h(a,b,c,!0)},captureWithPassiveFlag:function(a,b,c,d){return h(a,b,c,{capture:!0,passive:d})},listen:function(a,b,c){return h(a,b,c,!1)},registerDefault:function(a,b){throw c("unrecoverableViolation")("EventListener.registerDefault is not implemented.","comet_ui")},suppress:function(a){a.preventDefault(),a.stopPropagation()}};g["default"]=a}),98);
__d("CometInteractionTracingQPLConfigContext",["qpl","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));f=h;var j=f.useContext;f.useMemo;f={dialogTraceQPLEvent:c("qpl")._(30605361,"6204"),popoverTraceQPLEvent:c("qpl")._(30605361,"6204")};var k=i.createContext(f);function a(){return j(k).dialogTraceQPLEvent}function b(){return j(k).popoverTraceQPLEvent}function e(a){var b=d("react-compiler-runtime").c(6),c=a.children,e=a.dialogTraceQPLEvent;a=a.popoverTraceQPLEvent;var f;b[0]!==e||b[1]!==a?(f={dialogTraceQPLEvent:e,popoverTraceQPLEvent:a},b[0]=e,b[1]=a,b[2]=f):f=b[2];e=f;b[3]!==c||b[4]!==e?(a=i.jsx(k.Provider,{value:e,children:c}),b[3]=c,b[4]=e,b[5]=a):a=b[5];return a}g.defaultInteractionQPLEvents=f;g.useDialogTraceQPLEvent=a;g.usePopoverTraceQPLEvent=b;g.CometInteractionTracingQPLConfigContextProvider=e}),98);
__d("JstlMigrationFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1814852");b=d("FalcoLoggerInternal").create("jstl_migration",a);e=b;g["default"]=e}),98);
__d("getDataWithLoggerOptions",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){return babelHelpers["extends"]({},a,{__options:babelHelpers["extends"]({event_time:Date.now()/1e3},b)})}f["default"]=a}),66);
__d("generateLiteTypedLogger",["Banzai","JstlMigrationFalcoEvent","getDataWithLoggerOptions"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b,d){var e=a.split(":")[0],f=a.split(":")[1];e=="logger"?c("JstlMigrationFalcoEvent").log(function(){return{logger_config_name:f,payload:b}}):c("Banzai").post(a,b,d)}function a(a){return{log:function(b,d){h(a,c("getDataWithLoggerOptions")(b,d),c("Banzai").BASIC)},logVital:function(b,d){h(a,c("getDataWithLoggerOptions")(b,d),c("Banzai").VITAL)},logImmediately:function(b,d){h(a,c("getDataWithLoggerOptions")(b,d),{signal:!0})}}}g["default"]=a}),98);
__d("CometKeyCommandWidget",["createKeyCommandWidget"],(function(a,b,c,d,e,f,g){"use strict";a=c("createKeyCommandWidget")();g["default"]=a}),98);
__d("TrackingNodeConstants",[],(function(a,b,c,d,e,f){"use strict";a=58;b=126;c=69;d=42;e=47;var g=6,h=100,i=33,j=38,k=(g+1)*c,l="__tn__";f.BASE_CODE_START=a;f.BASE_CODE_END=b;f.BASE_CODE_SIZE=c;f.PREFIX_CODE_START=d;f.PREFIX_CODE_END=e;f.PREFIX_CODE_SIZE=g;f.ENCODE_NUMBER_MAX=h;f.ENCODED_STRING_WITH_TWO_SYMBOLS_PREFIX_CODE=i;f.ENCODED_STRING_WITH_THREE_SYMBOLS_PREFIX_CODE=j;f.TOTAL_IDS_SUPPORTED_BY_LEGACY_ENCODING=k;f.TN_URL_PARAM=l}),66);
__d("decodeTrackingNode",["TrackingNodeConstants"],(function(a,b,c,d,e,f,g){"use strict";function a(a){if(a.length===0)return[0];var b=function(a,b,e){var c=0;for(var f=b;f<e+b;f+=1){if(!(f<a.length&&a.charCodeAt(f)>=d("TrackingNodeConstants").BASE_CODE_START&&a.charCodeAt(f)<=d("TrackingNodeConstants").BASE_CODE_END))return null;c=c*d("TrackingNodeConstants").BASE_CODE_SIZE+(a.charCodeAt(f)-d("TrackingNodeConstants").BASE_CODE_START)}return c},c=function(a,c){if(c>=a.length)return[null,c];var e=c,f=null,g=0;switch(a.charCodeAt(0)){case d("TrackingNodeConstants").ENCODED_STRING_WITH_TWO_SYMBOLS_PREFIX_CODE:f=b(a,c,2);g=d("TrackingNodeConstants").TOTAL_IDS_SUPPORTED_BY_LEGACY_ENCODING;e+=2;break;case d("TrackingNodeConstants").ENCODED_STRING_WITH_THREE_SYMBOLS_PREFIX_CODE:f=b(a,c,3);g=d("TrackingNodeConstants").TOTAL_IDS_SUPPORTED_BY_LEGACY_ENCODING+Math.pow(d("TrackingNodeConstants").BASE_CODE_SIZE,2);e+=3;break;default:return[null,c]}return f===null?[null,c]:[g+((a=f)!=null?a:0)+1,e]},e=a.charCodeAt(0),f=1,g=0,h=0,i=0;if([d("TrackingNodeConstants").ENCODED_STRING_WITH_TWO_SYMBOLS_PREFIX_CODE,d("TrackingNodeConstants").ENCODED_STRING_WITH_THREE_SYMBOLS_PREFIX_CODE].includes(e)){var j;c=c(a,f);if(c[0]===null)return[0];i=(j=c[0])!=null?j:-1;f=c[1]}else{if(e>=d("TrackingNodeConstants").PREFIX_CODE_START&&e<=d("TrackingNodeConstants").PREFIX_CODE_END){if(a.length===1)return[0];h=e-d("TrackingNodeConstants").PREFIX_CODE_START+1;g=a.charCodeAt(1);f=2}else h=0,g=e;if(g<d("TrackingNodeConstants").BASE_CODE_START||g>d("TrackingNodeConstants").BASE_CODE_END)return[0];i=h*d("TrackingNodeConstants").BASE_CODE_SIZE+(g-d("TrackingNodeConstants").BASE_CODE_START)+1}if(a.length>f+2&&a.charAt(f)==="#"&&a.charAt(f+1)>="0"&&a.charAt(f+1)<="9"&&a.charAt(f+2)>="0"&&a.charAt(f+2)<="9")return[f+3,[i,parseInt(a.charAt(f+1)+a.charAt(f+2),10)+1]];return a.length>f&&a.charAt(f)>="0"&&a.charAt(f)<="9"?[f+1,[i,parseInt(a.charAt(f),10)+1]]:[f,[i]]}g["default"]=a}),98);
__d("getRouteInfoForCometProductAttributionDispatch",["FBLogger"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){if(b!=null)switch(b){case"rootView":return a.main;case"hostedView":b=a.hosted;if(b)return b;c("FBLogger")("comet_infra").mustfix("Navigation was dispatched from hostedView, but no hosted route in previous state");break;case"pushView":b=a.pushViewStack;if(b&&b.length>0){b=b[b.length-1];b.depth;b.key;b=babelHelpers.objectWithoutPropertiesLoose(b,["depth","key"]);return b}c("FBLogger")("comet_infra").mustfix("Navigation was dispatched from pushView, but no push view route in previous state");break}return a.main}g["default"]=a}),98);
__d("CometProductAttribution",["Random","WebSession","decodeTrackingNode","getRouteInfoForCometProductAttributionDispatch","getTopMostRouteInfo"],(function(a,b,c,d,e,f,g){"use strict";var h=function(a){var b=a.bookmark_id,c=a.bookmark_type_name,e=a.link_context,f=a.tap_point;a=a.trace_policy;return{bookmark_id:b,bookmark_type_name:c,link_context:e,session:d("WebSession").getId(),subsession:1,tap_point:f,timestamp:Date.now(),trace_policy:a}},i=function(a){var b=a.bookmark_id,c=a.link_context,e=a.navChainContent,f=a.rootName,g=a.tap_point,h=a.tracePolicy;a=a.trackingNodes;return{bookmark_id:b,"class":f,link_context:c,module:h,navChainContent:e,scope_id:Math.floor(d("Random").random()*1e6),tap_point:g,tracking_nodes:a,ts:Date.now()}},j=function(a){var b=a.productAttributionId;a=a.tracePolicy;if(typeof b==="string")return b;return typeof a==="string"?"tp-"+a:"missing"},k=new Set(["create_jewel","mega_menu","tap_tabbar","tap_search_bar","tap_bookmark","tap_community_panel_popover","tap_community_panel_shortcuts","topnav-link","logo","via_notification"]),l=function(a){return k.has(a)},m=function(a,b){var c="mp_listing";return a==="via_cold_start"&&((a=b.entityKeyConfig)==null?void 0:a.entity_type.value)===c},n=function(a,b){var c={"class":"",module:"inboundClick"};return[].concat(a,[babelHelpers["extends"]({},a[a.length-1],{"class":c["class"],module:c.module,navChainContent:b})])};a=function(a,b,c,d,e,f,g){var k;f===void 0&&(f=!1);k=(k=c==null?void 0:c.route)!=null?k:{};k=k.tracePolicy;b=typeof b==="string"?{tap_point:b}:b!=null?b:{tap_point:"unexpected"};var o=b.bookmark_id!=null?String(b.bookmark_id):j(a);k=h({bookmark_id:o,bookmark_type_name:(o=b==null?void 0:b.bookmark_type_name)!=null?o:"",link_context:d,tap_point:b.tap_point,trace_policy:k!=null?k:(o=a.tracePolicy)!=null?o:"missing"});a.productAttributionId!=null&&b.bookmark_id!=null&&a.productAttributionId!==String(b.bookmark_id)&&(k=babelHelpers["extends"]({},k,{route_bookmark_id:a.productAttributionId}));o=[i({bookmark_id:b.bookmark_id!=null?String(b.bookmark_id):a.productAttributionId,link_context:d,navChainContent:null,rootName:(o=a.rootView.resource)==null?void 0:o.getModuleId(),tap_point:b.tap_point,tracePolicy:(d=a.tracePolicy)!=null?d:"missing",trackingNodes:null})];if(c!=null&&!l(b.tap_point))if(f&&c.productAttribution.v2!=null){d=[].concat(c.productAttribution.v2);d[0]=o[0];o=d}else{f=c.productAttribution.v2;if(f!=null){d=f[0];c=f.slice(1);o=[].concat(o,[babelHelpers["extends"]({},d,{navChainContent:g!=null?g:null,tracking_nodes:e!=null?e:null})],c)}o.length>10&&(o=[].concat(o.slice(0,9),[o[o.length-1]]))}else if(m(b.tap_point,a)){o=n(o,String((f=a.params)==null?void 0:f.listing_id))}return{0:k,v2:o}};var o=function(a){return a.replace(/,;/g,"_")},p=function(a){return(a=a==null?void 0:(a=a.v2)==null?void 0:a.map(function(a){var b;return[(b=a["class"])!=null?b:"",a.module,a.tap_point,a.ts.toString(),a.scope_id.toString(),(b=a.bookmark_id)!=null?b:"",((b=a.tracking_nodes)!=null?b:[]).reduce(function(a,b){b=c("decodeTrackingNode")(b);return b.length===1?a:a.concat(b[1][0])},[]).join("#"),(b=a.navChainContent)!=null?b:""].map(o).join()}).join(";"))!=null?a:""};b=function(a){return a!=null?p((a=c("getTopMostRouteInfo")(a()))==null?void 0:a.productAttribution):null};e=function(a,b){if(a==null)return null;a=(a=c("getTopMostRouteInfo")(a()))==null?void 0:a.productAttribution.v2;if(a==null)return null;a.length!==0&&(a[0].tracking_nodes=b);return p({v2:a})};g.getProductAttributionEntry=h;g.getProductAttributionEntryV2=i;g.getProductAttributionIdFromRoute=j;g.isSpecialTapPoint=l;g.isMarketplacePDPColdStart=m;g.insertInboundClickEntryWithContentForColdStart=n;g.getProductAttributionFromRoute=a;g.filterEntryValue=o;g.minifyProductAttributionV2=p;g.getMinifiedTopMostRouteProductAttribution=b;g.minifiedNavigationChainWithTrackingNodes=e;g.getRouteInfoForDispatch=c("getRouteInfoForCometProductAttributionDispatch")}),98);
__d("CometRouterRouteTracePolicyContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext();g["default"]=b}),98);
__d("CometSuspenseFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1828945");b=d("FalcoLoggerInternal").create("comet_suspense",a);e=b;g["default"]=e}),98);
__d("CometTrackingNodeAbstractViewHierarchyWrapperContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;h||(h=d("react"));a=h.createContext;b=a(void 0);c=b;g["default"]=c}),98);
__d("encodeTrackingNode",["TrackingNodeConstants"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){var c=function(a){return Math.pow(d("TrackingNodeConstants").BASE_CODE_SIZE,a)},e=function(a,b){var c="";a=a;b=b;while(b>0){var e=a%d("TrackingNodeConstants").BASE_CODE_SIZE;c=String.fromCharCode(d("TrackingNodeConstants").BASE_CODE_START+e)+c;a=parseInt(a/d("TrackingNodeConstants").BASE_CODE_SIZE,10);b-=1}return c},f=function(a){a=a-d("TrackingNodeConstants").TOTAL_IDS_SUPPORTED_BY_LEGACY_ENCODING-1;if(a<c(2))return String.fromCharCode(d("TrackingNodeConstants").ENCODED_STRING_WITH_TWO_SYMBOLS_PREFIX_CODE)+e(a,2);a-=c(2);return String.fromCharCode(d("TrackingNodeConstants").ENCODED_STRING_WITH_THREE_SYMBOLS_PREFIX_CODE)+e(a,3)},g=(a-1)%d("TrackingNodeConstants").BASE_CODE_SIZE,h=parseInt((a-1)/d("TrackingNodeConstants").BASE_CODE_SIZE,10);if(a<1||a>(d("TrackingNodeConstants").PREFIX_CODE_SIZE+1)*d("TrackingNodeConstants").BASE_CODE_SIZE+Math.pow(d("TrackingNodeConstants").BASE_CODE_SIZE,2)+Math.pow(d("TrackingNodeConstants").BASE_CODE_SIZE,3))throw Error("Invalid tracking node: "+a);var i="";h>d("TrackingNodeConstants").PREFIX_CODE_SIZE?i+=f(a):(h>0&&(i+=String.fromCharCode(h-1+d("TrackingNodeConstants").PREFIX_CODE_START)),i+=String.fromCharCode(g+d("TrackingNodeConstants").BASE_CODE_START));b!==void 0&&b>0&&(b>10&&(i+="#"),i+=parseInt(Math.min(b,d("TrackingNodeConstants").ENCODE_NUMBER_MAX)-1,10));return i}g["default"]=a}),98);
__d("useCometTrackingNodes",["CometTrackingNodesContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("CometTrackingNodesContext"))}g["default"]=a}),98);
__d("CometTrackingNodeProvider.react",["CometTrackingNodeAbstractViewHierarchyWrapperContext","CometTrackingNodesContext","encodeTrackingNode","react","react-compiler-runtime","useCometTrackingNodes"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));b=h;var j=b.useContext;b.useMemo;function a(a){var b=d("react-compiler-runtime").c(13),e=a.children,f=a.index;a=a.trackingNode;var g=c("useCometTrackingNodes")(),h;bb0:{if(a==null){h=g;break bb0}var k;b[0]!==f||b[1]!==a?(k=c("encodeTrackingNode")(a,f),b[0]=f,b[1]=a,b[2]=k):k=b[2];f=k;b[3]!==g||b[4]!==f?(a=[f].concat(g),b[3]=g,b[4]=f,b[5]=a):a=b[5];h=a}k=h;g=e;f=j(c("CometTrackingNodeAbstractViewHierarchyWrapperContext"));if(f!=null){b[6]!==f||b[7]!==e||b[8]!==k?(a=f(k,e),b[6]=f,b[7]=e,b[8]=k,b[9]=a):a=b[9];g=a}b[10]!==g||b[11]!==k?(h=i.jsx(c("CometTrackingNodesContext").Provider,{value:k,children:g}),b[10]=g,b[11]=k,b[12]=h):h=b[12];return h}g["default"]=a}),98);
__d("CookieStore",["CookieCoreLoggingConfig","FBLogger","Random","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i=window.I_AM_CORE_COOKIE_INFRASTRUCTURE_AND_NEED_TO_ACCESS_COOKIES!=null?window.I_AM_CORE_COOKIE_INFRASTRUCTURE_AND_NEED_TO_ACCESS_COOKIES():null,j={set:function(a){document.cookie=a},get:function(){return document.cookie}};function k(){return i!=null?i:j}function l(a,b,c,d,e,f,g,h){return b+"="+encodeURIComponent(c)+"; "+(f!==0&&f!==void 0&&f!==null?"expires="+new Date(a+f).toUTCString()+"; ":"")+"path="+d+"; domain="+e+(g?"; secure":"")+(h?"; SameSite="+h:"")}function m(a,b,c){return a+"=; expires=Thu, 01-Jan-1970 00:00:01 GMT; path="+b+"; domain="+c}function n(){if(c("CookieCoreLoggingConfig").sampleRate>0){var a=(h||(h=c("performanceNow")))(),b=k().get();a=h()-a;var d=a>c("CookieCoreLoggingConfig").maximumIgnorableStallMs&&c("Random").coinflip(1/c("CookieCoreLoggingConfig").sampleRate);d&&c("FBLogger")("cookie_infra").addMetadata("COOKIE_INFRA","WALL_TIME",String(a)).warn("Cookie read exceeded %s milliseconds.",c("CookieCoreLoggingConfig").maximumIgnorableStallMs);return b}else return k().get()}var o=function(){function a(){this.$1=0}var b=a.prototype;b.setCookie=function(a,b,c,d,e,f,g,h){k().set(l(a,b,c,d,e,f,g,h))};b.clearCookie=function(a,b,c){k().set(m(a,b,c))};b.getCookie=function(a){var b;this.$1++;b=(b=n())==null?void 0:b.match("(?:^|;\\s*)"+a+"=(.*?)(?:;|$)");return b?decodeURIComponent(b[1]):null};return a}(),p=10*1e3;b=function(){function a(){this.$1={},this.$2=0,this.$3=0,this.$4=0}var b=a.prototype;b.setCookie=function(a,b,c,d,e,f,g,h){k().set(l(a,b,c,d,e,f,g,h)),this.$1[b]={value:c,updated:a}};b.clearCookie=function(a,b,c){k().set(m(a,b,c)),this.$1[a]={value:null,updated:Date.now()}};b.getCookie=function(a){a=this.$5(a);a=a.cookie;return a};b.$5=function(a){var b=Date.now(),c=this.$1[a];if(!c){if(this.$2+p<b){this.$6();return{cookie:this.$5(a).cookie,hit:!1}}this.$3++;return{cookie:null,hit:!0}}if(c.updated+p<b){this.$6();return{cookie:this.$5(a).cookie,hit:!1}}this.$3++;return{cookie:c.value,hit:!0}};b.$6=function(){var a;this.$4++;a=(a=(a=n())==null?void 0:a.split(";"))!=null?a:[];this.$2=Date.now();this.$1={};for(a of a){var b=a.match("\\s*([^=]+)=(.*)");if(!b)continue;this.$1[b[1]]={value:decodeURIComponent(b[2]),updated:this.$2}}};return a}();function a(){return new o()}g.newCookieStore=a;g.CookieCacheForTest=b;g.CookieStoreSlowForTest=o}),98);
__d("JsCrossSiteCookieUsageFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("6476");b=d("FalcoLoggerInternal").create("js_cross_site_cookie_usage",a);e=b;g["default"]=e}),98);
__d("CookieCore",["CookieCoreConfig","CookieDomain","CookiePrivacySandboxConfig","CookieStore","JsCrossSiteCookieUsageFalcoEvent","err","justknobx"],(function(a,b,c,d,e,f,g){var h=/_js_(.*)/,i;function j(){i||(i=d("CookieStore").newCookieStore());return i}function k(){return"."+c("CookieDomain").domain}function l(a){return window.self!==window.top?!1:!0}function m(a,b){if(!r(a))return;n(a,b,t(a),u(a),s(a),v(a));w(a,1)}function a(a,b,c){if(!r(a))return;n(a,b,t(a),u(a),s(a),v(a),c)}function n(a,b,c,d,e,f,g){var h=Date.now();c=c;if(c!=null)if(c>h)c-=h;else if(c===1){o(a,d,g);return}j().setCookie(h,a,b,d,g!=null?g:k(),c,e,f)}function b(a,b){if(!l(a))return;m(a,b)}function e(a,b,c,d,e,f){if(!l(a))return;n(a,b,c,d,e,null,f)}function o(a,b,c){b===void 0&&(b="/");b=b||"/";j().clearCookie(a,b,c!=null?c:k());w(a,2)}function f(a){if(!r(a))return null;w(a,0);return j().getCookie(a)}function p(a){return{insecure:a.i||!1,path:a.p||"/",ttlSeconds:a.t||0,sameSite:a.s||"None"}}function q(a){if(c("CookieCoreConfig")[a]!==void 0)return p(c("CookieCoreConfig")[a]);a=a.match(h);return a&&a.length>1?q(a[1]):null}function r(a){return q(a)!==null}function s(a){a=q(a);return a==null?!0:!a.insecure}function t(a){a=q(a);return a==null?null:a.ttlSeconds*1e3}function u(a){a=q(a);return a==null?"/":a.path}function v(a){a=q(a);return a==null||a.sameSite==null?null:a.sameSite}function w(a,b){var e=d("CookiePrivacySandboxConfig").is_affected_by_samesite_lax;e&&c("justknobx")._("2552")&&c("JsCrossSiteCookieUsageFalcoEvent").log(function(){return{cookie_name:a,cookie_op:b,js_backtrace:c("err")("read cookie backtrace: ").stack}})}g.set=m;g.setWithDomain_FOR_MESSENGER_LS_ONLY=a;g.setWithoutChecks=n;g.setIfFirstPartyContext=b;g.setWithoutChecksIfFirstPartyContext=e;g.clear=o;g.get=f}),98);
__d("Cookie",["CookieConsent","CookieCore","InitialCookieConsent","ODS"],(function(a,b,c,d,e,f,g){var h,i,j;function k(a){if(!(j||(j=c("CookieConsent"))).hasFirstPartyConsent()){(h||(h=d("ODS"))).bumpEntityKey(798,"defer_cookies","set."+a);return!1}return!0}function l(){return!(i||(i=c("InitialCookieConsent"))).noCookies}function a(a,b){if(!l()||!k(a))return;d("CookieCore").set(a,b)}function b(a,b){if(!l())return;d("CookieCore").set(a,b)}function e(a,b,c,e,f,g){if(!l()||!k(a))return;d("CookieCore").setWithoutChecks(a,b,c,e,f,null,g)}a={clear:(f=d("CookieCore")).clear,get:f.get,set:a,setIfFirstPartyContext:f.setIfFirstPartyContext,setWithoutCheckingUserConsent_DANGEROUS:b,setWithoutChecks:e,setWithoutChecksIfFirstPartyContext:f.setWithoutChecksIfFirstPartyContext};g["default"]=a}),98);
__d("CurrentAppID",["CurrentUserInitialData"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(){return(h||(h=c("CurrentUserInitialData"))).APP_ID}g.getAppID=a}),98);
__d("CurrentUser",["Cookie","CurrentUserInitialData"],(function(a,b,c,d,e,f){var g,h={getID:function(){return(g||(g=b("CurrentUserInitialData"))).USER_ID},getAccountID:function(){return(g||(g=b("CurrentUserInitialData"))).ACCOUNT_ID},getPossiblyNonFacebookUserID:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).NON_FACEBOOK_USER_ID)!=null?a:this.getID()},getEIMU:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).IG_USER_EIMU)!=null?a:"0"},getEmployeeWorkUserID:function(){return(g||(g=b("CurrentUserInitialData"))).WORK_USER_ID},getName:function(){return(g||(g=b("CurrentUserInitialData"))).NAME},getShortName:function(){return(g||(g=b("CurrentUserInitialData"))).SHORT_NAME},getAbraID:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).ABRA_ID)!=null?a:"0"},getAbraStorageID:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).ABRA_STORAGE_ID)!=null?a:"0"},getARID:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).AR_ID)!=null?a:"0"},getEPOU:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).EPOU_ID)!=null?a:"0"},getEOCPU:function(){var a;return(a=(g||(g=b("CurrentUserInitialData"))).EOCPU_ID)!=null?a:"0"},isLoggedIn:function(){return h.getPossiblyNonFacebookUserID()!=="0"},isLoggedInNow:function(){var a;if(!h.isLoggedIn())return!1;if((g||(g=b("CurrentUserInitialData"))).IS_INTERN_SITE)return!0;if((g||(g=b("CurrentUserInitialData"))).IS_ABRA_USER||(g||(g=b("CurrentUserInitialData"))).IS_ENTERPRISE_USER||(g||(g=b("CurrentUserInitialData"))).IS_IMAGINE_USER||(g||(g=b("CurrentUserInitialData"))).IS_INSTAGRAM_USER||(g||(g=b("CurrentUserInitialData"))).IS_META_SPARK_USER||(g||(g=b("CurrentUserInitialData"))).IS_OCULUS_USER||(g||(g=b("CurrentUserInitialData"))).IS_TOGETHER_APP_USER||(g||(g=b("CurrentUserInitialData"))).IS_WORK_MESSENGER_CALL_GUEST_USER||(g||(g=b("CurrentUserInitialData"))).IS_WORK_USER||(g||(g=b("CurrentUserInitialData"))).IS_WORKROOMS_USER||(g||(g=b("CurrentUserInitialData"))).IS_ANONYMOUS_CASTING_USER)return!0;if((g||(g=b("CurrentUserInitialData"))).ORIGINAL_USER_ID!=null&&(g||(g=b("CurrentUserInitialData"))).ORIGINAL_USER_ID!="")return(g||(g=b("CurrentUserInitialData"))).ORIGINAL_USER_ID===b("Cookie").get("c_user");return(g||(g=b("CurrentUserInitialData"))).IS_BUSINESS_DOMAIN===!0?(g||(g=b("CurrentUserInitialData"))).USER_ID==b("Cookie").get("c_user"):(g||(g=b("CurrentUserInitialData"))).USER_ID===((a=b("Cookie").get("i_user"))!=null?a:b("Cookie").get("c_user"))},isEmployee:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_EMPLOYEE},isContingentWorker:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_CONTINGENT},isTestUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_TEST_USER},hasWorkUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).HAS_WORK_USER},isWorkUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_WORK_USER},isWorkroomsUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_WORKROOMS_USER},isGray:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_GRAY},isUnderage:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_UNDERAGE},isManagedMetaAccount:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_MANAGED_META_ACCOUNT},isMessengerOnlyUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_MESSENGER_ONLY_USER},isDeactivatedAllowedOnMessenger:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_DEACTIVATED_ALLOWED_ON_MESSENGER},isMessengerCallGuestUser:function(){return!!(g||(g=b("CurrentUserInitialData"))).IS_MESSENGER_CALL_GUEST_USER},isBusinessPersonAccount:function(){return(g||(g=b("CurrentUserInitialData"))).IS_BUSINESS_PERSON_ACCOUNT},hasSecondaryBusinessPerson:function(){return(g||(g=b("CurrentUserInitialData"))).HAS_SECONDARY_BUSINESS_PERSON},getAppID:function(){return(g||(g=b("CurrentUserInitialData"))).APP_ID},isFacebookWorkAccount:function(){return(g||(g=b("CurrentUserInitialData"))).IS_FACEBOOK_WORK_ACCOUNT},isInstagramBusinessPerson:function(){return(g||(g=b("CurrentUserInitialData"))).IS_INSTAGRAM_BUSINESS_PERSON},getPageMessagingMailboxId:function(){var a;return String((a=(g||(g=b("CurrentUserInitialData"))).PAGE_MESSAGING_MAILBOX_ID)!=null?a:"0")}};e.exports=h}),null);
__d("DataStore",["DataStoreConfig","gkx","isEmpty"],(function(a,b,c,d,e,f){"use strict";var g,h=b("DataStoreConfig").expandoKey,i=b("DataStoreConfig").useExpando,j=b("gkx")("25572")&&window.WeakMap?new window.WeakMap():null,k={},l=1;function m(a){if(typeof a==="string")return"str_"+a;else{var b;return"elem_"+((b=a.__FB_TOKEN)!=null?b:a.__FB_TOKEN=[l++])[0]}}function n(a){if(j!=null&&typeof a==="object"){j.get(a)===void 0&&j.set(a,{});return j.get(a)}else if(i&&typeof a==="object")return a[h]||(a[h]={});a=m(a);return k[a]||(k[a]={})}var o={set:function(a,b,c){if(!a)throw new TypeError("DataStore.set: namespace is required, got "+typeof a);var d=n(a);d[b]=c;return a},get:function(a,b,c){if(!a)throw new TypeError("DataStore.get: namespace is required, got "+typeof a);var d=n(a),e=d[b];if(e===void 0&&a.getAttribute!=null)if(a.hasAttribute!=null&&!a.hasAttribute("data-"+b))e=void 0;else{a=a.getAttribute("data-"+b);e=a===null?void 0:a}c!==void 0&&e===void 0&&(e=d[b]=c);return e},remove:function(a,c){if(!a)throw new TypeError("DataStore.remove: namespace is required, got "+typeof a);var d=n(a),e=d[c];delete d[c];(g||(g=b("isEmpty")))(d)&&o.purge(a);return e},purge:function(a){if(j!=null&&typeof a==="object")return j["delete"](a);else i&&typeof a==="object"?delete a[h]:delete k[m(a)]},_storage:k};e.exports=o}),null);
__d("Deferred",["Promise"],(function(a,b,c,d,e,f){"use strict";var g;(g||(g=b("Promise"))).resolve();a=function(){function a(a){var c=this;a=a||g||(g=b("Promise"));this.$1=!1;this.$2=new a(function(a,b){c.$3=a,c.$4=b})}var c=a.prototype;c.getPromise=function(){return this.$2};c.resolve=function(a){this.$1=!0,this.$3(a)};c.reject=function(a){this.$1=!0,this.$4(a)};c.isSettled=function(){return this.$1};return a}();f["default"]=a}),66);
__d("EventEmitterWithValidation",["BaseEventEmitter"],(function(a,b,c,d,e,f){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;d=a.call(this)||this;d.$EventEmitterWithValidation1=Object.keys(b);d.$EventEmitterWithValidation2=Boolean(c);return d}var c=b.prototype;c.emit=function(b){if(this.$EventEmitterWithValidation1.indexOf(b)===-1){if(this.$EventEmitterWithValidation2)return;throw new TypeError(g(b,this.$EventEmitterWithValidation1))}return a.prototype.emit.apply(this,arguments)};return b}(b("BaseEventEmitter"));function g(a,b){a='Unknown event type "'+a+'". ';a+="Known event types: "+b.join(", ")+".";return a}e.exports=a}),null);
__d("FBJSON",[],(function(a,b,c,d,e,f){a=JSON.parse;b=JSON.stringify;f.parse=a;f.stringify=b}),66);
__d("FBNucleusCrossFilled24Icon.react",["react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){return i.jsxs("svg",babelHelpers["extends"]({viewBox:"0 0 24 24",width:"1em",height:"1em",fill:"currentColor"},a,{children:[a.title!=null&&i.jsx("title",{children:a.title}),a.children!=null&&i.jsx("defs",{children:a.children}),i.jsx("path",{d:"M19.884 5.884a1.25 1.25 0 0 0-1.768-1.768L12 10.232 5.884 4.116a1.25 1.25 0 1 0-1.768 1.768L10.232 12l-6.116 6.116a1.25 1.25 0 0 0 1.768 1.768L12 13.768l6.116 6.116a1.25 1.25 0 0 0 1.768-1.768L13.768 12l6.116-6.116z"})]}))}a.displayName=a.name+" [from "+f.id+"]";a._isSVG=!0;b=a;g["default"]=b}),98);
__d("FDSGlimmer.react",["BaseGlimmer.react","MetaConfig","react","useCurrentDisplayMode"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={dark_DEPRECATED:{backgroundColor:"xhzw6zf",$$css:!0},glimmer:{backgroundColor:"x113zjs7 xwyycs4",$$css:!0},glimmerAnimation:{animationName:"x43zylw xzvp1bk",$$css:!0},light_DEPRECATED:{backgroundColor:"x1vtvx1t",$$css:!0}};function a(a){var b=a.xstyle;a=babelHelpers.objectWithoutPropertiesLoose(a,["xstyle"]);var d=c("useCurrentDisplayMode")();d=d==="dark"?j.dark_DEPRECATED:j.light_DEPRECATED;var e=[j.glimmer,j.glimmerAnimation];return i.jsx(c("BaseGlimmer.react"),babelHelpers["extends"]({xstyle:[c("MetaConfig")._("73")?e:d,b]},a))}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("FDSGradientBorderContainer.react",[],(function(a,b,c,d,e,f){"use strict";function a(a){a=a.children;return a}f["default"]=a}),66);
__d("FbtLogging",["cr:1094907","cr:8828"],(function(a,b,c,d,e,f,g){"use strict";a=b("cr:1094907")==null?void 0:b("cr:1094907").logImpression;c=b("cr:8828")==null?void 0:b("cr:8828").logImpressionV2;g.logImpression=a;g.logImpressionV2=c}),98);
__d("IdleCallbackImplementation",["performanceNow","requestAnimationFramePolyfill"],(function(a,b,c,d,e,f,g){var h,i=[],j=0,k=0,l=-1,m=!1,n=1e3/60,o=2;function p(a){return a}function q(a){return a}function b(b,c){var d=k++;i[d]=b;s();if(c!=null&&c.timeout>0){var e=p(d);a.setTimeout(function(){return y(e)},c.timeout)}return p(d)}function r(a){a=q(a);i[a]=null}function s(){m||(m=!0,c("requestAnimationFramePolyfill")(function(a){m=!1,u((h||(h=c("performanceNow")))()-a)}))}function t(a){var b=n-o;if(a<b)return b-a;a=a%n;if(a>b||a<o)return 0;else return b-a}function u(a){var b=(h||(h=c("performanceNow")))();if(b>l){a=t(a);if(a>0){b=b+a;x(b);l=b}}v()&&s()}function v(){return j<i.length}function w(){while(v()){var a=i[j];j++;if(a)return a}return null}function x(a){var b;while((h||(h=c("performanceNow")))()<a&&(b=w()))b(new z(a))}function y(a){var b=q(a);b=i[b];b&&(r(a),b(new z(null)))}var z=function(){function a(a){this.didTimeout=a==null,this.$1=a}var b=a.prototype;b.timeRemaining=function(){var a=this.$1;if(a!=null){var b=(h||(h=c("performanceNow")))();if(b<a)return a-b}return 0};return a}();g.requestIdleCallback=b;g.cancelIdleCallback=r}),98);
__d("IntlQtEventFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1848815");b=d("FalcoLoggerInternal").create("intl_qt_event",a);e=b;g["default"]=e}),98);
__d("Log",[],(function(a,b,c,d,e,f){"use strict";var g=-1;b={DEBUG:3,INFO:2,WARNING:1,ERROR:0};c=function(a,b,c){for(var d=arguments.length,e=new Array(d>3?d-3:0),f=3;f<d;f++)e[f-3]=arguments[f];var h=0,i=c.replace(/%s/g,function(){return String(e[h++])}),j=window.console;j&&g>=b&&j[a in j?a:"log"](i)};function a(a){g=a}d=c.bind(null,"debug",b.DEBUG);e=c.bind(null,"info",b.INFO);var h=c.bind(null,"warn",b.WARNING),i=c.bind(null,"error",b.ERROR);f.Level=b;f.log=c;f.setLevel=a;f.debug=d;f.info=e;f.warn=h;f.error=i}),66);
__d("LogBrowserCrashReportsLowerRetentionFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("856");b=d("FalcoLoggerInternal").create("log_browser_crash_reports_lower_retention",a);e=b;g["default"]=e}),98);
__d("LogJsspStatsFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("6181");b=d("FalcoLoggerInternal").create("log_jssp_stats",a);e=b;g["default"]=e}),98);
__d("PlaybackSpeedExperiments",["CurrentUser","gkx"],(function(a,b,c,d,e,f,g){"use strict";function h(){return c("gkx")("26216")}function i(){return!1}function a(){return h()||i()?!0:!1}function j(){return i()?!1:!1}function b(){return c("CurrentUser").getID()!=="0"}function d(){if(h())return!1;if(i())return!0;return j()?!1:!1}function e(){return!0}g.enableWwwPlaybackSpeedControl=a;g.isInCometHeadroomTest=j;g.enableCometPlaybackSpeedControl=b;g.enableCometPlaybackSpeedControlNUX=d;g.enablePlaybackSpeedLogging=e}),98);
__d("ResourceTimingStore",["performance"],(function(a,b,c,d,e,f,g){"use strict";var h,i=4e3,j=3e3,k=new Map(),l=!1;function m(){var a=Array.from(k.entries());k=new Map(a.slice(-j))}function n(a){var b=a.indexOf("#");return b===-1?a:a.slice(0,b)}function o(a){for(a of a){if(!(a instanceof PerformanceResourceTiming))continue;var b="";try{b=new URL(a.name).pathname}catch(a){}if(!/\.(css|js)$/.test(b))continue;b=a;if(!(b!=null&&typeof b==="object"&&typeof b.encodedBodySize==="number"&&typeof b.decodedBodySize==="number"&&typeof b.transferSize==="number"))continue;k.set(n(a.name),b)}k.size>i&&m()}function p(a){o(a.getEntries())}function q(){if(l)return;l=!0;var a;if(typeof PerformanceObserver!=="undefined"){a=new PerformanceObserver(p);try{a.observe({buffered:!0,type:"resource"})}catch(a){}}typeof (h||(h=c("performance"))).getEntriesByType==="function"&&o((h||(h=c("performance"))).getEntriesByType("resource"))}function a(a){q();return k.get(n(a))}g.init=q;g.getEntryForURL=a}),98);
__d("TrustedTypesNoOpPolicy_DO_NOT_USE",["TrustedTypes","TrustedTypesUtils"],(function(a,b,c,d,e,f,g){"use strict";a={createScriptURL:d("TrustedTypesUtils").noop,createHTML:d("TrustedTypesUtils").noop,createScript:d("TrustedTypesUtils").noop};b=c("TrustedTypes").createPolicy("noop-do-not-use",a);e=b;g["default"]=e}),98);
__d("VersionRange",["invariant"],(function(a,b,c,d,e,f,g,h){"use strict";var i=/\./,j=/\|\|/,k=/\s+\-\s+/,l=/^(<=|<|=|>=|~>|~|>|)?\s*(.+)/,m=/^(\d*)(.*)/;function n(a,b){a=a.split(j);if(a.length>1)return a.some(function(a){return E.contains(a,b)});else return o(a[0].trim(),b)}function o(a,b){a=a.split(k);a.length>0&&a.length<=2||h(0,11800);if(a.length===1)return p(a[0],b);else{var c=a[0];a=a[1];y(c)&&y(a)||h(0,11801);return p(">="+c,b)&&p("<="+a,b)}}function p(a,b){a=a.trim();if(a==="")return!0;b=b.split(i);a=w(a);var c=a.modifier;a=a.rangeComponents;switch(c){case"<":return q(b,a);case"<=":return r(b,a);case">=":return t(b,a);case">":return u(b,a);case"~":case"~>":return v(b,a);default:return s(b,a)}}function q(a,b){return D(a,b)===-1}function r(a,b){a=D(a,b);return a===-1||a===0}function s(a,b){return D(a,b)===0}function t(a,b){a=D(a,b);return a===1||a===0}function u(a,b){return D(a,b)===1}function v(a,b){var c=b.slice();b=b.slice();b.length>1&&b.pop();var d=b.length-1,e=parseInt(b[d],10);x(e)&&(b[d]=e+1+"");return t(a,c)&&q(a,b)}function w(a){a=a.split(i);var b=a[0].match(l);b||h(0,3074);return{modifier:b[1],rangeComponents:[b[2]].concat(a.slice(1))}}function x(a){return!isNaN(a)&&isFinite(a)}function y(a){return!w(a).modifier}function z(a,b){for(var c=a.length;c<b;c++)a[c]="0"}function A(a,b){a=a.slice();b=b.slice();z(a,b.length);for(var c=0;c<b.length;c++){var d=b[c].match(/^[x*]$/i);if(d){b[c]=a[c]="0";if(d[0]==="*"&&c===b.length-1)for(d=c;d<a.length;d++)a[d]="0"}}z(b,a.length);return[a,b]}function B(a,b){var c=a.match(m),d=b.match(m);c=c&&c[1];d=d&&d[1];c=parseInt(c,10);d=parseInt(d,10);if(x(c)&&x(d)&&c!==d)return C(c,d);else return C(a,b)}function C(a,b){typeof a===typeof b||h(0,11802);if(typeof a==="string"&&typeof b==="string")if(a>b)return 1;else if(a<b)return-1;else return 0;if(typeof a==="number"&&typeof b==="number")if(a>b)return 1;else if(a<b)return-1;else return 0;typeof a===typeof b||h(0,11802);return 0}function D(a,b){a=A(a,b);b=a[0];a=a[1];for(var c=0;c<a.length;c++){var d=B(b[c],a[c]);if(d)return d}return 0}var E={contains:function(a,b){return n(a.trim(),b.trim())}};a=E;g["default"]=a}),98);
__d("UserAgent",["UserAgentData","VersionRange","memoizeStringOnly"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b,d,e){if(a===d)return!0;if(!d.startsWith(a))return!1;d=d.slice(a.length);if(b!=null){d=e?e(d):d;return c("VersionRange").contains(d,b)}return!1}function i(a){return c("UserAgentData").platformName==="Windows"?a.replace(/^\s*NT/,""):a}b={isBrowser:(a=c("memoizeStringOnly"))(function(a){return h(c("UserAgentData").browserName,c("UserAgentData").browserFullVersion,a)}),isBrowserArchitecture:a(function(a){return h(c("UserAgentData").browserArchitecture,null,a)}),isDevice:a(function(a){return h(c("UserAgentData").deviceName,null,a)}),isEngine:a(function(a){return h(c("UserAgentData").engineName,c("UserAgentData").engineVersion,a)}),isEngine_DEPRECATED_DANGEROUS:a(function(a){return h(c("UserAgentData").engineName,c("UserAgentData").engineVersion,a)}),isPlatform:a(function(a){return h(c("UserAgentData").platformName,c("UserAgentData").platformFullVersion,a,i)}),isPlatformArchitecture:a(function(a){return h(c("UserAgentData").platformArchitecture,null,a)})};d=b;g["default"]=d}),98);
__d("VideoPlaybackQuality",[],(function(a,b,c,d,e,f){function a(a){if(typeof a.getVideoPlaybackQuality==="function")return a.getVideoPlaybackQuality().droppedVideoFrames;a=a.webkitDroppedFrameCount;return typeof a==="number"?a:0}function b(a){if(typeof a.getVideoPlaybackQuality==="function")return a.getVideoPlaybackQuality().totalVideoFrames;a=a.webkitDecodedFrameCount;return typeof a==="number"?a:0}f.getDroppedFrames=a;f.getTotalFrames=b}),66);
__d("isSSR",["ExecutionEnvironment","XPlatReactEnvironment"],(function(a,b,c,d,e,f,g){"use strict";var h;a=d("XPlatReactEnvironment").isWeb()&&!(h||(h=c("ExecutionEnvironment"))).canUseDOM;b=a;g["default"]=b}),98);
__d("VideoPlayerConnectionQuality",["isSSR"],(function(a,b,c,d,e,f,g){"use strict";var h={POOR:"POOR",MODERATE:"MODERATE",GOOD:"GOOD",EXCELLENT:"EXCELLENT"},i=[{bandwidth:5e5,connectionQuality:h.POOR},{bandwidth:2e6,connectionQuality:h.MODERATE},{bandwidth:1e7,connectionQuality:h.GOOD}],j=100,k=null,l=null;a=function(a){if(c("isSSR"))return"MODERATE";if(k!==null&&l!==null&&k>=Date.now()-j)return l;a=a();var b=null;if(a!=null)for(var d=0;d<i.length;d++)if(a<i[d].bandwidth){b=i[d].connectionQuality;break}b===null&&(b=h.EXCELLENT);k=Date.now();l=b;return b};g.evaluate=a}),98);
__d("VideoPlayerContextSensitiveConfigUtils",[],(function(a,b,c,d,e,f){"use strict";var g=function(a,b){return b.every(function(b){return a[b.name]===b.value})};a=function(a,b){return b.find(function(b){return g(a,b.contexts)})};f.getFirstMatchingValueAndContextTargets=a}),66);
__d("VideoPlayerContextSensitiveConfigResolver",["VideoPlayerContextSensitiveConfigPayload","VideoPlayerContextSensitiveConfigUtils","cr:1724253"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){this.$1={},this.$2={},a==null?(this.$3=c("VideoPlayerContextSensitiveConfigPayload").static_values,this.$4=c("VideoPlayerContextSensitiveConfigPayload").context_sensitive_values):(this.$3=a.staticValues,this.$4=a.contextSensitiveValues)}var e=a.prototype;e.setContexts=function(a){this.$1=a,this.$2=this.$5(a)};e.getValue=function(a){if(this.$2[a]!=null)return this.$2[a];return this.$3[a]!=null?this.$3[a]:null};e.$5=function(a){var b=this;return Object.keys(this.$4).reduce(function(c,e){var f=b.$4[e];if(f!=null){f=d("VideoPlayerContextSensitiveConfigUtils").getFirstMatchingValueAndContextTargets(a,f);f!=null&&(c[e]=f.value)}return c},{})};a.getPayload=function(){return c("VideoPlayerContextSensitiveConfigPayload")};a.getSources=function(){return b("cr:1724253")};return a}();g["default"]=a}),98);
__d("VideoPlayerDashPerformanceLoggerEventFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1962341");b=d("FalcoLoggerInternal").create("video_player_dash_performance_logger_event",a);e=b;g["default"]=e}),98);
__d("VideoPlayerImplementationErrorNormalization",[],(function(a,b,c,d,e,f){"use strict";function a(a){var b=a.getType(),c=a.getDescription();switch(b){case"OZ_NETWORK":b=(b=(b=a.getExtra())==null?void 0:b.code)!=null?b:"0";b=parseInt(b,10);return b<100||b>599?"NetworkError":"HTTPError";case"OZ_SOURCE_BUFFER_QUOTA_EXCEEDED":a=(b=a.getExtra())==null?void 0:b.mimeType;if(a!=null&&a.indexOf("video")>-1)return"VideoDecodeError";else if(a!=null&&a.indexOf("audio")>-1)return"AudioDecodeError";else return"GenericDecodeError";case"OZ_XML_PARSER":case"OZ_MPD_PARSER":case"OZ_REPRESENTATION_PARSER":return"ManifestParseError";case"OZ_DRM_MANAGER":case"OZ_INITIALIZATION":if(/(OZ_XML_PARSER)|(OZ_MPD_PARSER)|(OZ_REPRESENTATION_PARSER)/.test(c))return"ManifestParseError";else return"RuntimeError";case"OZ_JAVASCRIPT_NATIVE":case"OZ_SOURCE_BUFFER":case"OZ_STREAM_APPEND_QUOTA_EXCEEDED_HANDLER_ERROR":case"OZ_NETWORK_REQUEST_STREAM_RETRY_HANDLER_ERROR":return"RuntimeError"}return null}function b(a,b){switch(a){case"OZ_XML_PARSER":case"OZ_MPD_PARSER":case"OZ_REPRESENTATION_PARSER":return"ManifestParseError";case"OZ_DRM_MANAGER":case"OZ_INITIALIZATION":if(/(OZ_XML_PARSER)|(OZ_MPD_PARSER)|(OZ_REPRESENTATION_PARSER)/.test(b))return"ManifestParseError";else return"RuntimeError";case"OZ_JAVASCRIPT_NATIVE":case"OZ_SOURCE_BUFFER":case"OZ_STREAM_APPEND_QUOTA_EXCEEDED_HANDLER_ERROR":case"OZ_NETWORK_REQUEST_STREAM_RETRY_HANDLER_ERROR":return"RuntimeError";case"BUFFERING_TIMEOUT":return"RuntimeError";default:b=[[["MEDIA_ERR_NETWORK"],"NetworkError"],[["MEDIA_ERR_ABORTED","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_ERR_UNKNOWN"],"GenericDecodeError"]];for(b of b){var c=b[0],d=b[1];if(c.some(function(b){return a.includes(b)}))return d}}return null}f.getVideoPlayerNormalizedErrorTypeFromOzError=a;f.getErrorTypeFromErrorName=b}),66);
__d("VideoPlayerShakaGlobalConfig",["VideoPlayerContextSensitiveConfigResolver"],(function(a,b,c,d,e,f,g){var h=new(c("VideoPlayerContextSensitiveConfigResolver"))(),i={};a=function(a){i=a};b=function(a,b){if(!!i&&typeof i[a]==="boolean")return i[a];a=h.getValue(a);return a!=null&&typeof a==="boolean"?a:b};d=function(a,b){if(!!i&&typeof i[a]==="number")return i[a];a=h.getValue(a);return a!=null&&typeof a==="number"?a:b};e=function(a,b){if(!!i&&typeof i[a]==="string")return i[a];a=h.getValue(a);return a!=null&&typeof a==="string"?a:b};g.setGlobalOverrideConfig=a;g.getBool=b;g.getNumber=d;g.getString=e}),98);
__d("XHRHttpError",[],(function(a,b,c,d,e,f){"use strict";var g="HTTP_CLIENT_ERROR",h="HTTP_PROXY_ERROR",i="HTTP_SERVER_ERROR",j="HTTP_TRANSPORT_ERROR",k="HTTP_UNKNOWN_ERROR";function a(a,b){if(b===0){a=a.getProtocol();return a==="file"||a==="ftp"?null:j}else if(b>=100&&b<200)return h;else if(b>=200&&b<300)return null;else if(b>=400&&b<500)return g;else if(b>=500&&b<600)return i;else if(b>=12001&&b<12156)return j;else return k}f.HTTP_CLIENT_ERROR=g;f.HTTP_PROXY_ERROR=h;f.HTTP_SERVER_ERROR=i;f.HTTP_TRANSPORT_ERROR=j;f.HTTP_UNKNOWN_ERROR=k;f.getErrorCode=a}),66);
__d("getCrossOriginTransport",["invariant","ExecutionEnvironment","err"],(function(a,b,c,d,e,f,g){var h;function i(){if(!(h||(h=b("ExecutionEnvironment"))).isInBrowser)throw b("err")("getCrossOriginTransport: %s","Cross origin transport unavailable in the server environment.");try{var a=new XMLHttpRequest();!("withCredentials"in a)&&typeof XDomainRequest!=="undefined"&&(a=new XDomainRequest());return a}catch(a){throw b("err")("getCrossOriginTransport: %s",a.message)}}i.withCredentials=function(){var a=i();"withCredentials"in a||g(0,5150);var b=a.open;a.open=function(){b.apply(this,arguments),this.withCredentials=!0};return a};e.exports=i}),null);
__d("ZeroRewrites",["URI","ZeroRewriteRules","getCrossOriginTransport","getSameOriginTransport","isFacebookURI"],(function(a,b,c,d,e,f){var g,h={rewriteURI:function(a){if(!b("isFacebookURI")(a)||h._isWhitelisted(a))return a;var c=h._getRewrittenSubdomain(a);c!==null&&c!==void 0&&(a=a.setSubdomain(c));return a},getTransportBuilderForURI:function(a){return h.isRewritten(a)?b("getCrossOriginTransport").withCredentials:b("getSameOriginTransport")},isRewriteSafe:function(a){if(Object.keys(b("ZeroRewriteRules").rewrite_rules).length===0||!b("isFacebookURI")(a))return!1;var c=h._getCurrentURI().getDomain(),d=new(g||(g=b("URI")))(a).qualify().getDomain();return c===d||h.isRewritten(a)},isRewritten:function(a){a=a.getQualifiedURI();if(Object.keys(b("ZeroRewriteRules").rewrite_rules).length===0||!b("isFacebookURI")(a)||h._isWhitelisted(a))return!1;var c=a.getSubdomain(),d=h._getCurrentURI(),e=h._getRewrittenSubdomain(d);return a.getDomain()!==d.getDomain()&&c===e},_isWhitelisted:function(a){a=a.getPath();a.endsWith("/")||(a+="/");return b("ZeroRewriteRules").whitelist&&b("ZeroRewriteRules").whitelist[a]===1},_getRewrittenSubdomain:function(a){a=a.getQualifiedURI().getSubdomain();return b("ZeroRewriteRules").rewrite_rules[a]},_getCurrentURI:function(){return new(g||(g=b("URI")))("/").qualify()}};e.exports=h}),null);
__d("getAsyncHeaders",["BDHeaderConfig","LSD","ZeroCategoryHeader","isFacebookURI","requireWeak"],(function(a,b,c,d,e,f,g){function a(a){var b={},d=c("isFacebookURI")(a);d&&c("ZeroCategoryHeader").value&&(b[c("ZeroCategoryHeader").header]=c("ZeroCategoryHeader").value);d=h(a);d&&(b["X-FB-LSD"]=d);d=i(a);d&&(b["X-ASBD-ID"]=d);c("requireWeak")("MessengerPWAVersionForUserAgent",function(c){c=c();c!=null&&!j(a)&&(b["x-fb-pwa"]=""+c)});return b}function h(a){return j(a)?null:c("LSD").token}function i(a){return j(a)?null:d("BDHeaderConfig").ASBD_ID}function j(a){var b;b=(b=(b=k())==null?void 0:(b=b.location)==null?void 0:b.origin)!=null?b:(b=window)==null?void 0:(b=b.location)==null?void 0:b.origin;return b==null?!0:!a.toString().startsWith("/")&&a.getOrigin()!==b}function k(){if(typeof document!=="undefined")return document;else return null}g["default"]=a}),98);
__d("xhrSimpleDataSerializer",[],(function(a,b,c,d,e,f){"use strict";function a(a){var b=[];for(var c in a)b.push(encodeURIComponent(c)+"="+encodeURIComponent(a[c]));return b.join("&")}f["default"]=a}),66);
__d("XHRRequest",["invariant","DTSGUtils","Env","ErrorGuard","FBLogger","LSD","Log","NetworkStatus","ResourceTimingsStore","ResourceTypes","SprinkleConfig","TimeSlice","URI","XHRHttpError","ZeroRewrites","cr:8959","cr:8960","fb-error","getAsyncHeaders","performance","xhrSimpleDataSerializer"],(function(a,b,c,d,e,f,g){var h,i,j,k,l=b("fb-error").ErrorXFBDebug,m=!1,n=!1,o={loadedBytes:0,totalBytes:0};function p(a){return b("ZeroRewrites").rewriteURI(new(h||(h=b("URI")))(a))}a=function(){"use strict";function a(a){this.$3=function(){return null},this.$20=p(a),this.$7="POST",this.$6={},this.setResponseType(null),this.setTransportBuilder(b("ZeroRewrites").getTransportBuilderForURI(this.getURI())),this.setDataSerializer(b("xhrSimpleDataSerializer")),this.$12=b("ResourceTimingsStore").getUID(b("ResourceTypes").XHR,a!=null?a.toString():"")}var c=a.prototype;c.setURI=function(a){this.$20=p(a);return this};c.getURI=function(){return this.$20};c.setResponseType=function(a){this.$14=a;return this};c.setMethod=function(a){this.$7=a;return this};c.getMethod=function(){return this.$7};c.setData=function(a){this.$2=a;return this};c.getData=function(){return this.$2};c.setRawData=function(a){this.$11=a;return this};c.setRequestHeader=function(a,b){this.$6[a]=b;return this};c.setTimeout=function(a){this.$15=a;return this};c.getTimeout=function(){return this.$15};c.setResponseHandler=function(a){this.$13=a;return this};c.getResponseHandler=function(){return this.$13};c.setErrorHandler=function(a){this.$5=a;return this};c.getErrorHandler=function(){return this.$5};c.setNetworkFailureHandler=function(a){this.$8=a;return this};c.getNetworkFailureHandler=function(){return this.$8};c.getResponseHeader=function(a){var b=this.$9;return b?b.getResponseHeader(a):null};c.setAbortHandler=function(a){this.$1=a;return this};c.getAbortHandler=function(){return this.$1};c.setTimeoutHandler=function(a){this.$16=a;return this};c.getTimeoutHandler=function(){return this.$16};c.setUploadProgressHandler=function(a){this.$19=a;return this};c.setDownloadProgressHandler=function(a){this.$4=a;return this};c.setTransportBuilder=function(a){!this.$18||!b("ZeroRewrites").isRewritten(this.$20)?this.$18=a:b("FBLogger")("iorg-FOS").blameToPreviousFile().mustfix("can not set new TransportBuilder for the request %s",String(this.getURI()));return this};c.setDataSerializer=function(a){this.$3=a;return this};c.setWithCredentials=function(a){this.$21=a;return this};c.send=function(){var a=this.$15,c=this.$18;if(!c)return;var d=c();c=this.getURI();if(c.toString().includes("/../")||c.toString().includes("/..\\")||c.toString().includes("\\../")||c.toString().includes("\\..\\")){b("Log").error("XHRRequest.send(): path traversal is not allowed.");return!1}if(m===!0)return;var e=new(h||(h=b("URI")))(c).getQualifiedURI().toString(),f=this.$12;b("ResourceTimingsStore").updateURI(b("ResourceTypes").XHR,f,e);b("ResourceTimingsStore").measureRequestSent(b("ResourceTypes").XHR,f);this.$9=d;this.$7==="POST"||!this.$11||g(0,2346,this.$11,c);e=(i||(i=b("Env"))).force_param;e&&(this.$2=babelHelpers["extends"]({},this.getData()||{},{},e));if(this.$7==="GET"&&b("DTSGUtils").shouldAppendToken(c)){e=b("cr:8960").getCachedToken?b("cr:8960").getCachedToken():b("cr:8960").getToken();e&&(this.$2?this.$2.fb_dtsg_ag=e:this.$2={fb_dtsg_ag:e},b("SprinkleConfig").param_name&&(this.$2[b("SprinkleConfig").param_name]=b("DTSGUtils").getNumericValue(e)))}if(this.$7==="POST"&&b("DTSGUtils").shouldAppendToken(c)){e=b("cr:8959").getCachedToken?b("cr:8959").getCachedToken():b("cr:8959").getToken();e&&(this.$2?this.$2.fb_dtsg=e:this.$2={fb_dtsg:e},b("SprinkleConfig").param_name&&(this.$2[b("SprinkleConfig").param_name]=b("DTSGUtils").getNumericValue(e)));b("LSD").token&&(this.$2?this.$2.lsd=b("LSD").token:this.$2={lsd:b("LSD").token},b("SprinkleConfig").param_name&&!e&&(this.$2[b("SprinkleConfig").param_name]=b("DTSGUtils").getNumericValue(b("LSD").token)))}this.$7==="GET"||this.$11?(c.addQueryData(this.$2),e=this.$11):e=this.$3(this.$2);function j(a){b("ResourceTimingsStore").measureResponseReceived(b("ResourceTypes").XHR,f);for(var c=arguments.length,d=new Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];a.apply(this,d)}j=b("TimeSlice").guard(j,"XHRRequest response received",{propagationType:b("TimeSlice").PropagationType.CONTINUATION});d.onreadystatechange=this.$22(j);d.onerror=this.$23(j);d.upload&&this.$19&&(d.upload.onprogress=this.$24.bind(this));this.$4&&(d.onprogress=this.$25.bind(this));a&&(this.$17=setTimeout(this.$26.bind(this),a));this.$21!=null&&(d.withCredentials=this.$21);d.open(this.$7,c.toString(),!0);j=!1;if(this.$6)for(a in this.$6)a.toLowerCase()==="content-type"&&(j=!0),d.setRequestHeader(a,this.$6[a]);this.$7=="POST"&&!this.$11&&!j&&d.setRequestHeader("Content-Type","application/x-www-form-urlencoded");var k=b("getAsyncHeaders")(c);Object.keys(k).forEach(function(a){d.setRequestHeader(a,k[a])});this.$14==="arraybuffer"&&("responseType"in d?d.responseType="arraybuffer":"overrideMimeType"in d?d.overrideMimeType("text/plain; charset=x-user-defined"):"setRequestHeader"in d&&d.setRequestHeader("Accept-Charset","x-user-defined"));this.$14==="blob"&&(d.responseType=this.$14);d.send(e)};c.abort=function(a){this.$27(),this.$1&&(j||(j=b("ErrorGuard"))).applyWithGuard(this.$1,null,[a],{name:"XHRRequest:_abortHandler"})};c.$27=function(){var a=this.$9;a&&(a.onreadystatechange=null,a.abort());this.$28()};c.$26=function(){this.$27(),this.$16&&(j||(j=b("ErrorGuard"))).applyWithGuard(this.$16,null,[],{name:"XHRRequest:_abortHandler"})};c.$29=function(a){if(this.$14)if("response"in a)return a.response;else if(this.$14==="arraybuffer"&&window.VBArray)return window.VBArray(a.responseBody).toArray();return a.responseText};c.$23=function(a){var c=this,d=this.$9;return function(){var e;e={errorCode:b("XHRHttpError").HTTP_TRANSPORT_ERROR,errorMsg:"Network Failure.",errorType:"Network",errorRawResponseHeaders:null,errorRawTransport:d==null?void 0:(e=d.constructor)==null?void 0:e.name,errorRawTransportStatus:0};c.$8?(j||(j=b("ErrorGuard"))).applyWithGuard(a.bind(void 0,c.$8),null,[e],{name:"XHRRequest:_networkFailureHandler"}):a(function(){});b("NetworkStatus").reportError()}};c.$22=function(a){var c=this,d=b("TimeSlice").guard(function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];return a.apply(this,c)},"XHRRequest onreadystatechange",{propagationType:b("TimeSlice").PropagationType.EXECUTION});return function(){var e=c.$9;if(e==null)return;var f=e.readyState;if(f>=2){var g=f===4;g&&l.addFromXHR(e);var h=c.getURI();h=b("XHRHttpError").getErrorCode(h,e.status);var i=c.$13;if(h!=null){if(g){var m=2*60*1e3-500;h==="HTTP_TRANSPORT_ERROR"&&c.$10&&c.$10+m<(k||(k=b("performance"))).now()&&(h="HTTP_STREAM_TIMEOUT");m={errorCode:h,errorMsg:c.$29(e),errorRawTransport:e.constructor.name,errorRawTransportStatus:e.status,errorRawResponseHeaders:i&&i.includeHeaders?e.getAllResponseHeaders():null,errorType:e.status?"HTTP "+e.status:"HTTP"};c.$5?(j||(j=b("ErrorGuard"))).applyWithGuard(a.bind(void 0,c.$5),null,[m],{name:"XHRRequest:_errorHandler"}):a(function(){})}}else if(i){if(g||i.parseStreaming&&f===3){m=g?a:d;f=(i==null?void 0:i.includeHeaders)?e.getAllResponseHeaders():null;e=c.$29(e);c.$10=(k||(k=b("performance"))).now();var o=200*1e3*1e3;i.parseStreaming&&typeof e==="string"&&e.length>o&&!n&&(n=!0,b("FBLogger")("comet_infra").mustfix("Streaming reponse exceeding size limits and will OOM soon: %s",String(c.getURI())));(j||(j=b("ErrorGuard"))).applyWithGuard(m.bind(void 0,i),null,[e,f,g],{name:"XHRRequest:handler"})}}else g&&a(function(){});g&&(h!="HTTP_TRANSPORT_ERROR"&&b("NetworkStatus").reportSuccess(),c.$28())}}};c.$24=function(a){o.loadedBytes=a.loaded,o.totalBytes=a.total,this.$19&&(j||(j=b("ErrorGuard"))).applyWithGuard(this.$19,null,[o],{name:"XHRRequest:_uploadProgressHandler"})};c.$25=function(a){a={loadedBytes:a.loaded,totalBytes:a.total};this.$4&&(j||(j=b("ErrorGuard"))).applyWithGuard(this.$4,null,[a],{name:"XHRRequest:_downloadProgressHandler"})};c.$28=function(){clearTimeout(this.$17),delete this.$9};a.disable=function(){m=!0};return a}();e.exports=a}),null);
__d("bumpVultureJSHash",["ODS"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){(h||(h=d("ODS"))).bumpEntityKey(7506,"vulture_js",a,b)}g["default"]=a}),98);
__d("structuredClone",[],(function(a,b,c,d,e,f){"use strict";b=(a=window)==null?void 0:a.structuredClone;f["default"]=b}),66);
__d("isArDotMetaDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)ar\\.meta\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("isWorkDotMetaDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)work\\.meta\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("isWorkroomsDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)workrooms\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("getCometAsyncFetchResponse",["CSRFGuard","CometAsyncFetchError","CometAsyncFetchResponse","ConstUriUtils","DTSG","DTSG_ASYNC","NetworkStatus","PHPQuerySerializer","Promise","XHRRequest","cometAsyncRequestHeaders","getAsyncParams","handleCometErrorCodeSideEffects","isArDotMetaDotComURI","isFacebookURI","isHorizonDotMetaDotComURI","isInstagramURI","isInternalFBURI","isMessengerDotComURI","isMetaAIURI","isWorkDotMetaDotComURI","isWorkplaceDotComURI","isWorkroomsDotComURI","recoverableViolation","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=250;function a(a,e){var f=0,g;return new(i||(i=b("Promise")))(function(b,i){var m,n=function(f,g){if(e.ignoreResponse===!0)return b();var h;f=f.trim();try{d("CSRFGuard").exists(f)&&(f=d("CSRFGuard").clean(f)),h=JSON.parse(f)}catch(b){c("recoverableViolation")('Unable to parse uri "'+a.toString()+'" response. Error: '+(b==null?void 0:b.message)+", response: "+f.substring(0,1e3),"comet_infra");i(b);return}if(l(a)){var j;f=(f=h)==null?void 0:f.dtsgToken;j=(j=h)==null?void 0:j.dtsgAsyncGetToken;f&&d("DTSG").setToken(f);j&&d("DTSG_ASYNC").setToken(j)}if(h.error){c("handleCometErrorCodeSideEffects")(h.error,h.errorSummary,h.errorDescription,h.redirectTo,e.shouldShowErrorDialog);i({error:h.error,errorMsg:h.errorDescription,errorType:h.errorSummary,redirectTo:h.redirectTo});return}return b(new(c("CometAsyncFetchResponse"))(h,g))};n.includeHeaders=!0;function o(a){var b=e.retryCount!=null&&e.retryCount>0&&f<=e.retryCount;if(b)c("setTimeout")(q,j);else{b=new(c("CometAsyncFetchError"))(a.errorMsg,a.errorCode,a.errorRawResponseHeaders,a.errorRawTransport,a.errorType);return i(b)}}function p(){var b=new(c("CometAsyncFetchError"))("Request to "+a+" was aborted",null,null,null,"Abort");return i(b)}function q(){var a;if(((a=e.abortSignal)==null?void 0:a.aborted)===!0)return p();r();s()}function r(){m!=null&&(m.abort(),m=null)}function s(){var b,g=(b=e.requestHeaders)!=null?b:{};Object.assign(g,d("cometAsyncRequestHeaders").getHeaders());b=Object.keys(g).reduce(function(a,b){return a.setRequestHeader(b,g[b])},new(c("XHRRequest"))(a)).setMethod(e.method).setData(babelHelpers["extends"]({},e.data,{},c("getAsyncParams")(e.method,(b=e.skipSRState)!=null?b:!1))).setRawData(e.formData).setResponseHandler(n).setErrorHandler(o).setAbortHandler(p).setUploadProgressHandler(e.onUploadProgress).setDataSerializer((h||(h=c("PHPQuerySerializer"))).serialize);m=b;e.withCredentials===!0&&k(a)&&b.setWithCredentials(!0);b.send();f++}e.abortSignal&&(e.abortSignal.onabort=function(){r()});c("NetworkStatus").isOnline()?q():g=c("NetworkStatus").onChange(function(a){a=a.online;a&&(q(),g.remove())})})}function k(a){a=d("ConstUriUtils").getUri(a);return a==null?!1:c("isMetaAIURI")(a)||c("isFacebookURI")(a)||c("isInstagramURI")(a)||c("isInternalFBURI")(a)||c("isMessengerDotComURI")(a)||c("isWorkplaceDotComURI")(a)||c("isWorkroomsDotComURI")(a)||c("isWorkDotMetaDotComURI")(a)||c("isHorizonDotMetaDotComURI")(a)||c("isArDotMetaDotComURI")(a)}function l(a){a=d("ConstUriUtils").getUri(a);if(a==null)return!1;return!a.getProtocol()&&!a.getDomain()?!0:document.location.origin===a.getOrigin()}g["default"]=a}),98);
__d("cometAsyncFetch",["getCometAsyncFetchResponse"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){b===void 0&&(b={data:{},method:"GET"});return c("getCometAsyncFetchResponse")(a,b).then(function(a){var c;return((c=b)==null?void 0:c.getFullPayload)===!0?a==null?void 0:a.getFullResponsePayload():a==null?void 0:a.getResponsePayload()})}g["default"]=a}),98);
__d("forEachObject",[],(function(a,b,c,d,e,f){"use strict";var g=Object.prototype.hasOwnProperty;function a(a,b,c){for(var d in a){var e=d;g.call(a,e)&&b.call(c,a[e],e,a)}}f["default"]=a}),66);
__d("getPlayerFormatForLogData",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){a=a.isFullscreen;return a===!0?"full_screen":b!=null?b:"inline"}f["default"]=a}),66);
__d("getVideoBrowserTabId",["guid"],(function(a,b,c,d,e,f,g){var h=c("guid")().slice(-8);function a(){return h}g["default"]=a}),98);
__d("isInIframe",[],(function(a,b,c,d,e,f){var g=typeof window!=="undefined"&&window.top!=null&&window!=window.top;function a(){return g}f["default"]=a}),66);
__d("isNode",[],(function(a,b,c,d,e,f){function a(a){var b;b=a!=null?(b=a.ownerDocument)!=null?b:a:document;b=(b=b.defaultView)!=null?b:window;return!!(a!=null&&(typeof b.Node==="function"?a instanceof b.Node:typeof a==="object"&&typeof a.nodeType==="number"&&typeof a.nodeName==="string"))}f["default"]=a}),66);
__d("mixInEventEmitter",["invariant","EventEmitterWithHolding","EventEmitterWithValidation","EventHolder"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a,b,c){b||h(0,3159);var d=a.prototype||a;d.__eventEmitter&&h(0,3160);a=a.constructor;a&&(a===Object||a===Function||h(0,3161));d.__types=babelHelpers["extends"]({},d.__types,{},b);d.__ignoreUnknownEvents=Boolean(c);Object.assign(d,i)}var i={emit:function(a,b,c,d,e,f,g){return this.__getEventEmitter().emit(a,b,c,d,e,f,g)},emitAndHold:function(a,b,c,d,e,f,g){return this.__getEventEmitter().emitAndHold(a,b,c,d,e,f,g)},addListener:function(a,b,c){return this.__getEventEmitter().addListener(a,b,c)},once:function(a,b,c){return this.__getEventEmitter().once(a,b,c)},addRetroactiveListener:function(a,b,c){return this.__getEventEmitter().addRetroactiveListener(a,b,c)},listeners:function(a){return this.__getEventEmitter().listeners(a)},removeAllListeners:function(){this.__getEventEmitter().removeAllListeners()},removeCurrentListener:function(){this.__getEventEmitter().removeCurrentListener()},releaseHeldEventType:function(a){this.__getEventEmitter().releaseHeldEventType(a)},__getEventEmitter:function(){if(!this.__eventEmitter){var a=new(c("EventEmitterWithValidation"))(this.__types,this.__ignoreUnknownEvents),b=new(c("EventHolder"))();this.__eventEmitter=new(c("EventEmitterWithHolding"))(a,b)}return this.__eventEmitter}};g["default"]=a}),98);
__d("once",[],(function(a,b,c,d,e,f){"use strict";function a(a){var b=g(a);for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&(b[c]=a[c]);return b}function g(a){var b=a,c;a=function(){if(b){for(var a=arguments.length,d=new Array(a),e=0;e<a;e++)d[e]=arguments[e];c=b.apply(this,d);b=null}return c};return a}f["default"]=a}),66);
__d("oz-player/shims/www/OzEventEmitterWWW",["BaseEventEmitter"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("BaseEventEmitter")}),98);
__d("oz-player/shims/OzEventEmitter",["oz-player/shims/www/OzEventEmitterWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/OzEventEmitterWWW")}),98);
__d("oz-player/utils/OzError",[],(function(a,b,c,d,e,f){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;c===void 0&&(c=0);d=a.call(this,b.description)||this;d.$OzErrorClass$p_1=b;d.name=b.type;d.message=b.description;b=d.stack;if(!(typeof b==="string"&&b!==""))if(Error.captureStackTrace){var e={};Error.captureStackTrace(e,d.constructor);b=e.stack}else{e=new Error().stack.split("\n");e.splice(/^Error/.test(e[0])?1:0,1);b="Error\n"+e.join("\n")}typeof b==="string"&&b!==""?d.stack=b.split("\n").map(function(a,b){return b===0?d.name+": "+d.message.replace(/\n.*/," (...)"):b-1<c?null:a}).filter(Boolean).join("\n"):d.stack="";return d}var c=b.prototype;c.getExtra=function(){return this.$OzErrorClass$p_1.extra||{}};c.getType=function(){return this.$OzErrorClass$p_1.type};c.getDescription=function(){return this.$OzErrorClass$p_1.description};return b}(babelHelpers.wrapNativeSuper(Error));f.OzErrorClass=a}),66);
__d("oz-player/utils/OzErrorUtils",["oz-player/utils/OzError"],(function(a,b,c,d,e,f,g){function h(a){if(typeof a==="object"&&a!=null&&a.stack===void 0)try{throw a}catch(a){}}function i(a){return a instanceof d("oz-player/utils/OzError").OzErrorClass}function j(a,b){b===void 0&&(b=0);b=new(d("oz-player/utils/OzError").OzErrorClass)(a,1+b);h(b);h((a=a.extra)==null?void 0:a.originalError);return b}function a(a){return j({type:"OZ_CANCELLED",description:"Cancelled: "+a},1)}function b(a){var b=a.description,c=a.requestUrl,d=a.responseStatus,e=a.responseHeaders;a=a.responseBody;return j({type:"OZ_NETWORK",description:b+" Status:"+d+(a!=null?" Body:"+a.length+":"+a.substr(0,200)+(a.length>200?"...":""):""),extra:{code:d.toString(),headers:e,url:c}},1)}function c(a,b){h(a);var c=i(a)?a:null;return j(babelHelpers["extends"]({},b,{type:c?c.getType():b.type,description:b.description+" - "+(!(a instanceof Error)&&typeof a==="object"&&a!=null&&typeof a.message==="string"?a.message:String(a)),extra:babelHelpers["extends"]({},b.extra,{originalError:a})}),1)}function e(a,b){return j({type:a.getType(),description:a.getDescription(),extra:babelHelpers["extends"]({},a.getExtra(),{mimeType:b})})}function f(a){var b,c;i(a)?(b=a.getExtra().code||"",c=a):(b=a.status!=null&&a.status!==""?a.status.toString():"0",c=j({type:"OZ_NETWORK",description:a.message,extra:{originalError:a,code:b,url:a.url}}));return[c,b]}g.isOzError=i;g.createOzError=j;g.createOzCancelledError=a;g.createOzNetworkError=b;g.convertPromiseRejectionReasonToOzError=c;g.getOzErrorWithMIMEType=e;g.getNormalizedErrorAndCode=f}),98);
__d("useCometRouteTracePolicy",["CometRouterRouteTracePolicyContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext,j="comet.app";function a(){var a;return(a=i(c("CometRouterRouteTracePolicyContext")))!=null?a:j}g["default"]=a}),98);
__d("useMigrateURLParamsToRouteParams",["CometRouteParams","CometRouteURL","MetaConfig"],(function(a,b,c,d,e,f,g){"use strict";function a(){var a=c("MetaConfig")._("61"),b=d("CometRouteParams").useRouteParams(),e=d("CometRouteURL").useRouteURLParams();if(a)return b;else return e}g["default"]=a}),98);
__d("useSetAttributeRef",["react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(a,b){var c=d("react-compiler-runtime").c(3),e;c[0]!==a||c[1]!==b?(e=function(c){c!=null&&c.setAttribute(a,b)},c[0]=a,c[1]=b,c[2]=e):e=c[2];return e}g["default"]=a}),98);