;/*FB_PKG_DELIM*/

__d("BaseTheme.react",["BaseThemeProvider.react","BaseView.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(16),e,f,g,h,j,k;b[0]!==a?(e=a.config,f=a.displayMode,h=a.ref,j=a.style,k=a.xstyle,g=babelHelpers.objectWithoutPropertiesLoose(a,["config","displayMode","ref","style","xstyle"]),b[0]=a,b[1]=e,b[2]=f,b[3]=g,b[4]=h,b[5]=j,b[6]=k):(e=b[1],f=b[2],g=b[3],h=b[4],j=b[5],k=b[6]);b[7]!==g||b[8]!==h||b[9]!==j||b[10]!==k?(a=function(a,b){return i.jsx(c("BaseView.react"),babelHelpers["extends"]({},g,{ref:h,style:babelHelpers["extends"]({},b,{},j),xstyle:[a,k]}))},b[7]=g,b[8]=h,b[9]=j,b[10]=k,b[11]=a):a=b[11];var l;b[12]!==e||b[13]!==f||b[14]!==a?(l=i.jsx(c("BaseThemeProvider.react"),{config:e,displayMode:f,children:a}),b[12]=e,b[13]=f,b[14]=a,b[15]=l):l=b[15];return l}g["default"]=a}),98);
__d("CLS",[],(function(a,b,c,d,e,f){"use strict";var g=typeof ((b=window.PerformanceObserver)==null?void 0:(c=b.supportedEntryTypes)==null?void 0:c.includes)==="function"&&window.PerformanceObserver.supportedEntryTypes.includes("layout-shift");function a(){if(!g)return null;var a=0,b=0,c=[],d=new window.PerformanceObserver(function(d){for(d of d.getEntries())if(!d.hadRecentInput){var e=c[0],f=c[c.length-1];b&&d.startTime-f.startTime<1e3&&d.startTime-e.startTime<5e3?(b+=d.value,c.push(d)):(b=d.value,c=[d]);b>a&&(a=b)}});d.observe({buffered:!0,type:"layout-shift"});return function(){d.disconnect();return a}}f.getCLSCallback=a}),66);
__d("ChannelClientID",["MqttWebDeviceID","gkx","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h=c("gkx")("21118")?c("uuidv4")():(a=c("MqttWebDeviceID")==null?void 0:c("MqttWebDeviceID").clientID)!=null?a:c("uuidv4")();b={getID:function(){return h}};f.exports=b}),34);
__d("CometDensityModeContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=!1;c=a.createContext([b,function(){}]);e=c;g["default"]=e}),98);
__d("CometLruCache",["recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h=function(){function a(a,b){this.$1=a,this.$2=b,a<=0&&c("recoverableViolation")("CometLruCache: Unable to create instance of cache with zero or negative capacity.","CometLruCache"),this.$3=new Map()}var b=a.prototype;b.set=function(a,b){this.$3["delete"](a);this.$3.set(a,{timestamp:Date.now(),value:b});if(this.$3.size>this.$1){a=this.$3.keys().next();a.done||this.$3["delete"](a.value)}};b.get=function(a){var b=this.$3.get(a);if(b!=null){if(Date.now()>b.timestamp+this.$2){this.$3["delete"](a);return null}this.$3["delete"](a);this.$3.set(a,b);return b.value}return null};b.has=function(a){return this.$3.has(a)};b["delete"]=function(a){this.$3["delete"](a)};b.size=function(){return this.$3.size};b.capacity=function(){return this.$1-this.$3.size};b.clear=function(){this.$3.clear()};return a}();function a(a,b){b===void 0&&(b=Number.MAX_SAFE_INTEGER);return new h(a,b)}g.create=a}),98);
__d("CometTrackingNodesContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext([]);g["default"]=b}),98);
__d("ConstUriUtils",["CometLruCache","ExecutionEnvironment","FBLogger","PHPQuerySerializer","PHPQuerySerializerNoEncoding","URIRFC3986","URISchemes","UriNeedRawQuerySVConfig","isSameOrigin","nullthrows","recoverableViolation","structuredClone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=d("CometLruCache").create(5e3),m=new RegExp("(^|\\.)facebook\\.com$","i"),n=new RegExp("(^|\\.)messenger\\.com$","i"),o=new RegExp("(^|\\.)instagram\\.com$","i"),p=new RegExp("(^|\\\\.)meta\\\\.(com|ai)$","i"),q=new RegExp("^(?:[^/]*:|[\\x00-\\x1f]*/[\\x00-\\x1f]*/)"),r=new RegExp("[\\x00-\\x2c\\x2f\\x3b-\\x40\\x5c\\x5e\\x60\\x7b-\\x7f\\uFDD0-\\uFDEF\\uFFF0-\\uFFFF\\u2047\\u2048\\uFE56\\uFE5F\\uFF03\\uFF0F\\uFF1F]"),s=c("UriNeedRawQuerySVConfig").uris.map(function(a){return{domain:a,valid:z(a)}}),t=[],u=[];function v(a,b){var d={};if(a!=null)for(a of a.entries())d[a[0]]=a[1];else c("FBLogger")("ConstUriUtils").warn("Passed a null query map in, this means poor client side flow coverage or client/server boundary type issue.");return b.serialize(d)}function w(a,b,d){var e=k||(k=c("PHPQuerySerializer"));if(["http","https"].includes(b)&&x(a)){if(a.includes("doubleclick.net")&&d!=null&&!d.startsWith("http"))return e;e=c("PHPQuerySerializerNoEncoding")}return e}function x(a){return a!=null&&s.some(function(b){return b.valid&&y(a,b.domain)})}function y(a,b){if(b===""||a==="")return!1;if(a.endsWith(b)){b=a.length-b.length-1;if(b===-1||a[b]===".")return!0}return!1}function z(a){return!r.test(a)}function A(a,b){var c=b.protocol!=null&&b.protocol!==""?b.protocol:a.getProtocol();c=b.domain!=null?w(b.domain,c):a.getSerializer();c={domain:a.getDomain(),fragment:a.getFragment(),fragmentSeparator:a.hasFragmentSeparator(),isGeneric:a.isGeneric(),originalRawQuery:a.getOriginalRawQuery(),path:a.getPath(),port:a.getPort(),protocol:a.getProtocol(),queryParams:a.getQueryParams(),serializer:c,subdomain:a.getSubdomain()};a=babelHelpers["extends"]({},c,{},b);c=b.queryParams!=null&&b.queryParams.size!==0;return F.getUribyObject(a,c)}function B(a,b,c,d){c===void 0&&(c=!1);var e=a.protocol!==""?a.protocol+":"+(a.isGeneric?"":"//"):"",f=a.domain!==""?a.domain:"",g=a.port!==""?":"+a.port:"",h=a.path!==""?a.path:e!==""&&e!=="mailto:"||f!==""||g!==""?"/":"";b=C(f,a.originalRawQuery,a.queryParams,b,c,d!=null?d:a.serializer);c=b.length>0?"?":"";d=a.fragment!==""?"#"+a.fragment:"";a=a.fragment===""&&a.fragmentSeparator?"#":"";return""+e+f+g+h+c+b+a+d}function C(a,b,c,d,e,f){e===void 0&&(e=!1);return!d&&(e||x(a))?b!=null?b:"":v(c,f)}function D(a){var b=a.trim();b=(h||(h=d("URIRFC3986"))).parse(b)||{fragment:null,host:null,isGenericURI:!1,query:null,scheme:null,userinfo:null};var c=b.host||"",e=c.split(".");e=e.length>=3?e[0]:"";var f=w(c,b.scheme||"",b.query),g=f.deserialize(b.query||"")||{};g=new Map(Object.entries(g));g=E({domain:c,fragment:b.fragment||"",fragmentSeparator:b.fragment==="",isGeneric:b.isGenericURI,originalRawQuery:b.query,path:b.path||"",port:b.port!=null?String(b.port):"",protocol:(b.scheme||"").toLowerCase(),queryParams:g,serializer:f,subdomain:e,userInfo:(c=b==null?void 0:b.userinfo)!=null?c:""},a);return g}function E(a,b,c,e){c===void 0&&(c=(j||(j=d("URISchemes"))).Options.INCLUDE_DEFAULTS);var f={components:babelHelpers["extends"]({},a),error:"",valid:!0},g=f.components;if(!(j||(j=d("URISchemes"))).isAllowed(a.protocol,c,e)){f.valid=!1;f.error='The URI protocol "'+String(a.protocol)+'" is not allowed.';return f}if(!z(a.domain||"")){f.valid=!1;f.error="This is an unsafe domain "+String(a.domain);return f}g.port=a.port!=null&&String(a.port)||"";if(Boolean(a.userInfo)){f.valid=!1;f.error="Invalid URI: (userinfo is not allowed in a URI "+String(a.userInfo)+")";return f}c=b!=null&&b!==""?b:B(g,!1);if(g.domain===""&&g.path.indexOf("\\")!==-1){f.valid=!1;f.error="Invalid URI: (no domain but multiple back-slashes "+c+")";return f}if(!g.protocol&&q.test(c)){f.valid=!1;f.error="Invalid URI: (unsafe protocol-relative URI "+c+")";return f}if(g.domain!==""&&g.path!==""&&!g.path.startsWith("/")){f.valid=!1;f.error="Invalid URI: (domain and pathwhere path lacks leading slash "+c+")";return f}return f}var F=function(){function a(a){this.queryParams=new Map(),this.domain=a.domain,this.fragment=a.fragment,this.fragmentSeparator=Boolean(a.fragmentSeparator),this.isGenericProtocol=Boolean(a.isGeneric),this.path=a.path,this.originalRawQuery=a.originalRawQuery,this.port=a.port,this.protocol=a.protocol,this.queryParams=a.queryParams,this.serializer=a.serializer,this.subdomain=a.subdomain}var b=a.prototype;b.addQueryParam=function(a,b){if(Boolean(a)){var c=this.getQueryParams();c.set(a,b);return A(this,{queryParams:c})}return this};b.addQueryParams=function(a){if(a.size>0){var b=this.getQueryParams();a.forEach(function(a,c){b.set(c,a)});return A(this,{queryParams:b})}return this};b.addQueryParamString=function(a){if(Boolean(a)){a=a.startsWith("?")?a.slice(1):a;var b=this.getQueryParams();a.split("&").map(function(a){a=a.split("=");var c=a[0];a=a[1];b.set(c,a)});return A(this,{queryParams:b})}return this};b.addTrailingSlash=function(){var a=this.getPath();return a.length>0&&a[a.length-1]!=="/"?this.setPath(a+"/"):this};b.getDomain=function(){return this.domain};b.getFragment=function(){return this.fragment};b.getOrigin=function(){var a=this.getPort();return this.getProtocol()+"://"+this.getDomain()+(a?":"+a:"")};b.getOriginalRawQuery=function(){return this.originalRawQuery};b.getPath=function(){return this.path};b.getPort=function(){return this.port};b.getProtocol=function(){return this.protocol.toLowerCase()};b.getQualifiedUri=function(){if(!this.getDomain()){var b;b=(b=typeof window!=="undefined"?window:self)==null?void 0:(b=b.location)==null?void 0:b.href;if(b==null){c("FBLogger")("ConstUriUtils").blameToPreviousFile().warn("Cannot get qualified URI for current URI as there is no current location");return null}(i||(i=c("ExecutionEnvironment"))).isInWorker&&b.startsWith("blob:")&&(b=b.substring(5,b.length));b=b.slice(0,b.indexOf("/",b.indexOf(":")+3));return a.getUri(b+this.toString())}return this};b.getQueryParam=function(a){a=this.queryParams.get(a);if(typeof a==="string")return a;else{a=JSON.stringify(a);return a==null?a:JSON.parse(a)}};b.getQueryData=function(){return Object.fromEntries(this.getQueryParams())};b.getQueryParams=function(){if(c("structuredClone")!=null)return c("structuredClone")(this.queryParams);var a=JSON.stringify(Array.from(this.queryParams),function(a,b){return Array.isArray(b)?{__CUUArr:!0,value:babelHelpers["extends"]({},b)}:b});a=JSON.parse(a,function(a,b){return b!=null&&typeof b==="object"&&b.__CUUArr?Object.keys(b.value).reduce(function(a,c){a[c]=b.value[c];return a},[]):b});return new Map(a)};b.getQueryString=function(a){a===void 0&&(a=!1);return C(this.domain,this.originalRawQuery,this.queryParams,!1,a,this.serializer)};b.getRegisteredDomain=function(){if(!this.getDomain())return"";if(!this.isFacebookUri())return null;var a=this.getDomain().split("."),b=a.indexOf("facebook");b===-1&&(b=a.indexOf("workplace"));return a.slice(b).join(".")};b.getSerializer=function(){return this.serializer};b.getSubdomain=function(){return this.subdomain};b.getUnqualifiedUri=function(){if(this.getDomain()){var b=this.toString();return a.getUri(b.slice(b.indexOf("/",b.indexOf(":")+3)))}return this};a.getUri=function(b){b=b.trim();var d=l.get(b);if(d==null){var e=D(b);if(e.valid)d=new a(e.components),l.set(b,d);else{c("FBLogger")("ConstUriUtils").blameToPreviousFrame().warn(e.error);return null}}return d};a.getUriOrThrow=function(b){return c("nullthrows")(a.getUri(b))};a.getUribyObject=function(b,d){var e=B(b,d),f=l.get(e);if(f==null){d&&(b.originalRawQuery=v(b.queryParams,b.serializer));d=E(b);if(d.valid)f=new a(d.components),l.set(e,f);else{c("recoverableViolation")(d.error,"ConstUri");return null}}return f};b.hasFragmentSeparator=function(){return this.fragmentSeparator};b.isEmpty=function(){return!(this.getPath()||this.getProtocol()||this.getDomain()||this.getPort()||this.queryParams.size>0||this.getFragment())};b.isFacebookUri=function(){var a=this.toString();if(a==="")return!1;return!this.getDomain()&&!this.getProtocol()?!0:["https","http"].indexOf(this.getProtocol())!==-1&&(m.test(this.getDomain())||n.test(this.getDomain())||o.test(this.getDomain())||p.test(this.getDomain()))};b.isGeneric=function(){return this.isGenericProtocol};b.isSameOrigin=function(a){return c("isSameOrigin")(this,a)};b.isSubdomainOfDomain=function(b){var c=a.getUri(b);return c!=null&&y(this.domain,b)};b.isSecure=function(){return this.getProtocol()==="https"};b.removeQueryParams=function(a){if(Array.isArray(a)&&a.length>0){var b=this.getQueryParams();a.map(function(a){return b["delete"](a)});return A(this,{queryParams:b})}return this};b.removeQueryParam=function(a){if(Boolean(a)){var b=this.getQueryParams();b["delete"](a);return A(this,{queryParams:b})}return this};b.removeSubdomain=function(){var a=this.getQualifiedUri();if(a==null)return null;var b=a.getDomain();b=b.split(".");b.length>=3&&(b=b.slice(-2));return A(a,{domain:b.join("."),subdomain:""})};b.replaceQueryParam=function(a,b){if(Boolean(a)){var c=this.getQueryParams();c.set(a,b);return A(this,{queryParams:c})}return this};b.replaceQueryParams=function(a){return A(this,{queryParams:a})};b.replaceQueryParamString=function(a){if(a!=null){a=a.startsWith("?")?a.slice(1):a;var b=this.getQueryParams();a.split("&").map(function(a){a=a.split("=");var c=a[0];a=a[1];b.set(c,a)});return A(this,{queryParams:b})}return this};b.setDomain=function(a){if(Boolean(a)){var b=a.split(".");b=b.length>=3?b[0]:"";return A(this,{domain:a,subdomain:b})}return this};b.setFragment=function(a){return a==="#"?A(this,{fragment:"",fragmentSeparator:!0}):A(this,{fragment:a,fragmentSeparator:a!==""})};b.setPath=function(a){return a!=null?A(this,{path:a}):this};b.setPort=function(a){return Boolean(a)?A(this,{port:a}):this};b.setProtocol=function(a){return Boolean(a)?A(this,{protocol:a}):this};b.setSecure=function(a){return this.setProtocol(a?"https":"http")};b.setSubDomain=function(a){if(Boolean(a)){var b=this.getQualifiedUri();if(b==null)return null;var c=b.getDomain();c=c.split(".");c.length>=3?c[0]=a:c.unshift(a);return A(b,{domain:c.join("."),subdomain:a})}return this};b.stripTrailingSlash=function(){return this.setPath(this.getPath().replace(/\/$/,""))};a.$1=function(a){a=a;for(var b of t)a=b(a);return a};a.$2=function(a,b){b=b;for(var c of u)b=c(a,b);return b};b.$3=function(b,c){c===void 0&&(c=!1);return B({domain:a.$1(this.domain),fragment:this.fragment,fragmentSeparator:this.fragmentSeparator,isGeneric:this.isGenericProtocol,originalRawQuery:this.originalRawQuery,path:this.path,port:this.port,protocol:this.protocol,queryParams:a.$2(this.domain,this.queryParams),serializer:b,subdomain:this.subdomain,userInfo:""},!1,c)};b.toStringRawQuery=function(){this.rawStringValue==null&&(this.rawStringValue=this.$3(c("PHPQuerySerializerNoEncoding")));return this.rawStringValue};b.toString=function(){this.stringValue==null&&(this.stringValue=this.$3(this.serializer));return this.stringValue};b.toStringPreserveQuery=function(){return this.$3(this.serializer,!0)};a.isValidUri=function(b){var c=l.get(b);if(c!=null)return!0;c=D(b);if(c.valid){l.set(b,new a(c.components));return!0}return!1};return a}();function a(a){if(a instanceof F)return a;else return null}function b(a){t.push(a)}function e(a){u.push(a)}f=F.getUri;var G=F.getUriOrThrow,H=F.isValidUri;g.isSubdomainOfDomain=y;g.isConstUri=a;g.registerDomainFilter=b;g.registerQueryParamsFilter=e;g.getUri=f;g.getUriOrThrow=G;g.isValidUri=H}),98);
__d("CurrentLocale",["IntlCurrentLocale"],(function(a,b,c,d,e,f,g){"use strict";a={get:function(){return c("IntlCurrentLocale").code}};b=a;g["default"]=b}),98);
__d("CurrentMessengerUser",["CurrentEnvironment","CurrentUser"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("CurrentUser").getID()}function b(){return c("CurrentEnvironment").instagramdotcom?c("CurrentUser").getEIMU():c("CurrentUser").getID()}function d(){return c("CurrentUser").getPageMessagingMailboxId()}function e(){return c("CurrentUser").isWorkUser()}function f(){return c("CurrentUser").isTestUser()}function h(){return c("CurrentUser").isEmployee()}function i(){return c("CurrentUser").getAppID()}function j(){return c("CurrentUser").getAccountID()}function k(){return c("CurrentUser").isLoggedInNow()}g.getID=a;g.getIDorEIMU=b;g.getPageMessagingMailboxId=d;g.isWorkUser=e;g.isTestUser=f;g.isEmployee=h;g.getAppID=i;g.getAccountID=j;g.isLoggedInNow=k}),98);
__d("DateConsts",[],(function(a,b,c,d,e,f){var g=1e3;c=60;d=60;e=24;var h=7,i=12,j=1e3,k=30.43,l=4.333,m=365.242,n=c*d,o=n*e,p=o*h,q=o*m,r=g*c,s=r*d,t=g*o,u=t*h,v=t*m,w={SUNDAY:0,MONDAY:1,TUESDAY:2,WEDNESDAY:3,THURSDAY:4,FRIDAY:5,SATURDAY:6};Object.freeze(w);function a(a,b){return new Date(a,b,0).getDate()}function b(){return Date.now()/g}var x={instantRange:{since:-864e10,until:864e10+1}};f.MS_PER_SEC=g;f.SEC_PER_MIN=c;f.MIN_PER_HOUR=d;f.HOUR_PER_DAY=e;f.DAYS_PER_WEEK=h;f.MONTHS_PER_YEAR=i;f.US_PER_MS=j;f.AVG_DAYS_PER_MONTH=k;f.AVG_WEEKS_PER_MONTH=l;f.AVG_DAYS_PER_YEAR=m;f.SEC_PER_HOUR=n;f.SEC_PER_DAY=o;f.SEC_PER_WEEK=p;f.SEC_PER_YEAR=q;f.MS_PER_MIN=r;f.MS_PER_HOUR=s;f.MS_PER_DAY=t;f.MS_PER_WEEK=u;f.MS_PER_YEAR=v;f.DAYS=w;f.getDaysInMonth=a;f.getCurrentTimeInSeconds=b;f["private"]=x}),66);
__d("ErrorMetadata",["fb-error"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}b.addGlobalMetadata=function(b,c,d){a.addGlobalMetadata.call(this,b,c,d)};return b}(c("fb-error").ErrorMetadata);g["default"]=a}),98);
__d("FbtErrorListenerWWW",["FBLogger"],(function(a,b,c,d,e,f,g){a=function(){function a(a){this.$1=a.hash,this.$2=a.translation}var b=a.prototype;b.onStringSerializationError=function(a){var b="Context not logged.";try{var d=JSON.stringify(a);d!=null&&(b=d.substr(0,250))}catch(a){b=a.message}d=(a==null?void 0:(d=a.constructor)==null?void 0:d.name)||"";c("FBLogger")("fbt").blameToPreviousDirectory().blameToPreviousDirectory().mustfix('Converting to a string will drop content data. Hash="%s" Translation="%s" Content="%s" (type=%s,%s)',this.$1,this.$2,b,typeof a,d)};b.onStringMethodUsed=function(a){c("FBLogger")("fbt").blameToPreviousDirectory().blameToPreviousDirectory().mustfix("Error using fbt string. Used method %s on Fbt string. Fbt string is designed to be immutable and should not be manipulated.",a)};b.onMissingParameterError=function(a,b){c("FBLogger")("fbt").blameToPreviousDirectory().blameToPreviousDirectory().mustfix('Expected fbt parameter names (%s) to also contain `%s`. Hash="%s" Translation="%s"',a.join(", "),b,this.$1,this.$2)};return a}();g["default"]=a}),98);
__d("FbtReactUtil",[],(function(a,b,c,d,e,f){a=typeof Symbol==="function"&&Symbol["for"]&&Symbol["for"]("react.element")||60103;var g=!1;b={REACT_ELEMENT_TYPE:a,injectReactShim:function(a){var b={validated:!0};g?Object.defineProperty(a,"_store",{configurable:!1,enumerable:!1,writable:!1,value:b}):a._store=b}};e.exports=b}),null);
__d("FbtResultBase",[],(function(a,b,c,d,e,f){"use strict";var g=function(){function a(a,b){this.$1=a,this.__errorListener=b,this.$3=!1,this.$2=null}var b=a.prototype;b.flattenToArray=function(){return a.flattenToArray(this.$1)};b.getContents=function(){return this.$1};b.toString=function(){if(Object.isFrozen(this))return this.$4();if(this.$3)return"<<Reentering fbt.toString() is forbidden>>";this.$3=!0;try{return this.$4()}finally{this.$3=!1}};b.$4=function(){if(this.$2!=null)return this.$2;var b="",c=this.flattenToArray();for(var d=0;d<c.length;++d){var e=c[d];if(typeof e==="string"||e instanceof a)b+=e.toString();else{var f;(f=this.__errorListener)==null?void 0:f.onStringSerializationError==null?void 0:f.onStringSerializationError(e)}}Object.isFrozen(this)||(this.$2=b);return b};b.toJSON=function(){return this.toString()};a.flattenToArray=function(b){var c=[];for(var d=0;d<b.length;++d){var e=b[d];Array.isArray(e)?c.push.apply(c,a.flattenToArray(e)):e instanceof a?c.push.apply(c,e.flattenToArray()):c.push(e)}return c};return a}();["anchor","big","blink","bold","charAt","charCodeAt","codePointAt","contains","endsWith","fixed","fontcolor","fontsize","includes","indexOf","italics","lastIndexOf","link","localeCompare","match","normalize","repeat","replace","search","slice","small","split","startsWith","strike","sub","substr","substring","sup","toLocaleLowerCase","toLocaleUpperCase","toLowerCase","toUpperCase","trim","trimLeft","trimRight"].forEach(function(a){g.prototype[a]=function(){var b;(b=this.__errorListener)==null?void 0:b.onStringMethodUsed==null?void 0:b.onStringMethodUsed(a);for(var c=arguments.length,d=new Array(c),e=0;e<c;e++)d[e]=arguments[e];return String.prototype[a].apply(this,d)}});a=g;e.exports=a}),null);
__d("FbtResult",["FbtReactUtil","FbtResultBase"],(function(a,b,c,d,e,f){var g=function(a){return a.content};a=function(a){"use strict";babelHelpers.inheritsLoose(c,a);function c(c,d){d=a.call(this,c,d)||this;d.$$typeof=b("FbtReactUtil").REACT_ELEMENT_TYPE;d.key=null;d.ref=null;d.type=g;d.props={content:c};return d}c.get=function(a){return new c(a.contents,a.errorListener)};return c}(b("FbtResultBase"));e.exports=a}),null);
__d("FbtPureStringResult",["FbtResult"],(function(a,b,c,d,e,f){a=function(a){"use strict";babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}return b}(b("FbtResult"));c=a;e.exports=c}),null);
__d("getFbsResult",["FbtPureStringResult"],(function(a,b,c,d,e,f){function a(a){return new(b("FbtPureStringResult"))(a.contents,a.errorListener)}e.exports=a}),null);
__d("getTranslatedInput",["Env","ExecutionEnvironment","FBLogger","MakeHasteTranslationsMap"],(function(a,b,c,d,e,f,g){var h,i;b="JHASH";var j=new RegExp("__"+b+"__(.+?)__"+b+"__"),k=!!(h||(h=c("Env"))).use_fbt_virtual_modules;function a(a){var b=a.table;if(k&&(i||(i=c("ExecutionEnvironment"))).isInBrowser){if(typeof b==="string"){var e=b.match(j);if(e!=null)return l(babelHelpers["extends"]({},a,{table:d("MakeHasteTranslationsMap").get(e[1])}))}c("FBLogger")("binary_transparency","inlined_translations").warn("Found inlined translated contents in client_fetch_translations experiment! Input: %s",JSON.stringify(b))}return l(a)}function l(a){var b=a.table;return typeof b==="string"&&b[0]==="_"&&b[1]==="j"&&(b[2]==="{"||b[2]==="[")?babelHelpers["extends"]({},a,{table:JSON.parse(b.substring(2))}):a}g["default"]=a}),98);
__d("translationOverrideListener",["requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h=c("requireDeferred")("IntlQtEventFalcoEvent").__setRef("translationOverrideListener");function a(a){h.onReady(function(b){return b.log(function(){return{hash:a}})})}g["default"]=a}),98);
__d("FbtEnv",["FbtErrorListenerWWW","FbtHooks","IntlViewerContext","cr:7730","getFbsResult","getTranslatedInput","justknobx","promiseDone","requireDeferred","translationOverrideListener"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("requireDeferred")("FbtLogging").__setRef("FbtEnv");d="JHASH";var j=new RegExp("__"+d+"__(.+?)__"+d+"__"),k=!1;function a(){if(k)return;k=!0;(h||(h=b("FbtHooks"))).register({errorListener:function(a){return new(c("FbtErrorListenerWWW"))(a)},getFbsResult:c("getFbsResult"),getFbtResult:b("cr:7730"),getTranslatedInput:c("getTranslatedInput"),onTranslationOverride:c("translationOverrideListener"),getViewerContext:function(){return c("IntlViewerContext")},logImpression:function(a,b){return c("promiseDone")(i.load().then(function(d){d.logImpression==null?void 0:d.logImpression(a);if(!c("justknobx")._("2269")){var e,f=b==null?void 0:b.inputTable;e=(e=b==null?void 0:b.tokens)!=null?e:[];if(typeof f==="string"){f=f.match(j);f!=null&&(d.logImpressionV2==null?void 0:d.logImpressionV2(f[1],e))}}}))}})}g.setupOnce=a}),98);
__d("FbtHooksImpl",[],(function(a,b,c,d,e,f){var g={};a={getErrorListener:function(a){return g.errorListener==null?void 0:g.errorListener(a)},logImpression:function(a,b){g.logImpression==null?void 0:g.logImpression(a,b)},onTranslationOverride:function(a){g.onTranslationOverride==null?void 0:g.onTranslationOverride(a)},getFbsResult:function(a){return g.getFbsResult(a)},getFbtResult:function(a){return g.getFbtResult(a)},getTranslatedInput:function(a){var b;return(b=g.getTranslatedInput==null?void 0:g.getTranslatedInput(a))!=null?b:a},getViewerContext:function(){return g.getViewerContext()},register:function(a){Object.assign(g,a)}};e.exports=a}),null);
__d("FbtHooks",["FbtEnv","FbtHooksImpl"],(function(a,b,c,d,e,f){e.exports=b("FbtHooksImpl"),b("FbtEnv").setupOnce()}),null);
__d("FbtNumberType",["IntlNumberTypeProps"],(function(a,b,c,d,e,f,g){g["default"]=c("IntlNumberTypeProps").module}),98);
__d("FbtTable",["invariant"],(function(a,b,c,d,e,f,g){"use strict";var h={ARG:{INDEX:0,SUBSTITUTION:1},access:function(a,b,c,d){if(c>=b.length){typeof a==="string"||Array.isArray(a)||g(0,21388,JSON.stringify(a));return a}var e=b[c];e=e[h.ARG.INDEX];if(e==null)return h.access(a,b,c+1,d);typeof a!=="string"&&!Array.isArray(a)||g(0,20954,typeof a);for(var f=0;f<e.length;++f){var i=a[e[f]];if(i==null)continue;d.push(e[f]);i=h.access(i,b,c+1,d);if(i!=null)return i}return null}};e.exports=h}),null);
__d("FbtTableAccessor",[],(function(a,b,c,d,e,f){"use strict";a={getEnumResult:function(a){return[[a],null]},getGenderResult:function(a,b,c){return[a,b]},getNumberResult:function(a,b,c){return[a,b]},getSubstitution:function(a){return[null,a]},getPronounResult:function(a){return[[a,"*"],null]}};e.exports=a}),null);
__d("GenderConst",[],(function(a,b,c,d,e,f){e.exports={NOT_A_PERSON:0,FEMALE_SINGULAR:1,MALE_SINGULAR:2,FEMALE_SINGULAR_GUESS:3,MALE_SINGULAR_GUESS:4,MIXED_UNKNOWN:5,NEUTER_SINGULAR:6,UNKNOWN_SINGULAR:7,FEMALE_PLURAL:8,MALE_PLURAL:9,NEUTER_PLURAL:10,UNKNOWN_PLURAL:11}}),null);
__d("IGDWebUtils",["CurrentUserInitialData"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a){return a!=null?a===1217981644879628||a===936619743392459||a===487152425211411||a===1035956773910536:!1}function a(){return i(Number((h||(h=c("CurrentUserInitialData"))).APP_ID))}g.isInstagramWebSupportedApp=i;g.isOnInstagramWeb=a}),98);
__d("ImageDownloadTracker",["cr:7422"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:7422")}),98);
__d("ImageDownloadTrackerWWW",["NetworkStatus","Promise","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i=2,j=250;function a(a,d){var e=0,f;return new(h||(h=b("Promise")))(function(b,g){function h(){var f=new Image();f.onload=function(){c("NetworkStatus").reportSuccess(),b()};f.onerror=function(){var a=e<=i;a?c("setTimeout")(h,j):(c("NetworkStatus").reportError(),g())};e++;d();f.src=a}c("NetworkStatus").isOnline()?h():f=c("NetworkStatus").onChange(function(a){a=a.online;a&&(h(),f.remove())})})}g["default"]=a}),98);
__d("PerfFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1744178");b=d("FalcoLoggerInternal").create("perf",a);e=b;g["default"]=e}),98);
__d("Locale",["SiteData"],(function(a,b,c,d,e,f){function a(){return b("SiteData").is_rtl}e.exports={isRTL:a}}),null);
__d("PerfXSharedFields",["CurrentLocale","Locale","SiteData"],(function(a,b,c,d,e,f,g){var h=typeof window!=="undefined"?window:self,i={addCommonValues:function(a){var b=h==null?void 0:h.navigator;try{b&&typeof b.hardwareConcurrency==="number"&&(a.num_cores=Math.floor(b.hardwareConcurrency)),b&&b.deviceMemory&&(a.ram_gb=b.deviceMemory),b&&b.connection&&(typeof b.connection.downlink==="number"&&(a.downlink_megabits=b.connection.downlink),typeof b.connection.effectiveType==="string"&&(a.effective_connection_type=b.connection.effectiveType),typeof b.connection.rtt==="number"&&(a.rtt_ms=b.connection.rtt))}catch(a){if(a.message!=="can't access dead object")throw a}a.client_push_phase=c("SiteData").push_phase;a.client_revision=c("SiteData").client_revision;c("SiteData").server_revision!=null&&(a.server_revision=c("SiteData").server_revision);a.locale=c("CurrentLocale").get();a.isRTL=Number(c("Locale").isRTL());return a},getCommonData:function(){var a={};i.addCommonValues(a);return a}};a=i;g["default"]=a}),98);
__d("QPLEvent",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a.i}function b(a){return(a=a.r)!=null?a:0}function c(a){return(a=a.m)!=null?a:1}f.getMarkerId=a;f.getSampleRate=b;f.getSamplingMethod=c}),66);
__d("QPLTimestamp",[],(function(a,b,c,d,e,f){"use strict";function g(a){if(a===0)return"0";a=a.toFixed(6).split(".",2);var b=a[0];a=a[1];return b==="0"?a.replace(/^0+/,""):b+a}function a(a,b){b=b-a;return g(b)}f.timestampToSerializedNanoseconds=g;f.calculateDurationAsNanoseconds=a}),66);
__d("crc32",[],(function(a,b,c,d,e,f){function b(a){var b=-1;for(var c=0,d=a.length;c<d;c++)b=b>>>8^g[(b^a.charCodeAt(c))&255];return~b}var g=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];a.Int32Array!==void 0&&(g=new Int32Array(g));f["default"]=b}),66);
__d("QPLUtils",["crc32"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return c("crc32")(a)>>>0}function a(a){return a!=null?h(a):0}g.unsignedCrc32=h;g.deriveInstanceKey=a}),98);
__d("QuickPerformanceLoggerTypes",[],(function(a,b,c,d,e,f){"use strict";a=1;b=3;f.EVENT_BASED_SAMPLING=a;f.USER_BASED_SAMPLING=b}),66);
__d("QPLCore",["QPLEvent","QPLTimestamp","QPLUtils","QuickPerformanceLoggerTypes","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a,b,c,d){var e=b[a];if(!e||Object.entries(e).length===0)return null;var f={};Object.keys(e).forEach(function(a){var b=e[a];b==null||b.length===0?delete e[a]:d?f[a]=d(b):f[a]=b});return b={},b[a]=babelHelpers["extends"]({},c&&c[a],{},f),b}function j(a,b){if(a==null)return b;var c=babelHelpers["extends"]({},b,{},i("string",a,b),{},i("int",a,b,Math.trunc),{},i("double",a,b),{},i("bool",a,b),{},i("string_array",a,b),{},i("int_array",a,b,function(a){return a.map(Math.trunc)}),{},i("double_array",a,b),{},i("bool_array",a,b));Object.keys(c).forEach(function(a){var b=c[a];(b==null||Object.entries(b).length===0)&&delete c[a]});return Object.entries(c).length!==0?c:null}function k(a,b){var c={};o.isMarkerTracked(b)&&(c.tracked_for_loss=!0);typeof b.absoluteTimeOrigin==="number"&&(c.absolute_time_origin_ns=d("QPLTimestamp").timestampToSerializedNanoseconds(b.absoluteTimeOrigin));b=babelHelpers["extends"]({},a,{},c);return b}function l(a,b){if(!b)return a;var c={};b.string&&(c.annotations=b.string);b["int"]&&(c.annotations_int=b["int"]);b["double"]&&(c.annotations_double=b["double"]);b.bool&&(c.annotations_bool=b.bool);b.string_array&&(c.annotations_string_array=b.string_array);b.int_array&&(c.annotations_int_array=b.int_array);b.double_array&&(c.annotations_double_array=b.double_array);b.bool_array&&(c.annotations_bool_array=b.bool_array);return babelHelpers["extends"]({},a,{},c)}var m=new Map([[d("QuickPerformanceLoggerTypes").EVENT_BASED_SAMPLING,"random_sampling"],[d("QuickPerformanceLoggerTypes").USER_BASED_SAMPLING,"per_user"]]);function n(a,b){if(a===0)return!1;if(a<=1)return!0;return typeof b==="string"?d("QPLUtils").unsignedCrc32(b)%a===0:Math.random()*a<=1}var o=function(){function a(a){this.$2=1;this.$3=100;this.$4=new Map();this.$5=new Map();this.$6=null;this.$8=new Map();this.activeMarkers=new Map();this.$1=a;this.$7=a.logger;this.$5=(a=a.listenersWithMarker)!=null?a:new Map();this.$9=new Map()}var b=a.prototype;b.getMarker=function(a,b){a=this.$10((h||(h=d("QPLEvent"))).getMarkerId(a));if(!a)return null;a=a.get(b);return!a?null:a};b.isMarkerOn=function(a,b){b===void 0&&(b=0);a=this.$10((h||(h=d("QPLEvent"))).getMarkerId(a));if(!a)return!1;a=a.get(b);return!!a};a.isMarkerTracked=function(a){return a.trackedForLoss===!0||a.type===2};b.$10=function(a){return this.activeMarkers.get(a)};b.addMarker=function(a,b,c){var d=this.activeMarkers.get(a);d||(d=new Map(),this.activeMarkers.set(a,d));d.set(b,c)};b.deleteMarker=function(a,b){var c;(c=this.activeMarkers.get(a))==null?void 0:c["delete"](b);this.$11(a,b)};b.markerStart=function(b,c,e,f){c===void 0&&(c=0);e===void 0&&(e=this.currentTimestamp());f=f===void 0?{}:f;var g=f.cancelExisting;g=g===void 0?!1:g;var i=f.cancelOnUnload;i=i===void 0?!1:i;var j=f.trackedForLoss;j=j===void 0?!1:j;var k=f.type;k=k===void 0?1:k;var l=f.samplingBasis;l=l===void 0?null:l;var m=f.qplInternalDoNotUseAbsoluteTimeOrigin,n=f.timeoutMS;n=n===void 0?null:n;var o=f.onMarkerTimeout__DoNotUse;o=o===void 0?null:o;f=f.enableE2ETracing;f=f===void 0?!1:f;var p=this.getMarker(b,c);if(p){p=Math.round(e-p.timestamp);g&&(this.markerAnnotate(b,{string:{cancelType:"DUPLICATE_EVENT"},"int":{time_between_markers_ms:p}},{instanceKey:c}),this.markerEnd(b,4,c,e))}g=this.$12(b,l);p=g[0];l=g[1];g=g[2];var q=null;f&&(q=this.$1.e2eTracingIDGenerator?this.$1.e2eTracingIDGenerator(b):null);var r={event:b,passesSampling:p,timestamp:e,sampleRate:l,samplingMethod:g,points:[],cancelOnUnload:i,trackedForLoss:j,type:k,timeoutMS:n,e2eTracingID:q};typeof m==="number"&&(r.absoluteTimeOrigin=m);this.$4.forEach(function(a){a.onMarkerStart&&a.onMarkerStart(b,c,e)});this.$5.forEach(function(a){a.onMarkerStartWithMarker(b,c,e,r)});p&&(this.addMarker((h||(h=d("QPLEvent"))).getMarkerId(b),c,r),n!=null&&this.$13(b,c,n,o));p&&a.isMarkerTracked(r)&&this.$14({marker_id:27787271,action_id:51,sample_rate:1,annotations_int:{tracked_marker_id:(h||(h=d("QPLEvent"))).getMarkerId(b)},marker_type:1});if(q!==null){this.markerAnnotate(b,{string:(f={},f.qpl_e2e__tracing_id=q,f)},{instanceKey:c})}p&&this.$4.forEach(function(a){a.onMarkerStarted&&a.onMarkerStarted(b,c,e)})};b.$15=function(a,b){var c=this.$9.get(a);c==null?void 0:c["delete"](b);(c==null?void 0:c.size)===0&&this.$9["delete"](a)};b.$16=function(a,b,c){this.$9.has(a)||this.$9.set(a,new Map());a=this.$9.get(a);a==null?void 0:a.set(b,c)};b.$11=function(a,b){var c,d;c=(c=this.$9.get(a))==null?void 0:c.get(b);if(!c)return;if(!((d=this.$1.runtimeAbstractionLayer)==null?void 0:d.clearTimeout))return;this.$1.runtimeAbstractionLayer.clearTimeout(c);this.$15(a,b)};b.$13=function(a,b,c,e){var f=this,g=(h||(h=d("QPLEvent"))).getMarkerId(a);try{var i;this.$11(g,b);if(!((i=this.$1.runtimeAbstractionLayer)==null?void 0:i.setTimeout))return;i=(i=this.$1.runtimeAbstractionLayer)==null?void 0:i.setTimeout(function(){e!=null&&e(a,b),f.$4.forEach(function(c){c.onTimeoutEvent!=null&&c.onTimeoutEvent({event:a,instanceKey:b})}),f.$15(g,b),f.markerEnd(a,113,b)},c);this.$16(g,b,i)}catch(a){}};b.markerAnnotate=function(a,b,c){c=c===void 0?{}:c;c=c.instanceKey;var d=c===void 0?0:c;this.$4.forEach(function(c){Object.keys(b).forEach(function(e){var f=b[e];if(!f)return;Object.keys(f).forEach(function(b){c.onAnnotation&&c.onAnnotation(a,d,b,f[b],e)})})});c=this.getMarker(a,d);if(!c)return;c.annotations=j(b,c.annotations)};b.markerPoint=function(a,b,c){c=c===void 0?{}:c;var d=c.instanceKey,e=d===void 0?0:d,f=c.data;d=c.timestamp;var g=d===void 0?this.currentTimestamp():d;this.$4.forEach(function(c){c.onMarkerPoint&&c.onMarkerPoint(a,e,b,f,g)});c=this.getMarker(a,e);if(!c)return;d={name:b,timeSinceStart:Math.trunc(g-c.timestamp)};var h=j(f);h&&(d.data=h);c.points.push(d)};b.markerEnd=function(a,b,c,e){c===void 0&&(c=0);e===void 0&&(e=this.currentTimestamp());var f=this.getMarker(a,c);if(!f){this.$4.forEach(function(d){var f={durationMs:0};d.onMarkerEnd&&d.onMarkerEnd(b,a,c,e,f)});return}var g=b,i=this.$17(f,f.timestamp,e);i&&b===2&&(g=5947);i={marker_id:(h||(h=d("QPLEvent"))).getMarkerId(a),action_id:g,instance_id:c,sample_rate:f.sampleRate,method:m.get(f.samplingMethod),duration_ns:d("QPLTimestamp").calculateDurationAsNanoseconds(f.timestamp,e),points:f.points,metadata:{application_analytics:{time_since_qpl_module_init:e-this.$1.moduleLoadTimestamp}},marker_type:f.type,flags:1,unique_marker_id_debug_only:f.uniqueMarkerDebugId};i=l(i,f.annotations);var j=k(i,f);this.$4.forEach(function(b){var d={durationMs:f?e-f.timestamp:0,logData:j};b.onMarkerEnd&&b.onMarkerEnd(g,a,c,e,d)});f.passesSampling&&(f.timestampIsApproximate!==!0&&this.$14(j),this.$6===(h||(h=d("QPLEvent"))).getMarkerId(a)&&this.$1.onDebuggingIdEnded&&this.$1.onDebuggingIdEnded());this.deleteMarker(h.getMarkerId(a),c)};b.markerDrop=function(a,b){b===void 0&&(b=0);this.deleteMarker((h||(h=d("QPLEvent"))).getMarkerId(a),b);var c=this.currentTimestamp();this.$4.forEach(function(d){d.onMarkerDrop&&d.onMarkerDrop(a,b,c)})};b.markEvent=function(a,b,c,e){e=e===void 0?{}:e;var f=e.timestamp,g=f===void 0?this.currentTimestamp():f,i=e.annotations;this.$4.forEach(function(b){b.onMarkEvent&&b.onMarkEvent({event:a,timestamp:g,annotations:i})});if((f=this.$1.quickLogConfigHelper)==null?void 0:f.isKillswitchOn())return;e=this.$12(a);f=e[0];var k=e[1];e=e[2];if(!f)return;f=j(typeof i==="function"?i():i);k={marker_id:(h||(h=d("QPLEvent"))).getMarkerId(a),action_id:51,instance_id:0,sample_rate:k,method:m.get(e),da_type:b,da_level:c,metadata:{application_analytics:{time_since_qpl_module_init:g-this.$1.moduleLoadTimestamp}},marker_type:1,flags:1};this.$14(l(k,f))};b.markerStartForJoin=function(a,b,c){c=c===void 0?{}:c;var d=c.instanceKey;d=d===void 0?0:d;var e=c.cancelExisting;e=e===void 0?!1:e;var f=c.cancelOnUnload;f=f===void 0?!1:f;var g=c.trackedForLoss;g=g===void 0?!1:g;var h=c.type;h=h===void 0?1:h;var i=c.qplInternalDoNotUseAbsoluteTimeOrigin;i=i===void 0?null:i;var j=c.timeoutMS;j=j===void 0?null:j;var k=c.monotonicTimestamp;k=k===void 0?this.currentTimestamp():k;var l=c.absoluteTimeOriginMs;l=l===void 0?this.currentUnixTimestamp():l;var m=c.sourceIsPrimary;m=m===void 0?!1:m;var n=c.closeSession,o=c.onMarkerTimeout__DoNotUse;c=c.unreliableSourceClockProcessId;this.markerStart(a,d,k,{cancelExisting:e,cancelOnUnload:f,trackedForLoss:g,type:h,samplingBasis:b,qplInternalDoNotUseAbsoluteTimeOrigin:i,timeoutMS:j,onMarkerTimeout__DoNotUse:o});k="unreliable";c!=null&&(k+="_"+c);this.markerAnnotate(a,{string:(e={},e.join_id=b,e.qpl_join__source_clock=k,e.qpl__source="client_js",e),"int":(f={},f.qpl_join__absolute_time_origin_ms=l,f),bool:(g={},g.qpl_join__source_is_primary=m,g)},{instanceKey:d});if(n!=null){this.markerAnnotate(a,{"int":(h={},h.qpl_join__close_session_after_seconds=n,h)},{instanceKey:d})}};b.addAlignmentPointForJoin=function(a,b,c){c=c===void 0?{}:c;var d=c.instanceKey;d=d===void 0?0:d;var e=c.requestId;e=e===void 0?"default_id":e;c=c.timestamp;c=c===void 0?this.currentTimestamp():c;b=this.$18(b);if(b==null)return;b=b+e;this.markerPoint(a,b,{instanceKey:d,timestamp:c})};b.setAlwaysOnSampleRate=function(a,b){this.$8.set(a,b)};b.setDefaultSampleRate=function(a){this.$3=a};b.currentTimestamp=function(){return this.$1.monotonicNowMs()};b.currentUnixTimestamp=function(){return this.$1.unixNowMs()};b.enableDebug=function(a){this.$6=a};b.disableDebug=function(){this.$6=null};b.addListener=function(a){var b=this,c=this.$2++;this.$4.set(c,a);return{dispose:function(){b.$4["delete"](c)}}};b.$12=function(a,b){if(this.$1.unsampleAllEvents&&this.$1.unsampleAllEvents()||this.$6===(h||(h=d("QPLEvent"))).getMarkerId(a))return[!0,1,d("QuickPerformanceLoggerTypes").EVENT_BASED_SAMPLING];var c=(h||(h=d("QPLEvent"))).getSampleRate(a),e=h.getSamplingMethod(a);if(this.$8.get(a)==null&&c!==0&&e===d("QuickPerformanceLoggerTypes").USER_BASED_SAMPLING)return[!0,c!=null?c:1,d("QuickPerformanceLoggerTypes").USER_BASED_SAMPLING];c=(a=(a=this.$8.get(a))!=null?a:c)!=null?a:this.$3;a=e!=null?e:d("QuickPerformanceLoggerTypes").EVENT_BASED_SAMPLING;e=n(c,b);return[e,c,a]};b.$14=function(a){this.$4.forEach(function(b){b.onUploadEvent&&b.onUploadEvent(a)});var b=this.$1.decorateEventBeforeUpload?this.$1.decorateEventBeforeUpload(a):a;this.$1.sendEvent(a,b)};b.$17=function(a,b,c){if(a.timeoutMS==null)return!1;c=c-b;return c>=a.timeoutMS};b.getActiveMarkerIds=function(a){var b=a.type,c=new Set();this.activeMarkers.forEach(function(a,d){a.forEach(function(a){if(a.type===b){c.add(d);return}})});return Array.from(c)};b.getActiveE2ETraceIds=function(){var a=new Map();this.activeMarkers.forEach(function(b,c){var d=new Map();b.forEach(function(a,b){a.e2eTracingID!=null&&d.set(b,{e2eTraceID:a.e2eTracingID,qplEvent:a.event})});d.size>0&&a.set(c,d)});return a};b.getActiveE2ETraceIdsAsArray=function(){var a=[];this.activeMarkers.forEach(function(b,c){b.forEach(function(b,c){b.e2eTracingID!=null&&a.push(b.e2eTracingID)})});return a};b.forEachMarkerInstance=function(a,b){a=this.$10(a);if(!a)return;for(a of a.entries()){var c=a[0],d=a[1];b(c,d.event)}};b.getMarkerStartTs=function(a,b){b=b===void 0?{}:b;b=b.instanceKey;b=b===void 0?0:b;a=this.getMarker(a,b);if(!a)return;return a.timestamp};b.markerLogDebugPoints=function(a,b){b=b===void 0?{}:b;b=b.instanceKey;b=b===void 0?0:b;var e=(h||(h=d("QPLEvent"))).getMarkerId(a);e=this.getMarker(a,b);if(!e)return;a=c("uuidv4")();e.uniqueMarkerDebugId=a};b.$18=function(a){switch(a){case 1:return"join_request_";case 0:return"join_request_";case 3:return"join_response_";case 2:return"join_response_";default:return null}};return a}();o.normalizeAnnotations=j;g["default"]=o}),98);
__d("QPLInspector",[],(function(a,b,c,d,e,f){a=function(){function a(){this.$1=[],this.$2={}}var b=a.prototype;b.appendLog=function(a){for(var b in this.$2){if(!Object.prototype.hasOwnProperty.call(this.$2,b)||a.marker_id!==b)continue;this.$2[a.marker_id].forEach(function(b){return b(a)})}this.$1.push(a)};b.dumpLogs=function(){return this.$1};b.addListener=function(a,b){Object.prototype.hasOwnProperty.call(this.$2,a)||(this.$2[a]=[]),this.$2[a].push(b),this.$1.forEach(function(c){c.marker_id===a&&b(c)})};b.removeListener=function(a,b){b=this.$2[a].indexOf(b);b!==-1&&this.$2[a].splice(b,1)};return a}();b=new a();f["default"]=b}),66);
__d("QuickPerformanceLogger",["Arbiter","Env","FBLogger","ODS","PerfFalcoEvent","PerfXSharedFields","Promise","QPLCore","QPLEvent","QPLInspector","Run","WebStorage","clearTimeout","cr:1984081","cr:686","gkx","guid","performanceAbsoluteNow","performanceNavigationStart","performanceNow","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l,m,n,o=typeof window!=="undefined"?window:self;function p(a){var b=c("PerfXSharedFields").getCommonData();b={memory_stats:{total_mem:b.ram_gb!=null?b.ram_gb*1073741824:null},network_stats:{downlink_megabits:b.downlink_megabits,network_subtype:b.effective_connection_type,rtt_ms:b.rtt_ms},sitedata_info:{client_push_phase:b.client_push_phase,client_revision:b.client_revision,server_revision:b.server_revision},locale_info:{locale:b.locale,isRTL:b.isRTL}};c("gkx")("20836")&&(b.workplace_info={is_gemini:c("gkx")("21050")});if(c("gkx")("21051")&&typeof o.__sapienzMetadataCallback__==="function"){var d=o.__sapienzMetadataCallback__();b.sapienz={request_id:String(d.requestId),config_name:String(d.configName)}}return babelHelpers["extends"]({},a,{metadata:babelHelpers["extends"]({},a.metadata,{},b)})}function q(a,b,c){return{i:a,m:c,r:b}}function r(a,d){return new(n||(n=b("Promise")))(function(){var b=d||a;typeof o.__je2e_recordQPLMarker==="function"&&o.__je2e_recordQPLMarker(b);(m||(m=c("Env"))).enable_qplinspector===!0&&c("QPLInspector").appendLog(b);c("gkx")("21053")||c("gkx")("20935")||c("gkx")("5679")?c("PerfFalcoEvent").logImmediately(function(){return b}):c("gkx")("2160")||c("gkx")("21055")||c("gkx")("1624")?c("PerfFalcoEvent").logCritical(function(){return b}):c("PerfFalcoEvent").log(function(){return b})})}function s(a){a=(h||(h=d("QPLEvent"))).getMarkerId(a);var b=a+"_"+c("guid")();(l||(l=d("ODS"))).bumpEntityKey(2401,"obc.www.all","qpl.e2e_tracing_id_generated."+a);return b}function t(a){a=babelHelpers["extends"]({},a,{config_type:c("gkx")("21056")?"alpha_beta":"prod"});return p(a)}function u(){return(m||(m=c("Env"))).enable_qplinspector===!0||typeof o.__je2e_recordQPLMarker==="function"||c("gkx")("21057")}function v(){c("Arbiter").inform("qpl_debugger_finished")}var w=(i||(i=c("performanceAbsoluteNow")))();typeof o.__je2e_felabsTracePlugin_setQplInit==="function"&&o.__je2e_felabsTracePlugin_setQplInit((j||(j=c("performanceNow")))());var x={debug:function(a,b,c){},warn:function(a){c("FBLogger")("qpl").blameToPreviousDirectory().warn(a)}},y="qpl";a=function(a){babelHelpers.inheritsLoose(e,a);function e(){var b;b=a.call(this,{decorateEventBeforeUpload:t,unsampleAllEvents:u,onDebuggingIdEnded:v,monotonicNowMs:i||(i=c("performanceAbsoluteNow")),unixNowMs:i||(i=c("performanceAbsoluteNow")),moduleLoadTimestamp:w,logger:x,sendEvent:r,e2eTracingIDGenerator:s,runtimeAbstractionLayer:{setTimeout:c("setTimeout"),clearTimeout:c("clearTimeout")},debugLoggingEnabled:(m||(m=c("Env"))).qpl_debug_logging})||this;var e=c("gkx")("21055")?c("gkx")("21058")?function(a){return d("Run").onBeforeUnload(a,!1)}:d("Run").onBeforeUnload:d("Run").onUnload;e(function(){b.$QuickPerformanceLogger$p_1(),b.$QuickPerformanceLogger$p_2(706,{respectUnloadPolicy:!0,timestamp:b.currentTimestamp()})});b.$QuickPerformanceLogger$p_3();b.initQplFlipperPlugin();b.initQplSapienzPlugin();return b}var f=e.prototype;f.$QuickPerformanceLogger$p_3=function(){var a=this,b=(k||(k=c("WebStorage"))).getSessionStorageForRead();if(!b){this.$QuickPerformanceLogger$p_4("#loadSavedState","sessionStorage is not available");return}var d=b.getItem(y);if(d==null)return;b.removeItem(y);b=JSON.parse(d);if(b==null){this.$QuickPerformanceLogger$p_4("#loadSavedState","Saved state failed to deserialize");return}if(b.markers==null||b.markers.length===0){this.$QuickPerformanceLogger$p_4("#loadSavedState","No saved markers found");return}b.markers.forEach(function(b){var c=b[0],d=b[1];b=b[2];a.addMarker(c,d,b);a.$QuickPerformanceLogger$p_4("#loadSavedState","Marker "+c+" (instanceKey: "+d+") resumed")})};f.$QuickPerformanceLogger$p_1=function(){var a=this,b=[];this.activeMarkers.forEach(function(a,c){a.forEach(function(a,d){a.resumeAfterNavigation===!0&&(delete a.resumeAfterNavigation,b.push([c,d,a]))})});if(b.length>0){var d={markers:b},e=(k||(k=c("WebStorage"))).getSessionStorage();e=k.setItemGuarded(e,y,JSON.stringify(d));e&&(b.forEach(function(b){var c=b[0],d=b[1];b=b[2];c=q(c,b.sampleRate,b.samplingMethod);a.markerEnd(c,96,d)}),this.$QuickPerformanceLogger$p_4("#storeSavedState","Failed to store saved state: "+e.message),c("FBLogger")("qpl").catching(e).warn("Failed to store QPL state: "+JSON.stringify(d,null,2)));b.forEach(function(b){var c=b[0];b=b[1];a.deleteMarker(c,b)})}};f.markerStoreBeforeNavigation=function(a,b){b=b===void 0?{}:b;b=b.instanceKey;b=b===void 0?0:b;var c=(h||(h=d("QPLEvent"))).getMarkerId(a);a=this.getMarker(a,b);if(!a){this.$QuickPerformanceLogger$p_4("markerStoreBeforeNavigation","Failed to set marker to store on page unload. Could not find marker "+c+", instancekey="+b);return}a.resumeAfterNavigation=!0;this.$QuickPerformanceLogger$p_4("markerStoreBeforeNavigation","Set marker "+c+" to store on page unload, instancekey="+b)};f.markerStartFromNavStart=function(a,b,d){b===void 0&&(b=0);d=d===void 0?{}:d;var e=d.cancelExisting;e=e===void 0?!1:e;var f=d.cancelOnUnload;f=f===void 0?!1:f;var g=d.trackedForLoss;g=g===void 0?!1:g;var h=d.type;h=h===void 0?1:h;var i=d.qplInternalDoNotUseConvertToTimeOnServer,j=d.onMarkerTimeout__DoNotUse;d=d.timeoutMS;d=d===void 0?null:d;var k=c("performanceNavigationStart")();i=typeof i==="function"?i(k):void 0;this.markerStart(a,b,k,{cancelExisting:e,cancelOnUnload:f,trackedForLoss:g,type:h,qplInternalDoNotUseAbsoluteTimeOrigin:i,onMarkerTimeout__DoNotUse:j,timeoutMS:d});if(c("performanceNavigationStart").isPolyfilled){k=this.getMarker(a,b);k&&(k.timestampIsApproximate=!0)}};f.markerStartForJoinFromNavStart=function(a,b,d){d=d===void 0?{}:d;var e=d.instanceKey;e=e===void 0?0:e;var f=d.cancelExisting;f=f===void 0?!1:f;var g=d.cancelOnUnload;g=g===void 0?!1:g;var h=d.trackedForLoss;h=h===void 0?!1:h;var i=d.type;i=i===void 0?1:i;var j=d.qplInternalDoNotUseConvertToTimeOnServer,k=d.absoluteTimeOriginMs,l=d.sourceIsPrimary;l=l===void 0?!1:l;var m=d.closeSession,n=d.unreliableSourceClockProcessId,o=d.onMarkerTimeout__DoNotUse;d=d.timeoutMS;var p=c("performanceNavigationStart")();j=typeof j==="function"?j(p):void 0;this.markerStartForJoin(a,b,{instanceKey:e,cancelExisting:f,cancelOnUnload:g,trackedForLoss:h,type:i,qplInternalDoNotUseAbsoluteTimeOrigin:j,monotonicTimestamp:p,absoluteTimeOriginMs:k,sourceIsPrimary:l,closeSession:m,unreliableSourceClockProcessId:n,timeoutMS:d,onMarkerTimeout__DoNotUse:o});if(c("performanceNavigationStart").isPolyfilled){b=this.getMarker(a,e);b&&(b.timestampIsApproximate=!0)}};f.$QuickPerformanceLogger$p_2=function(a,b){var c=this;b=b===void 0?{}:b;var d=b.timestamp,e=b.respectUnloadPolicy;this.activeMarkers.forEach(function(b,f){b.forEach(function(b,g){if(!e||b.cancelOnUnload===!0){b=q(f,b.sampleRate,b.samplingMethod);c.markerEnd(b,a,g,d)}})})};f.$QuickPerformanceLogger$p_4=function(a,b,c){x.debug(a,b,c)};f.initQplFlipperPlugin=function(){b("cr:686")!=null&&this.addListener(b("cr:686").qplFlipperPlugin.listener())};f.initQplSapienzPlugin=function(){b("cr:1984081")!=null&&this.addListener(b("cr:1984081").getQplSapienzListener())};return e}(c("QPLCore"));e=new a();f.exports=e}),34);
__d("OneTraceQPLLogger",["QuickPerformanceLogger","performanceNavigationStart","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j={CANCEL:4,CANCEL_BACKGROUND:630,CANCEL_NAVIGATION:615,DROPPED:216,ERROR:87,FAIL:3,OFFLINE:160,START:1,SUCCESS:2,TIMEOUT:113},k=c("performanceNavigationStart")();function a(a,b){if(a==null)return;(h||(h=c("QuickPerformanceLogger"))).markerStart(a,b.instanceKey,b.startTime+k)}function b(a,b){if(a==null)return;(h||(h=c("QuickPerformanceLogger"))).markerAnnotate(a,b.annotations,{instanceKey:b.instanceKey});for(var d in b.markerPoints)(h||(h=c("QuickPerformanceLogger"))).markerPoint(a,d,{data:b.markerPoints[d].data,instanceKey:b.instanceKey,timestamp:b.markerPoints[d].timeSinceStart+k});d=j[b.status];h.markerEnd(a,d,b.instanceKey,((a=b.endTime)!=null?a:(i||(i=c("performanceNow")))())+k)}g.qplActionMap=j;g.initQPL=a;g.logQPL=b}),98);
__d("InteractionTracingConfigDefault",["OneTraceQPLLogger","SiteData","gkx","performanceNavigationStart","qex"],(function(a,b,c,d,e,f,g){"use strict";b=6e4;e=new Set(["AppTiming","TierFlush","NavigationTiming","VisualCompletion","TestMetrics","ServerTimings"]);f=c("gkx")("22968")&&c("gkx")("1514");function a(){var a,b=c("gkx")("8821")||c("gkx")("9389"),d=c("gkx")("10863"),e=c("gkx")("10865");a=(a=c("qex")._("1696"))!=null?a:!1;return b||d||e||a}a={cancelOnBackground:f,defaultTracePolicy:"default",enableMemoryLogging:c("gkx")("20980"),enableVCTracker:!0,logLateMutationReactStack:c("gkx")("20981"),onlyLogLateMutationAfterSSRPaint:a(),logVCReactStack:c("gkx")("20982"),heroLatePlaceholderDetection:c("gkx")("20983"),heroDebugTracing:c("gkx")("10726"),pkgCohort:c("SiteData").pkg_cohort,timeout:b,qplActionCancelOnNavigation:c("gkx")("8460"),qplActionMap:d("OneTraceQPLLogger").qplActionMap,qplBaseTimestamp:c("performanceNavigationStart")(),useDocumentBodyForVCRoot:!0,navigationCancelsInteractions:!1,heroNestedRootsFix:c("gkx")("20984"),failLoggingFixCometErrorBoundary:c("gkx")("11582"),failOnCometErrorBoundary:c("gkx")("1154")?"fail":c("gkx")("196")?"annotate":"disabled",qplPointFilterRegex:/(^(server_)?adp_|Relay_|late_mutation.+([2-9]|\d\d))|(.+_)?ssr_.+([2-9]|\d\d)|Queue_|Render_/,cleanUpTraceTimeout:c("gkx")("13090")?10*1e3:30*60*1e3,allowedQPLPointTypes:e,clientRevision:c("SiteData").client_revision,serverRevision:(f=c("SiteData").server_revision)!=null?f:0,pushPhase:c("SiteData").push_phase,qplEnableE2ETracing:c("gkx")("15620")};g.DEFAULT_TRACING_CONFIG=a}),98);
__d("addAnnotations",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){Object.keys(b).forEach(function(c){var d;a[c]=Object.assign((d=a[c])!=null?d:{},b[c])})}f["default"]=a}),66);
__d("react-relay/relay-hooks/ProfilerContext",["react"],(function(a,b,c,d,e,f){"use strict";var g;a=g||b("react");c=a.createContext({wrapPrepareQueryResource:function(a){return a()}});e.exports=c}),null);
__d("useStable",["react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useRef;function a(a){var b=i(null),c=b.current;if(c===null){a=a();b.current={value:a};return a}else return c.value}g["default"]=a}),98);
__d("mergeRefs",["recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";function a(){for(var a=arguments.length,b=new Array(a),d=0;d<a;d++)b[d]=arguments[d];return function(a){var d=[];b.forEach(function(b){if(b==null)return;if(typeof b==="function"){var e=b(a);typeof e==="function"?d.push(e):d.push(function(){return b(null)});return}if(typeof b==="object"){b.current=a;d.push(function(){b.current=null});return}c("recoverableViolation")("mergeRefs cannot handle Refs of type boolean, number or string, received ref "+String(b)+" of type "+typeof b,"comet_ui")});return function(){d.forEach(function(a){return a()})}}}g["default"]=a}),98);
__d("foregroundRequestAnimationFrame",["Visibility","cancelAnimationFrame","clearTimeout","requestAnimationFrame","setTimeout"],(function(a,b,c,d,e,f,g){function a(a){if(c("Visibility").isHidden()){var b=c("setTimeout")(a,0);return function(){c("clearTimeout")(b)}}else{var d=c("requestAnimationFrame")(a);return function(){c("cancelAnimationFrame")(d)}}}g.foregroundRequestAnimationFrame=a}),98);
__d("MemoryUtils",[],(function(a,b,c,d,e,f){"use strict";function g(){return window.performance&&window.performance.memory}function h(){return window.navigator&&window.navigator.deviceMemory}function a(){return window.performance&&typeof window.performance.measureUserAgentSpecificMemory==="function"}function i(a){return typeof a.replace==="function"?a.replace(/\n/g," ").replace(/\s+/g," "):null}function b(){if(typeof window.gc!=="function")return!1;return typeof window.gc.toString!=="function"?!1:i(window.gc.toString())==="function gc() { [native code] }"}var j=b()?window.gc:null;function c(a){a===void 0&&(a={runGC:!0});var b=null;if(h()){var c=parseFloat(window.navigator.deviceMemory);isNaN(c)||(b=c*1024*1024*1024)}if(g()){j!=null&&a.runGC&&j();a=((c=window.performance)==null?void 0:c.memory)||{};c=a.jsHeapSizeLimit;var d=a.totalJSHeapSize;a=a.usedJSHeapSize;return{usedJSHeapSize:a,jsHeapSizeLimit:c,totalJSHeapSize:d,deviceMemory:b}}return{usedJSHeapSize:null,jsHeapSizeLimit:null,totalJSHeapSize:null,deviceMemory:null}}f.isMemoryAPISupported=g;f.isMeasureMemoryOriginTrialSupported=a;f.isGarbageCollectionAPIAvailable=b;f.getCurrentMemory=c}),66);
__d("VisibilityAPI",["performanceNow"],(function(a,b,c,d,e,f,g){var h,i=((f=self.document)==null?void 0:f.visibilityState)!==void 0||((f=self.document)==null?void 0:f.hidden)!==void 0;function a(){if(i)return document.visibilityState!==void 0?document.visibilityState==="hidden":document.hidden;else return!1}function b(a){var b=function(b){b=(b=b==null?void 0:b.timeStamp)!=null?b:(h||(h=c("performanceNow")))();a(b)};document.addEventListener("visibilitychange",b);return b}function d(a){document.removeEventListener("visibilitychange",a)}function e(){return 0}g.canUseVisibilityAPI=i;g.isVisibilityHidden=a;g.onVisibilityChange=b;g.removeVisibiltyChangeListener=d;g.getEarliestHiddenStartTime=e}),98);
__d("VisibilityState",["VisibilityAPI","performanceNow"],(function(a,b,c,d,e,f,g){var h,i=1e3,j=[],k=null;f=!1;var l=new Set();f||(d("VisibilityAPI").isVisibilityHidden()&&(k=d("VisibilityAPI").getEarliestHiddenStartTime()),f=!0);d("VisibilityAPI").canUseVisibilityAPI&&d("VisibilityAPI").onVisibilityChange(function(a){m(a,d("VisibilityAPI").isVisibilityHidden()),l.forEach(function(b){b(a,d("VisibilityAPI").isVisibilityHidden())})});function m(a,b){b?k=a:k!=null&&(j.push({end:a,start:k}),j.length>i&&j.shift(),k=null)}function a(a){l.add(a);return function(){l["delete"](a)}}function b(a){l["delete"](a)}function n(a,b){var d=[],e=Array.from(j);if(k!=null){var f=k;e.push({end:(h||(h=c("performanceNow")))(),start:f})}e.forEach(function(c){c.start<=a&&c.end>a?c.end<=b?d.push({end:c.end,start:a}):d.push({end:b,start:a}):c.start>a&&c.start<=b&&(c.end<=b?d.push({end:c.end,start:c.start}):d.push({end:b,start:c.start}))});return d}function e(a,b){return n(a,b).length>0}g.subscribe=a;g.unsubscribe=b;g.getHiddenSpans=n;g.wasHidden=e}),98);
__d("WebAPIs",["VisibilityAPI"],(function(a,b,c,d,e,f,g){"use strict";f=[];var h="IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype?window.IntersectionObserver:null;function a(a){return a.isIntersecting!=null?a.isIntersecting:a.intersectionRatio>0||a.intersectionRect&&(a.intersectionRect.height>0||a.intersectionRect.width>0)}h==null&&f.push("IntersectionObserver");var i=window.MutationObserver||window.WebKitMutationObserver;i==null&&f.push("MutationObserver");d("VisibilityAPI").canUseVisibilityAPI||f.push("VisibilityAPI");d=window.requestAnimationFrame;d==null&&f.push("requestAnimationFrame");var j=window.cancelAnimationFrame;d==null&&f.push("cancelAnimationFrame");function b(a){try{window.addEventListener("beforeunload",a)}catch(a){return null}return{remove:function(){try{window.removeEventListener("beforeunload",a)}catch(a){if(a.message!=="can't access dead object")throw a}}}}var k=function(){function a(a){this.$1=a}var b=a.prototype;b.deref=function(){return this.$1};return a}();k=typeof window.WeakRef==="function"?window.WeakRef:k;typeof window.WeakRef!=="function"&&f.push("WeakRef");var l=window.WeakMap;typeof window.WeakMap!=="function"&&f.push("WeakMap");typeof window.WeakSet!=="function"&&f.push("WeakSet");typeof window.FinalizationRegistry!=="function"&&f.push("FinalizationRegistry");function c(a){return a!=null&&a.isConnected!==!1}function e(a){a=a==null?void 0:a.deref();return a!=null&&a.isConnected!==!1?a:null}g.unavailableAPIs=f;g.IntersectionObserver=h;g.intersectionObserverEntryIsIntersecting=a;g.MutationObserver=i;g.requestAnimationFrame=d;g.cancelAnimationFrame=j;g.onBeforeUnload=b;g.WeakRef=k;g.WeakMap=l;g.isAttachedElement=c;g.derefOnlyAttachedElement=e}),98);
__d("VisualCompletionConstants",[],(function(a,b,c,d,e,f){"use strict";a={ATTRIBUTE_NAME:"data-visualcompletion",HERO_TRACING_HOLD:"HeroTracing",HERO_LATE_PLACEHOLDER_DETECTION:"HeroLatePlaceholderDetection",INTERACTION_TRACING_HOLD:"InteractionTracing",IGNORE:"ignore",IGNORE_DYNAMIC:"ignore-dynamic",IGNORE_LATE_MUTATION:"ignore-late-mutation",LOADING_STATE:"loading-state",MEDIA_VC_IMAGE:"media-vc-image",CSS_IMG:"css-img"};f["default"]=a}),66);
__d("VisualCompletionAttributes",["VisualCompletionConstants"],(function(a,b,c,d,e,f,g){"use strict";var h;a={IGNORE:(a={},a[(h||(h=c("VisualCompletionConstants"))).ATTRIBUTE_NAME]=h.IGNORE,a),IGNORE_DYNAMIC:(b={},b[h.ATTRIBUTE_NAME]=h.IGNORE_DYNAMIC,b),IGNORE_LATE_MUTATION:(d={},d[h.ATTRIBUTE_NAME]=h.IGNORE_LATE_MUTATION,d),LOADING_STATE:(e={},e[h.ATTRIBUTE_NAME]=h.LOADING_STATE,e),MEDIA_VC_IMAGE:(f={},f[h.ATTRIBUTE_NAME]=h.MEDIA_VC_IMAGE,f),CSS_IMG:(c={},c[h.ATTRIBUTE_NAME]=h.CSS_IMG,c)};g["default"]=a}),98);
__d("VisualCompletionTraceObserver",[],(function(a,b,c,d,e,f){"use strict";var g=[],h=[],i=[];a={subscribe:function(a){g.push(a)},subscribeStart:function(a){h.push(a)},unsubscribe:function(a){g=g.filter(function(b){return b!==a})},unsubscribeStart:function(a){h=h.filter(function(b){return b!==a})},emit:function(a){g.forEach(function(b){return b(a)})},emitStart:function(a){h.forEach(function(b){return b(a)})},subscribeImageLoadStart:function(a){i.push(a)},unsubscribeImageLoadStart:function(a){i=i.filter(function(b){return b!==a})},emitImageLoadStart:function(a,b,c,d){i.forEach(function(e){return e(a,b,c,d)})}};f["default"]=a}),66);
__d("IteratableElementWeakMap",["WebAPIs"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(){this.$1=[],this.$2=new(d("WebAPIs").WeakMap)()}var b=a.prototype;b["delete"]=function(a){this.$2["delete"](a),this.$1=this.$1.filter(function(b){b=b.deref();return b!==a&&b!=null})};b.set=function(a,b){this.$1=this.$1.filter(function(b){b=b.deref();return b!==a&&b!=null});var c=new(d("WebAPIs").WeakRef)(a);this.$1.push(c);this.$2.set(a,b)};b.get=function(a){return this.$2.get(a)};b.clear=function(){this.$1=[],this.$2=new(d("WebAPIs").WeakMap)()};b.has=function(a){return this.$2.has(a)};b.size=function(){var a=0;for(var b of this.$1){var c=b.deref();c!=null&&a++}return a};b.entries=function*(){this.$1=this.$1.filter(function(a){return a.deref()!=null});for(var a of this.$1){var b=a.deref();if(b){var c=this.$2.get(b);c!=null&&(yield [b,c])}}};b.keys=function*(){var a=this.entries();for(a of a){var b=a[0];a[1];yield b}};b.values=function*(){var a=this.entries();for(a of a){a[0];var b=a[1];yield b}};b.forEach=function(a,b){var c=this.entries();for(c of c){var d=c[0],e=c[1];a.call(b,e,d,this)}};return a}();g["default"]=a}),98);
__d("ResourceTimingAPI",["performance"],(function(a,b,c,d,e,f,g){"use strict";var h,i=!1,j=!1;function a(){i||(typeof ((h||(h=c("performance")))==null?void 0:(h||(h=c("performance"))).getEntriesByName)!=="function"||typeof ((h||(h=c("performance")))==null?void 0:(h||(h=c("performance"))).getEntriesByType)!=="function"?(j=!1,i=!0):(h||(h=c("performance"))).getEntriesByType("resource").length>0&&(j=!0,i=!0));return j}function b(a){if(typeof (h||(h=c("performance"))).getEntriesByName!=="function")return null;var b=(h||(h=c("performance"))).getEntriesByName(a);b.length===0&&a.indexOf("#")>=0&&(b=(h||(h=c("performance"))).getEntriesByName(a.split("#")[0]));return b&&b[0]}g.canUseResourceTimingAPI=a;g.getResourceTiming=b}),98);
__d("LCP",[],(function(a,b,c,d,e,f){"use strict";var g=typeof ((b=window.PerformanceObserver)==null?void 0:(c=b.supportedEntryTypes)==null?void 0:c.includes)==="function"&&window.PerformanceObserver.supportedEntryTypes.includes("largest-contentful-paint");function h(){return document==null?[]:Array.from(document.querySelectorAll('link[rel="preload"][as="image"]')).map(function(a){return(a=a.attributes.getNamedItem("href"))==null?void 0:a.value})}function a(){if(!g)return null;var a=null,b=new window.PerformanceObserver(function(b){for(b of b.getEntries())a=b});b.observe({buffered:!0,type:"largest-contentful-paint"});return function(){var c;b.disconnect();return a==null?null:{className:(c=a.element)==null?void 0:c.className,element:(c=a.element)==null?void 0:c.tagName,size:a.size,timestamp:a.startTime,url:a.url,preloaded:h().includes((c=a)==null?void 0:c.url)}}}f.getLCPCallback=a}),66);
__d("ResourceDownloadLogger",["performance"],(function(a,b,c,d,e,f,g){"use strict";var h,i={JS:"js",CSS:"css",IMG:"img",TRANSLATIONS:"translations"};function j(a){var b=a.indexOf("?");return b===-1?a:a.substring(0,b)}function k(a,b){var c=b.substring(b.lastIndexOf(".")+1);if(c==="js")return b.includes("rsrc-translations.php")?i.TRANSLATIONS:i.JS;else if(c==="css")return i.CSS;return a}function l(){return Object.keys(i).reduce(function(a,b){b=i[b];a[b]={cacheCount:0,cacheRate:0,encodedBodySize:0,decodedBodySize:0,totalCount:0,transferSize:0};return a},{})}function a(a,b){if(typeof (h||(h=c("performance"))).getEntriesByType!=="function")return new Map();var d=l(),e=(h||(h=c("performance"))).getEntriesByType("resource").filter(function(c){return c.startTime>=a&&c.startTime<=b}).map(function(a){return{encodedBodySize:a.encodedBodySize,decodedBodySize:a.decodedBodySize,transferSize:a.transferSize,type:k(a.initiatorType,j(a.name))}}).filter(function(a){return d[a.type]});e.forEach(function(a){var b=d[a.type];b.encodedBodySize+=a.encodedBodySize;b.decodedBodySize+=a.decodedBodySize;b.transferSize+=a.transferSize;b.totalCount++;a.transferSize===0&&b.cacheCount++});e=new Map();for(var f in d){var g=d[f];g.totalCount>0&&(g.cacheRate=Math.round(g.cacheCount/g.totalCount*100));for(var i in g)e.set(f+"_"+i,g[i])}return e}g["default"]=a}),98);
__d("currentVCTraces",[],(function(a,b,c,d,e,f){var g=null,h=null,i=new Map();function a(){return new Map(i)}function b(a,b){i.set(a,b)}function c(a){i["delete"](a)}function d(){return g}function e(a){g=a}function j(){return h}function k(a){h=a}f.getCurrentVCTraces=a;f.addVCTrace=b;f.removeVCTrace=c;f.getCurrentNavigationVCTrace=d;f.setCurrentNavigationVCTrace=e;f.getLastNavigationVCReport=j;f.setLastNavigationVCReport=k}),66);
__d("VisualCompletionUtil",["ImageDownloadTracker","ResourceTimingAPI","VisualCompletionConstants","VisualCompletionTraceObserver","WebAPIs","addAnnotations","currentVCTraces","performance","performanceNavigationStart","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=/url\(.*(http.*)\)/gi;function l(a){return(a.right-a.left)*(a.bottom-a.top)}function m(a,b){return{bottom:Math.max(Math.min(a.bottom,b.bottom),b.top),left:Math.min(Math.max(a.left,b.left),b.right),right:Math.max(Math.min(a.right,b.right),b.left),top:Math.min(Math.max(a.top,b.top),b.bottom)}}function n(a){return{height:window.innerHeight,width:window.innerWidth}}function a(a,b){if(typeof a.getBoundingClientRect!=="function")return!1;var c=n(b);a=a.getBoundingClientRect();var d=a.bottom,e=a.left,f=a.right;a=a.top;d=l(m({bottom:d,left:e,right:f,top:a},{bottom:c.height,left:0,right:c.width,top:-o(b).scrollY}));return d>0}function o(a){var b;a=(a=window.scrollX)!=null?a:0;b=(b=window.scrollY)!=null?b:0;return{scrollX:a,scrollY:b}}function b(a,b){var c=0;a=a;while(a&&a.offsetParent&&typeof a.offsetTop==="number")c+=a.offsetTop,a=a.offsetParent;if(a&&a.offsetParent==null&&typeof a.getBoundingClientRect==="function"){var d=a.getBoundingClientRect();d=d.top;d>=0?c+=d:a===document.body&&(c-=b.initialScrollY)}return c}function e(a){return a.split("#")[0]}function p(a){if(a==null||a==="")return!1;a=a.replace(/ /g,"");return a.indexOf("display:none")>=0||a.indexOf("visibility:hidden")>=0||a.indexOf("content-visibility:hidden")>=0}function q(a){a=a;while(a){if(typeof a.getAttribute==="function"&&p(a.getAttribute("style"))||typeof a.hasAttribute==="function"&&a.hasAttribute("hidden"))return!0;a=a.parentElement}return!1}function r(a,b){if(b.has(a))return b.get(a)===!0;var c=!1;typeof a.getAttribute==="function"&&p(a.getAttribute("style"))||typeof a.hasAttribute==="function"&&a.hasAttribute("hidden")?c=!0:a.parentElement!=null&&(c=r(a.parentElement,b));b.set(a,c);return c}function s(a,b,d){if(b.config.invisibleStyleMemoization===!0&&d!=null){if(r(a,d))return!0}else if(q(a))return!0;d=a;while(d){if(b.mutationRoots.has(d))return!1;if(b.pendingLoadingElements.has(d))return!0;if(typeof d.getAttribute==="function"){var e=d.getAttribute((h||(h=c("VisualCompletionConstants"))).ATTRIBUTE_NAME);if(e===(h||(h=c("VisualCompletionConstants"))).IGNORE)return!0;if(d!==a&&e===(h||(h=c("VisualCompletionConstants"))).IGNORE_DYNAMIC){b.excludeElement(a);return!0}}d=d.parentElement}return!1}function t(a,b,c){if(K(b)&&!s(b,a)&&b.textContent!=null&&b.parentElement!=null){a=b.parentElement;if(J(a)){b=b.textContent;typeof b==="string"&&(b=b.trim());if(b!=="")if(c!=null&&c.trim()===b)return null;else return a}}return null}function f(a,b,c){var d=[],e=0,f=null;c.config.invisibleStyleMemoization===!0&&(f=new Map());Array.from(a).forEach(function(a){if(c.config.max_mutation_record!=null&&++e>c.config.max_mutation_record)return;if(!J(a.target)&&!K(a.target))return;var g=a.target;if(a.type==="childList"&&a.addedNodes&&a.addedNodes.length)Array.from(a.addedNodes).forEach(function(a){a=a;if(s(a,c,f))return;if(u(a)){B(c.mutationSeq,a,"imgLoad",b,c);return}else if(J(a))typeof a.querySelectorAll==="function"&&A(c.mutationSeq,a,b,c,f),d.push([a,"mutationAdd"]);else if(c.config.observeTextMutation&&K(a)){a=t(c,a);a!=null&&d.push([a,"mutationTextAdd"])}});else if(a.type==="attributes"&&!s(g,c,f))if(a.attributeName==="hidden")g.hasAttribute("hidden")||d.push([g,"mutationHiddenAttribute"]);else if(a.attributeName==="style"){var h=g.getAttribute("style"),i=a.oldValue;p(i)&&!p(h)&&d.push([g,"mutationStyleAttribute"])}else(I(g)==="image"&&a.attributeName==="href"||I(g)==="IMG"&&a.attributeName==="src")&&B(c.mutationSeq,g,"mutationImageAttribute",b,c);else if(c.config.observeTextMutation&&a.type==="characterData"&&K(g)){i=t(c,g,a.oldValue);i!=null&&d.push([i,"mutationTextUpdate"])}});c.config.invisibleStyleMemoization===!0&&f!=null&&f.clear();return d}function u(a){return I(a)==="IMG"||I(a)==="image"}function v(a){return I(a)==="picture"}function w(a){return u(a)||v(a)}function x(a){if(typeof window.getComputedStyle!=="function")return null;a=window.getComputedStyle(a);var b=a["background-image"];if(a.visibility!=="hidden"&&b&&b!=="none"){k.lastIndex=0;a=k.exec(b);if(a&&a.length>1)return a[1].replace('"',"")}return null}function y(a){if(a.reported)return;var b=(i||(i=c("performanceNow")))();a.checkViewport();a.mutationRoots.forEach(function(b){if(b&&typeof b.querySelectorAll==="function"){b=b.querySelectorAll("div,i,span,li");Array.prototype.forEach.call(b,function(b){if(b.textContent||b.children.length||a.cssBgElements.has(b)||s(b,a)||!a.inViewport(b))return;var c=x(b);a.bgCheckCount++;if(c==null)return;c={element:new(d("WebAPIs").WeakRef)(b),url:c,resourceTiming:d("ResourceTimingAPI").getResourceTiming(c)};a.cssBgElements.set(b,c)})}});a.cssBgScanOverhead=i()-b}function z(a,b){if(typeof a.getAttribute==="function"&&a.getAttribute((h||(h=c("VisualCompletionConstants"))).ATTRIBUTE_NAME)===(h||(h=c("VisualCompletionConstants"))).LOADING_STATE)b.waitLoadingState(a);else if(typeof a.querySelectorAll==="function"){a=a.querySelectorAll("["+(h||(h=c("VisualCompletionConstants"))).ATTRIBUTE_NAME+"="+h.LOADING_STATE+"]");Array.from(a).forEach(function(a){b.waitLoadingState(a)})}}function A(a,b,c,d,e){b=b.querySelectorAll("img, image");Array.from(b).forEach(function(b){if(s(b,d,e))return;B(a,b,"imgLoad",c,d)})}function B(a,b,d,e,f){if(f.trackedImages.has(b))return;else f.trackedImages.add(b);f.config.enable_image_logging_emitter&&b.getAttribute("data-imgperflogname")!=null&&c("VisualCompletionTraceObserver").emitImageLoadStart(b,d,e,f.interactionType);if(b.complete){b.getAttribute((h||(h=c("VisualCompletionConstants"))).ATTRIBUTE_NAME)===(h||(h=c("VisualCompletionConstants"))).MEDIA_VC_IMAGE&&f.scheduleIntersectionObserver(a,b,d,e);return}var g=b.currentSrc||b.src;if(I(b)==="image"&&typeof b.getAttribute==="function"){var j=b.getAttribute("xlink:href");j!=null&&(g=j)}if(g==null||g===""||g.indexOf("http")!==0)return;var k=function(){f.imageDone(b)},l=function(){f.imageWait(b)};if(b.getAttribute("loading")==="lazy"){j=C(a,b,d,e,k,f);var m=j.loadHandler,n=j.errorHandler;f.scheduleElementVisibleObserver(b,function(){if(f.reported||b.complete)return;l();b.addEventListener("load",m);b.addEventListener("error",n)},{executeOnce:!0,cleanAfterReport:!0})}else if(f.config.use_image_download_tracker===!0){var o=(i||(i=c("performanceNow")))();c("ImageDownloadTracker")(g,l).then(function(){var g=(i||(i=c("performanceNow")))();f.scheduleIntersectionObserver(a,b,d,e,o,g);k()})["catch"](function(){return k()})}else{j=C(a,b,d,e,k,f);g=j.loadHandler;j=j.errorHandler;l();b.addEventListener("load",g);b.addEventListener("error",j)}}function C(a,b,d,e,f,g){var h=(i||(i=c("performanceNow")))(),j=function j(){var l=(i||(i=c("performanceNow")))();g.scheduleIntersectionObserver(a,b,d,e,h,l);f();b.removeEventListener("load",j);b.removeEventListener("error",k)},k=function a(){f(),b.removeEventListener("load",j),b.removeEventListener("error",a)};return{loadHandler:j,errorHandler:k}}function D(a,b){return typeof a.getAttribute==="function"&&a.getAttribute((h||(h=c("VisualCompletionConstants"))).ATTRIBUTE_NAME)===b}function E(a){var b=function(){a.scrolled=!0,a.markerPoints.has("scroll_start")||a.addMarkerPoint("scroll_start",(i||(i=c("performanceNow")))())};try{window.addEventListener("scroll",b,{passive:!0}),a.onComplete(function(){window.removeEventListener("scroll",b)})}catch(a){if(a.message!=="can't access dead object")throw a}}function F(a,b){a instanceof HTMLImageElement&&(b.record.isMediaVCElement=D(a,(h||(h=c("VisualCompletionConstants"))).MEDIA_VC_IMAGE),b.record.imgNaturalWidth=a.naturalWidth,b.record.imgNaturalHeight=a.naturalHeight)}function G(a,b){typeof window.devicePixelRatio==="number"&&c("addAnnotations")(a.annotations,{"double":{devicePixelRatio:window.devicePixelRatio}});a.navSequence===1&&(j||(j=c("performance")))&&(j||(j=c("performance"))).timing&&((j||(j=c("performance"))).timing.responseStart&&a.markerPoints.set("TTFB",{timestamp:(j||(j=c("performance"))).timing.responseStart-c("performanceNavigationStart")()}));if(b.interactionType==="INITIAL_LOAD"){a=(j||(j=c("performance")))==null?void 0:(j||(j=c("performance"))).getEntriesByType==null?void 0:(j||(j=c("performance"))).getEntriesByType("paint");a&&a.forEach(function(a){a.name==="first-contentful-paint"&&b.addFirstMarkerPoint("FCP",a.startTime)})}}var H={attributeFilter:["hidden","style","href","src"],attributeOldValue:!0,attributes:!0,characterData:!0,childList:!0,subtree:!0};function I(a){return(a=a.tagName)!=null?a:""}function J(a){return a.nodeType===Node.ELEMENT_NODE&&a.tagName!=="SCRIPT"&&a.tagName!=="STYLE"&&a.tagName!=="LINK"}function K(a){return a.nodeType===Node.TEXT_NODE}function L(a){var b=[],c=d("currentVCTraces").getCurrentVCTraces();c.forEach(function(c){var d=a;while(d!=null){if(d instanceof HTMLElement&&c.mutationRoots.has(d)){b.push(c);break}d=d.parentElement}});return b}function M(a){var b=[];L(a).forEach(function(c){b.push(c.waitLoadingState(a))});return b}function N(a){return{onError:function(){},onLoad:function(){},unmountCallback:function(){}}}function O(a){L(a).forEach(function(b){b.excludeElement(a)})}function P(a){var b=4,c=0,d=0;a.visualChangeRecordList=a.visualChangeRecordList.filter(function(e){e=e.record;if(e.isMediaVCElement&&e.visibleAtMutation===!0&&e.imgNaturalHeight!=null&&e.imgNaturalWidth!=null){var f=e.imgNaturalHeight*e.imgNaturalWidth,g=Number(f<c*b),h={ignored:g,resolution:f,mutationType:e.mutationType,naturalWidth:String(e.imgNaturalWidth),naturalHeight:String(e.imgNaturalHeight)};c>0&&(h.ratio=f/c);a.addMarkerPoint("mediaVCImage_"+d++,e.paintTime,h);if(g)return!1;else{a.addMarkerPoint("ImageMediaVC",e.paintTime);c=f;return!0}}return!0})}g.getPixels=l;g.getRectIntersection=m;g.getViewportSize=n;g.isInAboveTheFold=a;g.getScrollPosition=o;g.offsetTop=b;g.trimHash=e;g.checkInvisibleStyle=p;g.checkInvisibleStyleElement=q;g.checkInvisibleStyleElementRecursive=r;g.shouldIgnoreMutation=s;g.getParentElementForTextNodeChange=t;g.extractMutationElements=f;g.isImage=u;g.isPicture=v;g.isImageOrPicture=w;g.getStyleBackground=x;g.scanCssBgElements=y;g.checkLoadingStates=z;g.trackAllChildImages=A;g.trackImage=B;g.getImageLoadHandlers=C;g.checkDOMElementAttributeValue=D;g.setupScrollHandler=E;g.getImageNaturalSize=F;g.addPlatformMetaData=G;g.mutationObserverConfig=H;g.getTagName=I;g.isElementNode=J;g.isTextNode=K;g.findVCTraces=L;g.trackLoadingState=M;g.trackImageLoad=N;g.ignoreElement=O;g.processMediaVCImage=P}),98);
__d("VisualCompletionUtilCommon",[],(function(a,b,c,d,e,f){"use strict";function a(a){return{bottom:a.bottom,left:a.left,right:a.right,top:a.top}}f.castToRect=a}),66);
__d("logVCUserTiming",[],(function(a,b,c,d,e,f){"use strict";function a(a){if(a.config.UserTimingUtils){var b=a.config.UserTimingUtils;a.stateLog.forEach(function(a,c){var d=a[0];a=a[1];b.measureModern(c,{end:a,start:d},"VC Tracker State","\ud83d\udcaa Hero Logger")});a.visualChangeRecordList.forEach(function(a){var c;a=a.record;c=(c=a.finalState)!=null?c:"";c=a.mutationType+"_"+c+"_"+a.veid;b.measureModern(c,{detail:babelHelpers["extends"]({},a),end:a.paintTime,start:a.mutationTime},"VC Tracker Mutation")})}}f["default"]=a}),66);
__d("VisualCompletionLogger",["CLS","IteratableElementWeakMap","LCP","ResourceDownloadLogger","ResourceTimingAPI","VisibilityState","VisualCompletionConstants","VisualCompletionTraceObserver","VisualCompletionUtil","VisualCompletionUtilCommon","WebAPIs","addAnnotations","logVCUserTiming","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j="manualVCPointCandidate",k=1;function l(){return{bottom:0,left:0,right:0,top:0}}a=function(){function a(a,b,e,f,g){this.bgCheckCount=0;this.$8=0;this.$9=0;this.cssBgScanOverhead=0;this.mutationProcessingOverhead=0;this.trackedImages=new WeakSet();this.initialScrollY=0;this.scrolledVC=0;this.reported=!1;this.measureOriginalViewport=!0;this.mutationRoots=new Set();this.mutatedElementCount=0;this.config=g;this.$3=new(g=c("IteratableElementWeakMap"))();this.$6=new g();this.$4=new g();this.$5=[];this.$1=[];this.$2=[];this.cssBgElements=new g();this.visualChangeRecordList=[];this.$10=new Set();this.startTime=a;this.$7={height:0,width:0};this.initialScrollY=f==="INITIAL_LOAD"?0:d("VisualCompletionUtil").getScrollPosition(this).scrollY;this.$11=this.initialScrollY;this.currentScrollY=this.initialScrollY;this.navSequence=b;this.traceID=e;this.markerPoints=new Map();this.stateLog=new Map();this.annotations={string:{},"int":{},"double":{},bool:{},string_array:{},int_array:{},double_array:{},bool_array:{}};this.tagSet=new Map();this.navDone=!1;this.interactionType=f;this.scrolled=!1;this.$12=d("CLS").getCLSCallback();f==="INITIAL_LOAD"&&(this.$13=d("LCP").getLCPCallback());c("VisualCompletionTraceObserver").emitStart(f)}var b=a.prototype;b.addAnnotation=function(a,b){var d;c("addAnnotations")(this.annotations,{string:(d={},d[a]=b,d)})};b.addAnnotationInt=function(a,b){var d;c("addAnnotations")(this.annotations,{"int":(d={},d[a]=b,d)})};b.addAnnotationDouble=function(a,b){var d;c("addAnnotations")(this.annotations,{"double":(d={},d[a]=b,d)})};b.addAnnotationBoolean=function(a,b){var d;c("addAnnotations")(this.annotations,{bool:(d={},d[a]=b,d)})};b.addAnnotationIntArray=function(a,b){var d;c("addAnnotations")(this.annotations,{int_array:(d={},d[a]=b,d)})};b.addAnnotationDoubleArray=function(a,b){var d;c("addAnnotations")(this.annotations,{double_array:(d={},d[a]=b,d)})};b.addAnnotationStringArray=function(a,b){var d;c("addAnnotations")(this.annotations,{string_array:(d={},d[a]=b,d)})};b.addAnnotationBooleanArray=function(a,b){var d;c("addAnnotations")(this.annotations,{bool_array:(d={},d[a]=b,d)})};b.addFirstMarkerPoint=function(a,b,c){c===void 0&&(c={});var d=this.markerPoints.get(a);b>=this.startTime&&(!d||d.timestamp>b)&&this.markerPoints.set(a,{data:c,timestamp:b})};b.addMarkerPoint=function(a,b,c){c===void 0&&(c={}),b>=this.startTime&&this.markerPoints.set(a,{data:c,timestamp:b})};b.addVCPointCandidate=function(a,b){b===void 0&&(b={}),this.addMarkerPoint(j,a,b)};b.addVisualElement=function(a,b,c,e,f,g,h,i,j){this.scrolled&&(this.currentScrollY=d("VisualCompletionUtil").getScrollPosition(this).scrollY,this.scrolled=!1);var l=g;g===!0&&h!=null&&(h.bottom===h.top||h.left===h.right)&&(l=!1);g={element:new(d("WebAPIs").WeakRef)(b),record:{detached:!1,imgLoadEnd:j,imgLoadStart:i,isMediaVCElement:!1,mutationSeq:a,mutationTime:e,mutationType:c,paintTime:f,rectAtMutation:h,scrollY:this.currentScrollY,veid:k++,visibleAtMutation:l,visibleAtTraceEnd:!1}};d("VisualCompletionUtil").getImageNaturalSize(b,g);this.visualChangeRecordList.push(g)};b.logLoadingElementStart=function(a,b,c){this.$4.set(a,{start:c,rectangle:d("VisualCompletionUtilCommon").castToRect(b)})};b.logLoadingElementEnd=function(a,b){var c=this.$4.get(a);c&&(this.$5.push(babelHelpers["extends"]({},c,{end:b})),this.$4["delete"](a))};b.trackPagelet=function(a,b,c,e,f){this.$6.set(a,{data:{},element:new(d("WebAPIs").WeakRef)(a),mutationSeq:0,name:b,pageletTypeName:f,points:{hydration:c-this.startTime},vcCallback:e})};b.addTag=function(a,b){this.tagSet.has(a)||this.tagSet.set(a,new Set());a=this.tagSet.get(a);a&&a.add(b)};b.checkViewport=function(){this.$7=d("VisualCompletionUtil").getViewportSize(this)};b.calculate=function(a){var b=this;a===void 0&&(a=!1);this.checkViewport();var e=(h||(h=c("performanceNow")))();this.$9=0;this.initTree();!a&&d("ResourceTimingAPI").canUseResourceTimingAPI()&&this.getBackgroundImages();this.calculatePaintedPixels();var f={annotations:this.annotations,cssBgScanOverhead:this.cssBgScanOverhead,bgChecked:this.bgCheckCount,bgNum:0,bgPixels:0,vcCalculationOverhead:0,mutationProcessingOverhead:this.mutationProcessingOverhead,compNum:0,compPixels:0,cssBgElements:Array.from(this.cssBgElements.values()),elements:this.$2,finalScrollY:this.$11,imgNum:0,imgPixels:0,initialScrollY:this.initialScrollY,interactionType:this.interactionType,loadingElements:this.$5,markerPoints:this.markerPoints,navComplete:h(),navSequence:this.navSequence,pagelets:Array.from(this.$6.values()),paintedPixels:this.$9,scrollY:d("VisualCompletionUtil").getScrollPosition(this).scrollY,scrolledVC:this.scrolledVC,speedIndex:0,startTime:this.startTime,tagSet:this.tagSet,totalPixels:this.$8,traceId:this.traceID,tracePolicy:this.tracePolicy,stateLog:this.stateLog,vcWithoutImage:0,viewport:this.$7,visuallyComplete:0};c("addAnnotations")(this.annotations,{"int":{mutationRootCount:this.mutationRoots.size,mutatedElementCount:this.mutatedElementCount,visualChangeRecordCount:this.visualChangeRecordList.length,paintedElementCount:this.$2.length}});this.$2.length&&(this.addMarkerPoint("FP",this.$2[this.$2.length-1].timestamp),this.$2.forEach(function(a){var c=d("WebAPIs").derefOnlyAttachedElement(a.element);if(!c)return;var e=a.pagelet;if(f.vcWithoutImage===0&&a.type==="component"){var g={height:Math.floor(a.rectangle.bottom-a.rectangle.top),mutationType:a.mutationType,tagName:d("VisualCompletionUtil").getTagName(c),type:a.type,width:Math.floor(a.rectangle.right-a.rectangle.left),x:Math.floor(a.rectangle.left),y:Math.floor(a.rectangle.top)};e&&(g.pagelet=e.name);f.vcWithoutImage=a.latency;f.markerPoints.set("vcWithoutImage",{data:g,element:new(d("WebAPIs").WeakRef)(c),timestamp:a.latency+b.startTime});f.markerPoints.set("lastMutationBeforeVC",{element:new(d("WebAPIs").WeakRef)(c),timestamp:a.mutationTimestamp})}if(f.visuallyComplete===0){g={height:Math.floor(a.rectangle.bottom-a.rectangle.top),mutationType:a.mutationType,tagName:d("VisualCompletionUtil").getTagName(c),type:a.type,width:Math.floor(a.rectangle.right-a.rectangle.left),x:Math.floor(a.rectangle.left),y:Math.floor(a.rectangle.top)};e&&(g.pagelet=e.name);f.visuallyComplete=a.latency;f.markerPoints.set("visuallyComplete",{data:g,element:new(d("WebAPIs").WeakRef)(c),timestamp:a.latency+b.startTime});f.scrollY=a.scrollY}f.speedIndex+=a.pixels/b.$9*a.latency;switch(a.type){case"component":f.compNum++;f.compPixels+=a.pixels;break;case"img":f.imgNum++;f.imgPixels+=a.pixels;break;case"bg":f.bgNum++;f.bgPixels+=a.pixels;break}}));this.logPagelets();this.logPixelProgress();a&&(f.visuallyComplete=f.vcWithoutImage=(h||(h=c("performanceNow")))()-this.startTime,f.markerPoints.set("visuallyComplete",{data:{},timestamp:f.visuallyComplete+this.startTime}),f.markerPoints.set("vcWithoutImage",{data:{},timestamp:f.vcWithoutImage+this.startTime}));a=h();f.vcCalculationOverhead=a-e;this.setupMetaData(f);this.logCssBgElementsMetaData();this.reported=!0;e=h()-a;c("addAnnotations")(f.annotations,{"double":{vcMetaDataLoggingLatency:e,vcTotalLoggingOverhead:e+f.cssBgScanOverhead+f.mutationProcessingOverhead+f.vcCalculationOverhead}});c("logVCUserTiming")(this);return f};b.cleanupPaintedElements=function(){var a=new Set(),b=[].concat(this.$2);while(b.length>0){var c=b.pop();if(a.has(c))continue;a.add(c);delete c.element;b.push.apply(b,c.children)}};b.tearDown=function(){this.$3.clear(),this.$1=[],this.visualChangeRecordList=[],this.$10.clear(),this.mutationRoots.clear(),this.$4.clear(),this.$5=[],this.config.retain_element_reference||(this.cleanupPaintedElements(),this.cssBgElements.forEach(function(a){delete a.element}),this.$6.forEach(function(a){delete a.element})),this.$6.clear(),this.markerPoints.forEach(function(a){delete a.element}),this.cssBgElements.clear()};b.findPagelet=function(a){a=a;while(a){if(this.$6.has(a))return this.$6.get(a);a=a.parentElement}return null};b.findParent=function(a){a=a.parentElement;while(a){if(this.$3.has(a))return this.$3.get(a);a=a.parentElement}return null};b.checkExcluded=function(a){a=a;while(a){if(this.$10.has(a))return!0;else if(this.$3.has(a)||this.mutationRoots.has(a))return!1;else if(d("VisualCompletionUtil").checkDOMElementAttributeValue(a,(i||(i=c("VisualCompletionConstants"))).IGNORE))return!0;a=a.parentElement}return!1};b.getBackgroundImages=function(){var a=this;this.cssBgElements.forEach(function(b){if(b.element){var c,e=d("WebAPIs").derefOnlyAttachedElement(b.element);if(!e)return;var f=a.findParent(e);if(!f)return;var g=d("VisualCompletionUtil").offsetTop(e,a),h=a.getRelativeBoundingClientRect(e,a.measureOriginalViewport?g:void 0);h=d("VisualCompletionUtil").getRectIntersection(h,f.rectangle);var i=d("VisualCompletionUtil").getPixels(h);if(i===0||a.measureOriginalViewport&&g>a.$7.height)return;((c=b.resourceTiming)==null?void 0:c.responseEnd)==null&&(b.resourceTiming=d("ResourceTimingAPI").getResourceTiming(b.url));if(((c=b.resourceTiming)==null?void 0:c.responseEnd)!=null){c=b.resourceTiming;b=c.responseEnd;var j=a.findPagelet(e);if(c.startTime>=a.startTime&&b>f.timestamp){c={children:[],element:new(d("WebAPIs").WeakRef)(e),hadLateMutationExpected:!1,hadLateMutationUnexpected:!1,latency:b-a.startTime,mutationSeq:f.mutationSeq,mutationType:"bg",mutationTimestamp:f.mutationTimestamp,offsetTop:g,pagelet:j,parent:f,pixels:i,rectangle:h,scrollY:f.scrollY,timestamp:b,type:"bg",veid:String(k++)};f.children.push(c);a.$3.set(e,c);a.$2.push(c)}}}})};b.getRelativeBoundingClientRect=function(a,b){var c=l();if(typeof a.getBoundingClientRect!=="function")return c;c=d("VisualCompletionUtilCommon").castToRect(a.getBoundingClientRect());return{bottom:b!=null?b+(c.bottom-c.top):c.bottom,left:c.left,right:c.right,top:b!=null?b:c.top}};b.findFirstVisibleChild=function(a){var b=this,c=[a];a=function(){var a=[],e=0,f=void 0;c.forEach(function(c){if(typeof c.getBoundingClientRect!=="function")return;var g=d("VisualCompletionUtilCommon").castToRect(c.getBoundingClientRect()),h=d("VisualCompletionUtil").getPixels(g);if(h>0&&(g.bottom<=0||g.right<=0||g.left>=b.$7.width||g.top>=b.$7.height))return;h>e&&!b.checkExcluded(c)&&(e=h,f=c);c.children!=null&&(a=a.concat(Array.from(c.children)))});if(e>0&&f)return{v:f};c=a};while(c&&c.length){var e=a();if(typeof e==="object")return e.v}return null};b.getRectangle=function(a,b){a=this.findFirstVisibleChild(a);if(a==null)return l();a=this.getRelativeBoundingClientRect(a);var c=a.bottom,e=a.left,f=a.right;a=a.top;c={bottom:c,left:e,right:f,top:a};return d("VisualCompletionUtil").getRectIntersection(c,b)};b.excludeElement=function(a){this.$10.add(a)};b.initTree=function(){var a=this,b={bottom:this.$7.height,left:0,right:this.$7.width,top:-this.initialScrollY};this.visualChangeRecordList=this.visualChangeRecordList.sort(function(a,b){return a.record.paintTime-b.record.paintTime});d("VisualCompletionUtil").processMediaVCImage(this);this.visualChangeRecordList.forEach(function(c){var e=c.element;c=c.record;e=d("WebAPIs").derefOnlyAttachedElement(e);if(e==null){c.detached=!0;c.finalState="detached";return}var f=e,g=d("VisualCompletionUtil").offsetTop(e,a);c.offsetTop=g;var h=a.getRelativeBoundingClientRect(e,a.measureOriginalViewport?g:void 0);c.rectAtTraceEnd=h;c.visibleAtMutation===!1&&(f=a.findFirstVisibleChild(e));if(f==null){c.finalState=h.bottom-h.top>0&&h.right-h.left>0?"offscreen":"invisible";return}f!==e&&(c.nonEmptyChildFound=!0,g=d("VisualCompletionUtil").offsetTop(f,a),c.offsetTop=g,h=a.getRelativeBoundingClientRect(f,a.measureOriginalViewport?g:void 0),c.rectAtTraceEnd=h);if(a.inAboveTheFold(f)){e=d("VisualCompletionUtil").getRectIntersection(h,b);c.paintTime-a.startTime>a.scrolledVC&&(a.scrolledVC=c.paintTime-a.startTime,a.$11=c.scrollY);if(a.measureOriginalViewport&&g>a.$7.height){c.finalState="offscreen";return}h=a.findPagelet(f);g={children:[],element:new(d("WebAPIs").WeakRef)(f),hadLateMutationExpected:!1,hadLateMutationUnexpected:!1,imgLoadStart:c.imgLoadStart!=null?c.imgLoadStart-a.startTime:void 0,latency:c.paintTime-a.startTime,mutationSeq:c.mutationSeq,mutationType:c.mutationType,mutationTimestamp:c.mutationTime,offsetTop:g,pagelet:h,parent:null,pixels:d("VisualCompletionUtil").getPixels(e),rectangle:e,scrollY:c.scrollY,timestamp:c.paintTime,type:c.mutationType==="imgLoad"||c.mutationType==="mutationImageAttribute"?"img":"component",veid:String(c.veid)};c.visibleAtTraceEnd=!0;c.finalState="visible";a.$3.set(f,g)}});this.$3.forEach(function(b){var c=d("WebAPIs").derefOnlyAttachedElement(b.element);if(c){c=a.findParent(c);c?(b.type!=="component"&&(b.scrollY=c.scrollY),c.children.push(b),b.parent=c):a.$1.push(b)}});this.$3.forEach(function(b,c){var d=b.parent;while(d){if(b.timestamp<d.timestamp){a.$3["delete"](c);return}d=d.parent}a.$2.push(b)})};b.logPixelProgress=function(){var a=this,b=new Map([["vc98",.02],["vc95",.05]]),c=0,d=0,e=function(e){var f=a.$2[e];c+=f.pixels;if(b.size===0)return"break";b.forEach(function(b,d){!a.markerPoints.has(d)&&c/a.$9>=b&&a.addMarkerPoint(d,f.latency+a.startTime)});f.type==="component"&&(d+=f.pixels,f.children.forEach(function(a){a.type!=="component"&&(d+=a.pixels)}),b.forEach(function(c,e){!a.markerPoints.has(e+"WithoutImage")&&d/a.$9>=c&&(a.addMarkerPoint(e+"WithoutImage",f.latency+a.startTime),b["delete"](e))}))};for(var f=0;f<this.$2.length;f++){var g=e(f);if(g==="break")break}};b.logPagelets=function(){var a=this;this.$6.forEach(function(b,c){if(!a.inViewport(c)){b.vcCallback!=null&&b.vcCallback(null,null,b.data);a.$6["delete"](c);return}c=a.$3.has(c)?a.$3.get(c):a.findParent(c);if(c){var d=c.latency+a.startTime;b.points.firstPaint=d;b.points.visuallyComplete=d;b.points.vcWithoutImage=d;b.mutationSeq=c.mutationSeq}});this.$2.forEach(function(b){var c=b.pagelet;while(c!=null){var e,f=b.latency+a.startTime;c.points.visuallyComplete=Math.max((e=c.points.visuallyComplete)!=null?e:0,f);if(b.type==="component"){c.points.vcWithoutImage=Math.max((e=c.points.vcWithoutImage)!=null?e:0,f)}e=d("WebAPIs").derefOnlyAttachedElement(c.element);c=(e==null?void 0:e.parentElement)!=null?a.findPagelet(e==null?void 0:e.parentElement):null}});this.$6.forEach(function(b){b.points.visuallyComplete!=null&&b.pageletTypeName!=null&&b.pageletTypeName!==""&&a.markerPoints.set(b.pageletTypeName+"VC",{timestamp:b.points.visuallyComplete}),b.points.vcWithoutImage!=null&&a.markerPoints.set("VC_"+b.name,{data:b.data,timestamp:b.points.vcWithoutImage}),b.vcCallback!=null&&b.points.visuallyComplete!=null&&b.points.vcWithoutImage!=null&&b.vcCallback(b.points.visuallyComplete,b.points.vcWithoutImage,b.data),a.config.retain_element_reference||delete b.element})};b.calculatePaintedPixels=function(){var a=this;this.$2=this.$2.sort(function(a,b){return b.latency-a.latency});this.$2.forEach(function(b){var c=b.pixels,e=b.parent;while(e)b.rectangle=d("VisualCompletionUtil").getRectIntersection(b.rectangle,e.rectangle),b.pixels=d("VisualCompletionUtil").getPixels(b.rectangle),c=Math.min(c,b.pixels,e.pixels),e=e.parent;b.pixels=c;e=b.parent;while(e)e.pixels-=c,e=e.parent;a.$9+=c});this.$8=this.$9};b.inAboveTheFold=function(a){var b=d("VisualCompletionUtil").getViewportSize(this);a=d("VisualCompletionUtil").getPixels(this.getRectangle(a,{bottom:b.height,left:0,right:b.width,top:-d("VisualCompletionUtil").getScrollPosition(this).scrollY}));return a>0};b.inOriginalViewport=function(a){return this.inAboveTheFold(a)&&d("VisualCompletionUtil").offsetTop(a,this)<=this.$7.height};b.inViewport=function(a){return this.measureOriginalViewport?this.inOriginalViewport(a):this.inAboveTheFold(a)};b.setInitialScrollY=function(a){this.initialScrollY=a,this.currentScrollY=a};b.setTracePolicy=function(a){this.tracePolicy=a};b.setupMetaData=function(a){var b=this,e=this.markerPoints.get(j);e&&e.timestamp>a.visuallyComplete+a.startTime&&(a.visuallyComplete=e.timestamp-a.startTime,this.markerPoints.set("visuallyComplete",e),c("addAnnotations")(a.annotations,{"int":{isVCOverriden:1}}));a.tracePolicy!=null&&a.tracePolicy!==""&&c("addAnnotations")(a.annotations,{string:{tracePolicy:a.tracePolicy}});c("addAnnotations")(a.annotations,{string:{interactionId:a.traceId},"int":{height:a.viewport.height,width:a.viewport.width,scrollY:a.scrollY}});d("VisualCompletionUtil").addPlatformMetaData(a,this);c("addAnnotations")(a.annotations,{"double":{vcCalculationOverhead:a.vcCalculationOverhead,mutationProcessingOverhead:a.mutationProcessingOverhead,cssBgScanOverhead:a.cssBgScanOverhead},"int":{finalScrollY:a.finalScrollY,initialScrollY:a.initialScrollY}});a.markerPoints.set("logVC",{timestamp:a.navComplete});a.speedIndex>0&&a.markerPoints.set("speedIndex",{timestamp:a.speedIndex+a.startTime});a.elements.length&&(this.measureOriginalViewport&&a.scrolledVC>0&&a.markerPoints.set("scrolledVC",{timestamp:a.scrolledVC+a.startTime}));e=d("VisibilityState").getHiddenSpans(a.startTime,a.visuallyComplete+a.startTime);e.length>0&&this.addMarkerPoint("backgrounded",e[0].start);c("addAnnotations")(a.annotations,{"int":{hidden:Number(d("VisibilityState").wasHidden(a.startTime,a.visuallyComplete+a.startTime))}});e=c("ResourceDownloadLogger")(a.startTime,a.startTime+a.visuallyComplete);e.forEach(function(b,d){var e;c("addAnnotations")(a.annotations,{"int":(e={},e[d]=b,e)})});d("WebAPIs").unavailableAPIs.forEach(function(a){b.addTag("unavailableAPIs",a)});if(this.$12){e=this.$12();c("addAnnotations")(a.annotations,{"double":{CLS:e}})}if(this.$13){e=this.$13();if(e!=null){var f;f=this.config.log_lcp_metadata===!0?{lcpClassname:(f=e.className)!=null?f:"",lcpElement:(f=e.element)!=null?f:"",lcpWasPreloaded:e.preloaded,lcpUrl:(f=e.url)!=null?f:"",lcpSize:(f=e.size)!=null?f:""}:void 0;this.addMarkerPoint("LCP",e.timestamp,f)}}};b.logCssBgElementsMetaData=function(){var a=0;this.cssBgElements.forEach(function(b){((b=b.resourceTiming)==null?void 0:b.responseEnd)!=null&&a++});c("addAnnotations")(this.annotations,{"int":{cssBgElementCount:this.cssBgElements.size(),cssBgTimingCount:a}})};return a}();g["default"]=a}),98);
__d("VisualCompletionTracing",["IteratableElementWeakMap","ResourceTimingAPI","VisibilityAPI","VisibilityState","VisualCompletionLogger","VisualCompletionTraceObserver","VisualCompletionUtil","VisualCompletionUtilCommon","WebAPIs","addAnnotations","currentVCTraces","foregroundRequestAnimationFrame","performanceNow","setTimeoutAcrossTransitions"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Set();a=!1;!a&&d("VisibilityAPI").canUseVisibilityAPI&&d("VisibilityState").subscribe(function(a,b){b&&d("currentVCTraces").getCurrentVCTraces().forEach(function(a){a.pendingMutations.forEach(function(b,c){a.addVisualElement(b.mutationSeq,c,b.mutationType,b.mutationTimestamp,b.mutationTimestamp,void 0,void 0,b.imgLoadStart,b.imgLoadEnd),a.intersectionObserver&&a.intersectionObserver.unobserve(c),a.unlock(b.lockId)}),a.pendingMutations.clear()})});b=function(a){babelHelpers.inheritsLoose(b,a);function b(b,e,f,g,j){var k;k=a.call(this,b,e,f,g,j)||this;k.pendingLocks=new Map();k.pendingImages=new(c("IteratableElementWeakMap"))();k.$VisualCompletionTracing$p_1=new(c("IteratableElementWeakMap"))();k.$VisualCompletionTracing$p_2=null;k.loadingStateObserver=null;k.$VisualCompletionTracing$p_3=[];k.pendingLoadingElements=new(c("IteratableElementWeakMap"))();k.visibleLoadingElements=new Set();k.$VisualCompletionTracing$p_4=0;k.$VisualCompletionTracing$p_5=0;k.$VisualCompletionTracing$p_6=[];k.$VisualCompletionTracing$p_7=[];k.mutationSeq=0;k.mutationObserver=null;k.intersectionObserver=null;k.pendingMutations=new Map();k.loggingScheduled=!1;k.$VisualCompletionTracing$p_8=new(c("IteratableElementWeakMap"))();k.intersectionObserverCallback=function(a){Array.prototype.forEach.call(a,function(a){var b=k.pendingMutations.get(a.target);b&&(k.addVisualElement(b.mutationSeq,a.target,b.mutationType,b.mutationTimestamp,a.time,d("WebAPIs").intersectionObserverEntryIsIntersecting(a),d("VisualCompletionUtilCommon").castToRect(a.boundingClientRect),b.imgLoadStart,b.imgLoadEnd),d("WebAPIs").intersectionObserverEntryIsIntersecting(a)&&k.addFirstMarkerPoint("firstPaint",a.time,{mutationType:b.mutationType}),k.cleanupPendingMutation(a.target))})};k.mutationRecordHandler=function(a){k.mutationSeq++;var b=(h||(h=c("performanceNow")))();a=d("VisualCompletionUtil").extractMutationElements(a,b,babelHelpers.assertThisInitialized(k));k.trackElements(a,k.mutationSeq,b);k.mutationProcessingOverhead+=h()-b};k.loadingStateObserverCallback=function(a){Array.from(a).forEach(function(a){d("WebAPIs").intersectionObserverEntryIsIntersecting(a)?(k.loadingElementAdded(a.target),k.logLoadingElementStart(a.target,a.boundingClientRect,a.time),k.visibleLoadingElements.add(a.target),k.addFirstMarkerPoint("loadingState_start",a.time),k.addFirstMarkerPoint("firstPaint",a.time,{mutationType:"loadingState"})):k.loadingElementRemoved(a.target)})};k.elementVisibilityCallback=function(a){Array.from(a).forEach(function(a){if(!d("WebAPIs").intersectionObserverEntryIsIntersecting(a))return;a=a.target;var b=k.$VisualCompletionTracing$p_8.get(a);if(b==null)return;b.callback();b.executeOnce&&k.$VisualCompletionTracing$p_8["delete"](a)})};d("WebAPIs").MutationObserver&&(k.mutationObserver=new(d("WebAPIs").MutationObserver)(k.mutationRecordHandler));d("WebAPIs").IntersectionObserver&&(k.loadingStateObserver=new(d("WebAPIs").IntersectionObserver)(k.loadingStateObserverCallback),k.intersectionObserver=new(d("WebAPIs").IntersectionObserver)(k.intersectionObserverCallback),k.$VisualCompletionTracing$p_2=new(d("WebAPIs").IntersectionObserver)(k.elementVisibilityCallback));d("currentVCTraces").addVCTrace(f,babelHelpers.assertThisInitialized(k));g==="INTERACTION"?k.measureOriginalViewport=!1:(d("currentVCTraces").setCurrentNavigationVCTrace(babelHelpers.assertThisInitialized(k)),k.measureOriginalViewport=!0,j.measureOriginalViewportOnNavigation!=null&&(k.measureOriginalViewport=j.measureOriginalViewportOnNavigation),i.forEach(function(a){k.observeMutation(a)}));d("VisualCompletionUtil").setupScrollHandler(babelHelpers.assertThisInitialized(k));return k}var e=b.prototype;e.addMutationRoot=function(a){if(this.reported||this.checkDuplicatedMutationRoot(a))return function(){};this.mutationSeq++;this.trackElements([[a,"mutationRoot"]],this.mutationSeq,(h||(h=c("performanceNow")))());return this.observeMutation(a)};e.dumpLocks=function(){return[this.pendingLocks,this.pendingLoadingElements,this.pendingImages]};e.lock=function(a){var b=this;this.pendingLocks.set(a,(h||(h=c("performanceNow")))());return function(){b.unlock(a)}};e.imageDone=function(a){var b=this.pendingImages.get(a);if(b==null)return;this.stateLog.set("imgLoad_"+this.$VisualCompletionTracing$p_4++,[b,(h||(h=c("performanceNow")))()]);this.pendingImages["delete"](a);this.attemptMeasurement()};e.imageWait=function(a){if(this.reported)return;this.pendingImages.set(a,(h||(h=c("performanceNow")))())};e.cleanupPendingMutation=function(a,b){var c=this.pendingMutations.get(a);c&&(this.intersectionObserver!=null&&this.intersectionObserver.unobserve(a),this.unlock(c.lockId,b),this.pendingMutations["delete"](a))};e.loadingElementRemoved=function(a){var b=this.pendingLoadingElements.get(a);b!=null&&(this.stateLog.set("loadingState_"+this.$VisualCompletionTracing$p_4++,[b,(h||(h=c("performanceNow")))()]),this.logLoadingElementEnd(a,h()),this.pendingLoadingElements["delete"](a),this.visibleLoadingElements.has(a)&&(this.visibleLoadingElements["delete"](a),this.addMarkerPoint("loadingState_end",(h||(h=c("performanceNow")))())),this.attemptMeasurement())};e.loadingElementAdded=function(a){if(this.reported)return;this.pendingLoadingElements.set(a,(h||(h=c("performanceNow")))())};e.waitLoadingState=function(a){var b=this;if(this.reported)return function(){};var c=this.$VisualCompletionTracing$p_1.get(a);if(c)return function(){c(),b.loadingElementRemoved(a)};if(d("VisualCompletionUtil").shouldIgnoreMutation(a,this))return function(){};if(this.config.bypass_detached_element&&a.isConnected===!1)return function(){};this.loadingStateObserver&&this.loadingStateObserver.observe(a);(d("VisibilityAPI").canUseVisibilityAPI&&!d("VisibilityAPI").isVisibilityHidden()||d("VisualCompletionUtil").isInAboveTheFold(a,this))&&this.loadingElementAdded(a);var e=function(){b.loadingStateObserver&&b.loadingStateObserver.unobserve(a),b.$VisualCompletionTracing$p_1["delete"](a)};this.$VisualCompletionTracing$p_1.set(a,e);return function(){e(),b.loadingElementRemoved(a)}};e.trackImage=function(a,b,c,e){d("VisualCompletionUtil").trackImage(a,b,c,e,this)};e.unlock=function(a,b){var d=this.pendingLocks.get(a);if(d==null)return;this.pendingLocks["delete"](a);b=b!=null?b+"_"+a:a;this.stateLog.set("Lock("+b+")",[d,(h||(h=c("performanceNow")))()]);this.attemptMeasurement()};e.checkDuplicatedMutationRoot=function(a){a=a;while(a!=null){if(this.mutationRoots.has(a))return!0;a=a.parentElement}return!1};e.observeMutation=function(a){var b=this;if(this.reported||this.checkDuplicatedMutationRoot(a))return function(){};d("VisualCompletionUtil").isElementNode(a)&&this.mutationObserver&&this.mutationObserver.observe(a,d("VisualCompletionUtil").mutationObserverConfig);this.mutationRoots.add(a);return function(){b.mutationRoots["delete"](a)}};e.registerNavigationMutationRoot=function(a){if(this.reported||this.checkDuplicatedMutationRoot(a))return function(){};i.add(a);return function(){i["delete"](a)}};e.scheduleIntersectionObserver=function(a,b,e,f,g,i){var j=this;if(this.reported)return;if(this.$VisualCompletionTracing$p_1.has(b))return;if(this.config.bypass_detached_element&&b.isConnected===!1)return;this.mutatedElementCount++;if(d("VisibilityAPI").canUseVisibilityAPI&&d("VisibilityAPI").isVisibilityHidden())this.addVisualElement(a,b,e,f,f,void 0,void 0,g,i);else if(d("WebAPIs").IntersectionObserver){this.cleanupPendingMutation(b);var k=e+"_paint_"+this.$VisualCompletionTracing$p_4++;this.intersectionObserver&&this.intersectionObserver.observe(b);this.lock(k);this.pendingMutations.set(b,{mutationTimestamp:f,lockId:k,mutationType:e,mutationSeq:a,imgLoadStart:g,imgLoadEnd:i});this.config.intersection_observer_timeout!=null&&c("setTimeoutAcrossTransitions")(function(){var d=j.pendingMutations.get(b);d!=null&&d.lockId===k&&(b.isConnected!==!1&&j.addVisualElement(a,b,"mutationTimeout",f,(h||(h=c("performanceNow")))(),void 0,void 0,g,i),j.addAnnotationInt("intersection_observer_timeout_count",++j.$VisualCompletionTracing$p_5),j.cleanupPendingMutation(b,"timeout"))},this.config.intersection_observer_timeout)}else{var l=e+"_paint_"+this.$VisualCompletionTracing$p_4++;this.lock(l);d("foregroundRequestAnimationFrame").foregroundRequestAnimationFrame(function(){j.addVisualElement(a,b,e,f,(h||(h=c("performanceNow")))(),void 0,void 0,g,i),j.unlock(l)})}};e.scheduleElementVisibleObserver=function(a,b,c){var e=c.executeOnce;e=e===void 0?!0:e;c=c.cleanAfterReport;c=c===void 0?!0:c;if(this.reported)return;if(d("WebAPIs").IntersectionObserver){var f=this.$VisualCompletionTracing$p_8.get(a);f&&(this.$VisualCompletionTracing$p_2&&this.$VisualCompletionTracing$p_2.unobserve(a));this.$VisualCompletionTracing$p_8.set(a,{executeOnce:e,callback:b,cleanAfterReport:c});this.$VisualCompletionTracing$p_2&&this.$VisualCompletionTracing$p_2.observe(a)}else return};e.trackElements=function(a,b,c){var e=this;if(this.reported)return;var f=[];a.forEach(function(a){var b=a[0];d("VisualCompletionUtil").isElementNode(b)&&!d("VisualCompletionUtil").isImageOrPicture(b)&&f.push(a)});f.forEach(function(a){var f=a[0];a=a[1];d("VisualCompletionUtil").checkLoadingStates(f,e);e.scheduleIntersectionObserver(b,f,a,c)});var g=d("currentVCTraces").getCurrentNavigationVCTrace();this.interactionType==="INTERACTION"&&g!=null&&!g.reported&&this.config.allow_inclusion_in_navigation_vc!==!0&&a.forEach(function(a){a=a[0];g==null?void 0:g.excludeElement(a)})};e.onBeforeComplete=function(a){if(this.reported)return;this.$VisualCompletionTracing$p_6.push(a)};e.onComplete=function(a){if(this.reported)return;this.$VisualCompletionTracing$p_7.push(a)};e.attemptMeasurement=function(a){var b=this;a===void 0&&(a=!1);if(!a&&(this.loggingScheduled||this.reported||this.pendingLocks.size>0||this.pendingLoadingElements.size()>0||this.pendingImages.size()>0))return;this.loggingScheduled=!0;var e=function(){b.mutationObserver&&b.mutationObserver.disconnect();b.$VisualCompletionTracing$p_1.forEach(function(a){a()});b.loadingStateObserver&&b.loadingStateObserver.disconnect();b.$VisualCompletionTracing$p_1.clear();b.$VisualCompletionTracing$p_3.forEach(function(a){a&&a.disconnect()});d("currentVCTraces").removeVCTrace(b.traceID);b.intersectionObserver&&b.intersectionObserver.disconnect();b.pendingMutations.clear();!a&&d("ResourceTimingAPI").canUseResourceTimingAPI()&&b.config.check_css_bg_elements&&d("VisualCompletionUtil").scanCssBgElements(b);var e=b.calculate(a);b.$VisualCompletionTracing$p_6.forEach(function(a){a(e,b.pendingLoadingElements)});b.$VisualCompletionTracing$p_6=[];c("VisualCompletionTraceObserver").emit(e);b.$VisualCompletionTracing$p_7.forEach(function(a){a(e)});b.$VisualCompletionTracing$p_7=[];b.$VisualCompletionTracing$p_2&&b.$VisualCompletionTracing$p_2.disconnect();b.$VisualCompletionTracing$p_8.clear();b.visibleLoadingElements.clear();b.tearDown()},f=this.config.defer_expensive_calculation&&!a?this.config.defer_expensive_calculation:function(a){return a()};f(e)};e.forceMeasurement=function(){var a=this;if(this.reported)return;this.pendingLocks.size>0&&(c("addAnnotations")(this.annotations,{"int":{incompleteLockCount:this.pendingLocks.size}}),this.pendingLocks.forEach(function(b,d){a.addTag("incompleteLocks",d),a.stateLog.set("incomplete_"+d,[b,(h||(h=c("performanceNow")))()])}));this.pendingLoadingElements.size()>0&&(c("addAnnotations")(this.annotations,{"int":{incompleteLoadingElementsCount:this.pendingLoadingElements.size()}}),this.pendingLoadingElements.forEach(function(b){a.stateLog.set("incomplete_loadingState_"+a.$VisualCompletionTracing$p_4++,[b,(h||(h=c("performanceNow")))()])}));this.pendingImages.size()>0&&(c("addAnnotations")(this.annotations,{"int":{incompleteImageCount:this.pendingImages.size()}}),this.pendingImages.forEach(function(b){a.stateLog.set("incomplete_imgLoad_"+a.$VisualCompletionTracing$p_4++,[b,(h||(h=c("performanceNow")))()])}));this.attemptMeasurement(!0)};e.tearDown=function(){a.prototype.tearDown.call(this);for(var b of this.$VisualCompletionTracing$p_8.entries()){var c=b[0],d=b[1];d.cleanAfterReport&&this.$VisualCompletionTracing$p_8["delete"](c)}this.pendingLocks.clear();this.pendingLoadingElements.clear();this.pendingImages.clear()};return b}(c("VisualCompletionLogger"));g["default"]=b}),98);
__d("vc-tracker",["VisibilityAPI","VisibilityState","VisualCompletionAttributes","VisualCompletionConstants","VisualCompletionTraceObserver","VisualCompletionTracing","VisualCompletionUtil","WebAPIs","currentVCTraces"],(function(a,b,c,d,e,f,g){"use strict";var h;a={VisibilityState:{canUseVisibilityAPI:d("VisibilityAPI").canUseVisibilityAPI,getHiddenSpans:d("VisibilityState").getHiddenSpans,subscribe:d("VisibilityState").subscribe,wasHidden:d("VisibilityState").wasHidden},VisualCompletionAttributes:c("VisualCompletionAttributes"),VisualCompletionConstants:h||(h=c("VisualCompletionConstants")),VisualCompletionTraceObserver:c("VisualCompletionTraceObserver"),VisualCompletionTracing:c("VisualCompletionTracing"),derefOnlyAttachedElement:d("WebAPIs").derefOnlyAttachedElement,findVCTraces:d("VisualCompletionUtil").findVCTraces,getCurrentNavigationVCTrace:d("currentVCTraces").getCurrentNavigationVCTrace,getCurrentVCTraces:d("currentVCTraces").getCurrentVCTraces,getLastNavigationVCReport:d("currentVCTraces").getLastNavigationVCReport,ignoreElement:d("VisualCompletionUtil").ignoreElement,isAttachedElement:d("WebAPIs").isAttachedElement,setCurrentNavigationVCTrace:d("currentVCTraces").setCurrentNavigationVCTrace,setLastNavigationVCReport:d("currentVCTraces").setLastNavigationVCReport,trackImageLoad:d("VisualCompletionUtil").trackImageLoad,trackLoadingState:d("VisualCompletionUtil").trackLoadingState,trimHash:d("VisualCompletionUtil").trimHash};g["default"]=a}),98);
__d("InteractionTracing",["Env","InteractionTracingConfigDefault","InteractionTracingMetrics","JSSelfProfilerTrackedInteractions","WebSession","cr:70","cr:955714","interaction-tracing","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a){var e,f=a.cfg,g=babelHelpers.objectWithoutPropertiesLoose(a,["cfg"]);e=(e=g.startTime)!=null?e:(h||(h=c("performanceNow")))();if((i||(i=c("Env"))).jssp_header_sent&&(i||(i=c("Env"))).jssp_targeting_enabled){var j=c("JSSelfProfilerTrackedInteractions").interactions;if(j){j=!!j.find(function(b){return(b.tracePolicy==="*"||b.tracePolicy===a.tracePolicy)&&(b.action==="*"||b.action===a.traceType)});if(j&&b("cr:955714")){(j=b("cr:70").WebLoom)==null?void 0:j.addProvider(b("cr:955714"))}}}return babelHelpers["extends"]({startTime:e},g,{cfg:babelHelpers["extends"]({},d("InteractionTracingConfigDefault").DEFAULT_TRACING_CONFIG,{},f),deps:b("cr:70")})}a=babelHelpers["extends"]({},c("interaction-tracing").InteractionTracingCore,{transformStartMetadata:j,startInteraction:function(a,b){return c("interaction-tracing").InteractionTracingCore.startInteraction(j(a),b)},trace:function(a){function b(b,c,d,e,f,g,h,i,j){return a.apply(this,arguments)}b.toString=function(){return a.toString()};return b}(function(a,e,f,g,j,k,l,m,n){l===void 0&&(l=(h||(h=c("performanceNow")))());n=c("interaction-tracing").InteractionTracingCore.trace(babelHelpers["extends"]({},d("InteractionTracingConfigDefault").DEFAULT_TRACING_CONFIG,{},n),b("cr:70"),a,e,f,g,j,k,l,m);a=d("WebSession").getSessionId();a!=null&&c("InteractionTracingMetrics").addMetadata(n,"websession_id",a);(i||(i=c("Env"))).brsid!=null&&c("InteractionTracingMetrics").addAnnotation(n,"brsid",""+(i||(i=c("Env"))).brsid);return n}),navigation:c("interaction-tracing").NavigationTracing,getTraceStatus:c("interaction-tracing").getTraceStatus});g["default"]=a}),98);
__d("IntlNumberType",["FbtNumberType"],(function(a,b,c,d,e,f,g){a=function(a){return c("FbtNumberType")};g.get=a}),98);
__d("IntlPhonologicalRewrites",["IntlPhonologicalRules"],(function(a,b,c,d,e,f){"use strict";a={get:function(a){return b("IntlPhonologicalRules")}};e.exports=a}),null);
__d("IntlRedundantStops",[],(function(a,b,c,d,e,f){a=Object.freeze({equivalencies:{".":["\u0964","\u104b","\u3002"],"\u2026":["\u0e2f","\u0eaf","\u1801"],"!":["\uff01"],"?":["\uff1f"]},redundancies:{"?":["?",".","!","\u2026"],"!":["!","?","."],".":[".","!"],"\u2026":["\u2026",".","!"]}});f["default"]=a}),66);
__d("IntlPunctuation",["FbtHooks","IntlPhonologicalRewrites","IntlRedundantStops"],(function(a,b,c,d,e,f,g){var h;d="[.!?\u3002\uff01\uff1f\u0964\u2026\u0eaf\u1801\u0e2f\uff0e]";var i={};function j(a){var b=a!=null?a:"",c=i[b];c==null&&(c=i[b]=k(a));return c}function k(a){var b=[];a=c("IntlPhonologicalRewrites").get(a);for(var d in a.patterns){var e=a.patterns[d];for(var f in a.meta){var g=new RegExp(f.slice(1,-1),"g"),h=a.meta[f];d=d.replace(g,h);e=e.replace(g,h)}e==="javascript"&&(e=function(a){return a.slice(1).toLowerCase()});b.push([new RegExp(d.slice(1,-1),"g"),e])}return b}function a(a){var b=j((h||(h=c("FbtHooks"))).getViewerContext().locale);a=a;for(var d=0;d<b.length;d++){var e=b[d],f=e[0];e=e[1];a=a.replace(f,e)}return a.replace(/\x01/g,"")}var l=new Map();for(e in c("IntlRedundantStops").equivalencies)for(f of[e].concat(c("IntlRedundantStops").equivalencies[e]))l.set(f,e);var m=new Map();for(f in c("IntlRedundantStops").redundancies)m.set(f,new Set(c("IntlRedundantStops").redundancies[f]));function n(a,b){a=l.get(a);b=l.get(b);return((a=m.get(a))==null?void 0:a.has(b))===!0}function b(a,b){return n(a[a.length-1],b)?"":b}g.PUNCT_CHAR_CLASS=d;g.applyPhonologicalRules=a;g.dedupeStops=b}),98);
__d("IntlVariations",[],(function(a,b,c,d,e,f){e.exports={BITMASK_NUMBER:28,BITMASK_GENDER:3,NUMBER_ZERO:16,NUMBER_ONE:4,NUMBER_TWO:8,NUMBER_FEW:20,NUMBER_MANY:12,NUMBER_OTHER:24,GENDER_MALE:1,GENDER_FEMALE:2,GENDER_UNKNOWN:3}}),null);
__d("IntlVariationResolverImpl",["invariant","FbtHooks","IntlNumberType","IntlVariations"],(function(a,b,c,d,e,f,g){var h,i="_1";a={EXACTLY_ONE:i,getNumberVariations:function(a){var c=b("IntlNumberType").get((h||(h=b("FbtHooks"))).getViewerContext().locale).getVariation(a);c&b("IntlVariations").BITMASK_NUMBER||g(0,11647,c,typeof c);return a===1?[i,c,"*"]:[c,"*"]},getGenderVariations:function(a){a&b("IntlVariations").BITMASK_GENDER||g(0,11648,a,typeof a);return[a,"*"]}};e.exports=a}),null);
__d("IntlVariationResolver",["IntlVariationHoldout","IntlVariationResolverImpl"],(function(a,b,c,d,e,f,g){a={getNumberVariations:function(a){return d("IntlVariationResolverImpl").getNumberVariations(a)},getGenderVariations:function(a){return d("IntlVariationHoldout").disable_variation?["*"]:d("IntlVariationResolverImpl").getGenderVariations(a)}};b=a;g["default"]=b}),98);
__d("LogHistory",[],(function(a,b,c,d,e,f){var g=500,h={},i=[];function j(a,b,c,d){var e=d[0];if(typeof e!=="string"||d.length!==1)return;i.push({date:Date.now(),level:a,category:b,event:c,args:e});i.length>g&&i.shift()}var k=function(){function a(a){this.category=a}var b=a.prototype;b.debug=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];j("debug",this.category,a,c);return this};b.log=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];j("log",this.category,a,c);return this};b.warn=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];j("warn",this.category,a,c);return this};b.error=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];j("error",this.category,a,c);return this};return a}();function a(a){h[a]||(h[a]=new k(a));return h[a]}function b(){return i}function c(){i.length=0}function d(a){return a.map(function(a){var b=new Date(a.date).toISOString();return[b,a.level,a.category,a.event,a.args].join(" | ")}).join("\n")}f.getInstance=a;f.getEntries=b;f.clearEntries=c;f.formatEntries=d}),66);
__d("NumberFormatConsts",["NumberFormatConfig"],(function(a,b,c,d,e,f){a={get:function(a){return b("NumberFormatConfig")}};e.exports=a}),null);
__d("QPLAddCometRequestHeaders",["QuickPerformanceLogger","cr:6895"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(){b("cr:6895").registerHeaderProvider(function(){var a={},b=(h||(h=c("QuickPerformanceLogger"))).getActiveMarkerIds({type:2});b.length>0&&(a["X-FB-QPL-Active-Flows"]=b.sort().join(","));b=h.getActiveE2ETraceIdsAsArray();b.length>0&&(a["X-FB-QPL-Active-E2E-Trace-IDs"]=b.sort().join(","));return a})}g["default"]=a}),98);
__d("QPLUserFlow",["ErrorMetadata","ErrorPubSub","QuickPerformanceLogger","cr:1752405","justknobx","performance"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function k(a,b){if(b===null)return a!=null?a:null;a=a!=null?a:{};a.string||(a.string={});a.string.uf_debug_info=b;return a}var l=1024;function m(a){a=String(a);return a.length>l?a.substring(0,l-3)+"...":a}a=function(){function a(){var a=this;b("cr:1752405")();(h||(h=c("ErrorPubSub"))).unshiftListener(function(b){if(b.type!=="fatal")return;var d=a.getActiveFlowIDs();if(d.length===0)return;var e=new(c("ErrorMetadata"))();e.clearEntries();c("justknobx")._("3809")&&d.forEach(function(d){(i||(i=c("QuickPerformanceLogger"))).forEachMarkerInstance(d,function(c,d){a.addPoint(d,"JAVASCRIPT_ERROR_THROWN",{data:{string:{logging_source:b.loggingSource,message_format:m(b.messageFormat),type:b.type},string_array:{message_params:b.messageParams.map(m)}},instanceKey:c})})});d.forEach(function(a){e.addEntry("QPL","ACTIVE_FLOW_ID",a.toString())});d=e.format();b.metadata?b.metadata=[].concat(b.metadata,d):b.metadata=d})}var d=a.prototype;d.start=function(a,b){b=b===void 0?{}:b;var d=b.instanceKey;d=d===void 0?0:d;var e=b.annotations,f=b.cancelExisting;f=f===void 0?!1:f;var g=b.cancelOnUnload;g=g===void 0?!0:g;var h=b.timestamp,j=b.trackedForLoss;j=j===void 0?!0:j;var k=b.joinOptions,l=b.timeoutInMs,m=b.onFlowTimeout,n=b.qplInternalDoNotUseAbsoluteTimeOrigin;b=b.enableE2ETracing;b=b===void 0?!1:b;k!=null?(i||(i=c("QuickPerformanceLogger"))).markerStartForJoin(a,k.joinId,{instanceKey:d,cancelExisting:f,cancelOnUnload:g,trackedForLoss:j,type:2,qplInternalDoNotUseAbsoluteTimeOrigin:n,monotonicTimestamp:h,absoluteTimeOriginMs:k.absoluteTimeOriginMs,sourceIsPrimary:k.sourceIsPrimary,closeSession:k.closeSession,unreliableSourceClockProcessId:k.unreliableSourceClockProcessId,timeoutMS:l,onMarkerTimeout__DoNotUse:m}):(i||(i=c("QuickPerformanceLogger"))).markerStart(a,d,h,{cancelExisting:f,cancelOnUnload:g,trackedForLoss:j,type:2,qplInternalDoNotUseAbsoluteTimeOrigin:n,timeoutMS:l,onMarkerTimeout__DoNotUse:m,enableE2ETracing:b});e&&(i||(i=c("QuickPerformanceLogger"))).markerAnnotate(a,e,{instanceKey:d})};d.addAlignmentPointForJoin=function(a,b,d){d=d===void 0?{}:d;var e=d.instanceKey,f=d.requestId;d=d.timestamp;(i||(i=c("QuickPerformanceLogger"))).addAlignmentPointForJoin(a,b,{instanceKey:e,requestId:f,timestamp:d})};d.startFromNavStart=function(a,b){b=b===void 0?{}:b;var d=b.instanceKey;d=d===void 0?0:d;var e=b.annotations,f=b.cancelExisting;f=f===void 0?!1:f;var g=b.cancelOnUnload;g=g===void 0?!0:g;var h=b.trackedForLoss;h=h===void 0?!0:h;var k=b.joinOptions,l=b.timeoutInMs,m=b.onFlowTimeout;b=b.qplInternalDoNotUseConvertToTimeOnServer;k!=null?(i||(i=c("QuickPerformanceLogger"))).markerStartForJoinFromNavStart(a,k.joinId,{instanceKey:d,cancelExisting:f,cancelOnUnload:g,trackedForLoss:h,type:2,qplInternalDoNotUseConvertToTimeOnServer:b,absoluteTimeOriginMs:k.absoluteTimeOriginMs,sourceIsPrimary:k.sourceIsPrimary,closeSession:k.closeSession,unreliableSourceClockProcessId:k.unreliableSourceClockProcessId,timeoutMS:l,onMarkerTimeout__DoNotUse:m}):(i||(i=c("QuickPerformanceLogger"))).markerStartFromNavStart(a,d,{cancelExisting:f,cancelOnUnload:g,trackedForLoss:h,type:2,qplInternalDoNotUseConvertToTimeOnServer:b,timeoutMS:l,onMarkerTimeout__DoNotUse:m});e&&(i||(i=c("QuickPerformanceLogger"))).markerAnnotate(a,e,{instanceKey:d});if((k==null?void 0:k.addAlignmentPoints)===!0){g=(f=k==null?void 0:k.requestId)!=null?f:"default_id";h=(j||(j=c("performance")))==null?void 0:(j||(j=c("performance"))).timing.requestStart;h!=null&&this.addAlignmentPointForJoin(a,0,{instanceKey:d,requestId:g,timestamp:h});b=(j||(j=c("performance")))==null?void 0:(j||(j=c("performance"))).timing.responseEnd;b!=null&&this.addAlignmentPointForJoin(a,3,{instanceKey:d,requestId:g,timestamp:b})}};d.endSuccess=function(a,b){b=b===void 0?{}:b;var c=b.instanceKey;c=c===void 0?0:c;var d=b.annotations,e=b.timestamp;b=b.partialCompleteForFurtherJoin;b=b===void 0?!1:b;this.$1(a,b===!0?12524:2,c,d,e)};d.endFailure=function(a,b,c){c=c===void 0?{}:c;var d=c.instanceKey;d=d===void 0?0:d;var e=c.debugInfo,f=c.annotations,g=c.timestamp;c=c.error;this.markError(a,b,{debugInfo:e,instanceKey:d,error:c});this.$1(a,3,d,f,g)};d.endValidationFailure_DO_NOT_USE=function(a,b){b=b===void 0?{}:b;var c=b.instanceKey;c=c===void 0?0:c;var d=b.debugInfo,e=b.annotations;b=b.timestamp;this.markError(a,"validation_failed",{debugInfo:d,instanceKey:c});this.$1(a,7952,c,e,b)};d.endTimeout=function(a,b){b=b===void 0?{}:b;var d=b.instanceKey;d=d===void 0?0:d;var e=b.annotations;b=b.timestamp;b=b===void 0?(i||(i=c("QuickPerformanceLogger"))).currentTimestamp():b;this.$1(a,113,d,e,b)};d.endCancel=function(a,b){b=b===void 0?{}:b;var c=b.instanceKey;c=c===void 0?0:c;var d=b.cancelReason;d=d===void 0?4:d;var e=b.annotations;b=b.timestamp;this.$1(a,d,c,e,b)};d.$1=function(a,b,d,e,f){f===void 0&&(f=(i||(i=c("QuickPerformanceLogger"))).currentTimestamp()),e&&(i||(i=c("QuickPerformanceLogger"))).markerAnnotate(a,e,{instanceKey:d}),(i||(i=c("QuickPerformanceLogger"))).markerEnd(a,b,d,f)};d.addAnnotations=function(a,b,d){d=d===void 0?{}:d;d=d.instanceKey;(i||(i=c("QuickPerformanceLogger"))).markerAnnotate(a,b,{instanceKey:d})};d.addPoint=function(a,b,d){d=d===void 0?{}:d;var e=d.instanceKey,f=d.debugInfo,g=d.data;d=d.timestamp;g=k(g,f!=null?f:null);(i||(i=c("QuickPerformanceLogger"))).markerPoint(a,b,{data:g,instanceKey:e,timestamp:d})};d.markError=function(a,b,d){d=d===void 0?{}:d;var e=d.debugInfo,f=d.instanceKey;d=d.error;(i||(i=c("QuickPerformanceLogger"))).markerAnnotate(a,babelHelpers["extends"]({},this.$2(d),{bool:(d={},d.uf_has_error=!0,d)}),{instanceKey:f});this.addPoint(a,b,{debugInfo:e,instanceKey:f})};d.storeBeforeNavigation=function(a,b){b=b===void 0?{}:b;b=b.instanceKey;b=b===void 0?0:b;(i||(i=c("QuickPerformanceLogger"))).markerStoreBeforeNavigation(a,{instanceKey:b})};d.getActiveFlowIDs=function(){return(i||(i=c("QuickPerformanceLogger"))).getActiveMarkerIds({type:2})};d.$2=function(a){var b;if(a==null)return{};var c={"int":{},string:{}};c.string.uf_error_name=a.name;a=a;(a==null?void 0:(b=a.source)==null?void 0:b.code)!=null&&(c["int"].uf_graphql_error_code=a==null?void 0:a.source.code);if((a==null?void 0:(b=a.source)==null?void 0:(b=b.exception)==null?void 0:b["class"])!=null){c.string.uf_graphql_exception_class=a==null?void 0:(b=a.source)==null?void 0:(a=b.exception)==null?void 0:a["class"]}return c};return a}();d=new a();g["default"]=d}),98);
__d("ReactEventHelpers",[],(function(a,b,c,d,e,f){b=typeof window!=="undefined"&&window.navigator!=null?/^Mac/.test(window.navigator.platform):!1;c=typeof window!=="undefined"&&window.PointerEvent!=null;function a(a,b){return b==null?!1:typeof a.containsNode==="function"?a.containsNode(b):a.contains(b)}f.isMac=b;f.hasPointerEvents=c;f.isRelatedTargetWithin=a}),66);
__d("ReactEventHookPropagation",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){a=a._stopEventHookPropagation;return a!==void 0&&a[b]}function b(a,b){var c=a._stopEventHookPropagation;c||(c=a._stopEventHookPropagation={});c[b]=!0}f.hasEventHookPropagationStopped=a;f.stopEventHookPropagation=b}),66);
__d("warning",["cr:755"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:755")}),98);
__d("ReactUseEvent.react",["ReactDOM","react","useUnsafeRef_DEPRECATED"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(i||d("react")).useLayoutEffect;function a(a,b){var e=(h||(h=c("useUnsafeRef_DEPRECATED")))(null),f=e.current;b&&(b.passive=void 0);if(f===null){var g=d("ReactDOM").unstable_createEventHandle(a,b),i=new Map();f={setListener:function(a,b){var c=i.get(a);c!==void 0&&c();if(b===null){i["delete"](a);return}c=g(a,b);i.set(a,c)},clear:function(){var a=Array.from(i.values());for(var b=0;b<a.length;b++)a[b]();i.clear()}};e.current=f}j(function(){return function(){f!==null&&f.clear(),e.current=null}},[f]);return f}g["default"]=a}),98);
__d("ReactFocusEvent.react",["ReactDOM","ReactEventHelpers","ReactEventHookPropagation","ReactUseEvent.react","react","react-compiler-runtime","useUnsafeRef_DEPRECATED"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k=j.useCallback,l=j.useEffect,m=j.useLayoutEffect;j.useMemo;var n=j.useRef,o=d("ReactEventHelpers").hasPointerEvents?["keydown","pointermove","pointerdown","pointerup"]:["keydown","mousedown","mousemove","mouseup","touchmove","touchstart","touchend"],p={passive:!0},q=!0,r=!1;function a(){return q}function s(){o.forEach(function(a){window.addEventListener(a,v,!0)})}function t(a){var b=a.metaKey,c=a.altKey;a=a.ctrlKey;return!(b||!d("ReactEventHelpers").isMac&&c||a)}function u(a){var b=a.key;a=a.target;if(b==="Tab"||b==="Escape")return!1;b=a.isContentEditable;a=a.tagName;return a==="INPUT"||a==="TEXTAREA"||b}function v(a){if(a.type==="keydown")t(a)&&(q=!0);else{a=a.target.nodeName;if(a==="HTML")return;q=!1}}function w(a,b,c){if(a.type==="keydown"){a=a.nativeEvent;t(a)&&!u(a)&&c(!0)}else b.isFocused?c(b.isFocusVisible):(q=!1,c(!1))}function x(a,b,c,d){a.forEach(function(a){a.setListener(b,function(a){return w(a,c,d)})})}function y(){var a=d("react-compiler-runtime").c(4),b=c("ReactUseEvent.react")("mousedown",p),e=c("ReactUseEvent.react")(d("ReactEventHelpers").hasPointerEvents?"pointerdown":"touchstart",p),f=c("ReactUseEvent.react")("keydown",p),g;a[0]!==f||a[1]!==b||a[2]!==e?(g=[b,e,f],a[0]=f,a[1]=b,a[2]=e,a[3]=g):g=a[3];f=g;return f}function z(){var a=d("react-compiler-runtime").c(1),b;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=[],a[0]=b):b=a[0];l(A,b)}function A(){r||(r=!0,s())}function b(a,b){var e=d("react-compiler-runtime").c(26),f=b.disabled,g=b.onBlur,h=b.onFocus,i=b.onFocusChange,j=b.onFocusVisibleChange;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(b={isFocused:!1,isFocusVisible:!1},e[0]=b):b=e[0];var k=n(b),o=c("ReactUseEvent.react")("focusin",p),r=c("ReactUseEvent.react")("focusout",p),s=y();e[1]!==r||e[2]!==f||e[3]!==o||e[4]!==a.current||e[5]!==s||e[6]!==g||e[7]!==h||e[8]!==i||e[9]!==j?(b=function(){var b=a.current,c=k.current;if(b!==null&&b.nodeType===1){x(s,b,c,function(a){c.isFocused&&c.isFocusVisible!==a&&(c.isFocusVisible=a,j&&j(a))});var e=function(a){c.isFocused&&(c.isFocused=!1,g&&g(a),i&&i(!1),c.isFocusVisible&&j&&j(!1),c.isFocusVisible=q)};o.setListener(b,function(a){if(f===!0)return;if(d("ReactEventHookPropagation").hasEventHookPropagationStopped(a,"useFocus"))return;d("ReactEventHookPropagation").stopEventHookPropagation(a,"useFocus");!c.isFocused&&b===a.target&&(c.isFocused=!0,c.isFocusVisible=q,h&&h(a),i&&i(!0),c.isFocusVisible&&j&&j(!0))});r.setListener(b,function(a){if(f===!0)return;if(d("ReactEventHookPropagation").hasEventHookPropagationStopped(a,"useFocus"))return;d("ReactEventHookPropagation").stopEventHookPropagation(a,"useFocus");e(a)})}},e[1]=r,e[2]=f,e[3]=o,e[4]=a.current,e[5]=s,e[6]=g,e[7]=h,e[8]=i,e[9]=j,e[10]=b):b=e[10];var t;e[11]!==r||e[12]!==f||e[13]!==o||e[14]!==a||e[15]!==s||e[16]!==g||e[17]!==h||e[18]!==i||e[19]!==j?(t=[r,f,o,a,s,g,h,i,j],e[11]=r,e[12]=f,e[13]=o,e[14]=a,e[15]=s,e[16]=g,e[17]=h,e[18]=i,e[19]=j,e[20]=t):t=e[20];m(b,t);e[21]!==a.current||e[22]!==g||e[23]!==i||e[24]!==j?(b=function(){var b=a.current,c=k.current;return function(){if(a.current===null&&c.isFocused){c.isFocused=!1;var d=new window.FocusEvent("blur");Object.defineProperty(d,"target",{value:b});g&&g(d);i&&i(!1);c.isFocusVisible&&j&&j(!1);c.isFocusVisible=q}}},e[21]=a.current,e[22]=g,e[23]=i,e[24]=j,e[25]=b):b=e[25];l(b);z()}function e(a,b){var e=b.disabled,f=b.onAfterBlurWithin,g=b.onBeforeBlurWithin,h=b.onBlurWithin,i=b.onFocusWithin,j=b.onFocusWithinChange,l=b.onFocusWithinVisibleChange,m=n({isFocused:!1,isFocusVisible:!1}),o=(b=c("ReactUseEvent.react"))("focusin",p),r=b("focusout",p),s=b("afterblur",p),t=b("beforeblur",p),u=y();b=k(function(b){typeof a==="function"?a(b):a.current=b;var c=m.current;b!==null&&c!==null&&(x(u,b,c,function(a){c.isFocused&&c.isFocusVisible!==a&&(c.isFocusVisible=a,l&&l(a))}),o.setListener(b,function(a){if(e===!0)return;if(d("ReactEventHookPropagation").hasEventHookPropagationStopped(a,"useFocusWithin"))return;c.isFocused||(c.isFocused=!0,c.isFocusVisible=q,j&&j(!0),c.isFocusVisible&&l&&l(!0));!c.isFocusVisible&&q&&(c.isFocusVisible=q,l&&l(!0));i&&i(a)}),r.setListener(b,function(a){if(e===!0)return;var f=a.nativeEvent.relatedTarget;if(d("ReactEventHookPropagation").hasEventHookPropagationStopped(a,"useFocusWithin"))return;c.isFocused&&!d("ReactEventHelpers").isRelatedTargetWithin(b,f)?(c.isFocused=!1,j&&j(!1),c.isFocusVisible&&l&&l(!1),h&&h(a)):d("ReactEventHookPropagation").stopEventHookPropagation(a,"useFocusWithin")}),t.setListener(b,function(a){if(e===!0)return;g&&(g(a),s.setListener(document,function(a){f&&f(a),s.setListener(document,null)}))}))},[s,t,r,e,o,u,a,f,g,h,i,j,l]);z();return b}function B(){var a=d("react-compiler-runtime").c(4),b=C("mousedown",p),c=C(d("ReactEventHelpers").hasPointerEvents?"pointerdown":"touchstart",p),e=C("keydown",p),f;a[0]!==e||a[1]!==b||a[2]!==c?(f=[b,c,e],a[0]=e,a[1]=b,a[2]=c,a[3]=f):f=a[3];e=f;return e}function f(a){var b=d("react-compiler-runtime").c(15),c=a.disabled,e=a.onAfterBlurWithin,f=a.onBeforeBlurWithin,g=a.onBlurWithin,h=a.onFocusWithin,i=a.onFocusWithinChange,j=a.onFocusWithinVisibleChange,k=a.initializer;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(a={isFocused:!1,isFocusVisible:!1},b[0]=a):a=b[0];var l=n(a),m=C("focusin",p),o=C("focusout",p),r=C("afterblur",p),s=C("beforeblur",p),t=B();b[1]!==r||b[2]!==s||b[3]!==o||b[4]!==c||b[5]!==m||b[6]!==t||b[7]!==k||b[8]!==e||b[9]!==f||b[10]!==g||b[11]!==h||b[12]!==i||b[13]!==j?(a=function(a){var b=l.current;a!==null&&b!==null?(k&&k(a),x(t,a,b,function(a){b.isFocused&&b.isFocusVisible!==a&&(b.isFocusVisible=a,j&&j(a))}),m.setListener(a,function(a){if(c===!0)return;if(d("ReactEventHookPropagation").hasEventHookPropagationStopped(a,"useFocusWithin"))return;b.isFocused||(b.isFocused=!0,b.isFocusVisible=q,i&&i(!0),b.isFocusVisible&&j&&j(!0));!b.isFocusVisible&&q&&(b.isFocusVisible=q,j&&j(!0));h&&h(a)}),o.setListener(a,function(e){if(c===!0)return;var f=e.nativeEvent.relatedTarget;if(d("ReactEventHookPropagation").hasEventHookPropagationStopped(e,"useFocusWithin"))return;b.isFocused&&!d("ReactEventHelpers").isRelatedTargetWithin(a,f)?(b.isFocused=!1,i&&i(!1),b.isFocusVisible&&j&&j(!1),g&&g(e)):d("ReactEventHookPropagation").stopEventHookPropagation(e,"useFocusWithin")}),s.setListener(a,function(a){if(c===!0)return;f&&(f(a),r.setListener(document,function(a){e&&e(a),r.setListener(document,null)}))})):a===null&&(m.clear(),o.clear(),s.clear())},b[1]=r,b[2]=s,b[3]=o,b[4]=c,b[5]=m,b[6]=t,b[7]=k,b[8]=e,b[9]=f,b[10]=g,b[11]=h,b[12]=i,b[13]=j,b[14]=a):a=b[14];b=a;z();return b}function C(a,b){var e=(h||(h=c("useUnsafeRef_DEPRECATED")))(null),f=e.current;b&&(b.passive=void 0);if(f===null){var g=d("ReactDOM").unstable_createEventHandle(a,b),i=new Map();f={setListener:function(a,b){var c=i.get(a);c!==void 0&&c();if(b===null){i["delete"](a);return}c=g(a,function(){b.apply(void 0,arguments)});i.set(a,c)},clear:function(){var a=Array.from(i.values());for(var b=0;b<a.length;b++)a[b]();i.clear()}};e.current=f}return f}g.getGlobalFocusVisible=a;g.useFocus=b;g.useFocusWithin_DEPRECATED=e;g.useFocusWithin=f}),98);
__d("VideoPlayerVideoPixelsFitContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=(h||d("react")).createContext;b=a(null);c=b;g["default"]=c}),98);
__d("XPlatReactEnvironment",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("26340")}function b(){return c("gkx")("22979")}function d(){return c("gkx")("26341")}function e(){return c("gkx")("4008")}function f(){return c("gkx")("26342")}function h(){return c("gkx")("11826")}function i(){return!0}g.isFRLEnvironment=a;g.isInstagramEnvironment=b;g.isMWAEnvironment=d;g.isFBIGNativeEnvironment=e;g.isImagineEnvironment=f;g.isThreadsEnvironment=h;g.isWeb=i}),98);
__d("areEqual",[],(function(a,b,c,d,e,f){var g=[],h=[];function a(a,b){var c=g.length?g.pop():[],d=h.length?h.pop():[];a=i(a,b,c,d);c.length=0;d.length=0;g.push(c);h.push(d);return a}function i(a,b,c,d){if(a===b)return a!==0||1/a==1/b;if(a==null||b==null)return!1;if(typeof a!=="object"||typeof b!=="object")return!1;var e=Object.prototype.toString,f=e.call(a);if(f!=e.call(b))return!1;switch(f){case"[object String]":return a==String(b);case"[object Number]":return isNaN(a)||isNaN(b)?!1:a==Number(b);case"[object Date]":case"[object Boolean]":return+a==+b;case"[object RegExp]":return a.source==b.source&&a.global==b.global&&a.multiline==b.multiline&&a.ignoreCase==b.ignoreCase}e=c.length;while(e--)if(c[e]==a)return d[e]==b;c.push(a);d.push(b);try{e=0;if(f==="[object Array]"){e=a.length;if(e!==b.length)return!1;while(e--)if(!i(a[e],b[e],c,d))return!1}else if(a instanceof Set){if(a.size!==b.size)return!1;f=Array.from(b.values());for(e of a){var g=!1;for(var h=0;h<f.length;h++){var j=f[h];if(i(e,j,c,d)){g=!0;f.splice(h,1);break}}if(g===!1)return!1}return!0}else if(a instanceof Map){if(a.size!==b.size)return!1;j=Array.from(b);for(h of a){g=!1;for(e=0;e<j.length;e++){f=j[e];if(i(h,f,c,d)){g=!0;j.splice(e,1);break}}if(g===!1)return!1}return!0}else{if(a.constructor!==b.constructor)return!1;if(Object.prototype.hasOwnProperty.call(a,"valueOf")&&Object.prototype.hasOwnProperty.call(b,"valueOf"))return a.valueOf()==b.valueOf();f=Object.keys(a);if(f.length!=Object.keys(b).length)return!1;for(e=0;e<f.length;e++){if(f[e]==="_owner")continue;if(!Object.prototype.hasOwnProperty.call(b,f[e])||!i(a[f[e]],b[f[e]],c,d))return!1}}return!0}finally{c.pop(),d.pop()}}f["default"]=a}),66);
__d("cometAsyncRequestHeaders",[],(function(a,b,c,d,e,f){"use strict";var g=[];function a(){return g.reduce(function(a,b){b=b();return Object.assign(b,a)},{})}function b(a){g.push(a)}f.getHeaders=a;f.registerHeaderProvider=b}),66);
__d("errorCode",[],(function(a,b,c,d,e,f){"use strict";function a(a){throw new Error('errorCode("'+a+'"): This should not happen. Oh noes!')}f["default"]=a}),66);
__d("escapeRegex",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a.replace(/([.?*+\^$\[\]\\(){}|\-])/g,"\\$1")}e.exports=a}),null);
__d("intlNumUtils",["FbtHooks","NumberFormatConsts","escapeRegex"],(function(a,b,c,d,e,f){var g,h=3;f=["\u0433\u0440\u043d.","\u0434\u0435\u043d.","\u043b\u0432.","\u043c\u0430\u043d.","\u0564\u0580.","\u062c.\u0645.","\u062f.\u0625.","\u062f.\u0627.","\u062f.\u0628.","\u062f.\u062a.","\u062f.\u062c.","\u062f.\u0639.","\u062f.\u0643.","\u062f.\u0644.","\u062f.\u0645.","\u0631.\u0633.","\u0631.\u0639.","\u0631.\u0642.","\u0631.\u064a.","\u0644.\u0633.","\u0644.\u0644.","\u0783.","B/.","Bs.","Fr.","kr.","L.","p.","S/."];var i={};function j(a){i[a]||(i[a]=new RegExp(a,"i"));return i[a]}var k=j(f.reduce(function(a,c,d){return a+(d?"|":"")+"("+b("escapeRegex")(c)+")"},""));function l(a,c,d,e,f,g,i){d===void 0&&(d="");e===void 0&&(e=".");f===void 0&&(f=0);g===void 0&&(g={primaryGroupSize:h,secondaryGroupSize:h});var k=g.primaryGroupSize||h;g=g.secondaryGroupSize||k;i=i&&i.digits;var l;c==null?l=a.toString():typeof a==="string"?l=r(a,c):l=p(a,c);a=l.split(".");c=a[0];l=a[1];if(Math.abs(parseInt(c,10)).toString().length>=f){a="$1"+d+"$2$3";f="(\\d)(\\d{"+(k-0)+"})($|\\D)";k=c.replace(j(f),a);if(k!=c){c=k;f="(\\d)(\\d{"+(g-0)+"})("+b("escapeRegex")(d)+")";g=j(f);while((k=c.replace(g,a))!=c)c=k}}i!=null&&(c=m(c,i),l=l&&m(l,i));d=c;l&&(d+=e+l);return d}function m(a,b){var c="";for(var d=0;d<a.length;++d){var e=b[a.charCodeAt(d)-48];c+=e!==void 0?e:a[d]}return c}function a(a,c){var d=b("NumberFormatConsts").get((g||(g=b("FbtHooks"))).getViewerContext().locale);return l(a,c,"",d.decimalSeparator,d.minDigitsForThousandsSeparator,d.standardDecimalPatternInfo,d.numberingSystemData)}function n(a,c){var d=b("NumberFormatConsts").get((g||(g=b("FbtHooks"))).getViewerContext().locale);return l(a,c,d.numberDelimiter,d.decimalSeparator,d.minDigitsForThousandsSeparator,d.standardDecimalPatternInfo,d.numberingSystemData)}function o(a){return a&&Math.floor(Math.log10(Math.abs(a)))}function c(a,b,c){var d=o(a),e=a;d<c&&(e=a*Math.pow(10,-d+c));a=Math.pow(10,o(e)-c+1);e=Math.round(e/a)*a;if(d<c){e/=Math.pow(10,-d+c);if(b==null)return n(e,c-d-1)}return n(e,b)}function p(a,b){b=b==null?0:b;var c=Math.pow(10,b);a=(Math.round(a*c)/c).toString();if(!b)return a;if(a.indexOf("e-")!==-1)return a;c=a.indexOf(".");var d;c===-1?(a+=".",d=b):d=b-(a.length-c-1);for(b=0,c=d;b<c;b++)a+="0";return a}var q=function(a,b){a=a;for(var c=0;c<b;c++)a+="0";return a};function r(a,b){var c=a.indexOf("."),d=c===-1?a:a.slice(0,c);a=c===-1?"":a.slice(c+1);return b!=null?d+"."+q(a.slice(0,b),b-a.length):d}function s(a,c,d){d===void 0&&(d="");var e=u(),f=a;e&&(f=a.split("").map(function(a){return e[a]||a}).join("").trim());f=f.replace(/^[^\d]*\-/,"\x02");f=f.replace(k,"");a=b("escapeRegex")(c);c=b("escapeRegex")(d);d=j("^[^\\d]*\\d.*"+a+".*\\d[^\\d]*$");if(!d.test(f)){d=j("(^[^\\d]*)"+a+"(\\d*[^\\d]*$)");if(d.test(f)){f=f.replace(d,"$1\x01$2");return t(f)}d=j("^[^\\d]*[\\d "+b("escapeRegex")(c)+"]*[^\\d]*$");d.test(f)||(f="");return t(f)}d=j("(^[^\\d]*[\\d "+c+"]*)"+a+"(\\d*[^\\d]*$)");f=d.test(f)?f.replace(d,"$1\x01$2"):"";return t(f)}function t(a){a=a.replace(/[^0-9\u0001\u0002]/g,"").replace("\x01",".").replace("\x02","-");var b=Number(a);return a===""||isNaN(b)?null:b}function u(){var a=b("NumberFormatConsts").get((g||(g=b("FbtHooks"))).getViewerContext().locale),c={};a=a.numberingSystemData&&a.numberingSystemData.digits;if(a==null)return null;for(var d=0;d<a.length;d++)c[a.charAt(d)]=d.toString();return c}function d(a){var c=b("NumberFormatConsts").get((g||(g=b("FbtHooks"))).getViewerContext().locale);return s(a,c.decimalSeparator||".",c.numberDelimiter)}var v={formatNumber:a,formatNumberRaw:l,formatNumberWithThousandDelimiters:n,formatNumberWithLimitedSigFig:c,parseNumber:d,parseNumberRaw:s,truncateLongNumber:r,getFloatString:function(a,b,c){a=String(a);a=a.split(".");b=v.getIntegerString(a[0],b);return a.length===1?b:b+c+a[1]},getIntegerString:function(a,b){b=b;b===""&&(b=",");a=String(a);var c=/(\d+)(\d{3})/;while(c.test(a))a=a.replace(c,"$1"+b+"$2");return a}};e.exports=v}),null);
__d("substituteTokens",["invariant","IntlPunctuation"],(function(a,b,c,d,e,f,g,h){var i=Object.prototype.hasOwnProperty,j=new RegExp("\\{([^}]+)\\}("+d("IntlPunctuation").PUNCT_CHAR_CLASS+"*)","g");function k(a){return a}function a(a,b,c){if(b==null)return a;typeof b==="object"||h(0,6041,a);var e=[],f=[];a=a.replace(j,function(a,g,h){i.call(b,g)||(c==null?void 0:c.onMissingParameterError==null?void 0:c.onMissingParameterError(Object.keys(b),g));a=b[g];if(a!=null&&typeof a==="object"){e.push(a);f.push(g);return"\x17"+h}else if(a==null)return"";return String(a)+d("IntlPunctuation").dedupeStops(String(a),h)}).split("\x17").map(d("IntlPunctuation").applyPhonologicalRules);if(a.length===1)return a[0];var g=a[0]!==""?[a[0]]:[];for(var l=0;l<e.length;l++)g.push(k(e[l])),a[l+1]!==""&&g.push(a[l+1]);return g}f.exports=a}),34);
__d("fbt",["invariant","FbtEnv","FbtHooks","FbtQTOverrides","FbtResultBase","FbtTable","FbtTableAccessor","GenderConst","IntlVariationResolver","intlNumUtils","substituteTokens"],(function(a,b,c,d,e,f,g){var h;b("FbtEnv").setupOnce();var i=b("FbtQTOverrides").overrides,j=b("IntlVariationResolver").getGenderVariations,k=b("IntlVariationResolver").getNumberVariations,l=Object.prototype.hasOwnProperty,m=!1,n=b("FbtTable").ARG,o={number:0,gender:1},p={object:0,possessive:1,reflexive:2,subject:3},q={};function a(a,c,d){if(((d==null?void 0:d.hk)||(d==null?void 0:d.ehk))&&m)return{text:a,fbt:!0,hashKey:d.hk};c=(h||(h=b("FbtHooks"))).getTranslatedInput({table:a,args:c,options:d});var e=c.args;c=c.table;var f={};if(c.__vcg!=null){e=e||[];var k=(h||(h=b("FbtHooks"))).getViewerContext();k=k.GENDER;var l=j(k);e.unshift(b("FbtTableAccessor").getGenderResult(l,null,k))}l=[];e&&(typeof c!=="string"&&(c=b("FbtTable").access(c,e,0,l)),f=r(e),c!==null||g(0,479));var n;if(Array.isArray(c)){k=c[0];n=c[1];e="1_"+n;i[e]!=null&&i[e]!==""&&(k=i[e],(h||(h=b("FbtHooks"))).onTranslationOverride(n));e={inputTable:a,tokens:l};(h||(h=b("FbtHooks"))).logImpression(n,e)}else if(typeof c==="string")k=c;else throw new Error("Table access did not result in string: "+(c===void 0?"undefined":JSON.stringify(c))+", Type: "+typeof c);a=this.cachedResults[k];l=s(f);if(a&&!l)return a;else{e=b("substituteTokens")(k,f,(h||(h=b("FbtHooks"))).getErrorListener==null?void 0:(h||(h=b("FbtHooks"))).getErrorListener({translation:k,hash:n}));c=this._wrapContent(e,k,n,d==null?void 0:d.eo);l||(this.cachedResults[k]=c);return c}}function r(a){var b={};a.forEach(function(a){a=a[n.SUBSTITUTION];if(!a)return;for(var c in a)l.call(a,c)&&(b[c]==null||g(0,56656,c),b[c]=a[c])});return b}function s(a){for(a in a)return!0;return!1}function c(a,c){return b("FbtTableAccessor").getEnumResult(a)}function d(a){return b("FbtTableAccessor").getGenderResult(j(a),null,a)}function f(a,c,d){var e;e=(e={},e[a]=c,e);if(d)if(d[0]===o.number){var f=d.length>1?d[1]:c;typeof f==="number"||g(0,484);var h=k(f);typeof c==="number"&&(e[a]=b("intlNumUtils").formatNumberWithThousandDelimiters(c));return b("FbtTableAccessor").getNumberResult(h,e,f)}else if(d[0]===o.gender){a=d[1];a!=null||g(0,485);return b("FbtTableAccessor").getGenderResult(j(a),e,a)}else g(0,486);else return b("FbtTableAccessor").getSubstitution(e)}function t(a,b,c){return this._param(a,b,c)}function u(a,c,d){var e=k(a),f={};c&&(typeof d==="number"?f[c]=b("intlNumUtils").formatNumberWithThousandDelimiters(d):f[c]=d||b("intlNumUtils").formatNumberWithThousandDelimiters(a));return b("FbtTableAccessor").getNumberResult(e,f,a)}function v(a,c,d){c!==b("GenderConst").NOT_A_PERSON||!d||!d.human||g(0,11835);d=w(a,c);return b("FbtTableAccessor").getPronounResult(d)}function w(a,c){switch(c){case b("GenderConst").NOT_A_PERSON:return a===p.object||a===p.reflexive?b("GenderConst").NOT_A_PERSON:b("GenderConst").UNKNOWN_PLURAL;case b("GenderConst").FEMALE_SINGULAR:case b("GenderConst").FEMALE_SINGULAR_GUESS:return b("GenderConst").FEMALE_SINGULAR;case b("GenderConst").MALE_SINGULAR:case b("GenderConst").MALE_SINGULAR_GUESS:return b("GenderConst").MALE_SINGULAR;case b("GenderConst").MIXED_UNKNOWN:case b("GenderConst").FEMALE_PLURAL:case b("GenderConst").MALE_PLURAL:case b("GenderConst").NEUTER_PLURAL:case b("GenderConst").UNKNOWN_PLURAL:return b("GenderConst").UNKNOWN_PLURAL;case b("GenderConst").NEUTER_SINGULAR:case b("GenderConst").UNKNOWN_SINGULAR:return a===p.reflexive?b("GenderConst").NOT_A_PERSON:b("GenderConst").UNKNOWN_PLURAL}return b("GenderConst").NOT_A_PERSON}function x(a,c,d){var e=j(d),f={};f[a]=c;return b("FbtTableAccessor").getGenderResult(e,f,d)}function y(a,c,d,e){a=typeof a==="string"?[a]:a;var f=(h||(h=b("FbtHooks"))).getErrorListener({translation:c,hash:d});a=h.getFbtResult({contents:a,errorListener:f,extraOptions:e,patternHash:d,patternString:c});return a}function z(){m=!0}function A(){m=!1}function B(a){return a instanceof b("FbtResultBase")}var C=function(){};C._=a;C._enum=c;C._implicitParam=t;C._name=x;C._param=f;C._plural=u;C._pronoun=v;C._subject=d;C._wrapContent=y;C.disableJsonExportMode=A;C.enableJsonExportMode=z;C.isFbtInstance=B;C.cachedResults=q;C._getCachedFbt=void 0;a=C;e.exports=a}),null);
__d("filterNulls",[],(function(a,b,c,d,e,f){"use strict";function a(a){var b=[];for(a of a)a!=null&&b.push(a);return b}f["default"]=a}),66);
__d("flipObject",[],(function(a,b,c,d,e,f){"use strict";function a(a){return Object.entries(a).reduce(function(b,c){var d=c[0];c=c[1];Object.prototype.hasOwnProperty.call(a,d)&&typeof c!=="object"&&typeof c!=="function"&&c!=null&&(b[String(c)]=d);return b},{})}f["default"]=a}),66);
__d("hashString",[],(function(a,b,c,d,e,f){"use strict";function a(a){var b=0;for(var c=0;c<a.length;c++){var d=a.charCodeAt(c);b=(b<<5)-b+d;b|=0}return b}f["default"]=a}),66);
__d("isHorizonDotMetaDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)horizon\\.meta\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("routeBuilderUtils",[],(function(a,b,c,d,e,f){"use strict";function a(a){a=a.split("/");return a.filter(function(a){return a!==""}).map(function(a){var b=a.split(/{|}/);if(b.length<3)return{isToken:!1,part:a};else{a=b[0];var c=b[1];b=b[2];var d=c[0]==="?",e=c[d?1:0]==="*";c=c.substring((d?1:0)+(e?1:0));return{isToken:!0,optional:d,catchAll:e,prefix:a,suffix:b,token:c}}})}f.getPathParts=a}),66);
__d("jsRouteBuilder",["ConstUriUtils","FBLogger","routeBuilderUtils"],(function(a,b,c,d,e,f,g){"use strict";var h="#";function a(a,b,e,f,g){g===void 0&&(g=!1);var i=d("routeBuilderUtils").getPathParts(a);function j(j){try{var k=f!=null?babelHelpers["extends"]({},f,{},j):j!=null?j:{},l={};j="";var m=!1;j=i.reduce(function(a,c){if(!c.isToken)return a+"/"+c.part;else{var d,e=c.optional,f=c.prefix,g=c.suffix;c=c.token;if(e&&m)return a;d=(d=k[c])!=null?d:b[c];if(d==null&&e){m=!0;return a}if(d==null)throw new Error("Missing required template parameter: "+c);if(d==="")throw new Error("Required template parameter is an empty string: "+c);l[c]=!0;return a+"/"+f+d+g}},"");a.slice(-1)==="/"&&(j+="/");j===""&&(j="/");var n=d("ConstUriUtils").getUri(j);for(var o in k){var p=k[o];!l[o]&&p!=null&&n!=null&&(e!=null&&e.has(o)?p!==!1&&(n=n.addQueryParam(o,null)):n=n.addQueryParam(o,p))}return[n,j]}catch(b){p=b==null?void 0:b.message;o=c("FBLogger")("JSRouteBuilder").blameToPreviousFrame().blameToPreviousFrame();g&&(o=o.blameToPreviousFrame());o.mustfix("Failed building URI for base path: %s message: %s",a,p);return[null,h]}}return{buildUri:function(a){a=(a=j(a)[0])!=null?a:d("ConstUriUtils").getUri(h);if(a==null)throw new Error("Not even the fallback URL parsed validly!");return a},buildUriNullable:function(a){return j(a)[0]},buildURL:function(a){a=j(a);var b=a[0];a=a[1];return(b=b==null?void 0:b.toString())!=null?b:a},buildURLStringDEPRECATED:function(a){a=j(a);var b=a[0];a=a[1];return(b=b==null?void 0:b.toString())!=null?b:a},getPath:function(){return a}}}g["default"]=a}),98);
__d("padNumber",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){a=a.toString();return a.length>=b?a:"0".repeat(b-a.length)+a}f["default"]=a}),66);
__d("passiveEventListenerUtil",[],(function(a,b,c,d,e,f){"use strict";b=!1;try{c=Object.defineProperty({},"passive",{get:function(){b=!0}});window.addEventListener("test",null,c)}catch(a){}var g=b;function a(a){return g?a:typeof a==="boolean"?a:a.capture||!1}f.isPassiveEventListenerSupported=g;f.makeEventOptions=a}),66);
__d("qpl",["QPLHasteSupportDataStorage","recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h={};a={_:function(a,b){var d=h[b];if(d==null){var e=c("QPLHasteSupportDataStorage").get(b);e==null?(c("recoverableViolation")("Failed to find a Haste-supplied config for the QPL event "+("identified by token `"+b+"`."),"staticresources"),d={i:a}):d=babelHelpers["extends"]({i:a},e);h[b]=d}return d}};b=a;g["default"]=b}),98);
__d("relay-runtime/util/withProvidedVariables",["areEqual","warning"],(function(a,b,c,d,e,f){"use strict";var g;b=typeof WeakMap==="function";var h=b?new WeakMap():new Map();function a(a,b){if(b!=null){var c={};Object.assign(c,a);Object.keys(b).forEach(function(a){var d=b[a].get,e=d();if(!h.has(d))h.set(d,e),c[a]=e;else{e=h.get(d);c[a]=e}});return c}else return a}a.tests_only_resetDebugCache=void 0;e.exports=a}),null);
__d("throttle",["TimeSlice","TimeSliceInteractionSV","setTimeout","setTimeoutAcrossTransitions"],(function(a,b,c,d,e,f,g){function a(a,b,d){return h(a,b,d,c("setTimeout"),!1)}Object.assign(a,{acrossTransitions:function(a,b,d){return h(a,b,d,c("setTimeoutAcrossTransitions"),!1)},withBlocking:function(a,b,d){return h(a,b,d,c("setTimeout"),!0)},acrossTransitionsWithBlocking:function(a,b,d){return h(a,b,d,c("setTimeoutAcrossTransitions"),!0)}});function h(a,b,d,e,f){var g=b==null?100:b,h,i=null,j=0,k=null,l=[],m=c("TimeSlice").guard(function(){j=Date.now();if(i){var b=function(b){a.apply(h,b)}.bind(null,i),c=l.length;while(--c>=0)b=l[c].bind(null,b);l=[];b();i=null;k=e(m,g)}else k=null},"throttle_"+g+"_ms",{propagationType:c("TimeSlice").PropagationType.EXECUTION,registerCallStack:!0});m.__SMmeta=a.__SMmeta;return function(){c("TimeSliceInteractionSV").ref_counting_fix&&l.push(c("TimeSlice").getGuardedContinuation("throttleWithContinuation"));for(var a=arguments.length,b=new Array(a),n=0;n<a;n++)b[n]=arguments[n];i=b;h=this;d!==void 0&&(h=d);(k===null||Date.now()-j>g)&&(f===!0?m():k=e(m,0))}}b=a;g["default"]=b}),98);
__d("translatedServerString",[],(function(a,b,c,d,e,f){"use strict";function a(a){return a}f["default"]=a}),66);
__d("uriIsRelativePath",[],(function(a,b,c,d,e,f){"use strict";function a(a){return!a.getProtocol()&&!a.getDomain()&&!a.getPort()&&a.toString()!==""}f["default"]=a}),66);