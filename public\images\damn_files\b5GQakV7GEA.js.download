;/*FB_PKG_DELIM*/

__d("AutoplayRulesEngine",[],(function(a,b,c,d,e,f){"use strict";var g=-2,h=-1;a=function(){function a(a){this.$1=g,this.$2=a}var b=a.prototype;b.evaluateAutoplay=function(a,b){b=b();var c=h;if(a)for(var d=0;d<this.$2.length;d++){var e=this.$2[d](a);if(e!=="SKIP"){b=e;c=d;break}}this.$1=c;return b};b.getIndexOfLastWinningRule=function(){return this.$1};b.getRules=function(){return this.$2};return a}();f["default"]=a}),66);
__d("WebauthnAuthenticatorLoginFailureFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1744524");b=d("FalcoLoggerInternal").create("webauthn_authenticator_login_failure",a);e=b;g["default"]=e}),98);
__d("WebauthnAuthenticatorLoginSuccessFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1744525");b=d("FalcoLoggerInternal").create("webauthn_authenticator_login_success",a);e=b;g["default"]=e}),98);
__d("WebauthnAuthenticatorRegisterFailureFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1744526");b=d("FalcoLoggerInternal").create("webauthn_authenticator_register_failure",a);e=b;g["default"]=e}),98);
__d("WebauthnAuthenticatorRegisterSuccessFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1744527");b=d("FalcoLoggerInternal").create("webauthn_authenticator_register_success",a);e=b;g["default"]=e}),98);
__d("WebAuthN",["$InternalEnum","Promise","WebauthnAuthenticatorLoginFailureFalcoEvent","WebauthnAuthenticatorLoginSuccessFalcoEvent","WebauthnAuthenticatorRegisterFailureFalcoEvent","WebauthnAuthenticatorRegisterSuccessFalcoEvent"],(function(a,b,c,d,e,f,g){"use strict";var h;a=b("$InternalEnum")({SECURITY_KEY:"security-key",CLIENT_DEVICE:"client-device",HYBRID:"hybrid"});d=b("$InternalEnum")({USB:"usb",NFC:"nfc",BLE:"ble",INTERNAL:"internal"});e=function(a,d){d=d.publicKey;var e={challenge:Uint8Array.from(d.challenge.split("").map(function(a){return a.charCodeAt(0)})),rpId:d.rpId,allowCredentials:j(d.allowCredentials),userVerification:"preferred",timeout:6e4};d.extensions&&d.extensions.appid&&(e.extensions={appid:d.extensions.appid});return a.get({publicKey:e}).then(function(a){return(h||(h=b("Promise"))).resolve({credential_id:a.id,raw_id:k(a.rawId),type:a.type,response:{authenticator_data:o(a.response.authenticatorData),client_data_json:o(a.response.clientDataJSON),signature:o(a.response.signature),user_handle:o(a.response.userHandle)}})}).then(function(a){c("WebauthnAuthenticatorLoginSuccessFalcoEvent").log(function(){return{key_handle_id:a.credential_id}});return(h||(h=b("Promise"))).resolve(a)})["catch"](function(a){c("WebauthnAuthenticatorLoginFailureFalcoEvent").log(function(){return{error_code:a.code,error_name:a.name,error_message:a.message}});return(h||(h=b("Promise"))).reject(a)})};f=function(a){c("WebauthnAuthenticatorRegisterSuccessFalcoEvent").log(function(){return{key_handle_id:a.id}});return{credential_id:a.id,raw_id:k(a.rawId),type:a.type,client_data_json:o(a.response.clientDataJSON),attestation_object:o(a.response.attestationObject)}};var i=function(a){var d=window.navigator.credentials;if(d===null||d===void 0){var e="The user agent  does not support WebAuthN";c("WebauthnAuthenticatorRegisterFailureFalcoEvent").log(function(){return{error_code:-1,error_name:"NotSupportedError",error_message:e}});return(h||(h=b("Promise"))).reject(e)}try{a=a.publicKey;return d.create({publicKey:{challenge:l(a.challenge),attestation:a.attestation,authenticatorSelection:babelHelpers["extends"]({},a.authSelection),hints:a.hints,rp:a.rp,timeout:a.timeout,pubKeyCredParams:a.pubKeyCredParams,excludeCredentials:j(a.excludeCredentials),user:babelHelpers["extends"]({},a.user,{id:l(a.user.id)})}})}catch(a){c("WebauthnAuthenticatorRegisterFailureFalcoEvent").log(function(){return{error_code:a.code,error_name:a.name,error_message:a.message}});return(h||(h=b("Promise"))).reject(a)}},j=function(a){return a.map(function(a){return{type:a.type,id:m(a.id)}})},k=function(a){return o(a).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")},l=function(a){return new Uint8Array(a.length).map(function(b,c){return a.charCodeAt(c)})},m=function(a){return l(atob(n(a)))},n=function(a){return(a+"===").slice(0,a.length+(4-a.length%4)%4).replace(/-/g,"+").replace(/_/g,"/")},o=function(a){a=new Uint8Array(a);var b="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c=a.length%3;function d(a){return b.charAt(a>>18&63)+b.charAt(a>>12&63)+b.charAt(a>>6&63)+b.charAt(a&63)}var e="";for(var f=0,g=a.length-c;f<g;f+=3){var h=(a[f]<<16)+(a[f+1]<<8)+a[f+2];e+=d(h)}switch(c){case 1:h=a[a.length-1];e+=b.charAt(h>>2);e+=b.charAt(h<<4&63);e+="==";break;case 2:h=(a[a.length-2]<<8)+a[a.length-1];e+=b.charAt(h>>10);e+=b.charAt(h>>4&63);e+=b.charAt(h<<2&63);e+="=";break;default:break}return e};g.PublicKeyCredentialHints=a;g.AuthenticatorTransport=d;g.createLoginChallenge=e;g.createRegisterRequest=f;g.getKeyCrenditial=i;g.encodeCredentials=j;g.bufferEncode=k;g.string2buffer=l;g.base64ToBuffer=m;g.Base64DecodeUrl=n;g.encodeByteArray=o}),98);
__d("CAAWebBloksPasskeyUtils",["$InternalEnum","WebAuthN"],(function(a,b,c,d,e,f,g){"use strict";var h="Meta",i=6e4,j=b("$InternalEnum")({NOT_ALLOWED_ERROR:0,CONSTRAINT_ERROR:1,INVALID_STATE_ERROR:2,NOT_SUPPORTED_ERROR:3,SECURITY_ERROR:4,ABORT_ERROR:5,UNKNOWN_ERROR:6});function a(a,b){var c,e,f=b.getExpression("on_success"),g=b.getExpression("on_error"),j=b.getExpression("on_cancel");c=JSON.parse((c=b.get("additional_params"))!=null?c:"{}");if(g==null)return null;if(f==null){k(a,"on_success",g);return null}if(j==null){k(a,"on_cancel",g);return null}var m=b.get("prf_input_first");if(m!=null)return;m=(m=b.get("rp_id"))!=null?m:(m=c.rp)==null?void 0:m.id;if(m==null){k(a,"rp_id",g);return null}e=(e=b.get("challenge"))!=null?e:c.challenge;if(e==null){k(a,"challenge",g);return null}var n=b.get("userid");if(n==null){k(a,"userid",g);return null}var o=b.get("username");if(o==null){k(a,"username",g);return null}a=d("WebAuthN").encodeCredentials(c.excludeCredentials);c=(c=(c=b.get("enable_device_key_signature_ext"))==null?void 0:c.valueOf())!=null?c:!1;b=(b=(b=b.get("prefer_immediately_available_credentials"))==null?void 0:b.valueOf())!=null?b:!1;e={challenge:l(e)};e=d("WebAuthN").string2buffer(JSON.stringify(e));e={publicKey:{attestation:"none",authenticatorSelection:{residentKey:"preferred",userVerification:"required"},challenge:e,excludeCredentials:a,pubKeyCredParams:[{alg:-7,type:"public-key"},{alg:-257,type:"public-key"}],rp:{id:m,name:h},timeout:i,user:{displayName:o,id:d("WebAuthN").string2buffer(n.toString()),name:o}}};return{callbacks:{onCancel:j,onError:g,onSuccess:f},extensions:{enable_device_key_signature_ext:c,prefer_immediately_available_credentials:b},json:e}}function c(a,b){var c,e,f,g,h=b.getExpression("on_success"),j=b.getExpression("on_error"),l=b.getExpression("on_cancel");c=JSON.parse((c=b.get("additional_params"))!=null?c:"{}");if(j==null)return null;if(h==null){k(a,"on_success",j);return null}if(l==null){k(a,"on_cancel",j);return null}e=(e=b.get("rpid"))!=null?e:(e=c.rp)==null?void 0:e.id;if(e==null){k(a,"rpid",j);return null}f=(f=b.get("challenge"))!=null?f:c.challenge;if(f==null){k(a,"challenge",j);return null}a=(a=(a=b.get("enable_device_key_signature_ext"))==null?void 0:a.valueOf())!=null?a:!1;b=(b=(b=b.get("prefer_immediately_available_credentials"))==null?void 0:b.valueOf())!=null?b:!1;f=d("WebAuthN").string2buffer(f);g=(g=c.userVerification)!=null?g:"preferred";c=d("WebAuthN").encodeCredentials(c.allowCredentials);f={publicKey:{challenge:f,timeout:i,rpId:e,userVerification:g,allowCredentials:c}};return{callbacks:{onSuccess:h,onError:j,onCancel:l},json:f,extensions:{prefer_immediately_available_credentials:b,enable_device_key_signature_ext:a}}}function k(a,b,c){b="Missing required parameter "+b;a.executeCatch(c.getValue(),[1,b])}function e(a){var b={authenticatorAttachment:a.authenticatorAttachment,id:a.id,raw_id:d("WebAuthN").bufferEncode(a.rawId),response:{attestationObject:d("WebAuthN").bufferEncode(a.response.attestationObject),clientDataJSON:d("WebAuthN").bufferEncode(a.response.clientDataJSON)},type:a.type};b=l(JSON.stringify(b));return{extension_results:l(JSON.stringify(a.clientExtensionResults)),passkey_id:a.id,payload:b,payload_version:"web"}}function f(a){var b=l(JSON.stringify(a.toJSON==null?void 0:a.toJSON())),c=l(JSON.stringify(a.getClientExtensionResults()));return{passkey_id:a.id,payload:b,payload_version:"web",extension_results:c}}function l(a){a=btoa(a);return a.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function m(a,b,c,d){c=j.isValid(c)?c:j.UNKNOWN_ERROR;a.executeCatch(b.getValue(),[c,d])}g.PasskeyErrorCodes=j;g.getValidatedPasskeyRegistrationParams=a;g.getValidatedPasskeyAssertionParams=c;g.reportMissingParameter=k;g.buildBloksRegistrationArguments=e;g.buildBloksAssertionArguments=f;g.base64UrlEncode=l;g.onError=m}),98);
__d("CVCv3DisabledPlayerOrigins",[],(function(a,b,c,d,e,f){a=Object.freeze({BEEPER:"beeper",FB_STORIES:"fb_stories"});f["default"]=a}),66);
__d("CVCv3DisabledPlayerSubOrigins",[],(function(a,b,c,d,e,f){a=Object.freeze({LIVE_BEEPER:"live_beeper"});f["default"]=a}),66);
__d("CvcV3HttpEventFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1856513");b=d("FalcoLoggerInternal").create("cvc_v3_http_event",a);e=b;g["default"]=e}),98);
__d("CVCv3SubscriptionHelper",["CvcV3HttpEventFalcoEvent","DateConsts","guid"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,d){this.$1=a,this.$2=c("guid")(),this.$3=(b!=null?b:"null")+"::"+(d!=null?d:"null"),this.$4=null,this.$5=null}var b=a.prototype;b.isValidSubscription=function(){return!!this.$1};b.makeCVCv3StateUpdate=function(a,b,c,e){var f=null;a!=null&&!Number.isNaN(a)&&b!=null&&!Number.isNaN(b)&&(f={m:e,pf:Math.floor((b-a)*d("DateConsts").MS_PER_SEC),s:c,sa:Math.floor(a*d("DateConsts").MS_PER_SEC)});e={pps:this.$4,ps:f,si:this.$2,so:this.$3,vi:this.$1};this.$4=f;return e};b.makeUnifiedVideoCVCUpdate=function(a,b,c,d,e){a=this.makeCVCv3StateUpdate(a,b,c,d);this.$5!=null&&(a.tk=this.$5);return babelHelpers["extends"]({},a,{},e)};b.processUnifiedResponse=function(a){a=a;this.$5=a.tk;return a};b.clearAnyPreviousContext=function(){this.$4=null};b.logHttpRequestSuccess=function(a){var b=this;c("CvcV3HttpEventFalcoEvent").log(function(){return{name:"http_request_success",duration_ms:a!=null?a.toString():null,countable_id:b.$1}})};b.logHttpRequestFailure=function(a,b){var d=this;c("CvcV3HttpEventFalcoEvent").log(function(){return{name:"http_request_failed",error_msg:a,duration_ms:b!=null?b.toString():null,countable_id:d.$1}})};b.logHttpRequestTimeout=function(a){var b=this;c("CvcV3HttpEventFalcoEvent").log(function(){return{name:"http_request_timeout",duration_ms:a!=null?a.toString():null,countable_id:b.$1}})};b.logHttpResponseBad=function(a,b){var d=this;c("CvcV3HttpEventFalcoEvent").log(function(){return{name:"http_response_bad",error_msg:a,duration_ms:b!=null?b.toString():null,countable_id:d.$1}})};b.logDebugInfo=function(a){var b=this;c("CvcV3HttpEventFalcoEvent").log(function(){return{name:a,countable_id:b.$1}})};return a}();g["default"]=a}),98);
__d("CappedListItemContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext();g["default"]=b}),98);
__d("CometAudioManagerHooks",["CometAudioManagerContexts","react","recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){var a=i(d("CometAudioManagerContexts").AudioApiContext);return a}g.useAudioApi=a}),98);
__d("CometVideoPlayerLoggingConfigForPolaris",["IGVideoMetadataProvider","PolarisIsLoggedIn","gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return{disableCVCSubscription:!d("PolarisIsLoggedIn").isLoggedIn(),disableLogging:!1,loggingToSNAPLCreateMetadataProvider:d("IGVideoMetadataProvider").createIGVideoMetadataProvider,loggingToSNAPLEnabled:c("gkx")("3179"),loggingToVPLEnabled:!1}}g.createCometVideoPlayerLoggingConfigForPolaris=a}),98);
__d("makeAudioSymbol",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){return b+"::"+a}f["default"]=a}),66);
__d("CoreVideoPlayerAudioClient.react",["CometAudioManagerContexts","CometAudioManagerHooks","CometThrottle","VideoPlayerHooks","VideoPlayerPortalingPlaceInfoProvider.react","createVideoStateHook","makeAudioSymbol","react","usePrevious"],(function(a,b,c,d,e,f,g){"use strict";var h,i;b=h||d("react");var j=b.useContext,k=b.useEffect,l=b.useId,m=b.useMemo,n=b.useRef,o=b.useState;e=d("createVideoStateHook").createVideoStateHook();var p=e.valueHook;function a(){var a=p({isVolumeInitialized:!1}),b=l(),e=(i||(i=d("VideoPlayerHooks"))).useInstanceKey(),f=m(function(){return c("makeAudioSymbol")(b,e)},[b,e]),g=d("CometAudioManagerHooks").useAudioApi(),h=n(g);k(function(){h.current=g},[g]);var q=i.useController(),r=200,s=i.useLastPlayReason(),t=i.useLastMuteReason(),u=i.useMuted();r=c("CometThrottle")(i.useVolume,r,{trailing:!0});var v=r(),w=c("usePrevious")(v),x=i.usePaused(),y=j(d("CometAudioManagerContexts").CometAudioLocalScopeContext),z=j(d("CometAudioManagerContexts").CometAudioGroupContext),A=d("VideoPlayerPortalingPlaceInfoProvider.react").useVideoPlayerPortalingPlaceInfo(),B=i.useVolumeSetting(),C=m(function(){return(z==null?void 0:z.groupID)||d("CometAudioManagerContexts").makeAudioGroupID()},[z==null?void 0:z.groupID]);r=o(!0);var D=r[0];r=r[1];var E=m(function(){return{audioLocalScope:y,controller:q,groupID:C,instanceKey:e,lastMuteReason:t,lastPlayReason:s,muted:u,paused:x,previousVolume:w,symbol:f,videoPlayerPortalingPlaceInfo:A,volume:v}},[y,q,C,e,t,s,u,x,w,f,A,v]);k(function(){B!=null&&a.isVolumeInitialized!==!0&&(q.setVolume(B),a.isVolumeInitialized=!0)},[q,a,!0,B]);r=z||{allowSound:D,groupID:C,setAllowSound:r};var F=n(r);k(function(){g&&g.register(E,F.current);return function(){g&&g.unregister(f,C)}},[g,E,C,f]);k(function(){g&&g.update(E)},[g,E]);k(function(){!D&&h.current&&q.setMuted(!0,"audio_manager_initiated")},[D,h,q]);return null}g["default"]=a}),98);
__d("VideoPlayerAutoplayHooks",["VideoPlayerAutoplayContexts","react","unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){var a=i(d("VideoPlayerAutoplayContexts").AutoplayApiContext);if(a==null)throw c("unrecoverableViolation")("Empty AutoplayApiContext. Are you rendering useAutoplayApi outside of VideoAutoplayManagerX?","comet_video_player");return a}function b(){return i(d("VideoPlayerAutoplayContexts").VideoAutoplayLocalScopeContext)}g.useAutoplayApi=a;g.useVideoAutoplayLocalScope=b}),98);
__d("VideoPlayerDebugAutoplayAPI",["cr:1453865","emptyFunction"],(function(a,b,c,d,e,f,g){"use strict";d=(a=b("cr:1453865"))!=null?a:{useVideoPlayerDebugAPI:c("emptyFunction").thatReturns(null),useVideoPlayerDebugAPIDefinition:c("emptyFunction"),useVideoPlayerDebugInfo:c("emptyFunction").thatReturns(null)};g["default"]=d}),98);
__d("evaluateVideoAutoplayDefaultAllowRule",[],(function(a,b,c,d,e,f){"use strict";function a(){return"ALLOW"}a.displayName="evaluateVideoAutoplayDefaultRule";f["default"]=a}),66);
__d("makeSelectedAutoplayVideoSymbol",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){return b+"::"+a}f["default"]=a}),66);
__d("useAutoplayRulesEngine",["AutoplayRulesEngine","react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useEffect,j=b.useRef,k=b.useState;function a(a){var b=j(a),d=k(function(){return new(c("AutoplayRulesEngine"))(a)}),e=d[0],f=d[1];i(function(){b.current!==a&&f(function(){return new(c("AutoplayRulesEngine"))(a)})},[a]);i(function(){b.current=e.getRules()},[e]);return e}g["default"]=a}),98);
__d("useConcurrentAutoplayManagementAPI",["react","removeFromArray"],(function(a,b,c,d,e,f,g){"use strict";var h;e=h||d("react");var i=e.useId,j=e.useState;function a(){return i()}function k(){var a=[];return{isControllingComponent:function(b){var c=a.length;return a[c-1]===b},registerControllingComponent:function(b){c("removeFromArray")(a,b),a.push(b)},unregisterControllingComponent:function(b){var d=a.length;d>1&&c("removeFromArray")(a,b);return d>1}}}function b(){var a=j(function(){return k()});a=a[0];return a}g.useAutoplayControlID=a;g.createConcurrentAutoplayManagementAPI=k;g.useConcurrentAutoplayManagementAPI=b}),98);
__d("VideoPlayerViewabilityHooks",["VideoPlayerHooks"],(function(a,b,c,d,e,f,g){"use strict";var h;g.useVideoPlayerExtendedPassiveViewabilityInfo=(h||(h=d("VideoPlayerHooks"))).useVideoPlayerExtendedPassiveViewabilityInfo;g.useVideoPlayerPassiveViewabilityInfo=h.useVideoPlayerPassiveViewabilityInfo;g.useVideoPlayerViewabilityInfo=h.useVideoPlayerViewabilityInfo}),98);
__d("useEventCallbackOn",["react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(a,b,c){i(function(){a.addEventListener(b,c);return function(){a.removeEventListener(b,c)}},[a,b,c])}g["default"]=a}),98);
__d("useIsBackgrounded",["emptyFunction","react","useEventCallbackOn"],(function(a,b,c,d,e,f,g){"use strict";var h;f=h||d("react");var i=f.useCallback,j=f.useState;function b(){var b=a.document;if(typeof b.hidden!=="undefined")return"visibilitychange";else if(typeof b.mozHidden!=="undefined")return"mozvisibilitychange";else if(typeof b.msHidden!=="undefined")return"msvisibilitychange";else if(typeof b.webkitHidden!=="undefined")return"webkitvisibilitychange";return"visibilitychange"}function k(){var b=a.document;if(typeof b.hidden!=="undefined")return b.hidden;else if(typeof b.mozHidden!=="undefined")return b.mozHidden;else if(typeof b.msHidden!=="undefined")return b.msHidden;else if(typeof b.webkitHidden!=="undefined")return b.webkitHidden;return!1}var l=b();function m(b){var c=a.document.hasFocus?a.document.hasFocus():!0,d=k();return d||(b?!1:!c)}function n(){return k()}function e(b){var d=(b==null?void 0:b.noPauseOnBlurOrFocus)===!0,e=j(function(){return m(d)}),f=e[0],g=e[1];e=j(function(){return n()});var h=e[0],k=e[1],o=!!b&&b.scrollTerminatesHiddenOrBlurred===!0;e=i(function(){g(m(d)),k(n())},[g,d]);b=e;var p=i(function(){var a=m(d);a&&o&&f&&g(!1)},[f,d,o]);d&&(b=c("emptyFunction"));c("useEventCallbackOn")(a.window,"blur",b);c("useEventCallbackOn")(a.window,"focus",b);c("useEventCallbackOn")(a.document,l,e);c("useEventCallbackOn")(a.window,"scroll",p);return{isDocumentHidden:h,isDocumentHiddenOrBlurred:f}}g["default"]=e}),98);
__d("useVideoPlayerBandwidthEstimate",["VideoPlayerOzWWWGlobalConfig","oz-player/networks/OzBandwidthEstimator","react","useInterval"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useState,j=5e3;function a(){var a=function(){return c("oz-player/networks/OzBandwidthEstimator").getBandwidth(c("VideoPlayerOzWWWGlobalConfig"))},b=i(a()),d=b[0],e=b[1];c("useInterval")(function(){return e(a())},j);return d}g["default"]=a}),98);
__d("useVideoAutoplayState",["HiddenSubtreePassiveContext","VideoPlayerHooks","VideoPlayerShakaGlobalConfig","VideoPlayerViewabilityHooks","react","useIsBackgrounded","useVideoPlayerBandwidthEstimate"],(function(a,b,c,d,e,f,g){"use strict";var h,i;b=h||d("react");var j=b.startTransition,k=b.useContext,l=b.useEffect,m=b.useMemo,n=b.useRef,o=b.useState;function a(a){var b=(i||(i=d("VideoPlayerHooks"))).useAdClientToken(),e=i.useAutoplayGatingResult(),f=i.useBroadcastStatus(),g=i.useEnded(),h=i.useIsFrozenPassive(),p=i.useLastPauseReason(),q=i.useLastPlayReason(),r=i.useMuted(),s=i.usePaused(),t=k(c("HiddenSubtreePassiveContext")),u=!b;a=(a==null?void 0:a.noPauseOnBlurOrFocus)===!0||d("VideoPlayerShakaGlobalConfig").getBool("comet_www_no_pause_on_blur_or_focus_events",!1);a=c("useIsBackgrounded")({noPauseOnBlurOrFocus:a,scrollTerminatesHiddenOrBlurred:u});var v=a.isDocumentHidden,w=a.isDocumentHiddenOrBlurred,x=c("useVideoPlayerBandwidthEstimate")(),y=d("VideoPlayerViewabilityHooks").useVideoPlayerPassiveViewabilityInfo(),z=d("VideoPlayerViewabilityHooks").useVideoPlayerExtendedPassiveViewabilityInfo();u=o(t.getCurrentState().backgrounded);var A=u[0],B=u[1];a=o(t.getCurrentState().hidden);var C=a[0],D=a[1];l(function(){var a=function(){j(function(){var a=t.getCurrentState();D(a.hidden);B(a.backgrounded)})},b=t.subscribeToChanges(a);a();return function(){b.remove()}},[t,B,D]);u=o(function(){return h.getCurrentState()});var E=u[0],F=u[1];l(function(){var a=h.subscribeToChanges(function(a){F(a)}),b=h.getCurrentState();F(b);return function(){a.remove()}},[h,F]);var G=m(function(){return{adClientToken:b,autoplayGatingResult:e,bandwidthEstimate:x,broadcastStatus:f,controllerIsFrozen:E,currentSubtreeIsBackgrounded:A,currentSubtreeIsHidden:C,ended:g,hiddenSubtreePassive:t,isDocumentHidden:v,isDocumentHiddenOrBlurred:w,isFrozenPassive:h,lastPauseReason:p,lastPlayReason:q,muted:r,paused:s,videoPlayerExtendedPassiveViewabilityInfo:z,videoPlayerPassiveViewabilityInfo:y}},[b,e,x,f,E,A,C,g,t,v,w,h,p,q,r,s,z,y]);a=o(G);var H=a[0],I=a[1],J=n(H);l(function(){J.current=H},[H]);l(function(){var a=!1;for(var b in G)if(Object.prototype.hasOwnProperty.call(G,b)&&G[b]!==J.current[b]){a=!0;break}a&&I(G)},[G,I]);return H}g["default"]=a}),98);
__d("CoreVideoPlayerAutoplayClient.react",["CoreVideoPlayerAutoplayClientUtils","HiddenSubtreePassiveContext","VideoPlayerAutoplayHooks","VideoPlayerDebugAutoplayAPI","VideoPlayerHooks","VideoPlayerViewabilityConstants","WwwCometVideoAutoplayFalcoEvent","evaluateVideoAutoplayDefaultAllowRule","evaluateVideoAutoplayDefaultIgnoreRule","makeSelectedAutoplayVideoSymbol","react","useAutoplayRulesEngine","useConcurrentAutoplayManagementAPI","useVideoAutoplayState"],(function(a,b,c,d,e,f,g){"use strict";var h,i;b=h||d("react");var j=b.useCallback,k=b.useContext,l=b.useEffect,m=b.useId,n=b.useMemo,o=b.useRef,p=b.useState;function a(){var a=m(),b=(i||(i=d("VideoPlayerHooks"))).useCanAutoplay(),e=i.useInstanceKey(),f=n(function(){return c("makeSelectedAutoplayVideoSymbol")(a,e)},[a,e]),g=o(f);l(function(){g.current=f},[f]);var h=k(c("HiddenSubtreePassiveContext")),q=i.useController(),r=o(!1),s=d("useConcurrentAutoplayManagementAPI").useAutoplayControlID();l(function(){d("CoreVideoPlayerAutoplayClientUtils").log(g.current,"[ASSUME CONTROL] "+s),q.registerControllingComponent(s)},[q,s]);var t=i.useVideoPlayerPassiveViewabilityInfo(),u=q.getCurrentState();u=u.paused;var v=o(u),w=d("VideoPlayerAutoplayHooks").useVideoAutoplayLocalScope(),x=o(w);l(function(){x.current=w},[w]);var y=j(function(){var a=q.getCurrentState(),b=a.lastPlayReason;a=a.paused;var d=v.current;v.current=a;var e=t==null?void 0:t.getCurrent();if(e){e=e.visiblePercentage;a=!a&&a!==d;d=e<c("VideoPlayerViewabilityConstants").DEFAULT_VIEWABILITY_PERCENTAGE_FOR_PAUSE&&e>0;x.current.disableScrollBeforePlayWhenOffscreen!==!0&&a&&b==="user_initiated"&&d&&(r.current=!0)}},[q,t]),z=o(y);l(function(){z.current=y},[y]);l(function(){var a=q.subscribe(function(){y()});return function(){a.remove()}},[q,y]);var A=c("useVideoAutoplayState")({noPauseOnBlurOrFocus:w.noPauseOnBlurOrFocus}),B=c("useAutoplayRulesEngine")(w.autoplayLocalRules),C=b==="allow"?c("evaluateVideoAutoplayDefaultAllowRule"):c("evaluateVideoAutoplayDefaultIgnoreRule");u=p(function(){return C});var D=u[0],E=u[1];u=p(null);var F=u[0],G=u[1];u=p(function(){return B.evaluateAutoplay(A,C)});var H=u[0],I=u[1];u=p(null);var J=u[0],K=u[1],L=o(H),M=d("VideoPlayerAutoplayHooks").useAutoplayApi(),N=o(M);l(function(){N.current=M},[M]);var O=b!=="dangerously_disable_autoplay_management";l(function(){M.register(f,e,w,q,h,O);c("WwwCometVideoAutoplayFalcoEvent").log(function(){return{autoplay_event_name:"register",autoplay_scope_id:x.current.autoplayScopeID,event_creation_time:Date.now(),initiator:g.current,initiator_type:"autoplay_component",selected_autoplay_video_symbol:M.getAutoplayManagerDebugInfo(x.current).selectedAutoplayVideoSymbol,target:g.current,target_current_autoplay_decision:L.current}});return function(){M.unregister(f),c("WwwCometVideoAutoplayFalcoEvent").log(function(){return{autoplay_event_name:"unregister",autoplay_scope_id:x.current.autoplayScopeID,event_creation_time:Date.now(),initiator:g.current,initiator_type:"autoplay_component",selected_autoplay_video_symbol:M.getAutoplayManagerDebugInfo(x.current).selectedAutoplayVideoSymbol,target:g.current,target_current_autoplay_decision:L.current}})}},[M,w,q,h,e,O,f]);l(function(){var a=function(){var b=B.evaluateAutoplay(A,C);L.current=b;var e=B.getRules(),h=B.getIndexOfLastWinningRule(),a=h>=0?e[h]:C;e=t&&t.getCurrent();h=q.getCurrentState();h=h.paused;(J===null||J!==b)&&(I(b),K(b));if(b==="PAUSE"&&!h&&q.isControllingComponent(s)){h=!0;z.current();if(r.current){r.current=!1;var i=e==null?void 0:e.positionToViewport;i&&q.scrollIntoView(i.top<0);h=!1}if(h){i="[PAUSE] via short-circuit on "+a.name+".";d("CoreVideoPlayerAutoplayClientUtils").log(g.current,i);q.pause("autoplay_initiated");c("WwwCometVideoAutoplayFalcoEvent").log(function(){return{autoplay_event_name:"pause",autoplay_scope_id:x.current.autoplayScopeID,event_creation_time:Date.now(),initiator:g.current,initiator_type:"autoplay_component",selected_autoplay_video_symbol:M.getAutoplayManagerDebugInfo(x.current).selectedAutoplayVideoSymbol,target:g.current,target_current_autoplay_decision:b}})}}(F===null||F!==a)&&(E(function(){return a}),G(function(){return a}),c("WwwCometVideoAutoplayFalcoEvent").log(function(){return{autoplay_event_name:"rule_changed",autoplay_scope_id:x.current.autoplayScopeID,event_creation_time:Date.now(),initiator:g.current,initiator_type:"autoplay_component",selected_autoplay_video_symbol:M.getAutoplayManagerDebugInfo(x.current).selectedAutoplayVideoSymbol,target:g.current,target_current_autoplay_decision:b}}));d("CoreVideoPlayerAutoplayClientUtils").log(g.current,"[DECISION: "+b+"] from "+a.name);e&&M.update(f,b,e)},b=h.subscribeToChanges(function(a){h.getCurrentState().hidden||h.getCurrentState().backgrounded?q.isControllingComponent(s)||q.unregisterControllingComponent(s):q.registerControllingComponent(s)}),e=t?t.subscribe(function(){h.getCurrentState().hidden||h.getCurrentState().backgrounded?q.isControllingComponent(s)||q.unregisterControllingComponent(s):q.registerControllingComponent(s),a()}):null;a();return function(){b.remove(),e&&e.remove()}},[M,s,B,q,C,h,F,J,E,I,G,K,f,A,t]);l(function(){var a=q.isControllingComponent(s),b=q.getCurrentState();b=b.paused;d("CoreVideoPlayerAutoplayClientUtils").componentShouldPause(H,L.current,b,a)&&(d("CoreVideoPlayerAutoplayClientUtils").log(g.current,"[PAUSE] the video for "+H),q.pause("autoplay_initiated"),c("WwwCometVideoAutoplayFalcoEvent").log(function(){return{autoplay_event_name:"pause",autoplay_scope_id:x.current.autoplayScopeID,event_creation_time:Date.now(),initiator:g.current,initiator_type:"autoplay_component",selected_autoplay_video_symbol:N.current.getAutoplayManagerDebugInfo(x.current).selectedAutoplayVideoSymbol,target:g.current,target_current_autoplay_decision:H}}))},[H,q,s]);l(function(){return function(){d("CoreVideoPlayerAutoplayClientUtils").log(g.current,"[RELEASE CONTROL] "+s),q.unregisterControllingComponent(s)}},[q,s]);c("VideoPlayerDebugAutoplayAPI").useVideoPlayerDebugAPIDefinition({applicableRule:D,autoplayApiRef:N,autoplayDecision:H,autoplayLocalScope:w,symbol:f});return null}g["default"]=a}),98);
__d("VideoPlayerViewabilityProvider.react",["VideoPlayerViewabilityContexts","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=a.children,c=a.isDesktopPictureInPicture,e=a.isFullscreen;a=a.videoPlayerPassiveViewabilityInfo;return i.jsx(d("VideoPlayerViewabilityContexts").VideoPlayerPassiveViewabilityInfoContext.Provider,{value:a,children:i.jsx(d("VideoPlayerViewabilityContexts").VideoPlayerDesktopPictureInPictureContext.Provider,{value:c,children:i.jsx(d("VideoPlayerViewabilityContexts").VideoPlayerFullscreenContext.Provider,{value:e,children:b})})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("useBlockCappedListItemWhenPlayingVideo",["CappedListItemContext","err","react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useContext,j=b.useEffect;function a(a,b){var d=(b=i(c("CappedListItemContext")))==null?void 0:b.registerBlocker;j(function(){if(!d)return;var b=null,c=null,e=function(){var e=a.getCurrentState().playing;if(e!==b){b=e;c&&(c(),c=null);if(e){e="playing_video";var f;c=d({reason:e,source:f})}}};e();var f=a.subscribe(e);return function(){f.remove(),c&&(c(),c=null)}},[a,d])}g["default"]=a}),98);
__d("useDisableVirtualizationWhenPlayingVideo",["VirtualizationContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useContext,j=b.useEffect;function a(a,b){var d=i(c("VirtualizationContext"));j(function(){if(d==null)return;var c=d.createFlag,e=d.createPin,f=null,g=null,h=function(){g==null&&(g=c("HAS_PLAYER","playerInstanceKey: "+b));var d=a.getCurrentState();d.playing||d.stalling?f||(f=e("player "+b+" is playing")):d.isFullscreen?f||(f=e("player "+b+" is fullscreen")):(f==null?void 0:f(),f=null)};h();var i=a.subscribe(h);return function(){i.remove(),g==null?void 0:g(),g=null,f==null?void 0:f(),f=null}},[a,b,d])}g["default"]=a}),98);
__d("wrapWithContextProviders",["react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a,b){return b.reduceRight(function(a,b){return b(a)},a)}a.displayName=a.name+" [from "+f.id+"]";function b(a,b){return function(c){return i.jsx(a.Provider,{value:b,children:c})}}g.wrapWithContextProviders=a;g.makeRenderProviderFn=b}),98);
__d("VideoPlayerComponentContainer.react",["CometVisualCompletionAttributes","VideoPlayerContexts","VideoPlayerHooks","VideoPlayerViewabilityProvider.react","clearTimeout","cr:6094","react","setTimeout","useBlockCappedListItemWhenPlayingVideo","useDisableVirtualizationWhenPlayingVideo","wrapWithContextProviders"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=h||(h=d("react"));e=h;var l=e.useCallback,m=e.useEffect,n=e.useRef,o=e.useState;function p(a){var b=n(a);m(function(){b.current=a},[a]);var d=o(!1),e=d[0],f=d[1];d=o(function(){var a=null;return{cleanup:function(){a&&a()},pingNotIdle:function(){a&&a();f(!1);var d=c("setTimeout")(function(){f(!0)},b.current);a=function(){c("clearTimeout")(d),a=null}}}});d=d[0];var g=d.cleanup;d=d.pingNotIdle;m(function(){return g},[g]);return{isIdle:e,pingNotIdle:d}}function q(){var a=o(!1),b=a[0],c=a[1];a=3e3;a=p(a);var d=a.isIdle,e=a.pingNotIdle;a=l(function(){c(!0),e()},[e]);var f=l(function(){c(!1),e()},[e]),g=l(function(){c(!0),e()},[e]),h=l(function(){e()},[e]),i=l(function(){e()},[e]);return{isHovering:b,isIdle:d,onMouseDown:h,onMouseEnter:a,onMouseLeave:f,onMouseMove:g,onMouseUp:i}}function r(a){a=a.children;var b=q(),e=b.isHovering,f=b.isIdle,g=b.onMouseDown,h=b.onMouseEnter,l=b.onMouseLeave,m=b.onMouseMove;b=b.onMouseUp;var n=(i||(i=d("VideoPlayerHooks"))).useIsFullscreen();return k.jsx("div",babelHelpers["extends"]({className:{0:"x5yr21d x10l6tqk x13vifvy xh8yej3",1:"x5yr21d x10l6tqk x13vifvy xh8yej3 xjfk50j"}[!!(n&&f)<<0]},c("CometVisualCompletionAttributes").IGNORE,{onMouseDown:g,onMouseEnter:h,onMouseLeave:l,onMouseMove:m,onMouseUp:b,children:k.jsx((j||(j=d("VideoPlayerContexts"))).VideoPlayerMouseHoverContext.Provider,{value:e,children:k.jsx(j.VideoPlayerMouseIdleContext.Provider,{value:f,children:a})})}))}r.displayName=r.name+" [from "+f.id+"]";function a(a){var e=a.children;a=babelHelpers.objectWithoutPropertiesLoose(a,["children"]);var f=a.adClientToken,g=a.audioAvailabilityInfo,h=a.autoplayGatingResult,i=a.broadcastStatus,l=a.canAutoplay,m=a.controller,n=a.dimensions,o=a.initialTracePolicy,p=a.instanceKey,q=a.isDesktopPictureInPicture,s=a.isFullscreen,t=a.isNCSR,u=a.isPremiumMusicVideo,v=a.lastMuteReason,w=a.lastPauseReason,x=a.lastPlayReason,y=a.videoFBID,z=a.videoPixelsAspectRatio,A=a.videoPlayerPassiveViewabilityInfo,B=a.videoState,C=a.volumeSetting,D=B.activeCaptions,E=B.activeEmsgBoxes,F=B.availableAudioTracks,G=B.availableVideoQualities,H=B.availableVideoTracks,I=B.bufferEnd,J=B.captionDisplayStyle,K=B.captionsLoaded,L=B.captionsVisible,M=B.currentAudioTrackID,N=B.currentVideoQuality,O=B.currentVideoTrackID,P=B.duration,Q=B.ended,R=B.error,S=B.inbandCaptionsAutogenerated,T=B.inPlayStalling,U=B.isAbrEnabled,V=B.isLiveRewindActive,W=B.isLiveRewindAvailable,X=B.lastPlayedTimeMs,Y=B.latencyLevel,Z=B.loopCount,$=B.loopCurrent,aa=B.muted,ba=B.paused,ca=B.playerVersion,da=B.playing,ea=B.seekableRanges,fa=B.seeking,ga=B.selectedVideoQuality,ha=B.stalling,ia=B.streamInterrupted,ja=B.targetAudioTrack,ka=B.targetPlaybackRate,la=B.targetVideoQuality,ma=B.volume;B=B.watchTimeMs;e=k.jsx(r,{children:e});b("cr:6094")(p,{controller:m,coreVideoStates:a,videoPlayerPassiveViewabilityInfo:A});e=(a=d("wrapWithContextProviders")).wrapWithContextProviders(e,[a.makeRenderProviderFn((j||(j=d("VideoPlayerContexts"))).VideoFBIDContext,y),d("wrapWithContextProviders").makeRenderProviderFn(j.StallingContext,ha),d("wrapWithContextProviders").makeRenderProviderFn(j.PlayingContext,da),d("wrapWithContextProviders").makeRenderProviderFn(j.InPlayStallingContext,T),d("wrapWithContextProviders").makeRenderProviderFn(j.BufferEndContext,I),d("wrapWithContextProviders").makeRenderProviderFn(j.LastMuteReasonContext,v),d("wrapWithContextProviders").makeRenderProviderFn(j.LastPlayReasonContext,x),d("wrapWithContextProviders").makeRenderProviderFn(j.LastPauseReasonContext,w),d("wrapWithContextProviders").makeRenderProviderFn(j.PausedContext,ba),d("wrapWithContextProviders").makeRenderProviderFn(j.CurrentAudioTrackIDContext,M),d("wrapWithContextProviders").makeRenderProviderFn(j.CurrentVideoTrackIDContext,O),d("wrapWithContextProviders").makeRenderProviderFn(j.CurrentVideoQualityContext,N),d("wrapWithContextProviders").makeRenderProviderFn(j.MutedContext,aa),d("wrapWithContextProviders").makeRenderProviderFn(j.TargetAudioTrackContext,ja),d("wrapWithContextProviders").makeRenderProviderFn(j.VolumeContext,ma),d("wrapWithContextProviders").makeRenderProviderFn(j.SelectedVideoQualityContext,ga),d("wrapWithContextProviders").makeRenderProviderFn(j.SeekingContext,fa),d("wrapWithContextProviders").makeRenderProviderFn(j.DurationContext,P),d("wrapWithContextProviders").makeRenderProviderFn(j.EndedContext,Q),d("wrapWithContextProviders").makeRenderProviderFn(j.ErrorContext,R),d("wrapWithContextProviders").makeRenderProviderFn(j.DimensionsContext,n||null),d("wrapWithContextProviders").makeRenderProviderFn(j.InstanceKeyContext,p),d("wrapWithContextProviders").makeRenderProviderFn(j.ControllerContext,m),d("wrapWithContextProviders").makeRenderProviderFn(j.AvailableAudioTracksContext,F),d("wrapWithContextProviders").makeRenderProviderFn(j.AvailableVideoQualitiesContext,G),d("wrapWithContextProviders").makeRenderProviderFn(j.AvailableVideoTracksContext,H),d("wrapWithContextProviders").makeRenderProviderFn(j.PlayerVersionContext,ca),d("wrapWithContextProviders").makeRenderProviderFn(j.IsAbrEnabledContext,U),d("wrapWithContextProviders").makeRenderProviderFn(j.TargetVideoQualityContext,la),d("wrapWithContextProviders").makeRenderProviderFn(j.PlaybackRateContext,ka),d("wrapWithContextProviders").makeRenderProviderFn(j.CanAutoplayContext,l),d("wrapWithContextProviders").makeRenderProviderFn(j.VolumeSettingContext,C),d("wrapWithContextProviders").makeRenderProviderFn(j.AutoplayGatingResultContext,h),d("wrapWithContextProviders").makeRenderProviderFn(j.BroadcastStatusContext,i),d("wrapWithContextProviders").makeRenderProviderFn(j.LoopCountContext,Z),d("wrapWithContextProviders").makeRenderProviderFn(j.LoopCurrentContext,$),d("wrapWithContextProviders").makeRenderProviderFn(j.AdClientTokenContext,f),d("wrapWithContextProviders").makeRenderProviderFn(j.ActiveCaptionsContext,D),d("wrapWithContextProviders").makeRenderProviderFn(j.CaptionsVisibleContext,L),d("wrapWithContextProviders").makeRenderProviderFn(j.CaptionDisplayStyleContext,J),d("wrapWithContextProviders").makeRenderProviderFn(j.CaptionsLoadedContext,K),d("wrapWithContextProviders").makeRenderProviderFn(j.InbandCaptionsAutogeneratedContext,S),d("wrapWithContextProviders").makeRenderProviderFn(j.StreamInterruptedContext,ia),d("wrapWithContextProviders").makeRenderProviderFn(j.WatchTimeContext,B),d("wrapWithContextProviders").makeRenderProviderFn(j.LastPlayedTimeContext,X),d("wrapWithContextProviders").makeRenderProviderFn(j.SeekableRangesContext,ea),d("wrapWithContextProviders").makeRenderProviderFn(j.LatencyLevelContext,Y),d("wrapWithContextProviders").makeRenderProviderFn(j.IsLiveRewindActiveContext,V),d("wrapWithContextProviders").makeRenderProviderFn(j.IsLiveRewindAvailableContext,W),d("wrapWithContextProviders").makeRenderProviderFn(j.AudioAvailabilityInfoContext,g),d("wrapWithContextProviders").makeRenderProviderFn(j.IsNCSRContext,t),d("wrapWithContextProviders").makeRenderProviderFn(j.IsPremiumMusicVideoContext,u),d("wrapWithContextProviders").makeRenderProviderFn(j.InitialTracePolicyContext,o),d("wrapWithContextProviders").makeRenderProviderFn(j.ActiveEmsgBoxesContext,E),d("wrapWithContextProviders").makeRenderProviderFn(j.VideoPixelsAspectRatioContext,z!=null?z:null),function(a){return k.jsx(c("VideoPlayerViewabilityProvider.react"),{isDesktopPictureInPicture:q,isFullscreen:s,videoPlayerPassiveViewabilityInfo:A,children:a})}]);c("useBlockCappedListItemWhenPlayingVideo")(m,p);c("useDisableVirtualizationWhenPlayingVideo")(m,p);return k.jsx("div",{"data-instancekey":p,"data-testid":void 0,children:e})}a.displayName=a.name+" [from "+f.id+"]";e=k.memo(a);g["default"]=e}),98);
__d("GlobalVideoPortsID",["cometUniqueID","unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";function a(){return"id-pv-"+c("cometUniqueID")()}function b(a){if(typeof a==="string"){var b="id-pv-";if(a.indexOf(b)===0&&a.length>b.length)return a;else throw c("unrecoverableViolation")('Expected a GlobalVideoPortsVideoID, got a string that does not look like it: "'+a+'"',"comet_video_player")}else return null}function d(){return"id-pp-"+c("cometUniqueID")()}g.makeVideoID=a;g.ensureVideoID=b;g.makePlaceID=d}),98);
__d("VideoPlayerFullscreenController",["CometFullScreen","removeFromArray","unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return a.current!=null&&d("CometFullScreen").getFullScreenElement()===a.current}function a(a){var b=[];function e(){b.forEach(function(a){a()})}var f=null;return{getIsFullscreen:function(){return h(a)},requestSetIsFullscreen:function(b,e){var f=a.current;if(f==null)throw c("unrecoverableViolation")("Requested full screen while the element is not present","comet_video_player");d("CometFullScreen").isSupported()?b!==h(a)&&(b===!0?d("CometFullScreen").requestFullScreen(f):b===!1&&d("CometFullScreen").exitFullScreen()):b&&e!=null&&typeof e.webkitEnterFullScreen==="function"?e.webkitEnterFullScreen():!b&&e!=null&&typeof e.webkitExitFullScreen==="function"&&e.webkitExitFullscreen()},subscribe:function(a){b.length===0&&f==null&&(f=d("CometFullScreen").subscribeToFullScreenChangeEvent(e));b.push(a);return{remove:function(){c("removeFromArray")(b,a),b.length===0&&f!=null&&(f(),f=null)}}}}}g.createFullscreenController=a}),98);
__d("VideoPlayerPortalingPlaceState",["GlobalVideoPortsContexts","GlobalVideoPortsID","gkx","react","usePrevious"],(function(a,b,c,d,e,f,g){"use strict";var h;f=h||d("react");var i=f.useEffect,j=f.useState;function a(a,b){var e=c("usePrevious")(a);i(function(){a!==e&&(e!==null&&e&&!a&&b(d("GlobalVideoPortsID").makeVideoID()))},[a,e,b])}function b(a,b){var c=j(null),e=c[0];c=c[1];a!==e&&(e!==null&&e&&!a&&b(d("GlobalVideoPortsID").makeVideoID()),c(a))}var k=c("gkx")("24382")?b:a;function e(a){var b=a.portalingEnabled;a=a.portalingFromVideoID;var c=d("GlobalVideoPortsContexts").useGlobalVideoPortsLoader(),e=d("GlobalVideoPortsContexts").useGlobalVideoPortsManager(),f=d("GlobalVideoPortsContexts").useGlobalVideoPortsState(),g=e!=null&&f!=null,h=(g||c!=null)&&(b||a!=null),l=j(function(){return d("GlobalVideoPortsID").makePlaceID()});l=l[0];var m=j(function(){return d("GlobalVideoPortsID").makeVideoID()}),n=m[0];m=m[1];b=h&&b&&a!=null;a=b&&a!=null?a:n;k(b,m);i(function(){h&&!g&&c&&c()},[g,h,c]);return{canBecomePortableLater:h,currentVideoID:a,globalVideoPortsManager:e,globalVideoPortsState:f,thisPlaceID:l}}g.useRegenerateUniqueVideoID_exportedForTest=k;g.useVideoPlayerPortalingPlaceState=e}),98);
__d("VideoPlayerPortalingPlaceWithPortaling.react",["BaseContextualLayerAnchorRootContext","CoreVideoPlayerFitParentContainer.react","GlobalVideoPortsRenderers.react","getOwnObjectValues","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));b=h;var j=b.useContext,k=b.useEffect,l=b.useRef,m=b.useState;function a(a){var b=a.coreVideoPlayerMetaData,e=a.currentPlaceID,f=a.currentVideoID,g=a.fullscreenController,h=a.globalVideoPortsManager,n=a.globalVideoPortsState_DEPRECATED,o=a.implementations,p=a.isFullscreen,q=a.portablePlaceMetaData,r=a.previousPlaceMetaData,s=a.renderComponents,t=a.renderPlaceholder,u=a.thisPlaceID,v=a.trackingDataEncrypted,w=a.trackingNodes,x=a.viewportMarginsForViewability;a=m(function(){return d("CoreVideoPlayerFitParentContainer.react").createFitParentContainerDiv({debugRole:null})});var y=a[0],z=l(null);k(function(){h.addOrUpdatePlace({coreVideoPlayerMetaData:b,fullscreenController:g,implementations:o,injectCoreVideoStatesRef:z,isFullscreen:p,portablePlaceContainer:y,portablePlaceID:u,portablePlaceMetaData:q,portableVideoID:f,renderComponents:s,renderPlaceholder:t,trackingDataEncrypted:v,trackingNodes:w,viewportMarginsForViewability:x})},[].concat(c("getOwnObjectValues")(b),[f,g,p,h,y],c("getOwnObjectValues")(q||{}),[s,t,u,v,w,x]));k(function(){return function(){h.removePlace({portablePlaceID:u})}},[h,u]);a=j(c("BaseContextualLayerAnchorRootContext"));var A=l(null);return i.jsxs(c("BaseContextualLayerAnchorRootContext").Provider,{value:p?A:a,children:[i.jsx(d("CoreVideoPlayerFitParentContainer.react").CoreVideoPlayerFitParentDOMContainer,{debugRole:null,domElement:y},u),i.jsx(d("GlobalVideoPortsRenderers.react").GlobalVideoPortsVideoComponentsRenderer,{currentPlaceID:n!=null?(a=(a=h.getCurrentPlaceStateForVideo(n,f))==null?void 0:a.portablePlaceID)!=null?a:null:e,currentVideoID:f,injectCoreVideoStatesRef:z,previousPlaceMetaData:n!=null?(e=(a=h.getPortableVideoState(n,f))==null?void 0:a.previousPlaceMetaData)!=null?e:null:r,renderComponents:s,thisPlaceID:u}),i.jsx("div",{ref:A})]})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("VideoPlayerPortalingPlace.react",["BaseViewportMarginsContext","CometTrackingCodeContext","CometTrackingNodesContext","CoreVideoPlayer.react","CoreVideoPlayerFitParentContainer.react","GlobalVideoPortsID","GlobalVideoPortsRenderers.react","VideoPlayerFullscreenController","VideoPlayerPortalingPlaceInfoProvider.react","VideoPlayerPortalingPlaceState","VideoPlayerPortalingPlaceWithPortaling.react","react","recoverableViolation","useStable","useUnsafeRef_DEPRECATED"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));b=i;var k=b.useContext,l=b.useEffect,m=b.useState,n={bottom:0,left:0,right:0,top:0};function o(a){var b=a.ref,c=a.children,e=a.debugRole;a=a.testid;return j.jsx(d("CoreVideoPlayerFitParentContainer.react").CoreVideoPlayerFitParentContainer,{debugRole:e,ref:b,testid:void 0,children:c})}o.displayName=o.name+" [from "+f.id+"]";function a(a){var b=a.implementations,e=a.portalingEnabled,f=a.portalingFromVideoID,g=a.portalingPlaceMetaData,i=a.renderComponents,p=a.renderPlaceholder;a=babelHelpers.objectWithoutPropertiesLoose(a,["implementations","portalingEnabled","portalingFromVideoID","portalingPlaceMetaData","renderComponents","renderPlaceholder"]);l(function(){return function(){}},[]);var q=f!=null?d("GlobalVideoPortsID").ensureVideoID(f):null;f!=null&&q==null&&c("recoverableViolation")("The provided portalingFromVideoID ("+String(f)+") does not look like such an ID. The video player will not use portaling until a valid ID is provided","comet_video_player");f=d("VideoPlayerPortalingPlaceState").useVideoPlayerPortalingPlaceState({portalingEnabled:e,portalingFromVideoID:q});e=f.canBecomePortableLater;q=f.currentVideoID;var r=f.globalVideoPortsManager,s=f.globalVideoPortsState;f=f.thisPlaceID;var t=k(c("BaseViewportMarginsContext")),u=(h||(h=c("useUnsafeRef_DEPRECATED")))(null),v=c("useStable")(function(){return d("VideoPlayerFullscreenController").createFullscreenController(u)}),w=m(v.getIsFullscreen()),x=w[0],y=w[1];l(function(){var a=v.subscribe(function(){var a=v.getIsFullscreen();y(a)});y(v.getIsFullscreen());return function(){a.remove()}},[v,y]);w=k(c("CometTrackingNodesContext"));w=w.join("");var z=k(c("CometTrackingCodeContext"));z=(z=z.encrypted_tracking[0])!=null?z:"";t=x?n:t;var A="comet-video-player-place"+(a.videoFBID!=null?"-vid-"+a.videoFBID:"");if(!e)return j.jsx(o,{debugRole:null,ref:u,testid:void 0,children:j.jsx(d("VideoPlayerPortalingPlaceInfoProvider.react").VideoPlayerPortalingPlaceInfoProvider,{currentPlaceID:f,currentVideoID:q,portalingEnabled:!1,previousPlaceMetaData:null,thisPlaceID:f,children:j.jsx(c("CoreVideoPlayer.react"),babelHelpers["extends"]({},a,{fullscreenController:v,implementations:b,isFullscreen:x,renderWithCoreVideoStates:i,trackingDataEncrypted:z,trackingNodes:w,viewportMarginsForViewability:t}))})});if(r&&s){return j.jsx(o,{debugRole:null,ref:u,testid:void 0,children:j.jsx(c("VideoPlayerPortalingPlaceWithPortaling.react"),{coreVideoPlayerMetaData:a,currentPlaceID:(e=(A=r.getCurrentPlaceStateForVideo(s,q))==null?void 0:A.portablePlaceID)!=null?e:null,currentVideoID:q,fullscreenController:v,globalVideoPortsManager:r,globalVideoPortsState_DEPRECATED:null,implementations:b,isFullscreen:x,portablePlaceMetaData:g,previousPlaceMetaData:(e=(A=r.getPortableVideoState(s,q))==null?void 0:A.previousPlaceMetaData)!=null?e:null,renderComponents:i,renderPlaceholder:p,thisPlaceID:f,trackingDataEncrypted:z,trackingNodes:w,viewportMarginsForViewability:t})})}else{return j.jsx(o,{debugRole:null,ref:u,testid:void 0,children:j.jsx(d("GlobalVideoPortsRenderers.react").GlobalVideoPortsPlaceholderRenderer,{currentPlaceID:null,currentVideoID:q,previousPlaceMetaData:null,renderPlaceholder:p,thisPlaceID:f,videoPixelsAspectRatio:(b=a.videoPixelsAspectRatio)!=null?b:null})})}}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("VideoPlayerTracePolicyContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||(h=d("react"));var i=h.useContext;c={initialTracePolicy:null,routeTracePolicy:null};var j=b.createContext(c);function a(){return i(j)}g.VideoPlayerTracePolicyContext=j;g.useVideoPlayerTracePolicy=a}),98);
__d("VideoPlayerTracePolicyProvider.react",["VideoPlayerTracePolicyContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));b=h;var j=b.useEffect,k=b.useState;function a(a){var b=a.children,c=a.initialTracePolicy,e=a.routeTracePolicy;a=k(c);var f=a[0],g=a[1];a=k(e);var h=a[0],l=a[1];j(function(){f!==c&&g(c),h!==e&&l(e)},[c,e,f,h]);return i.jsx(d("VideoPlayerTracePolicyContext").VideoPlayerTracePolicyContext.Provider,{value:{initialTracePolicy:f,routeTracePolicy:h},children:b})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("CoreVideoPlayerWithComponents.react",["VideoPlayerComponentContainer.react","VideoPlayerPortalingPlace.react","VideoPlayerTracePolicyProvider.react","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useCallback;function a(a){var b=a.children,d=a.implementations,e=a.portalingEnabled,f=a.portalingFromVideoID,g=a.portalingPlaceMetaData,h=a.portalingRenderPlaceholder;a=babelHelpers.objectWithoutPropertiesLoose(a,["children","implementations","portalingEnabled","portalingFromVideoID","portalingPlaceMetaData","portalingRenderPlaceholder"]);var k=j(function(a){return i.jsx(c("VideoPlayerComponentContainer.react"),babelHelpers["extends"]({},a,{children:b}))},[b]);return i.jsx(c("VideoPlayerTracePolicyProvider.react"),{initialTracePolicy:a.initialTracePolicy,routeTracePolicy:a.routeTracePolicy,children:i.jsx(c("VideoPlayerPortalingPlace.react"),babelHelpers["extends"]({},a,{implementations:d,portalingEnabled:e,portalingFromVideoID:f,portalingPlaceMetaData:g,renderComponents:k,renderPlaceholder:h}))})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDSCheckPanoOutlineIcon.react",["IGDSSVGIconBase.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(3),e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e=i.jsx("polyline",{fill:"none",points:"22 5 9.002 17.998 2.005 11.004",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2"}),b[0]=e):e=b[0];b[1]!==a?(e=i.jsx(c("IGDSSVGIconBase.react"),babelHelpers["extends"]({},a,{viewBox:"0 0 24 24",children:e})),b[1]=a,b[2]=e):e=b[2];return e}b=i.memo(a);g["default"]=b}),98);
__d("InstagramChallengeRoutes",[],(function(a,b,c,d,e,f){"use strict";a="/challenge/action";b="/challenge/v2/action";f.TAKE_CHALLENGE_BASE_PATH=a;f.TAKE_CHALLENGE_TOA_BASE_PATH=b}),66);
__d("LiveTraceWwwVideoPlayerFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1743810");b=d("FalcoLoggerInternal").create("live_trace_www_video_player",a);e=b;g["default"]=e}),98);
__d("MediaPlaybackCompoundFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("2074");b=d("FalcoLoggerInternal").create("media_playback_compound",a);e=b;g["default"]=e}),98);
__d("MediaPlaybackCompoundEventLogger",["MediaPlaybackCompoundFalcoEvent"],(function(a,b,c,d,e,f,g){"use strict";a={logComet:function(a){c("MediaPlaybackCompoundFalcoEvent").log(function(){return a})},logCometImmediately:function(a){c("MediaPlaybackCompoundFalcoEvent").logImmediately(function(){return a})}};b=a;g["default"]=b}),98);
__d("MediaPlaybackTagMetadataHighFrequencyCategory",[],(function(a,b,c,d,e,f){a=Object.freeze({FB_BANDWIDTH:"fb_bandwidth",VIDEO_BANDWIDTH:"video_bandwidth",TOTAL_FRAME_COUNT:"total_frame_count",DROPPED_FRAME_COUNT:"dropped_frame_count",LARGE_DROPPED_FRAME_COUNT:"large_dropped_frame_count",VERY_LARGE_DROPPED_FRAME_COUNT:"very_large_dropped_frame_count",TASOS_VIDEO_TRANSPORT_BWE_BITS_PER_SEC:"tasos_video_transport_bwe_bits_per_sec",LIVE_LATENCY_FRAMES:"live_latency_frames"});f["default"]=a}),66);
__d("MediaPlaybackCompoundEventStateMachineLogger",["MediaPlaybackTagMetadataHighFrequencyCategory","NetworkStatus","emptyFunction","hashString"],(function(a,b,c,d,e,f,g){"use strict";var h=6e4,i=1e3,j=Object.freeze(["paused","completed","cancelled","error","heartbeat"]);d=b=c("emptyFunction");e=b;f=b;d=b;function k(){return{canLogPausedOrFinishedPlaying:!1,canLogPlayingEvent:!0,errorRecoveryAttemptState:{eventsLogged:0},hasPendingRequestedPlaying:!1,isLoggingBufferingSequence:!1,isLoggingScrubbingSequence:!1,lastLoggedError:null,lastLoggedTagMetadata:{},nextHeartbeatTime:null,shouldIgnoreDomPause:!1,shouldIgnoreDomPlay:!1,shouldLogRequestedPlayingForScrub:!1}}function a(a){var b={},d=a.initialLoggingMetaData;d;var e=[],f=k(),g=[],l={};function m(b){var c,f=b.events;b=b.state;var g=babelHelpers["extends"]({},d.coreVideoPlayerMetaData.loggingToSNAPLAdditionalData,{},l);g=(c=(c=a.metadataProvider)==null?void 0:c.getRequiredMetadata({logDataAdditions:g,loggingMetaData:d,state:b}))!=null?c:{current_watching_module:"",media_id:"",tracking_type:""};b={events:f,required_metadata:g};e.push(b)}function n(a){var c=a.event;a=a.state;g.push(c);if(j.includes(c.event_name)){c=[].concat(g);g=[];m({events:c,state:a})}return b}function o(a,b){var e=a.uncontrolledState.videoElementDuration;return{client_time_ms:a.uncontrolledState.clockTimestamp.toString(),event_name:b,media_time_ms:a.uncontrolledState.videoElementPlayheadPosition!=null?Math.round(a.uncontrolledState.videoElementPlayheadPosition*1e3).toString():"0",player_instance_id:Math.abs(c("hashString")(d.instanceKey)).toString(),player_instance_key:d.instanceKey,video_client_duration:e!=null?Math.trunc(e*1e3).toString():void 0}}function p(a){d;if(a.type==="notify_logging_metadata_change"){a=a.payload.loggingMetaData;d=a}}function q(a,c,d){if(a.controlledState.playbackState!==c.controlledState.playbackState&&c.controlledState.playbackState==="ended"&&f.canLogPausedOrFinishedPlaying){s(c);d=o(c,"completed");n({event:d,state:c});f.canLogPausedOrFinishedPlaying=!1;return b}else return b}function r(a,c){var d=o(a,"requested_playing");c=c!=null?babelHelpers["extends"]({},d,{media_time_ms:Math.round(c*1e3).toString()}):d;n({event:c,state:a});f.hasPendingRequestedPlaying=!0;f.canLogPausedOrFinishedPlaying=!0;return b}function s(c){var e,f=babelHelpers["extends"]({},d.coreVideoPlayerMetaData.loggingToSNAPLAdditionalData,{},l);e=(e=a.metadataProvider)==null?void 0:e.getTagMetadata({logDataAdditions:f,loggingMetaData:d,state:c});if(Object.keys(e!=null?e:{}).length>0){f=babelHelpers["extends"]({},o(c,"tags_changed"),{tag_metadata:e});n({event:f,state:c})}return b}function t(a){if(!f.canLogPausedOrFinishedPlaying)return b;else if(f.hasPendingRequestedPlaying){s(a);u(a);f.canLogPausedOrFinishedPlaying=!1;f.hasPendingRequestedPlaying=!1;return b}else{s(a);var c=o(a,"paused");n({event:c,state:a});f.canLogPausedOrFinishedPlaying=!1;f.hasPendingRequestedPlaying=!1;return b}}function u(a){var c=o(a,"cancelled");n({event:c,state:a});return b}function v(a,c,d){if(d.type==="dom_event_play_promise_rejected"&&f.hasPendingRequestedPlaying){a=d.payload.playPromiseRejectionReason;if(a!=null&&a.name==="NotAllowedError"){u(c);return b}else return b}else return b}function w(a,c,d){if((d.type==="controller_play_requested"||d.type==="dom_event_play"&&!f.shouldIgnoreDomPlay)&&a.controlledState.playbackState!==c.controlledState.playbackState){r(c);return b}else return b}function x(a){var c=o(a,"requested_seek");n({event:c,state:a});return b}function y(a,c,d){var e=c.controlledState.playbackState,g=a.controlledState.playbackState;if(d.type==="controller_scrub_begin_requested"&&!a.controlledState.scrubbing&&e!=="paused"&&e!=="ended"){t(c);x(c);f.isLoggingScrubbingSequence=!0;return b}else if(!a.controlledState.seeking&&c.controlledState.seeking&&!f.isLoggingScrubbingSequence&&e!=="paused"&&e!=="ended"&&!f.hasPendingRequestedPlaying){t(c);x(c);f.shouldLogRequestedPlayingForScrub=!0;return b}else if(d.type==="controller_scrub_end_requested"&&a.controlledState.scrubbing&&e!=="paused"&&e!=="ended"){r(c,d.payload.seekTargetPosition);return b}else if(a.controlledState.seeking&&!c.controlledState.seeking){f.shouldLogRequestedPlayingForScrub&&e!=="paused"&&e!=="ended"&&r(c);f.isLoggingScrubbingSequence=!1;f.shouldLogRequestedPlayingForScrub=!1;g!=="paused"&&g!=="ended"&&(f.canLogPlayingEvent=!0);return b}else return b}function z(a,c,d){if(a.controlledState.playbackState==="stalling"&&c.controlledState.playbackState==="playing"&&f.canLogPlayingEvent){d=o(c,"started_playing");n({event:d,state:c});f.canLogPlayingEvent=!1;f.hasPendingRequestedPlaying=!1;s(c);return b}else return b}function A(a,c,d){if((d.type==="controller_pause_requested"||d.type==="dom_event_pause"&&!f.shouldIgnoreDomPause)&&a.controlledState.playbackState!==c.controlledState.playbackState){t(c);return b}else return b}function B(a,c,d){var e=c.controlledState.playbackState;if(e!=="paused"&&e!=="ended"){d.type==="implementation_video_node_unmounted"?t(a):(d.type==="implementation_unmounted"||d.type==="implementation_engine_destroy_requested")&&t(c);return b}else return b}function C(c,d,e){c=d.controlledState.error;if(c!=null&&c!==f.lastLoggedError&&c.errorCode!=="410"){s(d);e=babelHelpers["extends"]({},(e=a.metadataProvider)==null?void 0:e.getErrorMetadata({videoPlayerError:c}),{name:"failed_playing"});e=babelHelpers["extends"]({},o(d,"error"),{error_metadata:e});n({event:e,state:d});f.lastLoggedError=c}return b}function D(d,e,g){if(g.type==="error_recovery_attempt"&&f.errorRecoveryAttemptState.eventsLogged<i){d=g.payload.recoverableError;if(d!=null&&d.errorName==="OZ_NETWORK"&&!c("NetworkStatus").isOnline())return b;g=babelHelpers["extends"]({},(g=a.metadataProvider)==null?void 0:g.getErrorMetadata({videoPlayerError:d}),{name:"error_recovery_attempt"});d=babelHelpers["extends"]({},o(e,"error"),{error_metadata:g});n({event:d,state:e});f.errorRecoveryAttemptState.eventsLogged++}return b}function E(a,c,d){d=a.controlledState.playbackState;a=c.controlledState.playbackState;if(f.hasPendingRequestedPlaying||f.shouldLogRequestedPlayingForScrub||f.isLoggingBufferingSequence)return b;if(d!=="stalling"&&a==="stalling"){d=o(c,"started_buffering");n({event:d,state:c});f.isLoggingBufferingSequence=!0;f.shouldIgnoreDomPause=!0;f.shouldIgnoreDomPlay=!0}return b}function F(a,c,d){a=a.controlledState.playbackState;var e=c.controlledState.playbackState;if(!f.isLoggingBufferingSequence)return b;if((d.type==="dom_event_playing"||d.type==="buffering_end_requested")&&a==="stalling"&&e!=="stalling"){a=0;d.payload.domEventPerfTimestamp!=null&&(a=Math.max(c.uncontrolledState.perfTimestamp-d.payload.domEventPerfTimestamp,0));e=c.uncontrolledState.clockTimestamp-a;d=babelHelpers["extends"]({},o(c,"stopped_buffering"),{client_time_ms:e.toString()});n({event:d,state:c});f.isLoggingBufferingSequence=!1;f.shouldIgnoreDomPause=!1;f.shouldIgnoreDomPlay=!1}return b}function G(a,c,d){a=c.controlledState.playbackState;a==="paused"||a==="ended"?f.nextHeartbeatTime=null:a!=="stalling"&&f.nextHeartbeatTime==null&&(f.nextHeartbeatTime=c.uncontrolledState.clockTimestamp+h);d=f.nextHeartbeatTime;if(d!=null){var e=c.uncontrolledState.clockTimestamp;if(e>=d){if(a!=="stalling"){d=o(c,"heartbeat");n({event:d,state:c})}f.nextHeartbeatTime=e+h}}return b}function H(e,g,h){e=babelHelpers["extends"]({},d.coreVideoPlayerMetaData.loggingToSNAPLAdditionalData,{},l);var i=f.lastLoggedTagMetadata,j=(h=a.metadataProvider)==null?void 0:h.getTagMetadata({logDataAdditions:e,loggingMetaData:d,state:g});if(j&&JSON.stringify(j)!==JSON.stringify(i)){var k={};Object.keys(j).forEach(function(a){if(j[a]!==i[a]){var b;k=babelHelpers["extends"]({},k,(b={},b[a]=j[a],b))}});var m=Object.values(c("MediaPlaybackTagMetadataHighFrequencyCategory"));h=Object.keys(k).every(function(a){return m.includes(a)});if(h)return b;e=babelHelpers["extends"]({},o(g,"tags_changed"),{tag_metadata:k});n({event:e,state:g});f.lastLoggedTagMetadata=babelHelpers["extends"]({},i,{},j)}return b}return{consumeLoggerEvents:function(){return e.length>0?e.splice(0):[]},handleStateMachine:function(a,b,c){p(c);var d=b.controlledState.playbackState,e=[H,v,w,y,E,F,z,q,A,B,C,D,G];e.forEach(function(d){d(a,b,c)});(d==="paused"||d==="ended")&&(f.canLogPlayingEvent=!0);c.type==="controller_pause_requested"&&(f.shouldIgnoreDomPause=!0);c.type==="controller_play_requested"&&(f.shouldIgnoreDomPlay=!0);c.type==="dom_event_pause"&&(f.shouldIgnoreDomPause=!1);c.type==="dom_event_play"&&(f.shouldIgnoreDomPlay=!1)},setLoggingToSNAPLAdditionalData:function(a){Object.assign(l,a)}}}g.HEARTBEAT_INTERVAL=h;g.createMediaPlaybackCompoundEventStateMachineLogger=a}),98);
__d("MediaPlaybackLogFlusher",["MediaPlaybackCompoundEventLogger","emptyFunction"],(function(a,b,c,d,e,f,g){"use strict";a=c("emptyFunction");b=function(){function a(a){this.$1=a}var b=a.prototype;b.flushLogs=function(){var a=this.$1.consumeLoggerEvents();a.forEach(function(a){a.required_metadata.tracking_type==="paid"?c("MediaPlaybackCompoundEventLogger").logCometImmediately(a):c("MediaPlaybackCompoundEventLogger").logComet(a)})};b.discardLogsWithoutFlushing=function(){var a=this.$1.consumeLoggerEvents()};return a}();g["default"]=b}),98);
__d("Mp4Box",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a){this.$4=a.getCursor(),this.$1=a.readUint32(),this.$2=a.readChars(4),this.$1===1?this.$1=a.readUint64():this.$1===0&&(this.$1=a.getDataView().byteLength-this.$4),this.$2==="uuid"&&(this.$3=a.readChars(16)),this.$5=a.getCursor()}var b=a.prototype;b.getBodyStart=function(){return this.$5};b.getBodySize=function(){var a=this.$5-this.$4;return this.getSize()-a};b.getSize=function(){return this.$1};b.getType=function(){return this.$2};b.getUuid=function(){return this.$3};b.getStart=function(){return this.$4};b.inspect=function(){return"{ size: "+this.$1+", type: "+this.$2+" }"};return a}();f["default"]=a}),66);
__d("Mp4DASHEventMessageBox",[],(function(a,b,c,d,e,f){a=function(){function a(a,b){this.$1=b,this.$2=null,b.getVersion()==0?this.$2={version:0,schemeIdUri:a.readZeroTerminatedString(this.$4(a,b)),value:a.readZeroTerminatedString(this.$4(a,b)),timescale:a.readUint32(),presentationTimeDelta:a.readUint32(),eventDuration:a.readUint32(),id:a.readUint32()}:b.getVersion()==1&&(this.$2={version:1,timescale:a.readUint32(),presentationTime:a.readUint64(),eventDuration:a.readUint32(),id:a.readUint32(),schemeIdUri:a.readZeroTerminatedString(this.$4(a,b)),value:a.readZeroTerminatedString(this.$4(a,b))}),this.$3=new DataView(a.getDataView().buffer,a.getCursor())}var b=a.prototype;b.getFullBox=function(){return this.$1};b.getEmsgFields=function(){return this.$2};b.getMessageData=function(){return this.$3};b.getStartTime=function(){var a=this.$2;if(a==null)return null;switch(a.version){case 0:return null;case 1:return this.$5(a)}};b.getDuration=function(){var a=this.$2;if(a==null)return null;var b=a.eventDuration;a=a.timescale;return a!==0?b/a:b};b.$5=function(a){var b=a.timescale;a=a.presentationTime;return b!==0?a/b:a};b.$4=function(a,b){return b.getBox().getSize()-(a.getCursor()-b.getBox().getStart())};return a}();a.canonicalType="emsg";f["default"]=a}),66);
__d("Mp4FullBox",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.$2=a.readUint8(),this.$1=a.readUint8(),this.$1=a.readUint8()+(this.$1<<8),this.$1=a.readUint8()+(this.$1<<8),this.$3=b}var b=a.prototype;b.getVersion=function(){return this.$2};b.getFlags=function(){return this.$1};b.getBox=function(){return this.$3};return a}();f["default"]=a}),66);
__d("Mp4Demuxer",["DataViewReader","Mp4Box","Mp4FullBox"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){this.$1=new(c("DataViewReader"))(a)}var b=a.prototype;b.parseBox=function(){return new(c("Mp4Box"))(this.$1)};b.parseFullBox=function(a){return new(c("Mp4FullBox"))(this.$1,a)};b.parseCanonicalBox=function(a,b){return new a(this.$1,b)};b.skipBox=function(a){this.$1.seek(a.getStart()+a.getSize())};b.withinBox=function(a){var b=this.$1.getCursor();return b>=a.getStart()&&b<a.getStart()+a.getSize()};b.atEnd=function(){return this.$1.getCursor()>=this.$1.getDataView().byteLength};b.reset=function(){this.$1.seek(0)};b.readBoxBodyText=function(a){this.$1.seek(a.getBodyStart());var b=new TextDecoder();a=new Uint8Array(this.$1.readBytes(a.getBodySize()));return b.decode(a)};return a}();g["default"]=a}),98);
__d("PolarisCountryCallingCodes",[],(function(a,b,c,d,e,f){"use strict";a=new Map([["AC","247"],["AD","376"],["AE","971"],["AF","93"],["AG","1"],["AI","1"],["AL","355"],["AM","374"],["AO","244"],["AR","54"],["AS","1"],["AT","43"],["AU","61"],["AW","297"],["AX","358"],["AZ","994"],["BA","387"],["BB","1"],["BD","880"],["BE","32"],["BF","226"],["BG","359"],["BH","973"],["BI","257"],["BJ","229"],["BL","590"],["BM","1"],["BN","673"],["BO","591"],["BQ","599"],["BR","55"],["BS","1"],["BT","975"],["BW","267"],["BY","375"],["BZ","501"],["CA","1"],["CC","61"],["CD","243"],["CF","236"],["CG","242"],["CH","41"],["CI","225"],["CK","682"],["CL","56"],["CM","237"],["CN","86"],["CO","57"],["CR","506"],["CU","53"],["CV","238"],["CW","599"],["CX","61"],["CY","357"],["CZ","420"],["DE","49"],["DJ","253"],["DK","45"],["DM","1"],["DO","1"],["DZ","213"],["EC","593"],["EE","372"],["EG","20"],["EH","212"],["ER","291"],["ES","34"],["ET","251"],["FI","358"],["FJ","679"],["FK","500"],["FM","691"],["FO","298"],["FR","33"],["GA","241"],["GB","44"],["GD","1"],["GE","995"],["GF","594"],["GG","44"],["GH","233"],["GI","350"],["GL","299"],["GM","220"],["GN","224"],["GP","590"],["GQ","240"],["GR","30"],["GT","502"],["GU","1"],["GW","245"],["GY","592"],["HK","852"],["HN","504"],["HR","385"],["HT","509"],["HU","36"],["ID","62"],["IE","353"],["IL","972"],["IM","44"],["IN","91"],["IO","246"],["IQ","964"],["IR","98"],["IS","354"],["IT","39"],["JE","44"],["JM","1"],["JO","962"],["JP","81"],["KE","254"],["KG","996"],["KH","855"],["KI","686"],["KM","269"],["KN","1"],["KP","850"],["KR","82"],["KW","965"],["KY","1"],["KZ","7"],["LA","856"],["LB","961"],["LC","1"],["LI","423"],["LK","94"],["LR","231"],["LS","266"],["LT","370"],["LU","352"],["LV","371"],["LY","218"],["MA","212"],["MC","377"],["MD","373"],["ME","382"],["MF","590"],["MG","261"],["MH","692"],["MK","389"],["ML","223"],["MM","95"],["MN","976"],["MO","853"],["MP","1"],["MQ","596"],["MR","222"],["MS","1"],["MT","356"],["MU","230"],["MV","960"],["MW","265"],["MX","52"],["MY","60"],["MZ","258"],["NA","264"],["NC","687"],["NE","227"],["NF","672"],["NG","234"],["NI","505"],["NL","31"],["NO","47"],["NP","977"],["NR","674"],["NU","683"],["NZ","64"],["OM","968"],["PA","507"],["PE","51"],["PF","689"],["PG","675"],["PH","63"],["PK","92"],["PL","48"],["PM","508"],["PR","1"],["PS","970"],["PT","351"],["PW","680"],["PY","595"],["QA","974"],["RE","262"],["RO","40"],["RS","381"],["RU","7"],["RW","250"],["SA","966"],["SB","677"],["SC","248"],["SD","249"],["SE","46"],["SG","65"],["SH","290"],["SI","386"],["SJ","47"],["SK","421"],["SL","232"],["SM","378"],["SN","221"],["SO","252"],["SR","597"],["SS","211"],["ST","239"],["SV","503"],["SX","1"],["SY","963"],["SZ","268"],["TA","290"],["TC","1"],["TD","235"],["TG","228"],["TH","66"],["TJ","992"],["TK","690"],["TL","670"],["TM","993"],["TN","216"],["TO","676"],["TR","90"],["TT","1"],["TV","688"],["TW","886"],["TZ","255"],["UA","380"],["UG","256"],["US","1"],["UY","598"],["UZ","998"],["VA","379"],["VC","1"],["VE","58"],["VG","1"],["VI","1"],["VN","84"],["VU","678"],["WF","681"],["WS","685"],["YE","967"],["YT","262"],["ZA","27"],["ZM","260"],["ZW","263"]]);b=a;f["default"]=b}),66);
__d("PolarisCountryCodeUtils",["PolarisConfig","PolarisCountryCallingCodes","memoize"],(function(a,b,c,d,e,f,g){"use strict";e=c("memoize")(function(){var a=d("PolarisConfig").getCountryCode()==="internal"?"US":d("PolarisConfig").getCountryCode(),b=c("PolarisCountryCallingCodes").get(a!=null?a:"");return a!=null&&b!=null?{code:a,phoneCode:b}:null});function a(a){var b=c("PolarisCountryCallingCodes").get(a||"");return a!=null&&b!=null?{code:a,phoneCode:b}:{code:"US",phoneCode:"1"}}function b(a){return a.code+" +"+a.phoneCode}g.getInitialCountryCode=e;g.countryCodeFromCode=a;g.stringFromCountryCode=b}),98);
__d("PolarisIGCorePhoneNumberInput.react",["cx","fbt","IGCoreModal.react","IGDSBox.react","IGDSDivider.react","IGDSText.react","ISOCountryCode","PolarisAssetManagerGlyphMapping","PolarisGenericStrings","PolarisIGCoreBox","PolarisIGCoreButton.react","PolarisIGCoreIconButton.react","PolarisIGCoreModalHeader.react","PolarisIGCoreTextInput.react","PolarisIGVirtualList.react","memoizeWithArgs","react"],(function(a,b,c,d,e,f,g,h,i){"use strict";var j,k=j||d("react"),l=c("memoizeWithArgs")(function(a,b,d){return Array.from(b.entries()).filter(function(b){var e=b[0];b[1];b=d[c("ISOCountryCode")[e]];if(!b)return!1;if(a==="")return!0;if(e.toUpperCase().includes(a))return!0;return b.toUpperCase().includes(a)?!0:!1})},function(a){return a}),m=k.memo(function(a){return k.jsx(c("PolarisIGCoreBox"),{dangerouslySetClassName:a.isFirst?void 0:{__className:"_aa89"},height:n,marginEnd:4,marginStart:4,children:k.jsx("button",{className:"_aa8a",onClick:a.onChangeCountryCode,type:"button",children:k.jsxs(c("IGDSBox.react"),{alignItems:"center",direction:"row",justifyContent:"between",position:"relative",children:[k.jsx(c("IGDSText.react"),{children:a.countryName}),k.jsx(c("IGDSText.react"),{color:"secondaryText",children:"+"+a.phoneCode})]})})})}),n=54;a=function(a){babelHelpers.inheritsLoose(b,a);function b(){var b,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(b=c=a.call.apply(a,[this].concat(e))||this,c.state={filterText:"",showCountryCodeSelector:!1},c.$1=function(a){var b=c.props.onBlur;b&&b(a)},c.$2=function(a){var b=c.props.onFocus;b&&b(a)},c.$3=function(a){var b=c.props.onFocusCountryCode;b&&b(a)},c.onCloseModal=function(){c.setState({showCountryCodeSelector:!1})},b)||babelHelpers.assertThisInitialized(c)}var e=b.prototype;e.displayPhoneCode=function(a){return a==null?"":a.code+" +"+a.phoneCode};e.getListOption=function(a){var b=this;return function(d){d=d.index;var e=a[d],f=e[0],g=e[1];return k.jsx(m,{countryCodeValue:f,countryName:b.props.countryNamesMap[c("ISOCountryCode")[f]],isFirst:d===0,onChangeCountryCode:function(){b.props.onChangeCountryCode({code:f,phoneCode:g}),b.onCloseModal()},phoneCode:g},f)}};e.render=function(){var a=this,b=this.props,e=b["aria-describedby"],f=b["aria-label"],g=b["aria-labelledby"],h=b.backgroundColor,j=b.disabled,m=b.endAdornment,o=b.hasError,p=b.maxLength,q=b.onChange,r=b.onKeyDown,s=b.placeholder,t=b.readOnly,u=b.required,v=b.style;b=b.value;var w=k.jsx("div",{className:"_aa8b",children:k.jsx(c("PolarisIGCoreButton.react"),{borderless:!0,color:"ig-secondary-button",onClick:function(){return a.setState({showCountryCodeSelector:!0})},children:k.jsx(c("IGDSText.react"),{color:"primaryButton",weight:"semibold",zeroMargin:!0,children:this.displayPhoneCode(this.props.countryCode)})})}),x=l(this.state.filterText.toUpperCase(),this.props.countryCodesMap,this.props.countryNamesMap),y=i._(/*BTDS*/"Select Country");return k.jsxs(k.Fragment,{children:[this.state.showCountryCodeSelector&&k.jsxs(c("IGCoreModal.react"),{"aria-label":y,dangerouslySetClassName:{__className:"_aa8c"},onClose:this.onCloseModal,size:"large",children:[" ",k.jsx(c("PolarisIGCoreModalHeader.react"),{onClose:this.onCloseModal,children:y}),k.jsxs(c("IGDSBox.react"),{position:"relative",children:[k.jsx(c("IGDSBox.react"),{marginEnd:2,marginStart:2,position:"relative",children:k.jsx(c("PolarisIGCoreTextInput.react"),{backgroundColor:"transparent",name:"filter",onChange:function(b){return a.setState({filterText:b.target.value})},onFocus:this.$3,placeholder:i._(/*BTDS*/"Country name or code"),startAdornment:k.jsx(c("PolarisIGCoreIconButton.react"),{alt:d("PolarisGenericStrings").SEARCH_TEXT,icon:d("PolarisAssetManagerGlyphMapping").ICONS.SEARCH_OUTLINE_24_GREY9}),style:"borderless",type:"search",value:this.state.filterText})}),k.jsx(c("IGDSDivider.react"),{}),k.jsx(c("PolarisIGVirtualList.react"),{className:"_aa8d",containerSize:"auto",estimatedItemSize:n,itemCount:x.length,renderer:this.getListOption(x)})]})]}),k.jsx(c("PolarisIGCoreTextInput.react"),{"aria-describedby":e,"aria-label":f,"aria-labelledby":g,autoComplete:"tel",backgroundColor:h,dangerouslySetClassName:{__className:"_aa8e"},disabled:j,endAdornment:m,hasError:o,maxLength:p,name:"phone",onBlur:this.$1,onChange:q,onFocus:this.$2,onKeyDown:r,placeholder:s,readOnly:t,required:u,startAdornment:w,style:v,type:"tel",value:b})]})};return b}(k.Component);g["default"]=a}),226);
__d("PolarisPhoneCommonStrings",["fbt","react"],(function(a,b,c,d,e,f,g,h){"use strict";var i;i||d("react");c=h._(/*BTDS*/"Add phone number");e=h._(/*BTDS*/"You may receive SMS updates from Instagram and can opt out at anytime.");f=h._(/*BTDS*/"Phone number");d=h._(/*BTDS*/"Adding your number will help you log in more easily, recover your account, and find people to follow.");var j=h._(/*BTDS*/"Change it"),k=h._(/*BTDS*/"Didn't enter the right phone number?"),l=h._(/*BTDS*/"Done"),m=h._(/*BTDS*/"Enter confirmation code"),n=h._(/*BTDS*/"Confirmation Code");function a(a){return h._(/*BTDS*/"Enter the confirmation code we sent to {phoneNumber}.",[h._param("phoneNumber",a)])}function b(a){return h._(/*BTDS*/"If you didn't get it, we can {resendCode}.",[h._param("resendCode",a)])}g.ADD_PHONE_NUMBER_LABEL=c;g.PHONE_NUMBER_CAPTION=e;g.PHONE_NUMBER_LABEL=f;g.PHONE_NUMBER_VALUE_PROP=d;g.CONFIRMATION_CODE_CHANGE_CODE=j;g.CONFIRMATION_CODE_CHANGE_CODE_QUESTION=k;g.CONFIRMATION_CODE_DONE=l;g.ENTER_CONFIRMATION_CODE_LABEL=m;g.CONFIRMATION_CODE_TITLE=n;g.confirmationCodeBodyText=a;g.confirmationCodeResendCode=b}),226);
__d("PolarisPhoneNumberCountryCodeInput.react",["CountryNames","PolarisCountryCallingCodes","PolarisIGCorePhoneNumberInput.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(2),e;b[0]!==a?(e=i.jsx(c("PolarisIGCorePhoneNumberInput.react"),babelHelpers["extends"]({countryCodesMap:c("PolarisCountryCallingCodes"),countryNamesMap:c("CountryNames")},a)),b[0]=a,b[1]=e):e=b[1];return e}g["default"]=a}),98);
__d("PolarisVideoHelpers",["PolarisMediaTypes","PolarisSizing"],(function(a,b,c,d,e,f,g){"use strict";function a(a){if(a.dashInfo==null)return null;a=a.dashInfo;var b=a.is_dash_eligible,c=a.number_of_qualities;a=a.video_dash_manifest;return!b||a==null?null:{isDashEligible:b,numberOfQualities:c,videoDashManifest:a}}function h(a,b){return(a=a.videoResources)==null?void 0:a.find(function(a){return a.type===b})}function b(a){var b;return(b=(b=h(a,d("PolarisMediaTypes").MediaVersionType.VIDEO_480_HIGH))==null?void 0:b.src)!=null?b:(b=h(a,d("PolarisMediaTypes").MediaVersionType.VIDEO_480_LOW))==null?void 0:b.src}function c(a){return(a=h(a,d("PolarisMediaTypes").MediaVersionType.VIDEO_640_HIGH))==null?void 0:a.src}function e(a,b){return{autoplayGatingResult:"unknown",autoplaySetting:a===!0?"default_autoplay":"off",canAutoplay:a===!0||b===!0?"allow":"deny"}}function f(a,b){if(a==null)return 100;a=d("PolarisSizing").getHeightPercent(a);return b?Math.min(a,d("PolarisSizing").CAPPED_HEIGHT_PERCENT):a}g.getDashInfoFromPost=a;g.getSDVideoSrcFromPost=b;g.getHDVideoSrcFromPost=c;g.getVideoPlayerAutoplayProps=e;g.getVideoPaddingPercentFromDimensions=f}),98);
__d("VideoPlayerErrorBoundary.react",["FBLogger","getErrorSafe","oz-player/utils/OzErrorUtils","react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=function(a){babelHelpers.inheritsLoose(b,a);function b(){var b,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(b=c=a.call.apply(a,[this].concat(e))||this,c.state={error:null},c.suppressReactDefaultErrorLoggingIUnderstandThisWillMakeBugsHarderToFindAndFix=!0,b)||babelHelpers.assertThisInitialized(c)}b.getDerivedStateFromError=function(a){return{error:c("getErrorSafe")(a)}};var e=b.prototype;e.componentDidCatch=function(a,b){b=b.componentStack;var e=c("getErrorSafe")(a);e.componentStack=b;b=this.props;var f=b.description,g=b.onError;b=b.project;a=d("oz-player/utils/OzErrorUtils").isOzError(a)?a.getType():a!=null&&typeof a.name==="string"?a.name:e.name;e.name=a;a="VideoPlayerErrorBoundary caught an "+a;f!=null&&(a=a+" ("+f+")");f=["OZ_NETWORK","BUFFERING_TIMEOUT","MEDIA_ERR_DECODE","OZ_NETWORK_REQUEST_STREAM_RETRY_HANDLER_ERROR"];var h=["Network failure:","Network failure.","HTTP error.","Bad URL timestamp","URL signature expired","No license for com.widevine.alpha","OZ_DRM_MANAGER: Endpoint returned error: DEVICE_CERTIFICATE_REVOKED"];f=f.every(function(a){return e.name!==a})&&h.every(function(a){return!e.message.startsWith(a)});h=c("FBLogger")(b==null?"comet_video_player":b).catching(e);["OZ_DRM_MANAGER","OZ_JAVASCRIPT_NATIVE","OZ_INITIALIZATION"].includes(e.name)&&(h=h.addToCategoryKey(e.message));f?h.fatal(a):h.warn(a);g!=null&&g(e)};e.render=function(){var a=this.props,b=a.children;a=a.fallback;var c=this.state.error;return c?typeof a==="function"?a(c):a:b};return b}(a.PureComponent);g["default"]=b}),98);
__d("VideoPlayerSurface.react",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");function a(a){a=a.children;return a!=null?a:null}a.displayName=a.name+" [from "+f.id+"]";c=b.memo(a);g["default"]=c}),98);
__d("VideoPlayerCaptionsAreaDeferred.react",["deferredLoadComponent","requireDeferredForDisplay"],(function(a,b,c,d,e,f,g){"use strict";a=c("deferredLoadComponent")(c("requireDeferredForDisplay")("VideoPlayerCaptionsArea.react").__setRef("VideoPlayerCaptionsAreaDeferred.react"));g["default"]=a}),98);
__d("VideoPlayerIMFMetadataContext",["react","unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||(h=d("react"));var i=h.useContext,j={imfExpectedFromEmsg:!1,specInlineJson:null},k=b.createContext(j);k.displayName="VideoPlayerIMFMetadataContext";e=k.Provider;function a(){var a=i(k);if(a===j)throw c("unrecoverableViolation")("useVideoPlayerIMFMetadata is not called from a component nested under VideoPlayerRelay/VideoPlayerX.","comet_video_player");return a}g.VideoPlayerIMFMetadataContextProvider=e;g.useVideoPlayerIMFMetadataFromContext=a}),98);
__d("WebSessionExtender",["WebSession","clearInterval","cr:13141","cr:913","setInterval"],(function(a,b,c,d,e,f,g){"use strict";var h=2e4,i=new Set(),j=null;function a(a,e){e===void 0&&(e="extender"),i.add(a),j==null&&(d("WebSession").extend(Date.now()+h+2e3),b("cr:13141")==null?void 0:b("cr:13141").extend(),j=c("setInterval")(function(){d("WebSession").extend(Date.now()+h+2e3),b("cr:13141")==null?void 0:b("cr:13141").extend(),b("cr:913")&&new(b("cr:913"))().setClientTime(Date.now()).setWebsessionID(d("WebSession").getId()).setReason(e).log()},h))}function e(a){i["delete"](a);a=i.size;a===0&&j!=null&&(c("clearInterval")(j),j=null)}g.subscribe=a;g.unsubscribe=e}),98);
__d("VideoPlayerWebSessionExtender.react",["VideoPlayerHooks","WebSessionExtender","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i;b=h||d("react");var j=b.useEffect,k=b.useId;function a(){var a=(i||(i=d("VideoPlayerHooks"))).usePlaying(),b=k();j(function(){if(a){d("WebSessionExtender").subscribe(b,"video");return function(){d("WebSessionExtender").unsubscribe(b)}}},[a,b]);return null}g["default"]=a}),98);
__d("useVideoPlayerDefaultLoadingIndicatorsLogic",["VideoPlayerHooks"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(){var a=(h||(h=d("VideoPlayerHooks"))).useStalling(),b=h.useStreamInterrupted(),c=h.useIsLiveRewindActive();b=b&&!c;c=a;a=b?"live_video_interrupted_overlay":c?"spinner":"none";return{liveVideoInterruptedOverlayVisible:b,loadingIndicatorType:a,spinnerVisible:c}}g["default"]=a}),98);
__d("useVideoPlayerDefaultLoadingIndicators",["CometPlaceholder.react","deferredLoadComponent","once","react","requireDeferred","requireDeferredForDisplay","useVideoPlayerDefaultLoadingIndicatorsLogic"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("deferredLoadComponent")(c("requireDeferredForDisplay")("VideoPlayerSpinner.react").__setRef("useVideoPlayerDefaultLoadingIndicators")),k=c("deferredLoadComponent")(c("requireDeferred")("VideoPlayerWithLiveVideoInterruptedOverlay.react").__setRef("useVideoPlayerDefaultLoadingIndicators")),l=c("once")(function(){return i.jsx(c("CometPlaceholder.react"),{fallback:null,children:i.jsx(k,{})})});function a(){var a=c("useVideoPlayerDefaultLoadingIndicatorsLogic")(),b=a.loadingIndicatorType;a=a.spinnerVisible;a=i.jsx(j,{isVisible:a});var d=null;switch(b){case"spinner":d=a;break;case"live_video_interrupted_overlay":d=l();break;case"none":default:d=a}return{loadingIndicatorElement:d,loadingIndicatorType:b}}g["default"]=a}),98);
__d("XVideoUnifiedCVCControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/video/unified_cvc/",Object.freeze({}),void 0);b=a;g["default"]=b}),98);
__d("UnifiedVideoCVCSubscription",["CVCv3DisabledPlayerOrigins","CVCv3DisabledPlayerSubOrigins","CVCv3SubscriptionHelper","DateConsts","XVideoUnifiedCVCControllerRouteBuilder","clearTimeout","cometAsyncFetch","promiseDone","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h=Object.values(c("CVCv3DisabledPlayerOrigins")),i=Object.values(c("CVCv3DisabledPlayerSubOrigins")),j=10;a=function(){function a(a,b,d,e,f,g){var j=this;this.$1=new(c("CVCv3SubscriptionHelper"))(a,b,d);this.$5=f;this.$6=null;this.$9=null;this.$10=!this.$1.isValidSubscription();this.$3=null;this.$2=null;a=b!=null?h.includes(b):!1;f=d!=null?i.includes(d):!1;!a&&!f&&(this.$3=e,this.$11=g,this.$4=e.subscribe(function(){if(j.$3==null){j.$1.logDebugInfo("empty_video_controller");return}var a=j.$3.getCurrentState();a.playing?j.$12(a):j.$13()}))}var b=a.prototype;b.$12=function(a){if(this.$3==null){this.$1.logDebugInfo("empty_video_controller");return}if(a.playing){if(this.$2==null){a=this.$3.getPlayheadPosition();a>=0&&(this.$2=a)}}else this.$2=null;this.$14(0)};b.stopUnifiedCVC=function(){this.$13()};b.destroy=function(){this.$13(),this.$4!=null&&this.$4.remove(),this.$4=null,this.$3=null};b.$13=function(){c("clearTimeout")(this.$8),c("clearTimeout")(this.$7),this.$8=null,this.$7=null,this.$2=null,this.$1.clearAnyPreviousContext(),this.$9=null};b.$15=function(){c("clearTimeout")(this.$7),this.$7=null};b.$16=function(){this.$9=null,this.$15(),this.$14(0)};b.$14=function(a){var b=this;if(this.$3==null||this.$8!=null||this.$9!=null||this.$10)return;this.$8=c("setTimeout")(function(){b.$8=null;var a=b.$17();if(a==null){b.$1.logDebugInfo("empty_request");return}b.$9=a;var e=Date.now(),f=!1;c("promiseDone")(a,function(c){if(a!==b.$9)return;b.$9=null;if(c!=null){c=b.$1.processUnifiedResponse(c);b.$18(c,e)}else b.$1.logHttpResponseBad("null payload",Date.now()-e)},function(a){f=!0,b.$1.logHttpRequestFailure(a!=null?JSON.stringify(a):null,Date.now()-e)});b.$7=c("setTimeout")(function(){f||b.$1.logHttpRequestTimeout(Date.now()-e),b.$16()},j*d("DateConsts").MS_PER_SEC)},a)};b.$18=function(a,b){this.$15();b=Date.now()-b;a.d!=null?(this.$1.logHttpRequestSuccess(b),this.$11!=null&&this.$11(a.d)):this.$1.logHttpResponseBad("no data field",b);if(a.a!=null){b=a.a.t;switch(b){case"p":b=a.a.pi;b==null&&(b=j);this.$14(b*d("DateConsts").MS_PER_SEC);break;case"s":this.$10=!0;break}}};b.$17=function(){var a=this.$19();if(a==null)return null;a={d:JSON.stringify(a)};return c("cometAsyncFetch")(c("XVideoUnifiedCVCControllerRouteBuilder").buildURL({}),{data:a,method:"POST"})};b.$19=function(){if(this.$3==null)return null;var a={};this.$6!=null&&(a.lc=this.$6);this.$5&&(a.ls=!0);var b=0,c=0;this.$2!=null&&(b=this.$2,c=this.$3.getPlayheadPosition());var d=this.$3.getCurrentState();b=this.$1.makeUnifiedVideoCVCUpdate(b,c,this.$20(d),d.muted,a);return b};b.$20=function(a){if(a.playing||a.seeking)return"playing";else if(a.ended)return"ended";else if(a.paused)return"paused";else return"unknown"};b.testing_setLastStartPosition=function(a){this.$2=a};b.testing_makeUnifiedStateUpdate=function(){return this.$19()};b.testing_handleUnifiedResponse=function(a){return this.$18(a,Date.now())};return a}();g["default"]=a}),98);
__d("useVideoPlayerUnifiedCVCProvider",["CvcV3HttpEventFalcoEvent","UnifiedVideoCVCSubscription","VideoPlayerHooks","createVideoStateHook","getPlayerFormatForLogData","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(h||d("react")).useEffect;b=d("createVideoStateHook").createVideoStateHook(null);var k=b.setterHook;e=d("createVideoStateHook").createVideoStateHook(null);var l=e.setterHook;f=e.valueHook;function a(a){var b=a.disableSubscription,e=a.playerFormat,f=a.subOrigin,g=a.videoFBID,h=(i||(i=d("VideoPlayerHooks"))).useIsLive(),m=i.useIsFullscreen(),n=i.useIsDesktopPictureInPicture(),o=i.useController(),p=k(),q=l();j(function(){if(b===!0){c("CvcV3HttpEventFalcoEvent").log(function(){return{countable_id:g,name:"disable_subscription"}});return}var a=new(c("UnifiedVideoCVCSubscription"))(g,c("getPlayerFormatForLogData")({isDesktopPictureInPicture:n,isFullscreen:m},e),f,o,h,function(a){q(a)});p(a);return function(){a.destroy()}},[n,m,h,b,e,q,p,f,o,g])}b=f;g.useVideoPlayerUnifiedCVCProvider=a;g.useVideoPlayerUnifiedCVCData=b}),98);
__d("VideoPlayerXImplSurface.react",["CoreVideoPlayerAudioClient.react","CoreVideoPlayerAutoplayClient.react","VideoPlayerCaptionsAreaDeferred.react","VideoPlayerIMFMetadataContext","VideoPlayerInteractionOverlay.react","VideoPlayerWebSessionExtender.react","cr:1954434","cr:99","gkx","react","useVideoPlayerDefaultLoadingIndicators","useVideoPlayerUnifiedCVCProvider"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var e=a.children,f=a.disableCVCSubscription,g=a.disableLoadingIndicator;g=g===void 0?!1:g;var h=a.disableLogging,j=a.instreamVideoAdBreaksPlayer,k=a.playerFormat,l=a.subOrigin,m=a.videoFBID,n=a.videoPlayerIMFFromVideoMetadata;a=a.videoPlayerSpherical;d("useVideoPlayerUnifiedCVCProvider").useVideoPlayerUnifiedCVCProvider({disableSubscription:h===!0||f===!0,playerFormat:k,subOrigin:l,videoFBID:m});h=c("useVideoPlayerDefaultLoadingIndicators")();f=h.loadingIndicatorElement;return i.jsxs(d("VideoPlayerIMFMetadataContext").VideoPlayerIMFMetadataContextProvider,{value:n!=null?n:null,children:[a,g?null:f,c("gkx")("24328")?i.jsx(d("VideoPlayerInteractionOverlay.react").VideoPlayerInteractionOverlay,{}):null,i.jsxs(c("VideoPlayerCaptionsAreaDeferred.react"),{children:[e,j]}),i.jsx(c("CoreVideoPlayerAutoplayClient.react"),{}),i.jsx(c("CoreVideoPlayerAudioClient.react"),{}),b("cr:1954434")?i.jsx(b("cr:1954434"),{}):null,b("cr:99")?i.jsx(b("cr:99"),{subOrigin:l}):null,i.jsx(c("VideoPlayerWebSessionExtender.react"),{})]})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("normalizeVideoPlayerLoopCount",["unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,d){b===void 0&&(b=null);d===void 0&&(d=null);if(a==null){var e;d=((e=b)!=null?e:!1)?(e=d)!=null?e:0:0;d===-1?e=-1:d<0||!Number.isFinite(d)||Math.floor(d)!==d?e=0:d===0&&b===!0?e=-1:e=d}else if(a===-1||a===Number.POSITIVE_INFINITY)e=-1;else if(a<0||!Number.isFinite(a)||Math.floor(a)!==a)throw c("unrecoverableViolation")("Invalid loopingCount: "+a,"comet_video_player");else e=a;return e}g["default"]=a}),98);
__d("VideoPlayerXImpl.react",["CoreVideoPlayerWithComponents.react","VideoPlayerXImplSurface.react","normalizeVideoPlayerLoopCount","react","usePlayerOriginRouteTracePolicy","useRouteProductAttribution"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=a.children,d=a.disableLoadingIndicator,e=a.implementations,f=a.instreamVideoAdBreaksPlayer,g=a.portalingEnabled,h=a.portalingFromVideoID,j=a.portalingPlaceMetaData,k=a.portalingRenderPlaceholder,l=a.videoPlayerIMFFromVideoMetadata,m=a.videoPlayerSpherical;a=babelHelpers.objectWithoutPropertiesLoose(a,["children","disableLoadingIndicator","implementations","instreamVideoAdBreaksPlayer","portalingEnabled","portalingFromVideoID","portalingPlaceMetaData","portalingRenderPlaceholder","videoPlayerIMFFromVideoMetadata","videoPlayerSpherical"]);var n=c("usePlayerOriginRouteTracePolicy")(),o=c("useRouteProductAttribution")();o||(o=a.productAttribution);var p=a.loopCount,q=a.playerFormat,r=a.subOrigin,s=a.videoFBID;p=c("normalizeVideoPlayerLoopCount")(p);return i.jsx(c("CoreVideoPlayerWithComponents.react"),babelHelpers["extends"]({},a,{implementations:e,loopCount:p,portalingEnabled:g,portalingFromVideoID:h,portalingPlaceMetaData:j,portalingRenderPlaceholder:k,productAttribution:o,routeTracePolicy:n,children:i.jsx(c("VideoPlayerXImplSurface.react"),{children:b,disableCVCSubscription:a.loggingConfig.disableCVCSubscription,disableLoadingIndicator:d,disableLogging:a.loggingConfig.disableLogging,instreamVideoAdBreaksPlayer:f,playerFormat:q,subOrigin:r,videoFBID:s,videoPlayerIMFFromVideoMetadata:l,videoPlayerSpherical:m})}))}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("VideoPlayerFallbackLearnMoreLink.react",["fbt","CometLink.react","FDSText.react","gkx","react"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react");function a(){var a=c("gkx")("20836")?"/help/work/1876956335887765/i-cant-view-or-play-videos-on-workplace":"https://www.facebook.com/help/396404120401278/list";return j.jsx(c("FDSText.react"),{color:"primaryOnMedia",type:"headlineEmphasized3",children:j.jsx(c("CometLink.react"),{href:a,target:"_blank",children:h._(/*BTDS*/"Learn more")})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("VideoPlayerFallbackCoverImplWithoutRetry.react",["FDSText.react","VideoPlayerFallbackLearnMoreLink.react","cr:1672302","cr:4149","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var d=a.background,e=a.debugError,f=a.message,g=a.showDebugWithoutError;g=g===void 0?!0:g;a=a.showLearnMoreLink;a=a===void 0?!0:a;return i.jsxs("div",babelHelpers["extends"]({className:"x6s0dn4 xatbrnm x9f619 x78zum5 x5yr21d xl56j7k x6ikm8r x10wlt62 x889kno x2vl965 x1a8lsjc xe2zdcy x1n2onr6 xh8yej3"},{children:[i.jsxs("div",babelHelpers["extends"]({},{0:{className:"x6s0dn4 x78zum5 xdt5ytf x3es6ox"},1:{className:"x6s0dn4 x78zum5 xdt5ytf x3es6ox x1vjfegm"}}[!!(d!=null)<<0],{children:[i.jsx("div",babelHelpers["extends"]({className:"x6s0dn4 x78zum5 xdt5ytf x193iq5w"},{children:i.jsx(c("FDSText.react"),{align:"center",color:"primaryOnMedia",type:"bodyLink3",children:f})})),a&&i.jsx("div",babelHelpers["extends"]({className:"x6s0dn4 x78zum5 xdt5ytf xw7yly9 x193iq5w"},{children:i.jsx(c("VideoPlayerFallbackLearnMoreLink.react"),{})})),(e!=null||g)&&i.jsxs(i.Fragment,{children:[b("cr:4149")?i.jsx(b("cr:4149"),{error:e}):null,b("cr:1672302")?i.jsx(b("cr:1672302"),{error:e}):null]})]})),d!=null&&i.jsx("div",babelHelpers["extends"]({className:"x1ey2m1c xtijo5x x1o0tod x10l6tqk x13vifvy"},{children:d}))]}))}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("VideoPlayerFallbackCover.react",["fbt","VideoPlayerFallbackCoverImplWithoutRetry.react","react"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react");function a(a){var b=a.background,d=a.debugError,e=a.message,f=a.showDebugWithoutError;a=a.showLearnMoreLink;e=e!=null?e:h._(/*BTDS*/"Sorry, we're having trouble playing this video.");return j.jsx(c("VideoPlayerFallbackCoverImplWithoutRetry.react"),{background:b,debugError:d,message:e,showDebugWithoutError:f,showLearnMoreLink:a})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("getVideoPlayerUserFacingErrorMessageFromError",["fbt","gkx"],(function(a,b,c,d,e,f,g,h){"use strict";function i(a){return c("gkx")("24345")&&a.message.toLowerCase().indexOf("audio_renderer_error")>=0}function j(a){return a.message.indexOf("DEVICE_CERTIFICATE_REVOKED")>=0}function k(a){return a.name.indexOf("VideoImplementationsDashManifestUnsupportedCodecs")>=0||a.name.indexOf("VideoImplementationsMediaSourceUnsupported")>=0}function l(a){return a.name==="OZ_NETWORK"&&a.message.includes("CDN URL expired")}function a(a){var b=null;i(a)?b=h._(/*BTDS*/"Audio renderer error: Please restart your computer."):j(a)?b=h._(/*BTDS*/"This video is DRM-protected, but your device certificate appears to be revoked. Please make sure your browser is up to date, and try again."):k(a)?b=h._(/*BTDS*/"This video cannot be played in the browser, operating system, or hardware you're using."):l(a)&&(b=h._(/*BTDS*/"This video content isn't available right now."));return b}g["default"]=a}),226);
__d("defaultErrorBoundaryFallback",["VideoPlayerFallbackCover.react","getVideoPlayerUserFacingErrorMessageFromError","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=c("getVideoPlayerUserFacingErrorMessageFromError")(a);return i.jsx(c("VideoPlayerFallbackCover.react"),{debugError:a,message:b})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("VideoPlayerX.react",["VideoPlayerErrorBoundary.react","VideoPlayerXImpl.react","defaultErrorBoundaryFallback","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=a.doNotRenderErrorBoundaryIUnderstandIMustProvideMyOwn;b=b===void 0?!1:b;var d=a.errorBoundaryFallback;a=babelHelpers.objectWithoutPropertiesLoose(a,["doNotRenderErrorBoundaryIUnderstandIMustProvideMyOwn","errorBoundaryFallback"]);a=i.jsx(c("VideoPlayerXImpl.react"),babelHelpers["extends"]({VideoPlayerShakaPerformanceLoggerClass:null},a));return b?a:i.jsx(c("VideoPlayerErrorBoundary.react"),{description:"VideoPlayerX",fallback:d!=null?d:c("defaultErrorBoundaryFallback"),children:a})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("computeAspectRatio",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){return a!=null&&b!=null&&a!==0&&b!==0?a/b:null}f["default"]=a}),66);
__d("usePolarisLOXLogExposureViaGraphQL",["CometRelay","fetchPolarisLoggedOutExperiment","promiseDone","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useEffect,j=b.useRef;function a(a,b){var e=d("react-compiler-runtime").c(4),f=d("CometRelay").useRelayEnvironment(),g=j(!1),h;e[0]!==b||e[1]!==f||e[2]!==a?(h=function(){!g.current&&a&&c("promiseDone")(c("fetchPolarisLoggedOutExperiment")(f,b)["finally"](function(){g.current=!0}))},e[0]=b,e[1]=f,e[2]=a,e[3]=h):h=e[3];i(h,void 0)}g["default"]=a}),98);
__d("usePolarisGetDashInfo",["PolarisConfig","PolarisRoutePropUtils","PolarisVideoUA","qex","react","react-compiler-runtime","usePolarisLOXLogExposureViaGraphQL"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function j(a){return a==null?!1:d("PolarisVideoUA").doesPlatformSupportDash()}function a(a){var b=d("react-compiler-runtime").c(5),e;b[0]!==a?(e=j(a),b[0]=a,b[1]=e):e=b[1];e=e;var f=i(d("PolarisRoutePropUtils").PolarisRoutePropContext);f=f==null?void 0:f.routePropQE.getBool("is_ios_video_dash_enabled");var g;b[2]!==e?(g=d("PolarisConfig").isLoggedOutUserMobile()&&e,b[2]=e,b[3]=g):g=b[3];var h;b[4]===Symbol["for"]("react.memo_cache_sentinel")?(h={name:"ig_mweb_dash_video_playback_universe",param:"is_dash_disabled"},b[4]=h):h=b[4];c("usePolarisLOXLogExposureViaGraphQL")(g,h);if(!e)return null;if(d("PolarisConfig").isLoggedOutUserMobile()&&d("PolarisVideoUA").isIOSMMSESupportedBrowser()&&!f)return null;return d("PolarisConfig").isLoggedOutUserMobile()&&((b=c("qex")._("4664"))!=null?b:!1)?null:a}g["default"]=a}),98);
__d("VideoPlayerProgressiveImplementationData",["err"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return/\.mpd(\?|$)/.test(a)}function a(a){var b=a.hdSrc,d=a.hdSrcPreferred;a=a.sdSrc;if(b==null&&a==null){var e=c("err")("Both HD and SD browser-native URLs are not available");e.name="GotNoBrowserNativeURLs";return e}if(a==null){e=c("err")("Browser-native SD URL is not available");e.name="GotNoBrowserNativeSDURL";return e}e=h(a);var f=b!=null&&h(b);if(e||f){e=c("err")("HD or SD browser-native URL points to a DASH manifest");e.name="GotBrowserNativeURLsPointToDASH";return e}return{hdSrc:b,hdSrcPreferred:d,sdSrc:a}}g.makeProgressiveImplementationData=a}),98);
__d("VideoLiveTrace",["DataViewReader","LiveTraceWwwVideoPlayerFalcoEvent","Mp4DASHEventMessageBox","Mp4Demuxer","throttle"],(function(a,b,c,d,e,f,g){var h="x-fb-video-livetrace-ids",i="x-fb-video-livetrace-parentsource",j="x-fb-video-livetrace-streamtype",k="x-fb-origin-hit",l="x-fb-edge-hit",m="PLY:WWW:",n=m+"DL:",o=m+"DIS:",p=1e3,q=/[\r\n]+/;a=function(){function a(a,b,d){var e=this;this.$6=[];this.$1=a;this.$2=null;a=d+":"+b.substring(0,5);this.$3=m+a;this.$4=n+a;this.$5=o+a;this.$7=c("throttle")(function(a){return e.$8(a)},p)}var b=a.prototype;b.setStreamType=function(a){this.$2=a};b.$9=function(a,b,d,e,f,g){var h,i=this,j=Date.now(),k=(h=this.$2)!=null?h:0;c("LiveTraceWwwVideoPlayerFalcoEvent").log(function(){return{stream_id:i.$1,stream_type:k,event_name:b,event_severity:f,event_creation_time:j,source:a,trace_id:d,parent_source:e,metadata:g}})};b.onUpdateStatus=function(a){this.$7(a)};b.$8=function(a){a=a.position*1e3;for(var b=this.$6.length-1;b>=0;b--){var c=this.$6[b];if(c.presentationTimestamp>a)continue;if(c.displayTimestamp==null)c.displayTimestamp=Date.now();else continue;this.$9(this.$5,"FRAME",c.traceId,this.$4,"SUCCESS",null)}};b.getAndFlushTracedFrames=function(){var a,b={currentTimeMs:Date.now(),streamId:this.$1},c={dl:[],dis:[]},d=[];this.$6.forEach(function(a){a.hasBeenFlushedAsDownloaded||(c.dl.push({id:a.traceId,timeMs:a.downloadTimestamp}),a.hasBeenFlushedAsDownloaded=!0),a.displayTimestamp!=null?c.dis.push({id:a.traceId,timeMs:a.displayTimestamp}):d.push(a)});this.$6=d;b[(a=this.$2)!=null?a:0]=c;return c.dl.length>0||c.dis.length>0?b:null};b.handleHeadersString=function(a,b){a=a.trim().split(q);this.$10(a.map(function(a){a=a.split(": ");return[a.shift().toLowerCase(),a.shift()]}),b)};b.handleHeaders=function(a,b){this.$10(this.$11(a),b)};b.handleHeadersAndBody=function(a,b,c){this.$12(this.$11(a),b,c)};b.$11=function(a){var b=[];for(a of a.entries())b.push(a);return b};b.$13=function(a){var b=Date.now(),d=new Map(),e=a.reduce(function(a,b){return a+b.byteLength},0),f=new Uint8Array(e),g=0;a.forEach(function(a){f.set(a,g),g+=a.byteLength});e=new(c("Mp4Demuxer"))(new DataView(f.buffer,f.byteOffset,f.byteLength));while(!e.atEnd()){a=e.parseBox();if(a.getType()===c("Mp4DASHEventMessageBox").canonicalType){var h=e.parseCanonicalBox(c("Mp4DASHEventMessageBox"),e.parseFullBox(a));if(h instanceof c("Mp4DASHEventMessageBox")){var i;i=(i=h.getEmsgFields())==null?void 0:i.schemeIdUri;if(i==null?void 0:i.startsWith("livedash:trace:")){i=h.getMessageData();h=new(c("DataViewReader"))(i).readZeroTerminatedString(i.byteLength);try{i=JSON.parse(h);Array.isArray(i)&&i.filter(function(a){return Array.isArray(a)&&a.length===2}).forEach(function(a){var c=a[0];a=a[1];d.set(c,{displayTimestamp:null,downloadTimestamp:b,hasBeenFlushedAsDownloaded:!1,presentationTimestamp:a,traceId:c})})}catch(a){}}}}e.skipBox(a)}return d};b.$14=function(a,b){var c=this,d="null",e=Date.now(),f=new Map(),g="";a.forEach(function(a){var b=a[0].toLowerCase();a=a[1];if(b===h&&a){var m=a.split(",");m.forEach(function(a){a=a.split(":");var b=+a[0];a=+a[1];f.set(b,{displayTimestamp:null,downloadTimestamp:e,hasBeenFlushedAsDownloaded:!1,presentationTimestamp:a,traceId:b})})}b===i&&(g=a);c.$2===null&&b===j&&(c.$2=parseInt(a,10));(b===k||b===l)&&parseInt(a,10)&&(d=b===k?"origin":"edge")});a=b||{};a.hit=d;return g!==""?{tracedFrames:f,eventMetaData:a,parentSource:g,streamType:this.$2}:null};b.$12=function(a,b,c){var d=this.$14(a,c);if(d==null||d.parentSource==="")return;if(b==null?void 0:b.length){a=this.$13(b);a.forEach(function(a,b){d.tracedFrames.set(b,a)})}this.$6=this.$6.concat(Array.from(d.tracedFrames.values()));c=d.tracedFrames.keys();for(b of c)this.$9(this.$4,"SEGMENT",b,d.parentSource,"SUCCESS",d.eventMetaData)};b.$10=function(a,b){this.$12(a,null,b)};b.handleXHR=function(a,b){this.handleHeadersString(a.getAllResponseHeaders(),b)};b.getLiveTraceContext=function(){return this.$2!=null?{streamId:this.$1,streamType:this.$2,sourceId:this.$3}:null};return a}();g["default"]=a}),98);
__d("VideoPlayerWwwFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("1914651");b=d("FalcoLoggerInternal").create("video_player_www",a);e=b;g["default"]=e}),98);
__d("VideoPlayerWwwLogger",["VideoPlayerWwwFalcoEvent"],(function(a,b,c,d,e,f,g){"use strict";function h(a){var b,c=a.logData.live_trace_stream_id!==null&&a.logData.live_trace_stream_type!==null;return{access_token:a.logData.access_token,ad_client_token:a.logData.ad_client_token,attribution_id:a.logData.attribution_id,attribution_id_v2:a.logData.attribution_id_v2,attribution_id_v2_root:a.logData.attribution_id_v2_root,audio_only:a.logData.audio_only,audio_representation_id:a.logData.audio_representation_id,autoplay_eligible:a.logData.autoplay_eligible,autoplay_failure_reasons:a.logData.autoplay_failure_reasons,autoplay_setting:a.logData.autoplay_setting,available_qualities:a.logData.available_qualities,broadcaster_origin:a.logData.broadcaster_origin,browser_tab_id:a.logData.browser_tab_id,caption_state:a.logData.caption_state,cast_client_app_id:a.logData.cast_client_app_id,client_latency_setting:a.logData.client_latency_setting,current_playback_speed:a.logData.current_playback_speed,current_viewability_percentage:Number(a.logData.current_viewability_percentage),current_volume:a.logData.current_volume,dash_perf_logging_enabled:a.logData.dash_perf_logging_enabled,data_connection_quality:a.logData.data_connection_quality,debug_reason:a.logData.debug_reason,device_id:a.logData.device_id,device_type:a.logData.device_type,downstream_share_origin_uri:a.logData.downstream_share_origin_uri,downstream_share_start_time:a.logData.downstream_share_start_time,downstream_share_visit_uuid:a.logData.downstream_share_visit_uuid,dropped_frame_count:Number(a.logData.dropped_frame_count),error:a.logData.error,error_code:a.logData.error_code,error_description:a.logData.error_description,error_domain:a.logData.error_domain,error_type:a.logData.error_type,error_user_info:a.logData.error_user_info,event_name:a.eventType,event_seq_num:a.logData.event_seq_num,external_log_id:a.logData.external_log_id,external_log_type:a.logData.external_log_id,fb_manifest_identifier:a.logData.fb_manifest_identifier,feed_aggregation_type:a.logData.feed_aggregation_type,feed_position:a.logData.feed_position,frame_events:a.logData.frame_events,ft:a.logData.ft,imf_emsg_id:a.logData.imf_emsg_id,imf_event_type:a.logData.imf_event_type,imf_topic:a.logData.imf_topic,interrupt_count:a.logData.interrupt_count!==null?Number(a.logData.interrupt_count):null,interrupt_time:Number(a.logData.interrupt_time),is_abr_enabled:a.logData.is_abr_enabled,is_fbms:a.logData.is_fbms,is_live_preview:(b=a.logData.is_live_preview)!=null?b:!1,is_live_video_rewound:a.logData.is_live_video_rewound,is_mixed_codec_manifest:a.logData.is_mixed_codec_manifest,is_p2p_playback:a.logData.is_p2p_playback,is_pip:a.logData.is_pip,is_predictive_playback:a.logData.is_predictive_playback,is_sound_on:a.logData.is_sound_on,is_stalling:a.logData.is_stalling,is_templated_manifest:a.logData.is_templated_manifest,last_viewability_percentage:Number(a.logData.last_viewability_percentage),live_trace_source_id:a.logData.live_trace_source_id,live_trace_stream_id:c?a.logData.live_trace_stream_id:null,live_trace_stream_type:c?a.logData.live_trace_stream_type:null,mpd_validation_errors:a.logData.mpd_validation_errors,network_connected:a.logData.network_connected,next_quality_label:a.logData.next_quality_label,next_representation_id:a.logData.next_representation_id,next_video_codecs:a.logData.next_video_codecs,notification_id:a.logData.notification_id,notification_medium:a.logData.notification_medium,offset_ms:a.logData.offset_ms!=null?String(a.logData.offset_ms):null,permalink_share_id:a.logData.permalink_share_id,playback_caption_format:a.logData.playback_caption_format,playback_caption_locale:a.logData.playback_caption_locale,playback_duration:a.logData.playback_duration,playback_is_broadcast:a.logData.playback_is_broadcast,playback_is_drm:a.logData.playback_is_drm,playback_is_live_streaming:a.logData.playback_is_live_streaming,player_format:a.logData.player_format,player_instance_key:a.logData.player_instance_key,player_mode:a.logData.player_mode,player_origin:a.logData.player_origin,player_state:a.logData.player_state,player_suborigin:a.logData.player_suborigin,player_version:a.logData.player_version,projection:a.logData.projection,quality_label:a.logData.quality_label,reaction_video_channel_type:a.logData.reaction_video_channel_type,representation_id:a.logData.representation_id,resource_url:a.logData.resource_url,routeTracePolicy:a.routeTracePolicy,seq_num:a.logData.seq_num,source:a.source_VPL_LOGGING_HACK,source_VPL_LOGGING_HACK:a.logData.source_VPL_LOGGING_HACK,srt_job_id:a.logData.srt_job_id,srt_job_tracking_id:a.logData.srt_job_tracking_id,stall_count:Number(a.logData.stall_count),stall_count_200_ms:Number(a.logData.stall_count_200_ms),stall_time:Number(a.logData.stall_time),state:a.logData.state,streaming_format:a.logData.streaming_format,time_ms:a.logData.time_ms,total_frame_count:Number(a.logData.total_frame_count),tracking_data_encrypted:a.logData.tracking_data_encrypted,tracking_nodes:a.logData.tracking_nodes,tv_session_id:a.logData.tv_session_id,v2_heart_beat:a.logData.v2_heart_beat,video_bandwidth:a.logData.video_bandwidth,video_buffer_end_position:a.logData.video_buffer_end_position,video_chaining_depth_level:a.logData.video_chaining_depth_level,video_chaining_parent_video_id:a.logData.video_chaining_parent_video_id,video_chaining_session_id:a.logData.video_chaining_session_id,video_channel_id:a.logData.video_channel_id,video_codecs:a.logData.video_codecs,video_id:a.logData.video_id,video_last_start_time_position:a.logData.video_last_start_time_position,video_play_reason:a.logData.video_play_reason,video_player_height:Number(a.logData.video_player_height),video_player_width:Number(a.logData.video_player_width),video_time_position:a.logData.video_time_position,vpts:a.logData.vpts,web_client_revision:a.logData.web_client_revision}}a={logComet:function(a){c("VideoPlayerWwwFalcoEvent").log(function(){return h(a)})},logCometImmediately:function(a){c("VideoPlayerWwwFalcoEvent").logImmediately(function(){return h(a)})}};b=a;g["default"]=b}),98);
__d("VideoPlayerBanzaiLogFlusher",["VideoPlayerWwwLogger","emptyFunction","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h=c("emptyFunction"),i=!1;c("gkx")("5551")&&(i=!0,h=function(a,b){try{var c;for(var d=arguments.length,e=new Array(d>2?d-2:0),f=2;f<d;f++)e[f-2]=arguments[f];(c=window.console).info.apply(c,["["+a+"][VideoPlayerBanzaiLogFlusher]"+b].concat(e))}catch(a){}});a=function(){function a(a,b){this.$1=a,this.$2=b}var b=a.prototype;b.flushLogs=function(){var a=this,b=this.$2.consumeLoggerEvents();b.forEach(function(b){var d,e={event:b.eventType,logData:b.logData,routeTracePolicy:b.routeTracePolicy,source:b.source_VPL_LOGGING_HACK};if(b.source_VPL_LOGGING_HACK==="animated_image_share"){i&&h(a.$1,"[flushLogs] "+b.eventType+" SKIP ANIMATED_IMAGE_SHARE",{videoPlayerLogData:e});return}d=(d=b.logData)==null?void 0:d.ad_client_token;i&&h(a.$1,"[flushLogs] "+b.eventType,{videoPlayerLogData:e});d!=null?c("VideoPlayerWwwLogger").logCometImmediately(b):c("VideoPlayerWwwLogger").logComet(b)})};b.discardLogsWithoutFlushing=function(){var a=this.$2.consumeLoggerEvents();i&&h(this.$1,"[discardLogsWithoutFlushing] "+a.map(function(a){return a.eventType}).join(","),a)};return a}();g["default"]=a}),98);
__d("VideoPlayerCaptionsController",["JSResourceForInteraction","recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h=c("JSResourceForInteraction")("VideoPlayerHTML5ApiCea608State").__setRef("VideoPlayerCaptionsController"),i=c("JSResourceForInteraction")("VideoPlayerHTML5ApiWebVttState").__setRef("VideoPlayerCaptionsController");function a(a){var b=a.captionsUrl,d=a.inbandCaptionsExpected,e=a.onCaptionsLoaded,f=null,g=null,j=null,k=null;function l(a){var b=i.load().then(function(c){if(b!==j)return;f=new c({captionsDisplay:null,onCaptionsLoaded:e,onReady:function(b){b.loadFromUrl(a)}})})["catch"](function(a){if(b!==j)return;c("recoverableViolation")("Failed to load the VideoPlayerHTML5ApiWebVttState module: "+a.message,"comet_video_player")});return b}function m(){var a=h.load().then(function(b){if(a!==k)return;g=new b({captionsDisplay:null,onCaptionsLoaded:e,onReady:function(a){a.processQueue()}})})["catch"](function(b){if(a!==k)return;c("recoverableViolation")("Failed to load the VideoPlayerHTML5ApiCea608State module: "+b.message,"comet_video_player")});return a}b!=null?j=l(b):d===!0&&(k=m());return{destroy:function(){f&&(f.destroy(),f=null),g&&(g.destroy(),g=null),j&&(j=null),k&&(k=null)},getCaptionFormat:function(){return f?"webvtt":g?"cea608":null},handleCea608BytesReceived:function(a){g&&g.enqueueBytes(a)},handleTimeUpdate:function(a){if(f){var b;f.source&&f.source.handleTimeUpdate(a);return(b=(b=f)==null?void 0:b.getCurrentScreenRepresentation())!=null?b:null}else if(g){g.source&&g.source.handleTimeUpdate(a);return(a=(b=g)==null?void 0:b.getCurrentScreenRepresentation())!=null?a:null}return null},updateCaptionsUrl:function(a){f&&(f.destroy(),f=null),a!=null&&(j=l(a))},updateInbandCaptionsExpected:function(a){g&&(g.destroy(),g=null),a&&(k=m())}}}g.createCaptionsController=a}),98);
__d("VideoPlayerImplementationReactVideoElement.react",["react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function j(a){a=a!=null?a:[];var b=a[0];a=a[1];if(b==null&&a==null)return null;var c="";if(typeof b==="string")c=b;else if(Array.isArray(b)){var d=b[0];b=b[1];c=d+" "+b}d="";if(typeof a==="string")d=a;else if(Array.isArray(a)){b=a[0];a=a[1];d=b+" "+a}return c+" "+d}function a(a){var b=a.alt,c=a.poster,d=a.seoSrc,e=a.seoWebCrawlerVideoTracks,f=a.videoElementCallbacks,g=a.videoElementPreloadDisabled,h=a.videoElementRefCallback;a=a.videoPixelsFit;d=d!=null?babelHelpers["extends"]({},f,{src:d}):f;f=j(a==null?void 0:a.objectPosition);return i.jsx("video",babelHelpers["extends"]({"aria-label":b!=null?b:void 0,className:"x1lliihq x5yr21d xh8yej3",controls:!1,"data-testid":void 0,muted:!0,playsInline:!0,preload:g===!0?"none":void 0},d,{poster:c,ref:h,style:a?{objectFit:a.objectFit,objectPosition:f}:void 0,children:e}))}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("VideoPlayerEmsgForStateMachine",["deepEquals"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b){var c;return[a.type,a.emsgScheme,(c=a.emsgId)!=null?c:""].concat(b?[(c=a.emsgStartTimeInSec)!=null?c:"",(b=a.emsgDurationInSec)!=null?b:""]:[]).join(":")}function i(a,b){a.set(h(b,!0),b);return a}function a(a,b){return b.reduce(i,new Map(a))}function b(a,b,d){a=b==null?[]:Array.from(a.values()).filter(function(a){var c=a.emsgStartTimeInSec;a=a.emsgDurationInSec||0;return c!=null&&b>=c&&b<c+a}).sort(function(a,b){return(a.emsgStartTimeInSec||0)-(b.emsgStartTimeInSec||0)});a=Array.from(a.reduce(function(a,b){a.set(h(b,!1),b);return a},new Map()).values());return c("deepEquals")(d,a)?d:a}g.makeEmsgBoxDedupKey=h;g.makeUpdatedAllEmsgBoxes=a;g.makeUpdatedActiveEmsgBoxes=b}),98);
__d("getErrorMessageFromMediaErrorCode",[],(function(a,b,c,d,e,f){"use strict";function a(a){switch(a){case 1:return"The fetching process for the media resource was aborted by the user agent at the users request.";case 2:return"A network error of some description caused the user agent to stop fetching the media resource, after the resource was established to be usable.";case 3:return"An error of some description occurred while decoding the media resource, after the resource was established to be usable.";case 4:return"The media resource indicated by the src attribute was not suitable."}return null}f["default"]=a}),66);
__d("VideoPlayerImplementationErrors",["VideoPlayerImplementationErrorNormalization","getErrorMessageFromMediaErrorCode","getErrorNameFromMediaErrorCode","getErrorSafe","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";function h(a){var b;a=/^([A-Z_]+:)|([A-Z_]+\s+\(0x[0-9A-Fa-f]+\))/.exec(a);a=((b=(b=a==null?void 0:a[1])!=null?b:a==null?void 0:a[2])!=null?b:"").replace(/\s+/,"_").replace(/[^A-Za-z0-9_]/g,"");return a!==""?a:null}function a(a){var b=a.errorLocation,d=a.hostCallError,e=a.videoElementError,f=a.videoElementNetworkState;a=a.videoElementReadyState;var g=e==null?void 0:e.code,h=d!=null&&typeof d.message==="string"?d.message:null;h==null&&(h=e==null?void 0:e.message);h==null&&g!=null&&(h=c("getErrorMessageFromMediaErrorCode")(g));e=c("getErrorNameFromMediaErrorCode")(g);return{createdTimestamp:Date.now(),errorLocation:b,errorMessageFormat:((b=h)!=null?b:"Empty error")+("; code: "+(g!=null?g:"undefined"))+("; readyState: "+a)+("; networkState: "+f),errorMessageParams:[],errorName:e,originalError:d}}function b(a,b){var d=c("getErrorSafe")(a);return{createdTimestamp:Date.now(),errorCode:typeof a==="object"&&a!=null?typeof a.code==="string"?a.code:typeof a.code==="number"?String(a.code):void 0:void 0,errorLocation:b,errorMessageFormat:(a=d.messageFormat)!=null?a:d.message,errorMessageParams:d.messageFormat!=null?(b=d.messageParams)!=null?b:[]:[],errorName:"VideoElementPlayPromiseRejectionReason_"+(d.name||"UnknownError")}}function i(a,b,d,e){e===void 0&&(e=null);b=c("getErrorSafe")(b);return{createdTimestamp:Date.now(),errorCode:null,errorLocation:d,errorMessageFormat:(b.name||"UnknownError")+" "+((d=b.messageFormat)!=null?d:b.message),errorMessageParams:b.messageFormat!=null?(d=b.messageParams)!=null?d:[]:[],errorName:a,stack:b.stack,url:(d=e)!=null?d:null}}function e(a,b){if(d("oz-player/utils/OzErrorUtils").isOzError(a)){var c=a,e=c.getExtra();return{createdTimestamp:Date.now(),errorCode:e.code,errorLocation:b,errorMessageFormat:c.getDescription(),errorMessageParams:[],errorName:c.getType(),errorType:d("VideoPlayerImplementationErrorNormalization").getVideoPlayerNormalizedErrorTypeFromOzError(c),stack:c.stack,url:e.url}}else return i("OZ_JAVASCRIPT_NATIVE",a,b)}function f(a,b){var c=h(a.message);c=c!=null?a.name+"_"+c:a.name;a=a.message;var d=[];return{createdTimestamp:Date.now(),errorLocation:b,errorMessageFormat:a,errorMessageParams:d,errorName:c,errorType:"GenericDecodeError"}}g.getMoreGranularErrorNameFromHTMLVideoElementErrorMessage=h;g.createVideoPlayerErrorFromHTMLVideoElementError=a;g.createVideoPlayerErrorFromVideoElementPlayPromiseRejectionReason=b;g.createVideoPlayerErrorFromGenericError=i;g.createVideoPlayerErrorFromOzImplementationError=e;g.createVideoPlayerErrorFromOzImplementationVideoNodeError=f}),98);
__d("VideoPlayerODS",["ODS","Random"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(a,b,c){d("Random").coinflip(c)&&(h||(h=d("ODS"))).bumpEntityKey(2079,a,b,c)};g.bumpEntityKey=a}),98);
__d("VideoPlayerImplementationStateMachine",["VideoPlayerEmsgForStateMachine","VideoPlayerImplementationErrors","VideoPlayerODS","gkx","recoverableViolation","shallowEqual","unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";function h(a){var b=a.bufferingDetected,c=a.seeking;a=a.waitingForDomPlaying;return!b&&!c&&!a}function i(a){var b=a.bufferingDetected,d=a.prevPlaybackState,e=a.seeking;a=a.waitingForDomPlaying;b=h({bufferingDetected:b,seeking:e,waitingForDomPlaying:a});e=d;switch(d){case"stalling":e=b?"playing":"stalling";break;case"ended":case"paused":case"playing":break;default:d;c("recoverableViolation")("unexpected playbackState: "+d,"comet_video_player");break}return e}function j(a){return a.loopCount>0&&a.loopCurrent<a.loopCount||a.loopCount===-1}function k(a,b,e){switch(e.type){case"host_call_pause":case"host_call_play":case"host_call_set_current_time":case"host_call_set_volume":case"host_call_set_muted":case"host_call_set_playback_rate":case"host_call_set_video_quality":case"host_call_set_latency_level":return babelHelpers["extends"]({},a,{hostCallQueue:a.hostCallQueue.concat([e])});case"controller_set_latency_level_requested":case"implementation_set_latency_level_requested":return babelHelpers["extends"]({},a,{latencyLevel:e.payload.latencyLevel,ullIneligibilityReason:e.payload.ullIneligibilityReason});case"dom_event_loadedmetadata":return babelHelpers["extends"]({},a,{domEventsLatestPerfMs:babelHelpers["extends"]({},a.domEventsLatestPerfMs,{clockMs:b.clockTimestamp,loadedmetadata:e.payload.domEventPerfTimestamp,perfMs:b.perfTimestamp})});case"dom_event_loadeddata":return babelHelpers["extends"]({},a,{domEventsLatestPerfMs:babelHelpers["extends"]({},a.domEventsLatestPerfMs,{clockMs:b.clockTimestamp,loadeddata:e.payload.domEventPerfTimestamp,perfMs:b.perfTimestamp})});case"dom_event_canplay":return babelHelpers["extends"]({},a,{domEventsLatestPerfMs:babelHelpers["extends"]({},a.domEventsLatestPerfMs,{canplay:e.payload.domEventPerfTimestamp,clockMs:b.clockTimestamp,perfMs:b.perfTimestamp})});case"dom_event_ended":return babelHelpers["extends"]({},a,{playbackState:"ended",waitingForDomPlaying:!1});case"dom_event_pause":if(a.playbackState==="ended")return a;if(b.videoElementEnded===!0)return babelHelpers["extends"]({},a,{playbackState:"ended",waitingForDomPlaying:!1});return a.playbackState==="paused"?a:babelHelpers["extends"]({},a,{playbackState:"paused",waitingForDomPlaying:!1});case"dom_event_play":var f=a.playbackState,g=f;switch(f){case"playing":case"stalling":case"ended":case"paused":g="stalling";break;default:f;c("recoverableViolation")("unexpected playbackState: "+f,"comet_video_player");break}f=!0;return babelHelpers["extends"]({},a,{playbackState:g,waitingForDomPlaying:f});case"dom_event_playing":g=a.bufferingDetected;f=a.seeking;var h=!1,k=a.playbackState;return babelHelpers["extends"]({},a,{playbackState:i({bufferingDetected:g,prevPlaybackState:k,seeking:f,waitingForDomPlaying:h}),waitingForDomPlaying:h});case"dom_event_timeupdate":g=d("VideoPlayerEmsgForStateMachine").makeUpdatedActiveEmsgBoxes(a.allEmsgBoxes,b.videoElementPlayheadPosition,a.activeEmsgBoxes);if(a.waitingForDomTimeUpdateAfterSeeked){k=a.bufferingDetected;f=a.seeking;h=!1;var l=a.playbackState;return babelHelpers["extends"]({},a,{activeEmsgBoxes:g,playbackState:i({bufferingDetected:k,prevPlaybackState:l,seeking:f,waitingForDomPlaying:h}),waitingForDomPlaying:h,waitingForDomTimeUpdateAfterSeeked:!1})}return babelHelpers["extends"]({},a,{activeEmsgBoxes:g});case"dom_event_seeking":k=a.playbackState;l=k;switch(k){case"paused":case"ended":break;case"stalling":case"playing":l="stalling";break;default:k,c("recoverableViolation")("unexpected playbackState: "+k,"comet_video_player")}return babelHelpers["extends"]({},a,{playbackState:l,seeking:!0,waitingForDomPlaying:!0});case"dom_event_seeked":f=a.bufferingDetected;h=a.waitingForDomPlaying;g=!1;k=a.playbackState;return babelHelpers["extends"]({},a,{implementationSeekSourcePosition:null,playbackState:i({bufferingDetected:f,prevPlaybackState:k,seeking:g,waitingForDomPlaying:h}),seeking:g,waitingForDomTimeUpdateAfterSeeked:!0});case"dom_event_error":e.payload.videoElementError==null&&d("VideoPlayerODS").bumpEntityKey("comet_video_player","dom_event_error.error_is_nullish",14);return babelHelpers["extends"]({},a,{playbackState:"paused",waitingForDomPlaying:!1});case"dom_event_play_promise_created":return babelHelpers["extends"]({},a,{hostCallPlayIDLast:e.payload.hostCallPlayID});case"dom_event_play_promise_resolved":return a.hostCallPlayIDLast!==e.payload.hostCallPlayID?a:babelHelpers["extends"]({},a,{hostCallPlayIDLast:null});case"dom_event_play_promise_rejected":if(a.hostCallPlayIDLast!==e.payload.hostCallPlayID)return a;if(c("gkx")("30214")){l=e.payload.playPromiseRejectionReason;return babelHelpers["extends"]({},a,{hostCallPlayIDLast:null},!(l!=null&&l.name==="AbortError")&&a.playbackState==="stalling"?{playbackState:"paused",waitingForDomPlaying:!1}:{})}return babelHelpers["extends"]({},a,{hostCallPlayIDLast:null,playbackState:a.playbackState==="stalling"?"paused":a.playbackState,waitingForDomPlaying:!1});case"dom_event_durationchange":if(c("gkx")("30214")){f=b.videoElementEnded===!0?"ended":b.videoElementPaused===!0?"paused":a.playbackState;if((a.playbackState==="stalling"||a.playbackState==="playing")&&f!==a.playbackState)return babelHelpers["extends"]({},a,{playbackState:f,waitingForDomPlaying:!1})}return a;case"implementation_host_call_queue_flushed":return babelHelpers["extends"]({},a,{hostCallQueue:[]});case"implementation_host_call_failed":k=d("VideoPlayerImplementationErrors").createVideoPlayerErrorFromHTMLVideoElementError({errorLocation:e.payload.errorLocation,hostCallError:e.payload.hostCallError,videoElementError:e.payload.videoElementError,videoElementNetworkState:e.payload.videoElementNetworkState,videoElementReadyState:e.payload.videoElementReadyState});return babelHelpers["extends"]({},a,{error:k,playbackState:"paused",waitingForDomPlaying:!1});case"implementation_mounted":return babelHelpers["extends"]({},a,{mountState:"mounted_onscreen"});case"implementation_unmounted":return babelHelpers["extends"]({},a,{mountState:"unmounted"});case"implementation_onscreen":return babelHelpers["extends"]({},a,{mountState:"mounted_onscreen"});case"implementation_offscreen":return babelHelpers["extends"]({},a,{mountState:"mounted_offscreen"});case"implementation_video_node_unmounted":return babelHelpers["extends"]({},a,{hostCallPlayIDLast:null});case"implementation_engine_initialized":g=(h=e.payload.streamingFormat)!=null?h:a.streamingFormat;return babelHelpers["extends"]({},a,{selectedVideoQuality:e.payload.selectedVideoQuality,streamingFormat:g});case"implementation_engine_qualities_changed":f=(l=e.payload.streamingFormat)!=null?l:a.streamingFormat;return babelHelpers["extends"]({},a,{selectedVideoQuality:e.payload.selectedVideoQuality,streamingFormat:f});case"implementation_engine_destroyed":return babelHelpers["extends"]({},a,{hostCallPlayIDLast:null});case"implementation_fatal_error":return babelHelpers["extends"]({},a,{error:e.payload.fatalError,hostCallPlayIDLast:null,playbackState:"paused",waitingForDomPlaying:!1});case"implementation_seek_requested":return babelHelpers["extends"]({},a,{implementationSeekSourcePosition:e.payload.seekSourcePosition});case"representation_changed":return babelHelpers["extends"]({},a);case"controller_pause_requested":return a.playbackState==="ended"?a:babelHelpers["extends"]({},a,{lastPausedTimeMs:Date.now(),lastPauseReason:e.payload.reason,lastPlayedTimeMs:0,playbackState:"paused",waitingForDomPlaying:!1,watchTimeMs:a.lastPlayedTimeMs>0?a.watchTimeMs+(Date.now()-a.lastPlayedTimeMs):a.watchTimeMs});case"controller_play_requested":return a.playbackState!=="paused"&&a.playbackState!=="ended"?a:babelHelpers["extends"]({},a,{hasPlayEverBeenRequested:!0,lastPlayedTimeMs:a.lastPlayedTimeMs===0?Date.now():a.lastPlayedTimeMs,lastPlayReason:e.payload.reason,loopCurrent:j(a)?e.payload.reason==="loop_initiated"?a.loopCurrent+1:a.loopCurrent:0,playbackState:"stalling",waitingForDomPlaying:!0});case"controller_seek_requested":return babelHelpers["extends"]({},a,{implementationSeekSourcePosition:(k=b.videoElementPlayheadPosition)!=null?k:a.implementationSeekSourcePosition,seeking:!0,seekTargetPosition:e.payload.seekTargetPosition});case"controller_quality_requested":return babelHelpers["extends"]({},a,{selectedVideoQuality:e.payload.selectedVideoQuality});case"controller_video_variant_requested":return babelHelpers["extends"]({},a,{selectedVideoVariant:e.payload.selectedVideoVariant});case"controller_set_caption_display_style_requested":return babelHelpers["extends"]({},a,{captionDisplayStyle:e.payload.captionDisplayStyle});case"controller_set_picture_in_picture_state_requested":return babelHelpers["extends"]({},a,{isDesktopPictureInPicture:e.payload.isInPictureInPictureMode});case"controller_set_caption_format_requested":return babelHelpers["extends"]({},a,{captionFormat:e.payload.captionFormat});case"controller_set_playback_rate":return babelHelpers["extends"]({},a,{targetPlaybackRate:e.payload.playbackRate});case"controller_muted_requested":return babelHelpers["extends"]({},a,{lastMuteReason:e.payload.reason,muted:e.payload.muted});case"controller_volume_requested":return babelHelpers["extends"]({},a,{volume:e.payload.volume});case"controller_scrub_begin_requested":return a.scrubbing?a:babelHelpers["extends"]({},a,{scrubbing:!0,seekTargetPosition:null});case"controller_scrub_end_requested":return!a.scrubbing?a:babelHelpers["extends"]({},a,{implementationSeekSourcePosition:(h=b.videoElementPlayheadPosition)!=null?h:a.implementationSeekSourcePosition,scrubbing:!1,seeking:e.payload.seekTargetPosition!=null,seekTargetPosition:e.payload.seekTargetPosition});case"buffering_begin_requested":g=a.playbackState;l=g;switch(g){case"paused":case"ended":break;case"playing":case"stalling":l="stalling";break;default:g;c("recoverableViolation")("unexpected playbackState: "+g,"comet_video_player");break}f=e.payload.bufferingType;return babelHelpers["extends"]({},a,{bufferingDetected:!0,lastBufferingType:f,playbackState:l});case"buffering_end_requested":k=a.seeking;h=a.waitingForDomPlaying;g=!1;f=a.playbackState;return babelHelpers["extends"]({},a,{bufferingDetected:g,playbackState:i({bufferingDetected:g,prevPlaybackState:f,seeking:k,waitingForDomPlaying:h})});case"controller_set_captions_visible_requested":l=e.payload.captionsVisible;return a.captionsVisible===l?a:babelHelpers["extends"]({},a,{activeCaptions:l?a.activeCaptions:null,captionsLocale:l?a.captionsLocale:null,captionsVisible:l});case"controller_set_active_captions_requested":g=e.payload.activeCaptions;k=(f=g==null?void 0:g.rows)!=null?f:[];h=e.payload.captionsLocale;l=a.activeCaptions;var m=(f=l==null?void 0:l.rows)!=null?f:[];return m.length===k.length&&k.every(function(a,b){return m[b]===a})?a:babelHelpers["extends"]({},a,{activeCaptions:g,captionsLocale:h});case"captions_loaded":return babelHelpers["extends"]({},a,{activeCaptions:a.activeCaptions,captionsLoaded:!0});case"captions_unloaded":return babelHelpers["extends"]({},a,{activeCaptions:null,captionsLoaded:!1,captionsLocale:null});case"inband_captions_autogenerated_changed":l=e.payload.inbandCaptionsAutogenerated;return a.inbandCaptionsAutogenerated===l?a:babelHelpers["extends"]({},a,{inbandCaptionsAutogenerated:l});case"stream_ended":return babelHelpers["extends"]({},a,{streamEnded:!0});case"stream_gone_before_start":return babelHelpers["extends"]({},a,{playbackState:"ended",streamEnded:!0,waitingForDomPlaying:!1});case"stream_interrupted":return babelHelpers["extends"]({},a,{streamInterrupted:!0});case"stream_resumed":return babelHelpers["extends"]({},a,{streamInterrupted:!1});case"seekable_ranges_changed":f=e.payload.seekableRanges;return babelHelpers["extends"]({},a,{seekableRanges:f});case"controller_set_is_live_rewind_active_requested":k=e.payload.isLiveRewindActive;return babelHelpers["extends"]({},a,{isLiveRewindActive:k});case"loop_count_change_requested":g=e.payload.loopCount;return g===a.loopCount?a:babelHelpers["extends"]({},a,{loopCount:g,loopCurrent:0});case"player_dimensions_changed":h=e.payload.dimensions;l=h.height;f=h.width;return f===a.dimensions.width&&l===a.dimensions.height?a:babelHelpers["extends"]({},a,{dimensions:{height:l,width:f}});case"emsg_boxes_parsed":k=d("VideoPlayerEmsgForStateMachine").makeUpdatedAllEmsgBoxes(a.allEmsgBoxes,e.payload.emsgBoxes);g=d("VideoPlayerEmsgForStateMachine").makeUpdatedActiveEmsgBoxes(k,b.videoElementPlayheadPosition,a.activeEmsgBoxes);return babelHelpers["extends"]({},a,{activeEmsgBoxes:g,allEmsgBoxes:k});case"register_emsg_observer":h=new Set(a.emsgObserverTokens);h.add(e.payload.token);return babelHelpers["extends"]({},a,{emsgObserverTokens:h});case"unregister_emsg_observer":l=new Set(a.emsgObserverTokens);l["delete"](e.payload.token);return babelHelpers["extends"]({},a,{emsgObserverTokens:l});default:return a}}function l(a,b,d){var e=b.type!=="dom_seeking";e=e?d:a.uncontrolledState;d=!c("shallowEqual")(e,a.uncontrolledState);b=k(a.controlledState,e,b);var f=!c("shallowEqual")(b,a.controlledState);return d||f?babelHelpers["extends"]({},a,{controlledState:f?b:a.controlledState,uncontrolledState:d?e:a.uncontrolledState}):a}var m,n;function o(a){var b=a.collectUncontrolledState,d=a.debugLogId;d=a.initialState;var e=a.onDispatched,f=a.onFatalError,g=d,h=g,i=0,j=!1,k=!1,m=!0,n=!0;return{dispatch:function(a){if(!n)return;var d=null,g=null;try{++i;if(i>=10)if(!j){j=!0;throw c("unrecoverableViolation")("Video player state machine loop detected","comet_video_player")}else return;var o=h.uncontrolledState;if(m)try{o=b()}catch(a){m=!1,g=a}var p=h;o=l(p,a,o);h=o;e(p,o,a)}catch(a){n=!1,d=a}finally{if(!k&&(d!=null||g!=null)){k=!0;try{f((p=d)!=null?p:g)}catch(a){}}--i}},getCurrentState:function(){return h},getInitialState:function(){return g}}}var p,q;function a(a){var b=a.collectUncontrolledState,c=a.debugLogId,d=a.initialState,e=a.onFatalError,f=a.stateTransitionHandlers;function g(a,b,c){try{var d=!0,e=0;while(d&&e<f.length){var g=f[e];d=g(a,b,c);++e}}finally{}}return o({collectUncontrolledState:b,debugLogId:c,initialState:d,onDispatched:g,onFatalError:e})}function b(a,b){function c(){}function d(a){return a}function e(c){return function(e){var f=d(e.currentTarget);b(f,["reactEvent("+e.type+")"]);a.dispatch(c)}}function f(c){return function(e){var f=d(e.currentTarget);b(f,["reactEvent("+e.type+")"]);a.dispatch(c(e,f))}}return{onAbort:c,onCanPlay:f(function(a){return{payload:{domEventPerfTimestamp:a.timeStamp},type:"dom_event_canplay"}}),onCanPlayThrough:c,onDurationChange:e({type:"dom_event_durationchange"}),onEmptied:c,onEncrypted:c,onEnded:e({type:"dom_event_ended"}),onError:f(function(a,b){return{payload:{videoElementError:b.error,videoElementNetworkState:b.networkState,videoElementReadyState:b.readyState},type:"dom_event_error"}}),onLoadedData:f(function(a){return{payload:{domEventPerfTimestamp:a.timeStamp},type:"dom_event_loadeddata"}}),onLoadedMetadata:f(function(a){return{payload:{domEventPerfTimestamp:a.timeStamp},type:"dom_event_loadedmetadata"}}),onLoadStart:c,onPause:e({type:"dom_event_pause"}),onPlay:e({type:"dom_event_play"}),onPlaying:f(function(a){return{payload:{domEventPerfTimestamp:a.timeStamp},type:"dom_event_playing"}}),onProgress:e({type:"dom_event_progress"}),onRateChange:e({type:"dom_event_ratechange"}),onSeeked:e({type:"dom_event_seeked"}),onSeeking:e({type:"dom_event_seeking"}),onStalled:c,onSuspend:c,onTimeUpdate:e({type:"dom_event_timeupdate"}),onVolumeChange:e({type:"dom_event_volumechange"}),onWaiting:e({type:"dom_event_waiting"})}}g.createVideoPlayerImplementationStateMachine=o;g.createVideoPlayerImplementationStateMachineWithStateTransitionHandlers=a;g.createReactVideoElementCallbacksForStateMachine=b}),98);
__d("VideoPlayerImplementationStateMachineHostCallQueue",["cometUniqueID","emptyFunction","gkx","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";b=c("emptyFunction");function h(){return"id-vpdom-"+c("cometUniqueID")()}function i(a){var b=a.engineExtrasAPI,d=a.hostCall,e=a.machine;a=a.videoElementAPI;switch(d.type){case"host_call_play":var f=a.play();f&&(e.dispatch({payload:{hostCallPlayID:d.payload.hostCallID},type:"dom_event_play_promise_created"}),c("promiseDone")(f.then(function(){e.dispatch({payload:{hostCallPlayID:d.payload.hostCallID},type:"dom_event_play_promise_resolved"})},function(a){e.dispatch({payload:{hostCallPlayID:d.payload.hostCallID,playPromiseRejectionReason:a},type:"dom_event_play_promise_rejected"})})));break;case"host_call_pause":a.pause();break;case"host_call_set_playback_rate":a.setPlaybackRate(d.payload.playbackRate);break;case"host_call_set_muted":a.setMuted(d.payload.muted);break;case"host_call_set_volume":a.setVolume(d.payload.volume);break;case"host_call_set_current_time":a.setPlayheadPosition(d.payload.currentTime);break;case"host_call_set_video_quality":b.setUserSelectedVideoQuality(d.payload.selectedVideoQuality);break;case"host_call_set_video_variant":b.setUserSelectedVideoVariant(d.payload.selectedVideoVariant);break;case"host_call_set_latency_level":b.setLatencyLevel(d.payload.latencyLevel);break;case"host_call_picture_in_picture":a.requestPictureInPicture();break;case"host_call_exit_picture_in_picture":a.exitPictureInPicture();break;default:d.type;return!1}return!0}function j(a){var b=a.engineExtrasAPI,c=a.hostCall,d=a.machine;a=a.videoElementAPI;if(!a||!b)d.dispatch(c);else{var e=!1;try{e=i({engineExtrasAPI:b,hostCall:c,machine:d,videoElementAPI:a})}catch(b){d.dispatch({payload:{errorLocation:"apply_host_call_catch",hostCall:c,hostCallError:b,videoElementError:a.getError(),videoElementNetworkState:a.getNetworkState(),videoElementReadyState:a.getReadyState()},type:"implementation_host_call_failed"})}e&&d.dispatch({payload:{hostCall:c},type:"implementation_host_call_applied"})}}function k(a){var b=[];function c(){b=b.filter(function(a){return a.type!=="host_call_play"&&a.type!=="host_call_pause"})}function d(a){b=b.filter(function(b){return b.type!==a})}a.forEach(function(a){switch(a.type){case"host_call_play":c();break;case"host_call_pause":c();break;case"host_call_set_playback_rate":d(a.type);break;case"host_call_set_muted":d(a.type);break;case"host_call_set_volume":d(a.type);break;case"host_call_set_current_time":d(a.type);break;case"host_call_set_video_quality":d(a.type);break;case"host_call_set_video_variant":d(a.type);break;case"host_call_set_latency_level":d(a.type);break;case"host_call_picture_in_picture":d(a.type);break;case"host_call_exit_picture_in_picture":d(a.type);break;default:a.type}b=b.concat([a])});return b}function a(a){var b=a.engineExtrasAPI,c=a.machine,d=a.reason,e=a.state,f=a.videoElementAPI;a=[{payload:{hostCallID:h(),reason:d,volume:e.controlledState.volume},type:"host_call_set_volume"},{payload:{hostCallID:h(),muted:e.controlledState.muted,reason:d},type:"host_call_set_muted"},{payload:{hostCallID:h(),reason:d,selectedVideoQuality:e.controlledState.selectedVideoQuality},type:"host_call_set_video_quality"},{payload:{hostCallID:h(),reason:d,selectedVideoVariant:e.controlledState.selectedVideoVariant},type:"host_call_set_video_variant"}];d=k(e.controlledState.hostCallQueue.concat(a));d.forEach(function(a){j({engineExtrasAPI:b,hostCall:a,machine:c,videoElementAPI:f})});c.dispatch({payload:{hostCallsFlushed:d},type:"implementation_host_call_queue_flushed"})}g.makeHostCallID=h;g.applyOrQueueHostCall=j;g.flushHostCallQueue=a}),98);
__d("VideoMimeTypes",[],(function(a,b,c,d,e,f){function a(a,b,c){return a+'; codecs="'+b+", "+c+'"'}e="mp4a.40.2";function b(a){return"avc1.42E0"+a.toString(16).toUpperCase()}function c(a){return"avc1.4D40"+a.toString(16).toUpperCase()}function d(a){return"avc1.6400"+a.toString(16).toUpperCase()}var g="video/mp4";b=a(g,b(30),e);var h=a(g,c(30),e);c=a(g,c(31),e);var i=a(g,d(50),e);a=a(g,d(51),e);g={h264baseline:b,h264main30avc:h,h264main31avc:c,h264high50avc:i,h264high51avc:a};f["default"]=g}),66);
__d("VideoPlayerLoggerPlayerStates",[],(function(a,b,c,d,e,f){a="started";b="unpaused";c={STARTED:a,UNPAUSED:b};f["default"]=c}),66);
__d("VideoPlayerMutedStateChange",[],(function(a,b,c,d,e,f){"use strict";function a(a){var b=a.currMuted,c=a.currVolume,d=a.prevMuted;a=a.prevVolume;b=b||c===0;c=d||a===0;if(c===b)return null;return b?"muted":"unmuted"}f.getVideoPlayerMutedStateChange=a}),66);
__d("VideoPlayerImplementationStateMachineLogger",["CometProductAttribution","NetworkStatus","PlaybackSpeedExperiments","SiteData","VideoMimeTypes","VideoPlayerConnectionQuality","VideoPlayerImplementationErrorNormalization","VideoPlayerLoggerPlayerStates","VideoPlayerMutedStateChange","VideoPlayerStateBasedLoggingEvents","emptyFunction","getPlayerFormatForLogData","getVideoBrowserTabId","gkx","justknobx","mapObject","performanceAbsoluteNow","qex","recoverableViolation","removeFromArray"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=6e4,k=(b=c("qex")._("1675"))!=null?b:0,l=1e3,m=null,n=function(a){var b;m=(b=m)!=null?b:document.createElement("video");return!("canPlayType"in m)?"":m.canPlayType(a)},o=c("emptyFunction"),p=c("emptyFunction"),q=c("emptyFunction"),r=c("emptyFunction"),s=!1;c("gkx")("5551")&&(s=!0,o=function(a,b){try{var c;for(var d=arguments.length,e=new Array(d>2?d-2:0),f=2;f<d;f++)e[f-2]=arguments[f];(c=window.console).info.apply(c,["["+a+"][VideoPlayerImplementationStateMachineLogger]"+b].concat(e))}catch(a){}},p=function(a,b,c,d){o(a,"[handleMetaData]",{loggingMetaData:c,loggingMetaDataPrevious:b,trackedChanges:d})},q=function(a,b,c,d){o(a,"[handleStateMachine]",{prevState:b,state:c,action:d})},r=function(a,b,c){o(a,"[setAdditionalLogData]",{key:b,value:c})});var t=(e=d("PlaybackSpeedExperiments").enablePlaybackSpeedLogging())!=null?e:!1;function u(){return{canLogPausedOrFinishedPlaying:!1,canLogPlayingEvent:!0,debugSubscribers:[],dontLogPauseOnUnpausedSeeking:!1,errorRecoveryAttemptState:{currentRecoverableError:null,eventsLogged:0,repeatCount:0},hasLoggedStallData:!1,hasLoggedStartedPlaying:!1,hasPausedOnce:!1,hasPendingRequestedPlaying:!1,initialSuborigin:void 0,inPlayStallCount200ms:0,interruptCount:0,interruptDuration:0,interruptEndTimestamp:0,interruptStartTimestamp:0,isLoggingScrubbingSequence:!1,lastLoggedError:null,lastLoggedFullscreenState:!1,lastLoggedPlaybackSpeed:null,lastLoggedViewabilityPercentage:void 0,lastStartTimePosition:null,lastTrackedAudioRepresentation:null,lastTrackedVideoRepresentation:null,nextHeartbeatTime:null,sequenceNumber:0,shouldIgnoreDomPause:!1,shouldIgnoreDomPlay:!1,shouldLogRequestedPlayingForScrub:!1,stallCount:0,stallCount200ms:0,stallCountTotal:0,stallDuration:0,stallDurationTotal:0,stallStartTimestamp:0,startStallCountTotal:0,startStallDurationTotal:0,vplEventSequenceNumber:0,warningState:{eventsLoggedPerKey:new Map(),eventsLoggedTotal:0,eventsMaxLoggedPerKey:function(){try{return Math.max(0,c("justknobx")._("1831")||0)}catch(a){return 0}}(),eventsMaxLoggedTotal:function(){try{return Math.max(0,c("justknobx")._("1832")||0)}catch(a){return 0}}()}}}function v(a,b){return b==="muted"||b==="unmuted"||b==="started_playing"||b==="caption_change"||b==="unpaused"?a.controlledState.captionsVisible?"on":"off":void 0}function a(a){var b=new Map(),e=a.initialLoggingMetaData,f=e,g=[],m=u(),w=new Set(c("VideoPlayerStateBasedLoggingEvents").StateBasedLoggingEventNames);function x(a){m.debugSubscribers.push(a);return function(){c("removeFromArray")(m.debugSubscribers,a)}}function y(a){m.debugSubscribers.forEach(function(b){return b(a)})}function z(a){var b=m.stallStartTimestamp;if(b>0){m.stallCount+=1;m.stallCountTotal+=1;m.hasPendingRequestedPlaying&&(m.startStallCountTotal+=1);a=a-b;b=a>200;m.stallDurationTotal+=a;m.stallDuration+=a;m.stallCount200ms+=b?1:0;m.inPlayStallCount200ms+=b&&!m.hasPendingRequestedPlaying?1:0;m.hasPendingRequestedPlaying&&(m.startStallDurationTotal+=a)}}function A(a,b){a=a.uncontrolledState.videoElementPlayheadPosition==null?null:a.uncontrolledState.videoElementPlayheadPosition;var c=null;switch(b){case"paused":case"cancelled_requested_playing":case"finished_playing":case"heart_beat":case"playback_speed_changed":case"representation_ended":c=m.lastStartTimePosition;break;case"scrubbed":c=a;break;default:break}switch(b){case"paused":case"cancelled_requested_playing":case"finished_playing":case"scrubbed":m.lastStartTimePosition=null;break;case"started_playing":case"unpaused":case"heart_beat":case"playback_speed_changed":case"representation_ended":m.lastStartTimePosition=a;break;default:break}return{video_last_start_time_position:c}}function B(a,b){var c=b.uncontrolledState.clockTimestamp;a=a.controlledState.streamInterrupted;b=b.controlledState.streamInterrupted;!a&&b?m.interruptStartTimestamp=c:a&&!b&&m.interruptStartTimestamp>0&&(m.interruptEndTimestamp=c,m.interruptCount+=1,m.interruptDuration+=m.interruptEndTimestamp-m.interruptStartTimestamp)}function C(a,b,c){var d=b.uncontrolledState.clockTimestamp;a=a.controlledState.playbackState;var e=b.controlledState.playbackState;a!=="stalling"&&e==="stalling"&&(y("stall_start"),m.stallStartTimestamp=d);if(a==="stalling"&&e!=="stalling"){d=0;(c.type==="dom_event_playing"||c.type==="buffering_end_requested")&&c.payload.domEventPerfTimestamp!=null&&(d=Math.max(b.uncontrolledState.perfTimestamp-c.payload.domEventPerfTimestamp,0));z(b.uncontrolledState.clockTimestamp-d);m.stallStartTimestamp=0;y("stall_end")}}function D(a,b,c){if(m.interruptCount>0&&m.interruptDuration>0){a={interrupt_count:m.interruptCount,interrupt_time:m.interruptDuration};m.interruptCount=0;m.interruptDuration=0;m.interruptStartTimestamp=0;m.interruptEndTimestamp=0;return a}return{interrupt_count:null,interrupt_time:null}}function E(a,b,c){var d={stall_count:null,stall_count_200_ms:null,stall_time:null},e=b.uncontrolledState.clockTimestamp,f=function(){var a={stall_count:m.stallCount,stall_count_200_ms:m.stallCount200ms,stall_time:m.stallDuration};m.stallCount=0;m.stallCount200ms=0;m.stallDuration=0;m.stallStartTimestamp=0;return a};a=a.controlledState.playbackState;var g=b.controlledState.playbackState;a!=="stalling"&&g==="stalling"&&(m.stallStartTimestamp=e);switch(c){case"started_playing":case"unpaused":case"finished_playing":case"paused":case"cancelled_requested_playing":case"playback_speed_changed":case"representation_ended":z(b.uncontrolledState.clockTimestamp);d=f();break;case"heart_beat":b.controlledState.playbackState!=="stalling"&&(z(b.uncontrolledState.clockTimestamp),d=f());break;default:break}return d}function F(a,b,c){a=b.controlledState.playbackState==="paused"&&a.controlledState.playbackState==="stalling";return c>0&&(b.controlledState.playbackState==="stalling"||a)}function G(a,b){a=a.current;var c=null;switch(b){case"finished_playing":case"paused":case"heart_beat":a&&(c=a.getAndFlushTracedFrames());return c!=null?JSON.stringify(c):null;default:return null}}function H(a){a=a.controlledState.playbackState;switch(a){case"playing":case"stalling":return"playing";case"paused":return"paused";case"ended":return"finished";default:a;return"unknown"}}function I(a){switch(a){case"normal":return"normal";case"low":return"low";case"ultra-low":return"ultra_low";default:return null}}function J(){return{state:m.hasLoggedStartedPlaying?c("VideoPlayerLoggerPlayerStates").UNPAUSED:c("VideoPlayerLoggerPlayerStates").STARTED}}function K(a){if((a==null?void 0:a.v2)&&(a==null?void 0:a.v2.length)>0){var b=a==null?void 0:a.v2;b=b[b.length-1];return[b["class"],b.module].map(d("CometProductAttribution").filterEntryValue).join(":")}return(a=a==null?void 0:(b=a.v2)==null?void 0:b.map(function(a){return[a["class"],a.module].map(d("CometProductAttribution").filterEntryValue).join(":")}).join(";"))!=null?a:""}function L(f){var i,j=f.eventType,k=f.logDataOverrides,l=f.prevState,n=f.state;f=A(n,j);f=f.video_last_start_time_position;var p=E(l,n,j),q=p.stall_count,r=p.stall_count_200_ms;p=p.stall_time;var u=D(l,n,j),x=u.interrupt_count;u=u.interrupt_time;var y=G(a.videoLiveTraceRef,j),z=[],B=a.initialLoggingMetaData.coreVideoPlayerMetaData.autoplayGatingResult;B&&z.push(B);B=j==="entered_fs"?!0:j==="exited_fs"?!1:m.lastLoggedFullscreenState;i=c("getPlayerFormatForLogData")({isDesktopPictureInPicture:(i=n.controlledState.isDesktopPictureInPicture)!=null?i:!1,isFullscreen:B},e.coreVideoPlayerMetaData.playerFormat);var C=n.uncontrolledState.viewabilityPercentage,J=Boolean(n.uncontrolledState.isFBIsLiveTemplated),L=Boolean(n.uncontrolledState.isFBWasLive),M=n.uncontrolledState.videoElementPlaybackRate;M=M==null||M===0?m.lastLoggedPlaybackSpeed:M;var N=a.initialLoggingMetaData.productAttribution;if(i==="watch_scroll"&&N&&N.v2){var O,P={0:babelHelpers["extends"]({},N["0"]),v2:[babelHelpers["extends"]({},N.v2[0])]};O=(O=e.productAttribution)==null?void 0:(O=O.v2)==null?void 0:O[0];if(O){var Q=P.v2[0];Q["class"]=O["class"];Q.scope_id=O.scope_id;Q.ts=O.ts}N=P}Q=null;O=null;Q=N!=null?d("CometProductAttribution").minifyProductAttributionV2(N):null;O=N!=null?K(N):null;P=F(l,n,p!=null?p:0);l={access_token:a.initialLoggingMetaData.accessToken,ad_client_token:e.coreVideoPlayerMetaData.adClientToken,attribution_id:N!=null&&Object.prototype.hasOwnProperty.call(N,"0")?JSON.stringify({0:N["0"]}):null,attribution_id_v2:Q,attribution_id_v2_root:O,audio_only:e.coreVideoPlayerMetaData.audioOnly,audio_representation_id:n.uncontrolledState.audioRepresentationID,autoplay_eligible:a.initialLoggingMetaData.coreVideoPlayerMetaData.canAutoplay==="allow",autoplay_failure_reasons:JSON.stringify(z),autoplay_setting:a.initialLoggingMetaData.coreVideoPlayerMetaData.autoplaySetting,available_qualities:n.uncontrolledState.availableQualities.length,broadcaster_origin:a.initialLoggingMetaData.coreVideoPlayerMetaData.broadcasterOrigin,browser_tab_id:c("getVideoBrowserTabId")(),caption_state:v(n,j),client_latency_setting:I(n.controlledState.latencyLevel),current_playback_speed:t?M:null,current_viewability_percentage:C,dash_perf_logging_enabled:e.coreVideoPlayerMetaData.VideoPlayerShakaPerformanceLoggerClass!=null&&e.coreVideoPlayerMetaData.VideoPlayerShakaPerformanceLoggerClass.isEnabled(),data_connection_quality:d("VideoPlayerConnectionQuality").evaluate(function(){return n.uncontrolledState.estimatedBandwidth}),downstream_share_origin_uri:(l=e.downstreamShareSignalTracking)==null?void 0:l.downstream_share_session_origin_uri,downstream_share_start_time:(N=e.downstreamShareSignalTracking)==null?void 0:N.downstream_share_session_start_time,downstream_share_visit_uuid:(Q=e.downstreamShareSignalTracking)==null?void 0:Q.downstream_share_session_id,dropped_frame_count:n.uncontrolledState.videoElementDroppedFrameCount,external_log_id:e.coreVideoPlayerMetaData.externalLogID,external_log_type:e.coreVideoPlayerMetaData.externalLogType,fb_manifest_identifier:n.uncontrolledState.manifestIdentifier,frame_events:y,interrupt_count:x,interrupt_time:u,is_abr_enabled:n.isAbrEnabled,is_fbms:n.uncontrolledState.isFBMS,is_live_video_rewound:n.controlledState.isLiveRewindActive,is_mixed_codec_manifest:n.uncontrolledState.isMixedCodecManifest,is_pip:n.controlledState.isDesktopPictureInPicture,is_predictive_playback:n.uncontrolledState.isPredictiveDash,is_sound_on:!n.controlledState.muted,is_stalling:P,is_templated_manifest:J||L,last_viewability_percentage:m.lastLoggedViewabilityPercentage,mpd_validation_errors:n.uncontrolledState.mpdValidationErrors,network_connected:n.uncontrolledState.networkConnected,playback_caption_format:n.controlledState.captionFormat,playback_caption_locale:n.controlledState.captionsLocale,playback_duration:n.uncontrolledState.videoElementDuration,playback_is_broadcast:a.initialLoggingMetaData.coreVideoPlayerMetaData.isVideoBroadcast,playback_is_drm:Boolean(a.initialLoggingMetaData.coreVideoPlayerMetaData.graphQLVideoDRMInfo),playback_is_live_streaming:a.initialLoggingMetaData.coreVideoPlayerMetaData.isLiveStreaming,player_format:i,player_instance_key:e.instanceKey,player_origin:e.coreVideoPlayerMetaData.playerOriginOverride,player_state:H(n),player_suborigin:e.coreVideoPlayerMetaData.subOrigin,player_version:n.playerVersion,projection:n.uncontrolledState.videoProjection,representation_id:n.uncontrolledState.videoRepresentationID,source_VPL_LOGGING_HACK:e.coreVideoPlayerMetaData.source_VPL_LOGGING_HACK,stall_count:q,stall_count_200_ms:r,stall_time:p,streaming_format:n.controlledState.streamingFormat,total_frame_count:n.uncontrolledState.videoElementTotalFrameCount,tracking_data_encrypted:a.initialLoggingMetaData.trackingDataEncrypted,tracking_nodes:a.initialLoggingMetaData.trackingNodes,v2_heart_beat:c("gkx")("24377")&&j==="heart_beat"?!0:null,video_bandwidth:n.uncontrolledState.estimatedBandwidth,video_buffer_end_position:n.uncontrolledState.videoElementLastBufferEndPosition,video_id:e.coreVideoPlayerMetaData.videoFBID,video_last_start_time_position:f,video_play_reason:n.controlledState.lastPlayReason,video_player_height:(O=e.dimensions)==null?void 0:O.height,video_player_width:(z=e.dimensions)==null?void 0:z.width,video_time_position:n.uncontrolledState.videoElementPlayheadPosition,web_client_revision:c("SiteData").client_revision};N=w.has(j)?++m.sequenceNumber:null;Q={event_seq_num:++m.vplEventSequenceNumber,seq_num:N,time_ms:Date.now(),vpts:(h||(h=c("performanceAbsoluteNow")))()};var R={};b.forEach(function(a,b){R[b]=a});y=e.coreVideoPlayerMetaData.loggingToVPLAdditionalData;x=babelHelpers["extends"]({},y,{},R);u=babelHelpers["extends"]({},x,{},l,{},k,{},Q);J=(P=m.initialSuborigin)!=null?P:e.coreVideoPlayerMetaData.subOrigin;i=(L=u.source_VPL_LOGGING_HACK)!=null?L:J;r=(q=e.coreVideoPlayerMetaData.initialTracePolicy)!=null?q:e.coreVideoPlayerMetaData.routeTracePolicy;p={eventType:j,logData:u,routeTracePolicy:r,source_VPL_LOGGING_HACK:i};g.push(p);m.initialSuborigin==null&&J!=null&&(m.initialSuborigin=J);m.lastLoggedFullscreenState=B;m.lastLoggedPlaybackSpeed=M;j==="viewability_changed"&&(m.lastLoggedViewabilityPercentage=C);s&&o(a.debugLogId,"[_push] "+j,{loggedEvent:p,loggerEventsLength:g.length,loggingState:JSON.stringify(m)})}var M={};function N(a,b,c){L({eventType:"requested_playing",logDataOverrides:babelHelpers["extends"]({},c,{},J()),prevState:a,state:b});m.hasPendingRequestedPlaying=!0;m.canLogPausedOrFinishedPlaying=!0;return M}function O(a,b,c){if(!m.canLogPausedOrFinishedPlaying)return M;else if(m.hasPendingRequestedPlaying){P(a,b,c);m.canLogPausedOrFinishedPlaying=!1;m.hasPendingRequestedPlaying=!1;return M}else{L({eventType:"paused",logDataOverrides:babelHelpers["extends"]({},c,{error_user_info:JSON.stringify({is_document_hidden:b.uncontrolledState.isDocumentHidden})}),prevState:a,state:b});m.canLogPausedOrFinishedPlaying=!1;m.hasPendingRequestedPlaying=!1;return M}}function P(a,b,c){var d=b.uncontrolledState.liveTraceContext;c=babelHelpers["extends"]({},c,{},J(),{live_trace_source_id:d?d.sourceId:void 0,live_trace_stream_id:d?d.streamId:void 0,live_trace_stream_type:d?d.streamType:void 0});L({eventType:"cancelled_requested_playing",logDataOverrides:c,prevState:a,state:b});return M}function Q(a,b,d){if(d.type==="dom_event_play_promise_rejected"&&d.payload.hostCallPlayID===a.controlledState.hostCallPlayIDLast&&b.controlledState.hostCallPlayIDLast==null&&m.hasPendingRequestedPlaying){d=d.payload.playPromiseRejectionReason;if(d!=null&&d.name==="NotAllowedError"){P(a,b,{debug_reason:"not_allowed"});return M}else if(!c("gkx")("30214")&&d!=null&&d.name==="AbortError"){P(a,b,{debug_reason:"aborted"});return M}else return M}else return M}function R(a,b,c){if((c.type==="controller_play_requested"||c.type==="dom_event_play"&&!m.shouldIgnoreDomPlay)&&a.controlledState.playbackState!==b.controlledState.playbackState){c=b.uncontrolledState.liveTraceContext;c=c?{live_trace_source_id:c.sourceId,live_trace_stream_id:c.streamId,live_trace_stream_type:c.streamType}:null;N(a,b,c);return M}else return M}function S(a,b,c){if(a.controlledState.playbackState==="stalling"&&b.controlledState.playbackState==="playing"&&m.canLogPlayingEvent){c=b.uncontrolledState.liveTraceContext;c=c?{live_trace_source_id:c.sourceId,live_trace_stream_id:c.streamId,live_trace_stream_type:c.streamType}:null;L({eventType:m.hasLoggedStartedPlaying?"unpaused":"started_playing",logDataOverrides:c,prevState:a,state:b});m.hasLoggedStartedPlaying=!0;m.canLogPlayingEvent=!1;m.hasPendingRequestedPlaying=!1;return M}else return M}function T(a,b,c){var d=b.controlledState.playbackState,e=a.controlledState.playbackState;if(c.type==="controller_scrub_begin_requested"&&!a.controlledState.scrubbing&&d!=="paused"&&d!=="ended"){O(a,b);m.isLoggingScrubbingSequence=!0;return M}else if(!a.controlledState.seeking&&b.controlledState.seeking&&!m.isLoggingScrubbingSequence&&d!=="paused"&&d!=="ended"&&!m.hasPendingRequestedPlaying){O(a,b);m.shouldLogRequestedPlayingForScrub=!0;return M}else if(c.type==="controller_scrub_end_requested"&&a.controlledState.scrubbing&&d!=="paused"&&d!=="ended"){N(a,b,{video_time_position:c.payload.seekTargetPosition});return M}else if(a.controlledState.seeking&&!b.controlledState.seeking){m.shouldLogRequestedPlayingForScrub&&d!=="paused"&&d!=="ended"&&N(a,b);L({eventType:"scrubbed",prevState:a,state:b});m.isLoggingScrubbingSequence=!1;m.shouldLogRequestedPlayingForScrub=!1;e!=="paused"&&e!=="ended"&&(m.canLogPlayingEvent=!0);return M}else return M}function U(a,b,c){if(a.controlledState.playbackState!==b.controlledState.playbackState&&b.controlledState.playbackState==="ended"&&m.canLogPausedOrFinishedPlaying){L({eventType:"finished_playing",prevState:a,state:b});m.canLogPausedOrFinishedPlaying=!1;return M}else return M}function V(a,b,c){if((c.type==="controller_pause_requested"||c.type==="dom_event_pause"&&!m.shouldIgnoreDomPause)&&a.controlledState.playbackState!==b.controlledState.playbackState){O(a,b,{debug_reason:"paused"});return M}else return M}function W(a,b,c){var d=b.controlledState.playbackState;if(d!=="paused"&&d!=="ended"){c.type==="implementation_video_node_unmounted"?O(a,a,{debug_reason:"unloaded"}):(c.type==="implementation_unmounted"||c.type==="implementation_engine_destroy_requested")&&O(a,b,{debug_reason:"unloaded"});return M}else return M}function X(a,b,c){if(c.type==="implementation_engine_representation_blocked"){c=c.payload.blockedRepresentationID;L({eventType:"video_playback_fallback",logDataOverrides:{representation_id:c},prevState:a,state:b})}return M}function aa(a,b,e){e=b.controlledState.muted;var f=b.controlledState.volume,g=a.controlledState.muted,h=a.controlledState.volume;e=d("VideoPlayerMutedStateChange").getVideoPlayerMutedStateChange({currMuted:e,currVolume:f,prevMuted:g,prevVolume:h});g={current_volume:Math.round(f*100)};switch(e){case"muted":L({eventType:"muted",logDataOverrides:g,prevState:a,state:b});return M;case"unmuted":L({eventType:"unmuted",logDataOverrides:g,prevState:a,state:b});return M;case null:if(h<f){L({eventType:"volume_increase",logDataOverrides:g,prevState:a,state:b});return M}else if(h>f){L({eventType:"volume_decrease",logDataOverrides:g,prevState:a,state:b});return M}else return M;default:e;c("recoverableViolation")('Unexpected mutedStateChange "'+e+'"',"comet_video_player");return M}}function ba(a,b,c){c=b.uncontrolledState.videoRepresentationID;var d=m.lastTrackedVideoRepresentation,e=b.uncontrolledState.audioRepresentationID,f=m.lastTrackedAudioRepresentation,g=b.controlledState.playbackState,h=b.controlledState.seeking;if(!h&&g!=="paused"&&g!=="ended"&&d!=null&&d!==c){var i=b.uncontrolledState.availableVideoTracks.find(function(a){return a.id===b.uncontrolledState.videoRepresentationID}),j=b.uncontrolledState.availableVideoTracks.find(function(a){return a.id===m.lastTrackedVideoRepresentation});L({eventType:"representation_ended",logDataOverrides:{next_quality_label:i==null?void 0:i.qualityLabel,next_representation_id:i==null?void 0:i.id,next_video_codecs:i==null?void 0:i.codec,quality_label:j==null?void 0:j.qualityLabel,representation_id:j==null?void 0:j.id,video_codecs:j==null?void 0:j.codec},prevState:a,state:b});m.lastTrackedVideoRepresentation=c;y("quality_change")}else d==null&&d!==c&&(m.lastTrackedVideoRepresentation=c);!h&&g!=="paused"&&g!=="ended"&&f!=null&&f!==e?(L({eventType:"representation_ended",logDataOverrides:{audio_representation_id:f,next_representation_id:e},prevState:a,state:b}),m.lastTrackedAudioRepresentation=e,y("audio_change")):f==null&&f!==e&&(m.lastTrackedAudioRepresentation=e);return M}function ca(a,b,d){var f=a.controlledState.error===d?babelHelpers["extends"]({},a,{controlledState:babelHelpers["extends"]({},a.controlledState,{error:{$ref:"$.player.lastError"}})}):a;a.uncontrolledState.videoElementError!=null&&(f=babelHelpers["extends"]({},f,{uncontrolledState:babelHelpers["extends"]({},f.uncontrolledState,{videoElementError:{code:a.uncontrolledState.videoElementError.code,message:a.uncontrolledState.videoElementError.message}})}));var g=b,h=typeof b.payload==="object"&&b.payload!=null?b.payload:null;if(h!=null){var j={};Object.keys(h).forEach(function(a){var b=h[a];b===d?j[a]={$ref:"$.player.lastError"}:j[a]=b});g=babelHelpers["extends"]({},b,{payload:j})}return{currentVideo:{audioStreamId:a.uncontrolledState.audioRepresentationID,dashAudioFormat:void 0,hasHD:void 0,hasRateLimit:void 0,hasSubtitles:a.controlledState.captionsLoaded,isDrm:Boolean(e.coreVideoPlayerMetaData.graphQLVideoDRMInfo),isHD:void 0,isLiveStream:e.coreVideoPlayerMetaData.isLiveStreaming,isRateLimited:void 0,liveManifestUrl:void 0,projection:a.uncontrolledState.videoProjection,resourceUrl:void 0,streamId:a.uncontrolledState.videoRepresentationID,streamType:a.controlledState.streamingFormat,tagHD:void 0,tagSD:void 0,videoID:e.coreVideoPlayerMetaData.videoFBID},player:{canPlayType:(i||(i=c("mapObject")))(c("VideoMimeTypes"),n),dimensions:e.dimensions?{height:e.dimensions.height,width:e.dimensions.width}:null,droppedFrames:a.uncontrolledState.videoElementDroppedFrameCount,initializationTime:void 0,initializationTimestamp:void 0,inPlayStallCount:void 0,inPlayStallTime:void 0,interruptCount:void 0,interruptTime:void 0,lastError:d,loggedError:m.lastLoggedError===d?{$ref:"$.player.lastError"}:m.lastLoggedError,stallCount:m.stallCount,stallTime:m.stallDuration,state:H(a),totalFrames:a.uncontrolledState.videoElementTotalFrameCount,version:a.playerVersion,videoSource:void 0,viewabilityPercentage:a.uncontrolledState.viewabilityPercentage},playerStateMachine:{action:g,state:f}}}function da(a){var b=a.errorMessageFormat,c=a.errorName;a=a.errorCode==null||a.errorCode===""?c:a.errorCode;c=c+"#"+a+": "+ea(b);return c}function Y(a,b,c,e){a=ca(a,b,c);b=c.errorMessageFormat;var f=c.errorName,g=c.errorType,h=c.url;c=c.errorCode==null||c.errorCode===""?f:c.errorCode;return{error:f,error_code:c,error_description:b,error_domain:f,error_type:g!=null?g:d("VideoPlayerImplementationErrorNormalization").getErrorTypeFromErrorName(f,b),error_user_info:JSON.stringify(babelHelpers["extends"]({},a,{},e)),resource_url:h}}var ea=function(a){return a.replace(/([0-9]{2,})/g,function(a){var b="";while(b.length<a.length)b+="#";return b})};function fa(a,b,d){if(!c("gkx")("24360"))return M;if(d.type==="implementation_warning"){var e,f=d.payload.warningError,g=da(f),h=m.warningState.eventsLoggedTotal;e=(e=m.warningState.eventsLoggedPerKey.get(g))!=null?e:0;if(h<m.warningState.eventsMaxLoggedTotal&&e<m.warningState.eventsMaxLoggedPerKey){m.warningState.eventsLoggedTotal++;m.warningState.eventsLoggedPerKey.set(g,e+1);g=Y(b,d,f,{warning:{loggedPerKey:e+1,loggedTotal:h+1}});L({eventType:"player_warning",logDataOverrides:babelHelpers["extends"]({},J(),{},g),prevState:a,state:b})}}return M}function ga(a,b,c){var d=b.controlledState.error;if(d!=null&&d!==m.lastLoggedError&&d.errorCode!=="410"){c=Y(b,c,d);L({eventType:"error",logDataOverrides:babelHelpers["extends"]({},J(),{},c),prevState:a,state:b});m.lastLoggedError=d}return M}function Z(a,b,c){var d=m.errorRecoveryAttemptState.currentRecoverableError;if(d!=null){c=Y(b,c,d);m.errorRecoveryAttemptState.eventsLogged++;m.errorRecoveryAttemptState.repeatCount=0;L({eventType:"error_recovery_attempt",logDataOverrides:babelHelpers["extends"]({},c),prevState:a,state:b})}return M}function ha(a,b,d){if(d.type==="error_recovery_attempt"&&m.errorRecoveryAttemptState.eventsLogged<l){var e=m.errorRecoveryAttemptState.currentRecoverableError,f=d.payload.recoverableError;if(f!=null&&f.errorName==="OZ_NETWORK"&&!c("NetworkStatus").isOnline())return M;m.errorRecoveryAttemptState.currentRecoverableError=f;e==null||e.errorName!==f.errorName||e.errorCode!==f.errorCode?Z(a,b,d):(m.errorRecoveryAttemptState.repeatCount++,m.errorRecoveryAttemptState.repeatCount>k&&Z(a,b,d))}return M}function ia(a,b,c){if(!t)return M;var d=b.uncontrolledState.videoElementPlaybackRate;c.type==="dom_event_ratechange"&&m.lastLoggedPlaybackSpeed!=null&&d!==0&&d!==m.lastLoggedPlaybackSpeed&&L({eventType:"playback_speed_changed",prevState:a,state:b});return M}function ja(a,b,c){c=b.controlledState.playbackState;c==="paused"||c==="ended"?m.nextHeartbeatTime=null:c!=="stalling"&&m.nextHeartbeatTime==null&&(m.nextHeartbeatTime=Date.now()+j);var d=m.nextHeartbeatTime;if(d!=null){var e=Date.now();e>=d&&(c!=="stalling"&&L({eventType:"heart_beat",prevState:a,state:b}),m.nextHeartbeatTime=e+j)}return M}function ka(a,b,c){if(c.type==="implementation_mounted"){L({eventType:"player_loaded",prevState:a,state:b});return M}else return M}function $(a,b,c){L({eventType:c?"entered_fs":"exited_fs",prevState:a,state:b})}function la(a,b,c){e.coreVideoPlayerMetaData.playerFormat!==f.coreVideoPlayerMetaData.playerFormat&&L({eventType:"player_format_changed",prevState:a,state:b});return M}function ma(a,b,c){if(c.type!=="notify_fullscreen_changed")return M;c=m.lastLoggedFullscreenState;var d=b.uncontrolledState.isFullscreen;if(c!==!0&&d===!0){$(a,b,d);return M}else if(c===!0&&d===!1){$(a,b,d);return M}else return M}function na(a,b,d){d=e.coreVideoPlayerMetaData.adClientToken;if((d==null||d==="")&&c("gkx")("24380"))return M;d=b.controlledState.playbackState;var f=m.lastLoggedViewabilityPercentage,g=b.uncontrolledState.viewabilityPercentage;if(d!=="paused"&&d!=="ended"&&f!=null&&f!==g){L({eventType:"viewability_changed",prevState:a,state:b});return M}else return M}function oa(a,b,c){if(c.type==="controller_set_captions_visible_requested"){L({eventType:"caption_change",prevState:a,state:b});return M}else return M}function pa(b){f=e;if(b.type==="notify_logging_metadata_change"){b=b.payload.loggingMetaData;if(s){var c=b.coreVideoPlayerMetaData.initialTracePolicy!==e.coreVideoPlayerMetaData.initialTracePolicy;p(a.debugLogId,e,b,{initialTracePolicyChanged:c})}e=b}}return{addDebugSubscriber:function(a){return x(a)},consumeLoggerEvents:function(){var b=g.splice(0);s&&(b.length>0&&o(a.debugLogId,"[consumeLoggerEvents] ("+b.length+" events)",b));return b},getLoggerState:function(){return m},handleStateMachine:function(b,c,d){pa(d);var e=c.controlledState.playbackState;C(b,c,d);B(b,c);var f=[la,ma,na,Q,R,T,S,U,V,X,aa,W,ka,oa,ba,fa,ga,ha,ja,ia];f.forEach(function(a){a(b,c,d)});s&&q(a.debugLogId,b,c,d);(e==="paused"||e==="ended")&&(m.canLogPlayingEvent=!0);d.type==="controller_pause_requested"&&(m.hasPausedOnce=!0,m.shouldIgnoreDomPause=!0);d.type==="controller_play_requested"&&(m.hasPausedOnce=!0,m.shouldIgnoreDomPlay=!0);d.type==="dom_event_pause"&&(m.shouldIgnoreDomPause=!1);d.type==="dom_event_play"&&(m.shouldIgnoreDomPlay=!1,m.lastLoggedViewabilityPercentage=c.uncontrolledState.viewabilityPercentage)},logVPLEvent:function(a){var b=a.eventType,c=a.logDataOverrides;a=a.state;L({eventType:b,logDataOverrides:c,prevState:a,state:a})},setLoggingToVPLAdditionalData:function(a,c){b.set(a,c),s&&r(a,c)}}}g.HEARTBEAT_INTERVAL=j;g.createVideoPlayerImplementationStateMachineLogger=a}),98);
__d("convertToViewabilityPercentage",[],(function(a,b,c,d,e,f){"use strict";function a(a){if(a>=.99)return 100;else if(a>=.75)return 75;else if(a>=.5)return 50;else if(a>=.25)return 25;else if(a>=0)return 0;else return-2}f["default"]=a}),66);
__d("VideoPlayerImplementationStateMachineStateUncontrolledState",["NetworkStatus","convertToViewabilityPercentage","gkx","performance"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var b,d=a.engineExtrasAPI,e=a.fullscreenControllerRef,f=a.videoElementAPI,g=a.videoLiveTraceRef;a=a.videoPlayerPassiveViewabilityInfoRef;g=g.current;var i=Date.now(),j=typeof (h||(h=c("performance"))).now==="function"?(h||(h=c("performance"))).now():0;if(f==null){var k;return{audioRepresentationID:void 0,availableAudioTracks:[],availableQualities:[],availableVideoTracks:[],clockTimestamp:i,currentPlayingAudioTrackID:void 0,currentPlayingVideoQuality:void 0,currentPlayingVideoTrackID:void 0,estimatedBandwidth:void 0,isDocumentHidden:document.hidden,isDRM:void 0,isFBIsLiveTemplated:void 0,isFBMS:void 0,isFBWasLive:void 0,isFullscreen:e.current?e.current.getIsFullscreen():void 0,isLiveRewindAvailable:void 0,isMixedCodecManifest:null,isPredictiveDash:void 0,liveTraceContext:g?(k=g.getLiveTraceContext())!=null?k:void 0:void 0,manifestIdentifier:void 0,mpdValidationErrors:void 0,networkConnected:void 0,perfTimestamp:j,targetAudioTrack:null,targetVideoQuality:"",videoElementDebugCurrentSrc:void 0,videoElementDebugSrc:void 0,videoElementDroppedFrameCount:void 0,videoElementDuration:void 0,videoElementEnded:void 0,videoElementError:void 0,videoElementLastBufferEndPosition:void 0,videoElementMuted:void 0,videoElementNetworkState:void 0,videoElementPaused:void 0,videoElementPlaybackRate:void 0,videoElementPlayheadPosition:void 0,videoElementReadyState:void 0,videoElementTotalFrameCount:void 0,videoElementVolume:void 0,videoProjection:void 0,videoRepresentationID:void 0,viewabilityPercentage:void 0}}k=f.getPlayheadPosition();a=a&&a.current;a=a&&a.getCurrent();return{audioRepresentationID:d?d.getCurrentPlayingAudioRepresentationID():void 0,availableAudioTracks:(b=d==null?void 0:d.getAvailableAudioTracks())!=null?b:[],availableQualities:(b=d==null?void 0:d.getAvailableVideoQualities())!=null?b:[],availableVideoTracks:(b=d==null?void 0:d.getAvailableVideoTracks())!=null?b:[],clockTimestamp:i,currentPlayingAudioTrackID:d?d.getCurrentPlayingAudioRepresentationID():void 0,currentPlayingVideoQuality:d?d.getCurrentPlayingVideoQuality():void 0,currentPlayingVideoTrackID:d?d.getCurrentPlayingVideoRepresentationID():void 0,estimatedBandwidth:d?d.getEstimatedBandwidth():void 0,isDocumentHidden:document.hidden,isDRM:d?d.isDrm():void 0,isFBIsLiveTemplated:d?d.isFBIsLiveTemplated():void 0,isFBMS:d?d.isFBMS():void 0,isFBWasLive:d?d.isFBWasLive():void 0,isFullscreen:e.current?e.current.getIsFullscreen():void 0,isLiveRewindAvailable:d?d.isLiveRewindAvailable():void 0,isMixedCodecManifest:d?d.isMixedCodecManifest():null,isPredictiveDash:d?d.isPredictiveDash():void 0,liveTraceContext:g?(b=g.getLiveTraceContext())!=null?b:void 0:void 0,manifestIdentifier:d?d.getManifestIdentifier():void 0,mpdValidationErrors:d?d.getMpdValidationErrors():void 0,networkConnected:c("NetworkStatus").isOnline(),perfTimestamp:j,targetAudioTrack:(i=d==null?void 0:d.getTargetAudioTrack())!=null?i:null,targetVideoQuality:(e=d==null?void 0:d.getCurrentTargetVideoQuality())!=null?e:"",videoElementDebugCurrentSrc:c("gkx")("24351")?(g=f.getUnderlyingVideoElement())==null?void 0:g.currentSrc:void 0,videoElementDebugSrc:c("gkx")("24351")?(b=f.getUnderlyingVideoElement())==null?void 0:b.src:void 0,videoElementDroppedFrameCount:f.getDroppedFrameCount(),videoElementDuration:f.getDuration(),videoElementEnded:f.getEnded(),videoElementError:f.getError(),videoElementLastBufferEndPosition:f.getLastBufferEndPosition(),videoElementMuted:f.getMuted(),videoElementNetworkState:f.getNetworkState(),videoElementPaused:f.getPaused(),videoElementPlaybackRate:f.getPlaybackRate(),videoElementPlayheadPosition:k,videoElementReadyState:f.getReadyState(),videoElementTotalFrameCount:f.getTotalFrameCount(),videoElementVolume:f.getVolume(),videoProjection:d==null?void 0:d.getVideoProjectionType(),videoRepresentationID:d?d.getCurrentPlayingVideoRepresentationID():void 0,viewabilityPercentage:a?c("convertToViewabilityPercentage")(a.visiblePercentage):void 0}}g.createVideoPlayerImplementationStateMachineStateUncontrolledState=a}),98);
__d("VideoPlayerImplementationStateMachineState",["VideoPlayerImplementationStateMachineStateUncontrolledState"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=a.alwaysShowCaptions,c=a.areCaptionsAutogenerated,e=a.captionDisplayStyle,f=a.dimensions,g=a.isAbrEnabled,h=a.playerVersion;a=a.streamingFormat;var i=!0,j=1,k=d("VideoPlayerImplementationStateMachineStateUncontrolledState").createVideoPlayerImplementationStateMachineStateUncontrolledState({engineExtrasAPI:null,fullscreenControllerRef:{current:null},videoElementAPI:null,videoLiveTraceRef:{current:null},videoPlayerPassiveViewabilityInfoRef:{current:null}});return{controlledState:{activeCaptions:null,activeEmsgBoxes:[],allEmsgBoxes:new Map(),bufferingDetected:!1,captionDisplayStyle:e,captionFormat:null,captionsLoaded:!1,captionsLocale:null,captionsVisible:b,dimensions:f,domEventsLatestPerfMs:{},emsgObserverTokens:new Set(),error:null,hasPlayEverBeenRequested:!1,hostCallPlayIDLast:null,hostCallQueue:[],implementationSeekSourcePosition:null,inbandCaptionsAutogenerated:c,isDesktopPictureInPicture:!1,isLiveRewindActive:!1,lastBufferingType:null,lastMuteReason:null,lastPausedTimeMs:0,lastPauseReason:null,lastPlayedTimeMs:0,lastPlayReason:null,latencyLevel:"normal",loopCount:0,loopCurrent:0,mountState:"before_mounted",muted:i,playbackState:"paused",scrubbing:!1,seekableRanges:null,seeking:!1,seekTargetPosition:null,selectedVideoQuality:"notselected",selectedVideoVariant:null,streamEnded:!1,streamingFormat:a,streamInterrupted:!1,targetPlaybackRate:1,ullIneligibilityReason:null,volume:j,waitingForDomPlaying:!1,waitingForDomTimeUpdateAfterSeeked:!1,watchTimeMs:0},isAbrEnabled:g,playerVersion:h,uncontrolledState:k}}g.createVideoPlayerImplementationStateMachineInitialState=a}),98);
__d("useSEOLoggedOutWebCrawler",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("23157")}g["default"]=a}),98);
__d("VideoPlayerImplementationEngineAPI",["CometEventListener","CometVideoPictureInPictureManagerContext","CometVisualCompletion","CurrentUser","ErrorMetadata","FBLogger","MediaPlaybackCompoundEventStateMachineLogger","MediaPlaybackLogFlusher","PlaybackSpeedExperiments","RunComet","SubscriptionsHandler","VideoLiveTrace","VideoPlayerBanzaiLogFlusher","VideoPlayerCaptionsController","VideoPlayerImplementationReactVideoElement.react","VideoPlayerImplementationStateMachine","VideoPlayerImplementationStateMachineHostCallQueue","VideoPlayerImplementationStateMachineLogger","VideoPlayerImplementationStateMachineState","VideoPlayerImplementationStateMachineStateUncontrolledState","VideoPlayerVideoPixelsFitContext","clearTimeout","cr:2336","cr:355","cr:4596","cr:506","deepEquals","err","gkx","qex","react","recoverableViolation","removeFromArray","setTimeout","unrecoverableViolation","useConcurrentAutoplayManagementAPI","useSEOLoggedOutWebCrawler","useStable"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));f=h;var j=f.useEffect,k=f.useRef,l=f.useState,m=function(){function a(a){var b=a.pauseRequestCallbacks,c=a.playRequestCallbacks,d=a.scrubBeginRequestCallbacks;a=a.scrubEndRequestCallbacks;this.$1=c;this.$2=b;this.$3=d;this.$4=a}var b=a.prototype;b.playRequest=function(a){var b=this;this.$1.push(a);return function(){c("removeFromArray")(b.$1,a)}};b.pauseRequest=function(a){var b=this;this.$2.push(a);return function(){c("removeFromArray")(b.$2,a)}};b.scrubBeginRequest=function(a){var b=this;this.$3.push(a);return function(){c("removeFromArray")(b.$3,a)}};b.scrubEndRequest=function(a){var b=this;this.$4.push(a);return function(){c("removeFromArray")(b.$4,a)}};return a}();function n(a){var b=a.concurrentAutoplayManagementAPI,d=a.debugAPI,e=a.exitPictureInPictureImpl,f=a.fullscreenControllerRef,g=a.getCurrentExposedState,h=a.getCurrentIsDesktopPictureInPicture,i=a.getCurrentIsFullscreen,j=a.getCurrentLiveRewindPlayheadPosition,k=a.getCurrentPlayheadPosition,l=a.internal_getCurrentStateMachineState,n=a.loggerAPI,o=a.pauseImpl,p=a.playImpl,q=a.registerEmsgObserverImpl,r=a.requestPictureInPictureImpl,s=a.scrubBeginImpl,t=a.scrubEndImpl,u=a.seekImpl,v=a.selectVideoQualityImpl,w=a.selectVideoVariantImpl,x=a.setCaptionsDisplayStyleImpl,y=a.setCaptionsUrlImpl,z=a.setCaptionsVisibleImpl,A=a.setIsLiveRewindActiveImpl,B=a.setLatencyLevelImpl,C=a.setMutedImpl,D=a.setPictureInPictureStateImpl,E=a.setPlaybackRateImpl,F=a.setVolumeImpl,G=a.subscribers,H=a.unregisterEmsgObserverImpl,I=a.videoElementAPIRef,J=new Set(),K=!1,L=null,M=[],N=[],O=[],P=[],Q=new m({pauseRequestCallbacks:N,playRequestCallbacks:M,scrubBeginRequestCallbacks:O,scrubEndRequestCallbacks:P}),R={freeze:function(){if(!g().paused)throw c("unrecoverableViolation")("Video player must be paused before the controller freeze","comet_video_player");var a={};try{var b=!1;J.size===0&&(b=!0,L={exposedState:g(),isFullscreen:i(),liveRewindPlayheadPosition:j(),playheadPosition:k(),stateMachineState:l()});J.add(a);b&&(K=!0,G.forEach(function(a){a()}))}finally{K=!1}return a},isFrozen:function(){return L!=null},unfreeze:function(a){if(!J.has(a))throw c("unrecoverableViolation")("Video player controller unfreeze token not found","comet_video_player");J["delete"](a);J.size===0&&(L=null,G.forEach(function(a){a()}))}},S=babelHelpers["extends"]({},b,{},R,{},n,{debugAPI:d,exitPictureInPicture:function(){if(R.isFrozen())return;e()},getCurrentState:function(){return L!=null?L.exposedState:g()},getIsDesktopPictureInPicture:function(){return L!=null?L.isFullscreen:h()},getIsFullscreen:function(){return L!=null?L.isFullscreen:i()},getLiveRewindPlayheadPosition:function(){return L!=null?L.liveRewindPlayheadPosition:j()},getPlayheadPosition:function(){return L!=null?L.playheadPosition:k()},internal_getStateMachineState:function(){return L!=null?L.stateMachineState:l()},internal_getVideoElement:function(){var a=I.current;if(a!=null){a=a.getUnderlyingVideoElement();return a}return null},internal_getVideoPixelsDimensions:function(){var a=I.current;return(a=a==null?void 0:a.getVideoPixelsDimensions())!=null?a:null},observeOn:function(){return Q},pause:function(a){if(R.isFrozen())return;N.forEach(function(b){return b(a)});o(a)},play:function(a){if(R.isFrozen())return;M.forEach(function(b){return b(a)});p(a)},registerEmsgObserver:function(){var a={};q(a);return a},requestPictureInPicture:function(){if(R.isFrozen())return;r()},requestSetIsFullscreen:function(a){if(R.isFrozen())return;var b=f.current;if(b){var c=S.internal_getVideoElement();b.requestSetIsFullscreen(a,c)}},scrollIntoView:function(a){var b=S.internal_getVideoElement();b&&b.scrollIntoView(a)},scrubBegin:function(){if(R.isFrozen())return;O.forEach(function(a){return a()});s()},scrubEnd:function(a){if(R.isFrozen())return;P.forEach(function(b){return b(a)});t(a)},seek:function(a){if(R.isFrozen())return;u(a)},selectVideoQuality:function(a){if(R.isFrozen())return;v(a)},selectVideoVariant:function(a){if(R.isFrozen())return;w(a)},setCaptionsDisplayStyle:function(a){if(R.isFrozen())return;x(a)},setCaptionsUrl:function(a){if(R.isFrozen())return;y(a)},setCaptionsVisible:function(a){if(R.isFrozen())return;z(a)},setIsLiveRewindActive:function(a){if(R.isFrozen())return;A(a)},setLatencyLevel:function(a){if(R.isFrozen())return;B(a)},setMuted:function(a,b){if(R.isFrozen())return;C(a,b)},setPictureInPictureState:function(a){if(R.isFrozen())return;D(a)},setPlaybackRate:function(a){if(R.isFrozen())return;E(a)},setVolume:function(a){if(R.isFrozen())return;F(a)},subscribe:function(a){var b=function(){if(R.isFrozen()&&!K)return;a()},d=f.current,e=d?d.subscribe(b):null;G.push(b);return{remove:function(){e&&e.remove(),c("removeFromArray")(G,b)}}},unregisterEmsgObserver:function(a){H(a)},videoElementAPIRef:I});return S}function o(a){var b=a.concurrentAutoplayManagementAPI,c=a.createExposedState,d=a.debugAPI,e=a.fullscreenControllerRef,f=a.loggerToSNAPL,g=a.loggerToVPL,h=a.machine,i=a.subscribers,j=a.videoElementAPIRef;a={logVPLEvent_DO_NOT_USE:function(a,b){g.logVPLEvent({eventType:a,logDataOverrides:b,state:h.getCurrentState()})},setLoggingToSNAPLAdditionalData:function(a){f==null?void 0:f.setLoggingToSNAPLAdditionalData(a)},setLoggingToVPLAdditionalData:function(a,b){g.setLoggingToVPLAdditionalData(a,b)}};return n({concurrentAutoplayManagementAPI:b,debugAPI:d,exitPictureInPictureImpl:function(){h.dispatch({type:"controller_picture_in_picture_exit_requested"})},fullscreenControllerRef:e,getCurrentExposedState:function(){return c(h.getCurrentState())},getCurrentIsDesktopPictureInPicture:function(){var a;return(a=h.getCurrentState().controlledState.isDesktopPictureInPicture)!=null?a:!1},getCurrentIsFullscreen:function(){var a=e.current;return a?a.getIsFullscreen():!1},getCurrentLiveRewindPlayheadPosition:function(){var a=h.getCurrentState();a=a.controlledState;var b=a.isLiveRewindActive,c=a.seekableRanges;a=a.seekTargetPosition;if(!b||a==null)return null;c=(b=c==null?void 0:c.end(0))!=null?b:0;return a-c},getCurrentPlayheadPosition:function(){var a,b=0;return(a=(a=(a=j.current)==null?void 0:a.getPlayheadPosition())!=null?a:h.getCurrentState().uncontrolledState.videoElementPlayheadPosition)!=null?a:b},internal_getCurrentStateMachineState:function(){return h.getCurrentState()},loggerAPI:a,pauseImpl:function(a){h.dispatch({payload:{reason:a},type:"controller_pause_requested"})},playImpl:function(a){h.dispatch({payload:{reason:a},type:"controller_play_requested"})},registerEmsgObserverImpl:function(a){h.dispatch({payload:{token:a},type:"register_emsg_observer"})},requestPictureInPictureImpl:function(){h.dispatch({type:"controller_picture_in_picture_requested"})},scrubBeginImpl:function(){h.dispatch({type:"controller_scrub_begin_requested"})},scrubEndImpl:function(a){h.dispatch({payload:{seekTargetPosition:a},type:"controller_scrub_end_requested"})},seekImpl:function(a){h.dispatch({payload:{seekTargetPosition:a},type:"controller_seek_requested"})},selectVideoQualityImpl:function(a){h.dispatch({payload:{selectedVideoQuality:a},type:"controller_quality_requested"})},selectVideoVariantImpl:function(a){h.dispatch({payload:{selectedVideoVariant:a},type:"controller_video_variant_requested"})},setCaptionsDisplayStyleImpl:function(a){h.dispatch({payload:{captionDisplayStyle:a},type:"controller_set_caption_display_style_requested"})},setCaptionsUrlImpl:function(a){h.dispatch({payload:{captionsUrl:a},type:"controller_set_captions_url_requested"})},setCaptionsVisibleImpl:function(a){h.dispatch({payload:{captionsVisible:a},type:"controller_set_captions_visible_requested"})},setIsLiveRewindActiveImpl:function(a){h.dispatch({payload:{isLiveRewindActive:a},type:"controller_set_is_live_rewind_active_requested"})},setLatencyLevelImpl:function(a){h.dispatch({payload:{latencyLevel:a},type:"controller_set_latency_level_requested"})},setMutedImpl:function(a,b){h.dispatch({payload:{muted:a,reason:b},type:"controller_muted_requested"})},setPictureInPictureStateImpl:function(a){h.dispatch({payload:{isInPictureInPictureMode:a},type:"controller_set_picture_in_picture_state_requested"})},setPlaybackRateImpl:function(a){h.dispatch({payload:{playbackRate:a},type:"controller_set_playback_rate"})},setVolumeImpl:function(a){h.dispatch({payload:{volume:a},type:"controller_volume_requested"})},subscribers:i,unregisterEmsgObserverImpl:function(a){h.dispatch({payload:{token:a},type:"unregister_emsg_observer"})},videoElementAPIRef:j})}function p(a){a=a.current;if(a==null)throw c("unrecoverableViolation")("Attempted to access VideoElementAPI while it is not available.","comet_video_player");return a}function q(a){var b=a.alwaysShowCaptions,e=a.areCaptionsAutogenerated,f=a.captionDisplayStyle,g=a.captionsControllerRef,h=a.debugLogId,i=a.dimensions,j=a.engineExtrasAPI,k=a.engineMetadata,l=a.fullscreenControllerRef,m=a.handleFatalError,n=a.handleStateMachine,o=a.videoElementAPIRef,q=a.videoLiveTraceRef,s=a.videoPlayerPassiveViewabilityInfoRef;function t(a){d("VideoPlayerImplementationStateMachineHostCallQueue").applyOrQueueHostCall({engineExtrasAPI:j,hostCall:a,machine:v,videoElementAPI:o.current})}var u=null;a=[function(a,b,d){d.type==="implementation_host_call_applied"&&d.payload.hostCall.type==="host_call_play"&&b.controlledState.lastPlayReason==="autoplay_initiated"?c("CometVisualCompletion").addFirstMarkerPoint("firstVideoAutoplayStalling"):b.controlledState.playbackState!==a.controlledState.playbackState&&b.controlledState.playbackState==="playing"&&b.controlledState.lastPlayReason==="autoplay_initiated"&&c("CometVisualCompletion").addFirstMarkerPoint("firstVideoAutoplayPlaying");return!0},function(a,b,c){n(a,b,c);return!0},function(a,b,c){return!0},function(a,b,c){if(a.controlledState.bufferingDetected===!1&&b.controlledState.bufferingDetected===!0||a.controlledState.bufferingDetected===!0&&b.controlledState.bufferingDetected===!1)if(b.controlledState.bufferingDetected){a=(c=b.controlledState.lastBufferingType)!=null?c:"in_play";c=j.getPerfLoggerProvider();u=c?c.getOperationLogger("buffering").setState("buffering").setType(a).start():null}else!b.controlledState.bufferingDetected&&u!=null&&(u.log(),u=null);return!0},function(a,b,c){return c.type!=="implementation_engine_destroy_requested"?!0:!1},function(a,b,c){if(c.type!=="implementation_engine_initialized")return!0;a=p(o);c=c.type;d("VideoPlayerImplementationStateMachineHostCallQueue").flushHostCallQueue({engineExtrasAPI:j,machine:v,reason:c,state:b,videoElementAPI:a});return!1},function(a,b,c){if(c.type!=="dom_event_ended")return!0;a=b.controlledState;(a.loopCount>0&&a.loopCurrent<a.loopCount||a.loopCount===-1)&&v.dispatch({payload:{reason:"loop_initiated"},type:"controller_play_requested"});return!1},function(a,b,c){if(c.type!=="controller_pause_requested")return!0;a=b.controlledState.playbackState;if(a!=="paused")return!1;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),reason:c.type},type:"host_call_pause"});return!1},function(a,b,c){if(c.type!=="controller_play_requested")return!0;a=b.controlledState.playbackState;if(a!=="stalling")return!1;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),reason:c.type},type:"host_call_play"});return!1},function(a,b,c){if(c.type!=="controller_seek_requested")return!0;a=b.controlledState;b=a.seeking;a=a.seekTargetPosition;b&&a!=null&&t({payload:{currentTime:a,hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),reason:c.type},type:"host_call_set_current_time"});return!1},function(a,b,c){if(c.type!=="controller_muted_requested")return!0;a=b.controlledState.muted;if(a!==c.payload.muted)return!1;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),muted:a,reason:c.type},type:"host_call_set_muted"});return!1},function(a,b,c){if(c.type!=="controller_scrub_begin_requested")return!0;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),playbackRate:0,reason:c.type},type:"host_call_set_playback_rate"});return!1},function(a,b,c){if(c.type!=="controller_scrub_end_requested")return!0;a=b.controlledState;b=a.seeking;var e=a.seekTargetPosition;a=a.targetPlaybackRate;b&&e!=null?(t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),playbackRate:a,reason:c.type+":seek"},type:"host_call_set_playback_rate"}),t({payload:{currentTime:e,hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),reason:c.type+":seek"},type:"host_call_set_current_time"})):t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),playbackRate:a,reason:c.type+":cancel"},type:"host_call_set_playback_rate"});return!1},function(a,b,c){if(c.type!=="buffering_begin_requested")return!0;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),playbackRate:0,reason:c.type},type:"host_call_set_playback_rate"});return!1},function(a,b,c){if(c.type!=="controller_set_playback_rate")return!0;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),playbackRate:c.payload.playbackRate,reason:c.type},type:"host_call_set_playback_rate"});return!1},function(a,b,c){if(c.type!=="buffering_end_requested")return!0;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),playbackRate:b.controlledState.targetPlaybackRate,reason:c.type},type:"host_call_set_playback_rate"});return!1},function(a,b,c){if(c.type!=="controller_volume_requested")return!0;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),reason:c.type,volume:b.controlledState.volume},type:"host_call_set_volume"});return!1},function(a,b,c){if(c.type!=="controller_video_variant_requested")return!0;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),reason:c.type,selectedVideoVariant:b.controlledState.selectedVideoVariant},type:"host_call_set_video_variant"});return!1},function(a,b,c){if(c.type!=="controller_quality_requested")return!0;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),reason:c.type,selectedVideoQuality:b.controlledState.selectedVideoQuality},type:"host_call_set_video_quality"});return!1},function(a,b,c){if(!(c.type==="controller_set_latency_level_requested"||c.type==="implementation_set_latency_level_requested"))return!0;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),latencyLevel:c.payload.latencyLevel,reason:c.type},type:"host_call_set_latency_level"});return!1},function(a,b,c){if(!(c.type==="dom_event_timeupdate"||c.type==="captions_loaded"))return!0;c=g.current;var e=b.controlledState.captionsVisible,f=o.current;f=f?f.getPlayheadPosition():0;if(d("PlaybackSpeedExperiments").enableCometPlaybackSpeedControl()&&b.controlledState.isLiveRewindActive){var h=b.controlledState.seekableRanges;h=h!=null?(h=h.end(h.length()-1))!=null?h:0:0;f>=h&&v.dispatch({payload:{isLiveRewindActive:!1},type:"controller_set_is_live_rewind_active_requested"})}if(!j.getRepresentationCaptionsExpectedFromManifest()&&c&&e&&b.controlledState.captionsLoaded){h=c.handleTimeUpdate(f);e=c.getCaptionFormat();b=a.controlledState.activeCaptions;c=a.controlledState.captionFormat;e!==c&&v.dispatch({payload:{captionFormat:e},type:"controller_set_caption_format_requested"});r(b,h)&&v.dispatch({payload:{activeCaptions:h,captionsLocale:null},type:"controller_set_active_captions_requested"})}a=q.current;a!=null&&a.onUpdateStatus({position:f});return!1},function(a,b,c){if(c.type!=="cea608_bytes_received")return!0;a=g.current;if(a){b=c.payload;c=b.timescale;b=b.videoBytes;a.handleCea608BytesReceived({timescale:c,videoBytes:b})}return!1},function(a,b,c){if(c.type!=="controller_set_captions_url_requested")return!0;a=g.current;if(a){b=c.payload.captionsUrl;a.updateCaptionsUrl(b)}return!1},function(a,b,c){if(c.type!=="controller_set_is_live_rewind_active_requested")return!0;j.setEnableLiveheadCatchup(!c.payload.isLiveRewindActive);!!d("PlaybackSpeedExperiments").enableCometPlaybackSpeedControl()&&!c.payload.isLiveRewindActive&&v.dispatch({payload:{playbackRate:1},type:"controller_set_playback_rate"});return!1},function(a,b,c){if(c.type!=="controller_picture_in_picture_requested")return!0;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),reason:c.type},type:"host_call_picture_in_picture"});return!1},function(a,b,c){if(c.type!=="controller_picture_in_picture_exit_requested")return!0;t({payload:{hostCallID:d("VideoPlayerImplementationStateMachineHostCallQueue").makeHostCallID(),reason:c.type},type:"host_call_exit_picture_in_picture"});return!1}];var v=d("VideoPlayerImplementationStateMachine").createVideoPlayerImplementationStateMachineWithStateTransitionHandlers({collectUncontrolledState:function(){var a=o.current;return d("VideoPlayerImplementationStateMachineStateUncontrolledState").createVideoPlayerImplementationStateMachineStateUncontrolledState({engineExtrasAPI:j,fullscreenControllerRef:l,videoElementAPI:a,videoLiveTraceRef:q,videoPlayerPassiveViewabilityInfoRef:s})},debugLogId:h,initialState:d("VideoPlayerImplementationStateMachineState").createVideoPlayerImplementationStateMachineInitialState({alwaysShowCaptions:b,areCaptionsAutogenerated:e,captionDisplayStyle:f,dimensions:i,isAbrEnabled:k.isAbrEnabled,playerVersion:k.playerVersion,streamingFormat:k.streamingFormat}),onFatalError:m,stateTransitionHandlers:a});return v}function r(a,b){if(a===null&&b===null)return!1;if(a===null||b===null)return!0;var c=(b=b.rows)!=null?b:[];a=(b=a.rows)!=null?b:[];return a.length!==c.length||a.some(function(a,b){return a!==c[b]})}function s(a,b,c,d){a!==b.current&&(c.current&&(c.current.remove(),c.current=null),b.current=a),b.current&&!c.current&&(c.current=b.current.subscribe(function(){d.dispatch({type:"notify_fullscreen_changed"})}))}function t(a,b,c,d){a!==b.current&&(c.current&&(c.current.remove(),c.current=null),b.current=a),b.current&&!c.current&&(c.current=b.current.subscribe(function(){d.dispatch({type:"notify_viewability_changed"})}))}function u(a){var b=a.engineMetadata;a=a.state;var c=a.controlledState,d=c.activeCaptions,e=c.activeEmsgBoxes,f=c.captionDisplayStyle,g=c.captionsLoaded,h=c.captionsVisible,i=c.domEventsLatestPerfMs,j=c.error,k=c.hasPlayEverBeenRequested,l=c.implementationSeekSourcePosition,m=c.inbandCaptionsAutogenerated,n=c.isDesktopPictureInPicture,o=c.isLiveRewindActive,p=c.lastMuteReason,q=c.lastPauseReason,r=c.lastPlayedTimeMs,s=c.lastPlayReason,t=c.latencyLevel,u=c.loopCount,v=c.loopCurrent,w=c.muted,x=c.playbackState,y=c.seekableRanges,z=c.seeking,A=c.seekTargetPosition,B=c.selectedVideoQuality,C=c.streamingFormat,D=c.streamInterrupted,E=c.targetPlaybackRate,F=c.ullIneligibilityReason,G=c.volume;c=c.watchTimeMs;var H=a.uncontrolledState,I=H.availableAudioTracks,J=H.availableQualities,K=H.availableVideoTracks,L=H.currentPlayingAudioTrackID,M=H.currentPlayingVideoQuality,N=H.currentPlayingVideoTrackID,O=H.isDRM,P=H.isFullscreen,Q=H.isLiveRewindAvailable,R=H.targetAudioTrack,S=H.targetVideoQuality,T=H.videoElementDuration,U=H.videoElementLastBufferEndPosition,V=H.videoProjection;H=H.videoRepresentationID;var W=x==="ended",X=x==="paused"||W,aa=x==="playing",Y=x==="inPlayStalling";x=x==="stalling"||Y;d={activeCaptions:d,activeEmsgBoxes:e,availableAudioTracks:I,availableVideoQualities:J,availableVideoTracks:K,bufferEnd:U!=null?U:0,captionDisplayStyle:f,captionsLoaded:g,captionsVisible:h,currentAudioTrackID:L!=null?L:null,currentVideoQuality:M!=null?M:"",currentVideoTrackID:N!=null?N:null,domEventsLatestPerfMs:i,duration:T!=null?T:0,ended:W,error:j,hasPlayEverBeenRequested:k,inbandCaptionsAutogenerated:m,inPlayStalling:Y,isAbrEnabled:b.isAbrEnabled,isDesktopPictureInPicture:n!=null?n:!1,isDRM:O!=null?O:null,isFullscreen:P!=null?P:!1,isLiveRewindActive:o,isLiveRewindAvailable:Q!=null?Q:!1,lastMuteReason:p,lastPauseReason:q,lastPlayedTimeMs:r,lastPlayReason:s,latencyLevel:t,loopCount:u,loopCurrent:v,muted:w,paused:X,playerVersion:a.playerVersion,playing:aa,seekableRanges:y,seeking:z,seekSourcePosition:l,seekTargetPosition:A,selectedVideoQuality:B,stalling:x,streamingFormat:C,streamInterrupted:D,targetAudioTrack:R,targetPlaybackRate:E,targetVideoQuality:S,ullIneligibilityReason:F,videoProjection:V,videoRepresentationID:H,volume:G,watchTimeMs:c};return d}function a(a){var e=a.checkInDOM,f=a.createDebugAPI,g=a.createVideoPlayerError,h=a.debugLog,i=a.debugLogId,j=a.destroyEngineParts,k=a.engineExtrasAPI,l=a.engineMetadata,m=a.handleFatalError,n=a.handleVideoElementChanged,p=a.handleVideoInfoChange,r=a.initialProps,v=a.setExposedStateInReact,w=r.loggingMetaData.instanceKey;h=r.loggingMetaData.coreVideoPlayerMetaData;a=h.broadcastId;var x=h.isLiveStreaming;h=h.isLiveTraceEnabled;var y={current:Boolean(x)&&Boolean(h)&&a!=null?new(c("VideoLiveTrace"))(a,w,c("CurrentUser").getAccountID()):null},z=r.loggingConfig.loggingToVPLEnabled==null?!0:r.loggingConfig.loggingToVPLEnabled,A=d("VideoPlayerImplementationStateMachineLogger").createVideoPlayerImplementationStateMachineLogger({debugLogId:i,initialLoggingMetaData:r.loggingMetaData,videoLiveTraceRef:y}),B=new(c("VideoPlayerBanzaiLogFlusher"))(i,A),C=r.loggingConfig.loggingToSNAPLEnabled==null?c("gkx")("8610"):r.loggingConfig.loggingToSNAPLEnabled;x=r.loggingConfig.loggingToSNAPLCreateMetadataProvider;var D=C?d("MediaPlaybackCompoundEventStateMachineLogger").createMediaPlaybackCompoundEventStateMachineLogger({debugLogId:i,initialLoggingMetaData:r.loggingMetaData,metadataProvider:x==null?void 0:x()}):null,E=D!=null?new(c("MediaPlaybackLogFlusher"))(D):null,F={current:(h=r.videoPlayerPassiveViewabilityInfo)!=null?h:null},G={current:(a=r.fullscreenController)!=null?a:null},H={current:null};x=d("useConcurrentAutoplayManagementAPI").createConcurrentAutoplayManagementAPI();var I=[],J=function(){I.forEach(function(a){a()}),!z||r.loggingConfig.disableLogging===!0?B.discardLogsWithoutFlushing():B.flushLogs(),!C||r.loggingConfig.disableLogging===!0?E==null?void 0:E.discardLogsWithoutFlushing():E==null?void 0:E.flushLogs()},K={current:r.onExposedStateChanged},L={current:null},M=null,N={current:{inbandCaptionsAutogeneratedFromManifest:!1,inbandCaptionsExpectedFromManifest:!1,inbandCaptionsExpectedFromProps:r.inbandCaptionsExpected,representationCaptionsExpectedFromManifest:!1,sideLoadCaptionsExpectedFromProps:r.sideLoadCaptionsExpected,sideLoadCaptionsUrlFromProps:(h=r.sideLoadCaptionsUrl)!=null?h:null}},O={current:null},P={current:null},Q=new(c("SubscriptionsHandler"))(),R=new(c("SubscriptionsHandler"))(),S="before_mounted";function T(a){if(c("gkx")("24349"))return a.controlledState.mountState==="mounted_onscreen"||a.controlledState.mountState==="mounted_offscreen";else return S==="mounted"}var U={current:null},V=function(a){a=u({engineMetadata:l,state:a});return a},W={current:null},X={current:null},aa=function(a){if(T(a)){if(a===W.current)return;var b=V(a);if(X.current!=null&&c("deepEquals")(X.current,b))return;W.current=a;X.current=b;v(b);a=K.current;a&&a({implementationController:oa,implementationExposedState:b})}};a=function(a,b,c){A.handleStateMachine(a,b,c),D==null?void 0:D.handleStateMachine(a,b,c),aa(b),J()};var Y=function(a,b){};h=function(a){Y(a,"state_machine_fatal_error")};var Z=q({alwaysShowCaptions:Boolean(r.alwaysShowCaptions),areCaptionsAutogenerated:Boolean(r.areCaptionsAutogenerated),captionDisplayStyle:r.captionDisplayStyle,captionsControllerRef:H,debugLogId:i,dimensions:r.dimensions,engineExtrasAPI:k,engineMetadata:l,fullscreenControllerRef:G,handleFatalError:h,handleStateMachine:a,videoElementAPIRef:U,videoLiveTraceRef:y,videoPlayerPassiveViewabilityInfoRef:F}),ba=!1,$=function(a){var b=L.current!=null,d=U.current!=null;Q.release();Z.dispatch({payload:{reason:a,videoElementAPIExisted:d,videoElementExisted:b},type:"implementation_engine_destroy_requested"});try{ba||(ba=!0,j(["destroyEngine("+a+")"]))}catch(g){try{var e=c("FBLogger")("comet_video_player").catching(g),f=r.videoFBID;f!=null&&(e=e.addMetadata("COMET_VIDEO","VIDEO_ID",f));e=e.addMetadata("COMET_VIDEO","VIDEO_IMPLEMENTATION_DEBUG_DATA",JSON.stringify({destroy_reason:a,player_instance_key:w,player_version:l.playerVersion,video_element_api_existed:d,video_element_existed:b}));e.warn("Error thrown by destroyEngineParts: %s",g.message)}catch(a){}}f=H.current;f!=null&&(Z.getCurrentState().controlledState.captionsLoaded&&Z.dispatch({type:"captions_unloaded"}),H.current=null,f.destroy());s(null,G,O,Z);t(null,F,P,Z);Z.dispatch({payload:{reason:a,videoElementAPIExisted:d,videoElementExisted:b},type:"implementation_engine_destroyed"});b&&c("gkx")("30214")&&ja(null,["destroy",a])};Y=function(a,b){a=g(a,b);Z.dispatch({payload:{fatalError:a},type:"implementation_fatal_error"});$("implementation_fatal_error");m(a)};function ca(a){return a.sideLoadCaptionsExpectedFromProps||a.sideLoadCaptionsUrlFromProps!=null}function da(a){return a.inbandCaptionsExpectedFromProps&&((a=a.inbandCaptionsExpectedFromManifest)!=null?a:!1)}function ea(a){return(a=a.inbandCaptionsAutogeneratedFromManifest)!=null?a:!1}var fa=function(a){var b=ca(a),c=da(a),e=ea(a),f=H.current;e!==ea(N.current)&&Z.dispatch({payload:{inbandCaptionsAutogenerated:e},type:"inband_captions_autogenerated_changed"});f!=null?c!==da(N.current)&&(f.updateInbandCaptionsExpected(c),!c&&Z.getCurrentState().controlledState.captionsLoaded&&Z.dispatch({type:"captions_unloaded"})):(b||c)&&f==null&&(H.current=d("VideoPlayerCaptionsController").createCaptionsController({captionsUrl:b?a.sideLoadCaptionsUrlFromProps:null,inbandCaptionsExpected:c,onCaptionsLoaded:function(){Z.dispatch({type:"captions_loaded"})}}));N.current=a},ga=function(a){k!=null&&a&&a.width>0&&a.height>0&&(k.setDimensions(a),Z.dispatch({payload:{dimensions:{height:a.height,width:a.width}},type:"player_dimensions_changed"}))},ha=function(a){a!==Z.getCurrentState().controlledState.loopCount&&Z.dispatch({payload:{loopCount:a},type:"loop_count_change_requested"})},ia=function(a){"srcObject"in a&&(a.srcObject=null),a.removeAttribute("src"),R.release(),R.engage()},ja=function(a,e){if(L.current===a)return;a!=null&&L.current!=null&&L.current!==a&&c("recoverableViolation")("The video element was recreated","comet_video_player");M!=null&&M();var f=L.current;f&&ia(f);L.current=a;b("cr:506")!=null&&a!=null&&(M=b("cr:506")(a,i));a!=null?(R.addSubscriptions(c("CometEventListener").listen(a,"enterpictureinpicture",function(){var a=d("CometVideoPictureInPictureManagerContext").isInPictureInPictureExp();a&&Z.dispatch({payload:{isInPictureInPictureMode:!0},type:"controller_set_picture_in_picture_state_requested"})}),c("CometEventListener").listen(a,"leavepictureinpicture",function(){var a=d("CometVideoPictureInPictureManagerContext").isInPictureInPictureExp();a&&Z.dispatch({payload:{isInPictureInPictureMode:!1},type:"controller_set_picture_in_picture_state_requested"})})),Z.dispatch({type:"implementation_video_node_mounted"}),n(a,[].concat(e,["handleVideoElement(non-null)"]))):(Z.dispatch({type:"implementation_video_node_unmounted"}),n(null,[].concat(e,["handleVideoElement(null)"])))};h=r.loggingMetaData.instanceKey;a=function(a){var b;K.current=a.onExposedStateChanged;if(!T(Z.getCurrentState()))return;s((b=a.fullscreenController)!=null?b:null,G,O,Z);t((b=a.videoPlayerPassiveViewabilityInfo)!=null?b:null,F,P,Z);if(!p(a)){b={inbandCaptionsAutogeneratedFromManifest:N.current.inbandCaptionsAutogeneratedFromManifest,inbandCaptionsExpectedFromManifest:N.current.inbandCaptionsExpectedFromManifest,inbandCaptionsExpectedFromProps:a.inbandCaptionsExpected,representationCaptionsExpectedFromManifest:N.current.representationCaptionsExpectedFromManifest,sideLoadCaptionsExpectedFromProps:a.sideLoadCaptionsExpected,sideLoadCaptionsUrlFromProps:(b=a.sideLoadCaptionsUrl)!=null?b:null};fa(b);ga(a.dimensions);ha((b=a.loopCount)!=null?b:0)}Z.dispatch({payload:{loggingMetaData:a.loggingMetaData},type:"notify_logging_metadata_change"})};var ka=null;h=function(){var a=Z.getCurrentState().controlledState.mountState;switch(a){case"before_mounted":Q.addSubscriptions(d("RunComet").onUnload(function(){$("page_unload")}));Z.dispatch({type:"implementation_mounted"});n(L.current,["handleReactEffectInit(before_mounted)"]);break;case"mounted_onscreen":break;case"mounted_offscreen":c("clearTimeout")(ka);ka=null;Z.dispatch({type:"implementation_onscreen"});break;case"unmounted":break;default:a}};var la=function(){var a=function a(){c("clearTimeout")(ka);ka=null;if(Z.getCurrentState().controlledState.mountState==="unmounted")return;var b=e();!b?(Z.dispatch({payload:{reason:"react_effect_cleanup"},type:"implementation_unmounted"}),$("implementation_unmounted:react_effect_cleanup")):ka=c("setTimeout")(a,1e3)},b=Z.getCurrentState().controlledState.mountState;switch(b){case"before_mounted":break;case"mounted_onscreen":Z.dispatch({type:"implementation_offscreen"});a();break;case"mounted_offscreen":a();break;case"unmounted":break}},ma=function(){T(Z.getCurrentState())||(S="mounted",Q.addSubscriptions(d("RunComet").onUnload(function(){$("page_unload")})),Z.dispatch({type:"implementation_mounted"}),n(L.current,["handleReactMount"]))},na=function(){T(Z.getCurrentState())&&(S="unmounted",Z.dispatch({payload:{reason:"react_effect_cleanup"},type:"implementation_unmounted"}),$("implementation_unmounted:react_effect_cleanup"))},oa=o({concurrentAutoplayManagementAPI:x,createExposedState:V,debugAPI:f({getVideoElementAPI:function(){return U.current},loggerToVPL:A}),fullscreenControllerRef:G,loggerToSNAPL:D,loggerToVPL:A,machine:Z,subscribers:I,videoElementAPIRef:U});x=d("VideoPlayerImplementationStateMachine").createReactVideoElementCallbacksForStateMachine(Z,ja);f=V(Z.getInitialState());la={handleReactEffectCleanup:c("gkx")("24349")?la:na,handleReactEffectInit:c("gkx")("24349")?h:ma,handleReactPropsChanged:a,implementationController:oa,initialExposedState:f,logFlusherToVPL:B,machine:Z,notifySubscribers:J,videoElementCallbacks:x,videoElementRefCallback:function(a){if(a==null&&c("gkx")("30214"))return;ja(a,["videoElementRefCallback"])}};return{engine:la,getCaptionsInfo:function(){return N.current},getVideoElement:function(){return L.current},getVideoLiveTrace:function(){return y.current},handleCaptionsInfoChange:fa,handleFatalImplementationError:Y,loggerToVPL:A,machine:Z,videoElementAPIRef:U}}function v(a,b){var d,e=c("err").apply(void 0,[a.errorName+": "+a.errorMessageFormat].concat(a.errorMessageParams));e.name=a.errorName;e.errorName=a.errorName;e.type="error";a.stack!=null&&a.stack!==""&&(e.stack=a.stack);var f=new(c("ErrorMetadata"))();f.addEntries(["COMET_VIDEO","ERROR_LOCATION",a.errorLocation],["COMET_VIDEO","ERROR_CODE",(d=a.errorCode)!=null?d:""],["COMET_VIDEO","ERROR_URL",(d=a.url)!=null?d:""]);b!=null&&f.addEntry("COMET_VIDEO","VIDEO_ID",b);d=a.originalError;d!=null&&(typeof d==="string"?f.addEntry("COMET_VIDEO","ORIGINAL_ERROR",d):d instanceof Error&&d.message!=null&&f.addEntry("COMET_VIDEO","ORIGINAL_ERROR",d.message));e.metadata=f;return e}function e(a,d){var e=l(null),f=e[0],g=e[1];e=l(null);var h=e[0],m=e[1];e=a.wrapVideoPixels_EXPERIMENTAL;var n=k(a),o=k(d),p=k(null),q=c("useStable")(function(){return o.current({checkInDOM:function(){var a=document.body,b=p.current;return a!=null&&b!=null&&a.contains(b)},debugLogId:String(n.current.loggingMetaData.instanceKey),handleFatalError:function(a){m(a)},initialProps:n.current,setExposedStateInReact:g})});d=f!=null?f:q.initialExposedState;f=(f=d.error)!=null?f:h;if(f!=null){h=a.VideoPlayerShakaPerformanceLoggerClass;h&&(c("gkx")("24350")||c("qex")._("1655"))&&h.flushQueuedLogs();throw v(f,n.current.videoFBID)}j(function(){q.handleReactEffectInit();return function(){q.handleReactEffectCleanup()}},[q]);var r=k(null);j(function(){a!==r.current&&q.handleReactPropsChanged(a),r.current=a},void 0);h=c("useSEOLoggedOutWebCrawler")();f=a.renderVideoPixelsFit?a.renderVideoPixelsFit(d):null;h=h?i.jsx(c("VideoPlayerImplementationReactVideoElement.react"),{alt:a.alt,poster:a.poster,seoSrc:a.seoWebCrawlerLookasideUrl,seoWebCrawlerVideoTracks:a.seoWebCrawlerVideoTracks,videoElementCallbacks:null,videoElementPreloadDisabled:a.preloadForProgressiveDisabled,videoElementRefCallback:null,videoPixelsFit:f}):i.jsx(c("VideoPlayerImplementationReactVideoElement.react"),{alt:a.alt,poster:a.poster,videoElementCallbacks:q.videoElementCallbacks,videoElementPreloadDisabled:a.preloadForProgressiveDisabled,videoElementRefCallback:q.videoElementRefCallback,videoPixelsFit:f});var s=a.renderWithExposedState({implementationController:q.implementationController,implementationExposedState:d});e=i.jsxs(i.Fragment,{children:[c("gkx")("24349")?i.jsx("div",{ref:p,style:{display:"none"}}):null,d.isDRM===!0?b("cr:2336")?i.jsx(b("cr:2336"),{controller:q.implementationController}):b("cr:4596")?i.jsx(b("cr:4596"),{controller:q.implementationController}):null:null,e?e(h):h,b("cr:355")!=null&&d.playerVersion==="comet_nextgendash"?i.jsx(b("cr:355"),{}):null,i.jsx(c("VideoPlayerVideoPixelsFitContext").Provider,{value:f,children:s})]});return{engine:q,exposedState:d,reactVideoComponents:s,reactVideoFrame:h,reactVideoFrameAndComponents:e}}g.internal_createVideoPlayerImplementationControllerImpl=n;g.internal_createVideoPlayerImplementationEngineStateMachineWithEffects=q;g.internal_createVideoPlayerImplementationExposedStateFromStateMachineState=u;g.createVideoPlayerImplementationEngine=a;g.internal_makeExpandedErrorFromVideoPlayerError=v;g.useVideoPlayerImplementationEngine=e}),98);
__d("VideoPlaybackQuality",[],(function(a,b,c,d,e,f){function a(a){if(typeof a.getVideoPlaybackQuality==="function")return a.getVideoPlaybackQuality().droppedVideoFrames;a=a.webkitDroppedFrameCount;return typeof a==="number"?a:0}function b(a){if(typeof a.getVideoPlaybackQuality==="function")return a.getVideoPlaybackQuality().totalVideoFrames;a=a.webkitDecodedFrameCount;return typeof a==="number"?a:0}f.getDroppedFrames=a;f.getTotalFrames=b}),66);
__d("VideoPlayerImplementationEngineVideoElementAPI",["Promise","VideoPlaybackQuality","VideoPlayerOzWWWGlobalConfig"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a){return isNaN(a)?0:a}function j(a){return i(a.duration)}function k(a){var b=[];try{a=a.buffered;for(var c=0;c<a.length;++c)b.push({endTime:a.end(c),startTime:a.start(c)})}catch(a){}return b}function l(a){try{a=a.buffered;if(a.length>0)return a.end(a.length-1)}catch(a){}return 0}function m(a){return i(a.currentTime)}function n(a){try{a=a.buffered;if(a.length>0)return a.start(0)}catch(a){}return 0}function a(a){return{exitPictureInPicture:function(){window.document.exitPictureInPicture()},getBufferedRanges:function(){return k(a)},getCanPlayPromise:function(){return new(h||(h=b("Promise")))(function(b,c){a.readyState===4?b():a.addEventListener("canplay",function(){return b()})})},getDOMLoadedMetadataPromise:function(){return new(h||(h=b("Promise")))(function(b,c){a.addEventListener("loadedmetadata",function(){return b()})})},getDroppedFrameCount:function(){return d("VideoPlaybackQuality").getDroppedFrames(a)},getDuration:function(){return j(a)},getEnded:function(){return a.ended},getError:function(){return a.error},getLastBufferEndPosition:function(){return l(a)},getMuted:function(){return a.muted},getNetworkState:function(){return a.networkState},getPaused:function(){return a.paused},getPlaybackRate:function(){return a.playbackRate},getPlayheadPosition:function(){return m(a)},getReadyState:function(){return a.readyState},getTotalFrameCount:function(){return d("VideoPlaybackQuality").getTotalFrames(a)},getUnderlyingVideoElement:function(){return a},getVideoPixelsDimensions:function(){if(a.readyState<a.HAVE_CURRENT_DATA)return null;var b=a.videoWidth,c=a.videoHeight;if(b<=0||c<=0)return null;var d=a.offsetWidth,e=a.offsetHeight;if(d<=0||e<=0)return null;b=b/c;c=d/e;c>b?d=e*b:e=d/b;return{height:e,width:d}},getVolume:function(){return a.volume},pause:function(){a.pause()},play:function(){var c=a.play();c=c&&typeof c.then==="function"?(h||(h=b("Promise"))).resolve(c):null;return c},requestPictureInPicture:function(){typeof a.requestPictureInPicture==="function"&&a.requestPictureInPicture()},setDuration:function(b){a.duration=b},setMuted:function(b){a.muted=b},setPlaybackRate:function(b){a.playbackRate=b},setPlayheadPosition:function(b){var d=b;if(b===0){b=c("VideoPlayerOzWWWGlobalConfig").getNumber("clamp_seek_to_first_buffer_range_epsilon",0);if(b>0){var e=n(a);e>0&&e<=b&&(d=e)}}a.currentTime=d},setVolume:function(b){a.volume=b}}}g.getDurationFromVideoElement=j;g.getBufferedRangesFromVideoElement=k;g.getLastBufferEndPositionFromVideoElement=l;g.getPlayheadPositionFromVideoElement=m;g.createVideoPlayerImplementationEngineVideoElementAPI=a}),98);
__d("VideoPlayerProgressiveImplementationEngineExtrasAPI",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return a!==null&&a.indexOf(".m3u8")!==-1}function a(a){var b=a.getPlayingVideoInfo;a=a.setUserSelectedVideoQuality;function d(){var a=b(),c="progressive_SD",d="progressive_HD";return a==null?null:h(a.hdSrc)||h(a.sdSrc)?null:a.hdSrc!=null&&a.playingSrc===a.hdSrc?d:a.sdSrc!=null&&a.playingSrc===a.sdSrc?c:null}return{getApproximateFBLSToPlayerDisplayLatency:function(){return null},getAvailableAudioTracks:function(){var a;return(a=(a=b())==null?void 0:a.availableAudioTracks)!=null?a:[]},getAvailableVideoQualities:function(){var a;return(a=(a=b())==null?void 0:a.availableQualities)!=null?a:[]},getAvailableVideoTracks:function(){var a;return(a=(a=b())==null?void 0:a.availableVideoTracks)!=null?a:[]},getCurrentAudioRepresentation:function(){return null},getCurrentPlayingAudioRepresentationID:function(){if(c("gkx")("440"))return d();else return null},getCurrentPlayingAudioTrackID:function(){var a;return(a=(a=b())==null?void 0:a.playingAudioTrackID)!=null?a:null},getCurrentPlayingVideoQuality:function(){var a;return(a=(a=b())==null?void 0:a.targetQuality)!=null?a:""},getCurrentPlayingVideoRepresentationID:function(){if(c("gkx")("440"))return d();else return"oep_hd"},getCurrentTargetVideoQuality:function(){var a;return(a=(a=b())==null?void 0:a.targetQuality)!=null?a:""},getCurrentVideoRepresentation:function(){return null},getEstimatedBandwidth:function(){return null},getInbandCaptionsAutogeneratedFromManifest:function(){return!1},getInbandCaptionsExpectedFromManifest:function(){return!1},getManifestIdentifier:function(){return null},getMpdValidationErrors:function(){return null},getPerfLoggerProvider:function(){return null},getRepresentationCaptionsExpectedFromManifest:function(){return!1},getStreamType:function(){var a=b();return a==null?"progressive":h(a.hdSrc)||h(a.sdSrc)?"hls":"progressive"},getTargetAudioTrack:function(){return null},getUserSelectedVideoQuality:function(){var a;return(a=(a=b())==null?void 0:a.selectedQuality)!=null?a:"notselected"},getUserSelectedVideoVariant:function(){return null},getVideoProjectionType:function(){return"cubemap"},getVideoRepresentations:function(){return null},isDrm:function(){var a;return((a=b())==null?void 0:a.graphQLVideoDRMInfo)!=null},isFBIsLiveTemplated:function(){return!1},isFBMS:function(){return!1},isFBWasLive:function(){return!1},isLiveRewindAvailable:function(){return!1},isMixedCodecManifest:function(){return!1},isPredictiveDash:function(){return!1},setDimensions:function(a){},setEnableLiveheadCatchup:function(){},setLatencyLevel:function(){},setUserSelectedVideoQuality:a,setUserSelectedVideoVariant:function(a){}}}g.createVideoPlayerProgressiveImplementationEngineExtrasAPI=a}),98);
__d("VideoPlayerProgressiveImplementationEngineUtils",[],(function(a,b,c,d,e,f){"use strict";var g="hd",h="sd";function a(a){var b=[];a.sdSrc!=null&&b.push(h);a.hdSrc!=null&&b.push(g);var c=[],d=[],e=a.hdSrcPreferred&&a.hdSrc!=null?g:h;return babelHelpers["extends"]({},a,{availableAudioTracks:c,availableQualities:b,availableVideoTracks:d,playingAudioTrackID:null,playingQuality:null,playingSrc:null,selectedQuality:e,targetQuality:e,targetSrc:null})}function b(a,b){var c=a.hdSrc,d=a.sdSrc,e,f;b==="notselected"||b==="auto"?(e=d!=null?h:c!=null?g:h,f=d!=null?d:c!=null?c:null):b===g&&c!=null?(e=g,f=c):b===h&&d!=null?(e=h,f=d):(e=h,f=null);f===""&&(f=null);return babelHelpers["extends"]({},a,{selectedQuality:b,targetQuality:e,targetSrc:f})}function c(a,b,c){return babelHelpers["extends"]({},a,{playingQuality:b,playingSrc:c})}f.createResolvedVideoInfoProgressive=a;f.updatePlayingVideoInfoProgressiveWithUserSelectedQuality=b;f.updatePlayingVideoInfoProgressiveWithCurrentPlayingQuality=c}),66);
__d("videoUrlUtils",[],(function(a,b,c,d,e,f){"use strict";b=1<<31;var g=~b,h=b+1;function i(a){a=a|0;return Math.max(h,Math.min(a,g))}function j(a){a=Number.parseInt(a,16);return Number.isFinite(a)?new Date(i(a)*1e3):null}function k(){return i(Date.now()/1e3)}var l={OE:"oe"};function m(a){a=a instanceof URL?a.searchParams:new URL(a).searchParams;a=a.get(l.OE);a=a==null?void 0:j(a);return{expirationDate:a}}function a(a){a=m(a);a=a.expirationDate;return a!=null&&a<=new Date(k()*1e3)}f.DEFAULT_UNIXTIME=b;f.MAX_INT=g;f.parseCdnUrlParams=m;f.isCdnUrlExpired=a}),66);
__d("VideoPlayerProgressiveImplementationEngine",["FBLogger","Promise","UserAgent","VideoPlayerImplementationEngineAPI","VideoPlayerImplementationEngineVideoElementAPI","VideoPlayerImplementationErrors","VideoPlayerODS","VideoPlayerProgressiveImplementationEngineExtrasAPI","VideoPlayerProgressiveImplementationEngineUtils","cr:1473550","cr:1680308","emptyFunction","err","getErrorNameFromMediaErrorCode","getErrorSafe","gkx","promiseDone","unrecoverableViolation","videoUrlUtils"],(function(a,b,c,d,e,f,g){"use strict";var h,i=c("emptyFunction");function j(a,b,c){var e=typeof a==="object"&&a!=null&&typeof a.name==="string"?a.name:null;return d("VideoPlayerImplementationErrors").createVideoPlayerErrorFromGenericError(["PROGRESSIVE_JAVASCRIPT_NATIVE",e].filter(Boolean).join("/"),a,b,c)}function a(a){var e=a.checkInDOM,f=a.debugLogId,g=a.handleFatalError,m=a.initialProps;a=a.setExposedStateInReact;var n={current:null},o={current:null},p={current:null},q={current:null},r=function(){var a=x(),e=o.current;if(a==null||e==null)return;a.addEventListener("error",function(){var e=a.error,f=e==null?void 0:e.code,g=c("getErrorNameFromMediaErrorCode")(f);f=e==null?void 0:e.message;var i=f==null||f===""?"Unknown media error":f,j=[],k=f!=null?d("VideoPlayerImplementationErrors").getMoreGranularErrorNameFromHTMLVideoElementErrorMessage(f):null,l=a.src;!e&&a.poster!=null&&a.poster!==""&&(i+="(possible_poster_load_failure)");if(l!==""){var m=null;(h||(h=b("Promise"))).resolve().then(function(){return window.fetch(l)}).then(function(a){var b,c=a.status;b=(b=a.headers.get("Content-Type"))!=null?b:"unknown";i=i+". Fetched video content with Status:"+c+" Content-Type:"+b;m="URL_RESPONSE_HTTP_"+c;if(c<200||c>=300)return a.text().then(function(a){j.push(a.length.toString()),i+=" Body:%s:"+a.substr(0,200)+(a.length>200?"...":"")},function(){})})["catch"](function(a){if(typeof a==="object"&&a!=null&&typeof a.name==="string"&&typeof a.message==="string"){var b;m=(b=m)!=null?b:"URL_RESPONSE_FETCH_FAILED/"+a.name;i=i+". Failed fetching video content with error: "+a.name+" "+a.message}else{m=(b=m)!=null?b:"URL_RESPONSE_FETCH_FAILED";i=i+". Failed fetching video content with unknown error"}})["finally"](function(){var a=c("err").apply(void 0,[i].concat(j));a.name=[g,k,m].filter(Boolean).join("/");z(a,"progressive_implementation_error_with_more_info")})}else{f=c("err").apply(void 0,[i].concat(j));f.name=[g,k,"VIDEO_ELEMENT_SRC_EMPTY"].filter(Boolean).join("/");z(f,"progressive_implementation_error_with_empty_src")}});try{var f=e.audioOnly,g=e.graphQLVideoDRMInfo,i=e.videoFBID;f===!0&&(a.style.display="none");f=g?(f=g.fairplayCert)!=null?f:null:null;if(b("cr:1680308")&&g&&f!=null&&i!=null){q.current=b("cr:1680308").newIfSupported(f,a,i,g.videoLicenseUriMap);if(q.current==null){f=c("err")("Fairplay not supported");z(f,"progressive_player_fairplay_handler_missing")}else q.current.addListener("error",function(a){a=c("err")(a.error);z(a,"progressive_player_fairplay_handler_error")})}var j=d("VideoPlayerImplementationEngineVideoElementAPI").createVideoPlayerImplementationEngineVideoElementAPI(a);B.current=j;p.current=e;i=w();y({inbandCaptionsAutogeneratedFromManifest:u.getInbandCaptionsAutogeneratedFromManifest(),inbandCaptionsExpectedFromManifest:u.getInbandCaptionsExpectedFromManifest(),inbandCaptionsExpectedFromProps:i==null?void 0:i.inbandCaptionsExpectedFromProps,representationCaptionsExpectedFromManifest:u.getRepresentationCaptionsExpectedFromManifest(),sideLoadCaptionsExpectedFromProps:i==null?void 0:i.sideLoadCaptionsExpectedFromProps,sideLoadCaptionsUrlFromProps:i==null?void 0:i.sideLoadCaptionsUrlFromProps});A.dispatch({payload:{selectedVideoQuality:u.getUserSelectedVideoQuality(),streamingFormat:u.getStreamType()},type:"implementation_engine_initialized"});g=function(){j.setPlayheadPosition(m.startTimestamp)};c("UserAgent").isBrowser("IE11")?c("promiseDone")(j.getDOMLoadedMetadataPromise().then(g)):g()}catch(a){z(a,"progressive_player_create_exception")}},s=function(a,b){if(b==null)return!0;else if(a.videoFBID!==b.videoFBID){var c=14;d("VideoPlayerODS").bumpEntityKey("comet_video_player","ProgressiveImplementation.video_fbid_changed",c);return!1}else if(a.hdSrc!==b.hdSrc||a.sdSrc!==b.sdSrc){c=14;d("VideoPlayerODS").bumpEntityKey("comet_video_player","ProgressiveImplementation.src_changed",c);return!1}else return!1},t=function(a){var b;b={audioOnly:a.audioOnly,graphQLVideoDRMInfo:(b=a.graphQLVideoDRMInfo)!=null?b:null,hdSrc:a.hdSrc===""?null:(b=a.hdSrc)!=null?b:null,hdSrcPreferred:a.hdSrcPreferred,sdSrc:a.sdSrc===""?null:(b=a.sdSrc)!=null?b:null,videoFBID:a.videoFBID};if(!s(b,n.current))return!1;if(b.hdSrc==null&&b.sdSrc==null)throw c("unrecoverableViolation")("Empty hdSrc and sdSrc","comet_video_player");n.current=b;o.current=d("VideoPlayerProgressiveImplementationEngineUtils").createResolvedVideoInfoProgressive(b);r();return!0},u=d("VideoPlayerProgressiveImplementationEngineExtrasAPI").createVideoPlayerProgressiveImplementationEngineExtrasAPI({getPlayingVideoInfo:function(){if(c("gkx")("440")){var a,b=p.current;if(!b)return null;a=(a=B.current)==null?void 0:a.getUnderlyingVideoElement().currentSrc;return babelHelpers["extends"]({},b,{playingSrc:a!=null&&a!==""?a:null})}else return p.current},setUserSelectedVideoQuality:function(a){var b=p.current;if(!b)throw c("unrecoverableViolation")("Attempt to switch quality when playingVideoInfo does not exist","comet_video_player");var e=B.current;if(!e)throw c("unrecoverableViolation")("Attempt to switch quality when videoElementAPI does not exist","comet_video_player");p.current=d("VideoPlayerProgressiveImplementationEngineUtils").updatePlayingVideoInfoProgressiveWithUserSelectedQuality(b,a);var f=p.current.targetSrc;if(f===null){c("FBLogger")("comet_video_player").addMetadata("COMET_VIDEO","VIDEO_ID",m.videoFBID==null?"":String(m.videoFBID)).warn("Received null targetSrc from setUserSelectedVideoQuality, selectedQuality: %s",String(a));return}k(f,m.expiredVideoUrlRefreshHandler).then(function(a){var b;if(((b=p.current)==null?void 0:b.targetSrc)!==f)return;l(e.getUnderlyingVideoElement(),{crossOrigin:m.crossOrigin_DO_NOT_USE_UNLESS_YOU_KNOW_WHAT_YOURE_DOING},a);a=(b=A.getCurrentState().uncontrolledState.videoElementPlayheadPosition)!=null?b:0;a>0&&e.setPlayheadPosition(a);A.getCurrentState().controlledState.playbackState==="playing"&&e.play();A.dispatch({payload:{},type:"representation_changed"})})["catch"](function(a){var b;if(((b=p.current)==null?void 0:b.targetSrc)!==f)return;b=c("getErrorSafe")(a);c("FBLogger")("comet_video_player").addMetadata("COMET_VIDEO","VIDEO_ID",m.videoFBID==null?"":String(m.videoFBID)).catching(b).warn("Failed refreshing video URL with original error: %s",String(b))})}}),v=function(){q.current&&(q.current.destroy(),q.current=null)};e=d("VideoPlayerImplementationEngineAPI").createVideoPlayerImplementationEngine({checkInDOM:e,createDebugAPI:function(a){var c=a.getVideoElementAPI;a=a.loggerToVPL;return b("cr:1473550")?b("cr:1473550").createVideoPlayerImplementationDebugAPI({engineExtrasAPI:u,getVideoElementAPI:c,loggerToVPL:a}):null},createVideoPlayerError:function(a,b){return j(a,b,(a=p.current)==null?void 0:a.targetSrc)},debugLog:i,debugLogId:f,destroyEngineParts:function(){v()},engineExtrasAPI:u,engineMetadata:{isAbrEnabled:!1,playerVersion:"comet_progressive",streamingFormat:"progressive"},handleFatalError:g,handleVideoElementChanged:function(a){a!=null&&r()},handleVideoInfoChange:t,initialProps:m,setExposedStateInReact:a});f=e.engine;var w=e.getCaptionsInfo,x=e.getVideoElement,y=e.handleCaptionsInfoChange,z=e.handleFatalImplementationError,A=e.machine,B=e.videoElementAPIRef;return f}function k(a,e){if(e&&d("videoUrlUtils").isCdnUrlExpired(a))return e(a).then(function(a){var d;return(d=a.refreshedUrl)!=null?d:(h||(h=b("Promise"))).reject(c("err")("Failed refreshing URL"+(a.reason!==null?" with reason: "+a.reason:"")))});else return(h||(h=b("Promise"))).resolve(a)}function l(a,b,c){b=b.crossOrigin;if(c==null||c==="")a.removeAttribute("src");else{var d=a.playbackRate;a.setAttribute("src",c);var e=null;if(b!=null){try{e=new URL(c).origin}catch(a){}if(e!=null&&location.origin!==e)switch(b){case"anonymous":case"use-credentials":a.setAttribute("crossOrigin",b);break;default:a.removeAttribute("crossOrigin");break}}a.playbackRate=d}}g.createVideoPlayerProgressiveImplementationEngine=a;g.internal_setHTMLVideoElementSrc=l}),98);
__d("VideoPlayerProgressiveImplementationV2.react",["VideoPlayerImplementationEngineAPI","VideoPlayerProgressiveImplementationEngine"],(function(a,b,c,d,e,f,g){"use strict";function a(a){a=d("VideoPlayerImplementationEngineAPI").useVideoPlayerImplementationEngine(a,d("VideoPlayerProgressiveImplementationEngine").createVideoPlayerProgressiveImplementationEngine);a=a.reactVideoFrameAndComponents;return a}g["default"]=a}),98);
__d("usePolarisLOXDashPrefetch",["CometRelay","fetchPolarisLoggedOutExperiment","gkx","polarisGetAppPlatform","promiseDone","react","react-compiler-runtime","shouldPrefetchLOXDash"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(2),b=d("CometRelay").useRelayEnvironment(),e;a[0]!==b?(e=function(){!d("polarisGetAppPlatform").isIOS()&&c("gkx")("15065")&&c("promiseDone")(c("fetchPolarisLoggedOutExperiment")(b,{name:"ig_mweb_dash_video_playback_universe",param:"is_dash_prefetch_enabled"}));return c("shouldPrefetchLOXDash")()},a[0]=b,a[1]=e):e=a[1];return e}g.usePolarisLOXDashPrefetch=a}),98);
__d("usePolarisVideoImplementations",["PolarisIsLoggedIn","VideoPlayerProgressiveImplementationData","VideoPlayerProgressiveImplementationV2.react","cr:21166","cr:21289","cr:21369","err","gkx","usePolarisLOXDashPrefetch"],(function(a,b,c,d,e,f,g){"use strict";var h=(f=(e=b("cr:21166"))!=null?e:b("cr:21289"))!=null?f:b("cr:21369");function a(a){var b=a.dashInfo,e=a.hdSrc,f=a.isAd,g=a.isLive;a=a.sdSrc;var j=[],k=d("usePolarisLOXDashPrefetch").usePolarisLOXDashPrefetch();f=(b=h==null?void 0:h({dashInfo:b,isAd:f,isLive:g}))!=null?b:[];g=f[0];b=f[1];g!=null&&(k(),j.push(g));f=d("VideoPlayerProgressiveImplementationData").makeProgressiveImplementationData({hdSrc:e,hdSrcPreferred:d("PolarisIsLoggedIn").isLoggedIn()?c("gkx")("5688"):!0,sdSrc:a});f instanceof Error||j.push({Component:c("VideoPlayerProgressiveImplementationV2.react"),data:f,typename:"VideoPlayerProgressiveImplementation"});j.length===0&&i(b);return j}function i(a){var b;a!=null?(b=c("err")("Cannot play video: "+a.message+" and no progressive URL is available"),a.name&&(b.name=a.name)):(b=c("err")("Cannot play video: No progressive URL is available"),b.name="VideoImplementationsNoProgressiveURL");b.project="comet_video_player";throw b}g["default"]=a}),98);
__d("PolarisVideo.react",["CometVideoPlayerLoggingConfigForPolaris","PolarisContainerModuleUtils","PolarisNavChain","PolarisRoutePropUtils","PolarisVideoHelpers","VideoPlayerErrorBoundary.react","VideoPlayerSurface.react","VideoPlayerX.react","computeAspectRatio","defaultErrorBoundaryFallback","react","react-compiler-runtime","usePolarisAnalyticsContext","usePolarisGetDashInfo","usePolarisVideoImplementations","useStable"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useContext;function k(a){var b=d("react-compiler-runtime").c(39),e=a.adInfo,f=a.alt,g=a.autoplay,h=a.canAutoplay,k=a.children,m=a.dashInfo,n=a.hdSrc,o=a.isAd,p=a.isLive,q=a.loopCount,r=a.mediaId,s=a.originalHeight,t=a.originalWidth,u=a.ownerId,v=a.poster,w=a.renderVideoPixelsFit,x=a.sdSrc,y=a.seoWebCrawlerLookasideUrl;a=a.trackingToken;m=c("usePolarisGetDashInfo")(m);var z;b[0]!==m||b[1]!==n||b[2]!==o||b[3]!==p||b[4]!==x?(z={dashInfo:m,hdSrc:n,isAd:o,isLive:p,sdSrc:x},b[0]=m,b[1]=n,b[2]=o,b[3]=p,b[4]=x,b[5]=z):z=b[5];m=c("usePolarisVideoImplementations")(z);b[6]!==g||b[7]!==h?(n=d("PolarisVideoHelpers").getVideoPlayerAutoplayProps(g,h),b[6]=g,b[7]=h,b[8]=n):n=b[8];o=n;b[9]!==s||b[10]!==t?(p=c("computeAspectRatio")(t,s),b[9]=s,b[10]=t,b[11]=p):p=b[11];x=p;z=c("usePolarisAnalyticsContext")();g=c("useStable")(l);h=j(d("PolarisRoutePropUtils").PolarisRoutePropContext);b[12]!==(h==null?void 0:h.routePropQE)?(n=h==null?void 0:h.routePropQE.getBool("is_ssr_enabled"),b[12]=h==null?void 0:h.routePropQE,b[13]=n):n=b[13];s=n;t=h==null?void 0:h.routeProp.getString("general_uuid");b[14]===Symbol["for"]("react.memo_cache_sentinel")?(p=d("CometVideoPlayerLoggingConfigForPolaris").createCometVideoPlayerLoggingConfigForPolaris(),b[14]=p):p=b[14];b[15]!==z?(n=d("PolarisContainerModuleUtils").getContainerModule(z),b[15]=z,b[16]=n):n=b[16];z=(h=e==null?void 0:e.tracking_token)!=null?h:a;h=e?"paid":"organic";b[17]!==g||b[18]!==u||b[19]!==n||b[20]!==z||b[21]!==h?(a={author_id:u,current_watching_module:n,nav_chain:g,tracking_token:z,tracking_type:h},b[17]=g,b[18]=u,b[19]=n,b[20]=z,b[21]=h,b[22]=a):a=b[22];e=s&&t!=null?r+t:void 0;b[23]!==k?(g=k!=null?k:i.jsx(c("VideoPlayerSurface.react"),{}),b[23]=k,b[24]=g):g=b[24];b[25]!==f||b[26]!==o||b[27]!==m||b[28]!==s||b[29]!==q||b[30]!==r||b[31]!==v||b[32]!==w||b[33]!==y||b[34]!==e||b[35]!==g||b[36]!==a||b[37]!==x?(u=i.jsx(c("VideoPlayerX.react"),babelHelpers["extends"]({alt:f,bypassUseCometSizeError:s,disableLoadingIndicator:!0,doNotRenderErrorBoundaryIUnderstandIMustProvideMyOwn:!0,implementations:m,loggingConfig:p,loggingToSNAPLAdditionalData:a,loopCount:q,portalingEnabled:!1,poster:v,preloadForProgressiveDisabled:!0,renderVideoPixelsFit:w,seoWebCrawlerLookasideUrl:y,subOrigin:"inline",videoFBID:r,videoPixelsAspectRatio:x,videoPlayerUniqueIDOverride:e},o,{children:g})),b[25]=f,b[26]=o,b[27]=m,b[28]=s,b[29]=q,b[30]=r,b[31]=v,b[32]=w,b[33]=y,b[34]=e,b[35]=g,b[36]=a,b[37]=x,b[38]=u):u=b[38];return u}function l(){var a;return(a=c("PolarisNavChain").getInstance())==null?void 0:a.getNavChainForSend()}function a(a){return i.jsx(c("VideoPlayerErrorBoundary.react"),{description:"PolarisVideoX",fallback:c("defaultErrorBoundaryFallback"),children:i.jsx(k,babelHelpers["extends"]({},a))})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("Popup",["isTruthy"],(function(a,b,c,d,e,f,g){function a(a,b,d,e){var f=[];c("isTruthy")(b)&&f.push("width="+b);c("isTruthy")(d)&&f.push("height="+d);var g=document.body;if(g!=null&&b!=null&&b!==0&&d!=null&&d!==0){var h="screenX"in window?window.screenX:window.screenLeft,i="screenY"in window?window.screenY:window.screenTop,j="outerWidth"in window?window.outerWidth:g.clientWidth;g="outerHeight"in window?window.outerHeight:g.clientHeight-22;h=Math.floor(h+(j-b)/2);j=Math.floor(i+(g-d)/2.5);f.push("left="+h);f.push("top="+j)}f.push("scrollbars");return window.open(a,e!=null&&e!==""?e:"_blank",f.join(","))}g.open=a}),98);
__d("SecurePostMessage",["invariant"],(function(a,b,c,d,e,f,g){"use strict";var h="*";a={sendMessageToSpecificOrigin:function(a,b,c,d){c!==h||g(0,21157),a.postMessage(b,c,d)},sendMessageForCurrentOrigin:function(a,b){a.postMessage(b)},sendMessageAllowAnyOrigin_UNSAFE:function(a,b,c){a.postMessage(b,h,c)}};e.exports=a}),null);
__d("VideoHomeTypedLiteLogger",["generateLiteTypedLogger"],(function(a,b,c,d,e,f){"use strict";e.exports=b("generateLiteTypedLogger")("logger:VideoHomeLoggerConfig")}),null);
__d("VideoPlayerLiveRewindControlContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({cachedLiveRewindTimestamp:null,onLiveRewindControlEvent:function(){}});g["default"]=b}),98);
__d("VideoPlayerPlaybackControlBase.react",["fbt","ix","VideoPlayerControlIcon.react","fbicon","react","unrecoverableViolation"],(function(a,b,c,d,e,f,g,h,i){"use strict";var j,k=j||d("react");function a(a){var b=a.onPress,e=a.playbackIcon,f=a.showTooltip;f=f===void 0?!0:f;a=a.useOutlineIcons;var g,j;switch(e){case"pause":g=h._(/*BTDS*/"Pause");j=a===!0?d("fbicon")._(i("497677"),20):d("fbicon")._(i("497675"),20);break;case"replay":g=h._(/*BTDS*/"Replay");j=a===!0?d("fbicon")._(i("534222"),20):d("fbicon")._(i("534219"),20);break;case"play":g=h._(/*BTDS*/"Play");j=a===!0?d("fbicon")._(i("484866"),20):d("fbicon")._(i("484863"),20);break;default:e;throw c("unrecoverableViolation")("The playback icon is unsupported "+e,"comet_video_player")}return k.jsx(c("VideoPlayerControlIcon.react"),{icon:j,label:g,onPress:b,tooltip:f?g:null})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("useFeedClickEventHandler",["react","useStoryClickEventLogger"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useCallback;function a(a,b){var d=c("useStoryClickEventLogger")();return i(function(c){a&&a(c);var e=c.type;if(e==="click"||e==="contextmenu"||e==="mousedown"&&typeof c.button==="number"&&(c.button===1||c.button===2)||e==="keydown"&&(c.key==="Enter"||c.key===" ")){e=typeof c.button==="number"?c.button:0;d(c.timeStamp,e,b)}},[a,d,b])}g["default"]=a}),98);
__d("useMinifiedProductAttribution",["CometProductAttribution","useRouteProductAttribution"],(function(a,b,c,d,e,f,g){"use strict";function a(){var a=c("useRouteProductAttribution")();return a!=null?d("CometProductAttribution").minifyProductAttributionV2(a):null}g["default"]=a}),98);
__d("VideoPlayerPlaybackControl.react",["CometTrackingNodeProvider.react","VideoHomeTypedLiteLogger","VideoPlayerHooks","VideoPlayerPlaybackControlBase.react","react","useFeedClickEventHandler","useMinifiedProductAttribution","useVideoPlayerControllerSubscription"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=h||(h=d("react")),k=h.useEffect;function a(a){var b=a.isNPC,e=b===void 0?!1:b;b=a.showTooltip;b=b===void 0?!0:b;var f=a.unmuteOnPlay,g=f===void 0?!1:f;f=a.useOutlineIcons;k(function(){e&&c("VideoHomeTypedLiteLogger").log({event:"npc_control_playback_button_impression"})},[e]);a=c("useVideoPlayerControllerSubscription")(function(a,b){var c=a.getCurrentState(),d=c.ended,e=c.paused,f=c.playing,g=c.stalling;a=a.getPlayheadPosition();c=c.duration;e=!e&&(f||g);g=!f&&d&&a>=c;return b!=null&&b.showPauseButton===e&&b.showReplayButton===g?b:{showPauseButton:e,showReplayButton:g}});var h=a.showPauseButton;a=a.showReplayButton;var l=(i||(i=d("VideoPlayerHooks"))).useController(),m=c("useMinifiedProductAttribution")(),n=c("useFeedClickEventHandler")(function(){e&&c("VideoHomeTypedLiteLogger").log({attribution_id_v2:m,click_point:"npc_control_playback_button",event:"click",event_target:"video"}),h?l.pause("user_initiated"):(g&&l.setMuted(!1,"user_initiated"),l.play("user_initiated"))});a=h?"pause":a?"replay":"play";return j.jsx(c("CometTrackingNodeProvider.react"),{trackingNode:592,children:j.jsx(c("VideoPlayerPlaybackControlBase.react"),{onPress:n,playbackIcon:a,showTooltip:b,useOutlineIcons:f})})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("getFormattedTimestamp",["DateConsts"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b="";a=a;isNaN(a)?a=0:a<0&&(a*=-1,b="-");var c=Math.floor(a/d("DateConsts").SEC_PER_HOUR),e=Math.floor((a-c*d("DateConsts").SEC_PER_HOUR)/d("DateConsts").SEC_PER_MIN);a=Math.round(a-c*d("DateConsts").SEC_PER_HOUR-e*d("DateConsts").SEC_PER_MIN);a===d("DateConsts").SEC_PER_MIN&&(a=0,e++);e===d("DateConsts").MIN_PER_HOUR&&(e=0,c++);a=("0"+a).slice(-2);if(c===0)return""+b+e+":"+a;else{e=("0"+e).slice(-2);return""+b+c+":"+e+":"+a}}g["default"]=a}),98);
__d("VideoPlayerPlaybackTimerBase.react",["getFormattedTimestamp","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=a.currentTime;a=a.duration;return i.jsxs("div",babelHelpers["extends"]({className:"x14ctfv x1rg5ohu x1pg5gke xss6m8b x7h9g57 xf6vk7d xpcyujq x9hgts1 x2b8uid x27saw0 x3ajldb"},{children:[i.jsx("span",babelHelpers["extends"]({className:"x1s688f x15hfatp"},{children:c("getFormattedTimestamp")(b)})),a!=null&&i.jsxs(i.Fragment,{children:[i.jsx("span",{children:" / "}),i.jsx("span",{children:c("getFormattedTimestamp")(a)})]})]}))}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("useLiveRewindUtils",["VideoPlayerHooks","VideoPlayerLiveRewindControlContext","react","useVideoPlayerControllerSubscription"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=h||d("react"),k=j.useCallback,l=j.useContext,m=10,n=10;function a(){var a=(i||(i=d("VideoPlayerHooks"))).useController(),b=l(c("VideoPlayerLiveRewindControlContext"));return k(function(){var c=a.getCurrentState().seekableRanges;c=c!=null?(c=c.end(c.length()-1))!=null?c:0:0;var d=Math.min(a.getPlayheadPosition()+m,c);d===c?(a.setIsLiveRewindActive(!1),a.seek(c)):a.seek(d);b.onLiveRewindControlEvent()},[b,a])}function b(){var a=(i||(i=d("VideoPlayerHooks"))).useController(),b=l(c("VideoPlayerLiveRewindControlContext"));return k(function(){var c=a.getCurrentState().seekableRanges;c=(c=c==null?void 0:c.start(0))!=null?c:0;c=Math.max(a.getPlayheadPosition()-n,c);a.seek(c);a.setIsLiveRewindActive(!0);b.onLiveRewindControlEvent()},[b,a])}function e(){var a=(i||(i=d("VideoPlayerHooks"))).useController(),b=l(c("VideoPlayerLiveRewindControlContext"));return k(function(){var c=a.getCurrentState().seekableRanges;c=c!=null?(c=c.end(c.length()-1))!=null?c:0:0;a.seek(c);a.play("user_initiated");a.setIsLiveRewindActive(!1);b.onLiveRewindControlEvent()},[b,a])}function f(){var a=(i||(i=d("VideoPlayerHooks"))).useController(),b=l(c("VideoPlayerLiveRewindControlContext"));return k(function(){var c=a.getCurrentState().seekableRanges;c=(c=c==null?void 0:c.start(0))!=null?c:0;a.seek(c);a.setIsLiveRewindActive(!0);b.onLiveRewindControlEvent()},[b,a])}function o(){var a=(i||(i=d("VideoPlayerHooks"))).useController(),b=l(c("VideoPlayerLiveRewindControlContext"));return k(function(){a.play("user_initiated"),b.onLiveRewindControlEvent()},[b,a])}function p(){var a=(i||(i=d("VideoPlayerHooks"))).useController(),b=l(c("VideoPlayerLiveRewindControlContext"));return k(function(){a.pause("user_initiated"),a.setIsLiveRewindActive(!0),b.onLiveRewindControlEvent()},[b,a])}function q(){var a=(i||(i=d("VideoPlayerHooks"))).useController(),b=l(c("VideoPlayerLiveRewindControlContext"));return k(function(){var c=a.getCurrentState().seekableRanges;c=(c=c==null?void 0:c.start(0))!=null?c:0;a.seek(c);a.setIsLiveRewindActive(!0);a.play("user_initiated");b.onLiveRewindControlEvent()},[b,a])}function r(){var a=(i||(i=d("VideoPlayerHooks"))).useController(),b=l(c("VideoPlayerLiveRewindControlContext"));return k(function(c){var d,e=a.getCurrentState().seekableRanges;d=(d=e==null?void 0:e.start(0))!=null?d:0;e=e!=null?(e=e.end(e.length()-1))!=null?e:0:0;d=Math.min(d+c,e);a.scrubEnd(d);d===e?a.setIsLiveRewindActive(!1):a.setIsLiveRewindActive(!0);b.onLiveRewindControlEvent()},[b,a])}function s(){return c("useVideoPlayerControllerSubscription")(function(a){return(a=(a=a.getCurrentState().seekableRanges)==null?void 0:a.start(0))!=null?a:0})}g.useLiveRewindForward=a;g.useLiveRewindBack=b;g.useLiveRewindLive=e;g.useLiveRewindStart=f;g.useLiveRewindPlay=o;g.useLiveRewindPause=p;g.useLiveRewindReplay=q;g.useLiveRewindScrub=r;g.useLiveRewindSeekableStartTime=s}),98);
__d("VideoPlayerPlaybackTimer.react",["VideoPlayerHooks","VideoPlayerPlaybackTimerBase.react","react","useLiveRewindUtils"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=h||d("react");b=a;function k(){var a=d("useLiveRewindUtils").useLiveRewindSeekableStartTime(),b=(i||(i=d("VideoPlayerHooks"))).useCurrentTimeThrottled(200),e=i.useCurrentTimeThrottled(1e3,function(a){var b=a.getCurrentState();a=a.getPlayheadPosition();return a-b.duration}),f=i.useIsLiveRewindActive();f=f?e:b-a;return j.jsx(c("VideoPlayerPlaybackTimerBase.react"),{currentTime:f,duration:null})}k.displayName=k.name+" [from "+f.id+"]";function l(){var a=(i||(i=d("VideoPlayerHooks"))).useDuration(),b=i.useCurrentTimeThrottled(200);return j.jsx(c("VideoPlayerPlaybackTimerBase.react"),{currentTime:b,duration:a})}l.displayName=l.name+" [from "+f.id+"]";function a(){var a=(i||(i=d("VideoPlayerHooks"))).useIsLive();if(a)return j.jsx(k,{});else return j.jsx(l,{})}a.displayName=a.name+" [from "+f.id+"]";e=b;g["default"]=e}),98);
__d("XControllerURIBuilder",["invariant","URI","gkx","isInternalFBURI"],(function(a,b,c,d,e,f,g,h){var i;a=function(){function a(a,b){this.$1={},this.$2=a,this.$3=b}var b=a.prototype;b.setInt=function(a,b){return this.__setParam(a,"Int",b)};b.setFBID=function(a,b){return this.__setParam(a,"FBID",b)};b.setFloat=function(a,b){return this.__setParam(a,"Float",b)};b.setString=function(a,b){return this.__setParam(a,"String",b)};b.setExists=function(a,b){b===!1&&(b=void 0);return this.__setParam(a,"Exists",b)};b.setBool=function(a,b){return this.__setParam(a,"Bool",b)};b.setBoolVector=function(a,b){return this.__setParam(a,"BoolVector",b)};b.setEnum=function(a,b){return this.__setParam(a,"Enum",b)};b.setPath=function(a,b){return this.__setParam(a,"Path",b)};b.setIntVector=function(a,b){return this.__setParam(a,"IntVector",b)};b.setIntKeyset=function(a,b){return this.__setParam(a,"IntKeyset",b)};b.setIntSet=function(a,b){return this.__setParam(a,"IntSet",b.join(","))};b.setFloatVector=function(a,b){return this.__setParam(a,"FloatVector",b)};b.setFloatSet=function(a,b){return this.__setParam(a,"FloatSet",b.join(","))};b.setStringVector=function(a,b){return this.__setParam(a,"StringVector",b)};b.setStringKeyset=function(a,b){return this.__setParam(a,"StringKeyset",b)};b.setStringSet=function(a,b){return this.__setParam(a,"StringSet",b)};b.setFBIDVector=function(a,b){return this.__setParam(a,"FBIDVector",b)};b.setFBIDSet=function(a,b){return this.__setParam(a,"FBIDSet",b)};b.setFBIDKeyset=function(a,b){return this.__setParam(a,"FBIDKeyset",b)};b.setEnumVector=function(a,b){return this.__setParam(a,"EnumVector",b)};b.setEnumSet=function(a,b){return this.__setParam(a,"EnumSet",b)};b.setEnumKeyset=function(a,b){return this.__setParam(a,"EnumKeyset",b)};b.setIntToIntMap=function(a,b){return this.__setParam(a,"IntToIntMap",b)};b.setIntToFloatMap=function(a,b){return this.__setParam(a,"IntToFloatMap",b)};b.setIntToStringMap=function(a,b){return this.__setParam(a,"IntToStringMap",b)};b.setIntToBoolMap=function(a,b){return this.__setParam(a,"IntToBoolMap",b)};b.setStringToIntMap=function(a,b){return this.__setParam(a,"StringToIntMap",b)};b.setStringToFloatMap=function(a,b){return this.__setParam(a,"StringToFloatMap",b)};b.setStringToStringMap=function(a,b){return this.__setParam(a,"StringToStringMap",b)};b.setStringToNullableStringMap=function(a,b){return this.__setParam(a,"StringToNullableStringMap",b)};b.setStringToBoolMap=function(a,b){return this.__setParam(a,"StringToBoolMap",b)};b.setStringToEnumMap=function(a,b){return this.__setParam(a,"StringToEnumMap",b)};b.setEnumToStringVectorMap=function(a,b){return this.__setParam(a,"EnumToStringVectorMap",b)};b.setEnumToStringMap=function(a,b){return this.__setParam(a,"EnumToStringMap",b)};b.setEnumToBoolMap=function(a,b){return this.__setParam(a,"EnumToBoolMap",b)};b.setEnumToEnumMap=function(a,b){return this.__setParam(a,"EnumToEnumMap",b)};b.setEnumToIntMap=function(a,b){return this.__setParam(a,"EnumToIntMap",b)};b.setEnumToFBIDVectorMap=function(a,b){return this.__setParam(a,"EnumToFBIDVectorMap",b)};b.setStringToIntDict=function(a,b){return this.__setParam(a,"StringToIntDict",b)};b.setStringToNullableIntDict=function(a,b){return this.__setParam(a,"StringToNullableIntDict",b)};b.setStringToFloatDict=function(a,b){return this.__setParam(a,"StringToFloatDict",b)};b.setStringToStringKeysetDict=function(a,b){return this.__setParam(a,"StringToStringKeysetDict",b)};b.setStringToNullableFloatDict=function(a,b){return this.__setParam(a,"StringToNullableFloatDict",b)};b.setStringToStringDict=function(a,b){return this.__setParam(a,"StringToStringDict",b)};b.setStringToNullableStringDict=function(a,b){return this.__setParam(a,"StringToNullableStringDict",b)};b.setStringToBoolDict=function(a,b){return this.__setParam(a,"StringToBoolDict",b)};b.setStringToEnumDict=function(a,b){return this.__setParam(a,"StringToEnumDict",b)};b.setEnumToIntDict=function(a,b){return this.__setParam(a,"EnumToIntDict",b)};b.setEnumToStringDict=function(a,b){return this.__setParam(a,"EnumToStringDict",b)};b.setHackType=function(a,b){return this.__setParam(a,"HackType",b)};b.setTypeAssert=function(a,b){return this.__setParam(a,"TypeAssert",b)};b.__validateRequiredParamsExistence=function(){for(var a in this.$3)!this.$3[a].required||Object.prototype.hasOwnProperty.call(this.$1,a)||h(0,903,a)};b.setParams=function(a){for(var b in a){this.__assertParamExists(b);var c=this.$3[b].type;this.__setParam(b,c,a[b])}return this};b.__assertParamExists=function(a){a in this.$3||h(0,37339,a)};b.__setParam=function(a,b,c){this.__assertParamExists(a);var d=this.$3[a].type,e={StringOrPFBID:"String",IntOrPFBID:"Int",FBIDOrPFBID:"FBID",PaymentLegacyAdAccountID:"Int"};e=e[d];d===b||e===b||h(0,37340,a,b,d);this.__setParamInt(a,c);return this};b.__setParamInt=function(a,b){this.$1[a]=b};b.getRequest_LEGACY_UNTYPED=function(a){return a.setReplaceTransportMarkers().setURI(this.getURI())};b.setPreviousActorIsPageVoice=function(a){this.__setParamInt("paipv",a?1:0);return this};b.getURI=function(){this.__validateRequiredParamsExistence();var a={},b="",d=/^(.*)?\{(\?)?(\*)?(.+?)\}(.*)?$/,e=this.$2.split("/"),f=!1;for(var g=0;g<e.length;g++){var j=e[g];if(j==="")continue;var k=d.exec(j);if(!k)b+="/"+j;else{j=k[2]==="?";var l=k[4],m=this.$3[l];m||h(0,11837,l,this.$2);if(j&&f)continue;if(this.$1[l]==null&&j){f=!0;continue}j=this.$1[l]!=null?this.$1[l]:m.defaultValue;j!=null||h(0,907,l);m=k[1]?k[1]:"";k=k[5]?k[5]:"";b+="/"+m+j+k;a[l]=!0}}this.$2.slice(-1)==="/"&&(b+="/");b===""&&(b="/");m=new(i||(i=c("URI")))(b);for(j in this.$1){k=this.$1[j];if(!a[j]&&k!=null){l=this.$3[j];m.addQueryData(j,l&&l.type==="Exists"?null:k)}}return m};b.getLookasideURI=function(){var a="lookaside.facebook.com";c("isInternalFBURI")((i||(i=c("URI"))).getRequestURI())?a="lookaside.internalfb.com":c("gkx")("21116")&&(a="lookaside.internmc.facebook.com");return this.getURI().setDomain(a).setProtocol("https")};a.create=function(b,c){return function(){return new a(b,c)}};return a}();a.prototype.getRequest=function(a){return this.getRequest_LEGACY_UNTYPED(a)};g["default"]=a}),98);
__d("XRequest",["invariant"],(function(a,b,c,d,e,f,g){var h=function a(b,c,d){var e;switch(b){case"Bool":e=c&&c!=="false"&&c!=="0"||!1;break;case"Int":e=c.toString();/-?\d+/.test(e)||g(0,11839,c);break;case"Float":e=parseFloat(c,10);isNaN(e)&&g(0,11840,c);break;case"FBID":e=c.toString();for(var f=0;f<e.length;++f){var h=e.charCodeAt(f);48<=h&&h<=57||g(0,11841,c)}break;case"String":e=c.toString();break;case"Enum":d===0?e=a("Int",c,null):d===1?e=a("String",c,null):d===2?e=c:g(0,5044,d);break;default:if(h=/^Nullable(\w+)$/.exec(b))c===null?e=null:e=a(h[1],c,d);else if(f=/^(\w+)Vector$/.exec(b)){!Array.isArray(c)?(e=c.toString(),e=e===""?[]:e.split(",")):e=c;var i=f[1];typeof i==="string"||g(0,5045);e=e.map(function(b){return a(i,b,d&&d.member)})}else if(h=/^(\w+)(Set|Keyset)$/.exec(b))!Array.isArray(c)?(e=c.toString(),e=e===""?[]:e.split(",")):e=c,e=e.reduce(function(a,b){a[b]=b;return a},{}),i=h[1],typeof i==="string"||g(0,5045),e=Object.keys(e).map(function(b){return a(i,e[b],d&&d.member)});else if(f=/^(\w+)To(\w+)Map$/.exec(b)){e={};var j=f[1],k=f[2];typeof j==="string"&&typeof k==="string"||g(0,5045);Object.keys(c).forEach(function(b){e[a(j,b,d&&d.key)]=a(k,c[b],d&&d.value)})}else g(0,11842,b)}return e};a=function(){function a(a,b,c){var d=this;this.$1=b;this.$2=babelHelpers["extends"]({},c.getQueryData());b=a.split("/").filter(function(a){return a});a=c.getPath().split("/").filter(function(a){return a});var e;for(var f=0;f<b.length;++f){var h=/^\{(\?)?(\*)?(\w+)\}$/.exec(b[f]);if(!h){b[f]===a[f]||g(0,5047,c.getPath());continue}var i=!!h[1],j=!!h[2];!j||f===b.length-1||g(0,11843,e);e=h[3];Object.prototype.hasOwnProperty.call(this.$1,e)||g(0,11844,e);this.$1[e].required?i&&g(0,5050,e):i||this.$1[e].defaultValue!=null||g(0,5057,e);a[f]&&(this.$2[e]=j?a.slice(f).join("/"):a[f])}Object.keys(this.$1).forEach(function(a){!d.$1[a].required||Object.prototype.hasOwnProperty.call(d.$2,a)||g(0,5051)})}var b=a.prototype;b.getExists=function(a){return this.$2[a]!==void 0};b.getBool=function(a){return this.$3(a,"Bool")};b.getInt=function(a){return this.$3(a,"Int")};b.getFloat=function(a){return this.$3(a,"Float")};b.getFBID=function(a){return this.$3(a,"FBID")};b.getString=function(a){return this.$3(a,"String")};b.getEnum=function(a){return this.$3(a,"Enum")};b.getOptionalInt=function(a){return this.$4(a,"Int")};b.getOptionalFloat=function(a){return this.$4(a,"Float")};b.getOptionalFBID=function(a){return this.$4(a,"FBID")};b.getOptionalString=function(a){return this.$4(a,"String")};b.getOptionalEnum=function(a){return this.$4(a,"Enum")};b.getIntVector=function(a){return this.$3(a,"IntVector")};b.getFloatVector=function(a){return this.$3(a,"FloatVector")};b.getFBIDVector=function(a){return this.$3(a,"FBIDVector")};b.getStringVector=function(a){return this.$3(a,"StringVector")};b.getEnumVector=function(a){return this.$3(a,"EnumVector")};b.getOptionalIntVector=function(a){return this.$4(a,"IntVector")};b.getOptionalFloatVector=function(a){return this.$4(a,"FloatVector")};b.getOptionalFBIDVector=function(a){return this.$4(a,"FBIDVector")};b.getOptionalStringVector=function(a){return this.$4(a,"StringVector")};b.getOptionalEnumVector=function(a){return this.$4(a,"EnumVector")};b.getIntSet=function(a){return this.$3(a,"IntSet")};b.getFBIDSet=function(a){return this.$3(a,"FBIDSet")};b.getFBIDKeyset=function(a){return this.$3(a,"FBIDKeyset")};b.getStringSet=function(a){return this.$3(a,"StringSet")};b.getEnumKeyset=function(a){return this.$3(a,"EnumKeyset")};b.getOptionalIntSet=function(a){return this.$4(a,"IntSet")};b.getOptionalFBIDSet=function(a){return this.$4(a,"FBIDSet")};b.getOptionalFBIDKeyset=function(a){return this.$4(a,"FBIDKeyset")};b.getOptionalStringSet=function(a){return this.$4(a,"StringSet")};b.getEnumToBoolMap=function(a){return this.$3(a,"EnumToBoolMap")};b.getEnumToEnumMap=function(a){return this.$3(a,"EnumToEnumMap")};b.getEnumToFloatMap=function(a){return this.$3(a,"EnumToFloatMap")};b.getEnumToIntMap=function(a){return this.$3(a,"EnumToIntMap")};b.getEnumToStringMap=function(a){return this.$3(a,"EnumToStringMap")};b.getIntToBoolMap=function(a){return this.$3(a,"IntToBoolMap")};b.getIntToEnumMap=function(a){return this.$3(a,"IntToEnumMap")};b.getIntToFloatMap=function(a){return this.$3(a,"IntToFloatMap")};b.getIntToIntMap=function(a){return this.$3(a,"IntToIntMap")};b.getIntToStringMap=function(a){return this.$3(a,"IntToStringMap")};b.getStringToBoolMap=function(a){return this.$3(a,"StringToBoolMap")};b.getStringToEnumMap=function(a){return this.$3(a,"StringToEnumMap")};b.getStringToFloatMap=function(a){return this.$3(a,"StringToFloatMap")};b.getStringToIntMap=function(a){return this.$3(a,"StringToIntMap")};b.getStringToStringMap=function(a){return this.$3(a,"StringToStringMap")};b.getOptionalEnumToBoolMap=function(a){return this.$4(a,"EnumToBoolMap")};b.getOptionalEnumToEnumMap=function(a){return this.$4(a,"EnumToEnumMap")};b.getOptionalEnumToFloatMap=function(a){return this.$4(a,"EnumToFloatMap")};b.getOptionalEnumToIntMap=function(a){return this.$4(a,"EnumToIntMap")};b.getOptionalEnumToStringMap=function(a){return this.$4(a,"EnumToStringMap")};b.getOptionalIntToBoolMap=function(a){return this.$4(a,"IntToBoolMap")};b.getOptionalIntToEnumMap=function(a){return this.$4(a,"IntToEnumMap")};b.getOptionalIntToFloatMap=function(a){return this.$4(a,"IntToFloatMap")};b.getOptionalIntToIntMap=function(a){return this.$4(a,"IntToIntMap")};b.getOptionalIntToStringMap=function(a){return this.$4(a,"IntToStringMap")};b.getOptionalStringToBoolMap=function(a){return this.$4(a,"StringToBoolMap")};b.getOptionalStringToEnumMap=function(a){return this.$4(a,"StringToEnumMap")};b.getOptionalStringToFloatMap=function(a){return this.$4(a,"StringToFloatMap")};b.getOptionalStringToIntMap=function(a){return this.$4(a,"StringToIntMap")};b.getOptionalStringToStringMap=function(a){return this.$4(a,"StringToStringMap")};b.getEnumToNullableEnumMap=function(a){return this.$3(a,"EnumToNullableEnumMap")};b.getEnumToNullableFloatMap=function(a){return this.$3(a,"EnumToNullableFloatMap")};b.getEnumToNullableIntMap=function(a){return this.$3(a,"EnumToNullableIntMap")};b.getEnumToNullableStringMap=function(a){return this.$3(a,"EnumToNullableStringMap")};b.getIntToNullableEnumMap=function(a){return this.$3(a,"IntToNullableEnumMap")};b.getIntToNullableFloatMap=function(a){return this.$3(a,"IntToNullableFloatMap")};b.getIntToNullableIntMap=function(a){return this.$3(a,"IntToNullableIntMap")};b.getIntToNullableStringMap=function(a){return this.$3(a,"IntToNullableStringMap")};b.getStringToNullableEnumMap=function(a){return this.$3(a,"StringToNullableEnumMap")};b.getStringToNullableFloatMap=function(a){return this.$3(a,"StringToNullableFloatMap")};b.getStringToNullableIntMap=function(a){return this.$3(a,"StringToNullableIntMap")};b.getStringToNullableStringMap=function(a){return this.$3(a,"StringToNullableStringMap")};b.getOptionalEnumToNullableEnumMap=function(a){return this.$4(a,"EnumToNullableEnumMap")};b.getOptionalEnumToNullableFloatMap=function(a){return this.$4(a,"EnumToNullableFloatMap")};b.getOptionalEnumToNullableIntMap=function(a){return this.$4(a,"EnumToNullableIntMap")};b.getOptionalEnumToNullableStringMap=function(a){return this.$4(a,"EnumToNullableStringMap")};b.getOptionalIntToNullableEnumMap=function(a){return this.$4(a,"IntToNullableEnumMap")};b.getOptionalIntToNullableFloatMap=function(a){return this.$4(a,"IntToNullableFloatMap")};b.getOptionalIntToNullableIntMap=function(a){return this.$4(a,"IntToNullableIntMap")};b.getOptionalIntToNullableStringMap=function(a){return this.$4(a,"IntToNullableStringMap")};b.getOptionalStringToNullableEnumMap=function(a){return this.$4(a,"StringToNullableEnumMap")};b.getOptionalStringToNullableFloatMap=function(a){return this.$4(a,"StringToNullableFloatMap")};b.getOptionalStringToNullableStringMap=function(a){return this.$4(a,"StringToNullableStringMap")};b.$3=function(a,b){this.$5(a,b);var c=this.$1[a];if(!Object.prototype.hasOwnProperty.call(this.$2,a)&&c.defaultValue!=null){c.required&&g(0,5052);return h(b,c.defaultValue,c.enumType)}c.required||b==="Bool"||c.defaultValue!=null||g(0,11845,b,a,b,a);return h(b,this.$2[a],c.enumType)};b.$4=function(a,b){this.$5(a,b);var c=this.$1[a];c.required&&g(0,11846,b,a,b,a);c.defaultValue&&g(0,5052);return Object.prototype.hasOwnProperty.call(this.$2,a)?h(b,this.$2[a],c.enumType):null};b.$5=function(a,b){Object.prototype.hasOwnProperty.call(this.$1,a)||g(0,37317,a),this.$1[a].type===b||g(0,11848,a,b,this.$1[a].type)};return a}();f["default"]=a}),66);
__d("XController",["XControllerURIBuilder","XRequest"],(function(a,b,c,d,e,f,g){a=function(){function a(a,b){this.$1=a,this.$2=b}var b=a.prototype;b.getURIBuilder=function(a){var b=this,d=new(c("XControllerURIBuilder"))(this.$1,this.$2);if(a){var e=this.getRequest(a);Object.keys(this.$2).forEach(function(a){var c=b.$2[a],f="";!c.required&&!Object.prototype.hasOwnProperty.call(c,"defaultValue")&&(f="Optional");f="get"+f+c.type;f=e[f](a);if(f==null||Object.prototype.hasOwnProperty.call(c,"defaultValue")&&f===c.defaultValue)return;c="set"+c.type;d[c](a,f)})}return d};b.getRequest=function(a){return new(c("XRequest"))(this.$1,this.$2,a)};a.create=function(b,c){return new a(b,c)};return a}();g["default"]=a}),98);
__d("XLogoutControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/logout.php",Object.freeze({}),void 0);b=a;g["default"]=b}),98);
__d("getPasskeyCredentials",["nullthrows"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("nullthrows")(navigator.credentials)}g.getPasskeyCredentials=a}),98);
__d("polarisGetUserFromUserInfo",["normalizr"],(function(a,b,c,d,e,f,g){"use strict";var h=new(d("normalizr").schema.Entity)("userInfo",{},{idAttribute:function(a){return String(a.pk)},processStrategy:function(a){var b;b=(b=a.mutual_followers_count)!=null?b:0;var c={additional_count:0,usernames:[]};if(b>0){var d;c.usernames=(d=(d=a.profile_context_links_with_user_ids)==null?void 0:d.map(function(a){return a.username}).filter(Boolean))!=null?d:[];c.additional_count=b-c.usernames.length}return{aiAgentOwnerUsername:a.ai_agent_owner_username,aiAgentType:a.ai_agent_type,bio:a.biography,counts:{followedBy:a.follower_count,follows:a.following_count,media:a.media_count},fullName:a.full_name,id:String(a.pk),interopMessagingUserFbid:a.interop_messaging_user_fbid!=null&&a.interop_messaging_user_fbid!==""?String(a.interop_messaging_user_fbid):void 0,isPrivate:a.is_private,isUnpublished:a.is_unpublished,isUserInCanada:a.is_in_canada,isVerified:a.is_verified,mutualFollowers:c,profilePictureUrl:a.profile_pic_url,shouldRemoveMessageButtonRiskyInteraction:a.remove_message_entrypoint,showAccountTransparencyDetails:a.show_account_transparency_details,transparencyLabel:(d=a.transparency_label)!=null?d:void 0,transparencyProduct:(b=a.transparency_product)!=null?b:void 0,username:a.username,website:a.external_url,websiteLinkshimmed:a.external_lynx_url}}});function a(a){return d("normalizr").normalize(a,h).entities.userInfo}g["default"]=a}),98);
__d("polarisManifestHasUnsupportedCodecs",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){b===void 0&&(b="mse");if(b==="mmse"&&window.ManagedMediaSource==null||b!=="mmse"&&window.MediaSource==null||a==null||typeof a!=="string")return!1;var c=/mimeType=\"([^\"]*)\"\s*codecs=\"([^\"]*)\"/g,d;while(d=c.exec(a)){d=d[1]+'; codecs="'+d[2]+'"';if(b==="mmse"){if(!window.ManagedMediaSource.isTypeSupported(d))return!0}else if(!window.MediaSource.isTypeSupported(d))return!0}return!1}f["default"]=a}),66);
__d("usePolarisShowToast",["PolarisReactRedux.react","PolarisToastActions","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";function a(){var a=d("react-compiler-runtime").c(2),b=d("PolarisReactRedux.react").useDispatch(),c;a[0]!==b?(c=function(a){b(d("PolarisToastActions").showToast(a))},a[0]=b,a[1]=c):c=a[1];return c}g["default"]=a}),98);