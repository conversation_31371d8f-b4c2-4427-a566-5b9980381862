;/*FB_PKG_DELIM*/

__d("BaseMiddot.react",["react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){return i.jsxs("span",babelHelpers["extends"]({},a,{children:[i.jsx("span",babelHelpers["extends"]({className:"xzpqnlu xjm9jq1 x6ikm8r x10wlt62 x10l6tqk x1i1rx1s"},{children:"\xa0"})),i.jsx("span",{"aria-hidden":"true",children:" \xb7 "})]}))}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("E2EELinkPreviewXMADataType",[],(function(a,b,c,d,e,f){a=Object.freeze({FALLBACK:1,FB_CLIENT_FETCH:2,OEMBED:4});f["default"]=a}),66);
__d("IGDChannelsGating",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("gkx")("24019")===!0}g.isChannelsTabInProfessionalInboxEnabled=a}),98);
__d("IgTags_InboxFolder",[],(function(a,b,c,d,e,f){a=Object.freeze({PRIMARY:0,GENERAL:1,ONLYIN3P:2,SUBSCRIBER:3,AD_RESPONSES:4,PAID_SUBS:5,AI_BOT:6,CUSTOM:7,SCHOOLS:8,DONE:9});f["default"]=a}),66);
__d("IGDProfessionalIGFolderContext.react",["I64","IgTags_InboxFolder","LSIntEnum","emptyFunction","react","react-compiler-runtime","usePolarisViewer"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=h||(h=d("react")),l=h,m=l.createContext;l.useCallback;var n=l.useContext,o=l.useState,p=(i||(i=d("LSIntEnum"))).ofNumber(c("IgTags_InboxFolder").PRIMARY),q=m(p),r=m(c("emptyFunction"));function a(a){var b=d("react-compiler-runtime").c(6);a=a.children;var c=o(p),e=c[0],f=c[1];b[0]===Symbol["for"]("react.memo_cache_sentinel")?(c=function(a){f(function(b){return(j||(j=d("I64"))).equal(b,a)?b:a})},b[0]=c):c=b[0];c=c;b[1]!==a?(c=k.jsx(r.Provider,{value:c,children:a}),b[1]=a,b[2]=c):c=b[2];b[3]!==e||b[4]!==c?(a=k.jsx(q.Provider,{value:e,children:c}),b[3]=e,b[4]=c,b[5]=a):a=b[5];return a}function b(){var a=c("usePolarisViewer")();a=(a=a==null?void 0:a.isProfessionalAccount)!=null?a:!1;return a}function e(){var a=c("usePolarisViewer")();a=(a=a==null?void 0:a.isBusinessAccount)!=null?a:!1;return a}function f(){return n(q)}function s(){return n(r)}g.IGDProfessionalFolderContextProvider=a;g.useIsProfessionalIGD=b;g.useIsBusinessAccount=e;g.useIGDProfessionalFolder=f;g.useIGDProfessionalFolderSetter=s}),98);
__d("IGDSEmoji.react",["react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(6),c=a.emoji;a=a.fontSize;var e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e="x15yrdiy",b[0]=e):e=b[0];var f;b[1]!==a?(f=a!=null?{fontSize:a}:void 0,b[1]=a,b[2]=f):f=b[2];b[3]!==c||b[4]!==f?(a=i.jsx("span",{className:e,style:f,children:c}),b[3]=c,b[4]=f,b[5]=a):a=b[5];return a}g["default"]=a}),98);
__d("LSCommunityBitOffset",["I64","LSBitFlag"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){if(a>=128)return d("LSBitFlag").has((h||(h=d("I64"))).lsl_(h.one,a-128),b.capabilities3);return a>=64?d("LSBitFlag").has((h||(h=d("I64"))).lsl_(h.one,a-64),b.capabilities2):d("LSBitFlag").has((h||(h=d("I64"))).lsl_(h.one,a),b.capabilities)}g.has=a}),98);
__d("LSCommunityChannelSyncSource",[],(function(a,b,c,d,e,f){a=Object.freeze({DEFAULT:0,THREAD_LIST:1,UNREAD_INDICATOR:2});f["default"]=a}),66);
__d("LSFeatureLimitsType",[],(function(a,b,c,d,e,f){a=Object.freeze({READ_ONLY:1,GENERIC_READ_ONLY_BLOCK:2,MESSAGE_SEND:4,MESSENGER_ONLY_ACCOUNTS_READ_ONLY_BLOCK:8,MESSAGE_SEND_PRIVATE:16,MESSAGE_SEND_PUBLIC:32,MESSAGE_SEND_FROM_PAGE:64,COMMERCE_MESSAGE_SEND:128,PAGE_MESSAGING:256,APPEALABLE_MESSAGE_SEND:512,SCREEN_CAPTURE_VIDEO_CALL:1024,MESSENGER_APPEALABLE_MESSAGE_SEND:2048});f["default"]=a}),66);
__d("genArrayBufferFromFile",["Promise"],(function(a,b,c,d,e,f){"use strict";var g;function a(a){return"arrayBuffer"in a&&typeof a.arrayBuffer==="function"?a.arrayBuffer():new(g||(g=b("Promise")))(function(b,c){var d=new FileReader();d.onload=function(){var a=d.result;a instanceof ArrayBuffer?b(a):c()};d.onerror=function(){c(d.error)};d.readAsArrayBuffer(a)})}f["default"]=a}),66);
__d("genIsNonAnimatedWebPFile",["genArrayBufferFromFile"],(function(a,b,c,d,e,f,g){"use strict";var h=8;function a(a){return c("genArrayBufferFromFile")(a).then(function(a){a=new Uint8Array(a);var b=a.slice(12,16).reduce(function(a,b,c){return a+String.fromCharCode(b)},"");if(b==="VP8X"){b=a[20];b=b.toString(2).padStart(8,"0");if(b[6]==="1")return!i(a)}return!0})}function i(a){var b=12,c=0;while(c<2&&b+h<a.length){var d=j(a,b);d==="ANMF"&&c++;d=k(a,b+4);var e=d%2;d=h+d+e;b+=d}return c>1}function j(a,b){return a.slice(b,b+4).reduce(function(a,b,c){return a+String.fromCharCode(b)},"")}function k(a,b){return parseInt(a.slice(b,b+4).reduce(function(a,b,c){return b.toString(2).padStart(8,"0")+a},""),2)}g["default"]=a}),98);
__d("LSGetAttachmentType",["LSIntEnum","MessagingAttachmentType","asyncToGeneratorRuntime","cometIsMimeTypeForMedia","genIsNonAnimatedWebPFile"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){return a.type==="image/webp"?(h||(h=d("LSIntEnum"))).ofNumber((yield c("genIsNonAnimatedWebPFile")(a))?c("MessagingAttachmentType").IMAGE:c("MessagingAttachmentType").VIDEO):j(a)});return i.apply(this,arguments)}function j(a){a=a.type;if(d("cometIsMimeTypeForMedia").isMimeTypeForPhoto(a))return(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").IMAGE);else if(a==="audio/wav")return(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").AUDIO);else if(a==="image/gif")return(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").ANIMATED_IMAGE);else if(d("cometIsMimeTypeForMedia").isMimeTypeForVideo(a))return(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").VIDEO);else return(h||(h=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").FILE)}g.LSGetAttachmentType=a;g.LSGetAttachmentTypeSyncButLessAccurate=j}),98);
__d("LSMessageContentType",[],(function(a,b,c,d,e,f){a=Object.freeze({TEXT:1,MEDIA_PREVIEW:2,AUDIO_CLIP:4,XMA_FALLBACK:8,LWA_WAVE:16,ADMIN:32,FILE_ATTACHMENT:64,GLYPH:128,TOMBSTONE:256,STORY_REPLY:512,SINGLE_XMA:1024,HSCROLL_XMA:2048,STICKER:4096,FORWARD_INDICATOR:8192,ANIMATED_IMAGE:16384,NULL_STATE:32768,ENCRYPTION_PLACEHOLDER:65536,FUTUREPROOF_PLACEHOLDER:131072,UNAVAILABLE_PLACEHOLDER:262144,LOCATION_PLACEHOLDER:524288,POLL_CREATION_PLACEHOLDER:1048576,CONTACT_SHARE_PLACEHOLDER:2097152,STORY_MENTION_PLACEHOLDER:4194304,META_AI_MESSAGE_PLACEHOLDER:8388608,BUMP_MESSAGE_PLACEHOLDER:16777216,BUMP_MESSAGE_ORIGINAL_UNAVAILABLE_PLACEHOLDER:33554432,STICKER_RECEIVER_FETCH_UNSUPPORTED_PLACEHOLDER:67108864,RECEIVER_FETCH:134217728,STORY_MENTION:268435456,XMA_RECEIVER_FETCH:536870912,NOTE_MENTION_PLACEHOLDER:1073741824,POST_MENTION_PLACEHOLDER:2147483648,LIVE_LOCATION_PLACEHOLDER:4294967296,MESSENGER_MEMORY_PLACEHOLDER:8589934592});f["default"]=a}),66);
__d("LSMessagingInitiatingSource",[],(function(a,b,c,d,e,f){a=Object.freeze({FACEBOOK_CHAT:0,FACEBOOK_INBOX:1,ROOMS_SIDE_CHAT:2,FACEBOOK_FULLSCREEN:3,NONE:4,CREATOR_MESSAGING_BIIM:5,CREATOR_MESSAGING_FBM:6,CREATOR_MESSAGING_MBS_WEB:7});f["default"]=a}),66);
__d("LSPlatformWaitForTaskCompletion",["I64","Promise","asyncToGeneratorRuntime","emptyFunction","err","justknobx","promiseDone","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=["acs_sync"];function a(a,b,c,d){return k.apply(this,arguments)}function k(){k=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,e,g,k){var l=new Set(),m=[],n=0,o=!0,p,q,r=new(i||(i=b("Promise")))(function(a,b){p=a,q=b}),s=c("uuidv4")(),t=!0,u=(yield a.runInTransaction(function(){var i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(i){yield i.mailbox_task_completion_notification_context.add({notificationName:g,notificationScopeKey:s});var r=a.tables.pending_tasks.subscribe(function(a,b){a=(h||(h=d("I64"))).to_string(a[0]);if(b.operation==="add"||b.operation==="put"){var e=b.value;if(c("justknobx")._("2903")&&j.some(function(a){return e.queueName.startsWith(a)}))return;if(k!=null&&e.queueName!==k)return;l.add(a)}else l["delete"](a)}),v=c("emptyFunction"),w=a.tables.mailbox_task_completion_api_tasks.subscribe(function(a,b){if(b.operation==="add"||b.operation==="put"&&b.value.notificationScopeKey===s){a=(h||(h=d("I64"))).to_string(b.value.taskId);l["delete"](a);b=b.value.success;b==null?t&&(m.push(a),n+=1):(o=b&&o,n-=1)}});v=a.subscribeToCommit(function(){n===0&&l.size===0&&(w(),r(),v(),c("promiseDone")(a.runInTransaction(function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){for(var b of m)yield a.mailbox_task_completion_api_tasks["delete"]((h||(h=d("I64"))).of_string(b))});return function(b){return a.apply(this,arguments)}}(),"readwrite","background",void 0,f.id+":150")),o?p(u):q(c("err")("Not all tasks for "+g+" succeeded")))});try{var x=(yield e(i));yield i.mailbox_task_completion_notification_context["delete"](s);return x}catch(a){v();w();r();throw a}});return function(a){return i.apply(this,arguments)}}(),"readwrite",void 0,void 0,f.id+":66"));t=!1;return r});return k.apply(this,arguments)}g["default"]=a}),98);
__d("LSThreadAttributionStore",["I64"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Map();function a(a,b){var c;a=(c=(c=i.get(b))!=null?c:a)!=null?c:{type:"MWLSEntrypoint",value:"unknown"};i["delete"](b);return a}function b(a,b){a=(b=(b=i.get(b))!=null?b:a)!=null?b:{type:"MWLSEntrypoint",value:"unknown"};return a}function c(a,b){i.set(a,{type:"MWLSEntrypoint",value:b})}function e(a,b){b=(h||(h=d("I64"))).of_string(b);i.set(a,{type:"LSThreadAttribution",value:b})}function f(a){i.set("",{type:"MWLSEntrypoint",value:a})}function j(){var a=i.get("");if(a==null||a.type==="LSThreadAttribution")return"unknown";else return a.value}g.getSourceAndResetAttribution=a;g.getSourceWithoutReset=b;g.setSource=c;g.setLSMessagingThreadAttribution=e;g.setSourceForNewThread=f;g.getSourceForNewThread=j}),98);
__d("LSThreadsRangesQueryStoredProcedure",["LSSynchronousPromise","LSThreadsRangesQuery","Promise","cr:8709"],(function(a,b,c,d,e,f,g){var h;function a(a,e){a=a.storedProcedure(c("LSThreadsRangesQuery"),e.parentThreadKey,e.isLoadingBefore,e.isLoadingAfter,e.minThreadKey,e.maxThreadKey,e.maxLastActivityTimestampMs,e.minLastActivityTimestampMs,e.additionalPagesToFetch,e.shouldSkipE2eeThreadsRanges);return(h||(h=b("Promise"))).resolve(d("LSSynchronousPromise").maybeExtractValueIfSynchronousPromise(a))}g["default"]=a}),98);
__d("LSUpdateOrInsertReactionV2",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[],d=[];return b.sequence([function(d){return b.db.table(226).fetch([[[a[0],a[1],a[2]]]]).next().then(function(d,e){var f=d.done;d=d.value;return f?b.db.table(12).fetch([[[a[1]]],"messageId"]).next().then(function(d,e){var c=d.done;d=d.value;return c?0:(e=d.item,b.sequence([function(c){return b.forEach(b.filter(b.db.table(226).fetch([[[a[0],a[1],a[2]]],"optimistic"]),function(c){return b.i64.eq(c.threadKey,a[0])&&c.messageId===a[1]&&b.i64.eq(c.reactionFbid,a[2])&&b.i64.lt(c.authorityLevel,a[5])}),function(a){return a["delete"]()})},function(c){return b.db.table(226).add({messageId:a[1],threadKey:a[0],reactionFbid:a[2],messageTimestamp:e.timestampMs,authorityLevel:a[5],count:a[4],viewerIsReactor:a[6],viewerReactionTimestampMs:a[7],lastUpdatedTimestampMs:a[8]})}]))}):(e=d.item,c[2]=e.lastUpdatedTimestampMs,c[1]=e.authorityLevel,b.i64.eq(a[5],b.i64.cast([0,20]))||b.i64.eq(c[1],b.i64.cast([0,80]))&&b.i64.eq(a[5],b.i64.cast([0,80]))||b.i64.eq(c[1],b.i64.cast([0,20]))&&b.i64.eq(a[5],b.i64.cast([0,80]))&&b.i64.le(b.i64.add(c[2],b.i64.cast([0,2e3])),a[8])||b.i64.eq(c[1],b.i64.cast([0,20]))&&b.i64.eq(a[5],b.i64.cast([0,60]))||b.i64.eq(c[1],b.i64.cast([0,60]))&&b.i64.eq(a[5],b.i64.cast([0,80]))&&b.i64.le(c[2],a[8])?c[3]=!0:c[3]=!1,c[3]?b.db.table(226).put({messageId:a[1],threadKey:a[0],reactionFbid:a[2],messageTimestamp:e.messageTimestamp,reactionLiteral:a[3],count:a[4],authorityLevel:a[5],viewerIsReactor:a[6],viewerReactionTimestampMs:b.i64.cast([0,0]),lastUpdatedTimestampMs:a[8]}):b.resolve())})},function(a){return b.resolve(d)}])}a.__sproc_name__="LSMailboxUpdateOrInsertReactionV2StoredProcedure";a.__tables__=["reactions_v2","messages"];e.exports=a}),null);
__d("MWCMThreadTypes.react",["I64","LSCommunityBitOffset","LSCommunityChannelSyncSource","LSIntEnum","MessagingThreadSubtype","MetaConfig","isUnjoinedCMThread"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=[c("LSCommunityChannelSyncSource").DEFAULT,c("LSCommunityChannelSyncSource").THREAD_LIST];function a(a){return(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(18))||(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(23))||(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(21))}function k(a){return(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(18))||(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(19))}function b(a){return(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(23))||(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(24))}function e(a){return a==null?!1:(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(c("MessagingThreadSubtype").MARKETPLACE_THREAD))}function f(a){return a==null?!1:(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(c("MessagingThreadSubtype").IG_CREATOR_SUBSCRIBER_BROADCAST_CHAT))}function l(a){return(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(21))||(h||(h=d("I64"))).equal(a,(i||(i=d("LSIntEnum"))).ofNumber(22))}function m(a,b){var e=d("LSCommunityBitOffset").has(1,b),f=(h||(h=d("I64"))).equal(a.threadType,(i||(i=d("LSIntEnum"))).ofNumber(21)),g=l(a.threadType)&&c("MetaConfig")._("35");b=b.takedownState!=null&&(h||(h=d("I64"))).equal(b.takedownState,(i||(i=d("LSIntEnum"))).ofNumber(1));return a.syncSource!=null&&!j.includes((h||(h=d("I64"))).to_int32(a.syncSource))?!1:!b&&(k(a.threadType)||g||e||f||a.hasPendingInvitation===!0)}g.isJoinedCMThread=a;g.isUnjoinedCMThread=d("isUnjoinedCMThread").isUnjoinedCMThread;g.isStandardCMThread=k;g.isBroadcastThread=b;g.isMarketplaceThread=e;g.isIGBroadcastChannelThread=f;g.isPrivateThread=l;g.isThreadEnabled=m}),98);
__d("MWMediaRenderQplUtils",["CurrentUser","E2EELinkPreviewXMADataType","I64","LSAuthorityLevel","LSBitFlag","LSIntEnum","LSMessageContentType","LSMessageReplySourceType","LSMessageReplySourceTypeV2","LSMessagingInitiatingSource","LSReplyMessageAttachmentType","LSXmaContentType","MAWLoggerUtils","MNLSXMALayoutType","MessagingAttachmentType","MessagingThreadType","QPLUserFlow","ServerTime","VisibilityAPI","VisibilityState","justknobx","mergeDeep","objectEntries","performanceAbsoluteNow","qpl","react","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=i||d("react"),m=l.useEffect,n=l.useLayoutEffect,o=l.useMemo,p=l.useRef,q=60*60*24,r=3*q,s=7*q,t=2*s,u=4*s,v=c("qpl")._(25302828,"2195"),w=c("justknobx")._("2945"),x=1e3,y=c("objectEntries")(c("LSMessageContentType")).reduce(function(a,b){var c=b[0];b=b[1];a.set(b,c);return a},new Map()),z=c("objectEntries")(c("LSReplyMessageAttachmentType")).reduce(function(a,b){var c=b[0];b=b[1];a.set(b,c);return a},new Map());function a(a){return A(a.displayedContentTypes)}function A(a){if(d("LSBitFlag").has((j||(j=d("LSIntEnum"))).ofNumber(1024),a)){var b;return(b=y.get(1024))!=null?b:"UNKNOWN"}return(b=y.get((k||(k=d("I64"))).to_float(a)))!=null?b:"UNKNOWN"}function b(a){return a==null?"UNKNOWN":(a=z.get((k||(k=d("I64"))).to_int32(a)))!=null?a:"UNKNOWN"}function B(a){var b;if((k||(k=d("I64"))).equal(a,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").ANIMATED_IMAGE)))b=(j||(j=d("LSIntEnum"))).ofNumber(16384);else if((k||(k=d("I64"))).equal(a,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").IMAGE)))b=(j||(j=d("LSIntEnum"))).ofNumber(2);else if((k||(k=d("I64"))).equal(a,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").VIDEO)))b=(j||(j=d("LSIntEnum"))).ofNumber(2);else if((k||(k=d("I64"))).equal(a,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").AUDIO)))b=(j||(j=d("LSIntEnum"))).ofNumber(4);else if((k||(k=d("I64"))).equal(a,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").FILE)))b=(j||(j=d("LSIntEnum"))).ofNumber(64);else if((k||(k=d("I64"))).equal(a,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").FILE)))b=(j||(j=d("LSIntEnum"))).ofNumber(64);else if((k||(k=d("I64"))).equal(a,(j||(j=d("LSIntEnum"))).ofNumber(c("MessagingAttachmentType").XMA)))b=(j||(j=d("LSIntEnum"))).ofNumber(1024);else return"UNKNOWN";return A(b)}var C=c("objectEntries")(c("MessagingAttachmentType")).reduce(function(a,b){var c=b[0];b=b[1];a.set(b,c);return a},new Map());function e(a){return D(a.attachmentType)}function D(a){return(a=C.get((k||(k=d("I64"))).to_float(a)))!=null?a:"UNKNOWN"}var E=c("objectEntries")(c("MessagingThreadType")).reduce(function(a,b){var c=b[0];b=b[1];a.set(b,c);return a},new Map());function f(a){a=a.threadType;return F(a)}function F(a){return(a=E.get((k||(k=d("I64"))).to_float(a)))!=null?a:"UNKNOWN"}var G=c("objectEntries")(c("LSXmaContentType")).reduce(function(a,b){var c=b[0];b=b[1];a.set(b,c);return a},new Map());function H(a){return(a=G.get((k||(k=d("I64"))).to_float(a)))!=null?a:"UNKNOWN"}var I=c("objectEntries")(c("LSMessagingInitiatingSource")).reduce(function(a,b){var c=b[0];b=b[1];a.set(b,c);return a},new Map());function J(a){return a!=null?(a=I.get((k||(k=d("I64"))).to_float(a)))!=null?a:"UNKNOWN":"UNKNOWN"}var K=c("objectEntries")(c("E2EELinkPreviewXMADataType")).reduce(function(a,b){var c=b[0];b=b[1];a.set(b,c);return a},new Map());function L(a){return(a=K.get((k||(k=d("I64"))).to_float(a)))!=null?a:"UNKNOWN"}var M=c("objectEntries")(c("LSMessageReplySourceType")).reduce(function(a,b){var c=b[0];b=b[1];a.set(b,c);return a},new Map());function N(a){return(a=M.get((k||(k=d("I64"))).to_float(a)))!=null?a:"UNKNOWN"}var O=c("objectEntries")(c("MNLSXMALayoutType")).reduce(function(a,b){var c=b[0];b=b[1];a.set(b,c);return a},new Map());function P(a){return a==null?"UNKNOWN":(a=O.get((k||(k=d("I64"))).to_float(a)))!=null?a:"UNKNOWN"}var Q=c("objectEntries")(c("LSMessageReplySourceTypeV2")).reduce(function(a,b){var c=b[0];b=b[1];a.set(b,c);return a},new Map());function R(a){return(a=Q.get((k||(k=d("I64"))).to_float(a)))!=null?a:"UNKNOWN"}function S(a,b,e,f){var g=(h||(h=c("performanceAbsoluteNow")))(),i=function(d,e){c("QPLUserFlow").addPoint(a,"window_visibility_change_"+(e?"hidden":"visible"),{instanceKey:b}),e&&c("QPLUserFlow").addAnnotations(a,{bool:{window_was_hidden:!0}},{instanceKey:b})},j=function(a){var b=d("VisibilityAPI").canUseVisibilityAPI?d("VisibilityAPI").isVisibilityHidden():void 0;b={bool:{window_is_hidden_on_end:b}};return a!=null?c("mergeDeep")(a,b):b};return{addAnnotations:function(d){c("QPLUserFlow").addAnnotations(a,d,{instanceKey:b})},addPoint:function(d,e){c("QPLUserFlow").addPoint(a,d,{data:e,instanceKey:b})},endCancel:function(f){e!=null&&(e.current=!0);var g=(h||(h=c("performanceAbsoluteNow")))(),k=j();d("VisibilityState").unsubscribe(i);c("setTimeout")(function(){c("QPLUserFlow").endCancel(a,{annotations:k,cancelReason:f,instanceKey:b,timestamp:g})},x)},endFail:function(f,g){e!=null&&(e.current=!0);var k=(h||(h=c("performanceAbsoluteNow")))(),l=c("mergeDeep")(j(g),{string:{fail_reason:f}});d("VisibilityState").unsubscribe(i);c("setTimeout")(function(){c("QPLUserFlow").endFailure(a,"render-failure",{annotations:l,instanceKey:b,timestamp:k})},x)},endSuccess:function(f,g){e!=null&&(e.current=!0);var k=g!=null?g:(h||(h=c("performanceAbsoluteNow")))(),l=j(f);d("VisibilityState").unsubscribe(i);c("setTimeout")(function(){c("QPLUserFlow").endSuccess(a,{annotations:l,instanceKey:b,timestamp:k})},x)},getInstanceKey:function(){return b},start:function(e){var j=g,k=f!=null?f:{};if(e!=null&&e.isManualRetry===!0){var l;j=(h||(h=c("performanceAbsoluteNow")))();k=c("mergeDeep")(k,{bool:{is_manual_retry:!0},"int":{manual_retry_count:((l=e.manualRetryCount)!=null?l:0)+1},string:{previousManualRetryErrorDetails:e.previousManualRetryErrorDetails}})}c("QPLUserFlow").start(a,{annotations:k,cancelOnUnload:!0,instanceKey:b,timeoutInMs:w,timestamp:j});d("VisibilityState").subscribe(i)}}}function T(a,b,e){var f=a.attachmentType,g=p(!1),h=o(function(){var h=d("MAWLoggerUtils").getInstanceKey(),i=c("CurrentUser").getAppID();i={string:{app_id:i!=null?i:void 0,attachment_type:D(f),content_type:B(f),entry_point:e,thread_type:F(b),transport_key:a.transportKey,xma_content_type:a.xmaContentType!=null?H(a.xmaContentType):void 0}};return babelHelpers["extends"]({},S(v,h,g,i))},[a.messageId]);n(function(){h==null?void 0:h.start()},[h]);m(function(){return function(){g.current===!1&&(h==null?void 0:h.getInstanceKey())!=null&&(h==null?void 0:h.endCancel(706))}},[h]);return h}function U(a,b,c){a=a+(b?"0":"1")+(c?"1":"0");return d("MAWLoggerUtils").getInstanceKeyFromId(a)}function V(a,b,e,f,g){g===void 0&&(g={});b=(k||(k=d("I64"))).le(b,(j||(j=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").OPTIMISTIC));a=U(a,b,e);c("QPLUserFlow").endFailure(v,f,{annotations:g,instanceKey:a})}function W(a,b,e,f,g){g===void 0&&(g={});b=(k||(k=d("I64"))).le(b,(j||(j=d("LSIntEnum"))).ofNumber(c("LSAuthorityLevel").OPTIMISTIC));a=U(a,b,e);c("QPLUserFlow").addPoint(v,f,{data:g,instanceKey:a})}function X(a){var b=(k||(k=d("I64"))).of_float(d("ServerTime").getMillis());b=k.to_float(k.sub(b,a))/1e3;b<q?a="less_than_one_day":b<r?a="between_one_and_three_days":b<s?a="between_three_days_and_one_week":b<t?a="between_one_and_two_weeks":b<u?a="between_two_weeks_and_one_month":a="more_than_one_month";return a}function Y(a){if(a==null||a<=0)return"invalid_duration";else if(a<=30)return"duration less than 30 seconds";else if(a<=1*60)return"duration between 30 seconds and 1 minute";else if(a<=2*60)return"duration between 1 minute and 2 minutes";else if(a<=3*60)return"duration between 2 minutes and 3 minutes";else if(a<=5*60)return"duration between 3 minutes and 5 minutes";else if(a<=10*60)return"duration between 5 minutes and 10 minutes";else if(a<=30*60)return"duration between 10 minutes and 30 minutes";else if(a<=1*60*60)return"duration between 30 minutes and 1 hour";else if(a<=2*60*60)return"duration between 1 hour and 2 hours";else return"duration above 2 hours"}g.MSGR_WEB_MEDIA_RENDER_EVENT=v;g.QPL_TIMEOUT=w;g.QPL_END_DELAY_MS=x;g.getMessageContentTypeString=a;g.getMessageContentTypeStringFromEnum=A;g.getReplyMessageAttachmentTypeStringFromEnum=b;g.deriveMessageContentTypeStringFromAttachmentTypeLessAccurate=B;g.getAttachmentTypeString=e;g.getAttachmentTypeStringFromEnum=D;g.getThreadTypeString=f;g.getThreadTypeStringFromEnum=F;g.getXmaContentTypeStringFromEnum=H;g.getMessagingInitiationSourceStringFromEnum=J;g.getE2EELinkPreviewXMADataTypeStringFromEnum=L;g.getMessageReplySourceTypeStringFromEnum=N;g.getMessageXmaLayoutTypeStringFromEnum=P;g.getMessageReplySourceTypeV2StringFromEnum=R;g.createQplFlowCommon=S;g.useCommonMediaRenderQplForAttachment=T;g.getThreadViewMediaRenderQplInstanceKey=U;g.endThreadViewMediaRenderQpl=V;g.addThreadViewMediaRenderQplPoint=W;g.calculateDiffBucketFromTimestamp=X;g.convertDurationToStringBucket=Y}),98);
__d("MWPContactContext.react",["react","shallowEqualI64"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));e=h;var j=e.useContext,k=e.useMemo,l=e.useRef,m=i.createContext({authorName:void 0,contact:void 0,nickname:void 0}),n=i.createContext({authorName:void 0,contact:void 0,nickname:void 0});function a(){return j(m)}function b(){return j(n)}function c(a){var b=a.children,c=a.contact,d=a.nickname,e=function(){var a;if(d!=null)a=d;else if(c!=null){var b=c.firstName;a=b!=null?b:c.name}else a=void 0;return a}();a=k(function(){return{authorName:e,contact:c,nickname:d}},[d,e,c]);var f=o(c),g=k(function(){return{authorName:e,contact:f,nickname:d}},[d,e,f]);return i.jsx(m.Provider,{value:a,children:i.jsx(n.Provider,{value:g,children:b})})}c.displayName=c.name+" [from "+f.id+"]";function o(a){var b=l();a=a?{blockedByViewerStatus:a.blockedByViewerStatus,capabilities:a.capabilities,capabilities2:a.capabilities2,contactTypeExact:a.contactTypeExact,name:a.name,profilePictureFallbackUrl:a.profilePictureFallbackUrl,profilePictureUrl:a.profilePictureUrl,profilePictureUrlExpirationTimestampMs:a.profilePictureUrlExpirationTimestampMs,secondaryName:a.secondaryName}:void 0;d("shallowEqualI64").shallowEqualI64(b.current,a)||(b.current=a);return b.current}g.useMWPContactFullContext=a;g.useMWPContactStableContext=b;g.MWPContactContextProvider=c}),98);
__d("useServerTime",["JSScheduler","ServerTime","clearInterval","react","setInterval"],(function(a,b,c,d,e,f,g){"use strict";var h,i;b=i||d("react");var j=b.useEffect,k=b.useState,l=6e4,m=new Set(),n=null,o=!1;function p(){m.forEach(function(a){return a()}),o=!1}function q(a){a===void 0&&(a=l),n=c("setInterval")(function(){o||(o=!0,(h||(h=c("JSScheduler"))).scheduleSpeculativeCallback(p))},a)}function r(){m.size===0&&(c("clearInterval")(n),n=null)}function s(a,b){b===void 0&&(b=l);m.add(a);n==null&&q(b);return function(){m["delete"](a),r()}}function t(){return new Date(d("ServerTime").getMillis())}function a(a){a===void 0&&(a=l);var b=k(function(){return t()}),c=b[0],d=b[1],e=function(){return d(t())};j(function(){return s(e,a)},[a]);return c}g["default"]=a}),98);
__d("MWPFeatureLimitedData",["I64","Int64Hooks","LSFeatureLimitsType","LSIntEnum","LSMessagingThreadTypeUtil","ReQL","ReQLSuspense","ServerTime","gkx","useReStore","useServerTime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=[(i||(i=d("LSIntEnum"))).ofNumber(c("LSFeatureLimitsType").READ_ONLY),i.ofNumber(c("LSFeatureLimitsType").GENERIC_READ_ONLY_BLOCK),i.ofNumber(c("LSFeatureLimitsType").MESSAGE_SEND)];function l(a){for(var b of k)if((j||(j=d("I64"))).equal(b,a.type_))return!0;return!1}function m(a,b){return(j||(j=d("I64"))).equal((i||(i=d("LSIntEnum"))).ofNumber(c("LSFeatureLimitsType").MESSAGE_SEND_PUBLIC),a.type_)&&d("LSMessagingThreadTypeUtil").isPublicCMThread(b)&&c("gkx")("26351")}function n(a,b){return(j||(j=d("I64"))).equal((i||(i=d("LSIntEnum"))).ofNumber(c("LSFeatureLimitsType").MESSAGE_SEND_PRIVATE),a.type_)&&d("LSMessagingThreadTypeUtil").isPrivateThread(b)}var o=function(a){var b=(j||(j=d("I64"))).of_float(d("ServerTime").getMillis()/1e3);return d("ReQL").fromTableAscending(a.tables.feature_limits).filter(function(a){return l(a)&&(j||(j=d("I64"))).gt(a.expirationTimestampSeconds,b)})};function p(a,b){var e=(j||(j=d("I64"))).of_float(c("useServerTime")().valueOf()/1e3);return d("Int64Hooks").useMemoInt64(function(){return d("ReQL").fromTableAscending(a.tables.feature_limits).filter(function(a){return(b!=null?l(a)||m(a,b)||n(a,b):l(a))&&(j||(j=d("I64"))).gt(a.expirationTimestampSeconds,e)})},[e,a,b])}function a(){var a=(h||(h=c("useReStore")))();return d("ReQLSuspense").useFirst(function(){return o(a)},[a],f.id+":127")}function b(a){var b=(h||(h=c("useReStore")))(),e=p(b,a);return d("ReQLSuspense").useFirst(function(){return e},[e],f.id+":136")}g.readonlyFeatureLimitQuery=o;g.useReadOnlyFeatureLimitData=a;g.useReadOnlyFeatureLimitDataWithThreadType=b}),98);
__d("MWPReplyContext.react",["I64","emptyFunction","react","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=h||(h=d("react"));b=h;e=b.createContext;var k=b.useCallback,l=b.useMemo,m=b.useState,n=c("requireDeferred")("MWPReplyLogging").__setRef("MWPReplyContext.react"),o=e({clearReply:c("emptyFunction"),reply:void 0,setReply:c("emptyFunction")});function p(a){try{return JSON.parse(a)}catch(a){return}}function a(a){var b=a.children,c=a.clearThreadReply,e=a.getRepliesState,f=a.persistRepliesState,g=a.senderId;a=a.threadKey;e=e!=null&&a!=null?p(e((i||(i=d("I64"))).to_string(a))):void 0;a=m(e);var h=a[0],q=a[1],r=k(function(a,b){n.onReady(function(c){c.logClearReply(b,a,h!=null)});q();if(c!=null)return c((i||(i=d("I64"))).to_string(a))},[c,h]),s=k(function(a,b){n.onReady(function(c){c.logSetReply(h,a,b)});q(a);if(f!=null)return f({messageId:a.messageId,offlineThreadingId:a.offlineThreadingId,senderId:g,sendStatusV2:a.sendStatusV2,threadKey:(i||(i=d("I64"))).to_string(b),timestampMs:a.timestampMs})},[f,h,g]);e=l(function(){return{clearReply:r,reply:h,setReply:s}},[h,s,r]);return j.jsx(o.Provider,{value:e,children:b})}a.displayName=a.name+" [from "+f.id+"]";g.MWPReplyContext=o;g.MWPReplyContextProvider=a}),98);
__d("ReQLSubscribe",["ErrorGuard","ReQL","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){a=a.take(1);var e=(h||(h=c("ErrorGuard"))).guard(b),f=!1,g=null,i=a.subscribe(function(a,b){if(f)return;switch(b.operation){case"delete":(g==null||g.current!==void 0)&&(g={current:void 0},e(g.current));return;case"add":case"put":((a=g)==null?void 0:a.current)!==b.value&&(g={current:b.value},e(g.current))}});c("promiseDone")(d("ReQL").firstAsync(a),function(a){!f&&g==null&&(g={current:a},e(a))});return function(){f||(f=!0,i==null?void 0:i())}}g.subscribeToFirst=a}),98);
__d("ThreadStatus",[],(function(a,b,c,d,e,f){a=Object.freeze({DEFAULT:0,PAUSED:1,PAUSED_AND_UPGRADED_TO_COMMUNITY:2,UPGRADED_TO_COMMUNITY_AND_TO_BE_PAUSED:3});f["default"]=a}),66);
__d("TimestampUtils",["fbt","formatDate"],(function(a,b,c,d,e,f,g,h){"use strict";var i=1e3*60*60*24,j=i*30;function k(a,b){return a.getFullYear()===b.getFullYear()&&a.getMonth()===b.getMonth()&&a.getDate()===b.getDate()}function l(a,b){return h._(/*BTDS*/"{date} at {time}",[h._param("date",a),h._param("time",b)])}function a(a,b){var d=c("formatDate")(b,"g:i A");if(k(b,a))return h._(/*BTDS*/"Today at {time}",[h._param("time",d)]);var e=new Date(a.valueOf()-i);if(k(b,e))return h._(/*BTDS*/"Yesterday at {time}",[h._param("time",d)]);e=new Date(a.valueOf()+i);if(k(b,e))return h._(/*BTDS*/"Tomorrow at {time}",[h._param("time",d)]);e=c("formatDate")(b,"F j");a.getFullYear()!==b.getFullYear()&&(e=c("formatDate")(b,"F j, Y"));return Math.abs(a.valueOf()-b.valueOf())<j?l(e,d):e}function b(a){return l(c("formatDate")(a,"l, F j, Y"),c("formatDate")(a,"g:i A"))}g.getAbsoluteTimestampFbt=l;g.getTimestamp=a;g.getAbsoluteTimestamp=b}),226);
__d("getFBTSafeGenderFromGenderConst",["GenderConst","IntlVariations"],(function(a,b,c,d,e,f,g){"use strict";function a(a){switch(a){case c("GenderConst").MALE_SINGULAR:return c("IntlVariations").GENDER_MALE;case c("GenderConst").FEMALE_SINGULAR:return c("IntlVariations").GENDER_FEMALE;default:return c("IntlVariations").GENDER_UNKNOWN}}g["default"]=a}),98);
__d("getInstamadilloLoggingExtraData",["I64","IGDInstamadilloUtils","IGDThreadTTLCUtils"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var b,c=d("IGDInstamadilloUtils").isInstamadilloCutover(a),e=d("IGDInstamadilloUtils").isInstamadilloTransportEnabled(a),f=d("IGDInstamadilloUtils").isIGDDisappearingModeEnabled(a);b=(b=a==null?void 0:a.consistentThreadFbid)!=null?b:(h||(h=d("I64"))).zero;b=babelHelpers["extends"]({},d("IGDThreadTTLCUtils").getTTLCLoggingExtraData(a),{consistent_thread_fbid:(h||(h=d("I64"))).to_string(b),is_dm:String(f),is_instamadillo:String(e),is_instamadillo_tlc:String(c)});a!=null&&(b.thread_key=(h||(h=d("I64"))).to_string(a.threadKey),(a==null?void 0:a.consistentThreadFbid)!=null&&(b.consistent_thread_fbid=(h||(h=d("I64"))).to_string(a.consistentThreadFbid)));return b}g["default"]=a}),98);
__d("isThreadLevelCutoverEnabled",["qex"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("qex")._("564")===!0}g["default"]=a}),98);
__d("loadLSThreadsForTheFirstTime",["I64","LSFactory","LSThreadsRangesQueryStoredProcedure"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){return c("LSThreadsRangesQueryStoredProcedure")(c("LSFactory")(a),{additionalPagesToFetch:(h||(h=d("I64"))).zero,isLoadingAfter:!1,isLoadingBefore:!0,maxLastActivityTimestampMs:h.max_int,maxThreadKey:h.max_int,minLastActivityTimestampMs:h.max_int,minThreadKey:h.max_int,parentThreadKey:b,shouldSkipE2eeThreadsRanges:!1})}g["default"]=a}),98);
__d("useHasIGBroadcastChannelTab",["IGDChannelsGating","IGDProfessionalIGFolderContext.react"],(function(a,b,c,d,e,f,g){"use strict";function a(){var a=d("IGDProfessionalIGFolderContext.react").useIsBusinessAccount(),b=d("IGDProfessionalIGFolderContext.react").useIsProfessionalIGD();return!b||a?!1:d("IGDChannelsGating").isChannelsTabInProfessionalInboxEnabled()}g["default"]=a}),98);
__d("useIsReadOnlyFeatureLimited",["MWPFeatureLimitedData","gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(){var a=d("MWPFeatureLimitedData").useReadOnlyFeatureLimitData();if(a!=null)return c("gkx")("24070");else return!1}g["default"]=a}),98);
__d("useIsThreadBlocked",["I64","LSContactBlockedByViewerStatus","LSIntEnum","LSMessagingThreadTypeUtil","MWPActor.react","MWThreadKey.react","ReQL","ReQLSuspense","useReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;function a(){var a=(j||(j=c("useReStore")))(),b=d("MWThreadKey.react").useMWThreadKeyMemoizedExn(),e=d("MWPActor.react").useActor(),g=d("ReQLSuspense").useFirst(function(){return d("ReQL").fromTableAscending(a.tables.threads,["threadKey","threadType"]).getKeyRange(b)},[a,b],f.id+":28"),k=d("ReQLSuspense").useFirst(function(){return(g==null?void 0:g.threadKey)!=null&&(g==null?void 0:g.threadType)!=null&&d("LSMessagingThreadTypeUtil").isOneToOne(g==null?void 0:g.threadType)?d("ReQL").leftJoin(d("ReQL").fromTableAscending(a.tables.participants,["contactId"]).getKeyRange(b).filter(function(a){return!(h||(h=d("I64"))).equal(a.contactId,e)}).take(1),d("ReQL").fromTableAscending(a.tables.contacts,["blockedByViewerStatus"])).map(function(a){a[0];a=a[1];return a}):d("ReQL").empty()},[e,a,g==null?void 0:g.threadKey,g==null?void 0:g.threadType,b],f.id+":37");if(k!=null)return!(h||(h=d("I64"))).equal(k.blockedByViewerStatus,(i||(i=d("LSIntEnum"))).ofNumber(c("LSContactBlockedByViewerStatus").UNBLOCKED));else return!1}g["default"]=a}),98);