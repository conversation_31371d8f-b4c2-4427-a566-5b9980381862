;/*FB_PKG_DELIM*/

__d("LSStoreProjectConfiguration",["LSBase64Decode.nop","LSIssueNewTask"],(function(a,b,c,d,e,f){function a(){var a=arguments,c=a[a.length-1],d=[],e=[];return c.sequence([function(e){return c.sequence([function(e){return c.nativeOperation(b("LSBase64Decode.nop"),a[4]).then(function(a){return a=a,d[0]=a[0],a})},function(e){return c.blobs.neq(d[0],void 0)?c.db.table(307).put({configId:a[1],projectName:a[0],cipherSuite:a[5],publicKey:d[0],maxEvals:a[2],redemptionLimit:a[3],expirationTimestampMs:a[6],tokenPollFrequencyMs:a[7],maxClientTokenPoolSize:a[8]}):(function(a){c.logger(a).info(a)}("issuing retry for project configurations task since public key was null"),d[1]=new c.Map(),d[1].set("project_name",a[0]),d[2]=d[1].get("project_name"),d[3]=c.toJSON(d[1]),c.storedProcedure(b("LSIssueNewTask"),["acs_credentials","_",d[2]].join(""),c.i64.cast([0,351]),d[3],void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0]),void 0,void 0,c.i64.cast([0,0]),c.i64.cast([0,0])))}])},function(a){return c.resolve(e)}])}a.__sproc_name__="LSAnonymousCredentialsStoreProjectConfigurationStoredProcedure";a.__tables__=["secure_acs_configurations"];e.exports=a}),null);