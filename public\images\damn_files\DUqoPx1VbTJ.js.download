;/*FB_PKG_DELIM*/

"use strict";(function(){var a=typeof globalThis!=="undefined"&&globalThis||typeof self!=="undefined"&&self||typeof global!=="undefined"&&global;if(typeof a.AbortController!=="undefined")return;var b=function(){function a(){this.__listeners=new Map()}a.prototype=Object.create(Object.prototype);a.prototype.addEventListener=function(a,b,c){if(arguments.length<2)throw new TypeError("TypeError: Failed to execute 'addEventListener' on 'CustomEventTarget': 2 arguments required, but only "+arguments.length+" present.");var d=this.__listeners,e=a.toString();d.has(e)||d.set(e,new Map());var f=d.get(e);f.has(b)||f.set(b,c)};a.prototype.removeEventListener=function(a,b,c){if(arguments.length<2)throw new TypeError("TypeError: Failed to execute 'addEventListener' on 'CustomEventTarget': 2 arguments required, but only "+arguments.length+" present.");var d=this.__listeners,e=a.toString();if(d.has(e)){var f=d.get(e);f.has(b)&&f["delete"](b)}};a.prototype.dispatchEvent=function(a){if(!(a instanceof Event))throw new TypeError("Failed to execute 'dispatchEvent' on 'CustomEventTarget': parameter 1 is not of type 'Event'.");var b=a.type,c=this.__listeners;c=c.get(b);if(c)for(b of c.entries()){var d=b[0],e=b[1];try{typeof d==="function"?d.call(this,a):d&&typeof d.handleEvent==="function"&&d.handleEvent(a)}catch(a){setTimeout(function(){throw a})}e&&e.once&&c["delete"](d)}return!0};return a}(),c={};a.AbortSignal=function(){function a(a){if(a!==c)throw new TypeError("Illegal constructor.");b.call(this);this._aborted=!1}a.prototype=Object.create(b.prototype);a.prototype.constructor=a;Object.defineProperty(a.prototype,"onabort",{get:function(){return this._onabort},set:function(a){var b=this._onabort;b&&this.removeEventListener("abort",b);this._onabort=a;this.addEventListener("abort",a)}});Object.defineProperty(a.prototype,"aborted",{get:function(){return this._aborted}});return a}();a.AbortController=function(){function a(){this._signal=new AbortSignal(c)}a.prototype=Object.create(Object.prototype);Object.defineProperty(a.prototype,"signal",{get:function(){return this._signal}});a.prototype.abort=function(){var a=this.signal;a.aborted||(a._aborted=!0,a.dispatchEvent(new Event("abort")))};return a}()})();



"use strict";Array.prototype.at==null&&(Array.prototype.at=function(a){a=parseInt(a,10);Number.isInteger(a)||(a=0);if(a>=0&&a<this.length)return this[a];else return this[this.length+a]});
"use strict";(function(){if(!Array.prototype.flat){var a=function b(a){return a<1?Array.prototype.slice.call(this):Array.prototype.reduce.call(this,function(c,d){Array.isArray(d)?c.push.apply(c,b.call(d,a-1)):c.push(d);return c},[])};Array.prototype.flat=function(){return a.call(this,isNaN(arguments[0])?1:Number(arguments[0]))}}if(!Array.prototype.flatMap){var b=function(a,b){var c=[];if(typeof b!=="function")throw new TypeError("Callback function must be callable.");for(var d=0;d<a.length;d++){var e=b.call(a,a[d],d,a);Array.isArray(e)?c.push.apply(c,e):c.push(e)}return c};Array.prototype.flatMap=function(a){var c=arguments[1]||this;return b(c,a)}}})();


(function(){"use strict";var a=Array.prototype.indexOf;Array.prototype.includes||(Array.prototype.includes=function(d){"use strict";if(d!==void 0&&Array.isArray(this)&&!Number.isNaN(d))return a.apply(this,arguments)!==-1;var e=Object(this),f=e.length?b(e.length):0;if(f===0)return!1;var g=arguments.length>1?c(arguments[1]):0,h=g<0?Math.max(f+g,0):g,i=Number.isNaN(d);while(h<f){var j=e[h];if(j===d||i&&Number.isNaN(j))return!0;h++}return!1});function b(a){return Math.min(Math.max(c(a),0),Number.MAX_SAFE_INTEGER)}function c(a){a=Number(a);return Number.isFinite(a)&&a!==0?d(a)*Math.floor(Math.abs(a)):a}function d(a){return a>=0?1:-1}if(!Array.prototype.values){var e=typeof Symbol==="function"?Symbol.iterator:"@@iterator",f=function(){function a(a){this.$1=void 0;this.$2=0;if(a==null)throw new TypeError("Cannot convert undefined or null to object");this.$1=Object(a)}var b=a.prototype;b.next=function(){if(this.$1==null||this.$2>=this.$1.length){this.$1=void 0;return{value:void 0,done:!0}}var a=this.$1[this.$2];this.$2++;return{value:a,done:!1}};b[e]=function(){return this};return a}();Array.prototype.values=function(){return new f(this)}}Array.prototype[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]||(Array.prototype[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=Array.prototype.values)})();
"use strict";Array.prototype.findLast==null&&(Array.prototype.findLast=function(a,b){var c=this;for(var d=c.length-1;d>=0;d--){var e=c[d],f=a.call(b,e,d,c);if(f)return e}return void 0});
"use strict";Array.prototype.findLastIndex==null&&(Array.prototype.findLastIndex=function(a,b){var c=this;for(var d=c.length-1;d>=0;d--){var e=c[d];e=a.call(b,e,d,c);if(e)return d}return-1});
"use strict";Array.prototype.toReversed==null&&(Array.prototype.toReversed=function(){return this.slice().reverse()});
"use strict";Array.prototype.toSorted==null&&(Array.prototype.toSorted=function(a){return this.slice().sort(a)});
"use strict";Array.prototype.toSpliced==null&&(Array.prototype.toSpliced=function(){var a=this.slice();a.splice.apply(a,arguments);return a});

"use strict";if(Array.prototype["with"]==null){var toIntegerOrInfinity=function(a){if(Number.isNaN(a)||a===0)return 0;return a===Infinity||a===-Infinity?a:Math.trunc(a)};Array.prototype["with"]=function(a,b){var c=this.length;a=toIntegerOrInfinity(a);var d;a>=0?d=a:d=c+a;if(d>=c||d<0)throw new RangeError("Invalid index");a=this.slice();a[d]=b;return a}}
(function(a){a.__t=function(a){return a[0]},a.__w=function(a){return a}})(typeof globalThis!=="undefined"?globalThis:typeof global!=="undefined"?global:typeof window!=="undefined"?window:typeof this!=="undefined"?this:typeof self!=="undefined"?self:{});
(function(a){var b={},c=function(a,b){if(!a&&!b)return null;var c={};typeof a!=="undefined"&&(c.type=a);typeof b!=="undefined"&&(c.signature=b);return c},d=function(a,b){return c(a&&/^[A-Z]/.test(a)?a:void 0,b&&(b.params&&b.params.length||b.returns)?"function("+(b.params?b.params.map(function(a){return/\?/.test(a)?"?"+a.replace("?",""):a}).join(","):"")+")"+(b.returns?":"+b.returns:""):void 0)},e=function(a,b,c){return a},f=function(a,b,c){if("typechecks"in __transform_includes){b=d(b?b.name:void 0,c);b&&__w(a,b)}return a},g=function(a,b,c){return c.apply(a,b)},h=function(a,c,d,e,f){if(f){f.callId||(f.callId=f.module+":"+(f.line||0)+":"+(f.column||0));e=f.callId;b[e]=(b[e]||0)+1}return d.apply(a,c)};typeof __transform_includes==="undefined"?(a.__annotator=e,a.__bodyWrapper=g):(a.__annotator=f,"codeusage"in __transform_includes?(a.__annotator=e,a.__bodyWrapper=h,a.__bodyWrapper.getCodeUsage=function(){return b},a.__bodyWrapper.clearCodeUsage=function(){b={}}):a.__bodyWrapper=g)})(typeof globalThis!=="undefined"?globalThis:typeof global!=="undefined"?global:typeof window!=="undefined"?window:typeof this!=="undefined"?this:typeof self!=="undefined"?self:{});
self.__DEV__=self.__DEV__||0,self.emptyFunction=function(){};



(function(a,b){var c="keys",d="values",e="entries",f=function(){var a=h(Array),b;a||(b=function(){"use strict";function a(a,b){this.$1=a,this.$2=b,this.$3=0}var b=a.prototype;b.next=function(){if(this.$1==null)return{value:void 0,done:!0};var a=this.$1,b=this.$1.length,f=this.$3,g=this.$2;if(f>=b){this.$1=void 0;return{value:void 0,done:!0}}this.$3=f+1;if(g===c)return{value:f,done:!1};else if(g===d)return{value:a[f],done:!1};else if(g===e)return{value:[f,a[f]],done:!1}};b[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=function(){return this};return a}());return{keys:a?function(a){return a.keys()}:function(a){return new b(a,c)},values:a?function(a){return a.values()}:function(a){return new b(a,d)},entries:a?function(a){return a.entries()}:function(a){return new b(a,e)}}}(),g=function(){var a=h(String),b;a||(b=function(){"use strict";function a(a){this.$1=a,this.$2=0}var b=a.prototype;b.next=function(){if(this.$1==null)return{value:void 0,done:!0};var a=this.$2,b=this.$1,c=b.length;if(a>=c){this.$1=void 0;return{value:void 0,done:!0}}var d=b.charCodeAt(a);if(d<55296||d>56319||a+1===c)d=b[a];else{c=b.charCodeAt(a+1);c<56320||c>57343?d=b[a]:d=b[a]+b[a+1]}this.$2=a+d.length;return{value:d,done:!1}};b[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=function(){return this};return a}());return{keys:function(){throw TypeError("Strings default iterator doesn't implement keys.")},values:a?function(a){return a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]()}:function(a){return new b(a)},entries:function(){throw TypeError("Strings default iterator doesn't implement entries.")}}}();function h(a){return typeof a.prototype[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]==="function"&&typeof a.prototype.values==="function"&&typeof a.prototype.keys==="function"&&typeof a.prototype.entries==="function"}var i=function(){"use strict";function a(a,b){this.$1=a,this.$2=b,this.$3=Object.keys(a),this.$4=0}var b=a.prototype;b.next=function(){var a=this.$3.length,b=this.$4,f=this.$2,g=this.$3[b];if(b>=a){this.$1=void 0;return{value:void 0,done:!0}}this.$4=b+1;if(f===c)return{value:g,done:!1};else if(f===d)return{value:this.$1[g],done:!1};else if(f===e)return{value:[g,this.$1[g]],done:!1}};b[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=function(){return this};return a}(),j={keys:function(a){return new i(a,c)},values:function(a){return new i(a,d)},entries:function(a){return new i(a,e)}};function k(a,b){if(typeof a==="string")return g[b||d](a);else if(Array.isArray(a))return f[b||d](a);else if(a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"])return a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]();else return j[b||e](a)}Object.assign(k,{KIND_KEYS:c,KIND_VALUES:d,KIND_ENTRIES:e,keys:function(a){return k(a,c)},values:function(a){return k(a,d)},entries:function(a){return k(a,e)},generic:j.entries});a.FB_enumerate=k})(typeof global==="object"?global:typeof this==="object"?this:typeof window==="object"?window:typeof self==="object"?self:{});





"use strict";(function(){if(typeof Element==="undefined"||Element.prototype.scroll)return;function a(a,b){b===void 0&&(b=!1);if(a.length===0)return;var c=a[0],d=a[1];c=Number(c)||0;d=Number(d)||0;if(a.length===1){a=a[0];if(a==null)return;c=a.left;d=a.top;c!==void 0&&(c=Number(c)||0);d!==void 0&&(d=Number(d)||0)}c!==void 0&&(this.scrollLeft=(b?this.scrollLeft:0)+c);d!==void 0&&(this.scrollTop=(b?this.scrollTop:0)+d)}Element.prototype.scroll=Element.prototype.scrollTo=function(){a.call(this,arguments)};Element.prototype.scrollBy=function(){a.call(this,arguments,!0)}})();



"use strict";function getValuesIteratorProto(a){return Object.getPrototypeOf(a)}function getIterators(){return[[].values(),[].keys(),[].entries(),new Uint8Array([]).values(),new Uint8Array([]).keys(),new Uint8Array([]).entries(),new Map().values(),new Map().keys(),new Map().entries(),new Set().values(),new Set().keys(),new Set().entries()]}function polyfillIteratorFunctions(){var a=new Set();getIterators().forEach(function(b){a.add(getValuesIteratorProto(b))});a.forEach(function(a){typeof a.drop!=="function"&&(a.drop=function(a){for(var b=0;b<a;b++)this.next();return this}),typeof a.every!=="function"&&(a.every=function(a){var b=Array.from(this);return b.every(a)}),typeof a.filter!=="function"&&(a.filter=function(a){var b=Array.from(this);return b.filter(a)}),typeof a.find!=="function"&&(a.find=function(a){var b=Array.from(this);return b.find(a)}),typeof a.flatMap!=="function"&&(a.flatMap=function(a){var b=Array.from(this);return b.flatMap(a).values()}),typeof a.forEach!=="function"&&(a.forEach=function(a){var b=Array.from(this);return b.forEach(a)}),typeof a.map!=="function"&&(a.map=function(a){var b=Array.from(this);return b.map(a).values()}),typeof a.reduce!=="function"&&(a.reduce=function(a,b){var c=Array.from(this);return c.reduce(function(b,c,d,e){return a(b,c,d)},b)}),typeof a.some!=="function"&&(a.some=function(a){var b=Array.from(this);return b.some(a)}),typeof a.take!=="function"&&(a.take=function(a){a=Array.from(this).slice(0,a);return a.values()}),typeof a.toArray!=="function"&&(a.toArray=function(){return Array.from(this)})})}polyfillIteratorFunctions();
(function(){function a(){if(typeof JSON!=="object"||typeof JSON.stringify!=="function")return!1;if(typeof navigator==="undefined"||!navigator.userAgent)return!0;var a=navigator.userAgent,b;if(a.indexOf("Firefox/")>-1){b=a.match(/Firefox\/([0-9]+)/);return b==null||!(parseInt(b[1],10)>=62)}else if(a.indexOf("Edg/")>-1){b=a.match(/Edg\/([0-9]+)/);return b==null||!(parseInt(b[1],10)>=79)}else if(a.indexOf("Chrome/")>-1){b=a.match(/Chrome\/([0-9]+)/);return b==null||!(parseInt(b[1],10)>=66)}else if(a.indexOf("CriOS/")>-1){b=a.match(/CriOS\/([0-9]+)/);return b==null||!(parseInt(b[1],10)>=66)}else if(a.indexOf("Safari/")>-1&&a.indexOf("Version/")>-1){b=a.match(/Version\/([0-9]+)/);return b==null||!(parseInt(b[1],10)>=12)}return!0}function b(){return JSON.stringify(["\u2028\u2029"])==='["\\u2028\\u2029"]'}a()&&!b()&&(JSON.stringify=function(a){var b=/\u2028/g,c=/\u2029/g;return function(d,e,f){d=a.call(this,d,e,f);d&&(-1<d.indexOf("\u2028")&&(d=d.replace(b,"\\u2028")),-1<d.indexOf("\u2029")&&(d=d.replace(c,"\\u2029")));return d}}(JSON.stringify))})();

(function(){var a=Object.prototype.hasOwnProperty;Object.entries=function(b){if(b==null)throw new TypeError("Object.entries called on non-object");var c=[];for(var d in b)a.call(b,d)&&c.push([d,b[d]]);return c};typeof Object.fromEntries!=="function"&&(Object.fromEntries=function(a){var b={};for(a of a){var c=a[0],d=a[1];b[c]=d}return b});Object.values=function(b){if(b==null)throw new TypeError("Object.values called on non-object");var c=[];for(var d in b)a.call(b,d)&&c.push(b[d]);return c}})();



"use strict";Set.prototype.difference==null&&(Set.prototype.difference=function(a){var b=new Set(this);for(a of a)b.has(a)&&b["delete"](a);return b});
"use strict";Set.prototype.intersection==null&&(Set.prototype.intersection=function(a){var b=new Set();for(var c of this)a.has(c)&&b.add(c);return b});
"use strict";Set.prototype.isDisjointFrom==null&&(Set.prototype.isDisjointFrom=function(a){if(this.size<=a.size){for(var b of this)if(a.has(b))return!1}else for(b of a)if(this.has(b))return!1;return!0});
"use strict";Set.prototype.isSubsetOf==null&&(Set.prototype.isSubsetOf=function(a){if(this.size>a.size)return!1;for(var b of this)if(!a.has(b))return!1;return!0});
"use strict";Set.prototype.isSupersetOf==null&&(Set.prototype.isSupersetOf=function(a){if(this.size<a.size)return!1;for(a of a)if(!this.has(a))return!1;return!0});
"use strict";Set.prototype.symmetricDifference==null&&(Set.prototype.symmetricDifference=function(a){var b=new Set(this);for(a of a)b.has(a)?b["delete"](a):b.add(a);return b});



String.prototype.contains||(String.prototype.contains=String.prototype.includes);
String.prototype.padStart||(String.prototype.padStart=function(a,b){a=a>>0;b=String(b||" ");if(this.length>a)return String(this);else{a=a-this.length;a>b.length&&(b+=b.repeat(a/b.length));return b.slice(0,a)+String(this)}}),String.prototype.padEnd||(String.prototype.padEnd=function(a,b){a=a>>0;b=String(b||" ");if(this.length>a)return String(this);else{a=a-this.length;a>b.length&&(b+=b.repeat(a/b.length));return String(this)+b.slice(0,a)}});
if(!String.prototype.matchAll){var MAX_CALLS_TO_EXEC=250;String.prototype.matchAll=function(a){if(!a.global)throw new TypeError("String.prototype.matchAll called with a non-global RegExp argument");var b=String(this),c=[],d,e=0;while((d=a.exec(b))&&e++<MAX_CALLS_TO_EXEC)c.push(d);return c}}
String.prototype.trimLeft||(String.prototype.trimLeft=function(){return this.replace(/^\s+/,"")}),String.prototype.trimRight||(String.prototype.trimRight=function(){return this.replace(/\s+$/,"")});





"use strict";(function(a){function a(){if(typeof URL!=="function")return!1;if(typeof URL.createObjectURL!=="function"||typeof URL.revokeObjectURL!=="function")return!1;return typeof File!=="function"||typeof Blob!=="function"?!1:!0}if(!a())return;var b={},c=URL.createObjectURL,d=URL.revokeObjectURL;URL.createObjectURL=function(a){var d=null,e=0;a instanceof File?(d="File",e=a.size):a instanceof Blob?(d="Blob",e=a.size):typeof MediaSource==="function"&&a instanceof MediaSource&&(d="MediaSource",e=0);a=c.call(URL,a);d!==null&&(b[a]={type:d,size:e});return a};URL.revokeObjectURL=function(a){d.call(URL,a),delete b[a]};URL._fbRegisteredObjectURL=function(){return Object.values(b)}})(this);
(function(a){var b=a.babelHelpers={},c=Object.prototype.hasOwnProperty;typeof Symbol!=="undefined"&&!(typeof Symbol==="function"?Symbol.asyncIterator:"@@asyncIterator")&&(Symbol.asyncIterator=Symbol("Symbol.asyncIterator"));function d(a){this.wrapped=a}function e(a){var b,c;function e(a,d){return new Promise(function(e,g){e={key:a,arg:d,resolve:e,reject:g,next:null};c?c=c.next=e:(b=c=e,f(a,d))})}function f(b,c){try{var e=a[b](c);c=e.value;var h=c instanceof d;Promise.resolve(h?c.wrapped:c).then(function(a){if(h){f(b==="return"?"return":"next",a);return}g(e.done?"return":"normal",a)},function(a){f("throw",a)})}catch(a){g("throw",a)}}function g(a,d){switch(a){case"return":b.resolve({value:d,done:!0});break;case"throw":b.reject(d);break;default:b.resolve({value:d,done:!1});break}b=b.next;b?f(b.key,b.arg):c=null}this._invoke=e;typeof a["return"]!=="function"&&(this["return"]=void 0)}typeof Symbol==="function"&&(typeof Symbol==="function"?Symbol.asyncIterator:"@@asyncIterator")&&(e.prototype[typeof Symbol==="function"?Symbol.asyncIterator:"@@asyncIterator"]=function(){return this});e.prototype.next=function(a){return this._invoke("next",a)};e.prototype["throw"]=function(a){return this._invoke("throw",a)};e.prototype["return"]=function(a){return this._invoke("return",a)};b.createClass=function(){function a(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1;d.configurable=!0;"value"in d&&(d.writable=!0);Object.defineProperty(a,d.key,d)}}return function(b,c,d){c&&a(b.prototype,c);d&&a(b,d);return b}}();b.inheritsLoose=function(a,b){Object.assign(a,b);a.prototype=Object.create(b&&b.prototype);a.prototype.constructor=a;a.__superConstructor__=b;return b};b.wrapNativeSuper=function(a){var c=typeof Map==="function"?new Map():void 0;b.wrapNativeSuper=function(a){if(a===null)return null;if(typeof a!=="function")throw new TypeError("Super expression must either be null or a function");if(c!==void 0){if(c.has(a))return c.get(a);c.set(a,d)}b.inheritsLoose(d,a);function d(){a.apply(this,arguments)}return d};return b.wrapNativeSuper(a)};b.assertThisInitialized=function(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a};b._extends=Object.assign;b["extends"]=b._extends;b.construct=function(a,b){return new(Function.prototype.bind.apply(a,[null].concat(b)))()};b.objectWithoutPropertiesLoose=function(a,b){var d={};for(var e in a){if(!c.call(a,e)||b.indexOf(e)>=0)continue;d[e]=a[e]}return d};b.taggedTemplateLiteralLoose=function(a,b){b||(b=a.slice(0));a.raw=b;return a};b.bind=Function.prototype.bind;b.wrapAsyncGenerator=function(a){return function(){return new e(a.apply(this,arguments))}};b.awaitAsyncGenerator=function(a){return new d(a)};b.asyncIterator=function(a){var b;if(typeof Symbol!=="undefined"){if(typeof Symbol==="function"?Symbol.asyncIterator:"@@asyncIterator"){b=a[typeof Symbol==="function"?Symbol.asyncIterator:"@@asyncIterator"];if(b!=null)return b.call(a)}if(typeof Symbol==="function"?Symbol.iterator:"@@iterator"){b=a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"];if(b!=null)return b.call(a)}}throw new TypeError("Object is not async iterable")};b.asyncGeneratorDelegate=function(a,b){var c={},d=!1;function e(c,e){d=!0;e=new Promise(function(b){b(a[c](e))});return{done:!1,value:b(e)}}typeof Symbol==="function"&&(typeof Symbol==="function"?Symbol.iterator:"@@iterator")&&(c[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]=function(){return this});c.next=function(a){if(d){d=!1;return a}return e("next",a)};typeof a["throw"]==="function"&&(c["throw"]=function(a){if(d){d=!1;throw a}return e("throw",a)});typeof a["return"]==="function"&&(c["return"]=function(a){if(d){d=!1;return a}return e("return",a)});return c}})(typeof global==="undefined"?self:global);

(function(a){if(a.require!=null)return;var b=null,c=null,d=[],e={},f={},g=0,h=0,i=0,j=0,k=0,l=1,m=2,n=4,o=8,p=16,aa=32,ba=64,q=128,r=256,ca={},s={},t=Object.prototype.hasOwnProperty,u=Object.prototype.toString;function v(a){a=Array.prototype.slice.call(a);var b={},c,d,f,g;while(a.length){d=a.shift();if(b[d])continue;b[d]=!0;f=e[d];if(!f||V(f))continue;if(f.dependencies)for(c=0;c<f.dependencies.length;c++)g=f.dependencies[c],V(g)||a.push(g.id)}for(d in b)t.call(b,d)&&a.push(d);b=[];for(c=0;c<a.length;c++){d=a[c];var h=d;f=e[d];d=f?f.dependencies:null;if(!f||!d)h+=" is not defined";else if(V(f))h+=" is ready";else{f=[];for(var i=0;i<d.length;i++)g=d[i],V(g)||f.push(g.id);h+=" is waiting for "+f.join(", ")}b.push(h)}return b.join("\n")}function w(b){var a=new Error(b);a.name="ModuleError";a.messageFormat=b;for(var c=arguments.length,d=new Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];a.messageParams=d.map(function(a){return String(a)});a.taalOpcodes=[2,2];return a}$=a.Env||{};var x=!!$.gk_require_when_ready_in_order,da=!!$.clear_js_factory_after_used,y=!!$.profile_require_factories,z=a.performance||{},A;if(z.now&&z.timing&&z.timing.navigationStart){var B=z.timing.navigationStart;A=function(){return z.now()+B}}else A=function(){return Date.now()};var C=0;function D(a){C++;var b=e[a];(!b||b.exports==null&&!b.factoryFinished)&&(I(a),b=e[a]);b&&b.refcount--===1&&(e[a]=null);return b}function E(a){return a.defaultExport!==s?a.defaultExport:a.exports}function F(a){a=D(a);if(a)return E(a)}function G(a){a=D(a);if(a)return a.defaultExport!==s?a.defaultExport:null}function H(a){a=D(a);if(a)return a.exports}function ea(a){a.factoryLength===-1&&(a.factoryLength=a.factory.length);return a.factoryLength}function I(d){var g=a.ErrorGuard;if(g&&!g.inGuard())return g.applyWithGuard(I,null,[d]);g=e[d];if(!g){var h='Requiring unknown module "%s"';throw w(h,d)}a.__onBeforeModuleFactory==null?void 0:a.__onBeforeModuleFactory(g);var i,j;if(g.hasError)if(g.error==null)throw w('Requiring module "%s" which threw an exception',d);else{h=J(g.error);K(h,{messageFormat:'Requiring module "%s" which threw an exception',messageParams:[d]});throw h}if(!V(g))throw w('Requiring module "%s" with unresolved dependencies: %s',d,v([d]));M(g);h=g.exports={};var k=g.factory,l=g.dependencies;if(u.call(k)==="[object Function]"&&l!=null){var n=l.length,p,q;try{try{xa(g)}catch(a){L(a,d)}var r=[],s=n;if(g.special&o){var x=g.special&aa?c:b;r=x.slice(0);r[x.length-2]=g;r[x.length-1]=h;s+=r.length}if(g.special&m){x=ea(g);s=Math.min(n+r.length,x)}for(h=0;h<n;h++){x=l[h];r.length<s&&r.push(F.call(null,x.id))}var z;y&&(z=A());f[g.id].factoryRun=!0;try{x=g.context!=null?g.context:a;p=k.apply(x,r)}catch(a){L(a,d)}finally{if(y){s=A();l=f[g.id];l.factoryTime=s-(z||0);l.factoryEnd=s;l.factoryStart=z}}}catch(a){g.hasError=!0;g.error=a;g.exports=null;throw a}finally{}p&&(g.exports=p);var B;g.special&ba?g.exports!=null&&t.call(g.exports,"default")&&(g.defaultExport=B=g.exports["default"]):g.defaultExport=B=g.exports;if(typeof B==="function"){n=B.__superConstructor__;if(!B.displayName||n&&n.displayName===B.displayName)try{B.displayName=(B.name||"(anonymous)")+" [from "+d+"]"}catch(a){}}g.factoryFinished=!0;da&&(g.factory=null,k=void 0)}else g.exports=k;h="__isRequired__"+d;x=e[h];x&&!V(x)&&T(h,ca);a.__onAfterModuleFactory==null?void 0:a.__onAfterModuleFactory(g)}function J(b){if(a.getErrorSafe!=null)return a.getErrorSafe(b);return b!=null&&typeof b==="object"&&typeof b.message==="string"?b:w("Non-error thrown: %s",String(b))}function K(b,c){var d=a.ErrorSerializer;d&&d.aggregateError(b,c)}function L(a,b){a=J(a);K(a,{messageFormat:'Module "%s"',messageParams:[b],forcedKey:b.startsWith("__")?null:b});throw a}function fa(){return C}function ga(){var a={};for(var b in f)Object.prototype.hasOwnProperty.call(f,b)&&(a[b]=f[b]);return a}function M(a){if(a.nonJSDeps)return;a.nonJSDeps=!0;a.dependencies&&a.dependencies.forEach(M)}var N=!!(a!=null&&a.document!=null&&"createElement"in a.document),O=typeof WorkerGlobalScope==="function";N=N||O;var P=$.use_fbt_virtual_modules===!0&&N,ha="$fbt_virtual",Q={},R=null,S=6e4;function ia(a){!(a in e)&&!(a in Q)&&(Q[a]=A()),R||(R=setTimeout(Z()(ja,"_checkFbtVirtualModuleTimeout"),S))}function ja(){R=null;var a=A(),b=Object.keys(Q).filter(function(b){var c=a-Q[b]>S;c&&delete Q[b];return c});Object.keys(Q).length>0&&(R=setTimeout(Z()(ja,"_checkFbtVirtualModuleTimeout"),S));b.length>0&&U.apply(null,[["FBLogger"],function(a){a("binary_transparency","vmod_timeout").warn("The following virtual modules are taking over %sms to be defined: %s...",S,b.join(",").slice(0,300))}])}function ka(a,b,c){if(P&&c!=null&&c&q){c=a+ha;b.push(c);ia(c)}}function T(b,c,e,g,h,i,l){c===void 0?(c=[],e=b,b=oa()):e===void 0&&(e=c,u.call(b)==="[object Array]"?(c=b,b=oa(c.join(","))):c=[]);var m={cancel:na.bind(this,b)},n=la(b);if(!c&&!e&&i){n.refcount+=i;return m}P&&(b in Q&&delete Q[b],Array.isArray(c)&&ka(b,c,g));f[b]={id:b,dependencies:c,meta:l,category:g,defined:y?A():null,factoryTime:null,factoryStart:null,factoryEnd:null,factoryRun:!1};if(n.dependencies&&n.reload!==!0){b.indexOf(":")!=-1?k++:j++;return m}i&&(n.refcount+=i);l=c.map(la);n.factory=e;n.dependencies=l;n.context=h;n.special=g;(n.nonJSDeps||va(n))&&(n.nonJSDeps=!1,M(n));W(n);if(d.length>0){var o=d;d=[];b=!wa(n)&&a.ScheduleJSWork?a.ScheduleJSWork:Ba;b(function(){if(x){for(var a=0;a<o.length;a++)F.call(null,o[a].id);o.length=0}else while(o.length>0)F.call(null,o.pop().id)})()}return m}function la(a){var b=e[a];if(b)return b;b=new ma(a,0);e[a]=b;return b}function ma(a,b,c){this.id=a,this.refcount=b,this.exports=c||null,this.defaultExport=c||s,this.factory=void 0,this.factoryLength=-1,this.factoryFinished=!1,this.dependencies=void 0,this.depPosition=0,this.context=void 0,this.special=0,this.hasError=!1,this.error=null,this.ranRecursiveSideEffects=!1,this.sideEffectDependencyException=null,this.nextDepWaitingHead=null,this.nextDepWaitingNext=null,this.tarjanGeneration=-1,this.tarjanLow=0,this.tarjanIndex=0,this.tarjanOnStack=!1,this.nonJSDeps=!1}function na(a){if(!e[a])return;var b=e[a];e[a]=null;if(b.dependencies)for(a=0;a<b.dependencies.length;a++){var c=b.dependencies[a];c.refcount--===1&&na(c.id)}}function U(a,b,c,d){c===void 0&&(c=null);d===void 0&&(d=0);var e="__requireLazy__x__"+g++;return T("__requireLazy__"+e,a,Z()(b,"requireLazy",{propagationType:0}),d|l|p,c,1)}function oa(a){return"__mod__"+(a!=null?a+"__":"")+g++}function pa(a,b,c){c.tarjanGeneration!=h&&(c.tarjanGeneration=h,c.tarjanLow=c.tarjanIndex=i++,c.tarjanOnStack=!0,b.push(c));if(c.dependencies!=null)for(var d=c.depPosition;d<c.dependencies.length;d++){var e=c.dependencies[d];e.tarjanGeneration!=h?(pa(a,b,e),c.tarjanLow=Math.min(c.tarjanLow,e.tarjanLow)):e.tarjanOnStack&&(c.tarjanLow=Math.min(c.tarjanLow,e.tarjanIndex))}if(c.tarjanLow==c.tarjanIndex){e=[];do{d=b.pop();d.tarjanOnStack=!1;e.push(d);if(c==b[0]&&d!=c&&d.dependencies!=null)for(var f=d.depPosition;f<d.dependencies.length;f++){var g=d.dependencies[f];!V(g)&&a.indexOf(g)==-1&&b.indexOf(g)==-1&&e.indexOf(g)==-1&&a.push(g)}}while(d!=c)}}function qa(a){var b=a.dependencies;if(!b)throw w("Called _replaceCycleLinkWithSCCDeps on an undefined module");h++;pa(b,[],a);a.depPosition++;W(a)}function ra(a,b){var c=b;while(!0){if(c.dependencies&&c.depPosition!=c.dependencies.length)c=c.dependencies[c.depPosition];else break;if(c==a){qa(a);return}}a.nextDepWaitingNext=b.nextDepWaitingHead;b.nextDepWaitingHead=a}function V(a){return a.dependencies!=null&&a.depPosition>=a.dependencies.length}function sa(a){a.depPosition++,W(a)}function ta(a){var b=a.nextDepWaitingHead;a.nextDepWaitingHead=null;while(b!=null){var c=b;c.nonJSDeps&&M(a);b=c.nextDepWaitingNext;c.nextDepWaitingNext=null;var d=!e[c.id];d||sa(c)}}function ua(a){return a.special&l}function va(a){return a.special&p}function wa(a){return a.special&r}function W(a){while(a.dependencies!=null&&a.depPosition<a.dependencies.length){var b=a.dependencies[a.depPosition],c=V(b);if(!c&&a!=b){ra(a,b);return}a.depPosition++}ua(a)&&d.push(a);a.nextDepWaitingHead!==null&&ta(a)}function xa(a){if(a.sideEffectDependencyException!=null)throw a.sideEffectDependencyException;if(a.ranRecursiveSideEffects)return;a.ranRecursiveSideEffects=!0;var b=a.dependencies;if(b)for(var c=0;c<b.length;c++){var d=b[c];try{xa(d)}catch(b){a.sideEffectDependencyException=b;throw b}if(d.special&n)try{F.call(null,d.id)}catch(b){a.sideEffectDependencyException=b;throw b}}}function X(a,b){e[a]=new ma(a,0,b),f[a]={id:a,dependencies:[],category:0,factoryLengthAccessTime:null,factoryTime:null,factoryStart:null,factoryEnd:null,factoryRun:!1}}X("module",0);X("exports",0);X("define",T);X("global",a);X("require",F);X("requireInterop",F);X("importDefault",G);X("importNamespace",H);X("requireDynamic",ya);X("requireLazy",U);X("requireWeak",Y);X("ifRequired",za);X("ifRequireable",Aa);b=[F.call(null,"global"),F.call(null,"require"),F.call(null,"requireDynamic"),F.call(null,"requireLazy"),F.call(null,"requireInterop"),null];c=[F.call(null,"global"),F.call(null,"require"),F.call(null,"importDefault"),F.call(null,"importNamespace"),F.call(null,"requireLazy"),F.call(null,"requireInterop"),null];T.amd={};a.define=T;a.require=F;a.requireInterop=F;a.importDefault=G;a.importNamespace=H;a.requireDynamic=ya;a.requireLazy=U;a.__onBeforeModuleFactory=null;a.__onAfterModuleFactory=null;function ya(a,b){throw new ReferenceError("requireDynamic is not defined")}function Y(a,b){za.call(null,a,function(a){b(a)},function(){T("__requireWeak__"+a+"__"+g++,["__isRequired__"+a],Z()(function(){return b(E(e[a]))},"requireWeak"),l,null,1)})}function za(a,b,c){a=e[a];if(a&&a.factoryFinished){if(typeof b==="function")return b(E(a))}else if(typeof c==="function")return c()}function Aa(a,b,c){var d=e[a];if(d&&d.nonJSDeps&&V(d)){if(typeof b==="function")return b(F.call(null,a))}else if(typeof c==="function")return c()}O={getDupCount:function(){return[j,k]},getModules:function(){var a={};for(var b in e)e[b]&&Object.prototype.hasOwnProperty.call(e,b)&&(a[b]=e[b]);return a},modulesMap:e,debugUnresolvedDependencies:v};function Ba(a){return a}function Z(){var b=a.TimeSlice&&a.TimeSlice.guard?a.TimeSlice.guard:Ba;return function(){return b.apply(void 0,arguments)}}X("__getTotalRequireCalls",fa);X("__getModuleTimeDetails",ga);X("__debug",O);a.__d=function(a,b,c,d){Z()(function(){T(a,b,c,(d||m)|o,null,null,null)},"define "+a,{root:!0})()};function $(a,b){return!0}if(a.__d_stub){for($=0;$<a.__d_stub.length;$++)a.__d.apply(null,a.__d_stub[$]);delete a.__d_stub}if(a.__rl_stub){for(N=0;N<a.__rl_stub.length;N++)U.apply(null,a.__rl_stub[N]);delete a.__rl_stub}Y=function(){};a.$RefreshReg$=Y;a.$RefreshSig$=function(){return function(a){return a}}})(typeof this!=="undefined"?this:typeof global!=="undefined"?global:typeof window!=="undefined"?window:typeof self!=="undefined"?self:{});
(function(a){var b=a.performance;b&&b.setResourceTimingBufferSize&&(b.setResourceTimingBufferSize(1e5),b.onresourcetimingbufferfull=function(){a.__isresourcetimingbufferfull=!0},b.setResourceTimingBufferSize=function(){})})(typeof this==="object"?this:typeof global==="object"?global:typeof window==="object"?window:typeof self==="object"?self:{});

__d("ExecutionEnvironment",[],(function(a,b,c,d,e,f){"use strict";b=!!(a!==void 0&&a.document&&a.document.createElement);c=typeof WorkerGlobalScope==="function";d=typeof SharedWorkerGlobalScope==="function"&&self instanceof SharedWorkerGlobalScope;e=!c&&b;a={canUseDOM:b,canUseEventListeners:b&&!!(a.addEventListener||a.attachEvent),canUseViewport:b&&!!window.screen,canUseWorkers:typeof Worker!=="undefined",isInBrowser:b||c,isInMainThread:e,isInSharedWorker:d,isInWorker:c};b=a;f["default"]=b}),66);
__d("BootloaderDocumentInserter",["ExecutionEnvironment"],(function(a,b,c,d,e,f,g){"use strict";var h,i=null;function j(){i||(i=document.head||document.getElementsByTagName("head")[0]||document.body);return i}function a(a){if((h||(h=c("ExecutionEnvironment"))).isInWorker){a(null);return}var b=document.createDocumentFragment();a(b);j().appendChild(b)}g.getDOMContainerNode=j;g.batchDOMInsert=a}),98);
__d("Env",[],(function(a,b,c,d,e,f){b={ajaxpipe_token:null,compat_iframe_token:null,iframeKey:"",iframeTarget:"",iframeToken:"",isCQuick:!1,jssp_header_sent:!1,jssp_targeting_enabled:!1,loadHyperion:!1,start:Date.now(),nocatch:!1,useTrustedTypes:!1,isTrustedTypesReportOnly:!1,enableDefaultTrustedTypesPolicy:!1,ig_server_override:"",barcelona_server_override:"",ig_mqtt_wss_endpoint:"",ig_mqtt_polling_endpoint:""};a.Env&&Object.assign(b,a.Env);a.Env=b;c=b;f["default"]=c}),66);
__d("fb-error-lite",[],(function(a,b,c,d,e,f){"use strict";var g={PREVIOUS_FILE:1,PREVIOUS_FRAME:2,PREVIOUS_DIR:3,FORCED_KEY:4};function a(a){var b=new Error(a);if(b.stack===void 0)try{throw b}catch(a){}b.messageFormat=a;for(var c=arguments.length,d=new Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];b.messageParams=d.map(function(a){return String(a)});b.taalOpcodes=[g.PREVIOUS_FRAME];return b}b={err:a,TAALOpcode:g};f["default"]=b}),66);
__d("sprintf",[],(function(a,b,c,d,e,f){function a(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];var e=0;return a.replace(/%s/g,function(){return String(c[e++])})}f["default"]=a}),66);
__d("invariant",["Env","fb-error-lite","sprintf"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){if(!a){var d=b;for(var e=arguments.length,f=new Array(e>2?e-2:0),g=2;g<e;g++)f[g-2]=arguments[g];if(typeof d==="number"){var h=i(d,f),j=h.message,k=h.decoderLink;d=j;f.unshift(k)}else if(d===void 0){d="Invariant: ";for(var l=0;l<f.length;l++)d+="%s,"}var m=d,n=new Error(m);n.name="Invariant Violation";n.messageFormat=d;n.messageParams=f.map(function(a){return String(a)});n.taalOpcodes=[c("fb-error-lite").TAALOpcode.PREVIOUS_FRAME];n.stack;throw n}}function i(a,b){var d="Minified invariant #"+a+"; %s";b.length>0&&(d+=" Params: "+b.map(function(a){return"%s"}).join(", "));a=(h||(h=c("Env"))).show_invariant_decoder===!0?"visit "+j(a,b)+" to see the full message.":"";return{message:d,decoderLink:a}}function j(a,b){a="https://www.internalfb.com/intern/invariant/"+a+"/";b.length>0&&(a+="?"+b.map(function(a,b){return"args["+b+"]="+encodeURIComponent(String(a))}).join("&"));return a}g["default"]=a}),98);
__d("ArbiterToken",["invariant"],(function(a,b,c,d,e,f,g,h){"use strict";a=function(){function a(a,b){this.unsubscribe=function(){for(var a=0;a<this.$2.length;a++)this.$2[a].remove();this.$2.length=0},this.$1=a,this.$2=b}var b=a.prototype;b.isForArbiterInstance=function(a){this.$1||h(0,2506);return this.$1===a};return a}();g["default"]=a}),98);
__d("performance",[],(function(a,b,c,d,e,f){"use strict";b=a.performance||a.msPerformance||a.webkitPerformance||{};c=b;f["default"]=c}),66);
__d("performanceNow",["performance"],(function(a,b,c,d,e,f,g){var h;if((h||(h=c("performance"))).now)b=function(){return(h||(h=c("performance"))).now()};else{d=a._cstart;e=Date.now();var i=typeof d==="number"&&d<e?d:e,j=0;b=function(){var a=Date.now(),b=a-i;b<j&&(i-=j-b,b=a-i);j=b;return b}}f=b;g["default"]=f}),98);
__d("removeFromArray",[],(function(a,b,c,d,e,f){function a(a,b){b=a.indexOf(b);b!==-1&&a.splice(b,1)}f["default"]=a}),66);
__d("fb-error",["performanceNow","removeFromArray"],(function(a,b,c,d,e,f){"use strict";var g,h={PREVIOUS_FILE:1,PREVIOUS_FRAME:2,PREVIOUS_DIR:3,FORCED_KEY:4};function i(b){var a=new Error(b);if(a.stack===void 0)try{throw a}catch(a){}a.messageFormat=b;for(var c=arguments.length,d=new Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];a.messageParams=d.map(function(a){return String(a)});a.taalOpcodes=[h.PREVIOUS_FRAME];return a}var j=!1,k={errorListener:function(b){var c=a.console,d=c[b.type]?b.type:"error";if(b.type==="fatal"||d==="error"&&!j){d=b.message;c.error("ErrorUtils caught an error:\n\n"+d+"\n\nSubsequent non-fatal errors won't be logged; see https://fburl.com/debugjs.",b);j=!0}}},l={skipDupErrorGuard:!1},m={config:l,setup:c},n=!1;function c(a){n===!1&&(n=!0,m.config=Object.freeze(a))}var o={access_token:null},p=6,q=6e4,r=10*q,s=new Map(),t=0;function u(){var a=(g||(g=b("performanceNow")))();if(a>t+q){var c=a-r;for(var d of s){var e=d[0],f=d[1];f.lastAccessed<c&&s["delete"](e)}t=a}}function aa(a){u();var c=(g||(g=b("performanceNow")))(),d=s.get(a);if(d==null){s.set(a,{dropped:0,logged:[c],lastAccessed:c});return 1}a=d.dropped;var e=d.logged;d.lastAccessed=c;while(e[0]<c-q)e.shift();if(e.length<p){d.dropped=0;e.push(c);return a+1}else{d.dropped++;return null}}var v={shouldLog:function(a){return aa(a.hash)}},ba="RE_EXN_ID";function w(a){var b=null;a==null||typeof a!=="object"?b=i("Non-object thrown: %s",String(a)):Object.prototype.hasOwnProperty.call(a,ba)?b=i("Rescript exception thrown: %s",JSON.stringify(a)):typeof (a===null||a===void 0?void 0:a.then)==="function"?b=i("Promise thrown: %s",JSON.stringify(a)):typeof a.message!=="string"?b=i("Non-error thrown: %s, keys: %s",String(a),JSON.stringify(Object.keys(a).sort())):a.messageFormat!=null&&typeof a.messageFormat!=="string"?b=i("Error with non-string messageFormat thrown: %s, %s, keys: %s",String(a.message),String(a),JSON.stringify(Object.keys(a).sort())):Object.isExtensible&&!Object.isExtensible(a)&&(b=i("Non-extensible thrown: %s",String(a.message)));if(b!=null){b.taalOpcodes=b.taalOpcodes||[];b.taalOpcodes.push(h.PREVIOUS_FRAME);return b}return a}var ca=typeof window==="undefined"?"<self.onerror>":"<window.onerror>",x;function da(a){var b=a.error!=null?w(a.error):i(a.message||"");b.fileName==null&&a.filename!=null&&(b.fileName=a.filename);b.line==null&&a.lineno!=null&&(b.line=a.lineno);b.column==null&&a.colno!=null&&(b.column=a.colno);b.guardList=[ca];b.loggingSource="ONERROR";(a=x)===null||a===void 0?void 0:a.reportError(b)}var y={setup:function(b){if(typeof a.addEventListener!=="function")return;if(x!=null)return;x=b;a.addEventListener("error",da)}},z=[],A={pushGuard:function(a){z.unshift(a)},popGuard:function(){z.shift()},inGuard:function(){return z.length!==0},cloneGuardList:function(){return z.map(function(a){return a.name})},findDeferredSource:function(){for(var a of z)if(a.deferredSource!=null)return a.deferredSource}};function ea(a){if(a.type!=null)return a.type;if(a.loggingSource=="GUARDED"||a.loggingSource=="ERROR_BOUNDARY")return"fatal";if(a.name=="SyntaxError")return"fatal";if(a.loggingSource=="ONERROR"&&a.message.indexOf("ResizeObserver loop")>=0)return"warn";return a.stack!=null&&a.stack.indexOf("chrome-extension://")>=0?"warn":"error"}var B=[],C=function(){function a(){this.metadata=[].concat(B)}var b=a.prototype;b.addEntries=function(){var a;(a=this.metadata).push.apply(a,arguments);return this};b.addEntry=function(a,b,c){this.metadata.push([a,b,c]);return this};b.isEmpty=function(){return this.metadata.length===0};b.clearEntries=function(){this.metadata=[]};b.format=function(){var a=[];this.metadata.forEach(function(b){if(b&&b.length){b=b.map(function(a){return a!=null?String(a).replace(/:/g,"_"):""}).join(":");a.push(b)}});return a};b.getAll=function(){return this.metadata};a.addGlobalMetadata=function(a,b,c){B.push([a,b,c])};a.getGlobalMetadata=function(){return B};a.unsetGlobalMetadata=function(a,b){B=B.filter(function(c){return!(Array.isArray(c)&&c[0]===a&&c[1]===b)})};return a}(),D={debug:1,info:2,warn:3,error:4,fatal:5};function d(a,b){if(Object.isFrozen(a))return;b.type&&((!a.type||D[a.type]>D[b.type])&&(a.type=b.type));var c=b.metadata;if(c!=null){var d;d=(d=a.metadata)!==null&&d!==void 0?d:new C();c!=null&&d.addEntries.apply(d,c.getAll());a.metadata=d}b.project!=null&&(a.project=b.project);b.errorName!=null&&(a.errorName=b.errorName);b.componentStack!=null&&(a.componentStack=b.componentStack);b.deferredSource!=null&&(a.deferredSource=b.deferredSource);b.blameModule!=null&&(a.blameModule=b.blameModule);b.loggingSource!=null&&(a.loggingSource=b.loggingSource);d=(c=a.messageFormat)!==null&&c!==void 0?c:a.message;c=(c=a.messageParams)!==null&&c!==void 0?c:[];if(d!==b.messageFormat&&b.messageFormat!=null){var e;d+=" [Caught in: "+b.messageFormat+"]";c.push.apply(c,(e=b.messageParams)!==null&&e!==void 0?e:[])}a.messageFormat=d;a.messageParams=c;e=b.forcedKey;d=a.forcedKey;c=e!=null&&d!=null?e+"_"+d:e!==null&&e!==void 0?e:d;a.forcedKey=c}function f(a){var b;return fa((b=a.messageFormat)!==null&&b!==void 0?b:a.message,a.messageParams||[])}function fa(a,b){var c=0;a=String(a);a=a.replace(/%s/g,function(){return c<b.length?b[c++]:"NOPARAM"});c<b.length&&(a+=" PARAMS"+JSON.stringify(b.slice(c)));return a}function ga(a){return(a!==null&&a!==void 0?a:[]).map(function(a){return String(a)})}var E={aggregateError:d,toReadableMessage:f,toStringParams:ga},ha=5,F=[];function G(a){F.push(a),F.length>ha&&F.shift()}function ia(a){var b=a.getAllResponseHeaders();if(b!=null&&b.indexOf("X-FB-Debug")>=0){b=a.getResponseHeader("X-FB-Debug");b&&G(b)}}function ja(){return F}var H={add:G,addFromXHR:ia,getAll:ja},ka="abcdefghijklmnopqrstuvwxyz012345";function I(){var a=0;for(var b=arguments.length,c=new Array(b),d=0;d<b;d++)c[d]=arguments[d];for(var e of c)if(e!=null){var f=e.length;for(var g=0;g<f;g++)a=(a<<5)-a+e.charCodeAt(g)}var h="";for(var i=0;i<6;i++)h=ka.charAt(a&31)+h,a>>=5;return h}var la=[/\(([^\s\)\()]+):(\d+):(\d+)\)$/,/@([^\s\)\()]+):(\d+):(\d+)$/,/^([^\s\)\()]+):(\d+):(\d+)$/,/^at ([^\s\)\()]+):(\d+):(\d+)$/],ma=/^\w+:\s.*?\n/g;Error.stackTraceLimit!=null&&Error.stackTraceLimit<80&&(Error.stackTraceLimit=80);function na(a){var b=a.name,c=a.message;a=a.stack;if(a==null)return null;if(b!=null&&c!=null&&c!==""){var d=b+": "+c+"\n";if(a.startsWith(d))return a.substr(d.length);if(a===b+": "+c)return null}if(b!=null){d=b+"\n";if(a.startsWith(d))return a.substr(d.length)}if(c!=null&&c!==""){b=": "+c+"\n";d=a.indexOf(b);c=a.substring(0,d);if(/^\w+$/.test(c))return a.substring(d+b.length)}return a.replace(ma,"")}function J(a){a=a.trim();var b;a;var c,d,e;if(a.includes("charset=utf-8;base64,"))b="<inlined-file>";else{var f;for(var g of la){f=a.match(g);if(f!=null)break}f!=null&&f.length===4?(c=f[1],d=parseInt(f[2],10),e=parseInt(f[3],10),b=a.substring(0,a.length-f[0].length)):b=a;b=b.replace(/^at /,"").trim()}g={identifier:b,script:c,line:d,column:e};g.text=K(g);return g}function oa(a){return a==null||a===""?[]:a.split(/\n\n/)[0].split("\n").map(J)}function pa(a){a=na(a);return oa(a)}function qa(a){if(a==null||a==="")return null;a=a.split("\n");a.splice(0,1);return a.map(function(a){return a.trim()})}function K(a){var b=a.identifier,c=a.script,d=a.line;a=a.column;b="    at "+(b!==null&&b!==void 0?b:"<unknown>");c!=null&&d!=null&&a!=null&&(b+=" ("+c+":"+d+":"+a+")");return b}function L(c){var d,e,f,i,j,k,l=pa(c);d=(d=c.taalOpcodes)!==null&&d!==void 0?d:[];var m=c.framesToPop;if(m!=null){m=Math.min(m,l.length);while(m-->0)d.unshift(h.PREVIOUS_FRAME)}m=(m=c.messageFormat)!==null&&m!==void 0?m:c.message;e=((e=c.messageParams)!==null&&e!==void 0?e:[]).map(function(a){return String(a)});var n=qa(c.componentStack),o=n==null?null:n.map(J),p=c.metadata?c.metadata.format():new C().format();p.length===0&&(p=void 0);var q=l.map(function(a){return a.text}).join("\n");f=(f=c.errorName)!==null&&f!==void 0?f:c.name;var r=ea(c),s=c.loggingSource,t=c.project;i=(i=c.lineNumber)!==null&&i!==void 0?i:c.line;j=(j=c.columnNumber)!==null&&j!==void 0?j:c.column;k=(k=c.fileName)!==null&&k!==void 0?k:c.sourceURL;var u=l.length>0;u&&i==null&&(i=l[0].line);u&&j==null&&(j=l[0].column);u&&k==null&&(k=l[0].script);o={blameModule:c.blameModule,cause:c.cause,column:j==null?null:String(j),clientTime:Math.floor(Date.now()/1e3),componentStackFrames:o,deferredSource:c.deferredSource!=null?L(c.deferredSource):null,extra:(u=c.extra)!==null&&u!==void 0?u:{},fbtrace_id:c.fbtrace_id,guardList:(j=c.guardList)!==null&&j!==void 0?j:[],hash:I(f,q,r,t,s),isNormalizedError:!0,line:i==null?null:String(i),loggingSource:s,message:E.toReadableMessage(c),messageFormat:m,messageParams:e,metadata:p,name:f,page_time:Math.floor((g||(g=b("performanceNow")))()),project:t,reactComponentStack:n,script:k,serverHash:c.serverHash,stack:q,stackFrames:l,type:r,xFBDebug:H.getAll()};c.forcedKey!=null&&(o.forcedKey=c.forcedKey);d.length>0&&(o.taalOpcodes=d);u=a.location;u&&(o.windowLocationURL=u.href);for(j in o)o[j]==null&&delete o[j];return o}function ra(a){return a!=null&&typeof a==="object"&&a.isNormalizedError===!0?a:null}var M={formatStackFrame:K,normalizeError:L,ifNormalizedError:ra},sa="<global.react>",N=[],O=[],P=50,Q=!1,R={history:O,addListener:function(a,b){b===void 0&&(b=!1),N.push(a),b||O.forEach(function(b){return a(b,(b=b.loggingSource)!==null&&b!==void 0?b:"DEPRECATED")})},unshiftListener:function(a){N.unshift(a)},removeListener:function(a){b("removeFromArray")(N,a)},reportError:function(a){a=M.normalizeError(a);R.reportNormalizedError(a)},reportNormalizedError:function(b){if(Q)return!1;var a=A.cloneGuardList();b.componentStackFrames&&a.unshift(sa);a.length>0&&(b.guardList=a);if(b.deferredSource==null){a=A.findDeferredSource();a!=null&&(b.deferredSource=M.normalizeError(a))}O.length>P&&O.splice(P/2,1);O.push(b);Q=!0;for(a=0;a<N.length;a++)try{var c;N[a](b,(c=b.loggingSource)!==null&&c!==void 0?c:"DEPRECATED")}catch(a){}Q=!1;return!0}};R.addListener(k.errorListener);var ta="<anonymous guard>",S=!1,T={applyWithGuard:function(a,b,c,d){if(m.config.skipDupErrorGuard&&"__isMetaErrorGuarded"in a)return a.apply(b,c);A.pushGuard({name:((d===null||d===void 0?void 0:d.name)!=null?d.name:null)||(a.name?"func_name:"+a.name:null)||ta,deferredSource:d===null||d===void 0?void 0:d.deferredSource});if(S)try{return a.apply(b,c)}finally{A.popGuard()}try{return Function.prototype.apply.call(a,b,c)}catch(h){try{b=d!==null&&d!==void 0?d:babelHelpers["extends"]({},null);var e=b.deferredSource,f=b.onError;b=b.onNormalizedError;var g=w(h);e={deferredSource:e,loggingSource:"GUARDED",project:(e=d===null||d===void 0?void 0:d.project)!==null&&e!==void 0?e:"ErrorGuard",type:d===null||d===void 0?void 0:d.errorType};E.aggregateError(g,e);d=M.normalizeError(g);g==null&&a&&(d.extra[a.toString().substring(0,100)]="function",c!=null&&c.length&&(d.extra[Array.from(c).toString().substring(0,100)]="args"));d.guardList=A.cloneGuardList();f&&f(g);b&&b(d);R.reportNormalizedError(d)}catch(a){}}finally{A.popGuard()}},guard:function(a,b){function c(){for(var c=arguments.length,d=new Array(c),e=0;e<c;e++)d[e]=arguments[e];return T.applyWithGuard(a,this,d,b)}c.__isMetaErrorGuarded=!0;a.__SMmeta&&(c.__SMmeta=a.__SMmeta);return c},inGuard:function(){return A.inGuard()},skipGuardGlobal:function(a){S=a}},U=1024,V=[],W=0;function X(a){return String(a)}function Y(a){return a==null?null:String(a)}function ua(a,b){var c={};b&&b.forEach(function(a){c[a]=!0});Object.keys(a).forEach(function(b){a[b]?c[b]=!0:c[b]&&delete c[b]});return Object.keys(c)}function Z(a){return(a!==null&&a!==void 0?a:[]).map(function(a){return{column:Y(a.column),identifier:a.identifier,line:Y(a.line),script:a.script}})}function va(a){a=String(a);return a.length>U?a.substring(0,U-3)+"...":a}function wa(a,b){var c;c={appId:Y(b.appId),cavalry_lid:b.cavalry_lid,access_token:o.access_token,ancestor_hash:a.hash,bundle_variant:(c=b.bundle_variant)!==null&&c!==void 0?c:null,clientTime:X(a.clientTime),column:a.column,componentStackFrames:Z(a.componentStackFrames),events:a.events,extra:ua(a.extra,b.extra),forcedKey:a.forcedKey,frontend_env:(c=b.frontend_env)!==null&&c!==void 0?c:null,guardList:a.guardList,line:a.line,loggingFramework:b.loggingFramework,messageFormat:va(a.messageFormat),messageParams:a.messageParams.map(va),name:a.name,sample_weight:Y(b.sample_weight),script:a.script,site_category:b.site_category,stackFrames:Z(a.stackFrames),type:a.type,page_time:Y(a.page_time),project:a.project,push_phase:b.push_phase,report_source:b.report_source,report_source_ref:b.report_source_ref,rollout_hash:(c=b.rollout_hash)!==null&&c!==void 0?c:null,script_path:b.script_path,server_revision:Y(b.server_revision),spin:Y(b.spin),svn_rev:String(b.client_revision),additional_client_revisions:Array.from((c=b.additional_client_revisions)!==null&&c!==void 0?c:[]).map(X),taalOpcodes:a.taalOpcodes==null?null:a.taalOpcodes.map(function(a){return a}),web_session_id:b.web_session_id,version:"3",xFBDebug:a.xFBDebug,tags:a.tags};b=a.blameModule;var d=a.deferredSource;b!=null&&(c.blameModule=String(b));d&&d.stackFrames&&(c.deferredSource={stackFrames:Z(d.stackFrames)});a.metadata&&(c.metadata=a.metadata);a.loadingUrls&&(c.loadingUrls=a.loadingUrls);a.serverHash!=null&&(c.serverHash=a.serverHash);a.windowLocationURL!=null&&(c.windowLocationURL=a.windowLocationURL);a.loggingSource!=null&&(c.loggingSource=a.loggingSource);return c}function xa(a,b,c){var d;W++;if(b.sample_weight===0)return!1;var e=v.shouldLog(a);if(e==null)return!1;if((d=b.projectBlocklist)!==null&&d!==void 0&&d.includes(a.project))return!1;d=wa(a,b);Object.assign(d,{ancestors:V.slice(),clientWeight:X(e),page_position:X(W)});V.length<15&&["fatal","error"].includes(a.type)&&V.push(a.hash);c(d);return!0}var ya={createErrorPayload:wa,postError:xa},$=null,za=!1;function Aa(a){if($==null)return;var b=$,c=a.reason,d,e=w(c),f=null;if(c!==e&&typeof c==="object"&&c!==null){d=Object.keys(c).sort().slice(0,3);typeof c.message!=="string"&&typeof c.messageFormat==="string"&&(c.message=c.messageFormat,e=w(c));if(typeof c.message!=="string"&&typeof c.errorMsg==="string")if(/^\s*\<!doctype/i.test(c.errorMsg)){var g=/<title>([^<]+)<\/title>(?:(?:.|\n)*<h1>([^<]+)<\/h1>)?/im.exec(c.errorMsg);if(g){var h;e=i('HTML document with title="%s" and h1="%s"',(h=g[1])!==null&&h!==void 0?h:"",(h=g[2])!==null&&h!==void 0?h:"")}else e=i("HTML document sanitized")}else/^\s*<\?xml/i.test(c.errorMsg)?e=i("XML document sanitized"):(c.message=c.errorMsg,e=w(c));e!==c&&typeof c.name==="string"&&(f=c.name);typeof c.name!=="string"&&typeof c.errorCode==="string"&&(f="UnhandledRejectionWith_errorCode_"+c.errorCode);typeof c.name!=="string"&&typeof c.error==="number"&&(f="UnhandledRejectionWith_error_"+String(c.error))}e.loggingSource="ONUNHANDLEDREJECTION";try{f=e===c&&f!=null&&f!==""?f:typeof (c===null||c===void 0?void 0:c.name)==="string"&&c.name!==""?c.name:d!=null&&d.length>0?"UnhandledRejectionWith_"+d.join("_"):"UnhandledRejection_"+(c===null?"null":typeof c),e.name=f}catch(a){}try{g=c===null||c===void 0?void 0:c.stack;(typeof g!=="string"||g==="")&&(g=e.stack);(typeof g!=="string"||g==="")&&(g=i("").stack);e.stack=e.name+": "+e.message+"\n"+g.split("\n").slice(1).join("\n")}catch(a){}try{h=a.promise;e.stack=e.stack+(h!=null&&typeof h.settledStack==="string"?"\n    at <promise_settled_stack_below>\n"+h.settledStack:"")+(h!=null&&typeof h.createdStack==="string"?"\n    at <promise_created_stack_below>\n"+h.createdStack:"")}catch(a){}try{f=a.promise;"__isPromiseWithTracing"in f&&f.__isPromiseWithTracing===!0&&f.deferredError!=null&&(e.deferredSource=w(f.deferredError))}catch(a){}b.reportError(e);a.preventDefault()}function Ba(b){$=b,typeof a.addEventListener==="function"&&!za&&(za=!0,a.addEventListener("unhandledrejection",Aa))}var Ca={onunhandledrejection:Aa,setup:Ba};l={preSetup:function(a){(a==null||a.ignoreOnError!==!0)&&y.setup(R),(a==null||a.ignoreOnUnahndledRejection!==!0)&&Ca.setup(R)},setup:function(a,b,c){R.addListener(function(d){var e;e=babelHelpers["extends"]({},a,{},(e=c===null||c===void 0?void 0:c())!==null&&e!==void 0?e:{});ya.postError(d,e,b)})}};var Da=20,Ea=function(){function a(a){var b=this;this.FATAL=function(a){var c=a.join("%s");for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];b.fatal.apply(b,[c].concat(e))};this.MUSTFIX=function(a){var c=a.join("%s");for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];b.mustfix.apply(b,[c].concat(e))};this.WARN=function(a){var c=a.join("%s");for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];b.warn.apply(b,[c].concat(e))};this.INFO=function(a){var c=a.join("%s");for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];b.info.apply(b,[c].concat(e))};this.DEBUG=function(a){var c=a.join("%s");for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];b.debug.apply(b,[c].concat(e))};this.project=a;this.events=[];this.metadata=new C();this.taalOpcodes=[]}var b=a.prototype;b.$1=function(b,c){var d=String(c),e=this.events,f=this.project,g=this.metadata,i=this.blameModule,j=this.forcedKey,k=this.error,l;for(var m=arguments.length,n=new Array(m>2?m-2:0),o=2;o<m;o++)n[o-2]=arguments[o];if(this.normalizedError)l=babelHelpers["extends"]({},this.normalizedError,{messageFormat:this.normalizedError.messageFormat+" [Caught in: "+d+"]",messageParams:E.toStringParams([].concat(this.normalizedError.messageParams,n)),project:f,type:b,loggingSource:"FBLOGGER"}),l.message=E.toReadableMessage(l),j!=null&&(l.forcedKey=l.forcedKey!=null?j+"_"+l.forcedKey:j);else if(k)this.taalOpcodes.length>0&&new a("fblogger").blameToPreviousFrame().blameToPreviousFrame().warn("Blame helpers do not work with catching"),E.aggregateError(k,{messageFormat:d,messageParams:E.toStringParams(n),errorName:k.name,forcedKey:j,project:f,type:b,loggingSource:"FBLOGGER"}),l=M.normalizeError(k);else{k=new Error(d);if(k.stack===void 0)try{throw k}catch(a){}k.messageFormat=d;k.messageParams=E.toStringParams(n);k.blameModule=i;k.forcedKey=j;k.project=f;k.type=b;k.loggingSource="FBLOGGER";k.taalOpcodes=[h.PREVIOUS_FRAME,h.PREVIOUS_FRAME].concat(this.taalOpcodes);l=M.normalizeError(k);l.name="FBLogger"}if(!g.isEmpty())if(l.metadata==null)l.metadata=g.format();else{var p=l.metadata.concat(g.format()),q=new Set(p);l.metadata=Array.from(q.values())}if(e.length>0){if(l.events!=null){var r;(r=l.events).push.apply(r,e)}else l.events=[].concat(e);if(l.events!=null&&l.events.length>Da){var s=l.events.length-Da;l.events.splice(0,s+1,"<first "+s+" events omitted>")}}l.tags=this.loggerTags;R.reportNormalizedError(l);return k};b.fatal=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];this.$1.apply(this,["fatal",a].concat(c))};b.mustfix=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];this.$1.apply(this,["error",a].concat(c))};b.warn=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];this.$1.apply(this,["warn",a].concat(c))};b.info=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];this.$1.apply(this,["info",a].concat(c))};b.debug=function(a){};b.mustfixThrow=function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];var e=this.$1.apply(this,["error",a].concat(c));e||(e=i("mustfixThrow does not support catchingNormalizedError"),e.taalOpcodes=e.taalOpcodes||[],e.taalOpcodes.push(h.PREVIOUS_FRAME));try{e.message=E.toReadableMessage(e)}catch(a){}throw e};b.catching=function(b){!(b instanceof Error)?new a("fblogger").blameToPreviousFrame().warn("Catching non-Error object is not supported"):this.error=b;return this};b.catchingNormalizedError=function(a){this.normalizedError=a;return this};b.event=function(a){this.events.push(a);return this};b.blameToModule=function(a){this.blameModule=a;return this};b.blameToPreviousFile=function(){this.taalOpcodes.push(h.PREVIOUS_FILE);return this};b.blameToPreviousFrame=function(){this.taalOpcodes.push(h.PREVIOUS_FRAME);return this};b.blameToPreviousDirectory=function(){this.taalOpcodes.push(h.PREVIOUS_DIR);return this};b.addToCategoryKey=function(a){this.forcedKey=a;return this};b.addMetadata=function(a,b,c){this.metadata.addEntry(a,b,c);return this};b.tags=function(a){this.loggerTags=a;return this};return a}();c=function(a,b){var c=new Ea(a);return b!=null?c.event(a+"."+b):c};c.addGlobalMetadata=function(a,b,c){C.addGlobalMetadata(a,b,c)};var Fa="<CUSTOM_NAME:",Ga=">";function Ha(a,b){if(a!=null&&b!=null)try{Object.defineProperty(a,"name",{value:Fa+" "+b+Ga})}catch(a){}return a}d={blameToPreviousFile:function(a){var b;a.taalOpcodes=(b=a.taalOpcodes)!==null&&b!==void 0?b:[];a.taalOpcodes.push(h.PREVIOUS_FILE);return a},blameToPreviousFrame:function(a){var b;a.taalOpcodes=(b=a.taalOpcodes)!==null&&b!==void 0?b:[];a.taalOpcodes.push(h.PREVIOUS_FRAME);return a},blameToPreviousDirectory:function(a){var b;a.taalOpcodes=(b=a.taalOpcodes)!==null&&b!==void 0?b:[];a.taalOpcodes.push(h.PREVIOUS_DIR);return a}};f={err:i,ErrorBrowserConsole:k,ErrorConfig:m,ErrorDynamicData:o,ErrorFilter:v,ErrorGlobalEventHandler:y,ErrorGuard:T,ErrorGuardState:A,ErrorMetadata:C,ErrorNormalizeUtils:M,ErrorPoster:ya,ErrorPubSub:R,ErrorSerializer:E,ErrorSetup:l,ErrorXFBDebug:H,FBLogger:c,getErrorSafe:w,getSimpleHash:I,TAAL:d,TAALOpcode:h,renameFunction:Ha};e.exports=f}),null);
__d("ErrorGuard",["fb-error"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("fb-error").ErrorGuard}),98);
__d("CallbackDependencyManager",["ErrorGuard"],(function(a,b,c,d,e,f){var g;a=function(){"use strict";function a(){this.$1=new Map(),this.$2=new Map(),this.$3=1,this.$4=new Map()}var c=a.prototype;c.$5=function(a,b){var c=0,d=new Set();for(var e=0,f=b.length;e<f;e++)d.add(b[e]);for(b of d.keys()){if(this.$4.get(b))continue;c++;e=this.$1.get(b);e===void 0&&(e=new Map(),this.$1.set(b,e));e.set(a,(e.get(a)||0)+1)}return c};c.$6=function(a){a=this.$1.get(a);if(!a)return;for(var c of a.entries()){var d=c[0],e=c[1]-1;a.set(d,e);e<=0&&a["delete"](d);e=this.$2.get(d);if(e!==void 0){e.$7--;if(e.$7<=0){e=e.$8;this.$2["delete"](d);(g||(g=b("ErrorGuard"))).applyWithGuard(e,null,[])}}}};c.addDependenciesToExistingCallback=function(a,b){var c=this.$2.get(a);if(!c)return null;b=this.$5(a,b);c.$7+=b;return a};c.isPersistentDependencySatisfied=function(a){return!!this.$4.get(a)};c.satisfyPersistentDependency=function(a){this.$4.set(a,1),this.$6(a)};c.satisfyNonPersistentDependency=function(a){var b=this.$4.get(a)===1;b||this.$4.set(a,1);this.$6(a);b||this.$4["delete"](a)};c.registerCallback=function(a,c){var d=this.$3;this.$3++;c=this.$5(d,c);if(c===0){(g||(g=b("ErrorGuard"))).applyWithGuard(a,null,[]);return null}this.$2.set(d,{$8:a,$7:c});return d};return a}();e.exports=a}),null);
__d("EventSubscription",[],(function(a,b,c,d,e,f){"use strict";a=function(a){var b=this;this.remove=function(){b.subscriber&&(b.subscriber.removeSubscription(b),b.subscriber=null)};this.subscriber=a};f["default"]=a}),66);
__d("EmitterSubscription",["EventSubscription"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d){b=a.call(this,b)||this;b.listener=c;b.context=d;return b}return b}(c("EventSubscription"));g["default"]=a}),98);
__d("EventSubscriptionVendor",["invariant"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(){this.$1={}}var b=a.prototype;b.addSubscription=function(a,b){b.subscriber===this||g(0,2828);this.$1[a]||(this.$1[a]=[]);var c=this.$1[a].length;this.$1[a].push(b);b.eventType=a;b.key=c;return b};b.removeAllSubscriptions=function(a){a===void 0?this.$1={}:delete this.$1[a]};b.removeSubscription=function(a){var b=a.eventType;a=a.key;b=this.$1[b];b&&delete b[a]};b.getSubscriptionsForType=function(a){return this.$1[a]};return a}();e.exports=a}),null);
__d("emptyFunction",[],(function(a,b,c,d,e,f){"use strict";function a(a){return function(){return a}}b=function(){};b.thatReturns=a;b.thatReturnsFalse=a(!1);b.thatReturnsTrue=a(!0);b.thatReturnsNull=a(null);b.thatReturnsThis=function(){return this};b.thatReturnsArgument=function(a){return a};c=b;f["default"]=c}),66);
__d("FBLogger",["fb-error"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("fb-error").FBLogger}),98);
__d("unrecoverableViolation",["FBLogger"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,d,e){d=d===void 0?{}:d;d=d.error;b=c("FBLogger")(b);d?b=b.catching(d):b=b.blameToPreviousFrame();for(d=0;d<((f=e==null?void 0:e.blameToPreviousFrame)!=null?f:0);++d){var f;b=b.blameToPreviousFrame()}f=e==null?void 0:e.categoryKey;f!=null&&(b=b.addToCategoryKey(f));return b.mustfixThrow(a)}g["default"]=a}),98);
__d("BaseEventEmitter",["EmitterSubscription","ErrorGuard","EventSubscriptionVendor","emptyFunction","unrecoverableViolation"],(function(a,b,c,d,e,f){var g;a=function(){"use strict";function a(){this.$2=new(b("EventSubscriptionVendor"))(),this.$1=null}var c=a.prototype;c.addListener=function(a,c,d){return this.$2.addSubscription(a,new(b("EmitterSubscription"))(this.$2,c,d))};c.removeListener=function(a){this.$2.removeSubscription(a)};c.once=function(a,b,c){var d=this;return this.addListener(a,function(){d.removeCurrentListener(),b.apply(c,arguments)})};c.removeAllListeners=function(a){this.$2.removeAllSubscriptions(a)};c.removeCurrentListener=function(){if(!this.$1)throw b("unrecoverableViolation")("Not in an emitting cycle; there is no current subscription","emitter");this.$2.removeSubscription(this.$1)};c.listeners=function(a){a=this.$2.getSubscriptionsForType(a);return a?a.filter(b("emptyFunction").thatReturnsTrue).map(function(a){return a.listener}):[]};c.emit=function(a){var b=this.$2.getSubscriptionsForType(a);if(b){var c=Object.keys(b),d;for(var e=0;e<c.length;e++){var f=c[e],g=b[f];if(g){this.$1=g;if(d==null){d=[g,a];for(var h=0,i=arguments.length<=1?0:arguments.length-1;h<i;h++)d[h+2]=h+1<1||arguments.length<=h+1?void 0:arguments[h+1]}else d[0]=g;this.__emitToSubscription.apply(this,d)}}this.$1=null}};c.__emitToSubscription=function(a,c){for(var d=arguments.length,e=new Array(d>2?d-2:0),f=2;f<d;f++)e[f-2]=arguments[f];(g||(g=b("ErrorGuard"))).applyWithGuard(a.listener,a.context,e,{name:"EventEmitter "+c+" event"})};return a}();e.exports=a}),null);
__d("EventEmitter",["BaseEventEmitter"],(function(a,b,c,d,e,f,g){a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}return b}(c("BaseEventEmitter"));g["default"]=a}),98);
__d("EventEmitterWithHolding",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.$2=a,this.$3=b,this.$1=null,this.$5=[],this.$4=0}var b=a.prototype;b.addListener=function(a,b,c){return this.$2.addListener(a,b,c)};b.once=function(a,b,c){return this.$2.once(a,b,c)};b.addRetroactiveListener=function(a,b,c){var d=this.$2.addListener(a,b,c),e=this.$5;e.push(!1);this.$4++;this.$3.emitToListener(a,b,c);this.$4--;e[e.length-1]&&d.remove();e.pop();return d};b.removeAllListeners=function(a){this.$2.removeAllListeners(a)};b.removeCurrentListener=function(){if(this.$4){var a=this.$5;a[a.length-1]=!0}else this.$2.removeCurrentListener()};b.listeners=function(a){return this.$2.listeners(a)};b.emit=function(a){var b;for(var c=arguments.length,d=new Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];(b=this.$2).emit.apply(b,[a].concat(d))};b.emitAndHold=function(a){var b,c;for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];this.$1=(b=this.$3).holdEvent.apply(b,[a].concat(e));(c=this.$2).emit.apply(c,[a].concat(e));this.$1=null};b.releaseCurrentEvent=function(){this.$1!=null?this.$3.releaseEvent(this.$1):this.$4>0&&this.$3.releaseCurrentEvent()};b.releaseHeldEventType=function(a){this.$3.releaseEventType(a)};return a}();f["default"]=a}),66);
__d("EventHolder",["invariant"],(function(a,b,c,d,e,f,g,h){"use strict";a=function(){function a(){this.$1={},this.$2=[]}var b=a.prototype;b.holdEvent=function(a){this.$1[a]=this.$1[a]||[];var b=this.$1[a],c={eventType:a,index:b.length};for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];b.push(e);return c};b.emitToListener=function(a,b,c){var d=this,e=this.$1[a];if(!e)return;e.forEach(function(e,f){if(!e)return;d.$2.push({eventType:a,index:f});b.apply(c,e);d.$2.pop()})};b.releaseCurrentEvent=function(){this.$2.length||h(0,1764),this.releaseEvent(this.$2[this.$2.length-1])};b.releaseEvent=function(a){delete this.$1[a.eventType][a.index]};b.releaseEventType=function(a){this.$1[a]=[]};return a}();g["default"]=a}),98);
__d("Arbiter",["invariant","ArbiterToken","CallbackDependencyManager","ErrorGuard","EventEmitter","EventEmitterWithHolding","EventHolder"],(function(a,b,c,d,e,f,g,h){"use strict";var i;function j(a){return Array.isArray(a)?a:[a]}function k(a){return a instanceof l||a===l?a:l}var l=function(){function a(){var a=new(c("EventEmitter"))();this.$3=new m();this.$2=new(c("EventEmitterWithHolding"))(a,this.$3);this.$1=new(c("CallbackDependencyManager"))();this.$4=[]}var b=a.prototype;b.subscribe=function(a,b,d){var e=this;a=j(a);a.forEach(function(a){a&&typeof a==="string"||h(0,1966,a)});typeof b==="function"||h(0,1967,b);d=d||"all";d==="new"||d==="all"||h(0,1968,d);a=a.map(function(a){var c=function(c){return e.$5(b,a,c)};c.__SMmeta=b.__SMmeta;if(d==="new")return e.$2.addListener(a,c);e.$4.push({});c=e.$2.addRetroactiveListener(a,c);e.$4.pop();return c});return new(c("ArbiterToken"))(this,a)};b.$5=function(a,b,d){var e=this.$4[this.$4.length-1];if(e[b]===!1)return;a=(i||(i=c("ErrorGuard"))).applyWithGuard(a,null,[b,d]);a===!1&&this.$2.releaseCurrentEvent();e[b]=a};b.unsubscribeCurrentSubscription=function(){this.$2.removeCurrentListener()};b.releaseCurrentPersistentEvent=function(){this.$2.releaseCurrentEvent()};b.subscribeOnce=function(a,b,c){var d=this;a=this.subscribe(a,function(a,c){d.unsubscribeCurrentSubscription();return b(a,c)},c);return a};b.unsubscribe=function(a){a.isForArbiterInstance(this)||h(0,1969),a.unsubscribe()};b.inform=function(a,b,c){var d=Array.isArray(a);a=j(a);c=c||"event";var e=c==="state"||c==="persistent";this.$4.push({});for(var f=0;f<a.length;f++){var g=a[f];g||h(0,1970,g);this.$3.setHoldingBehavior(g,c);this.$2.emitAndHold(g,b);this.$6(g,b,e)}g=this.$4.pop();return d?g:g[a[0]]};b.query=function(a){var b=this.$3.getHoldingBehavior(a);!b||b==="state"||h(0,1971,a);b=null;this.$3.emitToListener(a,function(a){b=a});return b};b.registerCallback=function(a,b){if(typeof a==="function")return this.$1.registerCallback(a,b);else return this.$1.addDependenciesToExistingCallback(a,b)};b.$6=function(a,b,c){if(b===null)return;c?this.$1.satisfyPersistentDependency(a):this.$1.satisfyNonPersistentDependency(a)};a.subscribe=function(b,c,d){return a.prototype.subscribe.apply(k(this),arguments)};a.unsubscribeCurrentSubscription=function(){return a.prototype.unsubscribeCurrentSubscription.apply(k(this))};a.releaseCurrentPersistentEvent=function(){return a.prototype.releaseCurrentPersistentEvent.apply(k(this))};a.subscribeOnce=function(b,c,d){return a.prototype.subscribeOnce.apply(k(this),arguments)};a.unsubscribe=function(b){return a.prototype.unsubscribe.apply(k(this),arguments)};a.inform=function(b,c,d){return a.prototype.inform.apply(k(this),arguments)};a.informSingle=function(b,c,d){return a.prototype.inform.apply(k(this),arguments)};a.query=function(b){return a.prototype.query.apply(k(this),arguments)};a.registerCallback=function(b,c){return a.prototype.registerCallback.apply(k(this),arguments)};a.$6=function(b,c,d){return a.prototype.$6.apply(k(this),arguments)};a.$5=function(b,c,d){return a.prototype.$5.apply(k(this),arguments)};return a}(),m=function(b){babelHelpers.inheritsLoose(a,b);function a(){var a;a=b.call(this)||this;a.$ArbiterEventHolder1={};return a}var c=a.prototype;c.setHoldingBehavior=function(a,b){this.$ArbiterEventHolder1[a]=b};c.getHoldingBehavior=function(a){return this.$ArbiterEventHolder1[a]};c.holdEvent=function(a){var c=this.$ArbiterEventHolder1[a];c!=="persistent"&&this.$ArbiterEventHolder2(a);if(c!=="event"){var d;for(var e=arguments.length,f=new Array(e>1?e-1:0),g=1;g<e;g++)f[g-1]=arguments[g];return(d=b.prototype.holdEvent).call.apply(d,[this,a].concat(f))}return void 0};c.$ArbiterEventHolder2=function(a){this.emitToListener(a,this.releaseCurrentEvent,this)};c.releaseEvent=function(a){a&&b.prototype.releaseEvent.call(this,a)};return a}(c("EventHolder"));l.call(l);a=l;g["default"]=a}),98);
__d("objectValues",[],(function(a,b,c,d,e,f){"use strict";function a(a){return Object.values(a)}f["default"]=a}),66);
__d("BootloaderEvents",["Arbiter","objectValues"],(function(a,b,c,d,e,f,g){var h="bootloader/bootload",i="bootloader/callback_timeout",j="bootloader/defer_timeout",k="hasteResponse/handle",l="bootloader/resource_in_longtail_bt_manifest",m="bootloader/bootload_error",n=new(c("Arbiter"))(),o=new Set(),p=new Set();function q(a,b){return"haste_response_ef:"+a+":"+(b!=null?b:"<unknown>")}function a(a){var b=new Map();for(a of c("objectValues")(a))for(var d of a){var e=d[0],f=d[1];b.set(e,f)}return b}function b(){return{blocking:new Map(),nonblocking:new Map(),"default":new Map()}}function d(a){o.add(a)}function e(a){o["delete"](a),n.inform(h,a,"persistent")}function f(a,b){p.add(q(a,b))}function r(a,b,c){n.inform(q(a,b),c,"persistent")}function s(a){n.inform(j,a,"persistent")}function t(a){return n.subscribe(h,function(b,c){return a(c)})}function u(a){return n.subscribe(j,function(b,c){return a(c)})}function v(){return new Set(o)}function w(a){n.inform(k,a,"persistent")}function x(a){return n.subscribe(k,function(b,c){b=q(c.source,c.sourceDetail);if(p.has(b)){n.subscribe(b,function(b,d){return a(babelHelpers["extends"]({},c,{efData:d}))});return}a(c)})}function y(a){return n.subscribe(l,function(b,c){a(c)})}function z(a,b){n.inform(l,{hashes:a,source:b},"persistent")}function A(a){return n.subscribe(i,function(b,c){a(c)})}function B(a){n.inform(i,a,"persistent")}function C(a){return n.subscribe(m,function(b,c){return a(c)})}function D(a){n.inform(m,a,"persistent")}g.flattenResourceMapSet=a;g.newResourceMapSet=b;g.notifyBootloadStart=d;g.notifyBootload=e;g.notifyHasteResponseEFStart=f;g.notifyHasteResponseEF=r;g.notifyDeferTimeout=s;g.onBootload=t;g.onDeferTimeout=u;g.getActiveBootloads=v;g.notifyHasteResponse=w;g.onHasteResponse=x;g.onResourceInLongTailBTManifest=y;g.notifyResourceInLongTailBTManifest=z;g.onBootloaderCallbackTimeout=A;g.notifyBootloaderCallbackTimeout=B;g.onBootloadError=C;g.notifyBootloadError=D}),98);
__d("performanceAbsoluteNow",["performance"],(function(a,b,c,d,e,f,g){var h,i=function(){return Date.now()};function a(a){i=a}var j=0,k=-1;b=typeof (h||(h=c("performance")))==="object";var l=b&&typeof (h||(h=c("performance"))).now==="function";b&&((h||(h=c("performance"))).timeOrigin?k=(h||(h=c("performance"))).timeOrigin:(h||(h=c("performance"))).timing&&(h||(h=c("performance"))).timing.navigationStart&&(k=(h||(h=c("performance"))).timing.navigationStart));var m;d=function(){return 0};if(l&&k!==-1){m=function(){return(h||(h=c("performance"))).now()+k};e=function(){return m()+j};d=function(){var a=Date.now()-m();a>500&&(j=a);return a};if(typeof window==="object"&&typeof window.addEventListener==="function"){f={capture:!1,passive:!0};window.addEventListener("blur",d,f);window.addEventListener("focus",d,f)}}else e=m=function(){return i()};b={setFallback:a,fromRelativeTime:function(){if(k===-1){var a=l?Date.now()-(h||(h=c("performance"))).now():0;return function(b){return b+a}}else return function(a){return a+k}}(),__adjust:d};f=Object.assign(e,b);a=f;g["default"]=a}),98);
__d("BootloaderEventsManager",["CallbackDependencyManager","performanceAbsoluteNow"],(function(a,b,c,d,e,f){var g;a=function(){"use strict";function a(){this.$1=new(b("CallbackDependencyManager"))(),this.$2=new Map()}var c=a.prototype;c.rsrcDone=function(a){return a};c.bootload=function(a){return"bl:"+a.join(",")};c.tierOne=function(a){return"t1:"+a};c.tierTwoStart=function(a){return"t2s:"+a};c.tierTwo=function(a){return"t2:"+a};c.tierThreeStart=function(a){return"t3s:"+a};c.tierThree=function(a){return"t3:"+a};c.tierOneLog=function(a){return"t1l:"+a};c.tierTwoLog=function(a){return"t2l:"+a};c.tierThreeLog=function(a){return"t3l:"+a};c.beDone=function(a){return"beDone:"+a};c.notify=function(a){this.$2.set(a,(g||(g=b("performanceAbsoluteNow")))()),this.$1.satisfyPersistentDependency(a)};c.getEventTime=function(a){return this.$2.get(a)};c.registerCallback=function(a,b){this.$1.registerCallback(a,b)};return a}();e.exports=a}),null);
__d("nullthrows",[],(function(a,b,c,d,e,f){function a(a,b){b===void 0&&(b="Got unexpected null or undefined");if(a!=null)return a;a=new Error(b);a.framesToPop=1;throw a}f["default"]=a}),66);
__d("BootloaderPreloader",["invariant","BootloaderDocumentInserter","ErrorGuard","ExecutionEnvironment","FBLogger","nullthrows"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=new Set(),l=new Set();function a(a){var b=a.allResources,e=a==null?void 0:(a=a.hsrp)==null?void 0:(a=a.hblp)==null?void 0:a.rsrcMap;if(e==null){c("FBLogger")("BootloaderPreloader").warn("Missing resource map in worker hrp");return}if(b==null){c("FBLogger")("BootloaderPreloader").warn("Missing allResources list in worker hrp");return}d("BootloaderDocumentInserter").batchDOMInsert(function(a){b.forEach(function(b){b=e[b];if(!b||b.type!=="js")return;m(b,a,"prefetch")})})}function m(a,b,d,e,f){d===void 0&&(d="preload");if((i||(i=c("ExecutionEnvironment"))).isInWorker)return;b=c("nullthrows")(b);var g=void 0;switch(a.type){case"async":return;case"css":g="style";break;case"js":g="script";break;default:g=a.type,h(0,3721)}if(k.has(a.src)||d==="prefetch"&&l.has(a.src))return;d==="preload"?k.add(a.src):d==="prefetch"?l.add(a.src):h(0,77517);if(a.d===1)return;var m=document.createElement("link");f!=null&&(m.onerror=(j||(j=c("ErrorGuard"))).guard(function(){f()}));m.href=a.src;m.rel=d;d==="preload"&&(m.as=g);e!=null&&m.setAttribute("fetchpriority",e);a.nc||(m.crossOrigin="anonymous");b.appendChild(m)}g.preloadWorkerJSFromHRP=a;g.preloadResource=m}),98);
__d("BootloaderRetryTracker",["ErrorGuard","performanceAbsoluteNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i;b=function(){function b(a){this.$2=[],this.$3=new Map(),this.$1=a,this.$4=this.$1.retries.length>0}var d=b.prototype;d.getAllRetryAttempts_FOR_DEBUG_ONLY=function(){return this.$3};d.getNumRetriesForSource=function(a){return(a=this.$3.get(a))!=null?a:0};d.maybeScheduleRetry=function(b,d,e){var f=this,g=this.getNumRetriesForSource(b);if(!this.$5()||g>=this.$1.retries.length){e();return}this.$2.push((h||(h=c("performanceAbsoluteNow")))());this.$3.set(b,g+1);a.setTimeout(function(){f.$5()?d():e()},this.$1.retries[g])};d.maybeRetryImmediately=function(a,b,d){var e=this.getNumRetriesForSource(a);if(!this.$5()||e>=this.$1.retries.length){d();return}this.$2.push((h||(h=c("performanceAbsoluteNow")))());this.$3.set(a,e+1);b()};d.$5=function(){if(!this.$4)return!1;var a=this.$2.length;if(a<this.$1.abortNum)return!0;a=this.$2[a-1]-this.$2[a-this.$1.abortNum];a<this.$1.abortTime&&((i||(i=c("ErrorGuard"))).applyWithGuard(this.$1.abortCallback,null,[]),this.$4=!1);return this.$4};return b}();g["default"]=b}),98);
__d("requireCond",[],(function(a,b,c,d,e,f){function a(a,b,c){throw new Error("Cannot use raw untransformed requireCond.")}b=a;f["default"]=b}),66);
__d("clearInterval",["cr:7385"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:7385")}),98);
__d("isEmpty",["invariant"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a){if(Array.isArray(a))return a.length===0;else if(typeof a==="object"){if(a){!i(a)||a.size===void 0||h(0,1445);for(var b in a)return!1}return!0}else return!a}function i(a){return typeof Symbol==="undefined"?!1:a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"]!=null}g["default"]=a}),98);
__d("setIntervalAcrossTransitions",["cr:7389"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:7389")}),98);
__d("CSSPoller",["CSSLoaderConfig","FBLogger","clearInterval","isEmpty","setIntervalAcrossTransitions"],(function(a,b,c,d,e,f,g){"use strict";var h,i=20,j=new Map(),k={expirationTime:null,startCSSPoll:function(a,b,d,e){var f=j.size!==0;k.expirationTime=Date.now()+c("CSSLoaderConfig").timeout;j.set(a,{link:e,onLoad:b,onError:d,pollAttempts:0});if(!f)var g=c("setIntervalAcrossTransitions")(function(){k.runCSSPolls()&&c("clearInterval")(g)},i)},runCSSPolls:function(){var a=[],b=k.expirationTime;if(b!=null&&Date.now()>=b){for(b of j.values())c("FBLogger")("CSSPoller").warn("Failed to poll CSS: %s with CORS setting %s after %d attempts",b.link.href,b.link.crossOrigin,b.pollAttempts),a.push(b.onError);j.clear()}else for(b of j){var d=b[0],e=b[1],f=e.link;try{var g=f.sheet;if(g!=null){var i;i=(i=g.cssRules)!=null?i:g.rules;i!=null&&(a.push(e.onLoad),j["delete"](d),c("FBLogger")("CSSPoller").info("Poll technique was able to poll CSS"))}}catch(a){e.pollAttempts++,c("FBLogger")("CSSPoller").catching(a).warn("Poll technique was unable to poll CSS: %s with CORS setting %s at attempt %d",f.href,f.crossOrigin,e.pollAttempts)}}if(!(h||(h=c("isEmpty")))(a)){for(g=0;g<a.length;g++)a[g]();k.expirationTime=Date.now()+c("CSSLoaderConfig").timeout}return j.size===0}};a=k;g["default"]=a}),98);
__d("CircularBuffer",["unrecoverableViolation"],(function(a,b,c,d,e,f,g){a=function(){function a(a){if(a<=0)throw c("unrecoverableViolation")("Buffer size should be a positive integer","comet_infra");this.$1=a;this.$2=0;this.$3=[];this.$4=[]}var b=a.prototype;b.write=function(a){var b=this;this.$3.length<this.$1?this.$3.push(a):(this.$4.forEach(function(a){return a(b.$3[b.$2])}),this.$3[this.$2]=a,this.$2++,this.$2%=this.$1);return this};b.onEvict=function(a){this.$4.push(a);return this};b.read=function(){return this.$3.slice(this.$2).concat(this.$3.slice(0,this.$2))};b.expand=function(a){if(a>this.$1){var b=this.read();this.$2=0;this.$3=b;this.$1=a}return this};b.dropFirst=function(a){if(a<=this.$1){var b=this.read();this.$2=0;b.splice(0,a);this.$3=b}return this};b.clear=function(){this.$2=0;this.$3=[];return this};b.currentSize=function(){return this.$3.length};b.lastElement=function(){return this.$3[this.$2]};return a}();g["default"]=a}),98);
__d("ResourceTypes",[],(function(a,b,c,d,e,f){"use strict";a={JS:"js",CSS:"css",XHR:"xhr"};b=a;f["default"]=b}),66);
__d("TimingAnnotations",[],(function(a,b,c,d,e,f){a=function(){function a(){}var b=a.prototype;b.addStringAnnotation=function(a,b){return this};b.addSetAnnotation=function(a,b){return this};b.addSetElement=function(a,b){return this};b.registerOnBeforeSend=function(a){return this};b.addVectorAnnotation=function(a,b){return this};b.addVectorElement=function(a,b){return this};return a}();b=function(){function a(){this.$1=null,this.$2=null,this.$3=null,this.$4=[]}var b=a.prototype;b.addStringAnnotation=function(a,b){this.$2=this.$2||new Map();this.$2.set(a,b);return this};b.addSetAnnotation=function(a,b){var c=this.$1||new Map(),d=c.get(a)||new Set();b.forEach(function(a){return d.add(a)});c.set(a,d);this.$1=c;return this};b.addSetElement=function(a,b){var c=this.$1||new Map(),d=c.get(a)||new Set();d.add(b);c.set(a,d);this.$1=c;return this};b.addVectorAnnotation=function(a,b){this.$3=this.$3||new Map();this.$3.set(a,b);return this};b.addVectorElement=function(a,b){var c=this.$3=this.$3||new Map(),d=this.$3.get(a)||[];d.push(b);c.set(a,d);return this};b.registerOnBeforeSend=function(a){this.$4.push(a);return this};b.prepareToSend=function(){var a=this;this.$4.forEach(function(b){return b(a)});this.$4=[];var b={};if(this.$1!=null)for(var c of this.$1){var d=c[0],e=c[1];b[d]=Array.from(e.values())}d={};if(this.$2!=null)for(e of this.$2){c=e[0];var f=e[1];d[c]=f}c={};if(this.$3!=null)for(f of this.$3){e=f[0];var g=f[1];c[e]=g}return{setProps:b,stringProps:d,vectorProps:c}};a.combine=function(a,b){var c;a!=null&&b!=null?(a.stringProps=babelHelpers["extends"]({},b.stringProps,{},a.stringProps),a.setProps=babelHelpers["extends"]({},b.setProps,{},a.setProps),c=a):a!=null?c=a:b!=null&&(c=b);return c};return a}();b.EmptyTimingAnnotations=a;b.EmptyTraceTimingAnnotations=a;b.TraceTimingAnnotations=b;f["default"]=b}),66);
__d("BaseDeserializePHPQueryData",[],(function(a,b,c,d,e,f){"use strict";var g=/^([-_\w]+)((?:\[[-_\w]*\])+)=?(.*)/;function h(a){return a==="hasOwnProperty"||a==="__proto__"?"\ud83d\udf56":a}function a(a,b){if(a==null||a==="")return{};var c={};a=a.replace(/%5B/gi,"[").replace(/%5D/gi,"]");a=a.split("&");var d=Object.prototype.hasOwnProperty;for(var e=0,f=a.length;e<f;e++){var i=a[e].match(g);if(!i){var j=a[e].indexOf("=");if(j===-1)c[b(a[e])]=null;else{var k=a[e].substring(0,j);j=a[e].substring(j+1);c[b(k)]=b(j)}}else{k=i[2].split(/\]\[|\[|\]/).slice(0,-1);j=i[1];i=b(i[3]||"");k[0]=j;j=c;for(var l=0;l<k.length-1;l++){var m=h(k[l]);if(m){if(!d.call(j,m)){var n=k[l+1]&&!k[l+1].match(/^\d{1,3}$/)?{}:[];j[m]=n;if(j[m]!==n)return c}j=j[m]}else k[l+1]&&!k[l+1].match(/^\d{1,3}$/)?j.push({}):j.push([]),j=j[j.length-1]}j instanceof Array&&k[k.length-1]===""?j.push(i):j[h(k[k.length-1])]=i}}return c}f.deserialize=a}),66);
__d("flattenPHPQueryData",["invariant"],(function(a,b,c,d,e,f,g,h){function a(a){return i(a,"",{})}function i(a,b,c){if(a==null)c[b]=void 0;else if(typeof a==="object"){typeof a.appendChild!=="function"||h(0,2616);for(var d in a)d!=="$$typeof"&&Object.prototype.hasOwnProperty.call(a,d)&&a[d]!==void 0&&i(a[d],b?b+"["+d+"]":d,c)}else c[b]=a;return c}g["default"]=a}),98);
__d("PHPQuerySerializer",["BaseDeserializePHPQueryData","flattenPHPQueryData"],(function(a,b,c,d,e,f,g){function a(a){var b=[];a=c("flattenPHPQueryData")(a);for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){var e=h(d);a[d]===void 0?b.push(e):b.push(e+"="+h(String(a[d])))}return b.join("&")}function h(a){return encodeURIComponent(a).replace(/%5D/g,"]").replace(/%5B/g,"[")}function b(a){return d("BaseDeserializePHPQueryData").deserialize(a,i)}function i(a){try{return decodeURIComponent(a.replace(/\+/g," "))}catch(b){return a}}e={decodeComponent:i,deserialize:b,encodeComponent:h,serialize:a};f.exports=e}),34);
__d("PHPQuerySerializerNoEncoding",["BaseDeserializePHPQueryData","flattenPHPQueryData"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=[];a=c("flattenPHPQueryData")(a);for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){var e=h(d);a[d]===void 0?b.push(e):b.push(e+"="+h(String(a[d])))}return b.join("&")}function h(a){return a}function b(a){return d("BaseDeserializePHPQueryData").deserialize(a,i)}function i(a){return a}e={decodeComponent:i,deserialize:b,encodeComponent:h,serialize:a};f=e;g["default"]=f}),98);
__d("ifRequired",[],(function(a,b,c,d,e,f){function a(a,b,c){var e;d&&d.call(null,[a],function(a){e=a});if(e&&b)return b(e);else if(!e&&c)return c()}f["default"]=a}),66);
__d("ifRequireable",["ifRequired"],(function(a,b,c,d,e,f,g){function a(a,b,d){return c("ifRequired").call(null,a,b,d)}g["default"]=a}),98);
__d("ReloadPage",["Env","ifRequireable"],(function(a,b,c,d,e,f,g){var h;function i(b){var d=c("ifRequireable")("BlueCompatRouter",function(a){return a});if((h||(h=c("Env"))).isCQuick&&d){d.sendMessage({compatAction:"reload"});return}a.window.location.reload(b)}function b(b){a.setTimeout(i,b)}g.now=i;g.delay=b}),98);
__d("PHPStrictQuerySerializer",["PHPQuerySerializer","flattenPHPQueryData"],(function(a,b,c,d,e,f,g){var h;function a(a){var b=[];a=c("flattenPHPQueryData")(a);for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)){var e=i(d);a[d]===void 0?b.push(e):b.push(e+"="+i(String(a[d])))}return b.join("&")}function i(a){return encodeURIComponent(a)}g.serialize=a;g.encodeComponent=i;g.deserialize=(h||(h=d("PHPQuerySerializer"))).deserialize;g.decodeComponent=h.decodeComponent}),98);
__d("URIRFC3986",[],(function(a,b,c,d,e,f){"use strict";var g=new RegExp("^([^:/?#]+:)?(//([^\\\\/?#@]*@)?(\\[[A-Fa-f0-9:.]+\\]|[^\\/?#:]*)(:[0-9]*)?)?([^?#]*)(\\?[^#]*)?(#.*)?");function a(a){if(a.trim()==="")return null;a=a.match(g);if(a==null)return null;var b=a[2]?a[2].substr(2):null,c=a[1]?a[1].substr(0,a[1].length-1):null;a={uri:a[0]?a[0]:null,scheme:c,authority:b,userinfo:a[3]?a[3].substr(0,a[3].length-1):null,host:a[2]?a[4]:null,port:a[5]?a[5].substr(1)?parseInt(a[5].substr(1),10):null:null,path:a[6]?a[6]:null,query:a[7]?a[7].substr(1):null,fragment:a[8]?a[8].substr(1):null,isGenericURI:b===null&&!!c};return a}f.parse=a}),66);
__d("$InternalEnum",[],(function(a,b,c,d,e,f){"use strict";var g=Object.prototype.hasOwnProperty,h=typeof WeakMap==="function"?new WeakMap():new Map();function i(a){var b=h.get(a);if(b!==void 0)return b;var c=new Map();Object.getOwnPropertyNames(a).forEach(function(b){c.set(a[b],b)});try{h.set(a,c)}catch(a){}return c}var j=Object.freeze(Object.defineProperties(Object.create(null),{isValid:{value:function(a){return i(this).has(a)}},cast:{value:function(a){return this.isValid(a)?a:void 0}},members:{value:function(){return i(this).keys()}},getName:{value:function(a){return i(this).get(a)}}}));function a(a){var b=Object.create(j);for(var c in a)g.call(a,c)&&Object.defineProperty(b,c,{value:a[c]});return Object.freeze(b)}var k=Object.freeze(Object.defineProperties(Object.create(null),{isValid:{value:function(a){return typeof a==="string"?g.call(this,a):!1}},cast:{value:j.cast},members:{value:function(){return Object.getOwnPropertyNames(this).values()}},getName:{value:function(a){return a}}}));a.Mirrored=function(a){var b=Object.create(k);for(var c=0,d=a.length;c<d;++c)Object.defineProperty(b,a[c],{value:a[c]});return Object.freeze(b)};Object.freeze(a.Mirrored);e.exports=Object.freeze(a)}),null);
__d("URISchemes",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";var g=new Set(["about","accountscenter","aidemos","aistudio","apk","blob","barcelona","cmms","fb","fba","fbatwork","fb-ama","fb-internal","fb-workchat","fb-workchat-secure","fb-messenger","fb-messenger-public","fb-messenger-group-thread","fb-page-messages","fb-pma","fbcf","fbconnect","fbinternal","fbmobilehome","fbrpc","file","flipper","ftp","gtalk","http","https","mailto","wss","ms-app","intent","itms","itms-apps","itms-services","lasso","market","svn+ssh","fbstaging","tel","sms","pebblejs","sftp","whatsapp","moments","flash","fblite","chrome-extension","webcal","instagram","iglite","fb124024574287414","fb124024574287414rc","fb124024574287414master","fb1576585912599779","fb929757330408142","designpack","fbpixelcloud","fbapi20130214","fb1196383223757595","tbauth","oculus","oculus.store","oculus.feed","oculusstore","socialplatform","odh","com.oculus.rd","aria","skype","ms-windows-store","callto","messenger","workchat","fb236786383180508","fb1775440806014337","data","fb-mk","munki","origami-file","fb-nimble-vrsrecorder","fb-nimble-monohandtrackingvis","together","togetherbl","horizonlauncher","horizon","venues","whatsapp-consumer","whatsapp-smb","fb-ide-opener","fb-vscode","fb-vscode-insiders","editor","spark-studio","spark-player","spark-simulator","cosmo-player","arstudio","manifold","origami-internal","origami-public","stella","mwa","mattermost","logaggregator","pcoip","cinema","home","oculus360photos","systemux","moonstone","hsr-asset-viewer","upi","q4bconfigurator","q4bnux","fb-viewapp","meta-ai","x-safari-https","meta-bloks","facebook-horizon"]),h=b("$InternalEnum")({EXPLICITLY_ALLOWED_SCHEMES_ONLY:"explicitly_allowed_schemes_only",INCLUDE_DEFAULTS:"include_defaults"});function a(a,b,c){b===void 0&&(b=h.INCLUDE_DEFAULTS);return a==null||a===""?!0:(c==null?void 0:c.has(a.toLowerCase()))||b===h.INCLUDE_DEFAULTS&&g.has(a.toLowerCase())}f.Options=h;f.isAllowed=a}),66);
__d("isSameOrigin",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){return!a.getProtocol()||!a.getDomain()||!b.getProtocol()||!b.getDomain()?!1:a.getOrigin()===b.getOrigin()}f["default"]=a}),66);
__d("setHostSubdomain",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){a=a.split(".");a.length<3?a.unshift(b):a[0]=b;return a.join(".")}f["default"]=a}),66);
__d("URIAbstractBase",["invariant","FBLogger","PHPStrictQuerySerializer","URIRFC3986","URISchemes","isSameOrigin","setHostSubdomain"],(function(a,b,c,d,e,f,g){var h,i,j=new RegExp("[\\x00-\\x2c\\x2f\\x3b-\\x40\\x5c\\x5e\\x60\\x7b-\\x7f\\uFDD0-\\uFDEF\\uFFF0-\\uFFFF\\u2047\\u2048\\uFE56\\uFE5F\\uFF03\\uFF0F\\uFF1F]"),k=new RegExp("^(?:[^/]*:|[\\x00-\\x1f]*/[\\x00-\\x1f]*/)"),l=[];a=function(){"use strict";a.parse=function(c,d,e,f){if(!d)return!0;if(d instanceof a){c.setProtocol(d.getProtocol());c.setDomain(d.getDomain());c.setPort(d.getPort());c.setPath(d.getPath());c.setQueryData(f.deserialize(f.serialize(d.getQueryData())));c.setFragment(d.getFragment());c.setIsGeneric(d.getIsGeneric());c.setForceFragmentSeparator(d.getForceFragmentSeparator());c.setOriginalRawQuery(d.getOriginalRawQuery());c.setQueryParamModified(!1);return!0}d=d.toString().trim();var g=(h||(h=b("URIRFC3986"))).parse(d)||{fragment:null,scheme:null,query:null};if(!e&&!(i||(i=b("URISchemes"))).isAllowed(g.scheme,c.$12,c.$13))return!1;c.setProtocol(g.scheme||"");if(!e&&j.test(g.host||""))return!1;c.setDomain(g.host||"");c.setPort(g.port||"");c.setPath(g.path||"");if(e)c.setQueryData(f.deserialize(g.query||"")||{});else try{c.setQueryData(f.deserialize(g.query||"")||{})}catch(a){return!1}c.setFragment(g.fragment||"");g.fragment===""&&c.setForceFragmentSeparator(!0);c.setIsGeneric(g.isGenericURI||!1);c.setOriginalRawQuery(g.query);c.setQueryParamModified(!1);if(g.userinfo!==null){if(e)throw new Error("URI.parse: invalid URI (userinfo is not allowed in a URI): "+d);return!1}if(!c.getDomain()&&c.getPath().indexOf("\\")!==-1){if(e)throw new Error("URI.parse: invalid URI (no domain but multiple back-slashes): "+d);return!1}if(!c.getProtocol()&&k.test(d)){if(e)throw new Error("URI.parse: invalid URI (unsafe protocol-relative URLs): "+d+"'");return!1}if(c.getDomain()&&c.getPath()&&!c.getPath().startsWith("/")){if(e)throw new Error("URI.parse: invalid URI (domain and path where path lacks leading slash): "+d);return!1}c.getProtocol()&&!c.getIsGeneric()&&!c.getDomain()&&c.getPath()!==""&&b("FBLogger")("uri").warn('URI.parse: invalid URI (protocol "'+c.getProtocol()+'" with no domain)');return!0};a.tryParse=function(b,c,d,e){d=new a(null,c,d,e);return a.parse(d,b,!1,c)?d:null};a.isValid=function(b,c,d,e){return!!a.tryParse(b,c,d,e)};function a(c,d,e,f){e===void 0&&(e=(i||(i=b("URISchemes"))).Options.INCLUDE_DEFAULTS),d||g(0,2966),this.$9=d,this.$7="",this.$1="",this.$6="",this.$5="",this.$3="",this.$4=!1,this.$8={},this.$2=!1,this.$12=e,this.$13=f,a.parse(this,c,!0,d),this.$11=!1}var c=a.prototype;c.setProtocol=function(a){(i||(i=b("URISchemes"))).isAllowed(a,this.$12,this.$13)||g(0,11793,a);this.$7=a;return this};c.getProtocol=function(){return(this.$7||"").toLowerCase()};c.setSecure=function(a){return this.setProtocol(a?"https":"http")};c.isSecure=function(){return this.getProtocol()==="https"};c.setDomain=function(a){if(j.test(a))throw new Error("URI.setDomain: unsafe domain specified: "+a+" for url "+this.toString());this.$1=a;return this};c.getDomain=function(){return this.$1};c.setPort=function(a){this.$6=a;return this};c.getPort=function(){return this.$6};c.setPath=function(a){this.$5=a;return this};c.getPath=function(){return this.$5};c.addQueryData=function(a,b){Object.prototype.toString.call(a)==="[object Object]"?Object.assign(this.$8,a):this.$8[a]=b;this.$11=!0;return this};c.setQueryData=function(a){this.$8=a;this.$11=!0;return this};c.getQueryData=function(){return this.$8};c.setQueryString=function(a){return this.setQueryData(this.$9.deserialize(a))};c.getQueryString=function(a,b,c){a===void 0&&(a=!1);b===void 0&&(b=function(){return!1});c===void 0&&(c=null);return this.$14(!1,a,b,c)};c.$14=function(a,b,c,d){a===void 0&&(a=!1);b===void 0&&(b=!1);c===void 0&&(c=function(){return!1});d===void 0&&(d=null);if(!this.$11&&(b||c(this.getDomain()))){return(b=this.$10)!=null?b:""}return(a&&d?d:this.$9).serialize(this.getQueryData())};c.removeQueryData=function(a){Array.isArray(a)||(a=[a]);for(var b=0,c=a.length;b<c;++b)delete this.$8[a[b]];this.$11=!0;return this};c.setFragment=function(a){this.$3=a;this.setForceFragmentSeparator(!1);return this};c.getFragment=function(){return this.$3};c.setForceFragmentSeparator=function(a){this.$2=a;return this};c.getForceFragmentSeparator=function(){return this.$2};c.setIsGeneric=function(a){this.$4=a;return this};c.getIsGeneric=function(){return this.$4};c.getOriginalRawQuery=function(){return this.$10};c.setOriginalRawQuery=function(a){this.$10=a;return this};c.setQueryParamModified=function(a){this.$11=a;return this};c.isEmpty=function(){return!(this.getPath()||this.getProtocol()||this.getDomain()||this.getPort()||Object.keys(this.getQueryData()).length>0||this.getFragment())};c.toString=function(a,b){a===void 0&&(a=function(){return!1});b===void 0&&(b=null);return this.$15(!1,!1,a,b)};c.toStringRawQuery=function(a,b){a===void 0&&(a=function(){return!1});b===void 0&&(b=null);return this.$15(!0,!1,a,b)};c.toStringPreserveQuery=function(a,b){a===void 0&&(a=function(){return!1});b===void 0&&(b=null);return this.$15(!1,!0,a,b)};c.toStringStrictQueryEncoding=function(a){a===void 0&&(a=function(){return!1});return this.$15(!0,!1,a,b("PHPStrictQuerySerializer"))};c.$15=function(a,b,c,d){a===void 0&&(a=!1);b===void 0&&(b=!1);c===void 0&&(c=function(){return!1});d===void 0&&(d=null);var e=this;for(var f=0;f<l.length;f++)e=l[f](e);return e.$16(a,b,c,d)};c.$16=function(a,b,c,d){a===void 0&&(a=!1);b===void 0&&(b=!1);c===void 0&&(c=function(){return!1});d===void 0&&(d=null);var e="",f=this.getProtocol();f&&(e+=f+":"+(this.getIsGeneric()?"":"//"));f=this.getDomain();f&&(e+=f);f=this.getPort();f&&(e+=":"+f);f=this.getPath();f?e+=f:e&&(e+="/");f=this.$14(a,b,c,d);f&&(e+="?"+f);a=this.getFragment();a?e+="#"+a:this.getForceFragmentSeparator()&&(e+="#");return e};a.registerFilter=function(a){l.push(a)};c.getOrigin=function(){var a=this.getPort();return this.getProtocol()+"://"+this.getDomain()+(a?":"+a:"")};c.isSameOrigin=function(a){return b("isSameOrigin")(this,a)};c.getQualifiedURIBase=function(){return new a(this,this.$9).qualify()};c.qualify=function(){if(!this.getDomain()){var b=new a(window.location.href,this.$9);this.setProtocol(b.getProtocol()).setDomain(b.getDomain()).setPort(b.getPort())}return this};c.setSubdomain=function(a){var c=this.qualify();c=c.getDomain();return this.setDomain(b("setHostSubdomain")(c,a))};c.getSubdomain=function(){if(!this.getDomain())return"";var a=this.getDomain().split(".");if(a.length<=2)return"";else return a[0]};c.isSubdomainOfDomain=function(b){var c=this.getDomain();return a.isDomainSubdomainOfDomain(c,b,this.$9)};a.isDomainSubdomainOfDomain=function(b,c,d){if(c===""||b==="")return!1;if(b.endsWith(c)){var e=b.length,f=c.length,g=e-f-1;if(e===f||b[g]==="."){e=new a(null,d);e.setDomain(c);return a.isValid(e,d)}}return!1};return a}();e.exports=a}),null);
__d("err",["fb-error"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("fb-error").err}),98);
__d("URIBase",["ExecutionEnvironment","PHPQuerySerializerNoEncoding","URIAbstractBase","URISchemes","UriNeedRawQuerySVChecker","err"],(function(a,b,c,d,e,f,g){var h,i;function j(a,b,d,e){try{return c("URIAbstractBase").parse(a,b,d,e)}catch(a){throw new Error(c("err")(a.message))}}a=function(a){babelHelpers.inheritsLoose(b,a);b.tryParse=function(a,c,e,f){e===void 0&&(e=(i||(i=d("URISchemes"))).Options.INCLUDE_DEFAULTS);e=new b(null,c,e,f);return j(e,a,!1,c)?e:null};b.isValid=function(a,c,e,f){e===void 0&&(e=(i||(i=d("URISchemes"))).Options.INCLUDE_DEFAULTS);return!!b.tryParse(a,c,e,f)};function b(b,c,e,f){e===void 0&&(e=(i||(i=d("URISchemes"))).Options.INCLUDE_DEFAULTS);e=a.call(this,b,c,e,f)||this;e.$URIBase$p_1=c;j(babelHelpers.assertThisInitialized(e),b,!0,c);return e}var e=b.prototype;e.setDomain=function(b){try{a.prototype.setDomain.call(this,b)}catch(a){throw new Error(c("err")(a.message))}return this};e.getQualifiedURIBase=function(){return new b(this,this.$URIBase$p_1).qualify()};e.qualify=function(){if(!this.getDomain()){var a=(typeof self!=="undefined"?self:typeof window!=="undefined"?window:{location:{href:""}}).location.href;(h||(h=c("ExecutionEnvironment"))).isInWorker&&a&&a.startsWith("blob:")&&(a=a.substring(5,a.length));a=new b(a,this.$URIBase$p_1);this.setProtocol(a.getProtocol()).setDomain(a.getDomain()).setPort(a.getPort())}return this};e.isSubdomainOfDomain=function(a){var c=this.getDomain();return b.isDomainSubdomainOfDomain(c,a,this.$URIBase$p_1)};b.isDomainSubdomainOfDomain=function(a,c,d){if(c===""||a==="")return!1;if(a.endsWith(c)){var e=a.length,f=c.length,g=e-f-1;if(e===f||a[g]==="."){e=new b(null,d);e.setDomain(c);return b.isValid(e,d)}}return!1};e.toString=function(){return a.prototype.toString.call(this,c("UriNeedRawQuerySVChecker").isDomainNeedRawQuery,c("PHPQuerySerializerNoEncoding"))};e.toStringRawQuery=function(){return a.prototype.toStringRawQuery.call(this,c("UriNeedRawQuerySVChecker").isDomainNeedRawQuery,c("PHPQuerySerializerNoEncoding"))};e.toStringPreserveQuery=function(){return a.prototype.toStringPreserveQuery.call(this,c("UriNeedRawQuerySVChecker").isDomainNeedRawQuery,c("PHPQuerySerializerNoEncoding"))};e.toStringStrictQueryEncoding=function(){return a.prototype.toStringStrictQueryEncoding.call(this,c("UriNeedRawQuerySVChecker").isDomainNeedRawQuery)};e.getQueryString=function(b){b===void 0&&(b=!1);return a.prototype.getQueryString.call(this,b,c("UriNeedRawQuerySVChecker").isDomainNeedRawQuery,c("PHPQuerySerializerNoEncoding"))};return b}(c("URIAbstractBase"));g["default"]=a}),98);
__d("UriNeedRawQuerySVChecker",["PHPQuerySerializer","URIBase","UriNeedRawQuerySVConfig"],(function(a,b,c,d,e,f){"use strict";var g,h,i=["http","https"];function a(a){if(a==null)return!1;a=a instanceof(g||(g=b("URIBase")))?a:(g||(g=b("URIBase"))).tryParse(a,h||(h=b("PHPQuerySerializer")));if(a==null)return!1;var c=a.getProtocol();return!i.includes(c)?!1:j(a.getDomain())}function j(a){return a!=null&&b("UriNeedRawQuerySVConfig").uris.some(function(c){return(g||(g=b("URIBase"))).isDomainSubdomainOfDomain(a,c,h||(h=b("PHPQuerySerializer")))})}e.exports={isUriNeedRawQuery:a,isDomainNeedRawQuery:j}}),null);
__d("isFacebookURI",[],(function(a,b,c,d,e,f){var g=null,h=["http","https"];function a(a){g||(g=new RegExp("(^|\\.)facebook\\.com$","i"));if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!0:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}a.setRegex=function(a){g=a};f["default"]=a}),66);
__d("memoize",["invariant"],(function(a,b,c,d,e,f,g,h){function a(a){var b=a,c;return function(){arguments.length&&h(0,4494);b&&(c=b(),b=null);return c}}g["default"]=a}),98);
__d("memoizeStringOnly",[],(function(a,b,c,d,e,f){"use strict";function a(a){var b={};return function(c){Object.prototype.hasOwnProperty.call(b,c)||(b[c]=a.call(this,c));return b[c]}}f["default"]=a}),66);
__d("unqualifyURI",[],(function(a,b,c,d,e,f){function a(a){return a.setProtocol("").setDomain("").setPort("")}f["default"]=a}),66);
__d("URI",["Env","FBLogger","PHPQuerySerializer","PHPQuerySerializerNoEncoding","ReloadPage","URIBase","UriNeedRawQuerySVChecker","cr:1078","cr:1080","ifRequired","isFacebookURI","memoize","memoizeStringOnly","unqualifyURI"],(function(a,b,c,d,e,f,g){var h,i,j,k=c("memoize")(function(){return new m(window.location.href)});function l(){return c("ifRequired")("PageTransitions",function(a){if(a.isInitialized())return a})}var m=function(f){babelHelpers.inheritsLoose(e,f);function e(a,b,e){var g=d("UriNeedRawQuerySVChecker").isUriNeedRawQuery(a)?c("PHPQuerySerializerNoEncoding"):h||(h=c("PHPQuerySerializer"));return f.call(this,a,g,b,e)||this}var g=e.prototype;g.setPath=function(a){this.path=a;return f.prototype.setPath.call(this,a)};g.getPath=function(){var a=f.prototype.getPath.call(this);return a?a.replace(/^\/+/,"/"):a};g.setProtocol=function(a){this.protocol=a;return f.prototype.setProtocol.call(this,a)};g.setDomain=function(a){this.domain=a;return f.prototype.setDomain.call(this,a)};g.setPort=function(a){this.port=a;return f.prototype.setPort.call(this,a)};g.setFragment=function(a){this.fragment=a;return f.prototype.setFragment.call(this,a)};g.stripTrailingSlash=function(){this.setPath(this.getPath().replace(/\/$/,""));return this};g.addTrailingSlash=function(){var a=this.getPath();a.length>0&&a[a.length-1]!=="/"&&this.setPath(a+"/");return this};g.valueOf=function(){return this.toString()};g.getRegisteredDomain=function(){if(!this.getDomain())return"";if(!c("isFacebookURI")(this))return null;var a=this.getDomain().split("."),b=a.indexOf("facebook");b===-1&&(b=a.indexOf("workplace"));return a.slice(b).join(".")};g.getUnqualifiedURI=function(){return c("unqualifyURI")(new e(this))};g.getQualifiedURI=function(){return new e(this).qualify()};g.isSameOrigin=function(a){a=a;a==null?a=k():a instanceof e||(a=new e(a.toString()));return f.prototype.isSameOrigin.call(this,a)};e.goURIOnNewWindow=function(a){e.goURIOnWindow(a,window.open("","_blank"),!0)};e.goURIOnWindow=function(a,b,c,d){c===void 0&&(c=!1),d===void 0&&(d=!1),e.goURIOnWindowWithReference(a,b,c,d)};e.goURIOnWindowWithReference=function(b,f,g,h){g===void 0&&(g=!1);h===void 0&&(h=!1);b=new e(b);g=g;var j=!f||f===window;if((i||(i=c("Env"))).isCQuick&&c("isFacebookURI")(b)&&j){j={cquick:(i||(i=c("Env"))).iframeKey,ctarget:i.iframeTarget,cquick_token:i.iframeToken};b.addQueryData(j);g=!1}j=b.toString();b=f?f:window;f=window.location.href===j&&b===window;!g&&a.PageTransitions?a.PageTransitions.go(j,h):f?d("ReloadPage").now():h?b.location.replace(j):b.location.href=j;return b};g.go=function(a,c){if(b("cr:1078")){b("cr:1078")(this,a,c);return}b("cr:1080")&&b("cr:1080")("uri.go");e.go(this,a,c)};e.tryParseURI=function(a){a=(j||(j=c("URIBase"))).tryParse(a,h||(h=c("PHPQuerySerializer")));return a?new e(a):null};e.isValidURI=function(a){return(j||(j=c("URIBase"))).isValid(a,h||(h=c("PHPQuerySerializer")))};e.getRequestURI=function(a,b){a===void 0&&(a=!0);b===void 0&&(b=!1);if(a){a=l();if(a)return a.getCurrentURI(!!b).getQualifiedURI()}return new e(window.location.href)};e.getMostRecentURI=function(){var a=l();return a?a.getMostRecentURI().getQualifiedURI():new e(window.location.href)};e.getNextURI=function(){var a=l();return a?a.getNextURI().getQualifiedURI():new e(window.location.href)};e.encodeComponent=function(a){return encodeURIComponent(a).replace(/%5D/g,"]").replace(/%5B/g,"[")};e.decodeComponent=function(a){return decodeURIComponent(a.replace(/\+/g," "))};e.normalize=function(a){return a!=null&&typeof a==="string"?this.normalizeString(a):new e(a).toString()};return e}(j||(j=c("URIBase")));m.go=function(a,c,d){b("cr:1080")&&b("cr:1080")("URI.go"),m.goURIOnWindow(a,window,c,d)};m.normalizeString=c("memoizeStringOnly")(function(a){return new m(a).toString()});m.expression=/(((\w+):\/\/)([^\/:]*)(:(\d+))?)?([^#?]*)(\?([^#]*))?(#(.*))?/;m.arrayQueryExpression=/^(\w+)((?:\[\w*\])+)=?(.*)/;g["default"]=m}),98);
__d("ResourceTimingsStore",["CircularBuffer","ResourceTypes","TimingAnnotations","URI","performanceAbsoluteNow"],(function(a,b,c,d,e,f){"use strict";var g,h,i=1e3,j=new(b("TimingAnnotations").EmptyTimingAnnotations)(),k={},l={};Object.keys(b("ResourceTypes")).forEach(function(a){a=b("ResourceTypes")[a];var c=new(b("CircularBuffer"))(i),d=new Map();c.onEvict(function(a){d["delete"](a)});k[a]={idx:1,entries:c};l[a]=d});function m(a,c,d){var e;switch(a){case"css":case"js":var f=n.parseMakeHasteURL(c);f=f==null?"unknown_resource":f[0];e=d+"_"+f;break;case"xhr":f=new(g||(g=b("URI")))(c).getQualifiedURI();c=f.getDomain()+f.getPath();e=d+"_"+c;break;default:a,e="never here"}return e}var n={getUID:function(a,b){var c=k[a],d=m(a,b,c.idx);c.entries.write(d);l[a].set(d,{uri:b,uid:d});c.idx++;return d},updateURI:function(a,b,c){a=l[a].get(b);a!=null&&(a.uri=c)},getMapFor:function(a){return l[a]},parseMakeHasteURL:function(a){a=a.match(/\/rsrc\.php\/.*\/([^\?]+)/);if(!a)return null;a=a[1];var b="",c=a.match(/\.(\w+)$/);c&&(b=c[1]);return[a,b]},measureRequestSent:function(a,c){a=l[a];a=a.get(c);if(a==null||a.requestSent!=null)return;else a.requestSent=(h||(h=b("performanceAbsoluteNow")))()},measureResponseReceived:function(a,c){a=l[a];a=a.get(c);if(a==null||a.requestSent==null||a.responseReceived!=null)return;else a.responseReceived=(h||(h=b("performanceAbsoluteNow")))()},annotate:function(a,c){a=l[a];a=a.get(c);if(!a)return j;else{c=a.annotations;if(c!=null)return c;else{c=new(b("TimingAnnotations"))();a.annotations=c;return c}}},getAnnotationsFor:function(a,b){a=l[a];a=a.get(b);if(!a)return null;else{b=a.annotations;return b!=null?b.prepareToSend():null}}};e.exports=n}),null);
__d("TimeSlice",["cr:1126"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:1126")}),98);
__d("CSSLoader",["CSSLoaderConfig","CSSPoller","ResourceTimingsStore","TimeSlice","ifRequired"],(function(a,b,c,d,e,f,g){var h=c("CSSLoaderConfig").loadEventSupported,i;function j(a){if(i)return;i=!0;var b=document.createElement("link");b.onload=function(){h=!0,b.parentNode&&b.parentNode.removeChild(b)};b.rel="stylesheet";b.href="data:text/css;base64,";a.appendChild(b)}function k(a,b,d,e,f){c("CSSPoller").startCSSPoll(a,d,e,f)}function l(a,b,d,e,f,g,i){i===void 0&&(i=!1);var l=c("ResourceTimingsStore").getUID("css",b);c("ResourceTimingsStore").annotate("css",l).addStringAnnotation("name",a).addStringAnnotation("source",b).addStringAnnotation("caller","CSSLoader.loadStyleSheet");c("ifRequired")("TimeSliceInteraction",function(c){c.informGlobally("CSSLoader.loadStyleSheet").addStringAnnotation("source",b).addStringAnnotation("name",a)});c("ResourceTimingsStore").measureRequestSent("css",l);var m=function(){c("ResourceTimingsStore").measureResponseReceived("css",l),e()},n=c("TimeSlice").getGuardedContinuation("CSSLoader link.onresponse");i?k(a,d,m,f,g):h!==!0?(k(a,d,m,f,g),h===void 0&&j(d)):(g.onload=n.bind(void 0,function(){g.onload=g.onerror=null,m()}),g.onerror=n.bind(void 0,function(){g.onload=g.onerror=null,f()}))}a={loadStyleSheet:function(a,b,c,d,e,f,g){var h=document.createElement("link");h.rel="stylesheet";h.type="text/css";h.href=b;d&&(h.crossOrigin="anonymous");e!=null&&(h.dataset.btmanifest=e);l(a,b,c,f,g,h);c.appendChild(h)},setupEventListenersForPotentiallyLoadedCSS:function(a,b,c,d,e,f){l(a,b,c,d,e,f,!0)}};f.exports=a}),34);
__d("ClientConsistencyEventEmitter",["BaseEventEmitter"],(function(a,b,c,d,e,f,g){"use strict";a=new(c("BaseEventEmitter"))();b=a;g["default"]=b}),98);
__d("requireWeak",[],(function(a,b,c,d,e,f){function a(a,b){d&&d.call(null,[a],b)}f["default"]=a}),66);
__d("ClientConsistency",["ClientConsistencyEventEmitter","FBLogger","SiteData","requireWeak"],(function(a,b,c,d,e,f){"use strict";var g=b("SiteData").client_revision,h=!1,i,j={},k=new Set(),l=new Set(),m=function(a){j={};var c=Object.keys(a).sort().reverse();i==="multiple_revs"&&(i=void 0);var d=function(c){var d=Number(c);c=(c=a[d])!=null?c:[];if(c.length===0){n(d);return"break"}c.forEach(function(a){var c;j[a]=Math.max((c=j[a])!=null?c:0,d);if(l.has(a))return;l.add(a);b("requireWeak").call(null,a,function(){if(!j[a])return;n(j[a],a)})})};for(c of c){var e=d(c);if(e==="break")break}},n=function(c,a){c>0&&i==null&&(b("FBLogger")("client_consistency").debug("Consistency action needed: %s:%s",c,String(a)),i="multiple_revs"),c===2&&b("ClientConsistencyEventEmitter").emit("softRefresh","multiple_revs"),c===3&&b("ClientConsistencyEventEmitter").emit("hardRefresh","multiple_revs")},o=function(a){var b=a.actions;a=a.rev;if(a===g)return;i==="multiple_revs"&&(i=void 0);b!=null&&m(b)};a={init:function(){if(h)return;b("ClientConsistencyEventEmitter").addListener("newEntry",function(a){o(a)});b("ClientConsistencyEventEmitter").addListener("softRefresh",function(a){i=a});b("ClientConsistencyEventEmitter").addListener("hardRefresh",function(a){i=a});h=!0},addAdditionalRevision:function(a){a!==g&&(k.add(a),b("ClientConsistencyEventEmitter").emit("newRevision",a))},getAdditionalRevisions:function(){return k},getPendingRefresh:function(){return i}};e.exports=a}),null);
__d("ErrorPubSub",["fb-error"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("fb-error").ErrorPubSub}),98);
__d("BitMap",[],(function(a,b,c,d,e,f){var g="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_";a=function(){function a(){this.$1=[],this.$2=null}var b=a.prototype;b.set=function(a){this.$2!=null&&!this.$1[a]&&(this.$2=null);this.$1[a]=1;return this};b.toString=function(){var a=[];for(var b=0;b<this.$1.length;b++)a.push(this.$1[b]?1:0);return a.length?i(a.join("")):""};b.toCompressedString=function(){if(this.$1.length===0)return"";if(this.$2!=null)return this.$2;var a=[],b=1,c=this.$1[0]||0,d=c.toString(2);for(var e=1;e<this.$1.length;e++){var f=this.$1[e]||0;f===c?b++:(a.push(h(b)),c=f,b=1)}b&&a.push(h(b));return this.$2=i(d+a.join(""))};return a}();function h(a){a=a.toString(2);var b="0".repeat(a.length-1);return b+a}function i(a){a=(a+"00000").match(/[01]{6}/g);var b="";for(var c=0;a!=null&&c<a.length;c++)b+=g[parseInt(a[c],2)];return b}f["default"]=a}),66);
__d("HasteBitMap",["BitMap"],(function(a,b,c,d,e,f,g){var h=new Map();function a(a,b){h.has(a)||h.set(a,new(c("BitMap"))());(a=h.get(a))==null?void 0:a.set(b)}function b(a){return(a=(a=h.get(a))==null?void 0:a.toCompressedString())!=null?a:""}g.add=a;g.toCompressedString=b}),98);
__d("HasteResourceIndexUtil",["invariant"],(function(a,b,c,d,e,f,g,h){b=0;function a(a){a.substr(0,1)===":"||h(0,88579,a);return a.substr(1).split(",").map(function(a){return parseInt(a,10)})}g.UNKNOWN_RESOURCE_INDEX=b;g.parseResourceIndexes=a}),98);
__d("JSResourceEvents",["performanceAbsoluteNow"],(function(a,b,c,d,e,f,g){var h,i=50,j=new Map();function a(a,b,d){a=a;b=b!=null?b:"";var e=j.get(a);e||j.set(a,e=new Map());a=e.get(b);a||e.set(b,a=new Map());e=a.get(d);e||a.set(d,e=[0,[]]);e[1][e[0]++%i]=(h||(h=c("performanceAbsoluteNow")))()}function k(a,b,c){var d=j.get(a);if(!d)return[];var e=[];for(d of d){var g=d[0],h=d[1];for(h of h){var i=h[0],k=h[1];for(k of k[1])k>=b&&k<=c&&e.push({module:a,ref:g||null,type:i,time:k})}}return e.sort(function(a,b){return a.time-b.time})}function b(a,b){var c=new Map();for(var d of j.keys()){var e=k(d,a,b);e.length&&c.set(d,e)}return c}g.notify=a;g.getEvents=k;g.getAllModuleEvents=b}),98);
__d("Promise",["cr:6640"],(function(a,b,c,d,e,f){"use strict";var g=(c=b("cr:6640"))!=null?c:a.Promise;g.allSettled||(g.allSettled=function(a){var b;if((typeof Symbol==="function"?Symbol.iterator:"@@iterator")in a)b=Array.from(a);else return g.reject(new TypeError("Promise.allSettled must be passed an iterable."));var c=Array(b.length);a=function(a,d){var e=b[a];d=typeof e==="object"&&e!==null&&typeof e.then==="function";c[a]=d?new g(function(a,b){e.then(function(b){a({status:"fulfilled",value:b})},function(b){a({status:"rejected",reason:b})})}):g.resolve({status:"fulfilled",value:e})};for(var d=0,e=b.length;d<e;++d)a(d,e);return g.all(c)});g.prototype["finally"]||(g.prototype["finally"]=function(a){return this.then(function(b){return g.resolve(a()).then(function(){return b})},function(b){return g.resolve(a()).then(function(){throw b})})});e.exports=g}),null);
__d("PromiseAnnotate",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){a.displayName=b;return a}function b(a){a=a.displayName;if(typeof a==="string")return a;else return null}f.setDisplayName=a;f.getDisplayName=b}),66);
__d("JSResourceReferenceImpl",["JSResourceEvents","Promise","PromiseAnnotate","ifRequireable","ifRequired"],(function(a,b,c,d,e,f,g){var h,i,j=function(a){return a},k=[],l=null;function m(a){l?a(l):k.push(a)}var n="JSResource: unknown caller";a=function(){a.setBootloader=function(a){l=a;for(a of k)a(l);k=[]};function a(a){this.$1=a}var e=a.prototype;e.getModuleId=function(){var a=this.$1;return a};e.getModuleIdAsRef=function(){return this.$1};e.load=function(){var a=this,c=this.$2;d("JSResourceEvents").notify(this.$1,c,"LOADED");var e=new(i||(i=b("Promise")))(function(b){m(function(e){return e.loadModules([a.getModuleIdAsRef()],function(e){d("JSResourceEvents").notify(a.$1,c,"PROMISE_RESOLVED"),b(e)},(e=a.$2)!=null?e:n)})});(h||(h=d("PromiseAnnotate"))).setDisplayName(e,"Bootload("+this.getModuleId()+")");return e};e.preload=function(){var a,b=this,c=(a=this.$2)!=null?a:n;m(function(a){return a.loadModules([b.getModuleIdAsRef()],function(){},"preload: "+c)})};e.equals=function(a){return this===a||this.$1==a.$1};e.getModuleIfRequireable=function(){d("JSResourceEvents").notify(this.$1,this.$2,"ACCESSED");return c("ifRequireable").call(null,this.$1,j)};e.getModuleIfRequired=function(){d("JSResourceEvents").notify(this.$1,this.$2,"ACCESSED");return c("ifRequired").call(null,this.$1,j)};a.disableForSSR_DO_NOT_USE=function(){this.$3=!1};e.isAvailableInSSR_DO_NOT_USE=function(){return this.constructor.$3};e.__setRef=function(a){this.$2=a;d("JSResourceEvents").notify(this.$1,this.$2,"CREATED");return this};a.loadAll=function(a,b){var c={},e=!1;for(var f of a){var g=f.$2;g&&(e=!0,c[g]=!0);d("JSResourceEvents").notify(f.$1,g,"LOADED")}m(function(d){return d.loadModules(a.map(function(a){return a.getModuleId()}),b,e?Object.keys(c).join(":"):n)})};return a}();a.$3=!0;g["default"]=a}),98);
__d("MakeHasteTranslationsMap",["invariant"],(function(a,b,c,d,e,f,g,h){"use strict";var i={};function a(a){Object.assign(i,a)}function b(a){a in i||h(0,62571,a);return i[a]}g.setBatch=a;g.get=b}),98);
__d("SimpleHook",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.__callbacks=[],this.call=this.$2}var b=a.prototype;b.hasCallback=function(a){var b=this.__callbacks;return b.length>0&&(a==null||b.some(function(b){return b===a||b.$1===a}))};b.add=function(a,b){var c=this,d;if((b==null?void 0:b.once)===!0){b=function(){c.remove(d),a.apply(null,arguments)};b.$1=a;d=b}else d=a;this.__callbacks.push(d);return d};b.removeLast=function(){return this.__callbacks.pop()};b.remove=function(a){return this.removeIf(function(b){return b===a})};b.removeIf=function(a){var b=this.__callbacks;this.__callbacks=b.filter(function(b){return!a(b)});return b.length>this.__callbacks.length};b.clear=function(){this.__callbacks=[]};b.$2=function(){var a=this.__callbacks;for(var b=0,c=a.length;b<c;++b){var d=a[b];d.apply(null,arguments)}};return a}();f.SimpleHook=a}),66);
__d("BanzaiLazyQueue",["SimpleHook"],(function(a,b,c,d,e,f,g){var h=[],i=new(d("SimpleHook").SimpleHook)();a={onQueue:i,queuePost:function(a,b,c){h.push([a,b,c]),i.call(a,b,c)},flushQueue:function(){var a=h;h=[];return a}};f.exports=a}),34);
__d("gkx",["invariant","BanzaiLazyQueue","ExecutionEnvironment","emptyFunction"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j={},k={};function l(a){var b=j[a];b!=null||h(0,11797,a);k[a]||(k[a]=!0,b.hash!=null&&((i||(i=c("ExecutionEnvironment"))).canUseDOM||(i||(i=c("ExecutionEnvironment"))).isInWorker)&&d("BanzaiLazyQueue").queuePost("gk2_exposure",{identifier:a,hash:b.hash}));return b.result}l.add=function(a,b){for(var c in a)b&&b.entry++,!(c in j)?j[c]=a[c]:b&&b.dup_entry++};l.addLoggedInternal=function(a){l.add(a);for(a in a)k[a]=!0};a=c("emptyFunction");l.getGKs=function(){return null};l.getLogged=function(){return Object.keys(k).map(function(a){return{identifier:a,hash:j[a].hash}})};l.setPass=a;l.setFail=a;l.clear=a;b=l;g["default"]=b}),98);
__d("GHLDetectionUtils",["gkx"],(function(a,b,c,d,e,f,g){"use strict";function h(a){return typeof a.replace==="function"?a.replace(/\n/g," ").replace(/\s+/g," "):null}function a(){return c("gkx")("5415")?!0:typeof JSON.parse==="function"&&!(JSON.parse.toString===JSON.parse.toString.toString&&h(JSON.parse.toString())==="function parse() { [native code] }"&&h(JSON.parse.toString.toString())==="function toString() { [native code] }")}function b(a){return c("gkx")("8869")?!0:typeof a==="function"&&!(a.toString===a.toString.toString&&h(a.toString())==="function XMLHttpRequest() { [native code] }"&&h(a.toString.toString())==="function toString() { [native code] }")}function d(a){return c("gkx")("9063")?!0:typeof a==="function"&&!(a.prototype.fillText.toString===a.prototype.fillText.toString.toString&&h(a.prototype.fillText.toString())==="function fillText() { [native code] }"&&h(a.prototype.fillText.toString.toString())==="function toString() { [native code] }")}g.isJSONParseShimmed=a;g.isXHRModified=b;g.isCanvasFillTextModified=d}),98);
__d("GHLNetworkLayer",["FBLogger","GHLDetectionUtils","gkx"],(function(a,b,c,d,e,f,g){"use strict";function b(){try{if(c("gkx")("8068")&&d("GHLDetectionUtils").isXHRModified(a.XMLHttpRequest)){var b=Object.getPrototypeOf(a.XMLHttpRequest);if(!d("GHLDetectionUtils").isXHRModified(b))return b}return null}catch(a){c("FBLogger")("ghl").mustfix("error while getGHLXhr: %s",a.message);return null}}g.getGHLXhr=b}),98);
__d("justknobx",["invariant"],(function(a,b,c,d,e,f,g,h){"use strict";var i={};a={getBool:function(a){h(0,47459)},getInt:function(a){h(0,47459)},_:function(a){var b=i[a];b!=null||h(0,47458,a);return b.r},add:function(a,b){for(var c in a)b&&b.entry++,!(c in i)?i[c]=a[c]:b&&b.dup_entry++}};b=a;g["default"]=b}),98);
__d("getSameOriginTransport",["ExecutionEnvironment","FBLogger","GHLNetworkLayer","err","justknobx","unrecoverableViolation"],(function(a,b,c,d,e,f,g){var h,i=null;function b(){if(!(h||(h=c("ExecutionEnvironment"))).canUseDOM&&!(h||(h=c("ExecutionEnvironment"))).isInWorker)throw c("unrecoverableViolation")("getSameOriginTransport: Same origin transport unavailable in the server environment.","comet_infra",{},{blameToPreviousFrame:1});try{if(i==null)try{var b=c("justknobx")._("3323")?d("GHLNetworkLayer").getGHLXhr():null;b!=null?i=b:i=a.XMLHttpRequest}catch(b){c("FBLogger")("ghl").warn("error while getGHLXhr in getSameOriginTransport: %s",b.message),i=a.XMLHttpRequest}return new i()}catch(a){throw c("err")("getSameOriginTransport: %s",a.message)}}g["default"]=b}),98);
__d("asyncToGeneratorRuntime",["Promise"],(function(a,b,c,d,e,f){"use strict";var g;function h(a,c,d,e,f,h,i){try{var j=a[h](i),k=j.value}catch(a){d(a);return}j.done?c(k):(g||(g=b("Promise"))).resolve(k).then(e,f)}function a(a){return function(){var c=this,d=arguments;return new(g||(g=b("Promise")))(function(b,e){var f=a.apply(c,d);function g(a){h(f,b,e,g,i,"next",a)}function i(a){h(f,b,e,g,i,"throw",a)}g(void 0)})}}f.asyncToGenerator=a}),66);
__d("getErrorSafe",["fb-error"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("fb-error").getErrorSafe}),98);
__d("promiseDone",["ErrorPubSub","PromiseAnnotate","getErrorSafe"],(function(a,b,c,d,e,f,g){var h,i;function j(a){a=c("getErrorSafe")(a);a.loggingSource="PROMISE_DONE";(i||(i=c("ErrorPubSub"))).reportError(a)}function k(a){return async function(b){try{b=await a(b);return b}catch(a){j(a)}}}function a(a,b,c){var e=c!=null?k(c):null,f=arguments.length>1?a.then(b,e):a;c==null&&f.then(null,j);var g=(h||(h=d("PromiseAnnotate"))).getDisplayName(a);g!=null&&void (h||(h=d("PromiseAnnotate"))).setDisplayName(f,g)}g.default=a}),98);
__d("MakeHasteTranslations",["BootloaderConfig","BootloaderRetryTracker","ExecutionEnvironment","FBLogger","MakeHasteTranslationsMap","Promise","TimeSlice","err","fb-error","getSameOriginTransport","promiseDone"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j={},k=new(c("BootloaderRetryTracker"))({retries:(f=c("BootloaderConfig").translationRetries)!=null?f:c("BootloaderConfig").jsRetries,abortNum:(f=c("BootloaderConfig").translationRetryAbortNum)!=null?f:c("BootloaderConfig").jsRetryAbortNum,abortTime:(f=c("BootloaderConfig").translationRetryAbortTime)!=null?f:c("BootloaderConfig").jsRetryAbortTime,abortCallback:function(){c("FBLogger")("binary_transparency").warn("Translations retry abort")}});function l(a){a=JSON.parse(a);if(a!=null&&typeof a==="object"&&typeof a.translations==="object"&&Array.isArray(a.virtual_modules))return a;throw c("err")("Invalid response shape")}function m(a){return new(i||(i=b("Promise")))(function(b,d){var e=c("TimeSlice").getGuardedContinuation("MakeHasteTranslationsFetcher genSendRequest"),f=c("getSameOriginTransport")();f.open("GET",a,!0);f.onreadystatechange=function(g){if(f.readyState!==4)return;e(function(){c("fb-error").ErrorXFBDebug.addFromXHR(f);try{if(f.status!==200)throw c("err")("Received non-200 response");b(l(f.responseText))}catch(e){k.maybeScheduleRetry(a,function(){return b(m(a))},function(){return d(c("err")("Error processing response. XHR Error: %s, XHR status: %s, Response Text: %s",e.toString(),f.status,f.responseText.length>300?f.responseText.slice(0,300)+"...":f.responseText))})}})};f.send()})}var n="data:application/json;base64";function o(a){if(!a.includes(n))throw c("err")("Invalid data uri mime type");a=a.split(",");a[0];a=a[1];if(a==null)throw c("err")("Data uri contains no contents");return l(atob(a))}function p(e,f){if(!(h||(h=c("ExecutionEnvironment"))).isInBrowser||j[e]==="inprogress"||j[e]==="complete")return(i||(i=b("Promise"))).resolve();j[e]="inprogress";return(f.includes(n)?(i||(i=b("Promise"))).resolve(o(f)):m(f)).then(function(b){d("MakeHasteTranslationsMap").setBatch(b.translations),b.virtual_modules.forEach(function(b){return a.define(b,{})}),j[e]="complete"})["catch"](function(a){j[e]="failed",c("FBLogger")("binary_transparency","translation_download_error").catching(a).warn("Unable to download and process translation map. Url: %s",f)})}function e(a){for(a of Object.entries(a)){var b=a[0],d=a[1];c("promiseDone")(p(b,d))}}g.genFetchAndProcessTranslations=p;g.fetchTranslationsForEarlyFlush=e}),98);
__d("clearTimeout",["cr:3725"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:3725")}),98);
__d("setTimeout",["cr:4344"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:4344")}),98);
__d("NetworkHeartbeat",["clearTimeout","getSameOriginTransport","setTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h="/nw/",i=6400,j=100,k=null,l=0,m=null,n=!1;function o(a,b){m=c("getSameOriginTransport")(),m.open("GET",h,!0),m.onload=function(){m&&m.status===204&&(n=!0),q(a)},m.onerror=function(){r(a,b)},m.ontimeout=function(){r(a,b)},m.send()}function p(){m=null,j=100,l=0,c("clearTimeout")(k)}function q(a){p(),a()}function r(a,b){k=c("setTimeout")(function(){s(a,b,void 0,!0)},j),l++,j<i&&(j=Math.min(j*Math.pow(2,l),i)),b()}function s(a,b,c,d){c===void 0&&(c=function(){return!0}),d===void 0&&(d=!1),n||(d||m==null&&c())&&o(a,b)}function a(){return m!=null}g.maybeStartHeartbeat=s;g.isHeartbeatPending=a}),98);
__d("NetworkStatusImpl",["NetworkHeartbeat","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=[],k=typeof window!=="undefined"?window:self,l=k==null?void 0:(h=k.navigator)==null?void 0:h.onLine,m=2,n=5e3,o=[],p=[],q=0,r=!0,s=!1,t=!1,u=function(){y(r,!0)},v=function(){y(s,!0)};function w(){var a=j.slice();a.forEach(function(a){a({online:l})})}function x(a){a=j.indexOf(a);a>-1&&j.splice(a,1)}function y(a,b){b===void 0&&(b=!1);var c=l===a;b=!b&&a===r&&d("NetworkHeartbeat").isHeartbeatPending();if(c||b)return;t=t||a===s;l=a;l||d("NetworkHeartbeat").maybeStartHeartbeat(u,v);w()}function z(){var a=(i||(i=c("performanceNow")))();o=o.filter(function(b){return A(b.startTime,a)});p=p.filter(function(b){return A(b.startTime,a)});return p.length/o.length<m}var A=function(a,b){return a>b-n};function a(){return l}function b(a){j.push(a);var b=!1;return{remove:function(){b||(b=!0,x(a))}}}function e(){var a=(i||(i=c("performanceNow")))();o.push({startTime:a});d("NetworkHeartbeat").maybeStartHeartbeat(u,v,z)}function f(){var a=(i||(i=c("performanceNow")))();p.push({startTime:a});A(q,a)||(p=p.filter(function(b){return A(b.startTime,a)}),q=a)}function B(){return t}k.addEventListener("online",function(){y(r)});k.addEventListener("offline",function(){y(s)});g.isOnline=a;g.onChange=b;g.reportError=e;g.reportSuccess=f;g.wasOffline=B}),98);
__d("NetworkStatusSham",[],(function(a,b,c,d,e,f){"use strict";function a(){return!0}function b(a){return{remove:function(){}}}function c(){return}function d(){return}function e(){return!1}f.isOnline=a;f.onChange=b;f.reportError=c;f.reportSuccess=d;f.wasOffline=e}),66);
__d("NetworkStatus",["NetworkStatusImpl","NetworkStatusSham","gkx"],(function(a,b,c,d,e,f,g){"use strict";a=(c("gkx")("7742")||c("gkx")("20935"))&&c("gkx")("20936")?d("NetworkStatusImpl"):d("NetworkStatusSham");b=a;g["default"]=b}),98);
__d("RequireDeferredFactoryEvent",["$InternalEnum"],(function(a,b,c,d,e,f){a=b("$InternalEnum")({SUPPORT_DATA:"sd",CSS:"css"});c=a;f["default"]=c}),66);
__d("RequireDeferredReference",["CallbackDependencyManager","Promise","RequireDeferredFactoryEvent","ifRequireable","ifRequired","performanceNow","promiseDone","requireWeak"],(function(a,b,c,d,e,f,g){"use strict";var h,i;a=1;d=2;e=16;var j=a|d|e,k=null;function l(){k==null&&(k=new(c("CallbackDependencyManager"))());return k}function m(a,b){return a+":"+b}var n=new Set();f=function(){function a(a){this.$1=a}var d=a.prototype;d.setRequireDeferredForDisplay=function(a){this.$2=a};d.isRequireDeferredForDisplay=function(){return this.$2};d.getModuleId=function(){var a=this.$1;return a};d.getModuleIdAsRef=function(){return this.$1};d.preload=function(){};d.getModuleIfRequired=function(){return c("ifRequired").call(null,this.$1,function(a){return a})};d.getModuleIfRequireable=function(){return c("ifRequireable").call(null,this.$1,function(a){return a})};d.isAvailableInSSR_DO_NOT_USE=function(){return!0};d.$3=function(a){var b=this,d=c("ifRequireable")("InteractionTracingMetrics",function(a){return a.currentInteractionLogger().addRequireDeferred(b.getModuleId(),(i||(i=c("performanceNow")))())}),e=!1,f=function(b,f){d==null?void 0:d((i||(i=c("performanceNow")))(),f),e||a(b)};c("ifRequireable").call(null,this.$1,function(a){return f(a,!0)},function(){c("requireWeak").call(null,b.$1,function(a){return f(a,!1)})});return{remove:function(){e=!0}}};d.load=function(){var a=this;return new(h||(h=b("Promise")))(function(b){return a.$3(b)})};d.__setRef=function(a){return this};d.onReadyImmediately=function(a){return this.$3(a)};d.onReady=function(a){var d=!1,e=this.$3(function(e){c("promiseDone")((h||(h=b("Promise"))).resolve().then(function(){d||a(e)}))});return{remove:function(){d=!0,e.remove()}}};d.loadImmediately=function(a){return this.$3(a)};a.getRDModuleName_DO_NOT_USE=function(a){return"rd:"+a};a.unblock=function(d,e){var f=l(),g=function(d){n.has(d)||(n.add(d),f.registerCallback(function(){define(a.getRDModuleName_DO_NOT_USE(d),[d],function(){b.call(null,d)},j)},Array.from(c("RequireDeferredFactoryEvent").members(),function(a){return m(d,a)}))),f.satisfyPersistentDependency(m(d,e))};for(d of d)g(d)};return a}();g["default"]=f}),98);
__d("ResourceHasher",["invariant"],(function(a,b,c,d,e,f,g,h){"use strict";d="placeholder";var i=0;function a(a){return"async:"+a}function b(){return"ejs:"+i++}function c(a){typeof a==="string"||h(0,19551,a);return a}g.PLACEHOLDER=d;g.getAsyncHash=a;g.createExternalJSHash=b;g.getValidResourceHash=c}),98);
__d("ServerJsRuntimeEnvironment",["invariant"],(function(a,b,c,d,e,f,g,h){"use strict";var i;function a(a,b){i==null||h(0,71696),i={platform:a,executionContext:b}}function b(){return i!=null}function c(){var a;return(a=i)==null?void 0:a.executionContext}function d(){var a;return(a=i)==null?void 0:a.platform}g.init=a;g.isRunningServerJsRuntime=b;g.getExecutionContext=c;g.getPlatform=d}),98);
__d("TrustedTypesPolicyName",["$InternalEnum"],(function(a,b,c,d,e,f){a=b("$InternalEnum")({DEFAULT:"default",NOOP_DO_NOT_USE:"noop-do-not-use",UNSAFE_FUNCTION_DO_NOT_USE:"unsafe-function-do-not-use",DDS_INLINE_STYLE:"dds-inline-style",GHL_PLUS_HTML:"ghl-plus-html",LINK_TAG_HTML:"link-tag-html",BOOTLOADER_DATA_URI:"bootloader-data-uri",OC_URI_SCRIPT_URL:"oc-uri-script-urls",FB_URI_SCRIPT_URL:"fb-uri-script-urls",META_URI_SCRIPT_URL:"meta-uri-script-urls",RL_TEALIUM_CDN_URI:"rl-tealium-cdn-uri",SAME_ORIGIN_SCRIPT_URL:"same-origin-script-urls",WEB_WORKER_URL:"web-worker-url",YOUTUBE_IFRAME_URL:"youtube-iframe-uri",IORG_WEB_WORKER_POLICY:"iorg-web-worker-policy",BIG_PIPE_MARKUP:"big-pipe-markup",GOOGLE_ANALYTICS_URL:"google-analytics-url",FBQ_SCRIPT_URL:"fbq-script-url",CBQ_SCRIPT_URL:"cbq-script-url",DOM_IE_FIX:"dom-ie-fix",OZ_PLAYER_XML:"oz-player-xml",CHROMECAST_EXTENSION_URI:"chromecast-extension-uri",TRANSLATED_CMS_HTML:"translated-cms-html",XHP_HTML:"xhp-html",GSAP_SPLIT_TEXT:"gsap-split-test",MARKDOWN_HTML:"markdown-html",BT_MANIFEST:"bt-manifest"});c=a;f["default"]=c}),66);
__d("TrustedTypesUtils",["FBLogger"],(function(a,b,c,d,e,f,g){var h=typeof window!=="undefined";f=function(a){return a};function a(a){var b=h&&typeof window.origin!=="undefined"?window.origin:"undefined";c("FBLogger")("saf_web_trusted_types_rollout",b).blameToPreviousFrame().blameToPreviousFrame().warn(a);return a}function b(a){c("FBLogger")("saf_web").info("[Trusted-Types][%s]: %s",h&&typeof window.origin!=="undefined"?window.origin:"undefined",a)}function i(a){c("FBLogger")("saf_web").warn("[Trusted-Types][%s]: %s",h&&typeof window.origin!=="undefined"?window.origin:"undefined",a)}function d(a){c("FBLogger")("saf_web").mustfix("[Trusted-Types][%s]: %s",h&&typeof window.origin!=="undefined"?window.origin:"undefined",a)}function e(a,b){i("String '"+a.toString().slice(0,15)+"' is flowing to DOM XSS sink. Default Trusted Type policy was executed and removed dangerous elements. "+("Returned string is: '"+b.toString().slice(0,15)+"' If this is breaking your feature, post in ")+"Security Infra group.")}g.isBrowser=h;g.noop=f;g.noopAndLog=a;g.logInfo=b;g.logWarning=i;g.logError=d;g.logDefaultPolicySanitization=e}),98);
__d("TrustedTypes",["Env","TrustedTypesUtils"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(){return d("TrustedTypesUtils").isBrowser&&typeof window.trustedTypes!=="undefined"}var i=a()?window.trustedTypes:null,j=new Map(),k={createHTML:d("TrustedTypesUtils").noop,createScriptURL:d("TrustedTypesUtils").noop,createScript:d("TrustedTypesUtils").noop};function l(a,b){return function(e){for(var f=arguments.length,g=new Array(f>1?f-1:0),i=1;i<f;i++)g[i-1]=arguments[i];if((h||(h=c("Env"))).isTrustedTypesReportOnly)try{return b.apply(void 0,[e].concat(g))}catch(b){d("TrustedTypesUtils").logError("Exception in policy "+a+": "+b.message+", returning original string.");return a==="default"?!1:e}return b.apply(void 0,[e].concat(g))}}function m(a,b){if(i==null||!(h||(h=c("Env"))).useTrustedTypes)return k;var e=j.get(a);if(e!=null){d("TrustedTypesUtils").logWarning("A policy with name "+a+" already exists, returning existing policy.");return e}try{var f=i.createPolicy(a,b);e={createHTML:l(a,function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];return f.createHTML.apply(f,[a].concat(c))}),createScriptURL:l(a,function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];return f.createScriptURL.apply(f,[a].concat(c))}),createScript:l(a,function(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];return f.createScript.apply(f,[a].concat(c))})};j.set(a,e);return e}catch(a){d("TrustedTypesUtils").logError("Error creating Trusted Types policy: "+a)}return k}function b(){return j.get("default")}function e(a){return(a=i==null?void 0:i.isHTML(a))!=null?a:!1}function f(a){return(a=i==null?void 0:i.isScriptURL(a))!=null?a:!1}function n(a){return(a=i==null?void 0:i.isScript(a))!=null?a:!1}function o(a){if(i==null||!(h||(h=c("Env"))).useTrustedTypes)return;if(!(h||(h=c("Env"))).enableDefaultTrustedTypesPolicy)return;m("default",a.policy)}a={isSupportedNatively:a,isHTML:e,isScript:n,isScriptURL:f,createPolicy:m,getDefaultPolicy:b,createDefaultPolicy:o};g["default"]=a}),98);
__d("TrustedTypesBootloaderDataURIScriptURLPolicy",["TrustedTypes"],(function(a,b,c,d,e,f,g){"use strict";a={createScriptURL:function(a){return a}};b=c("TrustedTypes").createPolicy("bootloader-data-uri",a);d=b;g["default"]=d}),98);
__d("isCdnURI",[],(function(a,b,c,d,e,f){"use strict";function a(a){if(a.getProtocol()!=="http"&&a.getProtocol()!=="https")return!1;var b=Number(a.getPort());return!!b&&b!==80&&b!==443?!1:a.isSubdomainOfDomain("fbcdn.net")}f["default"]=a}),66);
__d("isExternalFBURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)externalfb\\.com$","i");function a(a){return g.test(a.getDomain())}f["default"]=a}),66);
__d("isFacebookDotNetURI",[],(function(a,b,c,d,e,f){"use strict";function a(a){if(a.getProtocol()!=="http"&&a.getProtocol()!=="https")return!1;var b=Number(a.getPort());if(!!b&&b!==80&&b!==443)return!1;return a.isSubdomainOfDomain("facebook.net")?!0:!1}f["default"]=a}),66);
__d("isInstagramCDNURI",[],(function(a,b,c,d,e,f){var g=null;function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;if(!a.getDomain()&&!a.getProtocol())return!1;if(a.getProtocol()!=="https")return!1;g||(g=new RegExp("^static\\.cdninstagram\\.com$","i"));return g.test(a.getDomain())}f["default"]=a}),66);
__d("isInstagramURI",[],(function(a,b,c,d,e,f){var g=null;function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;if(!a.getDomain()&&!a.getProtocol())return!1;if(a.getProtocol()!=="https")return!1;g||(g=new RegExp("(^|\\.)instagram\\.com$","i"));return g.test(a.getDomain())}f["default"]=a}),66);
__d("isInternURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)intern(mc)?\\.facebook\\.com$","i");function a(a){return g.test(a.getDomain())}f["default"]=a}),66);
__d("isInternalFBURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)internalfb\\.com$","i");function a(a){return g.test(a.getDomain())}f["default"]=a}),66);
__d("isMetaDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)meta\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!0:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("isMetaDotComBlobURI",["URI","isMetaDotComURI"],(function(a,b,c,d,e,f,g){var h;function a(a){if(!a.includes("blob:"))return!1;a=a.replace("blob:","");a=(h||(h=c("URI"))).tryParseURI(a);return a!=null&&c("isMetaDotComURI")(a)}g["default"]=a}),98);
__d("isOculusDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)oculus\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("isWhatsAppCdnURI",[],(function(a,b,c,d,e,f){function a(a){var b=a.getProtocol();a=a.getDomain();return b==="https"&&a==="static.whatsapp.net"}f["default"]=a}),66);
__d("TrustedTypesMetaURIScriptURLPolicy",["TrustedTypes","URI","err","isCdnURI","isExternalFBURI","isFacebookDotNetURI","isFacebookURI","isInstagramCDNURI","isInstagramURI","isInternURI","isInternalFBURI","isMetaDotComBlobURI","isOculusDotComURI","isWhatsAppCdnURI"],(function(a,b,c,d,e,f,g){"use strict";var h;a={createScriptURL:function(a){if(c("isMetaDotComBlobURI")(a))return a;var b=(h||(h=c("URI"))).tryParseURI(a);if(b!=null&&(c("isFacebookURI")(b)||c("isCdnURI")(b)||c("isWhatsAppCdnURI")(b)||c("isFacebookDotNetURI")(b)||c("isExternalFBURI")(b)||c("isOculusDotComURI")(b)||c("isInstagramCDNURI")(b)||c("isInstagramURI")(b)||c("isInternURI")(b)||c("isInternalFBURI")(b)))return a;throw c("err")("Violated policy TrustedTypesMetaURIScriptURLPolicy: "+a+" is not a Meta URI.")}};b=c("TrustedTypes").createPolicy("meta-uri-script-urls",a);d=b;g["default"]=d}),98);
__d("__debug",[],(function(a,b,c,d,e,f){a={};f["default"]=a}),66);
__d("objectKeys",[],(function(a,b,c,d,e,f){function a(a){return Object.keys(a)}f["default"]=a}),66);
__d("setTimeoutAcrossTransitions",["cr:7391"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:7391")}),98);
__d("Bootloader",["invariant","BootloaderConfig","BootloaderDocumentInserter","BootloaderEndpoint","BootloaderEvents","BootloaderEventsManager","BootloaderPreloader","BootloaderRetryTracker","CSSLoader","ClientConsistency","ErrorPubSub","ExecutionEnvironment","FBLogger","HasteBitMap","HasteResourceIndexUtil","JSResourceReferenceImpl","MakeHasteTranslations","NetworkStatus","RequireDeferredReference","ResourceHasher","ResourceTimingsStore","ServerJsRuntimeEnvironment","SiteData","TimeSlice","TrustedTypesBootloaderDataURIScriptURLPolicy","TrustedTypesMetaURIScriptURLPolicy","__debug","clearTimeout","cr:696703","err","fb-error","gkx","ifRequireable","ifRequired","nullthrows","objectKeys","performanceAbsoluteNow","performanceNow","promiseDone","setTimeoutAcrossTransitions"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k,l,m=function(){},n=new Set(),o=!!(f=c("BootloaderConfig")).deferBootloads,p=[],q=new Map(),r=new Set(),s=new Set(),t=new Map(),u=new Map(),v=new Map(),w=new Map(),x=new Map(),y=new Map(),z=new Map(),A=new Map(),B=new Set(),C=!1,D=new Set(),E=!1,F=new(c("BootloaderEventsManager"))(),G=new Set(),H=new(c("BootloaderRetryTracker"))({retries:f.jsRetries,abortNum:f.jsRetryAbortNum,abortTime:f.jsRetryAbortTime,abortCallback:function(){c("FBLogger")("bootloader","js_retry_abort").info("JS retry abort")}});(i||(i=c("ErrorPubSub"))).unshiftListener(function(a){var b=[];for(var c of t){var d=c[0];c[1];if(u.has(d))continue;d=L(d);if(d.type==="csr"||d.type==="async")continue;b.push(d.src)}a.loadingUrls=b});function I(a){if(o||!E)return!1;for(a of a){var b=w.get(a);if(!b)return!1;for(b of[b.r,((c=b.rdfds)==null?void 0:c.r)||[],((c=b.rds)==null?void 0:c.r)||[]]){var c;for(c of b)if(!x.has(c))return!1}}return!0}function J(a){var b=w.get(a);if(!b)throw c("fb-error").TAAL.blameToPreviousFile(c("err")("Bootloader: %s is not in the component map",a));return b}function K(a){var b=J(a);b.be&&(delete b.be,$.done(d("ResourceHasher").getAsyncHash(a)))}function L(a){var b=x.get(a);if(!b)throw c("fb-error").TAAL.blameToPreviousFile(c("err")("No resource entry for hash: %s",a));return b}function aa(a,b){var c=d("ResourceHasher").getAsyncHash(a);if(!x.has(c))x.set(c,{type:"async",module:a,blocking:!!b});else{a=L(c);a.type==="async"||h(0,21557);a.blocking&&!b&&(a.blocking=!1)}return c}function M(a){return!V(a)}function f(a){if(!M(a))return!1;a=J(a);a=a.be;return!!a}function N(a,b,d){var e=(j||(j=c("performanceAbsoluteNow")))(),f=b.src,g=c("ResourceTimingsStore").getUID("js",f);c("ResourceTimingsStore").annotate("js",g).addStringAnnotation("name",a).addStringAnnotation("source",f);c("ResourceTimingsStore").measureRequestSent("js",g);c("nullthrows")(self.bl_worker_import_wrapper)(f).then(function(){var b=H.getNumRetriesForSource(f);b>0&&c("FBLogger")("bootloader").info("JS retry success [%s] at %s | time: %s | retries: %s",a,f,(j||(j=c("performanceAbsoluteNow")))()-e,b);c("ResourceTimingsStore").measureResponseReceived("js",g);d()})["catch"](function(h){c("ResourceTimingsStore").measureResponseReceived("js",g);var i=(j||(j=c("performanceAbsoluteNow")))();H.maybeScheduleRetry(f,function(){N(a,b,d)},function(){v.set(a,i),c("FBLogger")("bootloader").catching(h).warn("JS loading error [%s] at %s | time: %s | retries: %s | concurrency: %s",a,f,i-e,H.getNumRetriesForSource(f),t.size-u.size),c("NetworkStatus").reportError(),d()})})}function O(a,b,d,e){if((k||(k=c("ExecutionEnvironment"))).isInWorker){N(a,b,d);return}e=c("nullthrows")(e);var f=document.createElement("script");b.d?(f.src=c("TrustedTypesBootloaderDataURIScriptURLPolicy").createScriptURL(b.src),f.nonce=c("BootloaderConfig").nonce):f.src=c("TrustedTypesMetaURIScriptURLPolicy").createScriptURL(b.src);f.async=!0;c("BootloaderConfig").enableRetryOnStuckResource&&f.setAttribute("fetchPriority","high");b.nc||(f.crossOrigin="anonymous");b.m!=null&&(f.dataset.btmanifest=b.m);b.tsrc!=null&&(f.dataset.tsrc=b.tsrc);f.dataset.bootloaderHashClient=a;P(f,a,b,d);s.add(a);e.appendChild(f);return}function P(a,b,d,e){var f=a.src,g=(j||(j=c("performanceAbsoluteNow")))(),h=c("TimeSlice").getGuardedContinuation("Bootloader script.onresponse"),i=c("ResourceTimingsStore").getUID("js",f);c("ResourceTimingsStore").annotate("js",i).addStringAnnotation("name",b).addStringAnnotation("source",f);c("ifRequireable")("TimeSliceInteraction",function(a){a.informGlobally("bootloader._loadJS").addStringAnnotation("source",f).addStringAnnotation("name",b)});c("ResourceTimingsStore").measureRequestSent("js",i);var k=!1;a.onload=h.bind(void 0,function(){k=!0;var a=H.getNumRetriesForSource(f);a>0&&c("FBLogger")("bootloader").info("JS retry success [%s] at %s | time: %s | retries: %s",b,f,(j||(j=c("performanceAbsoluteNow")))()-g,a);c("ResourceTimingsStore").measureResponseReceived("js",i);e()});a.onerror=h.bind(void 0,function(){k=!0;var h=(j||(j=c("performanceAbsoluteNow")))();v.set(b,h);c("ResourceTimingsStore").measureResponseReceived("js",i);H.maybeScheduleRetry(f,function(){var c=a.parentNode;c&&(v["delete"](b),c.removeChild(a),O(b,d,e,c))},function(){c("FBLogger")("bootloader").warn("JS loading error [%s] at %s | time: %s | retries: %s | concurrency: %s",b,f,h-g,H.getNumRetriesForSource(f),t.size-u.size),c("NetworkStatus").reportError(),e()})});c("BootloaderConfig").enableRetryOnStuckResource&&c("setTimeoutAcrossTransitions")(h.bind(void 0,function(){if(k)return;H.maybeRetryImmediately(f,function(){var c=a.parentNode;c&&(c.removeChild(a),O(b,d,e,c))},function(){c("FBLogger")("bootloader").warn("JS loading timeout [%s] at %s | retries: %s | concurrency: %s",b,f,H.getNumRetriesForSource(f),t.size-u.size)})}),1e4)}function Q(a,b,d){return function(){c("FBLogger")("bootloader").warn("CSS timeout [%s] at %s | concurrency: %s",a,b.src,t.size-u.size),v.set(a,(j||(j=c("performanceAbsoluteNow")))()),c("NetworkStatus").reportError(),d()}}function R(a,b,c,d){if(!b.includes("/rsrc.php")||b.includes("/intern/rsrc.php"))return[];b=((b=b.match(/(.*\/)([^.]+)(\.)/))!=null?b:[])[2];return b==null?[]:(b=(b=b.match(/.{1,11}/g))==null?void 0:b.filter(function(b,e){return!c.has(e)&&a[e]>d}))!=null?b:[]}function S(a,b){var c=a.replace(/\/y[a-zA-Z0-9_-]\//,"/");if(c.includes("/intern/rsrc.php")||c.includes("/intern/rsrc-translations.php"))return c.replace(/(!)(.+)(\.(?:css|js)(?:$|\?))/,function(a,c,d,e){return c+d.split(",").filter(function(a,c){return!b.has(c)}).join(",")+e});else if(c.includes("/rsrc.php")||c.includes("/rsrc-translations.php"))return c.replace(/(.*\/)([^.]+)(\.)/,function(a,c,d,e){return c+d.match(/.{1,11}/g).filter(function(a,c){return!b.has(c)}).join("")+e});else return a}function ba(a,b,e,f){if(c("gkx")("15745")){if(t.has(a)&&!v.has(a))return}else if(t.has(a))return;v.has(a)&&v["delete"](a);t.set(a,(j||(j=c("performanceAbsoluteNow")))());var g=[];if((b.type==="js"||b.type==="css")&&b.p!=null&&b.d!==1&&c("BootloaderConfig").hypStep4){var i=d("HasteResourceIndexUtil").parseResourceIndexes(b.p),l=new Set(),m=0;i.forEach(function(b,c){b!==d("HasteResourceIndexUtil").UNKNOWN_RESOURCE_INDEX&&y.get(b)!==a?l.add(c):b>m&&(m=b)});if(m>c("BootloaderConfig").btCutoffIndex){var n=R(i,b.src,l,c("BootloaderConfig").btCutoffIndex);g.push(n)}if(l.size===i.length)return;else l.size>0&&(b.src=S(b.src,l),b.type==="js"&&b.tsrc!=null&&b.tsrc.trim()!==""&&(b.tsrc=S(c("nullthrows")(b.tsrc),l)))}b.type==="js"&&b.tsrc!=null&&b.tsrc.trim()!==""&&c("promiseDone")(d("MakeHasteTranslations").genFetchAndProcessTranslations(a,c("nullthrows")(b.tsrc)));d("BootloaderPreloader").preloadResource(b,e,void 0,void 0);switch(b.type){case"js":O(a,b,function(){$.done(a);for(var b of g)d("BootloaderEvents").notifyResourceInLongTailBTManifest(b,f)},e);break;case"css":n=function(){return $.done(a)};if((k||(k=c("ExecutionEnvironment"))).isInWorker){n();break}c("CSSLoader").loadStyleSheet(a,b.src,c("nullthrows")(e),!b.nc,b.m,n,Q(a,b,n));break;case"async":c("BootloaderEndpoint").load(b.module,b.blocking,a);break;default:b.type,h(0,3721)}}function T(a,e,f,g,i){var j=new Map(),k=i!=null?i:d("BootloaderEvents").newResourceMapSet();i=[];var l=[],m=[];for(a of W(a)){var n=a[0],o=a[1],p=void 0;switch(o.type){case"css":p=o.nonblocking?"nonblocking":"blocking";break;case"js":p="default";break;case"async":p=o.blocking?"blocking":"nonblocking";break;default:o.type,h(0,3721)}k[p].set(n,o);var q=F.rsrcDone(n);m.push(q);p!=="nonblocking"&&(l.push(q),p==="blocking"&&i.push(q));c("gkx")("15745")?(!t.has(n)||v.has(n))&&j.set(n,o):t.has(n)||j.set(n,o)}var r,s;!b("cr:696703")?r=s=function(a){return a()}:(s=b("cr:696703").scheduleLoggingPriCallback,r=b("cr:696703").getUserBlockingRunAtCurrentPriCallbackScheduler_DO_NOT_USE());var u=e.onBlocking,w=e.onAll,x=e.onLog;u&&F.registerCallback(function(){r(u)},i);w&&F.registerCallback(function(){r(w)},l);x&&F.registerCallback(function(){s(function(){return x(k)})},m);for(p of j){q=p[0];n=p[1];ba(q,n,f,g)}}function U(a,b,e){x.set(a,b);if(b.type==="async"||b.type==="csr")return;var f=b.p;if(f)for(f of d("HasteResourceIndexUtil").parseResourceIndexes(f)){if(f===d("HasteResourceIndexUtil").UNKNOWN_RESOURCE_INDEX)continue;(!y.has(f)||e)&&y.set(f,a);b.c&&c("BootloaderConfig").csrOn&&d("HasteBitMap").add("__csr",f)}ca(a)}function ca(a){c("BootloaderConfig").enableLoadingUnavailableResources&&G.has(a)&&(G["delete"](a),$.loadResources([a]))}function da(a,b){var e=F.bootload(b);if(B.has(e))return[e,null];B.add(e);var f=(j||(j=c("performanceAbsoluteNow")))();b={ref:a,components:b,timesliceContext:c("TimeSlice").getContext(),startTime:(a=q.get(e))!=null?a:f,fetchStartTime:f,callbackStart:0,callbackEnd:0,tierOne:d("BootloaderEvents").newResourceMapSet(),tierTwo:d("BootloaderEvents").newResourceMapSet(),tierThree:d("BootloaderEvents").newResourceMapSet(),beRequests:new Map()};d("BootloaderEvents").notifyBootloadStart(b);return[e,b]}function ea(a){return c("ifRequired").call(null,a,function(){return!0},function(){return!1})}function V(a){return c("ifRequireable").call(null,a,function(){return!0},function(){return!1})}function fa(a,b,f,g){A.has(a)||A.set(a,{firstBootloadStart:(j||(j=c("performanceAbsoluteNow")))(),logData:new Set()});g&&c("nullthrows")(A.get(a)).logData.add(g);var h=J(a),i=h.r,k=h.rdfds,l=h.rds;h=h.be;h=M(a)?aa(a,h):null;h==null&&F.notify(F.beDone(a));T(h!=null?[h].concat(i):i,{onAll:function(){return F.notify(F.tierOne(a))},onLog:function(){return F.notify(F.tierOneLog(a))}},f,a,g==null?void 0:g.tierOne);var m=(k==null?void 0:k.m)||[],n=function(d){T((k==null?void 0:k.r)||[],{onBlocking:function(){return c("RequireDeferredReference").unblock(m,"css")},onAll:function(){return F.registerCallback(function(){F.notify(F.tierTwoStart(a)),e.call(null,m.map(c("RequireDeferredReference").getRDModuleName_DO_NOT_USE),function(){return F.notify(F.tierTwo(a))})},[F.tierOne(a),b])},onLog:function(){return F.notify(F.tierTwoLog(a))}},d,a,g==null?void 0:g.tierTwo)};c("BootloaderConfig").tieredLoadingFromTier!=null&&c("BootloaderConfig").tieredLoadingFromTier<=2?F.registerCallback(function(){return d("BootloaderDocumentInserter").batchDOMInsert(n)},[F.tierOne(a)]):n(f);var o=(l==null?void 0:l.m)||[],p=function(b){T((l==null?void 0:l.r)||[],{onBlocking:function(){return c("RequireDeferredReference").unblock(o,"css")},onAll:function(){return F.registerCallback(function(){F.notify(F.tierThreeStart(a)),e.call(null,o.map(c("RequireDeferredReference").getRDModuleName_DO_NOT_USE),function(){return F.notify(F.tierThree(a))})},[F.tierTwo(a)])},onLog:function(){return F.notify(F.tierThreeLog(a))}},b,a,g==null?void 0:g.tierThree)};c("BootloaderConfig").tieredLoadingFromTier!=null&&c("BootloaderConfig").tieredLoadingFromTier<=3?F.registerCallback(function(){return d("BootloaderDocumentInserter").batchDOMInsert(p)},[F.tierTwo(a)]):p(f)}function W(a){var b=new Map();for(a of a){var e=x.get(a);if(!e){G.add(a);c("FBLogger")("bootloader").mustfix("Unable to resolve resource %s.",a);continue}var f=void 0;if(e.type==="csr")f=d("HasteResourceIndexUtil").parseResourceIndexes(e.src);else if(e.p)f=d("HasteResourceIndexUtil").parseResourceIndexes(e.p),f.includes(d("HasteResourceIndexUtil").UNKNOWN_RESOURCE_INDEX)&&b.set(a,e),f=f.filter(function(a){return a!==d("HasteResourceIndexUtil").UNKNOWN_RESOURCE_INDEX});else{b.set(a,e);continue}for(e of f){r.add(e);f=y.get(e);if(f==null){c("FBLogger")("bootloader").mustfix("SoT hash unavailable for hash %s",e);throw new Error("Got unexpected null or undefined")}var g=L(f);g.type!=="csr"||h(0,20056,f);b.set(f,g)}}return b.entries()}function X(a){var b=a.getAttribute("data-bootloader-hash");if(b==null)return;var e=d("ResourceHasher").getValidResourceHash(b);if(a.id){if(D.has(a.id))return;D.add(a.id)}b=a.tagName==="SCRIPT"?{src:a.src,type:"js"}:{src:a.href,type:"css"};a.crossOrigin==null&&(b.nc=1);b.type==="js"&&a.dataset.tsrc!=null&&a.dataset.tsrc.trim()!==""&&(b.tsrc=a.dataset.tsrc,c("promiseDone")(d("MakeHasteTranslations").genFetchAndProcessTranslations(e,b.tsrc)));b.type==="css"&&a.getAttribute("data-nonblocking")&&(b.nonblocking=1);var f=a.getAttribute("data-c");f=="1"?b.c=1:f=="2"&&(b.c=2);f=a.getAttribute("data-p");if(f!=null){b.p=f;f=d("HasteResourceIndexUtil").parseResourceIndexes(f);var g=Math.max.apply(Math,f);g>c("BootloaderConfig").btCutoffIndex&&d("BootloaderEvents").notifyResourceInLongTailBTManifest(R(f,b.src,new Set(),c("BootloaderConfig").btCutoffIndex),"pickupPageResource")}g=a.getAttribute("data-btmanifest");g!=null&&(b.m=g);x.has(e)&&!c("BootloaderConfig").silentDups&&c("FBLogger")("bootloader").warn("Duplicate resource [%s]: %s",e,b.src);U(e,b,!0);t.set(e,(j||(j=c("performanceAbsoluteNow")))());f=function(){return $.done(e)};g=b.type==="js"?!a.getAttribute("async"):((g=a.parentNode)==null?void 0:g.tagName)==="HEAD";g||window._btldr&&window._btldr[e]?f():b.type==="js"?P(a,e,b,f):(c("FBLogger")("bootloader").info("Encountered body CSS not handled by BootloaderScriptListener: {\n          hash: '%s',\n          src: '%s',\n          cohort: '%s',\n        }",e,b.src,c("SiteData").pkg_cohort),c("CSSLoader").setupEventListenersForPotentiallyLoadedCSS(e,b.src,d("BootloaderDocumentInserter").getDOMContainerNode(),f,Q(e,b,f),a))}function Y(){if(C)return;C=!0;if(!(k||(k=c("ExecutionEnvironment"))).canUseDOM||(k||(k=c("ExecutionEnvironment"))).isInWorker)return;Array.from(document.getElementsByTagName("link")).forEach(function(a){return X(a)});Array.from(document.getElementsByTagName("script")).forEach(function(a){return X(a)})}function Z(){E=!0;var a=p;p=[];a.forEach(function(a){var b=a[0],c=a[1],d=a[2];a=a[3];a(function(){$.loadModules.apply($,[b,c,d])})})}var $={loadModules:function(a,b,f){b===void 0&&(b=m);f===void 0&&(f="loadModules: unknown caller");var g=a,h,i=!1,k=function(){c("clearTimeout")(h),i||b.apply(void 0,arguments)};a={remove:function(){i=!0}};if(c("BootloaderConfig").fastPathForAlreadyRequired&&g.every(function(a){return V(a)})){e.call(null,g,function(){k.apply(void 0,arguments)});return a}if(!I(g)){var l="Deferred: Bootloader.loadModules";l=c("TimeSlice").getGuardedContinuation(l);p.push([g,k,f,l]);l=F.bootload(g);q.set(l,(l=q.get(l))!=null?l:(j||(j=c("performanceAbsoluteNow")))());return a}l=da(f,g);var n=l[0],o=l[1];F.registerCallback(e.bind(null,g,function(){o&&(o.callbackStart=(j||(j=c("performanceAbsoluteNow")))()),k.apply(void 0,arguments),o&&(o.callbackEnd=(j||(j=c("performanceAbsoluteNow")))()),F.notify(n)}),g.map(function(a){return F.tierOne(a)}));d("BootloaderDocumentInserter").batchDOMInsert(function(b){for(var a of g)fa(a,n,b,o)});if(o){l=new Set([n]);for(var r of g)l.add(F.beDone(r)),l.add(F.tierThree(r)),l.add(F.tierOneLog(r)),l.add(F.tierTwoLog(r)),l.add(F.tierThreeLog(r));F.registerCallback(function(){d("BootloaderEvents").notifyBootload(o)},Array.from(l));c("ifRequireable")("TimeSliceInteraction",function(a){a.informGlobally("Bootloader.loadResources").addSetAnnotation("requested_hashes",Array.from(d("BootloaderEvents").flattenResourceMapSet(o.tierOne).keys())).addSetAnnotation("rdfd_requested_hashes",Array.from(d("BootloaderEvents").flattenResourceMapSet(o.tierTwo).keys())).addSetAnnotation("rd_requested_hashes",Array.from(d("BootloaderEvents").flattenResourceMapSet(o.tierThree).keys())).addStringAnnotation("bootloader_reference",f).addSetAnnotation("requested_components",g)});h=c("setTimeoutAcrossTransitions")(function(){d("BootloaderEvents").notifyBootloaderCallbackTimeout(o)},c("BootloaderConfig").timeout)}return a},loadResources:function(a,b){Y(),d("BootloaderDocumentInserter").batchDOMInsert(function(c){return T(a.map(function(a){return d("ResourceHasher").getValidResourceHash(a)}),b!=null?b:Object.freeze({}),c,"loadResources")})},loadTieredResources:function(a,b){var e=c("BootloaderConfig").tieredLoadingFromTier!=null&&c("BootloaderConfig").tieredLoadingFromTier<=2,f=c("BootloaderConfig").tieredLoadingFromTier!=null&&c("BootloaderConfig").tieredLoadingFromTier<=3,g=!1,h=!1,i=function(c){var e;T(((e=a==null?void 0:a.rds)!=null?e:[]).map(function(a){return d("ResourceHasher").getValidResourceHash(a)}),b!=null?b:Object.freeze({}),c,"loadTieredResources")},j=function(b){var c;T(((c=a==null?void 0:a.rdfds)!=null?c:[]).map(function(a){return d("ResourceHasher").getValidResourceHash(a)}),{onAll:function(){e?i(b):f&&(h=!0,g&&i(b))}},b,"loadTieredResources")},k=function(b){var c;T(((c=a==null?void 0:a.r)!=null?c:[]).map(function(a){return d("ResourceHasher").getValidResourceHash(a)}),{onAll:function(){e?j(b):f&&(g=!0,h&&i(b))}},b,"loadTieredResources")};e?d("BootloaderDocumentInserter").batchDOMInsert(k):d("BootloaderDocumentInserter").batchDOMInsert(function(a){k(a),j(a)})},requestJSResource_UNSAFE_NEEDS_REVIEW_BY_SECURITY_AND_XFN:function(a){var b=d("ResourceHasher").createExternalJSHash();U(b,{type:"js",src:a,nc:1},!1);$.loadResources([b])},done:function(a){u.set(a,(j||(j=c("performanceAbsoluteNow")))()),F.notify(F.rsrcDone(a))},beDone:function(a,b,c){for(d of(d=(d=A.get(a))==null?void 0:d.logData)!=null?d:[]){var d;d.beRequests.set(b,c)}F.notify(F.beDone(a))},handlePayload:function(a,b){var e;for(f of(f=a.rsrcTags)!=null?f:[]){var f;X(document.getElementById(f))}f=(f=(f=a.consistency)==null?void 0:f.rev)!=null?f:null;$.setResourceMap((e=a.rsrcMap)!=null?e:{},a.sotUpgrades,f,b);a.indexUpgrades&&c("objectKeys")(a.indexUpgrades).forEach(function(b){var e=d("HasteResourceIndexUtil").parseResourceIndexes(c("nullthrows")(a.indexUpgrades)[b]);e.length&&e.forEach(function(a){return d("HasteBitMap").add(b,a)})});a.compMap&&$.enableBootload(a.compMap,b)},enableBootload:function(a,b){for(var c in a)b&&b.comp++,!w.has(c)?(w.set(c,a[c]),n.has(c)&&(n["delete"](c),K(c))):b&&b.dup_comp++;Y();o||Z()},undeferBootloads:function(a){a===void 0&&(a=!1);if(window.location.search.indexOf("&__deferBootloads=")!==-1)return;a&&o&&d("BootloaderEvents").notifyDeferTimeout({componentMapSize:w.size,pending:p.map(function(a){var b=a[0];a[1];var c=a[2];a[3];return{components:b,ref:c}}),time:(l||(l=c("performanceNow")))()});o=!1;w.size&&Z()},markComponentsAsImmediate:function(a){for(a of a)w.has(a)?K(a):n.add(a)},setResourceMap:function(a,b,e,f){var g=!1;for(var h in a){f&&f.rsrc++;h=d("ResourceHasher").getValidResourceHash(h);e!=null&&z.set(h,e);var i=a[h],j=x.get(h);!j?(i.type==="js"&&(g=!0),U(h,i,!1)):(f&&f.dup_rsrc++,(j.type==="js"&&i.type==="js"||j.type==="css"&&i.type==="css")&&(i.d&&!j.d&&(i.type==="js"&&(g=!0),i.nonce!=null&&j.type==="js"&&(j.nonce=i.nonce),j.src=i.src,j.d=1)))}g&&e!=null&&c("ClientConsistency").addAdditionalRevision(e);if(b)for(i of b){j=x.get(i);j&&U(i,j,!0)}},getURLToHashMap:function(){var a=new Map();for(var b of x){var c=b[0],d=b[1];if(d.type==="async"||d.type==="csr")continue;a.set(d.src,c)}return a},loadPredictedResourceMap:function(a,b,c){$.setResourceMap(a,null,c),$.loadResources(Object.keys(a),b)},getCSSResources:function(a){var b=[];for(a of W(a)){var c=a[0],d=a[1];d.type==="css"&&b.push(c)}return b},getBootloadPendingComponents:function(){var a=new Map();for(var b of A){var c=b[0];ea(c)||a.set(c,$.getComponentDebugState(c))}return a},getComponentDebugState:function(a){var b=function(a){return!!F.getEventTime(a)};return{phases:{tierOne:b(F.tierOne(a)),tierOneLog:b(F.tierOneLog(a)),tierTwo:b(F.tierTwo(a)),tierTwoLog:b(F.tierTwoLog(a)),tierThree:b(F.tierThree(a)),tierThreeLog:b(F.tierThreeLog(a)),beDone:b(F.beDone(a)),asyncHash:b(F.rsrcDone(d("ResourceHasher").getAsyncHash(a)))},unresolvedDeps:c("__debug").debugUnresolvedDependencies([a]),nonJSDeps:(b=c("__debug").modulesMap[a])==null?void 0:b.nonJSDeps,hasError:(b=c("__debug").modulesMap[a])==null?void 0:b.hasError}},getBootloadedComponents:function(){var a=new Map();for(var b of A){var c=b[0],d=b[1];a.set(c,d.firstBootloadStart)}return a},notifyManuallyLoadedResourcesInWorker:function(a,b){var e=function(e){var f=d("ResourceHasher").getValidResourceHash(e),g=a[f];if(g.type==="js"||g.type==="css"){x.has(f)&&!c("BootloaderConfig").silentDups&&c("FBLogger")("bootloader").warn("Duplicate manual resource [%s]: %s",f,g.src);U(f,g,!0);g.type==="js"&&g.tsrc!=null&&g.tsrc.trim()!==""&&c("promiseDone")(d("MakeHasteTranslations").genFetchAndProcessTranslations(f,c("nullthrows")(g.tsrc)));t.set(f,(j||(j=c("performanceAbsoluteNow")))());var h=function(){return $.done(f)};e=b[f];g.type==="js"&&e?c("promiseDone")(e,h,function(){N(f,g,h)}):h()}};for(var f in a)e(f)},getResourceState:function(a){return{loadStart:t.get(a),loadEnd:u.get(a),loadError:v.get(a)}},getComponentTiming:function(a){var b;return{tierTwoStart:(b=F.getEventTime(F.tierTwoStart(a)))!=null?b:0,tierTwoEnd:(b=F.getEventTime(F.tierTwo(a)))!=null?b:0,tierThreeStart:(b=F.getEventTime(F.tierThreeStart(a)))!=null?b:0,tierThreeEnd:(b=F.getEventTime(F.tierThree(a)))!=null?b:0}},getLoadedResourceCount:function(){return u.size},getErrorCount:function(){return v.size},forceFlush:function(){c("BootloaderEndpoint").forceFlush()},__debug:{componentMap:w,DOMAppendedJSHashes:s,requestedRsrcIndex:r,requested:t,resources:x,riMap:y,retries:H.getAllRetryAttempts_FOR_DEBUG_ONLY(),errors:v,loaded:u,bootloaded:A,notAvailableResources:G,queuedToMarkAsImmediate:n,_resolveCSRs:W,revMap:z,_getQueuedLoadModules:function(){return p},_dequeueLoadModules:function(a){a=p.splice(a,1);if(!a.length)return;a=a[0];var b=a[0],c=a[1],d=a[2];a=a[3];var e=o,f=E;o=!1;E=!0;a(function(){$.loadModules.apply($,[b,c,d])});o=e;E=f}}};c("JSResourceReferenceImpl").setBootloader($);o&&!a.__comet_ssr_is_server_env_DO_NOT_USE&&!d("ServerJsRuntimeEnvironment").isRunningServerJsRuntime()&&((l||(l=c("performanceNow")))()>15e3?$.undeferBootloads(!0):c("setTimeoutAcrossTransitions")(function(){$.undeferBootloads(!0)},15e3-(l||(l=c("performanceNow")))()));f=$;g["default"]=f}),98);
__d("CSRFGuard",[],(function(a,b,c,d,e,f){"use strict";c="for (;;);";var g=/^for ?\(;;\);/;d=c.length;function a(a){return!!a.match(g)}function b(a){var b=a.match(g);return b?a.substr(b[0].length):b}f.regex=g;f.length=d;f.exists=a;f.clean=b}),66);
/**
 * License: https://www.facebook.com/legal/license/Ga6vBwdwgUx/
 */
__d("ImmediateImplementation",["ImmediateImplementationExperiments"],(function(a,b,c,d,e,f){(function(c,d){"use strict";var e=1,g={},h={},i=h,j=!1,k=c.document,l,m,n,o="setImmediate$"+Math.random()+"$";function p(){var a=c.event;return!a?!1:a.isTrusted&&["change","click","contextmenu","dblclick","mouseup","pointerup","reset","submit","touchend"].includes(a.type)||a.type==="message"&&a.source===c&&typeof a.data==="string"&&a.data.indexOf(o)===0}function q(a){var b=a[0];a=Array.prototype.slice.call(a,1);g[e]=function(){b.apply(void 0,a)};i=i.next={handle:e++};return i.handle}function r(){var a,b;while(!j&&(a=h.next)){h=a;if(b=g[a.handle]){j=!0;try{b(),j=!1}finally{s(a.handle),j&&(j=!1,h.next&&l(r))}}}}function s(a){delete g[a]}function d(){if(c.postMessage&&!c.importScripts){var a=!0,b=function b(){a=!1,c.removeEventListener?c.removeEventListener("message",b,!1):c.detachEvent("onmessage",b)};if(c.addEventListener)c.addEventListener("message",b,!1);else if(c.attachEvent)c.attachEvent("onmessage",b);else return!1;c.postMessage("","*");return a}}function t(){var a=function(a){a.source===c&&typeof a.data==="string"&&a.data.indexOf(o)===0&&r()};c.addEventListener?c.addEventListener("message",a,!1):c.attachEvent("onmessage",a);l=function(){var a=q(arguments);c.originalPostMessage?c.originalPostMessage(o+a,"*"):c.postMessage(o+a,"*");return a};m=l}function u(){var a=new MessageChannel(),b=!1;a.port1.onmessage=function(a){b=!1,r()};l=function(){var c=q(arguments);b||(a.port2.postMessage(c),b=!0);return c};n=l}function v(){var a=k.documentElement;l=function(){var b=q(arguments),c=k.createElement("script");c.onreadystatechange=function(){c.onreadystatechange=null,a.removeChild(c),c=null,r()};a.appendChild(c);return b}}function w(){l=function(){setTimeout(r,0);return q(arguments)}}d()?c.MessageChannel&&b("ImmediateImplementationExperiments").prefer_message_channel?(t(),u(),l=function(){if(p())return m.apply(null,arguments);else return n.apply(null,arguments)}):t():c.MessageChannel?u():k&&k.createElement&&"onreadystatechange"in k.createElement("script")?v():w();f.setImmediate=l;f.clearImmediate=s})(typeof self==="undefined"?typeof a==="undefined"?this:a:self)}),null);
__d("clearImmediatePolyfill",["ImmediateImplementation"],(function(a,b,c,d,e,f){c=a.clearImmediate||b("ImmediateImplementation").clearImmediate;f["default"]=c}),66);
__d("clearImmediate",["clearImmediatePolyfill"],(function(a,b,c,d,e,f,g){function a(a){c("clearImmediatePolyfill")(a)}g["default"]=a}),98);
__d("currentCometRouterInstance",[],(function(a,b,c,d,e,f){"use strict";var g=null;function a(){return g}function b(a){g=a}f.get_THIS_IS_NOT_WHAT_YOU_WANT=a;f.register=b}),66);
__d("getTopMostRouteInfo",[],(function(a,b,c,d,e,f){"use strict";function a(a){var b=a.hosted,c=a.main;a=a.pushViewStack;if(a&&a.length>0){a=a[a.length-1];a.depth;a.key;a=babelHelpers.objectWithoutPropertiesLoose(a,["depth","key"]);return a}return b?b:c}f["default"]=a}),66);
__d("getTopMostRoute",["getTopMostRouteInfo"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return c("getTopMostRouteInfo")(a).route}g["default"]=a}),98);
__d("CurrentCanonicalRoute",["currentCometRouterInstance","getTopMostRoute"],(function(a,b,c,d,e,f,g){function a(){var a;a=(a=d("currentCometRouterInstance").get_THIS_IS_NOT_WHAT_YOU_WANT())==null?void 0:a.getCurrentRouterState();if(a!=null){a=c("getTopMostRoute")(a);return a.canonicalRouteName}return null}g.getForGlobalLoggingOnly=a}),98);
__d("isMessengerDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)messenger\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("isWorkplaceDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)workplace\\.com$","i");function a(a){return a.getProtocol()==="https"&&g.test(a.getDomain())}f["default"]=a}),66);
__d("DTSGUtils",["SprinkleConfig","isCdnURI","isFacebookURI","isInstagramURI","isMessengerDotComURI","isOculusDotComURI","isWorkplaceDotComURI"],(function(a,b,c,d,e,f){"use strict";a={getNumericValue:function(a){var c=0;for(var d=0;d<a.length;d++)c+=a.charCodeAt(d);d=c.toString();return b("SprinkleConfig").should_randomize?d:b("SprinkleConfig").version+d},shouldAppendToken:function(a){return!b("isCdnURI")(a)&&!a.isSubdomainOfDomain("fbsbx.com")&&(b("isFacebookURI")(a)||b("isInstagramURI")(a)||b("isMessengerDotComURI")(a)||b("isWorkplaceDotComURI")(a)||b("isOculusDotComURI")(a)||a.isSubdomainOfDomain("freebasics.com")||a.isSubdomainOfDomain("discoverapp.com"))}};e.exports=a}),null);
__d("HasteBitMapName",[],(function(a,b,c,d,e,f){a=Object.freeze({CSR:"__csr",HSDP:"__hsdp",HBLP:"__hblp"});f["default"]=a}),66);
__d("ge",[],(function(a,b,c,d,e,f){function a(a,b,c){if(typeof a!=="string")return a;else if(!b)return document.getElementById(a);else return g(a,b,c)}function g(a,b,c){var d;if(h(b)==a)return b;else if(b.getElementsByTagName){c=b.getElementsByTagName(c||"*");for(d=0;d<c.length;d++)if(h(c[d])==a)return c[d]}else{c=b.childNodes;for(d=0;d<c.length;d++){b=g(a,c[d]);if(b)return b}}return null}function h(a){return a.getAttribute?a.getAttribute("id"):null}f["default"]=a}),66);
__d("replaceTransportMarkers",["BanzaiLazyQueue","ge","memoize"],(function(a,b,c,d,e,f,g){var h=new Set();function i(a,e,f){var g=f!==void 0?e[f]:e,j;if(Array.isArray(g))for(j=0;j<g.length;j++)i(a,g,j);else if(g&&typeof g==="object")if(g.__m)g.__lazy?e[f]=c("memoize")(b.bind(null,g.__m)):e[f]=b.call(null,g.__m);else if(g.__jsr)e[f]=new(b.call(null,"JSResourceReferenceImpl"))(g.__jsr).__setRef("replaceTransportMarkers");else if(g.__dr)e[f]=new(b.call(null,"RequireDeferredReference"))(g.__dr).__setRef("replaceTransportMarkers");else if(g.__rc)g.__rc[0]===null?e[f]=null:e[f]=b.call(null,g.__rc[0]),g.__rc[1]&&(h.has(g.__rc[1])||(h.add(g.__rc[1]),d("BanzaiLazyQueue").queuePost("require_cond_exposure_logging",{identifier:g.__rc[1]})));else if(g.__e)e[f]=c("ge")(g.__e);else if(g.__rel)e[f]=a.relativeTo;else if(g.__bigPipeContext)e[f]=a.bigPipeContext;else if(g.__bbox)e[f]=g.__bbox;else{for(j in g)i(a,g,j);if(g.__map)e[f]=new Map(g.__map);else if(g.__set)e[f]=new Set(g.__set);else if(g.__imm){j=g.__imm;a=j.method;g=j.value;e[f]=b.call(null,"immutable")[a](g)}}}g["default"]=i}),98);
__d("ServerJSDefine",["BitMap","replaceTransportMarkers"],(function(a,b,c,d,e,f,g){var h=2,i=8,j=new(c("BitMap"))(),k={getLoadedModuleHash:function(){return j.toCompressedString()},getModuleNameAndHash:function(a){a=a.split("@");return{hash:a[1],name:a[0]}},handleDefine:function(a,b,d,e,g){e>=0&&j.set(e),define(a,b,function(h,i,j,k,b){h={data:d};c("replaceTransportMarkers")({relativeTo:g},h);if(e===-42){i=d!=null&&typeof d==="object"&&d.__throw8367__;throw new Error(a+": "+(typeof i==="string"?i:""))}b.exports=h.data},h|i)},handleDefines:function(a,b){a.forEach(function(a){var c;b!=null?c=[].concat(a,[b]):c=[].concat(a,[null]);k.handleDefine.apply(null,c)})}};a=k;g["default"]=a}),98);
__d("StaticSiteData",[],(function(a,b,c,d,e,f){a=Object.freeze({hs_key:"__hs",connection_class_server_guess_key:"__ccg",dpr_key:"dpr",spin_rev_key:"__spin_r",spin_time_key:"__spin_t",spin_branch_key:"__spin_b",spin_mhenv_key:"__spin_dev_mhenv",lite_iframe_locale_override_key:"__ltif_locale",weblite_key:"__wblt",weblite_iframe_key:"__wbltif",force_touch_key:"__fmt",kite_key:"__ktif",kite_legacy_key:"_ktif",haste_session_id_key:"__hsi",jsmod_key:"__dyn",comet_key:"__comet_req",canonical_route_key:"__crn"});f["default"]=a}),66);
/**
 * License: https://www.facebook.com/legal/license/fEQRQOuGUZF/
 */
__d("Alea",[],(function(a,b,c,d,e,f){function g(){var a=**********,b=function(b){b=b.toString();for(var c=0;c<b.length;c++){a+=b.charCodeAt(c);var d=.02519603282416938*a;a=d>>>0;d-=a;d*=a;a=d>>>0;d-=a;a+=d***********}return(a>>>0)*23283064365386963e-26};b.version="Mash 0.9";return b}function a(){var a=0,b=0,c=0,d=1;for(var e=arguments.length,f=new Array(e),h=0;h<e;h++)f[h]=arguments[h];var i=f.length>0?f:[new Date()],j=g();a=j(" ");b=j(" ");c=j(" ");for(var k=0;k<i.length;k++)a-=j(i[k]),a<0&&(a+=1),b-=j(i[k]),b<0&&(b+=1),c-=j(i[k]),c<0&&(c+=1);j=null;var l=function(){var e=2091639*a+d*23283064365386963e-26;a=b;b=c;c=e-(d=e|0);return c};l.version="Alea 0.9";l.args=i;return l}f["default"]=a}),66);
__d("Random",["Alea","ServerNonce"],(function(a,b,c,d,e,f){"use strict";var g=**********,h=b("ServerNonce").ServerNonce,i;function j(){i==null&&(i=b("Alea")(h));return i}var k={random:function(){var b=typeof Uint32Array==="function"?new Uint32Array(1):null,c=a.crypto||a.msCrypto;if(b!=null)try{var d=c==null?void 0:c.getRandomValues;if(typeof d==="function"){var e=d.bind(c);return function(){try{e(b)}catch(a){return j()()}return b[0]/g}}}catch(a){}return j()}(),uint32:function(){return Math.floor(k.random()*g)},intBetween:function(a,b){return Math.floor(k.random()*(b-a+1)+a)},coinflip:function(a){function b(b){return a.apply(this,arguments)}b.toString=function(){return a.toString()};return b}(function(a){if(a===0)return!1;return a<=1?!0:k.random()*a<=1})};e.exports=k}),null);
__d("WebSessionDefaultTimeoutMs",[],(function(a,b,c,d,e,f){"use strict";a=35e3;f["default"]=a}),66);
__d("CookieConsent",["InitialCookieConsent"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Set((h||(h=c("InitialCookieConsent"))).initialConsent),j=h.shouldShowCookieBanner,k={setConsented:function(){i.add(1),j=!1},shouldShowCookieBanner:function(){return j},shouldWaitForDeferredDatrCookie:function(){return(h||(h=c("InitialCookieConsent"))).shouldWaitForDeferredDatrCookie},isFirstPartyStorageAllowed:function(){return!(h||(h=c("InitialCookieConsent"))).noCookies&&k.hasFirstPartyConsent()},hasFirstPartyConsent:function(){return i.has(1)},hasIndividualThirdPartyIntegrationConsent:function(a){var b=k.hasThirdPartyConsent([a]);return(b=b.get(a))!=null?b:!1},hasThirdPartyConsent:function(a){var b=new Map();if(!(h||(h=c("InitialCookieConsent"))).hasGranularThirdPartyCookieConsent){var d=i.has(2);for(var e of a)(h||(h=c("InitialCookieConsent"))).exemptedIntegrations.includes(e)?b.set(e,!0):b.set(e,d);return b}for(e of a)(h||(h=c("InitialCookieConsent"))).optedInIntegrations.includes(e)?b.set(e,!0):b.set(e,!1);return b},isThirdPartyIntegrationEmbedAllowed:function(a){return!(h||(h=c("InitialCookieConsent"))).hasGranularThirdPartyCookieConsent?!(h||(h=c("InitialCookieConsent"))).exemptedIntegrations.includes(a)?!(h||(h=c("InitialCookieConsent"))).noCookies&&i.has(2):!(h||(h=c("InitialCookieConsent"))).noCookies:!(h||(h=c("InitialCookieConsent"))).noCookies&&(h||(h=c("InitialCookieConsent"))).optedInIntegrations.includes(a)}};a=k;g["default"]=a}),98);
__d("isQuotaExceededError",[],(function(a,b,c,d,e,f){"use strict";function g(b){return Boolean(b instanceof a.DOMException&&(b.code===22||b.code===1014||b.name==="QuotaExceededError"||b.name==="NS_ERROR_DOM_QUOTA_REACHED"))}function b(a,b){return Boolean(g(b)&&a&&a.length!==0)}f.isQuotaExceededError=g;f.isStorageQuotaExceededError=b}),66);
__d("WebStorage",["CookieConsent","FBLogger","err","isQuotaExceededError"],(function(a,b,c,d,e,f,g){"use strict";var h,i={},j={},k="localStorage",l="sessionStorage",m=!1,n=typeof window!=="undefined"?window:self;function o(a,b,d){if(!(h||(h=c("CookieConsent"))).isFirstPartyStorageAllowed()){m||(c("FBLogger")("web_storage").warn("Failed to get %s because of missing cookie consent",d.toString()),m=!0);return null}Object.prototype.hasOwnProperty.call(a,d)||(a[d]=b(d));return a[d]}function p(a){try{return n[a]}catch(a){c("FBLogger")("web_storage").warn("Failed to get storage for read %s",a.message)}return null}function q(a){var b=null;try{b=n[a];if(b!=null&&typeof b.setItem==="function"&&typeof b.removeItem==="function"){var e="__test__"+Date.now();b.setItem(e,"");b.removeItem(e)}else return null}catch(e){if(d("isQuotaExceededError").isStorageQuotaExceededError(b,e)===!1){c("FBLogger")("web_storage").catching(e).warn("Failed to get WebStorage of type `%s`",a);return null}}return b}function r(a){var b=null;try{b=n[a];if(b!=null&&typeof b.setItem==="function"&&typeof b.removeItem==="function"){a="__test__"+Date.now();b.setItem(a,"");b.removeItem(a)}}catch(a){if(d("isQuotaExceededError").isStorageQuotaExceededError(b,a)===!0)return!0}return!1}function s(a){var b=[];for(var c=0;c<a.length;c++)b.push(a.key(c)||"");return b}function t(a,b,d){if(a==null)return new Error("storage cannot be null");var e=null;try{a.setItem(b,d)}catch(g){var f=s(a).map(function(b){var c=(a.getItem(b)||"").length;return b+"("+c+")"});e=c("err")("%sStorage quota exceeded while setting %s(%s). Items(length) follows: %s",g.name?g.name+": ":"",b,d.length,f.join())}return e}a={getLocalStorage:function(){return o(i,q,k)},getAllowlistedKeyFromLocalStorage:function(a){var b;return(b=o(j,p,k))==null?void 0:b.getItem(a)},getSessionStorage:function(){return o(i,q,l)},getAllowlistedKeyFromSessionStorage:function(a){var b;return(b=o(j,p,l))==null?void 0:b.getItem(a)},getLocalStorageForRead:function(){return o(j,p,k)},getSessionStorageForRead:function(){return o(j,p,l)},isLocalStorageQuotaExceeded:function(){return r(k)},isSessionStorageQuotaExceeded:function(){return r(l)},setItemGuarded:t,setAllowlistedKeyToLocalStorage:function(a,b,c){return t(a,b,c)},clearCaches:function(){i={},j={}}};b=a;g["default"]=b}),98);
__d("WebSession",["FBLogger","Random","WebSessionDefaultTimeoutMs","WebStorage"],(function(a,b,c,d,e,f,g){"use strict";var h,i=36,j=6,k=Math.pow(i,j);function l(a){return a==null||Number.isFinite(a)===!1||a<=0?null:a}function m(a){if(a==null)return null;var b=parseInt(a,10);if(""+b!==a){c("FBLogger")("web_session").warn("Expected the web session expiry time to parse as an integer. Found `%s`.",String(a));return null}return l(b)}function n(a){if(a==null)return null;if(a.length!==j){c("FBLogger")("web_session").warn("Expected the web session id to be a %d character string. It was %d character(s). Received `%s`.",j,a.length,a);return null}if(/^[a-z0-9]+$/.test(a)===!1){c("FBLogger")("web_session").warn("Expected the web session ID to be a base-%d encoded string. Received `%s`.",i,a);return null}return a}function o(a){if(a==null)return null;if(typeof a!=="string"&&a instanceof String===!1){c("FBLogger")("web_session").warn("A non-string value was passed to `coerceSession`. This should be impossible according to this method's Flow type. The value was `%s`.",a);return null}a=a.split(":");var b=a[0];a=a[1];a=m(a);b=n(b);return a==null||b==null?null:{expiryTime:a,id:b}}function p(){var a=Math.floor(d("Random").random()*k);a=a.toString(i);return"0".repeat(j-a.length)+a}var q=null;function r(){q==null&&(q=p());return q}function s(a){a===void 0&&(a=Date.now());var b=(h||(h=c("WebStorage"))).getLocalStorageForRead();if(b==null)return null;try{b=o(b.getItem("Session"));return b&&a<b.expiryTime?b:null}catch(a){return null}}function t(){var a=(h||(h=c("WebStorage"))).getSessionStorageForRead();if(a==null)return null;a=n(a.getItem("TabId"));if(a==null){var b=(h||(h=c("WebStorage"))).getSessionStorage();if(b==null)return null;var d=p();h.setItemGuarded(b,"TabId",d);return d}return a}function a(a){if(a!==void 0&&l(a)==null){c("FBLogger")("web_session").warn("`WebSession.extend()` was passed an invalid target expiry time `%s`.",a);return}var b=Date.now();a=a!=null?a:b+c("WebSessionDefaultTimeoutMs");var d=s(b);if(d&&d.expiryTime>=a||a<=b)return;b=(h||(h=c("WebStorage"))).getLocalStorage();if(b!=null){d=d==null?p():d.id;(h||(h=c("WebStorage"))).setItemGuarded(b,"Session",d+":"+a)}}function u(){var a;return(a=s())==null?void 0:a.id}function b(){var a,b,c=r();a=(a=u())!=null?a:"";b=(b=t())!=null?b:"";return a+":"+b+":"+c}function e(){return r()}g.getTabId=t;g.extend=a;g.getSessionId=u;g.getId=b;g.getPageId_DO_NOT_USE=e}),98);
__d("asyncParams",[],(function(a,b,c,d,e,f){var g={};function a(a,b){g[a]=b}function b(){return g}function c(a){delete g[a]}f.add=a;f.get=b;f.remove=c}),66);
__d("getAsyncParamsForProfiling",["SiteData"],(function(a,b,c,d,e,f,g){"use strict";var h="__profiler_opts",i=30;b=300;function a(){var a;if(typeof URLSearchParams!=="function")return null;var b=new URLSearchParams(window.location.search),d=b.get(h);if(d==null)return null;var e={recursive:"0",t:i,traceid:c("SiteData").polytrace_id};d.split(";").forEach(function(a){a=a.split(/:|=/,2);var b=a[0];a=a[1];b=b.toLowerCase();switch(b){case"t":e.t=Math.min(parseInt(a,10)||i,300);break;case"recursive":e.recursive=a==="1"?"1":"0";break;case"uid":case"filter":case"traceid":a&&(e[b]=a);break}});if(e.recursive!=="1"||window.performance.now()>(e.t||0)*1e3||e.traceid==null)return null;d=Object.entries(e).map(function(a){return a.join(":")}).sort().join(";");var f=(a={},a[h]=d,a);b.forEach(function(a,b){b.endsWith("_sample")&&(f[b]=a)});return f}g.defaultTimeSpan=i;g.maxTimeSpan=b;g.getAsyncParamsForProfiling=a}),98);
__d("getAsyncParamsFromCurrentPageURI",[],(function(a,b,c,d,e,f){"use strict";var g={locale:!0,cxobfus:!0,js_debug:!0,cquick:!0,cquick_token:!0,wdplevel:!0,prod_graphql:!0,sri:!0},h={ctarget:!0,hl:!0,gk_enable:!0,gk_disable:!0,__pwa:!0};function a(){var a={};window.location.search.slice(1).split("&").forEach(function(b){b=b.split("=");var c=b[0];b=b[1];(c.substr(0,4)==="tfc_"||c.substr(0,4)==="tfi_"||c.substr(0,3)==="mh_"||g[c]>-1||h[c]>-1)&&(h[c]>-1?a[c]=decodeURIComponent(b):a[c]=b)});return a}f["default"]=a}),66);
__d("CSSCore",["invariant"],(function(a,b,c,d,e,f,g,h){function i(a,b){var c=a;while(c.parentNode)c=c.parentNode;if(c instanceof Element){c=c.querySelectorAll(b);return Array.prototype.indexOf.call(c,a)!==-1}return!1}function j(a,b){/\s/.test(b)&&h(0,11794,b);b&&(a.classList?a.classList.add(b):l(a,b)||(a.className=a.className+" "+b));return a}function k(a,b){/\s/.test(b)&&h(0,11795,b);b&&(a.classList?a.classList.remove(b):l(a,b)&&(a.className=a.className.replace(new RegExp("(^|\\s)"+b+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")));return a}function a(a,b,c){return(c?j:k)(a,b)}function l(a,b){/\s/.test(b)&&h(0,442);return a.classList?!!b&&a.classList.contains(b):(" "+a.className+" ").indexOf(" "+b+" ")>-1}function b(a,b){var c=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.msMatchesSelector||function(b){return i(a,b)};return c.call(a,b)}g.addClass=j;g.removeClass=k;g.conditionClass=a;g.hasClass=l;g.matchesSelector=b}),98);
__d("isSocialPlugin",["CSSCore","ExecutionEnvironment"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(){return!(h||(h=c("ExecutionEnvironment"))).canUseDOM?!1:!!document.body&&d("CSSCore").hasClass(document.body,"plugin")}g["default"]=a}),98);
__d("uniqueRequestID",[],(function(a,b,c,d,e,f){var g=36,h=1;function a(){return(h++).toString(g)}f["default"]=a}),66);
__d("getAsyncParams",["CometPersistQueryParams","CurrentCanonicalRoute","CurrentUserInitialData","DTSGUtils","Env","GetAsyncParamsExtraData","HasteBitMap","HasteBitMapName","JSErrorLoggingConfig","LSD","ServerJSDefine","SiteData","SprinkleConfig","StaticSiteData","WebConnectionClassServerGuess","WebSession","asyncParams","cr:8959","cr:8960","getAsyncParamsForProfiling","getAsyncParamsFromCurrentPageURI","isSocialPlugin","objectValues","requireWeak","uniqueRequestID"],(function(a,b,c,d,e,f,g){var h,i;function a(a,e){var f;e===void 0&&(e=!1);f=(f=c("GetAsyncParamsExtraData").extra_data)!=null?f:{};var g=babelHelpers["extends"]({},d("asyncParams").get(),{},f,(f={__user:(h||(h=c("CurrentUserInitialData"))).USER_ID,__a:1,__req:c("uniqueRequestID")()},f[c("StaticSiteData").hs_key]=c("SiteData").haste_session,f[c("StaticSiteData").dpr_key]=c("SiteData").pr,f[c("StaticSiteData").connection_class_server_guess_key]=c("WebConnectionClassServerGuess").connectionClass,f.__rev=c("SiteData").client_revision,f.__s=d("WebSession").getId(),f[c("StaticSiteData").haste_session_id_key]=c("SiteData").hsi,f));e||(g[c("StaticSiteData").jsmod_key]=c("ServerJSDefine").getLoadedModuleHash(),c("objectValues")(c("HasteBitMapName")).forEach(function(a){var b=d("HasteBitMap").toCompressedString(a);b!==""&&(g[a]=b)}));if(!c("SiteData").wbloks_env&&c("SiteData").comet_env!=null&&c("SiteData").comet_env!==0){g[c("StaticSiteData").comet_key]=(f=c("SiteData").comet_env)!=null?f:1}Object.entries(c("CometPersistQueryParams").relative).forEach(function(a){var b=a[0];a=a[1];a!=null&&(g[b]=String(a))});typeof window!=="undefined"&&((e=window)==null?void 0:e.location)!=null&&(Object.assign(g,c("getAsyncParamsFromCurrentPageURI")()),Object.assign(g,d("getAsyncParamsForProfiling").getAsyncParamsForProfiling()));(i||(i=c("Env"))).isCQuick&&!g.cquick&&(g.cquick=(i||(i=c("Env"))).iframeKey,g.ctarget=i.iframeTarget,g.cquick_token=i.iframeToken);if(a=="POST"){f=b("cr:8959").getCachedToken?b("cr:8959").getCachedToken():b("cr:8959").getToken();f&&(g.fb_dtsg=f,c("SprinkleConfig").param_name&&(g[c("SprinkleConfig").param_name]=c("DTSGUtils").getNumericValue(f)));c("LSD").token&&(g.lsd=c("LSD").token,c("SprinkleConfig").param_name&&!f&&(g[c("SprinkleConfig").param_name]=c("DTSGUtils").getNumericValue(c("LSD").token)))}if(a=="GET"){e=b("cr:8960").getCachedToken?b("cr:8960").getCachedToken():b("cr:8960").getToken();e&&(g.fb_dtsg_ag=e,c("SprinkleConfig").param_name&&(g[c("SprinkleConfig").param_name]=c("DTSGUtils").getNumericValue(e)))}c("isSocialPlugin")()&&(g.__sp=1);if(c("SiteData").spin){g[(f=c("StaticSiteData")).spin_rev_key]=c("SiteData")[f.spin_rev_key];g[f.spin_branch_key]=c("SiteData")[f.spin_branch_key];g[f.spin_time_key]=c("SiteData")[f.spin_time_key];c("SiteData")[c("StaticSiteData").spin_mhenv_key]&&(g[c("StaticSiteData").spin_mhenv_key]=c("SiteData")[c("StaticSiteData").spin_mhenv_key])}d("JSErrorLoggingConfig").sampleWeight!=null&&d("JSErrorLoggingConfig").sampleWeightKey!=null&&(g[d("JSErrorLoggingConfig").sampleWeightKey]=d("JSErrorLoggingConfig").sampleWeight);a=d("CurrentCanonicalRoute").getForGlobalLoggingOnly();a!=null&&(g[c("StaticSiteData").canonical_route_key]=a);c("requireWeak")("QPLUserFlow",function(a){a=a.getActiveFlowIDs();a.length>0&&(g.qpl_active_flow_ids=a.sort().join(","))});c("requireWeak")("MessengerPWAVersionForUserAgent",function(a){a=a();a!=null&&(g.__pwa=a)});return g}g["default"]=a}),98);
__d("setImmediatePolyfill",["invariant","ImmediateImplementation","PromiseUsePolyfillSetImmediateGK"],(function(a,b,c,d,e,f,g){var h=a.setImmediate;if(b("PromiseUsePolyfillSetImmediateGK").www_always_use_polyfill_setimmediate||!h){d=b("ImmediateImplementation");h=d.setImmediate}function c(a){typeof a==="function"||g(0,5912);for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];return h.apply(void 0,[a].concat(c))}e.exports=c}),null);
__d("setImmediateAcrossTransitions",["TimeSlice","setImmediatePolyfill"],(function(a,b,c,d,e,f,g){function a(a){var b=c("TimeSlice").guard(a,"setImmediate",{propagationType:c("TimeSlice").PropagationType.CONTINUATION,registerCallStack:!0});for(var d=arguments.length,e=new Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];return c("setImmediatePolyfill").apply(void 0,[b].concat(e))}g["default"]=a}),98);
__d("BootloaderEndpoint",["Bootloader","BootloaderConfig","BootloaderEndpointConfig","BootloaderRetryTracker","CSRFGuard","FBLogger","HasteResponse","TimeSlice","clearImmediate","fb-error","getAsyncParams","getSameOriginTransport","gkx","performanceAbsoluteNow","setImmediateAcrossTransitions"],(function(a,b,c,d,e,f){"use strict";var g,h=b("fb-error").ErrorXFBDebug,i=b("BootloaderEndpointConfig").endpointURI,j=0,k=null,l=null,m=new Map(),n=new Map(),o=new(b("BootloaderRetryTracker"))({retries:b("BootloaderConfig").jsRetries,abortNum:b("BootloaderConfig").jsRetryAbortNum,abortTime:b("BootloaderConfig").jsRetryAbortTime,abortCallback:function(){b("FBLogger")("bootloader","js_retry_abort").info("BL endpoint JS retry abort")}});function p(a,b,c){return{requestStart:a,responseStart:b,serverGenTime:0,uri:c,source:"bootloader_endpoint",sourceDetail:"Invalid response",isFirstIdentical:!1,timesliceContext:null,startTime:a,logTime:b,jsmodsStart:b,jsmodsEnd:b,rsrcs:{blocking:new Map(),nonblocking:new Map(),"default":new Map()},payloadStats:{hsdp:{entry:0,dup_entry:0},hblp:{rsrc:0,dup_rsrc:0,comp:0,dup_comp:0},sjsp:{define:0,dup_user_define:0,dup_system_define:0,require:0}}}}function q(a){return Array.from(a.keys()).join(",")}function a(a,c){var d={};a.size&&(d.modules=q(a));c.size&&(d.nb_modules=q(c));a=Object.entries(babelHelpers["extends"]({},d,{},b("getAsyncParams")("GET"))).map(function(a){var b=a[0];a=a[1];return encodeURIComponent(b)+"="+encodeURIComponent(String(a))}).join("&");return i+(i.includes("?")?"&":"?")+a}function r(a,c){if(a.size===0&&c.size===0)return;var d=v._getURL(a,c),e=b("getSameOriginTransport")(),f=j++,i="bootloader_endpoint-"+q(a)+"-"+q(c),k=(g||(g=b("performanceAbsoluteNow")))();function l(d){if(!b("BootloaderEndpointConfig").retryEnabled)return d();o.maybeScheduleRetry(i,function(){r(a,c)},function(){d()})}e.open("GET",d,!0);var m=b("TimeSlice").getGuardedContinuation("Bootloader _requestHastePayload");e.onreadystatechange=function(){if(e.readyState!==4)return;m(function(){h.addFromXHR(e);var j=e.status===200?JSON.parse(b("CSRFGuard").clean(e.responseText)):null;if(j==null)return l(function(){if(b("gkx")("5163")){var h=(g||(g=b("performanceAbsoluteNow")))();for(var i of[a,c])for(var j of i.entries()){var l=j[0],m=j[1];b("Bootloader").done(m);b("Bootloader").beDone(l,f,p(k,h,d))}}b("FBLogger")("bootloader").warn('Invalid bootloader response %s, blocking mods: %s; non-blocking mods: %s; "%s"',e.status,q(a),q(c),e.responseText.substr(0,256))});if(j.error)b("FBLogger")("bootloader").warn("Non-fatal error from bootloader endpoint, blocking mods: %s; non-blocking mods: %s",q(a),q(c));else if(j.__error)return l(function(){if(b("gkx")("5163")){var e=(g||(g=b("performanceAbsoluteNow")))();for(var h of[a,c])for(var i of h.entries()){var j=i[0],l=i[1];b("Bootloader").done(l);b("Bootloader").beDone(j,f,p(k,e,d))}}b("FBLogger")("bootloader").warn("Fatal error from bootloader endpoint, blocking mods: %s; non-blocking mods: %s",q(a),q(c))});o.getNumRetriesForSource(i)>0&&b("FBLogger")("bootloader").warn("Bootloader endpoint retry succeeded, blocking mods: %s; non-blocking mods: %s",q(a),q(c));b("TimeSlice").guard(function(){return s(d,j,a,c,f,k)},"Bootloader receiveEndpointData",{propagationType:b("TimeSlice").PropagationType.CONTINUATION})()})};e.send()}function s(a,c,d,e,f,h){var i=(g||(g=b("performanceAbsoluteNow")))(),j=c.serverGenTime,k=c.hrp;if(k==null){c=c;b("FBLogger")("be_null_hrp").mustfix("Found null hrp, blocking mods: %s; non-blocking mods: %s; response error: %s",q(d),q(e),c.error+", summary: "+c.errorSummary+", description: "+c.errorDescription);k=c}b("HasteResponse").handle(k,{source:"bootloader_endpoint",sourceDetail:JSON.stringify({b:Array.from(d.keys()),n:Array.from(e.keys())}),onBlocking:function(){for(var a of[d,e])for(var c of a.values())b("Bootloader").done(c)},onLog:function(c){for(var g of[d,e])for(var k of g.keys())b("Bootloader").beDone(k,f,babelHelpers["extends"]({requestStart:h,responseStart:i,serverGenTime:j,uri:a},c))}})}function t(){var a=m,c=n;b("clearImmediate")(l);l=null;k=null;m=new Map();n=new Map();r(a,c)}function u(){var a=b("BootloaderEndpointConfig").maxBatchSize;return a<=0?!1:m.size+n.size>=a}var v={load:function(a,c,d){(c?m:n).set(a,d);if(b("BootloaderEndpointConfig").debugNoBatching||u()){t();return}if(l!=null)return;k=b("TimeSlice").getGuardedContinuation("Schedule async batch request: Bootloader._loadResources");l=b("setImmediateAcrossTransitions")(function(){k&&k(function(){return t()})})},forceFlush:function(){k&&k(function(){return t()})},_getURL:a,_sendRequest:r};e.exports=v}),null);
__d("MetaConfigMap",[],(function(a,b,c,d,e,f){"use strict";var g={};a={add:function(a,b){for(var c in a)b&&b.entry++,!(c in g)?g[c]=a[c]:b&&b.dup_entry++},get:function(a){return g[a]}};b=a;f["default"]=b}),66);
__d("QPLHasteSupportDataStorage",[],(function(a,b,c,d,e,f){"use strict";var g={};a={add:function(a,b){Object.keys(a).forEach(function(c){b&&b.entry++;if(g[c]==null){var d=a[c];g[c]=d}else b&&b.dup_entry++})},get:function(a){return g[a]}};f["default"]=a}),66);
__d("bx",["unrecoverableViolation"],(function(a,b,c,d,e,f,g){var h={};function a(a){var b=h[a];if(!b)throw c("unrecoverableViolation")("bx"+('(...): Unknown file path "'+a+'"'),"staticresources");return b}a.add=function(a,b){var c=!1;for(c in a)b&&b.entry++,!(c in h)?(a[c].loggingID=c,h[c]=a[c]):b&&b.dup_entry++};a.getURL=function(a){return a.uri};g["default"]=a}),98);
__d("recoverableViolation",["FBLogger"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,d,e){d=d===void 0?{}:d;d=d.error;b=c("FBLogger")(b);d?b=b.catching(d):b=b.blameToPreviousFrame();d=e==null?void 0:e.categoryKey;d!=null&&(b=b.addToCategoryKey(d));e=(d=e==null?void 0:e.trackOnly)!=null?d:!1;e?b.debug(a):b.mustfix(a);return null}g["default"]=a}),98);
__d("getFalcoLogPolicy_DO_NOT_USE",["recoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h={r:1},i={};function a(a){var b=i[a];if(b==null){c("recoverableViolation")("Failed to find a Haste-supplied log policy for the Falco event ' +\n        'identified by token `"+a+"`. Failing open (ie. with a sampling rate of 1.0).","staticresources");return h}return b}a.add=function(a,b){Object.keys(a).forEach(function(c){b&&b.entry++,i[c]==null?i[c]=a[c]:b&&b.dup_entry++})};g["default"]=a}),98);
__d("ix",["invariant"],(function(a,b,c,d,e,f,g,h){var i={},j=new Set();function b(a){var b=i[a];!b&&h(0,11798,a);return b}b.add=function(a,b){var c=!1;for(c in a)b&&b.entry++,!(c in i)?(a[c].loggingID=c,i[c]=a[c]):b&&b.dup_entry++};b.getUsedPaths_ONLY_FOR_REACT_FLIGHT=function(){a.__flight_execution_mode_DO_NOT_USE==="flight"||h(0,34547);return Array.from(j)};b.getAllPaths=function(){var a=new Set();Object.values(i).map(function(a){if((a==null?void 0:a.sprited)===0)return a.uri;else if((a==null?void 0:a.sprited)===1)return a._spi;else if((a==null?void 0:a.sprited)===2)return a.spi}).forEach(function(b){return b!=null&&a.add(b)});return a};g["default"]=b}),98);
__d("qex",["invariant","BanzaiLazyQueue"],(function(a,b,c,d,e,f,g,h){"use strict";var i={},j={};a={_:function(a){var b=i[a];b!=null||h(0,11799,a);var c=b.r;b=b.l;b!=null&&!j[a]&&(j[a]=!0,d("BanzaiLazyQueue").queuePost("qex",{l:b}));return c},add:function(a,b){for(var c in a)b&&b.entry++,!(c in i)?i[c]=a[c]:b&&b.dup_entry++}};b=a;g["default"]=b}),98);
__d("HasteSupportData",["ix","MetaConfigMap","QPLHasteSupportDataStorage","bx","getFalcoLogPolicy_DO_NOT_USE","gkx","justknobx","qex"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a,b){var d=a.bxData,e=a.clpData,f=a.gkxData,g=a.ixData,i=a.metaconfigData,j=a.qexData,k=a.qplData;a=a.justknobxData;d!=null&&c("bx").add(d,b);e!=null&&c("getFalcoLogPolicy_DO_NOT_USE").add(e,b);f!=null&&c("gkx").add(f,b);g!=null&&h.add(g,b);i!=null&&c("MetaConfigMap").add(i,b);j!=null&&c("qex").add(j,b);k!=null&&c("QPLHasteSupportDataStorage").add(k,b);a!=null&&c("justknobx").add(a,b)}g.handle=a}),98);
__d("Parent",["CSSCore"],(function(a,b,c,d,e,f,g){function a(a,b){b=b.toUpperCase();a=i(a,function(a){return a.nodeName===b});return a instanceof Element?a:null}function b(a,b){a=i(a,function(a){return a instanceof Element&&d("CSSCore").hasClass(a,b)});return a instanceof Element?a:null}function c(a,b){a=a;if(typeof a.matches==="function"){while(a&&a!==document&&!a.matches(b))a=a.parentNode;return a instanceof Element?a:null}else if(typeof a.msMatchesSelector==="function"){while(a&&a!==document&&!a.msMatchesSelector(b))a=a.parentNode;return a instanceof Element?a:null}else return h(a,b)}function h(a,b){a=a;var c=a;while(c.parentNode)c=c.parentNode;if(!(c instanceof Element)&&!(c instanceof Document))return null;c=c.querySelectorAll(b);while(a){if(Array.prototype.indexOf.call(c,a)!==-1)return a instanceof Element?a:null;a=a.parentNode}return a instanceof Element?a:null}function e(a,b){a=i(a,function(a){return a instanceof Element&&!!a.getAttribute(b)});return a instanceof Element?a:null}function i(a,b){a=a;while(a){if(b(a))return a;a=a.parentNode}return null}g.byTag=a;g.byClass=b;g.bySelector=c;g.bySelector_SLOW=h;g.byAttribute=e;g.find=i}),98);
__d("ContextualComponent",["Parent"],(function(a,b,c,d,e,f,g){a=function(){a.forNode=function(b){return a.$1.get(b)||null};a.closestToNode=function(b){b=d("Parent").find(b,function(b){return!!a.forNode(b)});return b?a.forNode(b):null};a.register=function(b){return new a(b)};function a(a){var b=a.element,c=a.isRoot;a=a.parent;this.$2=c;this.$3=b;this.$4=a;this.$5=new Set();this.$6=[];this.$7=[];this.$8()}var b=a.prototype;b.onCleanup=function(a){this.$6.push(a)};b.onUnmount=function(a){this.$7.push(a)};b.cleanup=function(){this.$5.forEach(function(a){return a.cleanup()}),this.$6.forEach(function(a){return a()}),this.$6=[]};b.unmount=function(){this.cleanup();this.$5.forEach(function(a){return a.unmount()});this.$7.forEach(function(a){return a()});this.$7=[];var b=this.$4;b&&(a.$1["delete"](this.$3),b.$9(this))};b.reinitialize=function(){var b=this.$4;b&&(b.$9(this),this.$4=void 0);a.$1["delete"](this.$3);this.$8()};b.$8=function(){if(!this.$2&&!this.$4){var b=a.closestToNode(this.$3);b&&(this.$4=b)}this.$4&&this.$4.$10(this);a.$1.set(this.$3,this)};b.$10=function(a){this.$5.add(a)};b.$9=function(a){this.$5["delete"](a)};return a}();a.$1=new Map();g["default"]=a}),98);
__d("ServerJS",["ContextualComponent","ErrorGuard","ServerJSDefine","__debug","err","ge","replaceTransportMarkers"],(function(a,b,c,d,e,f){var g,h=1,i=2,j=16,k=0;a=function(){"use strict";function a(){this.$2={},this.$1=null,this.$4={},this.$3=void 0}var c=a.prototype;c.handle=function(a,b){return this.$5(a,b,m)};c.handleWithCustomApplyEach=function(a,b,c){this.$5(b,c,a)};c.$5=function(a,c,d){this.$3=c;if(a.__guard!=null)throw b("err")("ServerJS.handle called on data that has already been handled");a.__guard=!0;d(a.define||[],this.$6,this);d(a.markup||[],this.$7,this);d(a.elements||[],this.$8,this);this.$9(a.contexts||[]);d(a.instances||[],this.$10,this);var e=d(a.pre_display_requires||[],this.$11,this);e=e.concat(d(a.require||[],this.$11,this));return{cancel:function(){e.forEach(function(a){a&&a.cancel()})}}};c.handlePartial=function(a,b){var c=this;(a.instances||[]).forEach(function(a){p(c.$2,a)});(a.markup||[]).forEach(function(a){o(c.$2,a)});(a.elements||[]).forEach(function(a){o(c.$2,a)});return this.handle(a,b)};c.setRelativeTo=function(a){this.$1=a;return this};c.cleanup=function(a){var c=Object.keys(this.$2);a?d.call(null,c,a.guard(function(){},"SeverJS Cleanup requireLazy",{propagationType:a.PropagationType.ORPHAN})):d.call(null,c,function(){});this.$2={};function f(c){var d=this.$4[c],a=d[0],f=d[1];d=d[2];delete this.$4[c];f=f?'JS::call("'+a+'", "'+f+'", ...)':'JS::requireModule("'+a+'")';a=b("__debug").debugUnresolvedDependencies([a,c]);throw l(b("err")("%s did not fire because it has missing dependencies.\n%s",f,a),d)}for(a in this.$4)(g||(g=b("ErrorGuard"))).applyWithGuard(f,this,[a],{name:"ServerJS:cleanup id: "+a,project:"ServerJSCleanup"})};c.$6=function(a,c,d,e){return(g||(g=b("ErrorGuard"))).applyWithGuard(b("ServerJSDefine").handleDefine,b("ServerJSDefine"),[a,c,d,e,this.$1],{name:"JS::define"})};c.$11=function(a,c,d,e){return(g||(g=b("ErrorGuard"))).applyWithGuard(this.$12,this,[a,c,d,e],{name:c!=null?"JS::call":"JS::requireModule"})};c.$12=function(a,c,d,e){var f=this;a=b("ServerJSDefine").getModuleNameAndHash(a);var m=a.name,n=a.hash,o;typeof c==="object"?a=c:(a=d,o=c);d=[m].concat(a||[]);var p;o!=null?p="__call__"+m+"."+o:e!=null?p="__call__"+m:p="__requireModule__"+m;p+="__"+k++;this.$4[p]=[m,o,n];var q=this.$3&&this.$3.bigPipeContext,r=(g||(g=b("ErrorGuard"))).guard(function(a){a=b.call(null,m);delete f.$4[p];e&&b("replaceTransportMarkers")({relativeTo:f.$1,bigPipeContext:q},e);if(o!=null){if(!a[o])throw l(b("err")('Module %s has no method "%s"',m,o),n)}else if(e!=null&&typeof a!=="function")throw l(b("err")("Module %s is not a function but was called with args",m),n);var c=o!=null?a[o]:e!=null&&typeof a==="function"?a:null;c!=null&&(c.apply(a,e||[]),r.__SMmeta=c.__SMmeta||{},r.__SMmeta.module=r.__SMmeta.module||m,r.__SMmeta.name=r.__SMmeta.name||o)},{name:o!=null?"JS::call('"+m+"', '"+o+"', ...)":e!=null?"JS::call('"+m+"', ...)":"JS::requireModule('"+m+"')"});c=define(p,d,r,h|j|i,this,1,this.$3);return c};c.$10=function(a,c,d,e){(g||(g=b("ErrorGuard"))).applyWithGuard(this.$13,this,[a,c,d,e],{name:"JS::instance"})};c.$13=function(a,c,d,e){var f=this,g=null;a=b("ServerJSDefine").getModuleNameAndHash(a);var h=a.name;a=a.hash;if(c){var k=this.$3&&this.$3.bigPipeContext;g=function(){var a=b.call(null,c[0]);b("replaceTransportMarkers")({relativeTo:f.$1,bigPipeContext:k},d);var e=Object.create(a.prototype);a.apply(e,d);return e}}define(h,c,g,i|j,null,e)};c.$7=function(a,c,d,e){(g||(g=b("ErrorGuard"))).applyWithGuard(this.$14,this,[a,c,d,e],{name:"JS::markup"})};c.$14=function(a,c,d,e){a=b("ServerJSDefine").getModuleNameAndHash(a);var f=a.name,g=a.hash;define(f,[e],function(a){try{return a.replaceJSONWrapper(c).getRootNode()}catch(a){throw l(a,g)}},j,null,d)};c.$8=function(a,c,d,e){(g||(g=b("ErrorGuard"))).applyWithGuard(this.$15,this,[a,c,d,e],{name:"JS::element"})};c.$15=function(a,c,d,e){a=b("ServerJSDefine").getModuleNameAndHash(a);var f=a.name,g=a.hash;if(c===null&&d!=null){define(f,null,null,j,null,d);return}a=[];var i=j;d=d||0;e!=null&&(a.push(e),i|=h,d++);define(f,a,function(a){a=b("ge")(c,a);if(!a){var d="";throw l(b("err")('Could not find element "%s"%s',c,d),g)}return a},i,null,d)};c.$9=function(a){(g||(g=b("ErrorGuard"))).applyWithGuard(this.$16,this,[a],{name:"ContextualComponents"})};c.$16=function(a){var c=this,d=this.$3&&this.$3.bigPipeContext;a.map(function(a){b("replaceTransportMarkers")({relativeTo:c.$1,bigPipeContext:d},a);var e=a[0];return[a,n(e)]}).sort(function(a,b){return a[1]-b[1]}).forEach(function(a){a=a[0];var c=a[0];a=a[1];b("ContextualComponent").register({element:c,isRoot:a})})};return a}();function l(a,b){a.serverHash=b;return a}function m(a,b,c){return a.map(function(a){return b.apply(c,a)})}function n(a){var b=0;a=a;while(a)a=a.parentElement,b++;return b}function o(c,a){var d=b("ServerJSDefine").getModuleNameAndHash(a[0]);d=d.name;d in c||(a[2]=(a[2]||0)+1);c[d]=!0}function p(c,a){var d=b("ServerJSDefine").getModuleNameAndHash(a[0]);d=d.name;d in c||(a[3]=(a[3]||0)+1);c[d]=!0}e.exports=a}),null);
__d("HasteResponse",["Bootloader","BootloaderConfig","BootloaderEvents","ClientConsistencyEventEmitter","HasteSupportData","ServerJS","TimeSlice","__debug","fb-error","performanceAbsoluteNow"],(function(a,b,c,d,e,f){"use strict";var g,h=b("fb-error").getSimpleHash,i=new Set(),j={handleSRPayload:function(a,c){var d=a.hsdp;a=a.hblp;d&&b("HasteSupportData").handle(d,c==null?void 0:c.hsdp);a&&b("Bootloader").handlePayload(a,c==null?void 0:c.hblp);(a==null?void 0:a.consistency)!=null&&b("ClientConsistencyEventEmitter").emit("newEntry",a.consistency)},handle:function(a,c){var d=a.jsmods,e=a.allResources,f=a.hsrp;a=a.tieredResources;var k=c.source,l=c.sourceDetail,m=c.onBlocking,n=c.onLog;c=c.onAll;var o=(g||(g=b("performanceAbsoluteNow")))(),p;if(l==null)p=!0;else{var q=h(k,l);i.has(q)?p=!1:(p=!0,i.add(q))}var r={hsdp:{entry:0,dup_entry:0},hblp:{rsrc:0,dup_rsrc:0,comp:0,dup_comp:0},sjsp:{define:0,dup_user_define:0,dup_system_define:0,require:0}};f&&j.handleSRPayload(f,r);var s=0,t=0;q=function(){r.sjsp.require+=((d==null?void 0:d.require)||[]).length;r.sjsp.define+=((d==null?void 0:d.define)||[]).length;var a=b("__debug").getDupCount(),c=a[0];a=a[1];r.sjsp.dup_user_define-=c;r.sjsp.dup_system_define-=a;s=(g||(g=b("performanceAbsoluteNow")))();new(b("ServerJS"))().handle(d||{});t=g();var e=b("__debug").getDupCount();c=e[0];a=e[1];r.sjsp.dup_user_define+=c;r.sjsp.dup_system_define+=a;m==null?void 0:m()};f=function(a){a={source:k,sourceDetail:l,isFirstIdentical:p,timesliceContext:b("TimeSlice").getContext(),startTime:o,logTime:(g||(g=b("performanceAbsoluteNow")))(),jsmodsStart:s,jsmodsEnd:t,rsrcs:a,payloadStats:r};n==null?void 0:n(a);b("BootloaderEvents").notifyHasteResponse(a)};b("BootloaderConfig").tieredLoadingFromTier<=3&&a!=null?b("Bootloader").loadTieredResources(a,{onBlocking:q,onAll:c,onLog:f}):b("Bootloader").loadResources(e!=null?e:[],{onBlocking:q,onAll:c,onLog:f})}};e.exports=j}),null);
__d("DTSG",["invariant","DTSGInitialData"],(function(a,b,c,d,e,f,g,h){"use strict";var i=d("DTSGInitialData").token||null;function a(){return i}function b(a){i=a}function c(){h(0,5809)}function e(a){h(0,73819)}g.getToken=a;g.setToken=b;g.refresh=c;g.setTokenConfig=e}),98);
__d("DTSG_ASYNC",["DTSGInitData"],(function(a,b,c,d,e,f,g){"use strict";var h=c("DTSGInitData").async_get_token||null;function a(){return h}function b(a){h=a}g.getToken=a;g.setToken=b}),98);
__d("SchedulerFeatureFlags",["Env"],(function(a,b,c,d,e,f,g){var h;b=(a=(h||(h=c("Env")))==null?void 0:(h||c("Env")).react_scheduler_enable_paint)!=null?a:!1;d=!0;e=250;f=5e3;c=1e4;g.enableRequestPaint=b;g.enableSchedulerDebugging=d;g.userBlockingPriorityTimeout=e;g.normalPriorityTimeout=f;g.lowPriorityTimeout=c}),98);
__d("Scheduler-dev.classic",["SchedulerFeatureFlags"],(function(a,b,c,d,e,f){"use strict"}),null);
__d("Scheduler-profiling.classic",["SchedulerFeatureFlags"],(function(c,d,e,f,g,h){"use strict";"undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());var i=d("SchedulerFeatureFlags").enableRequestPaint;function j(c,d){var e=c.length;c.push(d);a:for(;0<e;){var f=e-1>>>1,g=c[f];if(0<m(g,d))c[f]=d,c[e]=g,e=f;else break a}}function k(c){return 0===c.length?null:c[0]}function l(c){if(0===c.length)return null;var d=c[0],e=c.pop();if(e!==d){c[0]=e;a:for(var f=0,g=c.length,h=g>>>1;f<h;){var i=2*(f+1)-1,j=c[i],k=i+1,l=c[k];if(0>m(j,e))k<g&&0>m(l,j)?(c[f]=l,c[k]=e,f=k):(c[f]=j,c[i]=e,f=i);else if(k<g&&0>m(l,e))c[f]=l,c[k]=e,f=k;else break a}}return d}function m(c,d){var e=c.sortIndex-d.sortIndex;return 0!==e?e:c.id-d.id}h.unstable_now=void 0;if("object"===typeof performance&&"function"===typeof performance.now){var n=performance;h.unstable_now=function(){return n.now()}}else{var o=Date,p=o.now();h.unstable_now=function(){return o.now()-p}}var q=[],r=[],s=1;c=null;var t=3,u=!1,v=!1,w=!1,x=!1,y="function"===typeof setTimeout?setTimeout:null,z="function"===typeof clearTimeout?clearTimeout:null,A="undefined"!==typeof setImmediate?setImmediate:null;function B(c){for(var d=k(r);null!==d;){if(null===d.callback)l(r);else if(d.startTime<=c)l(r),d.sortIndex=d.expirationTime,j(q,d);else break;d=k(r)}}function C(c){w=!1;B(c);if(!v)if(null!==k(q))v=!0,D||(D=!0,J());else{var d=k(r);null!==d&&L(C,d.startTime-c)}}var D=!1,E=-1,F=10,G=-1;function H(){return i&&x?!0:h.unstable_now()-G<F?!1:!0}function I(){i&&(x=!1);if(D){var d=h.unstable_now();G=d;var e=!0;try{a:{v=!1;w&&(w=!1,z(E),E=-1);u=!0;var f=t;try{b:{B(d);for(c=k(q);null!==c&&!(c.expirationTime>d&&H());){var g=c.callback;if("function"===typeof g){c.callback=null;t=c.priorityLevel;g=g(c.expirationTime<=d);d=h.unstable_now();if("function"===typeof g){c.callback=g;B(d);e=!0;break b}c===k(q)&&l(q);B(d)}else l(q);c=k(q)}if(null!==c)e=!0;else{g=k(r);null!==g&&L(C,g.startTime-d);e=!1}}break a}finally{c=null,t=f,u=!1}e=void 0}}finally{e?J():D=!1}}}var J;if("function"===typeof A)J=function(){A(I)};else if("undefined"!==typeof MessageChannel){e=new MessageChannel();var K=e.port2;e.port1.onmessage=I;J=function(){K.postMessage(null)}}else J=function(){y(I,0)};function L(c,d){E=y(function(){c(h.unstable_now())},d)}h.unstable_IdlePriority=5;h.unstable_ImmediatePriority=1;h.unstable_LowPriority=4;h.unstable_NormalPriority=3;h.unstable_Profiling=null;h.unstable_UserBlockingPriority=2;h.unstable_cancelCallback=function(c){c.callback=null};h.unstable_forceFrameRate=function(c){0>c||125<c?!1:F=0<c?Math.floor(1e3/c):10};h.unstable_getCurrentPriorityLevel=function(){return t};h.unstable_next=function(c){switch(t){case 1:case 2:case 3:var d=3;break;default:d=t}var e=t;t=d;try{return c()}finally{t=e}};h.unstable_requestPaint=function(){i&&(x=!0)};h.unstable_runWithPriority=function(c,d){switch(c){case 1:case 2:case 3:case 4:case 5:break;default:c=3}var e=t;t=c;try{return d()}finally{t=e}};h.unstable_scheduleCallback=function(c,d,e){var f=h.unstable_now();"object"===typeof e&&null!==e?(e=e.delay,e="number"===typeof e&&0<e?f+e:f):e=f;switch(c){case 1:var g=-1;break;case 2:g=250;break;case 5:g=1073741823;break;case 4:g=1e4;break;default:g=5e3}g=e+g;c={id:s++,callback:d,priorityLevel:c,startTime:e,expirationTime:g,sortIndex:-1};e>f?(c.sortIndex=e,j(r,c),null===k(q)&&c===k(r)&&(w?(z(E),E=-1):w=!0,L(C,e-f))):(c.sortIndex=g,j(q,c),v||u||(v=!0,D||(D=!0,J())));return c};h.unstable_shouldYield=H;h.unstable_wrapCallback=function(c){var d=t;return function(){var e=t;t=d;try{return c.apply(this,arguments)}finally{t=e}}};"undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error())}),null);
__d("nativeRequestAnimationFrame",[],(function(a,b,c,d,e,f){b=a.__fbNativeRequestAnimationFrame||a.requestAnimationFrame||a.webkitRequestAnimationFrame||a.mozRequestAnimationFrame||a.oRequestAnimationFrame||a.msRequestAnimationFrame;c=b;f["default"]=c}),66);
__d("requestAnimationFramePolyfill",["ExecutionEnvironment","nativeRequestAnimationFrame","performanceNow"],(function(a,b,c,d,e,f,g){var h,i,j=0;b=c("nativeRequestAnimationFrame");if((h||(h=c("ExecutionEnvironment"))).isInWorker&&b!=null)try{b(function(){})}catch(a){b=null}d=b||function(b){var d=(i||(i=c("performanceNow")))(),e=Math.max(0,16-(d-j));j=d+e;return a.setTimeout(function(){b((i||(i=c("performanceNow")))())},e)};e=d;g["default"]=e}),98);
__d("SchedulerFb-Internals_DO_NOT_USE",["Scheduler-dev.classic","Scheduler-profiling.classic","ifRequireable","requestAnimationFramePolyfill"],(function(a,b,c,d,e,f){"use strict";a.requestAnimationFrame===void 0&&(a.requestAnimationFrame=b("requestAnimationFramePolyfill"));var g;g=b("Scheduler-profiling.classic");e.exports={unstable_ImmediatePriority:g.unstable_ImmediatePriority,unstable_UserBlockingPriority:g.unstable_UserBlockingPriority,unstable_NormalPriority:g.unstable_NormalPriority,unstable_LowPriority:g.unstable_LowPriority,unstable_IdlePriority:g.unstable_IdlePriority,unstable_getCurrentPriorityLevel:g.unstable_getCurrentPriorityLevel,unstable_runWithPriority:g.unstable_runWithPriority,unstable_now:g.unstable_now,unstable_scheduleCallback:function(a,c,d){var e=b("ifRequireable")("TimeSlice",function(a){return a.guard(c,"unstable_scheduleCallback",{propagationType:a.PropagationType.CONTINUATION,registerCallStack:!0})},function(){return c});return g.unstable_scheduleCallback(a,e,d)},unstable_cancelCallback:function(a){return g.unstable_cancelCallback(a)},unstable_wrapCallback:function(a){var c=b("ifRequireable")("TimeSlice",function(b){return b.guard(a,"unstable_wrapCallback",{propagationType:b.PropagationType.CONTINUATION,registerCallStack:!0})},function(){return a});return g.unstable_wrapCallback(c)},unstable_pauseExecution:function(){return g.unstable_pauseExecution()},unstable_continueExecution:function(){return g.unstable_continueExecution()},unstable_shouldYield:g.unstable_shouldYield,unstable_requestPaint:g.unstable_requestPaint,unstable_forceFrameRate:g.unstable_forceFrameRate,unstable_Profiling:g.unstable_Profiling}}),null);
__d("JSScheduler",["SchedulerFb-Internals_DO_NOT_USE"],(function(a,b,c,d,e,f){"use strict";var g={unstable_Idle:(c=b("SchedulerFb-Internals_DO_NOT_USE")).unstable_IdlePriority,unstable_Immediate:c.unstable_ImmediatePriority,unstable_Low:c.unstable_LowPriority,unstable_Normal:c.unstable_NormalPriority,unstable_UserBlocking:c.unstable_UserBlockingPriority},h=!1,i=c.unstable_scheduleCallback,j=c.unstable_cancelCallback,k={cancelCallback:function(a){j(a)},cancelDelayedCallback_DO_NOT_USE:function(a){a=a;return j(a)},defer:function(a){var b=k.getCurrentPriorityLevel();return i(b,a)},deferUserBlockingRunAtCurrentPri_DO_NOT_USE:function(a){var c=k.getCurrentPriorityLevel();return i(g.unstable_UserBlocking,function(){b("SchedulerFb-Internals_DO_NOT_USE").unstable_runWithPriority(c,a)})},getCallbackScheduler:function(){var a=k.getCurrentPriorityLevel();return function(b){return i(a,b)}},getCurrentPriorityLevel:c.unstable_getCurrentPriorityLevel,getUserBlockingRunAtCurrentPriCallbackScheduler_DO_NOT_USE:function(){var a=k.getCurrentPriorityLevel();return function(c){return i(g.unstable_UserBlocking,function(){b("SchedulerFb-Internals_DO_NOT_USE").unstable_runWithPriority(a,c)})}},makeSchedulerGlobalEntry:function(c,d,e){c===void 0&&(c=null);d===void 0&&(d=!1);e===void 0&&(e=!1);c!=null&&b("SchedulerFb-Internals_DO_NOT_USE").unstable_forceFrameRate(c);d&&k.startEventProfiling();if(e===!0)return;a.ScheduleJSWork=function(a){return function(){for(var b=arguments.length,c=new Array(b),d=0;d<b;d++)c[d]=arguments[d];h?a.apply(void 0,c):k.deferUserBlockingRunAtCurrentPri_DO_NOT_USE(function(){h=!0;try{a.apply(void 0,c)}finally{h=!1}})}}},priorities:g,runWithPriority:c.unstable_runWithPriority,runWithPriority_DO_NOT_USE:c.unstable_runWithPriority,scheduleDelayedCallback_DO_NOT_USE:function(a,b,c){a=i(a,c,{delay:b});return a},scheduleImmediatePriCallback:function(a){return i(g.unstable_Immediate,a)},scheduleLoggingPriCallback:function(a){return i(g.unstable_Low,a)},scheduleNormalPriCallback:function(a){return i(g.unstable_Normal,a)},scheduleSpeculativeCallback:function(a){return i(g.unstable_Idle,a)},scheduleUserBlockingPriCallback:function(a){return i(g.unstable_UserBlocking,a)},shouldYield:c.unstable_shouldYield,startEventProfiling:function(){var a;a=(a=b("SchedulerFb-Internals_DO_NOT_USE").unstable_Profiling)==null?void 0:a.startLoggingProfilingEvents;typeof a==="function"&&a()},stopEventProfiling:function(){var a;a=(a=b("SchedulerFb-Internals_DO_NOT_USE").unstable_Profiling)==null?void 0:a.stopLoggingProfilingEvents;return typeof a==="function"?a():null}};e.exports=k}),null);
__d("createCancelableFunction",["emptyFunction"],(function(a,b,c,d,e,f,g){function a(a){var b=a;a=function(){for(var a=arguments.length,c=new Array(a),d=0;d<a;d++)c[d]=arguments[d];return b.apply(this,c)};a.cancel=function(){b=c("emptyFunction")};return a}g["default"]=a}),98);
__d("unexpectedUseInComet",["FBLogger","gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(a){if(!c("gkx")("20935"))return;a=a+" called unexpectedly. This is not supported in Comet!";var b=c("FBLogger")("comet_infra").blameToPreviousFrame().blameToPreviousFrame();b.mustfix(a)}g["default"]=a}),98);
__d("RunComet",["ExecutionEnvironment","FBLogger","createCancelableFunction","emptyFunction","recoverableViolation","setTimeout","unexpectedUseInComet"],(function(a,b,c,d,e,f,g){"use strict";var h,i={},j=!1,k=!1,l={remove:c("emptyFunction")};function m(a,b){i.unload==null&&(i.unload=[],i.afterunload=[],(h||(h=c("ExecutionEnvironment"))).canUseEventListeners&&window.addEventListener("unload",function(){p("unload"),p("afterunload")})),i[a]==null?(c("recoverableViolation")("EVENT_LISTENERS."+a+" wasn't initialized but should have been!","comet_infra"),i[a]=[b]):i[a].push(b)}function n(a){a||c("recoverableViolation")("Undefined event listener handler is not allowed","comet_infra");return c("createCancelableFunction")(a!=null?a:c("emptyFunction"))}function o(a){return{remove:function(){a.cancel()}}}function p(a){var b=i[a]||[];for(var d=0;d<b.length;d++){var e=b[d];try{e()}catch(b){c("FBLogger")("comet_infra").catching(b).mustfix("Hit an error while executing '"+a+"' event listeners.")}}i[a]=[]}function q(a){if(j){a();return o(n(c("emptyFunction")))}a=n(a);i.domcontentloaded==null?(i.domcontentloaded=[a],(h||(h=c("ExecutionEnvironment"))).canUseEventListeners&&window.addEventListener("DOMContentLoaded",function(){p("domcontentloaded")},!0)):i.domcontentloaded.push(a);return o(a)}function a(a){a=n(a);m("afterunload",a);return o(a)}function b(a){a=n(a);i.load==null?(i.load=[a],(h||(h=c("ExecutionEnvironment"))).canUseEventListeners&&window.addEventListener("load",function(){p("domcontentloaded"),p("load")})):i.load.push(a);k&&c("setTimeout")(function(){p("domcontentloaded"),p("load")},0);return o(a)}function d(a){a=n(a);m("unload",a);return o(a)}function e(a){a=n(a);i.beforeunload==null&&(i.beforeunload=[],(h||(h=c("ExecutionEnvironment"))).canUseEventListeners&&window.addEventListener("beforeunload",function(a){var b=i.beforeunload||[];for(b of b){var d=void 0;try{d=b()}catch(a){c("FBLogger")("comet_infra").catching(a).mustfix("Hit an error while executing onBeforeUnload event listeners.")}if(d!==void 0){d!=null&&d.body!=null&&(d=d.body);a.preventDefault();a.returnValue=d;return d}}}));i.beforeunload.push(a);return o(a)}var r=e;function f(a){c("unexpectedUseInComet")("Run.onLeave");return l}function s(a,b){c("unexpectedUseInComet")("Run.onCleanupOrLeave");return l}function t(a){c("unexpectedUseInComet")("Run.removeHook")}function u(){document.readyState==="loading"?q(function(){j=!0}):j=!0;if(document.readyState==="complete")k=!0;else{var a=window.onload;window.onload=function(){a&&a(),k=!0}}}(h||(h=c("ExecutionEnvironment"))).canUseDOM&&u();u=null;var v=null;g.onLoad=q;g.onAfterUnload=a;g.onAfterLoad=b;g.onUnload=d;g.onBeforeUnload=e;g.maybeOnBeforeUnload=r;g.onLeave=f;g.onCleanupOrLeave=s;g.__removeHook=t;g.__domContentCallback=u;g.__onloadCallback=v}),98);
__d("RunWWW",["cr:925100"],(function(a,b,c,d,e,f,g){"use strict";g.__domContentCallback=(a=b("cr:925100")).__domContentCallback;g.__onloadCallback=a.__onloadCallback;g.__removeHook=a.__removeHook;g.onAfterLoad=a.onAfterLoad;g.onAfterUnload=a.onAfterUnload;g.onBeforeUnload=a.onBeforeUnload;g.maybeOnBeforeUnload=a.maybeOnBeforeUnload;g.onCleanupOrLeave=a.onCleanupOrLeave;g.onLeave=a.onLeave;g.onLoad=a.onLoad;g.onUnload=a.onUnload}),98);
__d("IntervalTrackingBoundedBuffer",["CircularBuffer","ErrorPubSub"],(function(a,b,c,d,e,f,g){"use strict";var h,i=5e3;a=function(){function a(a){var b=this;this.$6=0;if(a!=null){if(a<=0)throw new Error("Size for a buffer must be greater than zero.")}else a=i;this.$4=a;this.$1=new(c("CircularBuffer"))(a);this.$1.onEvict(function(){b.$6++});this.$2=[];this.$3=1;this.$5=0}var b=a.prototype;b.open=function(){var a=this,b=this.$3++,c=!1,d,e=this.$5,f={id:b,startIdx:e,hasOverflown:function(){return f.getOverflowSize()>0},getOverflowSize:function(){return d!=null?d:Math.max(a.$6-e,0)},close:function(){if(c)return[];else{c=!0;d=a.$6-e;return a.$7(b)}}};this.$2.push(f);return f};b.pushElement=function(a){this.$2.length>0&&(this.$1.write(a),this.$5++);return this};b.isActive=function(){return this.$2.length>0};b.$8=function(a){return Math.max(a-this.$6,0)};b.$7=function(a){var b,d,e,f;for(var g=0;g<this.$2.length;g++){var i=this.$2[g],j=i.startIdx;i=i.id;i===a?(e=g,f=j):(d==null||j<d)&&(d=j);(b==null||j<b)&&(b=j)}if(e==null||b==null||f==null){(h||(h=c("ErrorPubSub"))).reportError(new Error("messed up state inside IntervalTrackingBoundedBuffer"));return[]}this.$2.splice(e,1);i=this.$8(f);j=this.$1.read().slice(i);g=this.$8(d==null?this.$5:d)-this.$8(b);g>0&&(this.$1.dropFirst(g),this.$6+=g);return j};return a}();g["default"]=a}),98);
__d("TimeSliceSham",["Env","ErrorGuard","IntervalTrackingBoundedBuffer"],(function(a,b,c,d,e,f){var g,h;c=(g||b("Env")).timesliceBufferSize;c==null&&(c=5e3);var i=new(b("IntervalTrackingBoundedBuffer"))(c),j={PropagationType:{CONTINUATION:0,EXECUTION:1,ORPHAN:2},guard:function(a,c){return(h||(h=b("ErrorGuard"))).guard(a,{name:"TimeSlice"+(c?": "+c:"")})},copyGuardForWrapper:function(a,b){return a},checkCoverage:function(){},setLogging:function(a,b){},getContext:function(){return null},getGuardedContinuation:function(a){function a(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];return a.apply(this,c)}return a},getReusableContinuation:function(a){return j.getPlaceholderReusableContinuation()},getPlaceholderReusableContinuation:function(){var a=function(a){return a()};a.last=a;return a},getGuardNameStack:function(){return[]},registerExecutionContextObserver:function(a){},catchUpOnDemandExecutionContextObservers:function(a){},getBuffer:function(){return i}};a.TimeSlice=j;e.exports=j}),6);
__d("setTimeoutCometInternals",["JSScheduler"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Map(),j=0;function a(a){if(a!=null){var b=i.get(a);b!==void 0&&(i["delete"](a),(h||(h=c("JSScheduler"))).cancelDelayedCallback_DO_NOT_USE(b))}}function b(a){if(a!=null){var b=i.get(a);b!==void 0&&(i["delete"](a),(h||(h=c("JSScheduler"))).cancelDelayedCallback_DO_NOT_USE(b))}}function d(a,b,d){for(var e=arguments.length,f=new Array(e>3?e-3:0),g=3;g<e;g++)f[g-3]=arguments[g];var k=j;j+=1;if(typeof b!=="function")return k;var l=function e(){var g=(h||(h=c("JSScheduler"))).scheduleDelayedCallback_DO_NOT_USE(a,d,e);i.set(k,g);b.apply(void 0,f)},m=(h||(h=c("JSScheduler"))).scheduleDelayedCallback_DO_NOT_USE(a,d,l);i.set(k,m);return k}function e(a,b,d){for(var e=arguments.length,f=new Array(e>3?e-3:0),g=3;g<e;g++)f[g-3]=arguments[g];var k=j;j+=1;if(typeof b!=="function")return k;var l=(h||(h=c("JSScheduler"))).scheduleDelayedCallback_DO_NOT_USE(a,d,function(){i["delete"](k),b.apply(void 0,f)});i.set(k,l);return k}g.clearInterval_DO_NOT_USE=a;g.clearTimeout_DO_NOT_USE=b;g.setIntervalAtPriority_DO_NOT_USE=d;g.setTimeoutAtPriority_DO_NOT_USE=e}),98);
__d("clearIntervalComet",["setTimeoutCometInternals"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=d("setTimeoutCometInternals").clearInterval_DO_NOT_USE}),98);
__d("clearIntervalWWW",["cr:1003267"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:1003267")}),98);
__d("clearTimeoutComet",["setTimeoutCometInternals"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=d("setTimeoutCometInternals").clearTimeout_DO_NOT_USE}),98);
__d("clearTimeoutWWW",["cr:806696"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:806696")}),98);
__d("clearTimeoutWWWOrMobile",["cr:7386"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:7386")}),98);
__d("setIntervalAcrossTransitionsWWW",["cr:896462"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:896462")}),98);
__d("setIntervalComet",["JSScheduler","setTimeoutCometInternals"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){var c=(h||(h=d("JSScheduler"))).getCurrentPriorityLevel()===(h||(h=d("JSScheduler"))).priorities.unstable_Idle?(h||(h=d("JSScheduler"))).priorities.unstable_Idle:(h||(h=d("JSScheduler"))).priorities.unstable_Low;for(var e=arguments.length,f=new Array(e>2?e-2:0),g=2;g<e;g++)f[g-2]=arguments[g];return d("setTimeoutCometInternals").setIntervalAtPriority_DO_NOT_USE.apply(d("setTimeoutCometInternals"),[c,a,b].concat(f))}g["default"]=a}),98);
__d("setIntervalWWW",["cr:896461"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:896461")}),98);
__d("setTimeoutAcrossTransitionsWWW",["cr:986633"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:986633")}),98);
__d("setTimeoutComet",["JSScheduler","setTimeoutCometInternals"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){var c=(h||(h=d("JSScheduler"))).getCurrentPriorityLevel()===(h||(h=d("JSScheduler"))).priorities.unstable_Idle?(h||(h=d("JSScheduler"))).priorities.unstable_Idle:(h||(h=d("JSScheduler"))).priorities.unstable_Low;for(var e=arguments.length,f=new Array(e>2?e-2:0),g=2;g<e;g++)f[g-2]=arguments[g];return d("setTimeoutCometInternals").setTimeoutAtPriority_DO_NOT_USE.apply(d("setTimeoutCometInternals"),[c,a,b].concat(f))}g["default"]=a}),98);
__d("setTimeoutWWW",["cr:807042"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:807042")}),98);
__d("setTimeoutWWWOrMobile",["cr:7390"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:7390")}),98);
__d("CometPreludeCriticalRequireConds",["DTSG","DTSG_ASYNC","JSScheduler","RunComet","RunWWW","TimeSliceSham","clearIntervalComet","clearIntervalWWW","clearTimeoutComet","clearTimeoutWWW","clearTimeoutWWWOrMobile","setIntervalAcrossTransitionsWWW","setIntervalComet","setIntervalWWW","setTimeoutAcrossTransitionsWWW","setTimeoutComet","setTimeoutWWW","setTimeoutWWWOrMobile"],(function(a,b,c,d,e,f){"use strict";var g;b("DTSG");b("DTSG_ASYNC");g||(g=b("JSScheduler"));b("TimeSliceSham");b("RunComet");b("RunWWW");b("clearIntervalComet");b("clearIntervalWWW");b("clearTimeoutComet");b("clearTimeoutWWW");b("clearTimeoutWWWOrMobile");b("setIntervalAcrossTransitionsWWW");b("setIntervalComet");b("setIntervalWWW");b("setTimeoutAcrossTransitionsWWW");b("setTimeoutComet");b("setTimeoutWWW");b("setTimeoutWWWOrMobile")}),null);
__d("CometResourceScheduler",["Bootloader","ErrorGuard"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Set(),j=new Set(),k=[];d={executedDeferredCallback:0,getPendingCallbacks:function(){return k},queuedDeferredCallback:0};function a(a){var b=[];for(a of a)j.has(a)||(j.add(a),b.push(a));b.length&&l(b)}function b(a){k.push(a),i.size===0&&m()}function l(a){a.forEach(function(a){return i.add(a)}),c("Bootloader").loadResources(a,{onAll:function(){a.forEach(function(a){return i["delete"](a)});if(i.size)return;m()}})}function m(){var a=k;k=[];a.forEach(function(a){return(h||(h=c("ErrorGuard"))).applyWithGuard(a,null,[])})}g.__debug=d;g.registerHighPriHashes=a;g.onHighPriComplete=b}),98);
__d("DeferredJSResourceScheduler",["Bootloader","CometResourceScheduler","JSScheduler"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){d("CometResourceScheduler").__debug.queuedDeferredCallback++,d("CometResourceScheduler").onHighPriComplete(function(){(h||(h=c("JSScheduler"))).scheduleLoggingPriCallback(function(){d("CometResourceScheduler").__debug.executedDeferredCallback++,c("Bootloader").loadResources(a)})})}g["default"]=a}),98);
__d("unstable_server-external-runtime-exp",["Promise"],(function(a,b,c,d,e,f){var g;(function(){function c(a){a=a.querySelectorAll("template");for(var b=0;b<a.length;b++)e(a[b])}function d(a){function b(a){for(var b=0;b<a.length;b++)for(var c=a[b].addedNodes,d=0;d<c.length;d++)c[d].parentNode&&e(c[d])}var c=new MutationObserver(b);c.observe(a,{childList:!0});window.addEventListener("DOMContentLoaded",function(){b(c.takeRecords()),c.disconnect()})}function e(a){if(1===a.nodeType&&a.dataset){var b=a.dataset;null!=b.rxi?(j(b.bid,b.dgst,b.msg,b.stck,b.cstck),a.remove()):null!=b.rri?(m(b.bid,b.sid,JSON.parse(b.sty)),a.remove()):null!=b.rci?(l(b.bid,b.sid),a.remove()):null!=b.rsi&&(n(b.sid,b.pid),a.remove())}}var f,h=new Map(),i=[],j=function(a,b,c,d,e){var f=document.getElementById(a);f&&(a=f.previousSibling,a.data="$!",f=f.dataset,b&&(f.dgst=b),c&&(f.msg=c),d&&(f.stck=d),e&&(f.cstck=e),a._reactRetry&&a._reactRetry())},k=function(a,c){function d(a,b){var c=a.getAttribute(b);c&&(b=a.style,h.push(a,b.viewTransitionName,b.viewTransitionClass),"auto"!==c&&(b.viewTransitionClass=c),(a=a.getAttribute("vt-name"))||(a="_T_"+f+++"_"),b.viewTransitionName=a,e=!0)}var e=!1,f=0,h=[];try{var j=document.__reactViewTransition;if(j){j.finished["finally"](k.bind(null,c));return}var l=new Map();for(j=1;j<c.length;j+=2)for(var m=c[j].querySelectorAll("[vt-share]"),n=0;n<m.length;n++){var o=m[n];l.set(o.getAttribute("vt-name"),o)}var p=[];for(m=0;m<c.length;m+=2){var q=c[m],r=q.parentNode;if(r){var s=r.getBoundingClientRect();if(s.left||s.top||s.width||s.height){o=q;for(j=0;o;){if(8===o.nodeType){s=o.data;if("/$"===s)if(0===j)break;else j--;else"$"!==s&&"$?"!==s&&"$~"!==s&&"$!"!==s||j++}else if(1===o.nodeType){n=o;q=n.getAttribute("vt-name");s=l.get(q);d(n,s?"vt-share":"vt-exit");s&&(d(s,"vt-share"),l.set(q,null));s=n.querySelectorAll("[vt-share]");for(n=0;n<s.length;n++){q=s[n];var t=q.getAttribute("vt-name"),u=l.get(t);u&&(d(q,"vt-share"),d(u,"vt-share"),l.set(t,null))}}o=o.nextSibling}for(q=c[m+1],u=q.firstElementChild;u;)null!==l.get(u.getAttribute("vt-name"))&&d(u,"vt-enter"),u=u.nextElementSibling;o=r;do for(t=o.firstElementChild;t;){s=t.getAttribute("vt-update");s&&"none"!==s&&!h.includes(t)&&d(t,"vt-update");t=t.nextElementSibling}while((o=o.parentNode)&&1===o.nodeType&&"none"!==o.getAttribute("vt-update"));u=q.querySelectorAll('img[src]:not([loading="lazy"])');p.push.apply(p,u)}}}if(e){var v=document.__reactViewTransition=document.startViewTransition({update:function(){a(c);for(var d=[document.documentElement.clientHeight,document.fonts.ready],e={},f=0;f<p.length;e={$jscomp$loop$prop$suspenseyImage$8:e.$jscomp$loop$prop$suspenseyImage$8},f++)if(e.$jscomp$loop$prop$suspenseyImage$8=p[f],!e.$jscomp$loop$prop$suspenseyImage$8.complete){var h=e.$jscomp$loop$prop$suspenseyImage$8.getBoundingClientRect();0<h.bottom&&0<h.right&&h.top<window.innerHeight&&h.left<window.innerWidth&&(h=new(g||(g=b("Promise")))(function(a){return function(b){a.$jscomp$loop$prop$suspenseyImage$8.addEventListener("load",b),a.$jscomp$loop$prop$suspenseyImage$8.addEventListener("error",b)}}(e)),d.push(h))}return(g||(g=b("Promise"))).race([g.all(d),new g(function(a){var b=performance.now();setTimeout(a,2300>b&&2e3<b?2300-b:500)})])},types:[]});v.ready["finally"](function(){for(var a=h.length-3;0<=a;a-=3){var b=h[a],c=b.style;c.viewTransitionName=h[a+1];c.viewTransitionClass=h[a+1];""===b.getAttribute("style")&&b.removeAttribute("style")}});v.finished["finally"](function(){document.__reactViewTransition===v&&(document.__reactViewTransition=null)});i=[];return}}catch(a){}a(c)}.bind(null,function(b){f=performance.now();for(var c=0;c<b.length;c+=2){var d=b[c],e=b[c+1],g=d.parentNode;if(g){var h=d.previousSibling,i=0;do{if(d&&8===d.nodeType){var j=d.data;if("/$"===j||"/&"===j)if(0===i)break;else i--;else"$"!==j&&"$?"!==j&&"$~"!==j&&"$!"!==j&&"&"!==j||i++}j=d.nextSibling;g.removeChild(d);d=j}while(d);for(;e.firstChild;)g.insertBefore(e.firstChild,d);h.data="$";a.__onSSRInject&&a.__onSSRInject();h._reactRetry&&h._reactRetry()}}b.length=0}),l=function(a,b){(b=document.getElementById(b))&&((b.parentNode.removeChild(b),a=document.getElementById(a))&&(a.previousSibling.data="$~",i.push(a,b),2===i.length&&(a="number"!==typeof f?0:f,b=performance.now(),a=2300>b&&2e3<b?2300-b:a+300-b,setTimeout(k.bind(null,i),a))))},m=function(a,c,d){function e(a){this._p=null,a()}for(var f=new Map(),i=document,k,m,n=i.querySelectorAll("link[data-precedence],style[data-precedence]"),o=[],p=0;m=n[p++];)"not all"===m.getAttribute("media")?o.push(m):("LINK"===m.tagName&&h.set(m.getAttribute("href"),m),f.set(m.dataset.precedence,k=m));n=0;m=[];var q,r;for(p=!0;;){if(p){var s=d[n++];if(!s){p=!1;n=0;continue}var t=!1,u=0,v=s[u++];if(r=h.get(v)){var w=r._p;t=!0}else{r=i.createElement("link");r.href=v;r.rel="stylesheet";for(r.dataset.precedence=q=s[u++];w=s[u++];)r.setAttribute(w,s[u++]);w=r._p=new(g||(g=b("Promise")))(function(a,b){r.onload=e.bind(r,a),r.onerror=e.bind(r,b)});h.set(v,r)}v=r.getAttribute("media");!w||v&&!window.matchMedia(v).matches||m.push(w);if(t)continue}else{r=o[n++];if(!r)break;q=r.getAttribute("data-precedence");r.removeAttribute("media")}t=f.get(q)||k;t===k&&(k=r);f.set(q,r);t?t.parentNode.insertBefore(r,t.nextSibling):(t=i.head,t.insertBefore(r,t.firstChild))}(d=document.getElementById(a))&&(d.previousSibling.data="$~");(g||(g=b("Promise"))).all(m).then(l.bind(null,a,c),j.bind(null,a,"CSS failed to load"))},n=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};(function(){addEventListener("submit",function(a){if(!a.defaultPrevented){var b=a.target,c=a.submitter,d=b.action,e=c;if(c){var f=c.getAttribute("formAction");null!=f&&(d=f,e=null)}"javascript:throw new Error('React form unexpectedly submitted.')"===d&&(a.preventDefault(),e?(a=document.createElement("input"),a.name=e.name,a.value=e.value,e.parentNode.insertBefore(a,e),e=new FormData(b),a.parentNode.removeChild(a)):e=new FormData(b),a=b.ownerDocument||b,(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(b,c,e))}})})();var o=performance.getEntriesByType?performance.getEntriesByType("paint"):[];0<o.length?f=o[0].startTime:requestAnimationFrame(function(){f=performance.now()});if(null!=document.body)"loading"===document.readyState&&d(document.body),c(document.body);else{var p=new MutationObserver(function(){null!=document.body&&("loading"===document.readyState&&d(document.body),c(document.body),p.disconnect())});p.observe(document.documentElement,{childList:!0})}})()}),null);
__d("unstable_server-external-runtime-old",["Promise"],(function(a,b,c,d,e,f){var g;(function(){function a(a){a=a.querySelectorAll("template");for(var b=0;b<a.length;b++)d(a[b])}function c(a){function b(a){for(var b=0;b<a.length;b++)for(var c=a[b].addedNodes,e=0;e<c.length;e++)c[e].parentNode&&d(c[e])}var c=new MutationObserver(b);c.observe(a,{childList:!0});window.addEventListener("DOMContentLoaded",function(){b(c.takeRecords()),c.disconnect()})}function d(a){if(1===a.nodeType&&a.dataset){var b=a.dataset;null!=b.rxi?(h(b.bid,b.dgst,b.msg,b.stck,b.cstck),a.remove()):null!=b.rri?(j(b.bid,b.sid,JSON.parse(b.sty)),a.remove()):null!=b.rci?(i(b.bid,b.sid),a.remove()):null!=b.rsi&&(k(b.sid,b.pid),a.remove())}}var e=new Map(),f=[],h=function(a,b,c,d,e){var f=document.getElementById(a);f&&(a=f.previousSibling,a.data="$!",f=f.dataset,b&&(f.dgst=b),c&&(f.msg=c),d&&(f.stck=d),e&&(f.cstck=e),a._reactRetry&&a._reactRetry())},i=function(a,b){function c(){performance.now();var a=f;f=[];for(var b=0;b<a.length;b+=2){var c=a[b],d=a[b+1],e=c.parentNode;if(e){var g=c.previousSibling,h=0;do{if(c&&8===c.nodeType){var i=c.data;if("/$"===i||"/&"===i)if(0===h)break;else h--;else"$"!==i&&"$?"!==i&&"$~"!==i&&"$!"!==i&&"&"!==i||h++}i=c.nextSibling;e.removeChild(c);c=i}while(c);for(;d.firstChild;)e.insertBefore(d.firstChild,c);g.data="$";g._reactRetry&&g._reactRetry()}}}(b=document.getElementById(b))&&((b.parentNode.removeChild(b),a=document.getElementById(a))&&(a.previousSibling.data="$~",f.push(a,b),2===f.length&&c()))},j=function(a,c,d){function f(a){this._p=null,a()}for(var j=new Map(),k=document,l,m,n=k.querySelectorAll("link[data-precedence],style[data-precedence]"),o=[],p=0;m=n[p++];)"not all"===m.getAttribute("media")?o.push(m):("LINK"===m.tagName&&e.set(m.getAttribute("href"),m),j.set(m.dataset.precedence,l=m));n=0;m=[];var q,r;for(p=!0;;){if(p){var s=d[n++];if(!s){p=!1;n=0;continue}var t=!1,u=0,v=s[u++];if(r=e.get(v)){var w=r._p;t=!0}else{r=k.createElement("link");r.href=v;r.rel="stylesheet";for(r.dataset.precedence=q=s[u++];w=s[u++];)r.setAttribute(w,s[u++]);w=r._p=new(g||(g=b("Promise")))(function(a,b){r.onload=f.bind(r,a),r.onerror=f.bind(r,b)});e.set(v,r)}v=r.getAttribute("media");!w||v&&!window.matchMedia(v).matches||m.push(w);if(t)continue}else{r=o[n++];if(!r)break;q=r.getAttribute("data-precedence");r.removeAttribute("media")}t=j.get(q)||l;t===l&&(l=r);j.set(q,r);t?t.parentNode.insertBefore(r,t.nextSibling):(t=k.head,t.insertBefore(r,t.firstChild))}(d=document.getElementById(a))&&(d.previousSibling.data="$~");(g||(g=b("Promise"))).all(m).then(i.bind(null,a,c),h.bind(null,a,"CSS failed to load"))},k=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};(function(){addEventListener("submit",function(a){if(!a.defaultPrevented){var b=a.target,c=a.submitter,d=b.action,e=c;if(c){var f=c.getAttribute("formAction");null!=f&&(d=f,e=null)}"javascript:throw new Error('React form unexpectedly submitted.')"===d&&(a.preventDefault(),e?(a=document.createElement("input"),a.name=e.name,a.value=e.value,e.parentNode.insertBefore(a,e),e=new FormData(b),a.parentNode.removeChild(a)):e=new FormData(b),a=b.ownerDocument||b,(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(b,c,e))}})})();var l=performance.getEntriesByType?performance.getEntriesByType("paint"):[];0<l.length?l[0].startTime:requestAnimationFrame(function(){performance.now()});if(null!=document.body)"loading"===document.readyState&&c(document.body),a(document.body);else{var m=new MutationObserver(function(){null!=document.body&&("loading"===document.readyState&&c(document.body),a(document.body),m.disconnect())});m.observe(document.documentElement,{childList:!0})}})()}),null);
__d("ReactDOMServerExternalRuntime",["Env","ExecutionEnvironment","unstable_server-external-runtime-exp","unstable_server-external-runtime-old"],(function(a,b,c,d,e,f,g){"use strict";var h,i;if((h||c("ExecutionEnvironment")).canUseDOM){((a=(i||(i=c("Env")))==null?void 0:(i||(i=c("Env"))).ssr_throttle_suspense_fallbacks)!=null?a:!1)?b("unstable_server-external-runtime-exp"):b("unstable_server-external-runtime-old");window._qplInl&&typeof window._qplInl==="object"&&window._qplInl.addPoint&&typeof window._qplInl.addPoint==="function"&&window._qplInl.addPoint("ssrPayloadListenerStart")}}),35);
__d("Run",["cr:310"],(function(a,b,c,d,e,f,g){"use strict";g.__domContentCallback=(a=b("cr:310")).__domContentCallback;g.__onloadCallback=a.__onloadCallback;g.onAfterLoad=a.onAfterLoad;g.onAfterUnload=a.onAfterUnload;g.onBeforeUnload=a.onBeforeUnload;g.maybeOnBeforeUnload=a.maybeOnBeforeUnload;g.onLeave=a.onLeave;g.onLoad=a.onLoad;g.onUnload=a.onUnload}),98);
__d("ScheduledApplyEach",["JSScheduler"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b,c){return a.map(function(a){(h||(h=d("JSScheduler"))).deferUserBlockingRunAtCurrentPri_DO_NOT_USE(function(){b.apply(c,a)})})}g["default"]=a}),98);
__d("ScheduledServerJS",["JSScheduler","ScheduledApplyEach","ServerJS"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b,e){b!=null&&new(c("ServerJS"))().handle(b),(h||(h=d("JSScheduler"))).runWithPriority(h.priorities.unstable_Normal,function(){e!=null&&new(c("ServerJS"))().handle(e),new(c("ServerJS"))().handleWithCustomApplyEach(c("ScheduledApplyEach"),a)})}g.handle=a}),98);
__d("ScheduledServerJSDefine",["JSScheduler","ServerJSDefine"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,b){a.forEach(function(a){var e=a;b!=null&&(e=[].concat(a,[b]));(h||(h=d("JSScheduler"))).deferUserBlockingRunAtCurrentPri_DO_NOT_USE(function(){c("ServerJSDefine").handleDefine.apply(null,e)})})}g.handleDefines=a}),98);
__d("ScheduledServerJSWithCSS",["Bootloader","JSScheduler","ScheduledServerJS"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a,b,e,f){return function(){c("Bootloader").loadResources(f,{onAll:function(){d("ScheduledServerJS").handle(a,b,e)}})}}function a(a,b,d,e){a=i(a,b,d,e);e.length>0&&(h||(h=c("JSScheduler"))).scheduleImmediatePriCallback(a)}g.handle=a}),98);
__d("performanceNavigationStart",["performance"],(function(a,b,c,d,e,f){var g,h=typeof window!=="undefined"?window:self;if((g||(g=b("performance"))).now)if((g||(g=b("performance"))).timing&&(g||(g=b("performance"))).timing.navigationStart)a=function(){return(g||(g=b("performance"))).timing.navigationStart};else{if(typeof h._cstart==="number")a=function(){return h._cstart};else{var i=Date.now();a=function(){return i}}a.isPolyfilled=!0}else a=function(){return 0},a.isPolyfilled=!0;e.exports=a}),null);
__d("bootstrapWebSession",["WebSession","WebSessionDefaultTimeoutMs","performanceNavigationStart"],(function(a,b,c,d,e,f,g){"use strict";function h(a){a=c("performanceNavigationStart")()||a;return Number.isInteger(a)?a:null}var i=!1;function a(a){if(i===!0)return;i=!0;a=h(a);a!=null&&a>0&&d("WebSession").extend(a+c("WebSessionDefaultTimeoutMs"))}g["default"]=a}),98);
__d("cx",[],(function(a,b,c,d,e,f){function a(a){throw new Error("cx: Unexpected class transformation.")}f["default"]=a}),66);
__d("shouldDisableAnimations",[],(function(a,b,c,d,e,f){"use strict";var g=4;function a(){return navigator!=null&&navigator.hardwareConcurrency!=null&&navigator.hardwareConcurrency<g}f["default"]=a}),66);
__d("maybeDisableAnimations",["cx","shouldDisableAnimations"],(function(a,b,c,d,e,f,g,h){"use strict";function a(){if(c("shouldDisableAnimations")()){var a;(a=document.documentElement)==null?void 0:a.classList.add("_8ykn")}}g["default"]=a}),98);
__d("qplAnnotationsIntServerJS",[],(function(a,b,c,d,e,f){"use strict";var g={};function a(a,b,c){if(a==null)return g;g[a]==null&&(g[a]={});b!=null&&c!=null&&(g[a][b]=c);return g}f["default"]=a}),66);
__d("qplAnnotationsStringServerJS",[],(function(a,b,c,d,e,f){"use strict";var g={};function a(a,b,c){if(a==null)return g;g[a]==null&&(g[a]={});b!=null&&c!=null&&(g[a][b]=c);return g}f["default"]=a}),66);
__d("qplTagServerJS",[],(function(a,b,c,d,e,f){"use strict";var g=[];function a(a){if(a==null)return g;g.push(a)}f["default"]=a}),66);
__d("UserTimingUtils.shared",["UserTimingUtils","performance"],(function(a,b,c,d,e,f,g){"use strict";var h,i=typeof (h||(h=c("performance"))).mark==="function"&&typeof (h||(h=c("performance"))).clearMarks==="function"&&typeof (h||(h=c("performance"))).measure==="function"&&typeof (h||(h=c("performance"))).clearMeasures==="function",j=!1;if(i&&(h||(h=c("performance"))).mark!=null){var k="__v3",l={};Object.defineProperty(l,"startTime",{get:function(){j=!0}});try{(h||(h=c("performance"))).mark(k,l)}catch(a){}finally{(h||(h=c("performance"))).clearMarks(k)}}function a(){return j}function b(a){i&&(h||(h=c("performance"))).mark(a)}function e(a,b,d){d===void 0&&(d=!0);if(i){try{(h||(h=c("performance"))).measure(a,b)}catch(a){}d&&(h||(h=c("performance"))).clearMarks(b);(h||(h=c("performance"))).clearMeasures(a)}}function f(a){if(i){try{a=(h||(h=c("performance"))).getEntriesByName(a,"mark");if(a!=null&&a.length>0)return!0}catch(a){}return!1}}function m(a){if(i)try{(h||(h=c("performance"))).clearMarks(a)}catch(a){}}async function n(a,b){var e=(h||(h=c("performance"))).now();b=await b();var f=h.now();d("UserTimingUtils").measureModern(a,{end:f,start:e});return b}function o(a,b,c){}function p(a,b){}function q(a,b,e){var f=(h||(h=c("performance"))).now();b();b=h.now();d("UserTimingUtils").measureModern(a,{end:b,start:f},e)}g.isUserTimingV3Supported=a;g.measureStart=b;g.measureEnd=e;g.hasMark=f;g.clearMarks=m;g.asyncMeasure=n;g.measureReactCommit=o;g.measureReactPostCommit=p;g.measureCallback=q}),98);
__d("UserTimingUtils",["UserTimingUtils.shared","performance","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,b,e,f){if(d("UserTimingUtils.shared").isUserTimingV3Supported()){var g=b;if(e!=null){var i,j;i=(i=b==null?void 0:(i=b.detail)==null?void 0:i.devtools)!=null?i:void 0;i=(j=i)!=null?j:{dataType:"track-entry",track:e,trackGroup:f};i.track=e;f=(j=b==null?void 0:b.detail)!=null?j:{devtools:i};g=babelHelpers["extends"]({},b,{detail:f})}try{(h||(h=c("performance"))).measure(a,g),h.clearMeasures(a)}catch(a){}}}function b(a,b,e,f){if(d("UserTimingUtils.shared").isUserTimingV3Supported()){var g=b;if(e!=null){var j;g={startTime:(j=b==null?void 0:b.startTime)!=null?j:(i||(i=c("performanceNow")))(),detail:babelHelpers["extends"]({},b,{devtools:{dataType:"track-entry",track:e,trackGroup:f}})}}(h||(h=c("performance"))).mark(a,g);h.clearMarks(a)}}g.measureStart=(e=d("UserTimingUtils.shared")).measureStart;g.measureEnd=e.measureEnd;g.hasMark=e.hasMark;g.clearMarks=e.clearMarks;g.asyncMeasure=e.asyncMeasure;g.measureReactCommit=e.measureReactCommit;g.measureReactPostCommit=e.measureReactPostCommit;g.measureModern=a;g.markModern=b}),98);
__d("nowServerJS",[],(function(a,b,c,d,e,f){"use strict";function a(){if(window._nowInl&&typeof window._nowInl==="function")return window._nowInl();var a=window.performance;return a&&a.now&&a.timing&&a.timing.navigationStart?a.now()+a.timing.navigationStart:new Date().getTime()}f["default"]=a}),66);
__d("qplTimingsServerJS",["UserTimingUtils","nowServerJS","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i={};function a(a,b,e){if(a==null)return i;i[a]==null&&(i[a]={});b!=null&&(i[a][b]=e!=null?e:c("nowServerJS")(),d("UserTimingUtils").measureModern(b,{start:0,end:(h||(h=c("performanceNow")))()},"ServerJS"))}g["default"]=a}),98);
__d("hyperionGlobals",[],(function(a,b,c,d,e,f){a=typeof globalThis==="object"?globalThis:typeof a==="object"?a:typeof window==="object"?window:typeof self==="object"?self:{};var g={getCallStack:function(){return[]},logger:console};function b(a){var b;g.getCallStack=(b=a.getCallStack)!=null?b:g.getCallStack;g.logger=a.logger}function c(){return g.logger}function d(a,b,c){if(!a){a=(a=c==null?void 0:c.getCallStack)!=null?a:g.getCallStack;c=(c=c==null?void 0:c.logger)!=null?c:g.logger;a=a(2);a&&a.length>0?c.error(b,a):c.error(b)}}var h={};function e(){return h}function i(a){h=a}f.assert=d;f.getFlags=e;f.getLogger=c;f.globalScope=a;f.setAssertLoggerOptions=b;f.setFlags=i}),66);
__d("hyperionHook",[],(function(a,b,c,d,e,f){var g=function(){};a=function(){function a(){this.call=g}var b=a.prototype;b.hasCallback=function(a){if(!this.$1)return a?this.call===a:this.call!==g;else{var b=this.$1;return b.length>0&&(!a||b.some(function(b){return b===a||b.$2===a}))}};b.createMultiCallbackCall=function(a){var b=function(){var b=a;for(var c=0,d=b.length;c<d;++c)b[c].apply(this,arguments)};return b};b.add=function(a,b){var c=a;if(b){var d=this;b=function b(){d.remove(b);return a.apply(this,arguments)};b.$2=a;c=b}this.call===g?this.call=c:!this.$1?(this.$1=[this.call,c],this.call=this.createMultiCallbackCall(this.$1)):this.$1.push(c);return a};b.remove=function(a){return this.removeIf(function(b){return b===a})};b.removeIf=function(a){if(this.$1){var b=this.$1.filter(function(b){return!a(b)}),c=this.$1.length>b.length;c&&(this.$1=b,this.call=this.createMultiCallbackCall(this.$1));return c}else if(a(this.call)){this.call=g;return!0}else return!1};b.clear=function(){this.call===g||!this.$1?this.call=g:this.$1.length=0};return a}();f.Hook=a}),66);
__d("hyperionCore",["Promise","__debug","hyperionGlobals","hyperionHook"],(function(a,b,c,d,e,f,g){var h,i,j,k,l,m=function(){function a(a){this.status=0,this.name=a}var b=a.prototype;b.interceptObjectOwnProperties=function(a){};return a}();function n(a,b){var c;while(a&&!c)c=Object.getOwnPropertyDescriptor(a,b),c&&(c.container=a),a=Object.getPrototypeOf(a);return c}function o(a,b,c){try{Object.defineProperty(a,b,c)}catch(a){}}var p=Object.prototype.hasOwnProperty;function aa(a,b){return p.call(a,b)}function q(a,b,c){if(!a||!b)return;c=Object.getOwnPropertyNames(a);for(var e=0,f=c.length;e<f;++e){var g=c[e];if(!(g in b)){var h=Object.getOwnPropertyDescriptor(a,g);d("hyperionGlobals").assert(h!=null,"Unexpected situation, we should have own property for "+g);try{Object.defineProperty(b,g,h)}catch(a){}}}b.toString=function(){return a.toString()};Object.prototype.hasOwnProperty.call(a,"valueOf")&&(b.valueOf=function(){return a.valueOf()});b.prototype=a.prototype;g=Object.getOwnPropertyDescriptor(a,"name");try{Object.defineProperty(b,"name",g)}catch(a){}}var r="__ext",s="__sproto",ba=0,t=[];function a(a){t.push(a);return function(){var b=t.indexOf(a);b>-1&&t.splice(b,1)}}function c(a){a=Object.getOwnPropertyDescriptor(a,s);return a==null?void 0:a.value}function e(a,b){Object.defineProperty(a,s,{value:b});return b}var u={};function ca(a){var b=typeof a;return a&&(b==="object"||b==="function")}function da(a){return aa(a,r)}function v(a,b){if(ca(a)&&!da(a)){b=b;for(var c=0;!b&&c<t.length;++c)b=t[c](a);b||(b=a[s]);if(b){c={virtualPropertyValues:{},shadowPrototype:b,id:ba++};u.value=c;Object.defineProperty(a,r,u);b.interceptObject(a)}}return a}function w(a,b){var c=a[r];!c&&b&&(v(a),c=a[r]);return c}function f(a,b){a=w(a,!0);return a==null?void 0:a.virtualPropertyValues[b]}function ea(a,b,c){a=w(a,!0);a?a.virtualPropertyValues[b]=c:d("hyperionGlobals").assert(!!a,"Could not get extension for the object");return c}var x="__ext",y=function(){},fa=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}var c=a.prototype;c.createMultiCallbackCall=function(a){return function(b){b=b;for(var c=0,d=a.length;c<d;++c)b=a[c].call(this,b);return b}};return a}((l||(l=d("hyperionHook"))).Hook),ga=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}var c=a.prototype;c.createMultiCallbackCall=function(a){return function(){var b=!1;for(var c=0,d=a.length;c<d;++c){var e=a[c];b=e.apply(this,arguments)||b}return b}};return a}(l.Hook),ha=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}var c=a.prototype;c.createMultiCallbackCall=function(a){return function(b){b=b;for(var c=0,d=a.length;c<d;++c)b=a[c].call(this,b);return b}};return a}(l.Hook),ia=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}return a}(l.Hook),ja=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}var c=a.prototype;c.createMultiCallbackCall=function(a){return function(){var b=[];for(var c=0,d=a.length;c<d;++c){var e=a[c];b.push(e.apply(this,arguments))}return function(a){a=a;for(var c=0,d=b.length;c<d;++c){var e=b[c];a=e.call(this,a)}return a}}};return a}(l.Hook),z=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,d){c===void 0&&(c=y);d===void 0&&(d=!1);a=b.call(this,a)||this;a.original=y;var e=babelHelpers.assertThisInitialized(a);a.interceptor=d?function(){var a=e.dispatcherFunc.apply(this,arguments);return v(a)}:function(){var a=e.dispatcherFunc.apply(this,arguments);return a};B(a.interceptor,babelHelpers.assertThisInitialized(a));a.implementation=c;a.dispatcherFunc=a.original;a.setOriginal(c);return a}var c=a.prototype;c.getOriginal=function(){return this.original};c.setOriginal=function(a){if(this.original===a)return;this.original=a;this.customFunc||(this.implementation=a);q(a,this.interceptor);B(a,this);this.updateDispatcherFunc()};c.setCustom=function(a){this.customFunc=a,this.implementation=a,this.updateDispatcherFunc()};c.updateDispatcherFunc=function(){var b=0;b|=this.onBeforeCallMapper?8:0;b|=this.onBeforeCallObserver?4:0;b|=this.onAfterCallMapper?2:0;b|=this.onAfterCallObserver?1:0;b|=this.onBeforeAndAterCallMapper?16:0;var c=a.dispatcherCtors[b];d("hyperionGlobals").assert(!!c,"unhandled interceptor state "+b);this.dispatcherFunc=c(this)};c.onBeforeCallMapperAdd=function(a){this.onBeforeCallMapper||(this.onBeforeCallMapper=new fa(),this.updateDispatcherFunc());return this.onBeforeCallMapper.add(a)};c.onBeforeCallMapperRemove=function(a){var b;((b=this.onBeforeCallMapper)==null?void 0:b.remove(a))&&(this.onBeforeCallMapper.hasCallback()||(this.onBeforeCallMapper=null),this.updateDispatcherFunc());return a};c.onBeforeCallObserverAdd=function(a){this.onBeforeCallObserver||(this.onBeforeCallObserver=new ga(),this.updateDispatcherFunc());return this.onBeforeCallObserver.add(a)};c.onBeforeCallObserverRemove=function(a){var b;((b=this.onBeforeCallObserver)==null?void 0:b.remove(a))&&(this.onBeforeCallObserver.hasCallback()||(this.onBeforeCallObserver=null),this.updateDispatcherFunc());return a};c.onAfterCallMapperAdd=function(a){this.onAfterCallMapper||(this.onAfterCallMapper=new ha(),this.updateDispatcherFunc());return this.onAfterCallMapper.add(a)};c.onAfterCallMapperRemove=function(a){var b;((b=this.onAfterCallMapper)==null?void 0:b.remove(a))&&(this.onAfterCallMapper.hasCallback()||(this.onAfterCallMapper=null),this.updateDispatcherFunc());return a};c.onAfterCallObserverAdd=function(a){this.onAfterCallObserver||(this.onAfterCallObserver=new ia(),this.updateDispatcherFunc());return this.onAfterCallObserver.add(a)};c.onAfterCallObserverRemove=function(a){var b;((b=this.onAfterCallObserver)==null?void 0:b.remove(a))&&this.updateDispatcherFunc();return a};c.onBeforeAndAfterCallMapperAdd=function(a){this.onBeforeAndAterCallMapper||(this.onBeforeAndAterCallMapper=new ja(),this.updateDispatcherFunc());return this.onBeforeAndAterCallMapper.add(a)};c.onBeforeAndAfterCallMapperRemove=function(a){var b;((b=this.onBeforeAndAterCallMapper)==null?void 0:b.remove(a))&&(this.onBeforeAndAterCallMapper.hasCallback()||(this.onBeforeAndAterCallMapper=null),this.updateDispatcherFunc());return a};c.getData=function(a){var b;return(b=this.data)==null?void 0:b[a]};c.setData=function(a,b){this.data||(this.data={}),this.data[a]=b};c.testAndSet=function(a){var b=this.getData(a)||!1;b||this.setData(a,!0);return b};return a}(m);z.dispatcherCtors=function(){var a;a=(a={},a[0]=function(a){var b;return(b=a.customFunc)!=null?b:a.original},a[1]=function(a){return function(){var b;b=a.implementation.apply(this,arguments);a.onAfterCallObserver.call.call(this,b);return b}},a[2]=function(a){return function(){var b;b=a.implementation.apply(this,arguments);b=a.onAfterCallMapper.call.call(this,b);return b}},a[3]=function(a){return function(){var b;b=a.implementation.apply(this,arguments);b=a.onAfterCallMapper.call.call(this,b);a.onAfterCallObserver.call.call(this,b);return b}},a[4]=function(a){return function(){var b;a.onBeforeCallObserver.call.apply(this,arguments)||(b=a.implementation.apply(this,arguments));return b}},a[5]=function(a){return function(){var b;a.onBeforeCallObserver.call.apply(this,arguments)||(b=a.implementation.apply(this,arguments),a.onAfterCallObserver.call.call(this,b));return b}},a[6]=function(a){return function(){var b;a.onBeforeCallObserver.call.apply(this,arguments)||(b=a.implementation.apply(this,arguments),b=a.onAfterCallMapper.call.call(this,b));return b}},a[7]=function(a){return function(){var b;a.onBeforeCallObserver.call.apply(this,arguments)||(b=a.implementation.apply(this,arguments),b=a.onAfterCallMapper.call.call(this,b),a.onAfterCallObserver.call.call(this,b));return b}},a[8]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);return b}},a[9]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);a.onAfterCallObserver.call.call(this,b);return b}},a[10]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);b=a.onAfterCallMapper.call.call(this,b);return b}},a[11]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);b=a.onAfterCallMapper.call.call(this,b);a.onAfterCallObserver.call.call(this,b);return b}},a[12]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);a.onBeforeCallObserver.call.apply(this,c)||(b=a.implementation.apply(this,c));return b}},a[13]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);a.onBeforeCallObserver.call.apply(this,c)||(b=a.implementation.apply(this,c),a.onAfterCallObserver.call.call(this,b));return b}},a[14]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);a.onBeforeCallObserver.call.apply(this,c)||(b=a.implementation.apply(this,c),b=a.onAfterCallMapper.call.call(this,b));return b}},a[15]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);a.onBeforeCallObserver.call.apply(this,c)||(b=a.implementation.apply(this,c),b=a.onAfterCallMapper.call.call(this,b),a.onAfterCallObserver.call.call(this,b));return b}},a[16]=function(a){return function(){var b,c=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,arguments);b=c.call(this,b);return b}},a[17]=function(a){return function(){var b,c=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,arguments);a.onAfterCallObserver.call.call(this,b);b=c.call(this,b);return b}},a[18]=function(a){return function(){var b,c=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,arguments);b=a.onAfterCallMapper.call.call(this,b);b=c.call(this,b);return b}},a[19]=function(a){return function(){var b,c=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,arguments);b=a.onAfterCallMapper.call.call(this,b);a.onAfterCallObserver.call.call(this,b);b=c.call(this,b);return b}},a[20]=function(a){return function(){var b;if(!a.onBeforeCallObserver.call.apply(this,arguments)){var c=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,arguments);b=c.call(this,b)}return b}},a[21]=function(a){return function(){var b;if(!a.onBeforeCallObserver.call.apply(this,arguments)){var c=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,arguments);a.onAfterCallObserver.call.call(this,b);b=c.call(this,b)}return b}},a[22]=function(a){return function(){var b;if(!a.onBeforeCallObserver.call.apply(this,arguments)){var c=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,arguments);b=a.onAfterCallMapper.call.call(this,b);b=c.call(this,b)}return b}},a[23]=function(a){return function(){var b;if(!a.onBeforeCallObserver.call.apply(this,arguments)){var c=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,arguments);b=a.onAfterCallMapper.call.call(this,b);a.onAfterCallObserver.call.call(this,b);b=c.call(this,b)}return b}},a[24]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments),d=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);b=d.call(this,b);return b}},a[25]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments),d=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);a.onAfterCallObserver.call.call(this,b);b=d.call(this,b);return b}},a[26]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments),d=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);b=a.onAfterCallMapper.call.call(this,b);b=d.call(this,b);return b}},a[27]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments),d=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);b=a.onAfterCallMapper.call.call(this,b);a.onAfterCallObserver.call.call(this,b);b=d.call(this,b);return b}},a[28]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);if(!a.onBeforeCallObserver.call.apply(this,c)){var d=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);b=d.call(this,b)}return b}},a[29]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);if(!a.onBeforeCallObserver.call.apply(this,c)){var d=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);a.onAfterCallObserver.call.call(this,b);b=d.call(this,b)}return b}},a[30]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);if(!a.onBeforeCallObserver.call.apply(this,c)){var d=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);b=a.onAfterCallMapper.call.call(this,b);b=d.call(this,b)}return b}},a[31]=function(a){return function(){var b,c=a.onBeforeCallMapper.call.call(this,arguments);if(!a.onBeforeCallObserver.call.apply(this,c)){var d=a.onBeforeAndAterCallMapper.call.call(this,arguments);b=a.implementation.apply(this,c);b=a.onAfterCallMapper.call.call(this,b);a.onAfterCallObserver.call.call(this,b);b=d.call(this,b)}return b}},a);return a}();function A(a){return a==null?void 0:a[x]}function B(a,b){a[x]=b}function C(a,b,c,e){b===void 0&&(b=!1);e===void 0&&(e="_annonymous");d("hyperionGlobals").assert(typeof a==="function","cannot intercept non-function input");var f=A(a);f||(f=c?new c(e,a,b):new z(e,a,b));return f}var D=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,d,e){d===void 0&&(d=!1);a=b.call(this,a,void 0,d)||this;a.interceptProperty(c.targetPrototype,!1,e);a.status!==1&&c.addPendingPropertyInterceptor(babelHelpers.assertThisInitialized(a));return a}var c=a.prototype;c.interceptProperty=function(a,b,c){var d;c=(d=c)!=null?d:n(a,this.name);if(b){var e;c?c.writable&&(c.value||Object.prototype.hasOwnProperty.call(c,"value"))&&(e=c.value,delete c.value,delete c.writable,c.get=function(){return e},c.set=function(a){e=a},c.configurable=!0):c={get:function(){return e},set:function(a){e=a},enumerable:!0,configurable:!0,container:a}}if(c)if(c.value)this.setOriginal(c.value),c.value=this.interceptor,o(c.container,this.name,c),this.status=1;else if(c.get||c.set){var f=this;d=c;var g=d.get,h=d.set;g&&(c.get=function(){var a=g.call(this);if(typeof a!=="function")return a;a!==f.interceptor&&f.setOriginal(a);return f.interceptor},B(c.get,f));h&&(c.set=function(a){h.call(this,f.interceptor);a!==f.interceptor&&a!==f.original&&f.setOriginal(a);return f.interceptor},B(c.set,f));o(c.container,this.name,c);this.status=c.configurable?1:4}else Object.prototype.hasOwnProperty.call(c,"value")&&(this.status=1);else this.status=2};c.interceptObjectOwnProperties=function(a){this.interceptProperty(a,!0)};return a}(z);function E(a,b){b=n(b.targetPrototype,a);var c;if(b){c=A(b.value);if(!c){var e=A(b.get),f=A(b.set);d("hyperionGlobals").assert(!(e&&f)||e===f,"Getter/Setter of method "+a+" have differnt interceptors");c=e!=null?e:f}b.interceptor=c}return b}function F(a,b,c,d){var e;c===void 0&&(c=!1);var f=E(a,b);return(e=f==null?void 0:f.interceptor)!=null?e:new(d!=null?d:D)(a,b,c,f)}function G(a){var b=function(){var b;switch(arguments.length){case 0:b=new a();break;case 1:b=new a(arguments[0]);break;case 2:b=new a(arguments[0],arguments[1]);break;case 3:b=new a(arguments[0],arguments[1],arguments[2]);break;case 4:b=new a(arguments[0],arguments[1],arguments[2],arguments[3]);break;case 5:b=new a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4]);break;case 6:b=new a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]);break;default:throw"Unsupported case!"}return b};q(a,b);return b}var ka=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c){a=b.call(this,a,c,!0)||this;a.ctorInterceptor=null;return a}var c=a.prototype;c.setOriginal=function(a){this.ctorInterceptor=G(a);return b.prototype.setOriginal.call(this,this.ctorInterceptor)};return a}(z);function la(a,b){b===void 0&&(b="_annonymousCtor");return C(a,!0,ka,b)}var ma=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,d){a=b.call(this,a,c,!0,d)||this;a.ctorInterceptor=null;return a}var c=a.prototype;c.setOriginal=function(a){this.ctorInterceptor=G(a);return b.prototype.setOriginal.call(this,this.ctorInterceptor)};return a}(D);function H(a,b){var c,d=E(a,b);return(c=d==null?void 0:d.interceptor)!=null?c:new ma(a,b,d)}function I(a,b){return(b==null?void 0:b.useCaseInsensitivePropertyName)?(""+a).toLocaleLowerCase():a}var J=function(){function a(a,b){this.onBeforInterceptObj=new((l||(l=d("hyperionHook"))).Hook)();this.onAfterInterceptObj=new l.Hook();this.targetPrototype=a;this.parentShadowPrototype=b;this.extension=Object.create((a=b==null?void 0:b.extension)!=null?a:null);if(this.parentShadowPrototype){b=this.targetPrototype;a=this.parentShadowPrototype.targetPrototype;var c=!1;while(b&&!c)c=b===a,b=Object.getPrototypeOf(b);d("hyperionGlobals").assert(c,"Invalid prototype chain")}}var b=a.prototype;b.callOnBeforeInterceptObject=function(a){var b;(b=this.parentShadowPrototype)==null?void 0:b.callOnBeforeInterceptObject(a);(b=this.onBeforInterceptObj)==null?void 0:b.call(a)};b.callOnAfterInterceptObject=function(a){var b;(b=this.parentShadowPrototype)==null?void 0:b.callOnAfterInterceptObject(a);(b=this.onAfterInterceptObj)==null?void 0:b.call(a)};b.interceptObjectItself=function(a){var b;(b=this.parentShadowPrototype)==null?void 0:b.interceptObjectItself(a);if(this.pendingPropertyInterceptors)for(b of this.pendingPropertyInterceptors)b.interceptObjectOwnProperties(a)};b.interceptObject=function(a){this.callOnBeforeInterceptObject(a),this.interceptObjectItself(a),this.callOnAfterInterceptObject(a)};b.addPendingPropertyInterceptor=function(a){this.pendingPropertyInterceptors||(this.pendingPropertyInterceptors=[]),this.pendingPropertyInterceptors.push(a)};b.getVirtualProperty=function(a){var b=this.extension;a=I(a,b);return b[a]};b.setVirtualProperty=function(a,b){var c=this.extension;a=I(a,c);c[a]=b;return b};b.removeVirtualPropery=function(a,b){var c=this.extension;a=I(a,c);c[a]===b&&delete c[a]};return a}(),K=function(){function a(){}var b=a.prototype;b.getExports=function(a){return null};b.updateExports=function(a,b,c,d){};return a}(),na=function(b){babelHelpers.inheritsLoose(a,b);function a(a){var c;c=b.call(this)||this;c.$WebpackModuleRuntime1=a;return c}var c=a.prototype;c.getExports=function(a){var b=this,c=new RegExp(a+"(?:/index)?[.]js$");a=Object.keys(this.$WebpackModuleRuntime1).filter(function(a){return c.test(a)}).map(function(a){return b.$WebpackModuleRuntime1[a]});return a[0].exports};return a}(K),oa=function(b){babelHelpers.inheritsLoose(a,b);function a(a){var c;c=b.call(this)||this;c.$MetaModuleRuntime1=a;return c}var c=a.prototype;c.updateExports=function(a,b,c,d){c["default"]!=null&&(this.$MetaModuleRuntime1.modulesMap[a].defaultExport=b["default"])};return a}(K),L=function(){if(typeof __webpack_module_cache__==="object")return new na(__webpack_module_cache__);else if(typeof b==="function")try{var a=b("__debug");if(typeof a==="object")return new oa(a)}catch(a){}return new K()}();function M(a,b,c,d){var e=b,f=L.getExports(a);f&&f!==e&&(e=f);f=new J(e,null);e={};for(var g=0;g<c.length;++g){var h=c[g];e[h]=F(h,f)}L.updateExports(a,b,e,d);N(a,b,e,d);return e}function N(a,b,c,e){if(Array.isArray(e)){var f=Object.keys(c);for(var g=0;g<f.length;++g){var h=f[g];b[h]!==c[h].interceptor&&e.push(h)}d("hyperionGlobals").assert(e.length===0,e.map(function(b){return"could not intercept "+a+"."+b}).join("\n"))}}var pa=Object.freeze({__proto__:null,interceptModuleExports:M,validateModuleInterceptor:N});h=(h=c(d("hyperionGlobals").globalScope))!=null?h:new J(d("hyperionGlobals").globalScope,null);var O=F("setInterval",h),P=F("setTimeout",h);h=H("Promise",h);var qa=Object.freeze({__proto__:null,IPromiseConstructor:h,setInterval:O,setTimeout:P}),Q=Object.getPrototypeOf((k||(k=b("Promise"))).resolve());i=(i=c(Q))!=null?i:e(Q,new J(Q,null));Q=h;h=F("then",i);var R=F("catch",i),S=F("finally",i);j=(j=c(k||(k=b("Promise"))))!=null?j:e(k||(k=b("Promise")),new J(k||(k=b("Promise")),null));var T=F("all",j),U=F("allSettled",j),V=F("any",j),W=F("race",j),X=F("reject",j);j=F("resolve",j);S=Object.freeze({__proto__:null,Catch:R,Finally:S,IPromisePrototype:i,all:T,allSettled:U,any:V,constructor:Q,race:W,reject:X,resolve:j,then:h});var Y="__attributeInterceptor";m=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d){var e;e=a.call(this,b)||this;e.getter=new z(b,c);e.setter=new z(b,d);e.getter.setData(Y,babelHelpers.assertThisInitialized(e));e.setter.setData(Y,babelHelpers.assertThisInitialized(e));return e}return b}(m);var Z=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,d){a=b.call(this,a)||this;a.interceptProperty(c.targetPrototype,!1,d);a.status!==1&&c.addPendingPropertyInterceptor(babelHelpers.assertThisInitialized(a));return a}var c=a.prototype;c.interceptProperty=function(a,b,c){var d;c=(d=c)!=null?d:n(a,this.name);if(b){var e;d=function(){return e};b=function(a){e=a};c?c.value&&c.writable&&(e=c.value,delete c.value,delete c.writable,c.get=d,c.set=b,c.configurable=!0):c={get:d,set:b,enumerable:!0,configurable:!0,container:a}}if(c)if(c.get||c.set){d=c;b=d.get;a=d.set;b&&(this.getter.setOriginal(b),c.get=this.getter.interceptor);a&&(this.setter.setOriginal(a),c.set=this.setter.interceptor);o(c.container,this.name,c);this.status=c.configurable?1:4}else c.value&&(this.status=3);else this.status=2};c.interceptObjectOwnProperties=function(a){return this.interceptProperty(a,!0)};return a}(m);function ra(a,b){b=n(b.targetPrototype,a);if(b){var c=A(b.get),e=A(b.set);c=c==null?void 0:c.getData(Y);e=e==null?void 0:e.getData(Y);d("hyperionGlobals").assert(!(c&&e)||c===e,"Getter/Setter of attribute "+a+" have differnt interceptors");b.interceptor=c!=null?c:e}return b}function $(a,b,c){var d,e=ra(a,b);return(d=e==null?void 0:e.interceptor)!=null?d:new c(a,b,e)}function sa(a,b){return $(a,b,Z)}g.AttributeInterceptor=Z;g.AttributeInterceptorBase=m;g.Catch=R;g.IGlobalThis=qa;g.IPromise=S;g.IPromisePrototype=i;g.IRequire=pa;g.ShadowPrototype=J;g.all=T;g.allSettled=U;g.any=V;g.constructor=Q;g.getFunctionInterceptor=A;g.getObjectExtension=w;g.getOwnShadowPrototypeOf=c;g.getVirtualPropertyValue=f;g.intercept=v;g.interceptAttribute=sa;g.interceptAttributeBase=$;g.interceptConstructor=la;g.interceptConstructorMethod=H;g.interceptFunction=C;g.interceptMethod=F;g.interceptModuleExports=M;g.race=W;g.registerShadowPrototype=e;g.registerShadowPrototypeGetter=a;g.reject=X;g.resolve=j;g.setInterval=O;g.setTimeout=P;g.setVirtualPropertyValue=ea;g.then=h;g.validateModuleInterceptor=N}),98);
__d("hyperionDOM",["hyperionCore","hyperionGlobals"],(function(a,b,c,d,e,f,g){var h,i=new Map(),j=new Map();(h||(h=d("hyperionCore"))).registerShadowPrototypeGetter(function(a){if(a instanceof Node){var b;return(b=j.get(a.nodeName))!=null?b:i.get(a.nodeType)}return null});c=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,e){var f;f=(f=e==null?void 0:e.targetPrototype)!=null?f:b==null?void 0:b.prototype;if(!f&&e){b=e.sampleObject;var g=e.nodeName,l=e.nodeType;b=b;if(!b&&l)switch(l){case window.document.DOCUMENT_NODE:b=window.document;break;case window.document.ELEMENT_NODE:b=k;break;default:d("hyperionGlobals").assert(!1,"Unsupported and unexpected nodeType "+l);break}!b&&g&&(b=window.document.createElement(g));b&&(f=Object.getPrototypeOf(b))}d("hyperionGlobals").assert(f&&typeof f==="object","Cannot create shadow prototype for undefined");l=a.call(this,f,c)||this;if(e){g=e.nodeName;b=e.nodeType;g&&j.set(g.toUpperCase(),babelHelpers.assertThisInitialized(l));b&&i.set(b,babelHelpers.assertThisInitialized(l))}if((e==null?void 0:e.registerOnPrototype)&&f)try{(h||(h=d("hyperionCore"))).registerShadowPrototype(f,babelHelpers.assertThisInitialized(l))}catch(a){}return l}return b}(h.ShadowPrototype);var k=window.document.head;function l(a,b){a=(a=(h||(h=d("hyperionCore"))).getObjectExtension(a,!0))==null?void 0:a.shadowPrototype;return!a?null:a.getVirtualProperty(b)}e=new c(Event,null,{sampleObject:new Event("tmp"),registerOnPrototype:!0});f=h.interceptMethod("stopPropagation",e);e=Object.freeze({__proto__:null,IEventPrototype:e,stopPropagation:f});var m=new c(EventTarget,null,{sampleObject:k}),n=h.interceptMethod("addEventListener",m),o=h.interceptMethod("dispatchEvent",m),p=h.interceptMethod("removeEventListener",m);o=Object.freeze({__proto__:null,IEventTargetPrototype:m,addEventListener:n,dispatchEvent:o,removeEventListener:p});var q=new c(Node,m,{sampleObject:k}),r=h.interceptMethod("appendChild",q),s=h.interceptMethod("cloneNode",q,!0),t=h.interceptMethod("insertBefore",q),u=h.interceptMethod("removeChild",q),v=h.interceptMethod("replaceChild",q);s=Object.freeze({__proto__:null,INodePrototype:q,appendChild:r,cloneNode:s,insertBefore:t,removeChild:u,replaceChild:v});var w=new c(CSSStyleDeclaration,null,{sampleObject:k.style});w.extension.useCaseInsensitivePropertyName=!0;var x=h.interceptMethod("getPropertyValue",w),y=h.interceptMethod("removeProperty",w),z=h.interceptMethod("setProperty",w);w=Object.freeze({__proto__:null,ICSSStyleDeclarationPrototype:w,getPropertyValue:x,removeProperty:y,setProperty:z});x=new c(Attr,q,{sampleObject:k.attributes[0],nodeType:document.ATTRIBUTE_NODE});var A=h.interceptAttribute("value",x);function aa(){A.getter.setCustom(function(){var a=this,b=a.ownerElement;if(b){var c=l(b,a.name);if(c){c=c.getRawValue(b);if(c!=null)return c}}return A.getter.getOriginal().call(a)}),A.setter.setCustom(function(a){var b=this,c=b.ownerElement;if(c){var d=l(c,b.name);if(d)return d.setRawValue(c,a)}return A.setter.getOriginal().call(b,a)})}var B=new c(Element,q,{sampleObject:k,nodeType:document.ELEMENT_NODE});B.extension.useCaseInsensitivePropertyName=!0;var C=h.interceptMethod("getAttribute",B),D=h.interceptMethod("getAttributeNS",B),E=h.interceptMethod("setAttribute",B),F=h.interceptMethod("setAttributeNS",B),G=h.interceptMethod("setAttributeNode",B),H=h.interceptMethod("setAttributeNodeNS",B);function ba(){C.setCustom(function(a){var b=l(this,a);if(b){var c=b.getRawValue(this);if(c!==null)return c}return C.getOriginal().apply(this,arguments)});E.setCustom(function(b,a){var c=l(this,b);if(c)return c.setRawValue(this,a);else return E.getOriginal().apply(this,arguments)});D.setCustom(function(a,b){var c=l(this,b);if(c){var d=c.getRawValue(this);if(d!==null)return d}return D.getOriginal().apply(this,arguments)});F.setCustom(function(b,c,a){var d=l(this,c);if(d)return d.setRawValue(this,a);else return F.getOriginal().apply(this,arguments)});function a(a){return function(b){var c,d=!b.ownerElement,e=l(this,b.name);if(d&&e){d=b.value;c=a.call(this,b);e.setRawValue(this,d)}else c=a.call(this,b);return c}}G.setCustom(a(G.getOriginal()));H.setCustom(a(H.getOriginal()))}var ca=function(){function a(a,b){this.rawValue=a,this.processedValue=b}var b=a.prototype;b.getRawValue=function(a){return this.rawValue.getter.interceptor.call(a)};b.setRawValue=function(b,a){return this.rawValue.setter.interceptor.call(b,a)};b.getProcessedValue=function(a){return this.processedValue.getter.interceptor.call(a)};b.setProcessedValue=function(b,a){return this.processedValue.setter.interceptor.call(b,a)};return a}(),I=function(){aa(),ba(),I=function(){}},da=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c,e){c=b.call(this,a,c,e)||this;c.raw=new((h||(h=d("hyperionCore"))).AttributeInterceptorBase)(a,function(){return C.getOriginal().call(this,a)},function(b){return E.getOriginal().call(this,a,b)});B.setVirtualProperty(a,new ca(c.raw,babelHelpers.assertThisInitialized(c)));I();return c}return a}(h.AttributeInterceptor);function a(a,b){return(h||(h=d("hyperionCore"))).interceptAttributeBase(a,b,da)}y=B;z=h.interceptMethod("after",y);x=h.interceptMethod("append",y);q=h.interceptMethod("before",y);var J=h.interceptMethod("getAttributeNames",y),K=h.interceptMethod("getAttributeNode",y,!0),L=h.interceptMethod("getAttributeNodeNS",y,!0),M=h.interceptMethod("getBoundingClientRect",y),N=h.interceptMethod("getClientRects",y),O=h.interceptMethod("getElementsByClassName",y),P=h.interceptMethod("getElementsByTagName",y),Q=h.interceptMethod("getElementsByTagNameNS",y),R=h.interceptMethod("hasAttribute",y),S=h.interceptMethod("hasAttributeNS",y),T=h.interceptMethod("hasAttributes",y),ea=h.interceptMethod("insertAdjacentElement",y),U=h.interceptMethod("insertAdjacentHTML",y),V=h.interceptMethod("insertAdjacentText",y),fa=h.interceptMethod("prepend",y),ga=h.interceptMethod("remove",y),W=h.interceptMethod("removeAttribute",y),X=h.interceptMethod("removeAttributeNode",y),Y=h.interceptMethod("removeAttributeNS",y),ha=h.interceptMethod("replaceChildren",y),ia=h.interceptMethod("replaceWith",y),Z=h.interceptMethod("toggleAttribute",y),$=a("id",y),ja=h.interceptAttribute("innerHTML",y);J=Object.freeze({__proto__:null,IElementtPrototype:y,after:z,append:x,before:q,getAttribute:C,getAttributeNS:D,getAttributeNames:J,getAttributeNode:K,getAttributeNodeNS:L,getBoundingClientRect:M,getClientRects:N,getElementsByClassName:O,getElementsByTagName:P,getElementsByTagNameNS:Q,hasAttribute:R,hasAttributeNS:S,hasAttributes:T,id:$,innerHTML:ja,insertAdjacentElement:ea,insertAdjacentHTML:U,insertAdjacentText:V,prepend:fa,remove:ga,removeAttribute:W,removeAttributeNS:Y,removeAttributeNode:X,replaceChildren:ha,replaceWith:ia,setAttribute:E,setAttributeNS:F,setAttributeNode:G,setAttributeNodeNS:H,toggleAttribute:Z});K=new c(HTMLElement,y,{sampleObject:k,nodeType:document.ELEMENT_NODE});L=h.interceptAttribute("style",K);M=Object.freeze({__proto__:null,IHTMLElementtPrototype:K,style:L});N=new c(HTMLInputElement,K,{sampleObject:document.createElement("input"),nodeType:document.ELEMENT_NODE});O=h.interceptAttribute("checked",N);P=Object.freeze({__proto__:null,IHTMLInputElementPrototype:N,checked:O});var ka=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}return a}(h.AttributeInterceptor);function b(a,b){return(h||(h=d("hyperionCore"))).interceptAttributeBase(a,b,ka)}Q=new c(Window,m,{targetPrototype:window,registerOnPrototype:!0});R=h.interceptMethod("fetch",Q);S=h.interceptMethod("requestAnimationFrame",Q);T=h.interceptMethod("requestIdleCallback",Q);$=h.interceptConstructorMethod("IntersectionObserver",Q);U=h.interceptConstructorMethod("MutationObserver",Q);V=b("onerror",Q);W=b("ondevicemotion",Q);Y=b("ondeviceorientation",Q);X=b("onorientationchange",Q);Z=Object.freeze({__proto__:null,IWindowPrototype:Q,IntersectionObserver:$,MutationObserver:U,fetch:R,ondevicemotion:W,ondeviceorientation:Y,onerror:V,onorientationchange:X,requestAnimationFrame:S,requestIdleCallback:T});L=new c(History,null,{sampleObject:window.history,registerOnPrototype:!0});K=h.interceptMethod("back",L);N=h.interceptMethod("forward",L);$=h.interceptMethod("go",L);U=h.interceptMethod("pushState",L);W=h.interceptMethod("replaceState",L);Y=Object.freeze({__proto__:null,IHistoryPrototype:L,back:K,forward:N,go:$,pushState:U,replaceState:W});V=new c(XMLHttpRequest,m,{sampleObject:new XMLHttpRequest(),registerOnPrototype:!0});X=h.interceptConstructorMethod("XMLHttpRequest",Q);L=h.interceptMethod("open",V);K=h.interceptMethod("send",V);N=b("onabort",V);$=b("onerror",V);c=b("onload",V);m=b("onloadend",V);Q=b("onloadstart",V);var la=b("onprogress",V),ma=b("readystatechange",V);b=b("ontimeout",V);g.ICSSStyleDeclaration=w;g.IElement=J;g.IElementtPrototype=y;g.IEvent=e;g.IEventTarget=o;g.IHTMLElement=M;g.IHTMLInputElement=P;g.IHistory=Y;g.INode=s;g.IWindow=Z;g.addEventListener=n;g.after=z;g.append=x;g.appendChild=r;g.before=q;g.checked=O;g.constructor=X;g.fetch=R;g.innerHTML=ja;g.insertAdjacentElement=ea;g.insertBefore=t;g.interceptElementAttribute=a;g.onabort=N;g.onerror=$;g.onload=c;g.onloadend=m;g.onloadstart=Q;g.onprogress=la;g.ontimeout=b;g.open=L;g.prepend=fa;g.pushState=U;g.readystatechange=ma;g.remove=ga;g.removeChild=u;g.removeEventListener=p;g.replaceChild=v;g.replaceChildren=ha;g.replaceState=W;g.replaceWith=ia;g.requestAnimationFrame=S;g.requestIdleCallback=T;g.send=K;g.setAttribute=E;g.stopPropagation=f}),98);
__d("Hyperion",["Env","ExecutionEnvironment","hyperionCore","hyperionDOM"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k;(j||(j=c("ExecutionEnvironment"))).isInBrowser&&!(j||c("ExecutionEnvironment")).isInWorker&&(k||(k=c("Env"))).loadHyperion===!0&&(h||(h=d("hyperionCore"))).intercept(a,(i||(i=d("hyperionDOM"))).IWindow.IWindowPrototype)}),34);
__d("cancelAnimationFramePolyfill",[],(function(a,b,c,d,e,f){"use strict";b=a.__fbNativeCancelAnimationFrame||a.cancelAnimationFrame||a.webkitCancelAnimationFrame||a.mozCancelAnimationFrame||a.oCancelAnimationFrame||a.msCancelAnimationFrame||a.clearTimeout;c=b;f["default"]=c}),66);
__d("cancelAnimationFrame",["cancelAnimationFramePolyfill"],(function(a,b,c,d,e,f,g){function a(a){c("cancelAnimationFramePolyfill")(a)}g["default"]=a}),98);
__d("TimerStorage",[],(function(a,b,c,d,e,f){a={ANIMATION_FRAME:"ANIMATION_FRAME",IDLE_CALLBACK:"IDLE_CALLBACK",IMMEDIATE:"IMMEDIATE",INTERVAL:"INTERVAL",TIMEOUT:"TIMEOUT"};var g={};Object.keys(a).forEach(function(a){return g[a]={}});b=babelHelpers["extends"]({},a,{set:function(a,b){g[a][b]=!0},unset:function(a,b){delete g[a][b]},clearAll:function(a,b){Object.keys(g[a]).forEach(b),g[a]={}},getStorages:function(){return{}}});c=b;f["default"]=c}),66);
__d("requestAnimationFrameAcrossTransitions",["TimeSlice","requestAnimationFramePolyfill"],(function(a,b,c,d,e,f,g){function a(a){a=c("TimeSlice").guard(a,"requestAnimationFrame",{propagationType:c("TimeSlice").PropagationType.CONTINUATION});return c("requestAnimationFramePolyfill")(a)}g["default"]=a}),98);
__d("requestAnimationFrame",["TimeSlice","TimerStorage","requestAnimationFrameAcrossTransitions"],(function(a,b,c,d,e,f,g){function a(a){function b(b){c("TimerStorage").unset(c("TimerStorage").ANIMATION_FRAME,d),a(b)}c("TimeSlice").copyGuardForWrapper(a,b);b.__originalCallback=a;var d=c("requestAnimationFrameAcrossTransitions")(b);c("TimerStorage").set(c("TimerStorage").ANIMATION_FRAME,d);return d}g["default"]=a}),98);
__d("setInterval",["cr:7388"],(function(a,b,c,d,e,f,g){g["default"]=b("cr:7388")}),98);
__d("replaceNativeTimer",["Hyperion","cancelAnimationFrame","clearInterval","clearTimeout","requestAnimationFrame","setInterval","setTimeout"],(function(a,b,c,d,e,f){b("Hyperion");a.__fbNativeSetTimeout=a.setTimeout;a.__fbNativeClearTimeout=a.clearTimeout;a.__fbNativeSetInterval=a.setInterval;a.__fbNativeClearInterval=a.clearInterval;a.__fbNativeRequestAnimationFrame=a.requestAnimationFrame;a.__fbNativeCancelAnimationFrame=a.cancelAnimationFrame;b("setTimeout").nativeBackup=a.setTimeout;b("clearTimeout").nativeBackup=a.clearTimeout;b("setInterval").nativeBackup=a.setInterval;b("clearInterval").nativeBackup=a.clearInterval;b("requestAnimationFrame").nativeBackup=a.requestAnimationFrame;b("cancelAnimationFrame").nativeBackup=a.cancelAnimationFrame;a.setTimeout=b("setTimeout");a.clearTimeout=b("clearTimeout");a.setInterval=b("setInterval");a.clearInterval=b("clearInterval");a.requestAnimationFrame=b("requestAnimationFrame");a.cancelAnimationFrame=b("cancelAnimationFrame");function c(){}e.exports=c}),null);
__d("CometPreludeCritical",["Bootloader","CometPreludeCriticalRequireConds","CometResourceScheduler","DeferredJSResourceScheduler","Env","HasteResponse","HasteSupportData","JSScheduler","ReactDOMServerExternalRuntime","Run","ScheduledApplyEach","ScheduledServerJS","ScheduledServerJSDefine","ScheduledServerJSWithCSS","ServerJS","bootstrapWebSession","maybeDisableAnimations","qplAnnotationsIntServerJS","qplAnnotationsStringServerJS","qplTagServerJS","qplTimingsServerJS","replaceNativeTimer"],(function(a,b,c,d,e,f){"use strict";var g,h;b("Bootloader");b("CometPreludeCriticalRequireConds");b("CometResourceScheduler");b("DeferredJSResourceScheduler");g||(g=b("Env"));b("HasteResponse");b("HasteSupportData");h||(h=b("JSScheduler"));b("Run");b("ScheduledApplyEach");b("ScheduledServerJS");b("ScheduledServerJSWithCSS");b("ScheduledServerJSDefine");b("ServerJS");b("bootstrapWebSession");b("replaceNativeTimer");b("qplTimingsServerJS");b("qplTagServerJS");b("qplAnnotationsIntServerJS");b("qplAnnotationsStringServerJS");b("maybeDisableAnimations");b("ReactDOMServerExternalRuntime")}),null);
__d("ErrorGuardState",["fb-error"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("fb-error").ErrorGuardState}),98);
__d("ErrorNormalizeUtils",["fb-error"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("fb-error").ErrorNormalizeUtils}),98);
__d("ErrorSerializer",["fb-error"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("fb-error").ErrorSerializer}),98);
__d("ErrorUtils",["ErrorGuard","ErrorGuardState","ErrorNormalizeUtils","ErrorPubSub","ErrorSerializer","getErrorSafe"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l;j||b("ErrorGuardState");b("ErrorNormalizeUtils");k||(k=b("ErrorPubSub"));b("getErrorSafe");l||(l=b("ErrorGuard"));b("ErrorSerializer");a.getErrorSafe=c("getErrorSafe");a.ErrorGuard=h||(h=c("ErrorGuard"));a.ErrorSerializer=c("ErrorSerializer");d={history:(i||(i=c("ErrorPubSub"))).history,applyWithGuard:function(a,b,d,e,f,g){return(h||(h=c("ErrorGuard"))).applyWithGuard(a,b,d!=null?d:[],{name:f,onNormalizedError:e,deferredSource:g==null?void 0:g.deferredSource})},guard:function(a,b,d){a=(h||(h=c("ErrorGuard"))).guard(a,b!=null?{name:b}:null);d!=null&&(a=a.bind(d));return a},normalizeError:function(a){var b;return(b=c("ErrorNormalizeUtils").ifNormalizedError(a))!=null?b:c("ErrorNormalizeUtils").normalizeError(c("getErrorSafe")(a))}};a.ErrorUtils=d;e=d;typeof __t==="function"&&__t.setHandler&&__t.setHandler((i||(i=c("ErrorPubSub"))).reportError);g["default"]=e}),99);
__d("ServerJSPayloadListener",["FBLogger","ServerJS","err"],(function(a,b,c,d,e,f,g){"use strict";function h(a){if(!(a instanceof HTMLScriptElement))return;var b=a.dataset.contentLen;if(a.dataset.processed||a.textContent.length.toString()!==b)return;a.dataset.processed="1";b=null;try{b=JSON.parse(a.textContent);if(b==null)throw c("err")("ServerJS payload marked with data-sjs was parsed as null");new(c("ServerJS"))().handle(b)}catch(a){c("FBLogger")("serverjs_listener").catching(a).mustfix("ServerJS based data-sjs payload failed to parse and execute.")}}function b(){if(a.document==null)return;var b=document.querySelectorAll("script[data-sjs]:not([data-processed])");b.forEach(h)}g.process=b}),99);
__d("ServerJSPayloadListener_NEW",["FBLogger","ServerJS","err"],(function(a,b,c,d,e,f,g){"use strict";function h(a){if(!(a instanceof HTMLScriptElement))return;var b=a.dataset.contentLen;if(a.dataset.processed||a.textContent.length.toString()!==b)return;a.dataset.processed="1";b=null;try{b=JSON.parse(a.textContent);if(b==null)throw c("err")("ServerJS payload marked with data-sjs was parsed as null");new(c("ServerJS"))().handle(b)}catch(a){c("FBLogger")("serverjs_listener").catching(a).mustfix("ServerJS based data-sjs payload failed to parse and execute.")}}function b(){if(a.document==null)return;if(!window.Env||!window.Env.sjsListenerNew)return;var b=document.querySelectorAll("script[data-sjs]:not([data-processed])");b.forEach(h)}g.process=b}),99);
__d("TrustedTypesSecurityInfraLoggingDefaultPolicy_FOR_ROLLOUT_ONLY_DO_NOT_USE",["TrustedTypesUtils"],(function(a,b,c,d,e,f,g){"use strict";a={name:"security_infra_logging_FOR_ROLLOUT_ONLY_DO_NOT_USE",policy:{createScriptURL:function(a,b){return d("TrustedTypesUtils").noopAndLog(a)},createHTML:function(a,b){return d("TrustedTypesUtils").noopAndLog(a)},createScript:function(a,b){return d("TrustedTypesUtils").noopAndLog(a)}}};b=a;g["default"]=b}),98);
__d("TrustedTypesDefaultPolicy",["Env","TrustedTypes","TrustedTypesSecurityInfraLoggingDefaultPolicy_FOR_ROLLOUT_ONLY_DO_NOT_USE","TrustedTypesUtils"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||c("Env")).defaultTrustedTypesPolicyName==="security_infra_logging_FOR_ROLLOUT_ONLY_DO_NOT_USE"&&(d("TrustedTypesUtils").logInfo("A default Trusted Types policy for rollout is in use. To view violations see project `saf_web_trusted_types_rollout` in LogView."),c("TrustedTypes").createDefaultPolicy(c("TrustedTypesSecurityInfraLoggingDefaultPolicy_FOR_ROLLOUT_ONLY_DO_NOT_USE")))}),35);
__d("Visibility",["BaseEventEmitter","ExecutionEnvironment","TimeSlice"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;(h||(h=c("ExecutionEnvironment"))).canUseDOM&&(document.hidden!==void 0?(i="hidden",j="visibilitychange"):document.mozHidden!==void 0?(i="mozHidden",j="mozvisibilitychange"):document.msHidden!==void 0?(i="msHidden",j="msvisibilitychange"):document.webkitHidden!==void 0&&(i="webkitHidden",j="webkitvisibilitychange"));a=function(a){babelHelpers.inheritsLoose(b,a);function b(){var b,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];return(b=c=a.call.apply(a,[this].concat(e))||this,c.HIDDEN="hidden",c.VISIBLE="visible",c.hiddenKey=i,c.hiddenEvent=j,b)||babelHelpers.assertThisInitialized(c)}var d=b.prototype;d.isHidden=function(){return i?document[i]:!1};d.isSupported=function(){return(h||(h=c("ExecutionEnvironment"))).canUseDOM&&document.addEventListener&&j!==void 0};return b}(c("BaseEventEmitter"));var k=new a();k.isSupported()&&document.addEventListener(k.hiddenEvent,c("TimeSlice").guard(function(a){k.emit(k.isHidden()?k.HIDDEN:k.VISIBLE,{changeTime:a.timeStamp})},"visibility change"));b=k;g["default"]=b}),98);
__d("VisibilityListener",["Visibility","performanceNow"],(function(a,b,c,d,e,f,g){"use strict";var h,i=Date.now()-(h||(h=c("performanceNow")))(),j=[],k=!1,l=1e4;j.push({key:i,value:c("Visibility").isHidden()});function m(a,b){if(k||j.length>l){k=!0;return}j.push({key:a+i,value:b})}c("Visibility").addListener(c("Visibility").VISIBLE,function(a){m(a.changeTime,!1)});c("Visibility").addListener(c("Visibility").HIDDEN,function(a){m(a.changeTime,!0)});function n(a,b){if(k)return null;var d;for(a=j.length-1;a>=0;a--)if(j[a].key<=b){d=j.slice(0,a+1);break}if(d===void 0)return null;d[d.length-1].value!==c("Visibility").isHidden()&&(d[d.length]={key:b,value:c("Visibility").isHidden()});return d}function a(a,b){var d=n(a,b);if(!d)return null;if(d.length<2)return c("Visibility").isHidden()?b-a:0;var e=d.length-1;b=d[e].value?b-d[e].key:0;for(--e;e>0;e--)if(d[e].key>a)d[e].value&&(b+=d[e+1].key-d[e].key);else break;d[e].value&&(b=d[e+1].key-a);return b}function b(){return!0}g.getHiddenTimings=n;g.getHiddenTime=a;g.supported=b}),98);
__d("CometPreludeRunWhenReady",["ErrorUtils","ServerJSPayloadListener","ServerJSPayloadListener_NEW","TrustedTypesDefaultPolicy","VisibilityListener"],(function(a,b,c,d,e,f){"use strict";var g;b("TrustedTypesDefaultPolicy");g||(g=b("ErrorUtils"));b("VisibilityListener");b("ServerJSPayloadListener");b("ServerJSPayloadListener_NEW")}),null);
__d("CometPrelude",["CometPreludeCritical","CometPreludeRunWhenReady"],(function(a,b,c,d,e,f){"use strict";b("CometPreludeCritical"),b("CometPreludeRunWhenReady")}),null);