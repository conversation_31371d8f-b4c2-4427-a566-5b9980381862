.sidebar {
    width: 250px;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    overflow-y: auto;
    z-index: 1030;
}

.sidebar .nav-link {
    color: #6c757d;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease-in-out;
}

.sidebar .nav-link:hover {
    color: #000;
    background-color: rgba(34, 187, 234, 0.1);
}

.sidebar .nav-link.active {
    color: #22bbea;
    background-color: rgba(34, 187, 234, 0.1);
}

.sidebar .nav-link i {
    font-size: 1.1rem;
}

.sidebar hr {
    margin: 1rem 0;
    color: #dee2e6;
    opacity: 0.25;
}

/* Main content adjustment */
.main-content {
    margin-left: 250px;
    padding: 1.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }
}
