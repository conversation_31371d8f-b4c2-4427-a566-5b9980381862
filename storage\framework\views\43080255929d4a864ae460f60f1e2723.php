<?php $__env->startSection('title', 'Student Feedback - Cook Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0"><i class="bi bi-chat-dots me-2"></i>Student Feedback</h3>
                        <p class="mb-0 text-muted">View and analyze student feedback on meals</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-primary fs-6 me-3"><?php echo e($stats['total_feedback']); ?> Total Reviews</span>
                        <?php if($stats['total_feedback'] > 0): ?>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="confirmDeleteAll()">
                                <i class="bi bi-trash me-1"></i> Delete All Feedback
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Enhanced Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-funnel me-2"></i>Filter & Search Feedback</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="<?php echo e(route('cook.feedback')); ?>">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">Search in Comments & Suggestions</label>
                                <input type="text" class="form-control" id="search" name="search" value="<?php echo e(request('search')); ?>" placeholder="Search for keywords...">
                            </div>
                            <div class="col-md-3">
                                <label for="anonymous_filter" class="form-label">Student Identity</label>
                                <select class="form-control" id="anonymous_filter" name="anonymous_filter">
                                    <option value="">All Feedback</option>
                                    <option value="identified" <?php echo e(request('anonymous_filter') == 'identified' ? 'selected' : ''); ?>>Identified Students</option>
                                    <option value="anonymous" <?php echo e(request('anonymous_filter') == 'anonymous' ? 'selected' : ''); ?>>Anonymous Students</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="rating" class="form-label">Rating</label>
                                <select class="form-control" id="rating" name="rating">
                                    <option value="">All Ratings</option>
                                    <option value="5" <?php echo e(request('rating') == '5' ? 'selected' : ''); ?>>⭐⭐⭐⭐⭐ (5 Stars)</option>
                                    <option value="4" <?php echo e(request('rating') == '4' ? 'selected' : ''); ?>>⭐⭐⭐⭐ (4 Stars)</option>
                                    <option value="3" <?php echo e(request('rating') == '3' ? 'selected' : ''); ?>>⭐⭐⭐ (3 Stars)</option>
                                    <option value="2" <?php echo e(request('rating') == '2' ? 'selected' : ''); ?>>⭐⭐ (2 Stars)</option>
                                    <option value="1" <?php echo e(request('rating') == '1' ? 'selected' : ''); ?>>⭐ (1 Star)</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo e(request('date_from')); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo e(request('date_to')); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="meal_type" class="form-label">Meal Type</label>
                                <select class="form-control" id="meal_type" name="meal_type">
                                    <option value="">All Meals</option>
                                    <option value="breakfast" <?php echo e(request('meal_type') == 'breakfast' ? 'selected' : ''); ?>>🌅 Breakfast</option>
                                    <option value="lunch" <?php echo e(request('meal_type') == 'lunch' ? 'selected' : ''); ?>>🌞 Lunch</option>
                                    <option value="dinner" <?php echo e(request('meal_type') == 'dinner' ? 'selected' : ''); ?>>🌙 Dinner</option>
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="bi bi-search me-1"></i>Filter
                                </button>
                                <a href="<?php echo e(route('cook.feedback')); ?>" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Feedback List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-chat-dots me-2"></i>Student Feedback</h5>
                    <div class="d-flex gap-2 align-items-center">
                        <form method="GET" class="d-flex gap-2">
                            <select name="rating_filter" class="form-select form-select-sm" onchange="this.form.submit()">
                                <option value="">All Ratings</option>
                                <option value="5" <?php echo e(request('rating_filter') == '5' ? 'selected' : ''); ?>>5 Stars</option>
                                <option value="4" <?php echo e(request('rating_filter') == '4' ? 'selected' : ''); ?>>4 Stars</option>
                                <option value="3" <?php echo e(request('rating_filter') == '3' ? 'selected' : ''); ?>>3 Stars</option>
                                <option value="2" <?php echo e(request('rating_filter') == '2' ? 'selected' : ''); ?>>2 Stars</option>
                                <option value="1" <?php echo e(request('rating_filter') == '1' ? 'selected' : ''); ?>>1 Star</option>
                                <option value="low" <?php echo e(request('rating_filter') == 'low' ? 'selected' : ''); ?>>Needs Attention (1-2★)</option>
                            </select>
                        </form>
                        <?php if($feedbacks->count() > 0): ?>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="confirmDeleteAll()">
                                <i class="bi bi-trash3 me-1"></i>Clear All
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php $__empty_1 = true; $__currentLoopData = $feedbacks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feedback): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="border-bottom p-4 notification-item <?php echo e($feedback->rating <= 2 ? 'bg-light-danger' : ''); ?>"
                             data-feedback-created="<?php echo e($feedback->created_at->toISOString()); ?>"
                             data-feedback-id="<?php echo e($feedback->id); ?>"
                             data-rating="<?php echo e($feedback->rating); ?>">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-start">
                                        <div class="me-3">
                                            <div class="bg-<?php echo e($feedback->rating >= 4 ? 'success' : ($feedback->rating >= 3 ? 'warning' : 'danger')); ?> text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="bi bi-<?php echo e($feedback->rating >= 4 ? 'hand-thumbs-up' : ($feedback->rating >= 3 ? 'dash-circle' : 'hand-thumbs-down')); ?>"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">
                                                <span class="badge bg-primary me-2"><?php echo e(ucfirst($feedback->meal_type)); ?></span>
                                                Feedback for <?php echo e($feedback->meal_date->format('M d, Y')); ?>

                                                <?php if($feedback->is_anonymous): ?>
                                                    <span class="badge bg-secondary ms-2">
                                                        <i class="bi bi-incognito"></i> Anonymous
                                                    </span>
                                                <?php endif; ?>
                                            </h6>
                                            <p class="mb-1">
                                                <strong>Student:</strong>
                                                <?php if($feedback->is_anonymous): ?>
                                                    Anonymous Student
                                                <?php else: ?>
                                                    <?php echo e($feedback->student->name ?? 'Student'); ?>

                                                <?php endif; ?>
                                            </p>
                                            <p class="mb-1">
                                                <strong>Rating:</strong>
                                                <?php for($i = 1; $i <= 5; $i++): ?>
                                                    <i class="bi <?php echo e($i <= $feedback->rating ? 'bi-star-fill text-warning' : 'bi-star'); ?>"></i>
                                                <?php endfor; ?>
                                                <span class="ms-1"><?php echo e($feedback->rating); ?>/5</span>
                                            </p>
                                            <p class="mb-1">
                                                <strong>Submitted:</strong> <?php echo e($feedback->created_at->format('M d, Y \a\t g:i A')); ?>

                                            </p>

                                            <?php if($feedback->comments): ?>
                                                <p class="mb-0 text-muted">
                                                    <strong>Comments:</strong> <?php echo e(Str::limit($feedback->comments, 100)); ?>

                                                </p>
                                            <?php endif; ?>
                                            <?php if($feedback->suggestions): ?>
                                                <p class="mb-0 text-muted">
                                                    <strong>Suggestions:</strong> <?php echo e(Str::limit($feedback->suggestions, 100)); ?>

                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="mb-2">
                                        <?php if($feedback->rating >= 4): ?>
                                            <span class="badge bg-success">Positive Feedback</span>
                                        <?php elseif($feedback->rating >= 3): ?>
                                            <span class="badge bg-warning">Average Feedback</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Needs Attention</span>
                                        <?php endif; ?>
                                        <br><small class="text-muted"><?php echo e($feedback->rating); ?>/5 Stars</small>
                                    </div>
                                    <div>
                                        <?php if($feedback->comments || $feedback->suggestions): ?>
                                            <button type="button" class="btn btn-outline-primary btn-sm me-2"
                                                    onclick="showFeedbackDetails(<?php echo e($feedback->id); ?>, '<?php echo e(addslashes($feedback->comments)); ?>', '<?php echo e(addslashes($feedback->suggestions)); ?>', '<?php echo e($feedback->meal_type); ?>', '<?php echo e($feedback->meal_date->format('M d, Y')); ?>')">
                                                <i class="bi bi-eye me-1"></i>View Details
                                            </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                onclick="confirmDelete(<?php echo e($feedback->id); ?>, '<?php echo e($feedback->meal_type); ?>', '<?php echo e($feedback->meal_date->format('M d, Y')); ?>')">
                                            <i class="bi bi-trash me-1"></i>Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="p-5 text-center">
                            <div class="mb-4">
                                <i class="bi bi-hourglass-split fs-1 text-muted"></i>
                            </div>
                            <h4 class="text-muted">Waiting for Student Feedback</h4>
                            <p class="text-muted mb-4">
                                Students have not submitted any feedback yet.<br>
                                Meal feedback requires students to rate their dining experience first.
                            </p>
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>How it works:</strong>
                                <ol class="text-start mt-2 mb-0">
                                    <li>Students eat meals provided by the kitchen</li>
                                    <li>Students submit feedback with ratings and comments</li>
                                    <li>Cook reviews feedback to improve meal quality</li>
                                    <li>Cook implements changes based on student suggestions</li>
                                </ol>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Pagination -->
            <?php if($feedbacks->hasPages()): ?>
                <div class="d-flex justify-content-center mt-4">
                    <?php echo e($feedbacks->links()); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Feedback Details Modal -->
<div class="modal fade" id="feedbackDetailsModal" tabindex="-1" aria-labelledby="feedbackDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="feedbackDetailsModalLabel">
                    <i class="bi bi-chat-dots me-2"></i>Feedback Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Meal Type:</strong>
                        <span id="modal_meal_type"></span>
                    </div>
                    <div class="col-md-6">
                        <strong>Date:</strong>
                        <span id="modal_meal_date"></span>
                    </div>
                </div>
                <div class="mb-3">
                    <strong>Comments:</strong>
                    <div class="bg-light p-3 rounded mt-2">
                        <p id="modal_comments" class="mb-0"></p>
                    </div>
                </div>
                <div class="mb-3">
                    <strong>Suggestions:</strong>
                    <div class="bg-light p-3 rounded mt-2">
                        <p id="modal_suggestions" class="mb-0"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Show feedback details modal
function showFeedbackDetails(feedbackId, comments, suggestions, mealType, mealDate) {
    document.getElementById('modal_meal_type').textContent = mealType.charAt(0).toUpperCase() + mealType.slice(1);
    document.getElementById('modal_meal_date').textContent = mealDate;
    document.getElementById('modal_comments').textContent = comments || 'No comments provided';
    document.getElementById('modal_suggestions').textContent = suggestions || 'No suggestions provided';

    const modal = new bootstrap.Modal(document.getElementById('feedbackDetailsModal'));
    modal.show();
}

// Confirm delete single feedback
function confirmDelete(feedbackId, mealType, mealDate) {
    if (confirm(`Are you sure you want to delete this feedback?\n\nMeal: ${mealType} on ${mealDate}\n\nThis action cannot be undone.`)) {
        deleteFeedback(feedbackId);
    }
}

// Confirm delete all feedback
function confirmDeleteAll() {
    const totalFeedback = <?php echo e($stats['total_feedback']); ?>;
    if (confirm(`Are you sure you want to delete ALL ${totalFeedback} feedback records?\n\nThis will permanently remove all student feedback from the system.\n\nThis action cannot be undone.`)) {
        if (confirm('This is your final warning!\n\nDeleting all feedback will remove valuable student input data.\n\nAre you absolutely sure?')) {
            deleteAllFeedback();
        }
    }
}

// Delete single feedback via AJAX
function deleteFeedback(feedbackId) {
    fetch(`/cook/feedback/${feedbackId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the feedback item from the page
            const feedbackElement = document.querySelector(`[data-feedback-id="${feedbackId}"]`);
            if (feedbackElement) {
                feedbackElement.style.transition = 'opacity 0.3s ease';
                feedbackElement.style.opacity = '0';
                setTimeout(() => {
                    feedbackElement.remove();
                    // Update the total count
                    updateFeedbackCount(-1);
                }, 300);
            }

            // Show success message
            showAlert('success', 'Feedback deleted successfully');
        } else {
            showAlert('error', data.message || 'Failed to delete feedback');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while deleting feedback');
    });
}

// Delete all feedback via AJAX
function deleteAllFeedback() {
    fetch('/cook/feedback', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload the page to show empty state
            window.location.reload();
        } else {
            showAlert('error', data.message || 'Failed to delete all feedback');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An error occurred while deleting all feedback');
    });
}

// Update feedback count in header
function updateFeedbackCount(change) {
    const countBadge = document.querySelector('.badge.bg-primary.fs-6');
    if (countBadge) {
        const currentText = countBadge.textContent;
        const currentCount = parseInt(currentText.match(/\d+/)[0]);
        const newCount = currentCount + change;
        countBadge.textContent = `${newCount} Total Reviews`;

        // Hide delete all button if no feedback left
        if (newCount === 0) {
            const deleteAllBtn = document.querySelector('button[onclick="confirmDeleteAll()"]');
            if (deleteAllBtn) {
                deleteAllBtn.style.display = 'none';
            }
        }
    }
}

// Show alert messages
function showAlert(type, message) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at the top of the container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<style>
/* ULTIMATE MODAL FIXES - HIGHEST PRIORITY */
.modal {
    z-index: 999999 !important;
    position: fixed !important;
}

.modal-backdrop {
    z-index: 999998 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    pointer-events: auto !important;
}

.modal.show {
    z-index: 999999 !important;
    display: block !important;
}

.modal-dialog {
    z-index: 1000000 !important;
    position: relative !important;
    pointer-events: auto !important;
}

.modal-content {
    z-index: 1000001 !important;
    position: relative !important;
    pointer-events: auto !important;
}

/* Ensure modals are clickable */
#feedbackDetailsModal {
    z-index: 999999 !important;
    pointer-events: auto !important;
}

#feedbackDetailsModal .modal-dialog {
    pointer-events: auto !important;
}

#feedbackDetailsModal .modal-content {
    pointer-events: auto !important;
}

/* Match stock-management styling */
.notification-item {
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
}

.bg-light-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
}

/* Statistics cards styling */
.card.border-primary {
    border-color: #0d6efd !important;
    border-width: 2px;
}

.card.border-warning {
    border-color: #ffc107 !important;
    border-width: 2px;
}

.card.border-info {
    border-color: #0dcaf0 !important;
    border-width: 2px;
}

.card.border-danger {
    border-color: #dc3545 !important;
    border-width: 2px;
}

.card.border-success {
    border-color: #198754 !important;
    border-width: 2px;
}

.card.border-secondary {
    border-color: #6c757d !important;
    border-width: 2px;
}

.card.border-dark {
    border-color: #212529 !important;
    border-width: 2px;
}

/* Icon styling */
.fs-1.opacity-25 {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 2rem !important;
}

.card-body {
    position: relative;
}
</style>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\setup\Capstone\Github\Capstone14\capstone\resources\views/cook/feedback/index.blade.php ENDPATH**/ ?>