<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class UsersTableSeeder extends Seeder
{
    public function run()
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        try {
            // Clear existing users
            DB::table('users')->truncate();
            
            // Create admin user
            User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => 'admin123',
                'role' => 'admin',
                'email_verified_at' => now(),
            ]);

            // Create cook users
            $cooks = [
                [
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'password' => 'cook123'
                ],
                [
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'password' => 'cook123'
                ]
            ];

            foreach ($cooks as $cook) {
                User::create([
                    'name' => $cook['name'],
                    'email' => $cook['email'],
                    'password' => $cook['password'],
                    'role' => 'cook',
                    'email_verified_at' => now(),
                ]);
            }

            // Create kitchen team users
            $kitchenStaff = [
                [
                    'name' => '<PERSON>',
                    'email' => '<EMAIL>',
                    'password' => 'kitchen123'
                ],
                [
                    'name' => 'Robert Johnson',
                    'email' => '<EMAIL>',
                    'password' => 'kitchen123'
                ]
            ];

            foreach ($kitchenStaff as $staff) {
                User::create([
                    'name' => $staff['name'],
                    'email' => $staff['email'],
                    'password' => $staff['password'],
                    'role' => 'kitchen',
                    'email_verified_at' => now(),
                ]);
            }

            // Create student users
            $students = [
                [
                    'name' => 'Jasper Drake',
                    'email' => '<EMAIL>',
                    'password' => 'student123'
                ],
                [
                    'name' => 'Emma Wilson',
                    'email' => '<EMAIL>',
                    'password' => 'student123'
                ],
                [
                    'name' => 'Michael Brown',
                    'email' => '<EMAIL>',
                    'password' => 'student123'
                ],
                [
                    'name' => 'Sarah Davis',
                    'email' => '<EMAIL>',
                    'password' => 'student123'
                ]
            ];

            foreach ($students as $student) {
                User::create([
                    'name' => $student['name'],
                    'email' => $student['email'],
                    'password' => $student['password'],
                    'role' => 'student',
                    'email_verified_at' => now(),
                ]);
            }

            $this->command->info('Users seeded successfully!');
        } catch (\Exception $e) {
            $this->command->error('Error seeding users: ' . $e->getMessage());
        } finally {
            // Re-enable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }
    }
}
