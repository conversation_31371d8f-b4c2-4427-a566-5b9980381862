;/*FB_PKG_DELIM*/

__d("IGDSChevronRightPanoFilledIcon.react",["IGDSSVGIconBase.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(3),e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e=i.jsx("polyline",{fill:"none",points:"8.351 3.354 17 12 8.351 20.646",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"3"}),b[0]=e):e=b[0];b[1]!==a?(e=i.jsx(c("IGDSSVGIconBase.react"),babelHelpers["extends"]({},a,{viewBox:"0 0 24 24",children:e})),b[1]=a,b[2]=e):e=b[2];return e}b=i.memo(a);g["default"]=b}),98);
__d("PolarisStoriesV3NavigationContext",["emptyFunction","react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({hasNext:!1,hasNextReel:!1,hasPrev:!1,hasPrevReel:!1,isNavigating:c("emptyFunction").thatReturnsFalse,next:c("emptyFunction"),nextReel:c("emptyFunction"),prev:c("emptyFunction"),prevReel:c("emptyFunction")});g["default"]=b}),98);
__d("PolarisStoriesV3PauseReason",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum").Mirrored(["None","Hidden","KeyCommand","LongPress","MediaOverlayCTA","PauseButton","Press","Reply","Sticker","VideoPlayer"]);c=a;f["default"]=c}),66);
__d("PolarisStoriesV3PlayerControllerContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext(null);g["default"]=b}),98);
__d("PolarisStoriesV3PlayingState",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum").Mirrored(["NotStarted","Playing","Stalling","Paused","Ended"]);c=a;f["default"]=c}),66);
__d("XPolarisClipsTabControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/reels/{?shortcode}/",Object.freeze({}),void 0);b=a;g["default"]=b}),98);
__d("usePolarisDashInfo_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisDashInfo_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"is_dash_eligible",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"number_of_qualities",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"video_dash_manifest",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisDashInfo",["CometRelay","react","react-compiler-runtime","usePolarisDashInfo_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;(i||d("react")).useMemo;function a(a){var c=d("react-compiler-runtime").c(4);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisDashInfo_media.graphql"),a);var e=a.is_dash_eligible===1,f;c[0]!==a.number_of_qualities||c[1]!==a.video_dash_manifest||c[2]!==e?(f={isDashEligible:e,numberOfQualities:a.number_of_qualities,videoDashManifest:a.video_dash_manifest},c[0]=a.number_of_qualities,c[1]=a.video_dash_manifest,c[2]=e,c[3]=f):f=c[3];a=f;return a}g["default"]=a}),98);
__d("usePolarisMediaOverlayInfo_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisMediaOverlayInfo_media",selections:[{alias:null,args:null,concreteType:"XDTMediaOverlayPayloadSchema",kind:"LinkedField",name:"media_overlay_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"description",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"misinformation_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_layout",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null}],storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisMediaOverlayInfo",["CometRelay","polarisMisinformationTypeUtils","react-compiler-runtime","usePolarisMediaOverlayInfo_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var c=d("react-compiler-runtime").c(2);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisMediaOverlayInfo_media.graphql"),a);a=a==null?void 0:a.media_overlay_info;if(c[0]!==a){var e;e=a!=null?{banner:null,bloks_data:null,buttons:null,description:a.description,icon:null,misinformation_type:d("polarisMisinformationTypeUtils").getMisinformationType(a.misinformation_type),overlay_layout:(e=a.overlay_layout)!=null?e:0,overlay_type:(e=(e=a.overlay_type)==null?void 0:e.toString())!=null?e:"",title:a.title}:null;c[0]=a;c[1]=e}else e=c[1];return e}g["default"]=a}),98);
__d("usePolarisStoriesV3NavigationContext",["PolarisStoriesV3NavigationContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3NavigationContext"))}g["default"]=a}),98);
__d("usePolarisStoriesV3PlayerController",["FBLogger","PolarisStoriesV3PlayerControllerContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){var a=i(c("PolarisStoriesV3PlayerControllerContext"));if(a==null)throw c("FBLogger")("ig_web").mustfixThrow("PolarisStoriesV3PlayerControllerContext is empty");return a}g["default"]=a}),98);
__d("usePolarisStoriesV3PausePlayer",["PolarisStoriesV3PlayingState","react","react-compiler-runtime","usePolarisStoriesV3PlayerController"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(a){var b=d("react-compiler-runtime").c(4),e=c("usePolarisStoriesV3PlayerController")(),f,g;b[0]!==e||b[1]!==a?(f=function(){var b=e.getPlayingState();if(b===c("PolarisStoriesV3PlayingState").Playing){e.pause(a);return function(){e.play()}}},g=[e,a],b[0]=e,b[1]=a,b[2]=f,b[3]=g):(f=b[2],g=b[3]);i(f,g)}g["default"]=a}),98);