;/*FB_PKG_DELIM*/

__d("ZenonMWThriftSendMessageMutation_facebookRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9720271894689175"}),null);
__d("ZenonMWThriftSendMessageMutation.graphql",["ZenonMWThriftSendMessageMutation_facebookRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"input"}],c=[{alias:null,args:[{kind:"Variable",name:"data",variableName:"input"}],concreteType:"RtcWebSendMultiwayThriftSignalingMessageResponsePayload",kind:"LinkedField",name:"rtc_web_send_multiway_thrift_signaling_message",plural:!1,selections:[{alias:null,args:null,kind:"<PERSON><PERSON><PERSON><PERSON><PERSON>",name:"response",storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"ZenonMWThriftSendMessageMutation",selections:c,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"ZenonMWThriftSendMessageMutation",selections:c},params:{id:b("ZenonMWThriftSendMessageMutation_facebookRelayOperation"),metadata:{},name:"ZenonMWThriftSendMessageMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("ZenonMWThriftSendMessageMutation",["CometRelayErrorHandling","Promise","RequestStreamBodyUtils","ZenonMWThriftMessageSerializer","ZenonMWThriftSendMessageMutation.graphql","asyncToGeneratorRuntime","cr:1012418","gkx"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function j(a,e,f){return new(i||(i=b("Promise")))(function(g,i){b("cr:1012418").commitMutation(f,{mutation:h!==void 0?h:h=b("ZenonMWThriftSendMessageMutation.graphql"),onCompleted:function(a){return g(a)},onError:function(a){c("gkx")("20935")&&d("CometRelayErrorHandling").markErrorAsHandled(a);return i(a)},variables:{input:{endpoint:JSON.stringify(a),message:d("RequestStreamBodyUtils").uint8ArrayToBase64(d("ZenonMWThriftMessageSerializer").serializeMWThriftMessage(e,!0))}}})})}function k(a){a=a.rtc_web_send_multiway_thrift_signaling_message;if(a!=null&&a.response!=null){a=d("RequestStreamBodyUtils").base64ToUint8Array(a.response);a=d("ZenonMWThriftMessageSerializer").deserializeMWThriftMessage(a,!0);var b=a.messageBody;a=a.messageHeader;return{body:b,header:a}}return{body:null,header:null}}function a(a,b,c){return l.apply(this,arguments)}function l(){l=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b,c){a=(yield j(a,b,c));return k(a)});return l.apply(this,arguments)}g.sendMessage=a}),98);
__d("ZenonStateSyncPayloadSerializer",["RequestStreamBodyUtils","ZenonAppProvider"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return d("ZenonAppProvider").isInstagramApp()?d("RequestStreamBodyUtils").stringToUint8Array(a):d("RequestStreamBodyUtils").base64ToUint8Array(a)}g.stateSyncStringToUint8Array=a}),98);
__d("ZenonMWTranslatorUtils",["ChannelClientID","CurrentUser","IGDWebUtils","OverlayConfigServerLayer","RequestStreamBodyUtils","RpWebMqttEnabledAppIds","ZenonDismissReason","ZenonMWMessageTypes","ZenonParticipantState","ZenonScreenShare","ZenonSignalingProtocol","ZenonSignalingTypes","ZenonStateSyncPayloadSerializer","addDevTierOverridesToHeaderExtensions","err"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k="signalingDominantSpeakerUpdate",l="signalingVideoUploadUpdate",m="E2eeState",n=new Set(c("RpWebMqttEnabledAppIds").APP_IDS);function a(a,b,c,e){c===void 0&&(c=!1),a&&Object.keys(a).forEach(function(f){if(f===m&&e)return;var g=a[f],h=g.data;g=g.version;if(h!=null){h={data:d("ZenonStateSyncPayloadSerializer").stateSyncStringToUint8Array(h),eventName:"stateSyncNotifyRequest",responseRequired:c,topic:f,version:g};b.push(h)}})}function b(a){var b;if(a){a=a[m];(a==null?void 0:a.data)!=null&&(b=d("ZenonStateSyncPayloadSerializer").stateSyncStringToUint8Array(a.data));return b}}function e(a,b,d){var e={appId:t(),deviceId:c("ChannelClientID").getID(),userId:b.userInfo.userID};b=z(b,a);return{endpoint:e,jsonPayload:{body:d,header:b}}}function f(a,b,d,e,f){var g={appId:t(),deviceId:c("ChannelClientID").getID(),userId:b.userInfo.userID};b=A(b,a,e,f);return{endpoint:g,jsonPayload:{body:d,header:b}}}function o(a){a=d("ZenonDismissReason").mwDismissToDmissReason(a);return a!=null?a:d("ZenonDismissReason").ZenonDismissReason.CallEnded}function p(a){a=N[a];return a!=null?a:d("ZenonMWMessageTypes").ZenonMWParticipantCallState.UNKNOWN}function q(a){var b=null;a!=null&&a.forEach(function(a){a=(a=a.body)==null?void 0:a.genericMessage;if(a!=null&&a.topic==="collision_context_payload"){a=a.data;if(a!=null){var c;a=JSON.parse(a);b={groupThreadID:(c=a.group_thread_id)!=null?c:null,peerID:(c=a.peer_id)!=null?c:null,serverInfoData:(c=a.server_info_data)!=null?c:null}}}});return b}function r(a){a=(a=a.message.body)!=null?a:{};var b=a.dominantSpeakerSignalingInfo,c=a.genericMessage;a=a.videoUploadSignalingInfo;if(c)try{return atob(c.data)}catch(a){return c.data}else if(b)return JSON.stringify(b);else if(a)return JSON.stringify(a);return null}function s(a){a=(a=a.message.body)!=null?a:{};var b=a.dominantSpeakerSignalingInfo,c=a.genericMessage;a=a.videoUploadSignalingInfo;if(c)return c.topic;else if(b)return k;else if(a)return l;return null}function t(){var a=c("CurrentUser").getAppID();if(a!=null&&d("IGDWebUtils").isInstagramWebSupportedApp(Number(a)))return a;a=a!=null?a:219994525426954..toString();return n.has(Number(a))?a:219994525426954..toString()}function u(a){a=a&&a.length>0?a.find(function(a){return((a=a.body)==null?void 0:(a=a.genericMessage)==null?void 0:a.topic)==="room_metadata"}):null;if(a){var b;b=(b=a.body)==null?void 0:(b=b.genericMessage)==null?void 0:b.data;if(b!=null){b=JSON.parse(b);a=(a=a.header)==null?void 0:a.sender;if(b.link_hash!=null&&b.room_name!=null&&a!=null)return{linkHash:b.link_hash,profileURL:b.profile_url,ringSubtitle:b.ring_subtitle,roomName:b.room_name,sender:a}}}return null}function v(a,b){b=c("OverlayConfigServerLayer").createFromHeader(b);if(b){b={eventName:"overlayConfigServerUpdateRequest",serverLayer:b};a.push(b)}}function w(a){return a.reduce(function(a,b){var c;c=(c=b.body)==null?void 0:(c=c.genericMessage)==null?void 0:c.topic;b=(b=b.body)==null?void 0:(b=b.genericMessage)==null?void 0:b.data;c!=null&&b!=null&&(a[c]=b);return a},{})}function x(a){var b=a.clientSessionId,d=a.conferenceName,e=a.receiver,f=a.receiverUserId,g=a.sequenceNumber,h=a.serverInfoData,i=a.transactionId;if(f==null)throw c("err")("Incoming MW messages should have receiverUserId populated.");b=b;var j={userID:"2"};i={actorID:(e=e==null?void 0:e.actorId)!=null?e:null,messageID:i,messageTags:(e=a.messageTags)!=null?e:[],protocol:c("ZenonSignalingProtocol").MW,remoteInfo:j,retryCount:a.retryCount,roomInfo:{name:d},sequenceNumber:g,signalingID:b,userInfo:{userID:f!=null?f:"1"}};h!=null&&(i.remoteSignalingID=h);return i}function y(a,b){return Object.keys(a).map(function(c){var d=a[c];return{body:{genericMessage:{data:d,topic:c}},header:{recipients:b,topic_DEPRECATED:c}}})}function z(a,b){var e=a.messageID,f=a.messageTags,g=a.remoteSignalingID,h=a.roomInfo,i=a.signalingID,j=c("addDevTierOverridesToHeaderExtensions")(window.location.href);h={clientStack:d("ZenonMWMessageTypes").ZenonMWClientStack.ZENON,conferenceName:h.name,messageTags:f,retryCount:a.retryCount,sequenceNumber:a.sequenceNumber,transactionId:e,type:b};(j.multiwayCoreTier!==""||j.multiwayWwwTier!=="")&&(h.extensions=j);i!=null&&(h.clientSessionId=i);g!=null&&(h.serverInfoData=g);a.actorID!=null&&(h.sender={id:a.actorID});return h}function A(a,b,c,e){a=z(a,b);a.responseStatusCode=c!=null?c:d("ZenonMWMessageTypes").ZenonMWResponseStatusCode.OK;e!=null&&(a.responseSubCode=e);return a}function B(a){var b={tracks:{}},c=!d("ZenonScreenShare").screenShareWithReplaceTrack();a.tracks.forEach(function(a){var d={enabled:a.enabled,name:a.name,owner:a.participantID},e=c?K(a.type):null;e!=null&&(d.label=e);b.tracks[a.trackID]=d});return b}function C(a){if(a==null)return null;switch(a){case d("ZenonMWMessageTypes").ZenonMWTrackLabel.DEFAULT_AUDIO:return"audio";case d("ZenonMWMessageTypes").ZenonMWTrackLabel.DEFAULT_VIDEO:return"video";case d("ZenonMWMessageTypes").ZenonMWTrackLabel.SCREEN_AUDIO:return"screen_audio";case d("ZenonMWMessageTypes").ZenonMWTrackLabel.SCREEN_VIDEO:return"screen"}}function D(a){a=M[a];return a!=null?a:d("ZenonMWMessageTypes").ZenonMWDeviceStatus.OK}function E(a){switch(a){case d("ZenonDismissReason").ZenonDismissReason.IgnoreCall:return d("ZenonMWMessageTypes").ZenonMWHangupReason.IGNORE_CALL;case d("ZenonDismissReason").ZenonDismissReason.HangupCall:return d("ZenonMWMessageTypes").ZenonMWHangupReason.HANGUP_CALL;case d("ZenonDismissReason").ZenonDismissReason.NoAnswerTimeout:return d("ZenonMWMessageTypes").ZenonMWHangupReason.NO_ANSWER_TIMEOUT;case d("ZenonDismissReason").ZenonDismissReason.ClientError:return d("ZenonMWMessageTypes").ZenonMWHangupReason.CLIENT_ERROR;case d("ZenonDismissReason").ZenonDismissReason.InAnotherCall:return d("ZenonMWMessageTypes").ZenonMWHangupReason.IN_ANOTHER_CALL;case d("ZenonDismissReason").ZenonDismissReason.ClientInterrupted:return d("ZenonMWMessageTypes").ZenonMWHangupReason.CLIENT_INTERRUPTED;case d("ZenonDismissReason").ZenonDismissReason.SessionMigrated:return d("ZenonMWMessageTypes").ZenonMWHangupReason.SESSION_MIGRATED;default:return d("ZenonMWMessageTypes").ZenonMWHangupReason.HANGUP_CALL}}function F(a){var b={};a.tracks.forEach(function(a){b[a.trackID]=a.enabled});return b}function G(a){var b={},c=!d("ZenonScreenShare").screenShareWithReplaceTrack();a.tracks.forEach(function(a){var d={enabled:a.enabled},e=c?K(a.type):null;e!=null&&(d.label=e);b[a.trackID]=d});return{tracks:b}}function H(a){return O[a]}function I(a){return a==null?null:P[a]}function J(a){var b={};a.forEach(function(a,c){b[c]={data:a.data?d("RequestStreamBodyUtils").uint8ArrayToBase64(a.data):void 0,version:a.version}});return b}function K(a){switch(a){case"audio":return d("ZenonMWMessageTypes").ZenonMWTrackLabel.DEFAULT_AUDIO;case"video":return d("ZenonMWMessageTypes").ZenonMWTrackLabel.DEFAULT_VIDEO;case"screen":return d("ZenonMWMessageTypes").ZenonMWTrackLabel.SCREEN_VIDEO;case"screen_audio":return d("ZenonMWMessageTypes").ZenonMWTrackLabel.SCREEN_AUDIO;default:return null}}function L(a,b,c){c===void 0&&(c=0);var d={fromVersion:b,tracks:[],version:c};a!=null&&Object.keys(a.tracks).forEach(function(b){var c=a.tracks[b];b={enabled:c.enabled,name:c.name,participantID:c.owner,trackID:b,type:C(c.label)};d.tracks.push(b)});return d}var M={IN_ANOTHER_CALL:(i=d("ZenonMWMessageTypes")).ZenonMWDeviceStatus.IN_ANOTHER_CALL,NOT_SUPPORTED:i.ZenonMWDeviceStatus.NOT_SUPPORTED,OK:i.ZenonMWDeviceStatus.OK},N=Object.freeze((h={},h[i.ZenonMWParticipantCallState.UNKNOWN]=(j=d("ZenonParticipantState")).ZenonParticipantState.UNKNOWN,h[i.ZenonMWParticipantCallState.DISCONNECTED]=j.ZenonParticipantState.DISCONNECTED,h[i.ZenonMWParticipantCallState.NO_ANSWER]=j.ZenonParticipantState.NO_ANSWER,h[i.ZenonMWParticipantCallState.REJECTED]=j.ZenonParticipantState.REJECTED,h[i.ZenonMWParticipantCallState.UNREACHABLE]=j.ZenonParticipantState.UNREACHABLE,h[i.ZenonMWParticipantCallState.CONNECTION_DROPPED]=j.ZenonParticipantState.CONNECTION_DROPPED,h[i.ZenonMWParticipantCallState.CONTACTING]=j.ZenonParticipantState.CONTACTING,h[i.ZenonMWParticipantCallState.RINGING]=j.ZenonParticipantState.RINGING,h[i.ZenonMWParticipantCallState.CONNECTING]=j.ZenonParticipantState.CONNECTING,h[i.ZenonMWParticipantCallState.CONNECTED]=j.ZenonParticipantState.CONNECTED,h[i.ZenonMWParticipantCallState.PARTICIPANT_LIMIT_REACHED]=j.ZenonParticipantState.PARTICIPANT_LIMIT_REACHED,h[i.ZenonMWParticipantCallState.IN_ANOTHER_CALL]=j.ZenonParticipantState.IN_ANOTHER_CALL,h[i.ZenonMWParticipantCallState.RING_TYPE_UNSUPPORTED]=j.ZenonParticipantState.RING_TYPE_UNSUPPORTED,h[i.ZenonMWParticipantCallState.PENDING_APPROVAL]=j.ZenonParticipantState.PENDING_APPROVAL,h[i.ZenonMWParticipantCallState.APPROVED]=j.ZenonParticipantState.APPROVED,h[i.ZenonMWParticipantCallState.FAILED_APPROVAL]=j.ZenonParticipantState.FAILED_APPROVAL,h[i.ZenonMWParticipantCallState.UNCALLABLE]=j.ZenonParticipantState.UNREACHABLE,h[i.ZenonMWParticipantCallState.HANGUP_IN_WAITING_ROOM]=j.ZenonParticipantState.HANGUP_IN_WAITING_ROOM,h[i.ZenonMWParticipantCallState.UNCALLABLE_DELAYED]=j.ZenonParticipantState.UNREACHABLE,h)),O=Object.freeze((j={},j[(h=d("ZenonSignalingTypes")).ZenonSignalingStatusCode.OK]=i.ZenonMWResponseStatusCode.OK,j[h.ZenonSignalingStatusCode.REJECTED_FROM_VERSION_DOES_NOT_MATCH]=i.ZenonMWResponseStatusCode.CONDITIONAL_REQUEST_FAILED,j[h.ZenonSignalingStatusCode.METHOD_NOT_ALLOWED]=i.ZenonMWResponseStatusCode.METHOD_NOT_ALLOWED,j)),P=Object.freeze((j={},j[h.ZenonSignalingStatusSubCode.CLIENT_TERMINATED]=i.ZenonMWResponseSubCode.CLIENT_TERMINATED,j));g.E2EE_STATE_SYNC_TOPIC=m;g.addStateStoreSignalingEvents=a;g.fetchE2eeServerState=b;g.createMWRequest=e;g.createMWResponse=f;g.fromMWDismissReason=o;g.fromMWParticipantState=p;g.getCollisionContextFromAppMessages=q;g.getGenericDataMessageData=r;g.getGenericDataMessageTopic=s;g.getMWAppID=t;g.getRoomMetadataFromAppMessages=u;g.maybeAddOverlayConfigServerUpdateRequest=v;g.mwAppMessagesToSignalingAppMessages=w;g.mwMessageHeaderToSignalingMessageHeader=x;g.signalingMessageAppMessagesToMWAppMessages=y;g.toMWClientMediaStatus=B;g.toMWClientTrackContentType=C;g.toMWDeviceStatus=D;g.toMWHangupReason=E;g.toMWMediaStatus=F;g.toMWMediaStatusEx=G;g.toMWResponseStatusCode=H;g.toMWResponseStatusSubCode=I;g.toMWSyncStateStore=J;g.toMWTrackLabel=K;g.toZenonMediaStates=L}),98);
__d("ZenonGraphQLMWThriftMessageSender",["ChannelClientID","MultiwayCommonTypes","Promise","RpZenonBinaryThriftSignalingSitevarConfig","ZenonActorHooks","ZenonMWThriftMessageDebugLogger","ZenonMWThriftMessageLogger","ZenonMWThriftMessageMap","ZenonMWThriftMessageReliabilityLogger","ZenonMWThriftMessageTranslator","ZenonMWThriftSendMessageMutation","ZenonMWTranslatorUtils","ZenonValidateMWThriftMessage","asyncToGeneratorRuntime","err","filterNulls","unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(){function a(a){this.$3=a;a=c("RpZenonBinaryThriftSignalingSitevarConfig").supported_message_types_mqtt.map(function(a){return d("ZenonMWThriftMessageMap").messageTypeFromString(a)});this.$4=new Set(c("filterNulls")(a))}var e=a.prototype;e.handleResponse=function(a){var b=this.$1;if(b==null)throw c("unrecoverableViolation")("Should never have null message receiver","rtc_www");var e=a.header||null;a=a.body||null;if(e!=null&&a!=null){a={messageBody:a,messageHeader:e};c("ZenonMWThriftMessageDebugLogger").logMWThriftMessage("RECEIVED","GraphQL Thrift",a);d("ZenonMWThriftMessageReliabilityLogger").logReceivedMessage(a);this.$2&&this.$2({mwThriftMessage:a,name:"mwThriftMessageRecv"});e=d("ZenonMWThriftMessageTranslator").toSignalingMessage(a);if(e==null)return;b(e);d("ZenonMWThriftMessageReliabilityLogger").logProcessingMessage(a)}};e.$5=function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a,b){d("ZenonMWThriftMessageReliabilityLogger").logSendingMessage(b);c("ZenonMWThriftMessageDebugLogger").logMWThriftMessage("SENDING","GraphQL Thrift",b);this.$2&&d("ZenonMWThriftMessageLogger").logSentMessage(b,this.$2);try{a=(yield d("ZenonMWThriftSendMessageMutation").sendMessage(a,b,this.$3));d("ZenonMWThriftMessageReliabilityLogger").logSentMessage(b);this.$2&&this.$2({mwThriftMessage:b,name:"mwThriftMessageSent"});this.handleResponse(a)}catch(e){a=e!=null?e.toString():"Message Send Error";d("ZenonMWThriftMessageReliabilityLogger").logSendMessageFailed(b,"[GraphQL Thrift] "+a);b=d("ZenonMWThriftMessageMap").messageTypeToString(b.messageHeader.type);c("ZenonMWThriftMessageDebugLogger").logSendMultiwayThriftMessageFailure(a,b,e)}});function e(b,c){return a.apply(this,arguments)}return e}();e.sendMessage=function(a){var e=d("ZenonMWThriftMessageTranslator").toMWThriftMessage(a);if(!e||!c("ZenonValidateMWThriftMessage")(e))return(h||(h=b("Promise"))).reject(c("err")("Invalid MW Thrift message"));if(e.messageHeader.type!=null&&!this.$4.has(e.messageHeader.type))return(h||(h=b("Promise"))).resolve();var f=e.messageHeader.type;a={appId:d("ZenonMWTranslatorUtils").getMWAppID(),deviceId:c("ChannelClientID").getID(),userId:a.getHeader().userInfo.userID};return a.userId!==d("ZenonActorHooks").ZenonActor.getAccountID()||f!==d("MultiwayCommonTypes").MessageType.DATA_MESSAGE?this.$5(a,e):(h||(h=b("Promise"))).reject(c("err")("GraphQL only sends DATA_MESSAGE for Page users"))};e.setMessageReceiver=function(a){this.$1=a};e.setLoggingEventHandler=function(a){this.$2=a};return a}();g["default"]=a}),98);