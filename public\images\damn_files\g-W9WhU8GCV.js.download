;/*FB_PKG_DELIM*/

__d("PolarisSharerInfoQuery.graphql",["PolarisSharerInfoQuery_instagramRelayOperation","relay-runtime"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={defaultValue:null,kind:"LocalArgument",name:"mediaId"},c={defaultValue:null,kind:"LocalArgument",name:"shid"},d=[{kind:"Variable",name:"media_id",variableName:"mediaId"},{kind:"Variable",name:"shid",variableName:"shid"}],e={alias:null,args:null,kind:"Scalar<PERSON>ield",name:"profile_pic_url",storageKey:null},f={alias:null,args:null,kind:"ScalarField",name:"profile_pic_url_hd",storageKey:null},g={alias:null,args:null,kind:"<PERSON>alar<PERSON><PERSON>",name:"full_name",storageKey:null},h={alias:null,args:null,kind:"<PERSON>alar<PERSON><PERSON>",name:"username",storageKey:null};return{fragment:{argumentDefinitions:[a,c],kind:"Fragment",metadata:null,name:"PolarisSharerInfoQuery",selections:[{alias:null,args:d,concreteType:"XDTShidUserResponse",kind:"LinkedField",name:"xdt_get_relationship_for_shid_logged_out",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"sender",plural:!1,selections:[e,f,g,h],storageKey:null}],storageKey:null}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[c,a],kind:"Operation",name:"PolarisSharerInfoQuery",selections:[{alias:null,args:d,concreteType:"XDTShidUserResponse",kind:"LinkedField",name:"xdt_get_relationship_for_shid_logged_out",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"sender",plural:!1,selections:[e,f,g,h,{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null}],storageKey:null}],storageKey:null}]},params:{id:b("PolarisSharerInfoQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_get_relationship_for_shid_logged_out"]},name:"PolarisSharerInfoQuery",operationKind:"query",text:null}}}();b("relay-runtime").PreloadableQueryRegistry.set(a.params.id,a);e.exports=a}),null);
__d("PolarisSharerInfoQuery",["PolarisSharerInfoQuery.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h!==void 0?h:h=b("PolarisSharerInfoQuery.graphql");g.SHARER_INFO_QUERY=a}),98);
__d("PolarisSharerInfo.next.react",["CometRelay","PolarisReactRedux.react","PolarisSharerInfoQuery","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(a){var b=d("react-compiler-runtime").c(5),c=a.mediaId;a=a.polarisSharerInfoQuery;var e=(a=d("CometRelay").usePreloadedQuery(d("PolarisSharerInfoQuery").SHARER_INFO_QUERY,a).xdt_get_relationship_for_shid_logged_out)==null?void 0:a.sender,f=d("PolarisReactRedux.react").useDispatch(),g;b[0]!==f||b[1]!==c||b[2]!==e?(a=function(){e!=null&&f({fullName:e.full_name,profilePicUrl:e.profile_pic_url,profilePicUrlHd:e.profile_pic_url_hd,sharedMediaId:c,type:"UPDATE_SHARER_INFORMATION",username:e.username})},g=[e,f,c],b[0]=f,b[1]=c,b[2]=e,b[3]=a,b[4]=g):(a=b[3],g=b[4]);i(a,g);return null}g["default"]=a}),98);