/* Basic App Styles */
body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

/* COMPREHENSIVE Z-INDEX HIERARCHY - SYSTEM-WIDE FIX */

/* 1. Base Layer (0-999) */
/* Normal content, cards, etc. */

/* 2. Navigation Layer (1000-1099) */
.sidebar,
.cook-sidebar,
.kitchen-sidebar,
.student-sidebar,
.admin-sidebar {
    z-index: 1020 !important;
}

.sidebar-overlay {
    z-index: 1025 !important;
}

/* 3. Header Layer (1100-1199) */
.cook-header,
.kitchen-header,
.student-header,
.admin-header,
header.cook-header,
header.kitchen-header,
header.student-header,
header.admin-header {
    z-index: 1100 !important;
}

.header-backdrop {
    z-index: -1 !important;
}

/* 4. Dropdown Layer (1200-1299) */
.dropdown-menu,
.profile-menu,
.notification-dropdown {
    z-index: 1200 !important;
}

/* 5. Toast/Alert Layer (1300-1399) */
.toast-container,
.alert-container {
    z-index: 1300 !important;
}

/* 6. Modal Layer (1400-1499) - HIGHEST PRIORITY */
.modal-backdrop {
    z-index: 1400 !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
}

.modal {
    z-index: 1450 !important;
    position: fixed !important;
}

.modal.show {
    z-index: 1450 !important;
    display: block !important;
}

.modal-dialog {
    z-index: 1460 !important;
    position: relative !important;
    pointer-events: auto !important;
}

.modal-content {
    z-index: 1470 !important;
    position: relative !important;
    pointer-events: auto !important;
    background: white !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
}

/* Ensure modals are above everything */
.modal.fade.show {
    z-index: 1450 !important;
}

/* Fix for stuck modal states */
body.modal-open {
    overflow: hidden !important;
    padding-right: 0 !important;
}

/* Ensure all modal interactive elements work */
.modal input,
.modal textarea,
.modal button,
.modal select,
.modal .btn,
.modal .form-control,
.modal a,
.modal .dropdown-menu {
    pointer-events: auto !important;
}

/* Button Group Styles */
.btn-group .btn {
    border-radius: 0.375rem;
    margin-right: 2px;
}

.btn-group .btn:not(:last-child) {
    margin-right: 2px;
}

/* Toast Styles */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Card Hover Effects */
.card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease-in-out;
}

/* Table Responsive */
.table-responsive {
    border-radius: 0.5rem;
}

/* Form Improvements */
.form-control:focus {
    border-color: #22bbea;
    box-shadow: 0 0 0 0.2rem rgba(34, 187, 234, 0.25);
}

.form-select:focus {
    border-color: #22bbea;
    box-shadow: 0 0 0 0.2rem rgba(34, 187, 234, 0.25);
}

/* Alert Improvements */
.alert {
    border-radius: 0.5rem;
    border: none;
}

/* Badge Styles */
.badge {
    font-weight: 500;
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.cursor-pointer {
    cursor: pointer;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
