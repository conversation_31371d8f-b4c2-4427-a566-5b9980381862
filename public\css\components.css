/* Component Styles
   Contains styles for all reusable components including tables, forms, buttons, and admin/user management
*/

/* Table styles */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    background-color: #f8f9fa;
    font-weight: 600;
    padding: 12px 15px;
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #22bbea;
    box-shadow: 0 0 0 0.2rem rgba(34, 187, 234, 0.25);
}

/* Button styles */
.btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.25rem;
    transition: all 0.2s;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Action buttons */
.btn-primary {
    background-color: #22bbea;
    border-color: #22bbea;
}

.btn-secondary {
    background-color: #ff9933;
    border-color: #ff9933;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-success {
    background-color: #22bbea;
    border-color: #22bbea;
}

.btn-warning {
    background-color: #ff9933;
    border-color: #ff9933;
    color: white;
}

/* Button hover states */
.btn-primary:hover {
    background-color: #1a9bd1;
    border-color: #1a9bd1;
}

.btn-secondary:hover {
    background-color: #e6851a;
    border-color: #e6851a;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.btn-success:hover {
    background-color: #1a9bd1;
    border-color: #1a9bd1;
}

.btn-warning:hover {
    background-color: #e6851a;
    border-color: #e6851a;
    color: white;
}

/* Card styles */
.card {
    border: none;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* List group styles */
.list-group-item {
    border: 1px solid #dee2e6;
    padding: 0.75rem 1.25rem;
}

.list-group-item-action {
    transition: all 0.2s;
}

.list-group-item-action:hover {
    background-color: #f8f9fa;
}

/* Validation styles */
.is-invalid {
    border-color: #dc3545;
}

/* Badge Color Overrides - Consistent Color Scheme */
.badge.bg-primary {
    background-color: #22bbea !important;
}

.badge.bg-secondary {
    background-color: #ff9933 !important;
    color: white !important;
}

.badge.bg-success {
    background-color: #22bbea !important;
}

.badge.bg-warning {
    background-color: #ff9933 !important;
    color: white !important;
}

.badge.bg-info {
    background-color: #22bbea !important;
}

.badge.bg-light {
    background-color: #f8f9fa !important;
    color: #212529 !important;
}

.badge.bg-dark {
    background-color: #212529 !important;
}

/* Text Color Overrides */
.text-primary {
    color: #22bbea !important;
}

.text-secondary {
    color: #ff9933 !important;
}

.text-success {
    color: #22bbea !important;
}

.text-warning {
    color: #ff9933 !important;
}

.text-info {
    color: #22bbea !important;
}

/* Alert Color Overrides */
.alert-primary {
    background-color: rgba(34, 187, 234, 0.1) !important;
    border-color: #22bbea !important;
    color: #1a9bd1 !important;
}

.alert-secondary {
    background-color: rgba(255, 153, 51, 0.1) !important;
    border-color: #ff9933 !important;
    color: #e6851a !important;
}

.alert-success {
    background-color: rgba(34, 187, 234, 0.1) !important;
    border-color: #22bbea !important;
    color: #1a9bd1 !important;
}

.alert-warning {
    background-color: rgba(255, 153, 51, 0.1) !important;
    border-color: #ff9933 !important;
    color: #e6851a !important;
}

.alert-info {
    background-color: rgba(34, 187, 234, 0.1) !important;
    border-color: #22bbea !important;
    color: #1a9bd1 !important;
}

/* Progress Bar Color Overrides */
.progress-bar.bg-primary {
    background-color: #22bbea !important;
}

.progress-bar.bg-secondary {
    background-color: #ff9933 !important;
}

.progress-bar.bg-success {
    background-color: #22bbea !important;
}

.progress-bar.bg-warning {
    background-color: #ff9933 !important;
}

.progress-bar.bg-info {
    background-color: #22bbea !important;
}

/* Toast Color Overrides */
.toast.bg-primary {
    background-color: #22bbea !important;
}

.toast.bg-secondary {
    background-color: #ff9933 !important;
}

.toast.bg-success {
    background-color: #22bbea !important;
}

.toast.bg-warning {
    background-color: #ff9933 !important;
}

.toast.bg-info {
    background-color: #22bbea !important;
}

/* Outline Button Color Overrides */
.btn-outline-primary {
    border-color: #22bbea !important;
    color: #22bbea !important;
}

.btn-outline-primary:hover {
    background-color: #22bbea !important;
    border-color: #22bbea !important;
    color: white !important;
}

.btn-outline-secondary {
    border-color: #ff9933 !important;
    color: #ff9933 !important;
}

.btn-outline-secondary:hover {
    background-color: #ff9933 !important;
    border-color: #ff9933 !important;
    color: white !important;
}

.btn-outline-success {
    border-color: #22bbea !important;
    color: #22bbea !important;
}

.btn-outline-success:hover {
    background-color: #22bbea !important;
    border-color: #22bbea !important;
    color: white !important;
}

.btn-outline-warning {
    border-color: #ff9933 !important;
    color: #ff9933 !important;
}

.btn-outline-warning:hover {
    background-color: #ff9933 !important;
    border-color: #ff9933 !important;
    color: white !important;
}

.btn-outline-info {
    border-color: #22bbea !important;
    color: #22bbea !important;
}

.btn-outline-info:hover {
    background-color: #22bbea !important;
    border-color: #22bbea !important;
    color: white !important;
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Admin/User Management specific styles */
.admin-table {
    width: 100%;
    margin-bottom: 1rem;
}

.admin-form {
    max-width: 600px;
    margin: 0 auto;
}

.admin-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* Role dropdown styles */
.role-select {
    width: 100%;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table {
        display: block;
        overflow-x: auto;
    }
    
    .admin-form {
        max-width: 100%;
        padding: 0 1rem;
    }
    
    .admin-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .admin-actions .btn {
        width: 100%;
    }
} 