;/*FB_PKG_DELIM*/

__d("IgDirectInboxV2ItemImpressionFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("6154");b=d("FalcoLoggerInternal").create("ig_direct_inbox_v2_item_impression",a);e=b;g["default"]=e}),98);
__d("IgDirectInboxV2ItemInteractionFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("6133");b=d("FalcoLoggerInternal").create("ig_direct_inbox_v2_item_interaction",a);e=b;g["default"]=e}),98);
__d("IgInboxV2ContentType",["$InternalEnum"],(function(a,b,c,d,e,f){a=b("$InternalEnum")({ACTIVE_NOW:"active_now",AMBIENT_LOCATION:"ambient_location",AUDIO_NOTE:"audio_note",AVATAR_NOTE:"avatar_note",BIRTHDAY_INDICATOR:"birthday_indicator",FRIEND_MAP_ENTRYPOINT:"friend_map_entrypoint",FRIEND_MAP_NOTE:"friend_map_note",GIF_NOTE:"gif_note",LISTENING_NOW:"listening_now",LIVE_NOTE:"live_note",LOCATION_NOTE:"location_note",MEDIA_NOTES_AUTHOR:"media_notes_author",MEDIA_NOTES_STACK:"media_notes_stack",MUSIC_NOTE:"music_note",NOTE:"note",NOTE_CHAT:"note_chat",POG_VIDEO:"pog_video",PROMPT_NOTE:"prompt_note",PROMPT_RESPONSE_NOTE:"prompt_response_note",STACKED_PROMPT:"stacked_prompt",STORY:"story",SUGGESTED_PROMPT:"suggested_prompt"});c=a;f["default"]=c}),66);
__d("IgInboxV2CreateNoteAudience",["$InternalEnum"],(function(a,b,c,d,e,f){a=b("$InternalEnum")({ALL_FOLLOWERS:"all_followers",CLOSE_FRIENDS:"close_friends",GROUP_PROFILE:"group_profile",MUTUAL_FOLLOWERS:"mutual_followers",MUTUALS_WITH_PROFILE:"mutuals_with_profile"});c=a;f["default"]=c}),66);
__d("PolarisInboxTrayBubble.react",["PolarisInboxTrayItemLayout.react","react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={base:{zIndex:"x1vjfegm",$$css:!0},large:{marginBottom:"x1hsp7zs",$$css:!0},medium:{marginBottom:"x9a3u73",$$css:!0},mediumMobile:{marginBottom:"xh3wvx0",$$css:!0},small:{marginBottom:"x9a3u73",$$css:!0}},l={large:{minHeight:"x1ibwipt",$$css:!0},medium:{minHeight:"x7wppnt",$$css:!0},mediumMobile:{minHeight:"xbktkl8",$$css:!0},small:{minHeight:"x7wppnt",$$css:!0}},m={base:{alignItems:"x6s0dn4",display:"x78zum5",width:"xezivpi",$$css:!0},large:{maxWidth:"xaka53j",minWidth:"x5w4yej",$$css:!0},medium:{maxWidth:"xj7dor9",minWidth:"xz0mmh2",$$css:!0},mediumMobile:{maxWidth:"x1f0l55g",minWidth:"x6c5l4p",$$css:!0},small:{maxWidth:"xj7dor9",minWidth:"xz0mmh2",$$css:!0}},n={base:{backgroundColor:"xsnw5ke",boxSizing:"x9f619",display:"x78zum5",filter:"x13b9bq5",maxWidth:"x193iq5w",position:"x1n2onr6",textAlign:"x2b8uid",wordBreak:"x13faqbe","::after_backgroundColor":"x3zg9eu","::after_borderStartStartRadius":"xrw4ojt","::after_borderStartEndRadius":"xg6frx5","::after_borderEndEndRadius":"xw872ko","::after_borderEndStartRadius":"xhgbb2x","::after_content":"x1s928wv","::after_position":"x1j6awrg",$$css:!0},large:{borderStartStartRadius:"x12ol6y4",borderStartEndRadius:"x180vkcf",borderEndEndRadius:"x1khw62d",borderEndStartRadius:"x709u02",filter:"x12rx1aw",fontSize:"x1ms8i2q",lineHeight:"x1ehagqq",minHeight:"x1rfaoo0",paddingTop:"xyamay9",paddingInlineEnd:"xv54qhq",paddingBottom:"x1l90r2v",paddingInlineStart:"xf7dkkf","::after_bottom":"x1xhcax0","::after_boxShadow":"x12trbbe","::after_height":"x6giem4","::after_insetInlineStart":"x1ek332p","::after_left":null,"::after_right":null,"::after_width":"x26pben",$$css:!0},medium:{borderStartStartRadius:"x12l2aii",borderStartEndRadius:"x1mbk4o",borderEndEndRadius:"x14vvt0a",borderEndStartRadius:"x1w3ol1v",fontSize:"xxt2rmt",lineHeight:"xc7aqrk",minHeight:"xgujvf1",paddingTop:"x1y1aw1k",paddingInlineEnd:"xf159sx",paddingBottom:"xwib8y2",paddingInlineStart:"xmzvs34","::after_bottom":"xdb1ctf","::after_boxShadow":"x1cf5b39","::after_height":"x1bymyaz","::after_insetInlineStart":"x18ebn6z","::after_left":null,"::after_right":null,"::after_width":"xg4xgkf",$$css:!0},mediumMobile:{borderStartStartRadius:"x12ol6y4",borderStartEndRadius:"x180vkcf",borderEndEndRadius:"x1khw62d",borderEndStartRadius:"x709u02",fontSize:"xvs91rp",lineHeight:"x17ydfre",minHeight:"x1wiwyrm",paddingTop:"xz9dl7a",paddingInlineEnd:"xpdmqnj",paddingBottom:"xsag5q8",paddingInlineStart:"x1g0dm76","::after_bottom":"xdb1ctf","::after_boxShadow":"x1cf5b39","::after_height":"xua7q2g","::after_insetInlineStart":"x18ebn6z","::after_left":null,"::after_right":null,"::after_width":"x1jczhmm",$$css:!0},small:{borderStartStartRadius:"x12l2aii",borderStartEndRadius:"x1mbk4o",borderEndEndRadius:"x14vvt0a",borderEndStartRadius:"x1w3ol1v",fontSize:"xxt2rmt",lineHeight:"xc7aqrk",minHeight:"xgujvf1",paddingTop:"x1y1aw1k",paddingInlineEnd:"xf159sx",paddingBottom:"xwib8y2",paddingInlineStart:"xmzvs34","::after_bottom":"xdb1ctf","::after_boxShadow":"x1cf5b39","::after_height":"x1bymyaz","::after_insetInlineStart":"x18ebn6z","::after_left":null,"::after_right":null,"::after_width":"xg4xgkf",$$css:!0}},o={base:{alignItems:"x6s0dn4",display:"x78zum5",overflowX:"xw2csxc",overflowY:"x1odjw0f",$$css:!0},large:{minWidth:"x46jg8d",$$css:!0},medium:{minWidth:"x15kz4h8",$$css:!0},mediumMobile:{minWidth:"xnei2rj",$$css:!0},small:{minWidth:"x15kz4h8",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(25);a=a.children;var e=d("PolarisInboxTrayItemLayout.react").usePolarisInboxTrayItemLayoutSize()||"small",f;b[0]!==e?(f=(h||(h=c("stylex"))).props(k.base,k[e]),b[0]=e,b[1]=f):f=b[1];var g;b[2]!==e?(g=(h||(h=c("stylex"))).props(l[e]),b[2]=e,b[3]=g):g=b[3];var i;b[4]!==e?(i=(h||(h=c("stylex"))).props(m.base,m[e]),b[4]=e,b[5]=i):i=b[5];var p;b[6]!==e?(p=(h||(h=c("stylex"))).props(n.base,n[e]),b[6]=e,b[7]=p):p=b[7];var q;b[8]!==e?(q=(h||(h=c("stylex"))).props(o.base,o[e]),b[8]=e,b[9]=q):q=b[9];b[10]!==a||b[11]!==q?(e=j.jsx("div",babelHelpers["extends"]({},q,{children:a})),b[10]=a,b[11]=q,b[12]=e):e=b[12];b[13]!==p||b[14]!==e?(a=j.jsx("div",babelHelpers["extends"]({},p,{children:e})),b[13]=p,b[14]=e,b[15]=a):a=b[15];b[16]!==i||b[17]!==a?(q=j.jsx("div",babelHelpers["extends"]({},i,{children:a})),b[16]=i,b[17]=a,b[18]=q):q=b[18];b[19]!==g||b[20]!==q?(p=j.jsx("div",babelHelpers["extends"]({},g,{children:q})),b[19]=g,b[20]=q,b[21]=p):p=b[21];b[22]!==f||b[23]!==p?(e=j.jsx("div",babelHelpers["extends"]({},f,{"data-testid":void 0,children:p})),b[22]=f,b[23]=p,b[24]=e):e=b[24];return e}g["default"]=a}),98);
__d("PolarisInboxTrayItemBottomSheetGlimmer.react",["IGDSBox.react","IGDSGlimmer.react","PolarisIGCoreModalBackdrop.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={author:{height:"x1qx5ct2",marginTop:"xw7yly9",width:"x1oysuqx",$$css:!0},glimmerComposer:{borderStartStartRadius:"xr4gsrn",borderStartEndRadius:"xa83c8o",borderEndEndRadius:"x3oym20",borderEndStartRadius:"x1i84rja",height:"xsdox4t",marginInlineStart:"xyqm7xq",marginInlineEnd:"x1ys307a",marginLeft:null,marginRight:null,$$css:!0},note:{height:"x1qx5ct2",marginTop:"x14vqqas",width:"x1hfn5x7",$$css:!0},pog:{borderStartStartRadius:"x1c9tyrk",borderStartEndRadius:"xeusxvb",borderEndEndRadius:"x1pahc9y",borderEndStartRadius:"x1ertn4p",height:"x1peatla",width:"x1fu8urw",$$css:!0},root:{backgroundColor:"xgf5ljw",borderStartStartRadius:"xbjudin",borderStartEndRadius:"xnlwouz",borderEndEndRadius:"x16uus16",borderEndStartRadius:"xbiv7yw",bottom:"x1ey2m1c",height:"x1ri49h8",insetInlineEnd:"xtijo5x",insetInlineStart:"x1o0tod",left:null,right:null,overflowX:"x6ikm8r",overflowY:"x10wlt62",paddingBottom:"x84yb8i",position:"xixxii4",$$css:!0}};function a(){var a=d("react-compiler-runtime").c(3),b,e;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=i.jsx("div",babelHelpers["extends"]({className:"x1rfj78v xsag5q8 xv54qhq xf7dkkf x1cnzs8"},{children:i.jsx(c("IGDSGlimmer.react"),{index:0,xstyle:j.pog})})),e={className:"x6ikm8r x10wlt62 xh8yej3"},a[0]=b,a[1]=e):(b=a[0],e=a[1]);a[2]===Symbol["for"]("react.memo_cache_sentinel")?(b=i.jsx(c("PolarisIGCoreModalBackdrop.react"),{children:i.jsxs(c("IGDSBox.react"),{xstyle:j.root,children:[i.jsxs(c("IGDSBox.react"),{direction:"row",justifyContent:"start",children:[b,i.jsx("div",babelHelpers["extends"]({},e,{children:i.jsxs(c("IGDSBox.react"),{direction:"column",flex:"grow",children:[i.jsx(c("IGDSGlimmer.react"),{index:0,xstyle:j.author}),i.jsx(c("IGDSGlimmer.react"),{index:0,xstyle:j.note})]})}))]}),i.jsx(c("IGDSGlimmer.react"),{index:0,xstyle:j.glimmerComposer})]})}),a[2]=b):b=a[2];return b}g["default"]=a}),98);
__d("PolarisInboxTrayItemBottomSheetSelfActionGlimmer.react",["IGDSGlimmer.react","PolarisIGCoreModalBackdrop.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={deleteNoteButtonGlimmer:{height:"x1qx5ct2",width:"x1exxlbk",$$css:!0},newNoteButtonGlimmer:{borderStartStartRadius:"x1obq294",borderStartEndRadius:"x5a5i1n",borderEndEndRadius:"xde0f50",borderEndStartRadius:"x15x8krk",height:"x10w6t97",marginTop:"xdj266r",marginInlineEnd:"x14z9mp",marginInlineStart:"x1lziwak",marginBottom:"x1yztbdb",width:"xh8yej3",$$css:!0},selfNotePogGlimmer:{alignItems:"x6s0dn4",borderStartStartRadius:"x1c9tyrk",borderStartEndRadius:"xeusxvb",borderEndEndRadius:"x1pahc9y",borderEndStartRadius:"x1ertn4p",display:"x78zum5",height:"xnnlda6",justifyContent:"xl56j7k",marginBottom:"x1chd833",marginTop:"x1ajfak3",width:"x15yg21f",$$css:!0},selfNoteTextGlimmer:{height:"x1qx5ct2",marginBottom:"xieb3on",width:"xq1dxzn",$$css:!0}};function a(){var a=d("react-compiler-runtime").c(1);if(a[0]===Symbol["for"]("react.memo_cache_sentinel")){var b;b=i.jsx(c("PolarisIGCoreModalBackdrop.react"),{onClose:k,children:i.jsxs("div",babelHelpers["extends"]({className:"xgf5ljw xbjudin xnlwouz x16uus16 xbiv7yw x1ey2m1c x14luw17 xtijo5x x1o0tod x6ikm8r x10wlt62 xixxii4"},{children:[i.jsxs("div",babelHelpers["extends"]({className:"x6s0dn4 x78zum5 xdt5ytf xl56j7k xv54qhq x1l90r2v xf7dkkf xexx8yu x2b8uid"},{children:[i.jsx("div",babelHelpers["extends"]({className:"x6s0dn4 x1c9tyrk xeusxvb x1pahc9y x1ertn4p x78zum5 xnnlda6 xl56j7k x1chd833 x1ajfak3 x15yg21f"},{children:i.jsx(b=c("IGDSGlimmer.react"),{index:0,xstyle:j.selfNotePogGlimmer})})),i.jsx("div",babelHelpers["extends"]({className:"x1qx5ct2 xieb3on xq1dxzn"},{children:i.jsx(b,{index:0,xstyle:j.selfNoteTextGlimmer})}))]})),i.jsxs("div",babelHelpers["extends"]({className:"x6s0dn4 x78zum5 xdt5ytf xl56j7k x1ys307a xyqm7xq x12nagc xdj266r"},{children:[i.jsx("div",babelHelpers["extends"]({className:"x1obq294 x5a5i1n xde0f50 x15x8krk x10w6t97 xdj266r x14z9mp x1lziwak x1yztbdb xh8yej3"},{children:i.jsx(b,{index:0,xstyle:j.newNoteButtonGlimmer})})),i.jsx("div",babelHelpers["extends"]({className:"x1qx5ct2 x1exxlbk"},{children:i.jsx(b,{index:0,xstyle:j.deleteNoteButtonGlimmer})}))]}))]}))});a[0]=b}else b=a[0];return b}function k(){}g["default"]=a}),98);
__d("PolarisInboxTrayItemBottomSheetTrigger_pogInfo.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisInboxTrayItemBottomSheetTrigger_pogInfo",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"pog_users",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null}],storageKey:null},action:"THROW"}],type:"XDTInboxTrayItemPogInfo",abstractKey:null};e.exports=a}),null);
__d("PolarisInboxTrayItemBottomSheetTrigger.react",["CometPlaceholder.react","CometPressable.react","CometRelay","JSResourceForInteraction","PolarisInboxTrayItemBottomSheetGlimmer.react","PolarisInboxTrayItemBottomSheetSelfActionGlimmer.react","PolarisInboxTrayItemBottomSheetTrigger_pogInfo.graphql","lazyLoadComponent","react","react-compiler-runtime","usePolarisViewer"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useState,l=c("lazyLoadComponent")(c("JSResourceForInteraction")("PolarisInboxTrayItemBottomSheet.react").__setRef("PolarisInboxTrayItemBottomSheetTrigger.react")),m={pressable:{height:"x5yr21d",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(14),f=a.children,g=a.onLeaveNewNoteClick,i=a.onTriggerPress,n=a.pogInfo;a=a.trayItem;n=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisInboxTrayItemBottomSheetTrigger_pogInfo.graphql"),n);var o=k(!1),p=o[0],q=o[1];o=(o=c("usePolarisViewer")())==null?void 0:o.id;n=o!=null&&n.pog_users[0].id===o;e[0]!==p||e[1]!==i?(o=function(){i(),q(!p)},e[0]=p,e[1]=i,e[2]=o):o=e[2];var r;e[3]!==f||e[4]!==o?(r=j.jsx(c("CometPressable.react"),{onPress:o,overlayDisabled:!0,testid:void 0,xstyle:m.pressable,children:f}),e[3]=f,e[4]=o,e[5]=r):r=e[5];e[6]!==p||e[7]!==n||e[8]!==g||e[9]!==a?(f=p&&j.jsx(c("CometPlaceholder.react"),{fallback:n?j.jsx(c("PolarisInboxTrayItemBottomSheetSelfActionGlimmer.react"),{}):j.jsx(c("PolarisInboxTrayItemBottomSheetGlimmer.react"),{}),children:j.jsx(l,{isSelfNote:n,onClose:function(){return q(!1)},openComposer:g,trayItem:a})}),e[6]=p,e[7]=n,e[8]=g,e[9]=a,e[10]=f):f=e[10];e[11]!==r||e[12]!==f?(o=j.jsxs(j.Fragment,{children:[r,f]}),e[11]=r,e[12]=f,e[13]=o):o=e[13];return o}g["default"]=a}),98);
__d("PolarisInboxTrayItemBubbleText_note.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisInboxTrayItemBubbleText_note",selections:[{alias:null,args:null,kind:"ScalarField",name:"text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_emoji_only",storageKey:null}],type:"XDTLazyNoteDict",abstractKey:null};e.exports=a}),null);
__d("PolarisInboxTrayItemBubbleText.react",["CometRelay","FDSLineClamp.react","PolarisInboxTrayItemBubbleText_note.graphql","polarisGetIGFormattedText","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={emojiOnly:{fontSize:"x10gsz20",lineHeight:"x1npbak5",paddingInlineStart:"x135b78x",paddingInlineEnd:"x11lfxj5",paddingLeft:null,paddingRight:null,$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(6),f=a.lines;a=a.note;f=f===void 0?3:f;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisInboxTrayItemBubbleText_note.graphql"),a);var g=a==null?void 0:a.text;if(g==null)return null;a=(a==null?void 0:a.is_emoji_only)&&k.emojiOnly;var i;e[0]!==g?(i=c("polarisGetIGFormattedText")(g,{className:"x5n08af x117nqv4 x9n4tj2"}),e[0]=g,e[1]=i):i=e[1];e[2]!==f||e[3]!==a||e[4]!==i?(g=j.jsx(c("FDSLineClamp.react"),{lines:f,xstyle:a,children:i}),e[2]=f,e[3]=a,e[4]=i,e[5]=g):g=e[5];return g}g["default"]=a}),98);
__d("PolarisInboxTrayItemBubble_trayItem.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisInboxTrayItemBubble_trayItem",selections:[{alias:null,args:null,concreteType:"XDTLazyNoteDict",kind:"LinkedField",name:"note_dict",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"note_style",storageKey:null},{alias:null,args:null,concreteType:"XDTNoteResponseInfo",kind:"LinkedField",name:"note_response_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMusicNoteResponseInfo",kind:"LinkedField",name:"music_note_response_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMusicInfo",kind:"LinkedField",name:"music_info",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisInboxTrayItemBubbleMusic_musicInfo"}],storageKey:null}],storageKey:null}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisInboxTrayItemBubbleText_note"}],storageKey:null}],type:"XDTInboxTrayItem",abstractKey:null};e.exports=a}),null);
__d("PolarisInboxTrayItemBubble.react",["CometRelay","PolarisInboxTrayBubble.react","PolarisInboxTrayItemBubbleText.react","PolarisInboxTrayItemBubble_trayItem.graphql","PolarisNotesTypes","cr:7592","justknobx","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e,f=d("react-compiler-runtime").c(14),g=a.playMusic,i=a.showFullText;a=a.trayItem;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisInboxTrayItemBubble_trayItem.graphql"),a);if(((e=a.note_dict)==null?void 0:e.note_style)===d("PolarisNotesTypes").NoteStyle.EMPTY)return null;e=(e=a.note_dict)==null?void 0:(e=e.note_response_info)==null?void 0:(e=e.music_note_response_info)==null?void 0:e.music_info;var k;f[0]!==e?(k=e!=null&&c("justknobx")._("2330"),f[0]=e,f[1]=k):k=f[1];k=k;a=a.note_dict;var l;f[2]===Symbol["for"]("react.memo_cache_sentinel")?(l={className:"xh8yej3"},f[2]=l):l=f[2];var m;f[3]!==e||f[4]!==g||f[5]!==k?(m=k&&b("cr:7592")!=null&&e&&j.jsx(b("cr:7592"),{musicInfo:e,playMusic:g}),f[3]=e,f[4]=g,f[5]=k,f[6]=m):m=f[6];f[7]!==a||f[8]!==k||f[9]!==i?(e=a&&j.jsx(c("PolarisInboxTrayItemBubbleText.react"),{lines:i===!0?0:k?1:3,note:a}),f[7]=a,f[8]=k,f[9]=i,f[10]=e):e=f[10];f[11]!==m||f[12]!==e?(g=j.jsx(c("PolarisInboxTrayBubble.react"),{children:j.jsxs("div",babelHelpers["extends"]({},l,{children:[m,e]}))}),f[11]=m,f[12]=e,f[13]=g):g=f[13];return g}g["default"]=a}),98);
__d("PolarisInboxTrayItemBubbleMusicInfo_musicInfo.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisInboxTrayItemBubbleMusicInfo_musicInfo",selections:[{alias:null,args:null,concreteType:"XDTTrackData",kind:"LinkedField",name:"music_asset_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"display_artist",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_explicit",storageKey:null}],storageKey:null}],type:"XDTMusicInfo",abstractKey:null};e.exports=a}),null);
__d("PolarisInboxTrayWafeformIcon.react",["fbt","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react");function a(){var a=d("react-compiler-runtime").c(1),b;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=j.jsxs("svg",{fill:"currentColor",viewBox:"0 0 24 30",width:"100%",children:[j.jsx("title",{children:h._(/*BTDS*/"Icon depicting sound waves with three vertical bars changing in height in a pulsating manner")}),j.jsx("rect",babelHelpers["extends"]({className:"x1c74tu6 xa4qsjk x1u6grsq xeqyi8g"},{height:"30",rx:"2",ry:"2",width:"4"})),j.jsx("rect",babelHelpers["extends"]({className:"x1c74tu6 xa4qsjk x1u6grsq xoy5rgs"},{height:"22",rx:"2",ry:"2",width:"4",x:"10",y:"4"})),j.jsx("rect",babelHelpers["extends"]({className:"x1c74tu6 xa4qsjk x1u6grsq xeqyi8g"},{height:"30",rx:"2",ry:"2",width:"4",x:"20"}))]}),a[0]=b):b=a[0];return b}g["default"]=a}),226);
__d("PolarisInboxTrayItemBubbleMusicInfo.react",["fbt","CometRelay","FDSLineClamp.react","IGDSExplicitEFilledIcon.react","PolarisInboxTrayItemBubbleMusicInfo_musicInfo.graphql","PolarisInboxTrayItemLayout.react","PolarisInboxTrayWafeformIcon.react","cr:3197","justknobx","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||d("react"),l=h._(/*BTDS*/"Explicit"),m={trackArtist:{color:"x1roi4f4",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(25);a=a.musicInfo;var f=d("PolarisInboxTrayItemLayout.react").usePolarisInboxTrayItemLayoutSize();a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisInboxTrayItemBubbleMusicInfo_musicInfo.graphql"),a);a=a.music_asset_info;if(a==null)return null;var g;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(g=c("justknobx")._("2358"),e[0]=g):g=e[0];g=g;var h;e[1]!==a.title?(h=g?a.title:k.jsx(c("FDSLineClamp.react"),{lines:1,children:a.title}),e[1]=a.title,e[2]=h):h=e[2];var j;e[3]!==a.is_explicit||e[4]!==f?(j=a.is_explicit&&k.jsx("div",babelHelpers["extends"]({className:"xdwrcjd"},{children:k.jsx(c("IGDSExplicitEFilledIcon.react"),{alt:l,color:"ig-secondary-icon",size:f==="large"?14:10})})),e[3]=a.is_explicit,e[4]=f,e[5]=j):j=e[5];var n;e[6]!==h||e[7]!==j?(n=k.jsxs(k.Fragment,{children:[h,j]}),e[6]=h,e[7]=j,e[8]=n):n=e[8];h=n;e[9]===Symbol["for"]("react.memo_cache_sentinel")?(j={className:"x6s0dn4 x78zum5 x117nqv4 xl56j7k"},e[9]=j):j=e[9];e[10]!==f?(n={0:{className:"x78zum5 x1ohifd9 x2fvf9 x11inojt"},1:{className:"x78zum5 x2fvf9 x1ptkvgd xcsk191"}}[!!(f==="large")<<0],e[10]=f,e[11]=n):n=e[11];e[12]===Symbol["for"]("react.memo_cache_sentinel")?(f=k.jsx(c("PolarisInboxTrayWafeformIcon.react"),{}),e[12]=f):f=e[12];e[13]!==n?(f=k.jsx("div",babelHelpers["extends"]({},n,{children:f})),e[13]=n,e[14]=f):f=e[14];e[15]!==h?(n=g&&b("cr:3197")!=null?k.jsx(b("cr:3197"),{children:k.jsx("div",babelHelpers["extends"]({className:"x6s0dn4 x78zum5 x1n2onr6"},{children:h}))}):h,e[15]=h,e[16]=n):n=e[16];e[17]!==f||e[18]!==n?(g=k.jsxs("div",babelHelpers["extends"]({},j,{children:[f,n]})),e[17]=f,e[18]=n,e[19]=g):g=e[19];e[20]!==a.display_artist?(h=k.jsx(c("FDSLineClamp.react"),{lines:1,xstyle:m.trackArtist,children:a.display_artist}),e[20]=a.display_artist,e[21]=h):h=e[21];e[22]!==g||e[23]!==h?(j=k.jsxs(k.Fragment,{children:[g,h]}),e[22]=g,e[23]=h,e[24]=j):j=e[24];return j}g["default"]=a}),226);
__d("PolarisInboxTrayItemBubbleMusicPlayer_musicInfo.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisInboxTrayItemBubbleMusicPlayer_musicInfo",selections:[{alias:null,args:null,concreteType:"XDTTrackData",kind:"LinkedField",name:"music_asset_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"progressive_download_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"web_30s_preview_download_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"display_artist",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"cover_artwork_uri",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTMusicConsumptionInfoDict",kind:"LinkedField",name:"music_consumption_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"audio_asset_start_time_in_ms",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"should_mute_audio",storageKey:null}],storageKey:null}],type:"XDTMusicInfo",abstractKey:null};e.exports=a}),null);
__d("PolarisInboxTrayItemBubbleMusicPlayer.react",["CometRelay","PolarisInboxTrayItemBubbleMusicPlayer_musicInfo.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));c=i;var k=c.useEffect,l=c.useRef,m=1e3,n=30;function o(a,b){b=(b=(b=b.music_consumption_info)==null?void 0:b.audio_asset_start_time_in_ms)!=null?b:0;var c=Math.round(b/m),d=c+n;k(function(){var b=a.current;b!=null&&(b.ontimeupdate=function(){b.currentTime>=d&&(b.currentTime=c)},b.currentTime=c,void b.play())},[d,c])}function p(a){var b,c=d("react-compiler-runtime").c(5),e=(b=a.music_asset_info)==null?void 0:b.display_artist,f=(b=a.music_asset_info)==null?void 0:b.title,g=(b=a.music_asset_info)==null?void 0:b.cover_artwork_uri;c[0]!==e||c[1]!==g||c[2]!==f?(a=function(){if(!("mediaSession"in navigator))return;window.navigator.mediaSession.metadata=new window.MediaMetadata({artist:e,artwork:[{src:g}],title:f})},b=[e,g,f],c[0]=e,c[1]=g,c[2]=f,c[3]=a,c[4]=b):(a=c[3],b=c[4]);k(a,b)}function a(a){var c,e=d("react-compiler-runtime").c(2);a=a.musicInfo;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisInboxTrayItemBubbleMusicPlayer_musicInfo.graphql"),a);var f=l();o(f,a);p(a);if(((c=a.music_consumption_info)==null?void 0:c.should_mute_audio)===!0)return null;a=(c=a==null?void 0:(c=a.music_asset_info)==null?void 0:c.web_30s_preview_download_url)!=null?c:a==null?void 0:(c=a.music_asset_info)==null?void 0:c.progressive_download_url;if(a==null)return null;e[0]!==a?(c=j.jsx("audio",{controls:!1,ref:f,src:a}),e[0]=a,e[1]=c):c=e[1];return c}g["default"]=a}),98);
__d("PolarisInboxTrayItemBubbleMusic_musicInfo.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisInboxTrayItemBubbleMusic_musicInfo",selections:[{args:null,kind:"FragmentSpread",name:"PolarisInboxTrayItemBubbleMusicInfo_musicInfo"},{args:null,kind:"FragmentSpread",name:"PolarisInboxTrayItemBubbleMusicPlayer_musicInfo"}],type:"XDTMusicInfo",abstractKey:null};e.exports=a}),null);
__d("PolarisInboxTrayItemBubbleMusic.react",["CometRelay","PolarisInboxTrayItemBubbleMusicInfo.react","PolarisInboxTrayItemBubbleMusicPlayer.react","PolarisInboxTrayItemBubbleMusic_musicInfo.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(8),f=a.musicInfo;a=a.playMusic;a=a===void 0?!1:a;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisInboxTrayItemBubbleMusic_musicInfo.graphql"),f);var g;e[0]!==f?(g=j.jsx(c("PolarisInboxTrayItemBubbleMusicInfo.react"),{musicInfo:f}),e[0]=f,e[1]=g):g=e[1];var i;e[2]!==f||e[3]!==a?(i=a&&j.jsx(c("PolarisInboxTrayItemBubbleMusicPlayer.react"),{musicInfo:f}),e[2]=f,e[3]=a,e[4]=i):i=e[4];e[5]!==g||e[6]!==i?(f=j.jsxs(j.Fragment,{children:[g,i]}),e[5]=g,e[6]=i,e[7]=f):f=e[7];return f}g["default"]=a}),98);
__d("PolarisInboxTrayItemPopoverFallback.react",["IGDSGlimmer.react","IGDSPopover.react","PolarisInboxTrayItemGlimmer.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={glimmer:{backgroundColor:"x1lynahi",$$css:!0},glimmerComposer:{borderStartStartRadius:"x1rl75mt",borderStartEndRadius:"x19t5iym",borderEndEndRadius:"xz7t8uv",borderEndStartRadius:"x13xmedi",height:"xsdox4t",marginBottom:"xod5an3",marginInlineStart:"xyqm7xq",marginInlineEnd:"x1ys307a",marginLeft:null,marginRight:null,$$css:!0},popover:{borderStartStartRadius:"x12ol6y4",borderStartEndRadius:"x180vkcf",borderEndEndRadius:"x1khw62d",borderEndStartRadius:"x709u02",$$css:!0}};function a(){var a=d("react-compiler-runtime").c(3),b,e;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b={className:"xxsgkw5"},e=i.jsx("div",babelHelpers["extends"]({className:"x78zum5 xl56j7k x1miatn0 x1gan7if"},{children:i.jsx(c("PolarisInboxTrayItemGlimmer.react"),{size:"large"})})),a[0]=b,a[1]=e):(b=a[0],e=a[1]);a[2]===Symbol["for"]("react.memo_cache_sentinel")?(b=i.jsx(c("IGDSPopover.react"),{popoverContent:i.jsxs("div",babelHelpers["extends"]({},b,{children:[e,i.jsx(c("IGDSGlimmer.react"),{index:0,xstyle:[j.glimmer,j.glimmerComposer]})]})),popoverXStyle:j.popover}),a[2]=b):b=a[2];return b}g["default"]=a}),98);
__d("PolarisInboxTrayItemPopoverTrigger.react",["CometPressable.react","IGDSLazyPopoverTrigger.react","JSResourceForInteraction","PolarisInboxTrayItemPopoverFallback.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("JSResourceForInteraction")("PolarisInboxTrayItemPopover.react").__setRef("PolarisInboxTrayItemPopoverTrigger.react"),k={pressable:{height:"x5yr21d",$$css:!0}};function l(){return i.jsx(c("PolarisInboxTrayItemPopoverFallback.react"),{})}l.displayName=l.name+" [from "+f.id+"]";function a(a){var b=d("react-compiler-runtime").c(12),e=a.children,f=a.onLeaveNewNoteClick,g=a.onTriggerPress,h=a.onVisibilityChange,m=a.popoverPositionOverride;a=a.trayItem;m=m===void 0?"end":m;var n;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(n=i.jsx(l,{}),b[0]=n):n=b[0];var o;b[1]!==f||b[2]!==a?(o={openComposer:f,trayItem:a},b[1]=f,b[2]=a,b[3]=o):o=b[3];b[4]!==e||b[5]!==g?(f=function(a,b){return i.jsx(c("CometPressable.react"),{onPress:function(){g(),b()},overlayDisabled:!0,overlayFocusRingPosition:"inset",ref:a,testid:void 0,xstyle:k.pressable,children:e})},b[4]=e,b[5]=g,b[6]=f):f=b[6];b[7]!==h||b[8]!==m||b[9]!==o||b[10]!==f?(a=i.jsx(c("IGDSLazyPopoverTrigger.react"),{align:"start",fallback:n,onVisibilityChange:h,popoverProps:o,popoverResource:j,popoverType:"dialog",position:m,preloadTrigger:"button",children:f}),b[7]=h,b[8]=m,b[9]=o,b[10]=f,b[11]=a):a=b[11];return a}g["default"]=a}),98);
__d("PolarisInboxTrayMarquee.next.react",["PolarisMarquee.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useState;function a(a){var b=d("react-compiler-runtime").c(10),e=a.children;a=a.isReplySheet;a=a===void 0?!1:a;var f=j(),g=f[0],h=f[1];b[0]===Symbol["for"]("react.memo_cache_sentinel")?(f={className:"x6s0dn4 x78zum5 xw2csxc x1odjw0f x1n2onr6"},b[0]=f):f=b[0];var k;b[1]!==g||b[2]!==a?(k=g===!0&&i.jsxs(i.Fragment,{children:[i.jsx("div",babelHelpers["extends"]({},{0:{className:"x5yr21d x10l6tqk x1kky2od x1vjfegm xzxgvzf"},1:{className:"x5yr21d x10l6tqk x1kky2od x1vjfegm x1qzgeok"}}[!!a<<0])),i.jsx("div",babelHelpers["extends"]({},{0:{className:"x5yr21d x10l6tqk x1kky2od x1vjfegm x103n6ev xtijo5x"},1:{className:"x5yr21d x10l6tqk x1kky2od x1vjfegm xi1dc19 xtijo5x"}}[!!a<<0]))]}),b[1]=g,b[2]=a,b[3]=k):k=b[3];b[4]===Symbol["for"]("react.memo_cache_sentinel")?(g=function(){return h(!0)},b[4]=g):g=b[4];b[5]!==e?(a=i.jsx(c("PolarisMarquee.react"),{onAnimate:g,children:e}),b[5]=e,b[6]=a):a=b[6];b[7]!==k||b[8]!==a?(g=i.jsxs("div",babelHelpers["extends"]({},f,{children:[k,a]})),b[7]=k,b[8]=a,b[9]=g):g=b[9];return g}g["default"]=a}),98);
__d("PolarisProfileNoteBubbleImpl_trayItems.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisProfileNoteBubbleImpl_trayItems",selections:[{alias:null,args:null,concreteType:"XDTInboxTrayItemPogInfo",kind:"LinkedField",name:"pog_info",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisInboxTrayItemBottomSheetTrigger_pogInfo"}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisInboxTrayItemBubble_trayItem"},{args:null,kind:"FragmentSpread",name:"usePolarisInboxTrayItemInteractionLogger_trayItem"},{args:null,kind:"FragmentSpread",name:"usePolarisInboxTrayItemImpressionLogger_trayItem"},{args:null,kind:"FragmentSpread",name:"PolarisInboxTrayItemPopover_trayItem"},{args:null,kind:"FragmentSpread",name:"PolarisInboxTrayItemBottomSheet_trayItem"}],type:"XDTInboxTrayItem",abstractKey:null};e.exports=a}),null);
__d("usePolarisInboxTrayItemAudioClusterId_trayItem.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisInboxTrayItemAudioClusterId_trayItem",selections:[{alias:null,args:null,concreteType:"XDTLazyNoteDict",kind:"LinkedField",name:"note_dict",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTNoteResponseInfo",kind:"LinkedField",name:"note_response_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMusicNoteResponseInfo",kind:"LinkedField",name:"music_note_response_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMusicInfo",kind:"LinkedField",name:"music_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTTrackData",kind:"LinkedField",name:"music_asset_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"audio_cluster_id",storageKey:null}],storageKey:null}],storageKey:null}],storageKey:null}],storageKey:null}],storageKey:null}],type:"XDTInboxTrayItem",abstractKey:null};e.exports=a}),null);
__d("usePolarisInboxTrayItemAudioClusterId.react",["CometRelay","usePolarisInboxTrayItemAudioClusterId_trayItem.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisInboxTrayItemAudioClusterId_trayItem.graphql"),a);return a==null?void 0:(a=a.note_dict)==null?void 0:(a=a.note_response_info)==null?void 0:(a=a.music_note_response_info)==null?void 0:(a=a.music_info)==null?void 0:(a=a.music_asset_info)==null?void 0:a.audio_cluster_id}g["default"]=a}),98);
__d("usePolarisInboxTrayItemContentTypes_trayItem.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null}];return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisInboxTrayItemContentTypes_trayItem",selections:[{alias:null,args:null,concreteType:"XDTLazyNoteDict",kind:"LinkedField",name:"note_dict",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTNoteResponseInfo",kind:"LinkedField",name:"note_response_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMusicNoteResponseInfo",kind:"LinkedField",name:"music_note_response_info",plural:!1,selections:a,storageKey:null},{alias:null,args:null,concreteType:"XDTNotePogVideoResponseInfo",kind:"LinkedField",name:"note_pog_video_response_info",plural:!1,selections:a,storageKey:null}],storageKey:null}],storageKey:null}],type:"XDTInboxTrayItem",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisInboxTrayItemContentTypes.react",["CometRelay","react-compiler-runtime","usePolarisInboxTrayItemContentTypes_trayItem.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var c,e=d("react-compiler-runtime").c(3);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisInboxTrayItemContentTypes_trayItem.graphql"),a);c=a==null?void 0:(c=a.note_dict)==null?void 0:(c=c.note_response_info)==null?void 0:c.music_note_response_info;a=a==null?void 0:(a=a.note_dict)==null?void 0:(a=a.note_response_info)==null?void 0:a.note_pog_video_response_info;var f;e[0]!==c||e[1]!==a?(f=[],c!=null&&f.push("music_note"),a!=null&&f.push("pog_video"),e[0]=c,e[1]=a,e[2]=f):f=e[2];return f}g["default"]=a}),98);
__d("usePolarisInboxTrayItemImpressionLogger_trayItem.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisInboxTrayItemImpressionLogger_trayItem",selections:[{alias:null,args:null,kind:"ScalarField",name:"inbox_tray_item_id",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTLazyNoteDict",kind:"LinkedField",name:"note_dict",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"author_id",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"usePolarisInboxTrayItemContentTypes_trayItem"},{args:null,kind:"FragmentSpread",name:"usePolarisInboxTrayItemAudioClusterId_trayItem"}],type:"XDTInboxTrayItem",abstractKey:null};e.exports=a}),null);
__d("usePolarisInboxTrayItemImpressionLogger.react",["CometRelay","IgDirectInboxV2ItemImpressionFalcoEvent","PolarisNavChain","react-compiler-runtime","usePolarisInboxTrayItemAudioClusterId.react","usePolarisInboxTrayItemContentTypes.react","usePolarisInboxTrayItemImpressionLogger_trayItem.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var e=d("react-compiler-runtime").c(5),f=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisInboxTrayItemImpressionLogger_trayItem.graphql"),a),g=c("usePolarisInboxTrayItemContentTypes.react")(f),i=c("usePolarisInboxTrayItemAudioClusterId.react")(f);if(e[0]!==i||e[1]!==g||e[2]!==(f==null?void 0:f.inbox_tray_item_id)||e[3]!==(f==null?void 0:(a=f.note_dict)==null?void 0:a.author_id)){var j;a=function(a){c("IgDirectInboxV2ItemImpressionFalcoEvent").log(function(){var b;return babelHelpers["extends"]({audio_cluster_id:i,content:g,nav_chain:(b=(b=c("PolarisNavChain").getInstance())==null?void 0:b.getNavChainForSend())!=null?b:"",note_id:f==null?void 0:f.inbox_tray_item_id,target_user_id:f==null?void 0:(b=f.note_dict)==null?void 0:b.author_id},a)})};e[0]=i;e[1]=g;e[2]=f==null?void 0:f.inbox_tray_item_id;e[3]=f==null?void 0:(j=f.note_dict)==null?void 0:j.author_id;e[4]=a}else a=e[4];j=a;return j}g["default"]=a}),98);
__d("usePolarisInboxTrayItemInteractionLogger_trayItem.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisInboxTrayItemInteractionLogger_trayItem",selections:[{alias:null,args:null,kind:"ScalarField",name:"inbox_tray_item_id",storageKey:null},{alias:null,args:null,concreteType:"XDTLazyNoteDict",kind:"LinkedField",name:"note_dict",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"author_id",storageKey:null}],storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisInboxTrayItemContentTypes_trayItem"},{args:null,kind:"FragmentSpread",name:"usePolarisInboxTrayItemAudioClusterId_trayItem"}],type:"XDTInboxTrayItem",abstractKey:null};e.exports=a}),null);
__d("usePolarisInboxTrayItemInteractionLogger.react",["CometRelay","IgDirectInboxV2ItemInteractionFalcoEvent","PolarisNavChain","PolarisNotesTypes","react-compiler-runtime","usePolarisInboxTrayItemAudioClusterId.react","usePolarisInboxTrayItemContentTypes.react","usePolarisInboxTrayItemInteractionLogger_trayItem.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){switch(a){case d("PolarisNotesTypes").NoteAudienceOptionValues.BESTIES:return"close_friends";case d("PolarisNotesTypes").NoteAudienceOptionValues.MUTUAL_FOLLOWS:return"mutual_followers";default:return null}}function e(a){var e=d("react-compiler-runtime").c(5),f=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisInboxTrayItemInteractionLogger_trayItem.graphql"),a),g=c("usePolarisInboxTrayItemContentTypes.react")(f),i=c("usePolarisInboxTrayItemAudioClusterId.react")(f);if(e[0]!==i||e[1]!==g||e[2]!==(f==null?void 0:f.inbox_tray_item_id)||e[3]!==(f==null?void 0:(a=f.note_dict)==null?void 0:a.author_id)){var j;a=function(a){c("IgDirectInboxV2ItemInteractionFalcoEvent").log(function(){var b;return babelHelpers["extends"]({audio_cluster_id:i,content:g,nav_chain:(b=(b=c("PolarisNavChain").getInstance())==null?void 0:b.getNavChainForSend())!=null?b:"",note_id:f==null?void 0:f.inbox_tray_item_id,target_user_id:f==null?void 0:(b=f.note_dict)==null?void 0:b.author_id},a)})};e[0]=i;e[1]=g;e[2]=f==null?void 0:f.inbox_tray_item_id;e[3]=f==null?void 0:(j=f.note_dict)==null?void 0:j.author_id;e[4]=a}else a=e[4];j=a;return j}g.mapAudience=a;g.usePolarisInboxTrayItemInteractionLogger=e}),98);
__d("PolarisProfileNoteBubbleImpl.react",["CometRelay","JSResourceForInteraction","PolarisInboxTrayItemBubble.react","PolarisInboxTrayItemPopoverTrigger.react","PolarisProfileNoteBubbleImpl_trayItems.graphql","PolarisUA","deferredLoadComponent","emptyFunction","react","react-compiler-runtime","requireDeferredForDisplay","useIGDSLazyDialog","usePartialViewImpression","usePolarisInboxTrayItemImpressionLogger.react","usePolarisInboxTrayItemInteractionLogger.react"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useState,l=c("deferredLoadComponent")(c("requireDeferredForDisplay")("PolarisInboxTrayItemBottomSheetTrigger.react").__setRef("PolarisProfileNoteBubbleImpl.react"));function a(a){var e=d("react-compiler-runtime").c(11);a=a.trayItem;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisProfileNoteBubbleImpl_trayItems.graphql"),a);var f=k(!1),g=f[0];f=f[1];var i=d("usePolarisInboxTrayItemInteractionLogger.react").usePolarisInboxTrayItemInteractionLogger(a),m=c("usePolarisInboxTrayItemImpressionLogger.react")(a),n;e[0]!==m?(n={onImpressionStart:function(){return m()}},e[0]=m,e[1]=n):n=e[1];n=c("usePartialViewImpression")(n);var o;e[2]===Symbol["for"]("react.memo_cache_sentinel")?(o=c("JSResourceForInteraction")("PolarisProfileNoteComposerDialog.react").__setRef("PolarisProfileNoteBubbleImpl.react"),e[2]=o):o=e[2];o=c("useIGDSLazyDialog")(o);var p=o[0];e[3]!==g||e[4]!==i||e[5]!==p||e[6]!==a?(o=d("PolarisUA").isMobile()?j.jsx(l,{onLeaveNewNoteClick:function(){return p({},c("emptyFunction"))},onTriggerPress:function(){return i({action:"tap"})},pogInfo:a.pog_info,trayItem:a,children:j.jsx(c("PolarisInboxTrayItemBubble.react"),{trayItem:a})}):j.jsx(c("PolarisInboxTrayItemPopoverTrigger.react"),{onLeaveNewNoteClick:function(){return p({},c("emptyFunction"))},onTriggerPress:function(){return i({action:"tap"})},onVisibilityChange:f,popoverPositionOverride:"start",trayItem:a,children:!g&&j.jsx(c("PolarisInboxTrayItemBubble.react"),{trayItem:a})}),e[3]=g,e[4]=i,e[5]=p,e[6]=a,e[7]=o):o=e[7];e[8]!==n||e[9]!==o?(f=j.jsx("div",{ref:n,children:o}),e[8]=n,e[9]=o,e[10]=f):f=e[10];return f}g["default"]=a}),98);
__d("PolarisProfileNoteBubbleQuery.graphql",["PolarisProfileNoteBubbleQuery_instagramRelayOperation","relay-runtime"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"user_id"}],c=[{fields:[{kind:"Variable",name:"user_id",variableName:"user_id"}],kind:"ObjectValue",name:"request"}],d={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},e={alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null};return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"PolarisProfileNoteBubbleQuery",selections:[{alias:null,args:c,concreteType:"XDTGetInboxTrayItemsResponse",kind:"LinkedField",name:"xdt_get_inbox_tray_items",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTInboxTrayItem",kind:"LinkedField",name:"inbox_tray_items",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisProfileNoteBubbleImpl_trayItems"}],storageKey:null}],storageKey:null}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"PolarisProfileNoteBubbleQuery",selections:[{alias:null,args:c,concreteType:"XDTGetInboxTrayItemsResponse",kind:"LinkedField",name:"xdt_get_inbox_tray_items",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTInboxTrayItem",kind:"LinkedField",name:"inbox_tray_items",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTInboxTrayItemPogInfo",kind:"LinkedField",name:"pog_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"pog_users",plural:!0,selections:[d,{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url_hd",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"interop_messaging_user_fbid",storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTLazyNoteDict",kind:"LinkedField",name:"note_dict",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"note_style",storageKey:null},{alias:null,args:null,concreteType:"XDTNoteResponseInfo",kind:"LinkedField",name:"note_response_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMusicNoteResponseInfo",kind:"LinkedField",name:"music_note_response_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMusicInfo",kind:"LinkedField",name:"music_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTTrackData",kind:"LinkedField",name:"music_asset_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"display_artist",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_explicit",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"progressive_download_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"web_30s_preview_download_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"cover_artwork_uri",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"audio_cluster_id",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTMusicConsumptionInfoDict",kind:"LinkedField",name:"music_consumption_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"audio_asset_start_time_in_ms",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"should_mute_audio",storageKey:null}],storageKey:null}],storageKey:null},e],storageKey:null},{alias:null,args:null,concreteType:"XDTNotePogVideoResponseInfo",kind:"LinkedField",name:"note_pog_video_response_info",plural:!1,selections:[e,{alias:null,args:null,concreteType:"XDTNotePogVideoDict",kind:"LinkedField",name:"video_dict",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,concreteType:"XDTVideoVersion",kind:"LinkedField",name:"video_versions",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null}],storageKey:null},d],storageKey:null},{alias:null,args:null,concreteType:"XDTNotePogImageDict",kind:"LinkedField",name:"image_dict",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"image_url",storageKey:null},d],storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_emoji_only",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"author_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"audience",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"created_at",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"inbox_tray_item_id",storageKey:null}],storageKey:null}],storageKey:null}]},params:{id:b("PolarisProfileNoteBubbleQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_get_inbox_tray_items"]},name:"PolarisProfileNoteBubbleQuery",operationKind:"query",text:null}}}();b("relay-runtime").PreloadableQueryRegistry.set(a.params.id,a);e.exports=a}),null);
__d("PolarisProfileNoteBubble.react",["fbt","CometPressable.react","CometRelay","IGDSBox.react","IGDSSpinner.react","JSResourceForInteraction","PolarisInboxTrayBubble.react","PolarisInboxTrayItemLayout.react","PolarisProfileNoteBubbleImpl.react","PolarisProfileNoteBubbleQuery.graphql","PolarisUA","QPLUserFlow","emptyFunction","qpl","react","react-compiler-runtime","useIGDSLazyDialog"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||d("react"),l={fallbackContainer:{alignItems:"x6s0dn4",backgroundColor:"xvbhtw8",bottom:"x1ey2m1c",color:"x5n08af",display:"x78zum5",insetInlineEnd:"xtijo5x",insetInlineStart:"x1o0tod",left:null,right:null,justifyContent:"xl56j7k",paddingTop:"xyamay9",paddingInlineEnd:"xv54qhq",paddingBottom:"x1l90r2v",paddingInlineStart:"xf7dkkf",position:"xixxii4",top:"x13vifvy",$$css:!0},trigger:{zIndex:"x1vjfegm",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(15),f=a.children,g=a.isOwnProfile;a=a.queryReference;a=d("CometRelay").usePreloadedQuery(i!==void 0?i:i=b("PolarisProfileNoteBubbleQuery.graphql"),a).xdt_get_inbox_tray_items.inbox_tray_items;var j;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(j=c("JSResourceForInteraction")("PolarisProfileNoteComposerDialog.react").__setRef("PolarisProfileNoteBubble.react"),e[0]=j):j=e[0];j=c("useIGDSLazyDialog")(j,d("PolarisUA").isMobile()?m:void 0);var n=j[0];e[1]!==n?(j=function(){n({},c("emptyFunction")),c("QPLUserFlow").start(c("qpl")._(379203828,"2170"))},e[1]=n,e[2]=j):j=e[2];j=j;if(a.length===0&&!g)return f;var o=d("PolarisUA").isMobile()?"small":"medium",p;e[3]===Symbol["for"]("react.memo_cache_sentinel")?(p={0:{className:"x78zum5 x1q0g3np xjcabj4 xl56j7k"},1:{className:"x78zum5 x1q0g3np xl56j7k"}}[!!d("PolarisUA").isMobile()<<0],e[3]=p):p=e[3];var q;e[4]!==j||e[5]!==g||e[6]!==a[0]||e[7]!==a.length?(q=a.length===0&&g?k.jsx(c("CometPressable.react"),{onPress:j,overlayDisabled:!0,xstyle:l.trigger,children:k.jsx(c("PolarisInboxTrayBubble.react"),{children:k.jsx("div",babelHelpers["extends"]({className:"x1roi4f4"},{children:h._(/*BTDS*/"Note...")}))})}):k.jsx("div",babelHelpers["extends"]({className:"x78zum5 x1q0g3np xl56j7k x1vjfegm"},{children:k.jsx(c("PolarisProfileNoteBubbleImpl.react"),{isOwnProfile:g,trayItem:a[0]})})),e[4]=j,e[5]=g,e[6]=a[0],e[7]=a.length,e[8]=q):q=e[8];e[9]!==f||e[10]!==q?(j=k.jsx(d("PolarisInboxTrayItemLayout.react").PolarisInboxTrayItemLayout,{innerSlot:f,rootSlot:q,size:o}),e[9]=f,e[10]=q,e[11]=j):j=e[11];e[12]!==p||e[13]!==j?(g=k.jsx("div",babelHelpers["extends"]({},p,{children:j})),e[12]=p,e[13]=j,e[14]=g):g=e[14];return g}function m(){return k.jsx(c("IGDSBox.react"),{xstyle:l.fallbackContainer,children:k.jsx(c("IGDSSpinner.react"),{animated:!0,size:"medium"})})}m.displayName=m.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("PolarisProfileNoteWrapper.react",["PolarisSuspenseWithErrorBoundary.react","deferredLoadComponent","react","react-compiler-runtime","requireDeferredForDisplay"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("deferredLoadComponent")(c("requireDeferredForDisplay")("PolarisProfileNoteBubble.react").__setRef("PolarisProfileNoteWrapper.react"));function a(a){var b=d("react-compiler-runtime").c(4),e=a.children,f=a.isOwnProfile;a=a.profileNoteQuery;var g;b[0]!==e||b[1]!==f||b[2]!==a?(g=a?i.jsx(c("PolarisSuspenseWithErrorBoundary.react"),{errorRenderer:function(){return e},loadingRenderer:e,children:i.jsx(j,{isOwnProfile:f,queryReference:a,children:e})}):e,b[0]=e,b[1]=f,b[2]=a,b[3]=g):g=b[3];return g}g["default"]=a}),98);