<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e(config('app.name', 'Capstone')); ?></title>

    <!-- IMMEDIATE MODAL Z-INDEX FIX - HIGHEST PRIORITY -->
    <style>
    /* EMERGENCY MODAL FIXES - APPLIED IMMEDIATELY BEFORE ANY OTHER CSS */
    .modal-backdrop, .modal-backdrop.fade, .modal-backdrop.show {
        z-index: 9999998 !important;
        position: fixed !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
    }
    .modal, .modal.fade, .modal.show, .modal.fade.show {
        z-index: 9999999 !important;
        position: fixed !important;
    }
    .modal-dialog {
        z-index: 10000000 !important;
        position: relative !important;
        pointer-events: auto !important;
    }
    .modal-content {
        z-index: 10000001 !important;
        position: relative !important;
        pointer-events: auto !important;
        background: white !important;
    }
    .cook-header, .kitchen-header, .student-header, .admin-header,
    header.cook-header, header.kitchen-header, header.student-header, header.admin-header {
        z-index: 1100 !important;
        position: fixed !important;
    }
    /* Force all modal elements to be clickable */
    .modal *, .modal input, .modal textarea, .modal button, .modal select,
    .modal .btn, .modal .form-control, .modal a {
        pointer-events: auto !important;
    }
    </style>

    <!-- IMMEDIATE MODAL FIX SCRIPT -->
    <script>
    // IMMEDIATE MODAL FIX FUNCTION - AVAILABLE IMMEDIATELY
    window.forceFixModal = function() {
        // Only log when there are visible modals to fix
        const visibleModals = document.querySelectorAll('.modal.show').length;
        if (visibleModals === 0) return; // No visible modals, skip processing

        // Fix all backdrops
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => {
            backdrop.style.cssText = `
                z-index: 9999998 !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background-color: rgba(0, 0, 0, 0.5) !important;
                pointer-events: auto !important;
            `;
        });

        // Fix all modals
        document.querySelectorAll('.modal').forEach(modal => {
            if (modal.classList.contains('show') || modal.style.display === 'block') {
                modal.style.cssText = `
                    z-index: 9999999 !important;
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100vw !important;
                    height: 100vh !important;
                    display: block !important;
                    pointer-events: auto !important;
                `;

                const dialog = modal.querySelector('.modal-dialog');
                if (dialog) {
                    dialog.style.cssText = `
                        z-index: 10000000 !important;
                        position: relative !important;
                        pointer-events: auto !important;
                        margin: 1.75rem auto !important;
                        transform: none !important;
                    `;
                }

                const content = modal.querySelector('.modal-content');
                if (content) {
                    content.style.cssText = `
                        z-index: 10000001 !important;
                        position: relative !important;
                        pointer-events: auto !important;
                        background: white !important;
                        border: none !important;
                        border-radius: 12px !important;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
                    `;
                }

                // Make all elements clickable
                modal.querySelectorAll('*').forEach(el => {
                    el.style.pointerEvents = 'auto';
                });
            }
        });

        // Only log when fixes are actually applied
        const visibleModals = document.querySelectorAll('.modal.show').length;
        if (visibleModals > 0) {
            console.log(`🔧 Modal fixes applied to ${visibleModals} visible modals`);
        }
    };

    // Auto-fix only when needed (much less aggressive)
    let modalFixInterval = null;

    // Function to start modal fixing when needed
    window.startModalFix = function() {
        if (!modalFixInterval) {
            modalFixInterval = setInterval(window.forceFixModal, 500); // Every 500ms instead of 50ms
        }
    };

    // Function to stop modal fixing
    window.stopModalFix = function() {
        if (modalFixInterval) {
            clearInterval(modalFixInterval);
            modalFixInterval = null;
        }
    };

    // Only run modal fix when modals are actually present
    const observer = new MutationObserver(function(mutations) {
        const hasModals = document.querySelectorAll('.modal.show').length > 0;
        if (hasModals) {
            window.startModalFix();
        } else {
            window.stopModalFix();
        }
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class']
    });
    </script>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="<?php echo e(asset('css/app.css')); ?>" onerror="console.warn('app.css not found')">
    <link rel="stylesheet" href="<?php echo e(asset('css/sidebar.css')); ?>" onerror="console.warn('sidebar.css not found')">


    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">

    <!-- Enhanced Custom CSS -->
    <style>
        :root {
            --sidebar-width: 250px;
            --navbar-height: 70px;
            --primary-color: #22bbea;
            --secondary-color: #ff9933;
            --success-color: #22bbea;
            --warning-color: #ff9933;
            --bg-color: #f8f9fc;
            --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --border-radius: 8px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

            /* Z-index system - FIXED HIERARCHY */
            --z-sidebar: 1000;
            --z-header: 1010;
            --z-dropdown: 1040;
            --z-modal-backdrop: 1055;
            --z-modal: 1060;
            --z-notification: 1070;
            --z-tooltip: 1080;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', sans-serif;
            background-color: var(--bg-color);
            overflow-x: hidden;
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        /* Enhanced Sidebar Styles */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: var(--z-sidebar);
            background:
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.08);
            transition: var(--transition);
            border-right: 1px solid rgba(34, 187, 234, 0.1);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
            opacity: 0.6;
        }

        /* Mobile Sidebar */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 280px;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1030;
                display: none;
            }

            .sidebar-overlay.show {
                display: block;
            }
        }

        .sidebar-heading {
            font-size: 0.65rem;
            letter-spacing: 0.1rem;
        }

        .sidebar .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(0, 0, 0, 0.7);
            transition: all 0.2s;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .sidebar .nav-link .icon {
            font-size: 1.1rem;
            margin-right: 0.75rem;
            width: 1.25rem;
            text-align: center;
        }

        .sidebar .nav-link.active {
            color: #ffffff;
            background-color: #22bbea;
            font-weight: bold;
        }
        
        .sidebar .nav-link:hover {
            color: #ffffff;
            background-color: var(--primary-color);
            transform: translateX(3px);
        }

        .sidebar hr {
            margin: 1rem 0;
            opacity: 0.15;
        }

        .logout-btn {
            width: 100%;
            text-align: left;
            background: none;
            border: none;
            padding: 0.75rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            color: #dc3545;
            transition: all 0.2s;
        }

        .logout-btn:hover {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            .sidebar {
                padding: 0.75rem;
            }

            .sidebar-header {
                padding: 1rem;
            }

            .sidebar .nav-link {
                padding: 0.6rem 0.75rem;
            }

            .sidebar-category {
                margin: 1.25rem 0 0.5rem 0.5rem;
            }
        }

        /* Enhanced Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            padding-top: calc(var(--navbar-height) + 20px);
            min-height: 100vh;
            background: var(--bg-color);
            padding: calc(var(--navbar-height) + 20px) 1.5rem 1.5rem;
            transition: var(--transition);
            position: relative;
            z-index: 1;
        }

        .main-content::before {
            content: '';
            position: fixed;
            top: 0;
            left: var(--sidebar-width);
            right: 0;
            height: var(--navbar-height);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: var(--z-header);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* Enhanced Mobile Header Styles */
        .mobile-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: var(--navbar-height);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(34, 187, 234, 0.2);
            z-index: var(--z-header);
            display: none;
            transition: var(--transition);
        }

        .mobile-menu-btn,
        .mobile-user-btn {
            padding: 0.5rem !important;
            font-size: 1rem !important;
            min-width: 44px !important;
            height: 44px !important;
            border-radius: var(--border-radius) !important;
            background: rgba(34, 187, 234, 0.1);
            border: 1px solid rgba(34, 187, 234, 0.2);
            color: var(--primary-color);
            transition: var(--transition);
        }

        .mobile-menu-btn:hover,
        .mobile-user-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-1px);
        }

        .mobile-title {
            font-size: 1.1rem !important;
            font-weight: 600;
            flex: 1;
            text-align: center;
            margin: 0 1rem;
            color: var(--primary-color);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .mobile-dropdown {
            min-width: 180px !important;
            font-size: 0.9rem !important;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(34, 187, 234, 0.2);
        }

        .mobile-dropdown .dropdown-item {
            padding: 0.4rem 0.8rem !important;
        }

        /* Enhanced Responsive Design - Fix Overlapping Issues */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                z-index: var(--z-sidebar);
                width: min(280px, 85vw);
                box-shadow: 4px 0 30px rgba(0, 0, 0, 0.15);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0 !important;
                padding: calc(var(--navbar-height) + 10px) 1rem 1rem !important;
                width: 100vw !important;
                max-width: 100vw !important;
                overflow-x: hidden !important;
            }

            .main-content::before {
                left: 0;
            }

            .mobile-header {
                display: flex !important;
                align-items: center;
                justify-content: space-between;
                padding: 0 1rem;
                height: var(--navbar-height);
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.4);
                backdrop-filter: blur(2px);
                z-index: 1030;
                display: none;
                transition: var(--transition);
            }

            .sidebar-overlay.show {
                display: block;
                animation: fadeIn 0.3s ease-out;
            }

            /* Fix notification popup positioning on mobile */
            .notification-popup {
                top: calc(var(--navbar-height) + 10px) !important;
                left: 10px !important;
                right: 10px !important;
                max-width: none !important;
                z-index: var(--z-notification) !important;
            }

            /* Fix dropdown positioning */
            .dropdown-menu {
                z-index: var(--z-dropdown) !important;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: calc(var(--navbar-height) + 5px) 0.5rem 0.5rem !important;
            }

            .sidebar {
                width: min(260px, 90vw);
            }
        }

        /* Animation keyframes */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); }
            to { transform: translateX(100%); }
        }

        /* Preserve original sidebar navigation styles */
        .main-content .nav-link {
            color: var(--secondary-color);
            transition: all 0.2s ease-in-out;
            border-radius: 0.35rem;
            margin: 0.2rem 0;
        }

        .main-content .nav-link.active {
            color: #ffffff;
            background-color: #22bbea;
            font-weight: bold;
        }

        .main-content .nav-link:hover {
            color: #ffffff;
            background-color: var(--primary-color);
            transform: translateX(3px);
        }

        .navbar-brand {
            font-weight: 600;
            color: var(--primary-color);
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 0.5rem;
            box-shadow: var(--card-shadow);
        }

        .card-header {
            background-color: transparent;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* Table Styles */
        .table th {
            font-weight: 600;
            color: var(--secondary-color);
            border-top: none;
        }

        .table td {
            vertical-align: middle;
        }

        /* Responsive Table Styles */
        @media (max-width: 768px) {
            .table-responsive {
                border: none;
                margin-bottom: 0;
            }

            .table th,
            .table td {
                padding: 0.5rem 0.25rem;
                font-size: 0.875rem;
            }

            .table th {
                font-size: 0.8rem;
            }

            /* Stack table cells on very small screens */
            @media (max-width: 576px) {
                .table-stack {
                    display: block;
                }

                .table-stack thead {
                    display: none;
                }

                .table-stack tbody,
                .table-stack tr,
                .table-stack td {
                    display: block;
                    width: 100%;
                }

                .table-stack tr {
                    border: 1px solid #dee2e6;
                    margin-bottom: 0.5rem;
                    border-radius: 0.375rem;
                    padding: 0.5rem;
                    background: white;
                }

                .table-stack td {
                    border: none;
                    padding: 0.25rem 0;
                    position: relative;
                    padding-left: 50%;
                }

                .table-stack td:before {
                    content: attr(data-label) ": ";
                    position: absolute;
                    left: 0;
                    width: 45%;
                    font-weight: bold;
                    color: var(--secondary-color);
                }
            }
        }

        /* Button Styles */
        .btn {
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #1a9bd1;
            border-color: #1a9bd1;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background-color: #e6851a;
            border-color: #e6851a;
            color: white;
        }

        .btn-warning {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }

        .btn-warning:hover {
            background-color: #e6851a;
            border-color: #e6851a;
            color: white;
        }

        /* Responsive Button and Form Styles */
        @media (max-width: 768px) {
            .btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }

            .btn-group {
                flex-wrap: wrap;
            }

            .btn-group .btn {
                margin-bottom: 0.25rem;
            }

            /* Stack buttons vertically on small screens */
            .btn-stack-mobile {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .btn-stack-mobile .btn {
                width: 100%;
            }

            /* Form controls */
            .form-control,
            .form-select {
                font-size: 16px; /* Prevent zoom on iOS */
            }

            /* Card adjustments */
            .card {
                margin-bottom: 1rem;
            }

            .card-body {
                padding: 1rem 0.75rem;
            }

            .card-header {
                padding: 0.75rem;
            }

            /* Modal adjustments */
            .modal-dialog {
                margin: 0.5rem;
            }

            .modal-content {
                border-radius: 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .btn {
                padding: 0.4rem 0.6rem;
                font-size: 0.8rem;
            }

            .card-body {
                padding: 0.75rem 0.5rem;
            }

            .card-header {
                padding: 0.5rem;
                font-size: 0.9rem;
            }

            /* Hide less important columns on very small screens */
            .d-none-xs {
                display: none !important;
            }

            /* Compact spacing */
            .row {
                margin-left: -0.25rem;
                margin-right: -0.25rem;
            }

            .row > * {
                padding-left: 0.25rem;
                padding-right: 0.25rem;
            }
        }
    </style>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body>
    <?php if(auth()->guard()->check()): ?>
        <?php if(request()->is('login') || request()->is('register')): ?>
            <?php echo $__env->yieldContent('content'); ?>
        <?php else: ?>
            <!-- Sidebar -->
            <?php if(Auth::user()->role == 'student'): ?>
                <?php echo $__env->make('Component.student-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <!-- Header -->
                <?php echo $__env->make('Component.student-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php elseif(Auth::user()->role == 'cook'): ?>
                <?php echo $__env->make('Component.cook-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <!-- Header -->
                <?php echo $__env->make('Component.cook-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php elseif(Auth::user()->role == 'kitchen'): ?>
                <?php echo $__env->make('Component.kitchen-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <!-- Header -->
                <?php echo $__env->make('Component.kitchen-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php else: ?>
                <?php echo $__env->make('Component.admin-sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <!-- Header -->
                <?php echo $__env->make('Component.admin-header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>

            <!-- Enhanced Mobile Header -->
            <div class="d-md-none mobile-header">
                <div class="d-flex align-items-center justify-content-between px-3 py-2" style="height: 100%;">
                    <button class="mobile-menu-btn" id="mobileSidebarToggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="mobile-title">
                        <img src="<?php echo e(asset('images/PN-Logo.png')); ?>" alt="Logo" style="height: 32px; margin-right: 8px;">
                        <?php echo e(config('app.name', 'Capstone')); ?>

                    </div>
                    <div class="dropdown">
                        <button class="mobile-user-btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end mobile-dropdown">
                            <li><span class="dropdown-item-text"><?php echo e(Auth::user()->name); ?></span></li>
                            <li><span class="dropdown-item-text small text-muted"><?php echo e(ucfirst(Auth::user()->role)); ?></span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="bi bi-box-arrow-right me-2"></i>Logout
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Sidebar Overlay for Mobile -->
            <div class="sidebar-overlay" id="sidebarOverlay"></div>

            <!-- Main Content -->
            <main class="main-content">
                <?php if(session('error')): ?>
                    <div class="alert alert-danger mb-4">
                        <?php echo e(session('error')); ?>

                    </div>
                <?php endif; ?>
                <?php if(session('success')): ?>
                    <div class="alert alert-success mb-4">
                        <?php echo e(session('success')); ?>

                    </div>
                <?php endif; ?>
                <?php echo $__env->yieldContent('content'); ?>
            </main>
        <?php endif; ?>
    <?php else: ?>
        <div class="auth-wrapper d-flex align-items-center justify-content-center p-4">
            <div class="auth-card p-4" style="width: 100%; max-width: 420px;">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Global Error Prevention System -->
    <script src="<?php echo e(asset('js/error-prevention.js')); ?>"></script>





    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- UNIVERSAL MODAL FIXES - APPLIED TO ALL USER TYPES -->
    <style>
    /* ULTIMATE MODAL Z-INDEX FIXES - MAXIMUM PRIORITY */

    /* Force headers to stay below modals */
    .cook-header,
    .kitchen-header,
    .student-header,
    .admin-header,
    header.cook-header,
    header.kitchen-header,
    header.student-header,
    header.admin-header {
        z-index: 1100 !important;
        position: fixed !important;
    }

    /* AGGRESSIVE MODAL FIXES - OVERRIDE EVERYTHING */
    .modal-backdrop,
    .modal-backdrop.fade,
    .modal-backdrop.show {
        z-index: 9999998 !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        pointer-events: auto !important;
    }

    /* Modal container - MAXIMUM Z-INDEX */
    .modal,
    .modal.fade,
    .modal.show,
    .modal.fade.show {
        z-index: 9999999 !important;
        position: fixed !important;
        display: block !important;
    }

    /* Modal dialog - ABOVE EVERYTHING */
    .modal-dialog {
        z-index: 10000000 !important;
        position: relative !important;
        pointer-events: auto !important;
        margin: 1.75rem auto !important;
        transform: none !important;
    }

    /* Modal content - HIGHEST POSSIBLE Z-INDEX */
    .modal-content {
        z-index: 10000001 !important;
        position: relative !important;
        pointer-events: auto !important;
        background: white !important;
        border: none !important;
        border-radius: 12px !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
    }

    /* Force all modal elements to be visible and clickable */
    .modal *,
    .modal input,
    .modal textarea,
    .modal button,
    .modal select,
    .modal .btn,
    .modal .form-control,
    .modal a,
    .modal .dropdown-menu,
    .modal .form-check-input,
    .modal .form-check-label,
    .modal .modal-header,
    .modal .modal-body,
    .modal .modal-footer {
        pointer-events: auto !important;
        z-index: inherit !important;
    }

    /* Fix body scroll lock */
    body.modal-open {
        overflow: hidden !important;
        padding-right: 0 !important;
    }

    /* Ensure dropdowns in headers stay below modals */
    .profile-menu,
    .notification-dropdown,
    .dropdown-menu {
        z-index: 1200 !important;
    }

    /* Toast notifications above modals */
    .toast-container {
        z-index: 10000002 !important;
    }

    /* SPECIFIC MODAL OVERRIDES */
    #editDeadlineModal,
    #editPollDeadlineModal,
    #pollResultsModal,
    #feedbackDetailsModal,
    #imageModal {
        z-index: 9999999 !important;
    }

    #editDeadlineModal .modal-dialog,
    #editPollDeadlineModal .modal-dialog,
    #pollResultsModal .modal-dialog,
    #feedbackDetailsModal .modal-dialog,
    #imageModal .modal-dialog {
        z-index: 10000000 !important;
    }

    #editDeadlineModal .modal-content,
    #editPollDeadlineModal .modal-content,
    #pollResultsModal .modal-content,
    #feedbackDetailsModal .modal-content,
    #imageModal .modal-content {
        z-index: 10000001 !important;
    }
    </style>

    <script>
    // UNIVERSAL MODAL FUNCTIONS - AVAILABLE TO ALL USER TYPES

    function universalCleanupModals() {
        console.log('🧹 Universal modal cleanup');

        // Remove all modal backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());

        // Reset body classes and styles
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';

        // Hide all modals
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.classList.remove('show');
            modal.style.display = 'none';
            modal.setAttribute('aria-hidden', 'true');
            modal.removeAttribute('aria-modal');
            modal.removeAttribute('role');
        });
    }

    function universalShowModal(modalId) {
        console.log('🔄 Universal show modal:', modalId);

        // Clean up first
        universalCleanupModals();

        const modalElement = document.getElementById(modalId);
        if (!modalElement) {
            console.error('❌ Modal not found:', modalId);
            return false;
        }

        try {
            // Apply universal modal styling
            modalElement.style.cssText = `
                z-index: 1450 !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background-color: rgba(0, 0, 0, 0.5) !important;
                display: block !important;
                pointer-events: auto !important;
            `;

            modalElement.classList.add('show');
            modalElement.setAttribute('aria-modal', 'true');
            modalElement.setAttribute('role', 'dialog');
            modalElement.removeAttribute('aria-hidden');

            // Style the dialog
            const modalDialog = modalElement.querySelector('.modal-dialog');
            if (modalDialog) {
                modalDialog.style.cssText = `
                    z-index: 1460 !important;
                    position: relative !important;
                    pointer-events: auto !important;
                    margin: 1.75rem auto !important;
                `;
            }

            // Style the content
            const modalContent = modalElement.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.cssText = `
                    z-index: 1470 !important;
                    position: relative !important;
                    pointer-events: auto !important;
                    background: white !important;
                    border: none !important;
                    border-radius: 12px !important;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
                `;
            }

            // Make all interactive elements clickable
            modalElement.querySelectorAll('input, textarea, button, select, .btn, .form-control, a, .form-check-input, .form-check-label').forEach(el => {
                el.style.pointerEvents = 'auto';
            });

            // Set body modal state
            document.body.classList.add('modal-open');
            document.body.style.overflow = 'hidden';

            // Add close functionality
            modalElement.onclick = function(e) {
                if (e.target === modalElement) {
                    universalHideModal(modalId);
                }
            };

            // Close button functionality
            modalElement.querySelectorAll('[data-bs-dismiss="modal"], .btn-close').forEach(btn => {
                btn.onclick = function(e) {
                    e.preventDefault();
                    universalHideModal(modalId);
                };
            });

            console.log('✅ Universal modal shown:', modalId);
            return true;

        } catch (error) {
            console.error('❌ Error showing modal:', error);
            return false;
        }
    }

    function universalHideModal(modalId) {
        console.log('🔄 Universal hide modal:', modalId);

        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            modalElement.classList.remove('show');
            modalElement.style.display = 'none';
            modalElement.setAttribute('aria-hidden', 'true');
            modalElement.removeAttribute('aria-modal');
            modalElement.removeAttribute('role');
        }

        // Clean up after hiding
        setTimeout(() => {
            universalCleanupModals();
        }, 100);
    }

    // Make functions globally available
    window.universalCleanupModals = universalCleanupModals;
    window.universalShowModal = universalShowModal;
    window.universalHideModal = universalHideModal;

    // Also provide aliases for backward compatibility
    window.showModalSafely = universalShowModal;
    window.hideModalSafely = universalHideModal;
    window.cleanupAllModals = universalCleanupModals;

    // AGGRESSIVE MODAL MONITOR - FORCE FIX ANY MODAL
    function forceFixAllModals() {
        const modals = document.querySelectorAll('.modal');
        const backdrops = document.querySelectorAll('.modal-backdrop');

        // Force fix backdrops
        backdrops.forEach(backdrop => {
            backdrop.style.cssText = `
                z-index: 9999998 !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background-color: rgba(0, 0, 0, 0.5) !important;
                pointer-events: auto !important;
            `;
        });

        // Force fix modals
        modals.forEach(modal => {
            if (modal.classList.contains('show') || modal.style.display === 'block') {
                modal.style.cssText = `
                    z-index: 9999999 !important;
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100vw !important;
                    height: 100vh !important;
                    display: block !important;
                    pointer-events: auto !important;
                `;

                const dialog = modal.querySelector('.modal-dialog');
                if (dialog) {
                    dialog.style.cssText = `
                        z-index: 10000000 !important;
                        position: relative !important;
                        pointer-events: auto !important;
                        margin: 1.75rem auto !important;
                        transform: none !important;
                    `;
                }

                const content = modal.querySelector('.modal-content');
                if (content) {
                    content.style.cssText = `
                        z-index: 10000001 !important;
                        position: relative !important;
                        pointer-events: auto !important;
                        background: white !important;
                        border: none !important;
                        border-radius: 12px !important;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
                    `;
                }

                // Make all elements clickable
                modal.querySelectorAll('*').forEach(el => {
                    el.style.pointerEvents = 'auto';
                });
            }
        });
    }

    // Monitor for new modals and fix them immediately
    function startModalMonitor() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            if (node.classList && node.classList.contains('modal')) {
                                console.log('🔧 New modal detected, force fixing...');
                                setTimeout(forceFixAllModals, 10);
                            }
                            if (node.classList && node.classList.contains('modal-backdrop')) {
                                console.log('🔧 New backdrop detected, force fixing...');
                                setTimeout(forceFixAllModals, 10);
                            }
                        }
                    });
                }
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    if (mutation.target.classList.contains('modal') && mutation.target.classList.contains('show')) {
                        console.log('🔧 Modal shown, force fixing...');
                        setTimeout(forceFixAllModals, 10);
                    }
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class', 'style']
        });

        // Also monitor every 100ms for any modals that might appear
        setInterval(forceFixAllModals, 100);
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        console.log('✅ Universal modal system initialized');

        // Clean up any existing modal states
        universalCleanupModals();

        // Start aggressive modal monitoring
        startModalMonitor();

        // Force fix any existing modals
        setTimeout(forceFixAllModals, 100);

        // Add global event listeners
        document.addEventListener('hidden.bs.modal', function(e) {
            setTimeout(() => {
                universalCleanupModals();
            }, 100);
        });

        document.addEventListener('shown.bs.modal', function(e) {
            console.log('🔧 Modal shown event, force fixing...');
            setTimeout(forceFixAllModals, 10);
        });

        // Emergency cleanup shortcut (Ctrl+Alt+M)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.altKey && e.key === 'm') {
                console.log('🚨 Emergency modal cleanup');
                universalCleanupModals();
                e.preventDefault();
            }

            // Force fix shortcut (Ctrl+Alt+F)
            if (e.ctrlKey && e.altKey && e.key === 'f') {
                console.log('🔧 Force fix all modals');
                forceFixAllModals();
                e.preventDefault();
            }
        });
    });
    </script>
</body>
</html>
<?php /**PATH C:\setup\Capstone\Github\Capstone14\capstone\resources\views/layouts/app.blade.php ENDPATH**/ ?>