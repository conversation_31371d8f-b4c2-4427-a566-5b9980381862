;/*FB_PKG_DELIM*/

__d("IGDBroadcastChannelQPTooltip.react",["cr:1017","cr:1271","react","useHasIGBroadcastChannelTab"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useMemo;function a(){var a=c("useHasIGBroadcastChannelTab")();return j(function(){return a&&b("cr:1017")!==null&&b("cr:1271")!==null?i.jsx(b("cr:1271"),{slot:b("cr:1017").SLOTS.inbox_folder_menu}):null},[a])}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGPresenceUnifiedSetupQuery.graphql",["IGPresenceUnifiedSetupQuery_instagramRelayOperation","relay-runtime"],(function(a,b,c,d,e,f){"use strict";a={fragment:{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"IGPresenceUnifiedSetupQuery",selections:[{args:null,kind:"FragmentSpread",name:"useIGInitPresenceUnifiedClient_Query"}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[],kind:"Operation",name:"IGPresenceUnifiedSetupQuery",selections:[{alias:null,args:[{kind:"Literal",name:"_request_data",value:{}}],concreteType:"XDTGetPresenceDisabledResponse",kind:"LinkedField",name:"xdt_api__v1__get__presence__disabled",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"disabled",storageKey:null}],storageKey:"xdt_api__v1__get__presence__disabled(_request_data:{})"}]},params:{id:b("IGPresenceUnifiedSetupQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__get__presence__disabled"]},name:"IGPresenceUnifiedSetupQuery",operationKind:"query",text:null}};b("relay-runtime").PreloadableQueryRegistry.set(a.params.id,a);e.exports=a}),null);
__d("useIGInitPresenceUnifiedClient_Query.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"useIGInitPresenceUnifiedClient_Query",selections:[{alias:null,args:[{kind:"Literal",name:"_request_data",value:{}}],concreteType:"XDTGetPresenceDisabledResponse",kind:"LinkedField",name:"xdt_api__v1__get__presence__disabled",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"disabled",storageKey:null}],storageKey:"xdt_api__v1__get__presence__disabled(_request_data:{})"}],type:"Query",abstractKey:null};e.exports=a}),null);
__d("useIGInitPresenceUnifiedClient",["RelayHooks","asyncToGeneratorRuntime","promiseDone","react","useIGInitPresenceUnifiedClient_Query.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(i||d("react")).useEffect;function a(a,e){e=d("RelayHooks").useFragment(h!==void 0?h:h=b("useIGInitPresenceUnifiedClient_Query.graphql"),e);e=e.xdt_api__v1__get__presence__disabled;var f=(e=e.disabled)!=null?e:!0;j(function(){var d=function(){var c=b("asyncToGeneratorRuntime").asyncToGenerator(function*(){!f?yield a.startStream():yield a.closeStream()});return function(){return c.apply(this,arguments)}}();c("promiseDone")(d());return function(){return c("promiseDone")(a.closeStream())}},[a,f])}g["default"]=a}),98);
__d("usePresenceUnifiedActivityMonitor.react",["CometUserActivityMonitor","RealtimeNexusSessionDataTypes","promiseDone","react","useDebounced"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useCallback,j=b.useEffect,k=2e3;function a(a){var b=i(function(b){c("promiseDone")(a.reportUserPresence(b))},[a]),e=c("useDebounced")(b,k);j(function(){var b=function(b){b==="ACTIVE"?(e(d("RealtimeNexusSessionDataTypes").PresenceAvailability.ACTIVE),a.addAdditionalContacts([]),a.startAdditionalContactsPolling()):e(d("RealtimeNexusSessionDataTypes").PresenceAvailability.IDLE)},c=d("CometUserActivityMonitor").getActivityState();b(c);var f=d("CometUserActivityMonitor").subscribe(b);return function(){return f&&f.remove()}},[a,e])}g["default"]=a}),98);
__d("IGPresenceUnifiedSetup.react",["IGPresenceUnifiedSetupQuery.graphql","RelayHooks","useIGInitPresenceUnifiedClient","usePresenceUnifiedActivityMonitor.react"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var e=a.client;a=a.presenceSetupQueryRef;a=d("RelayHooks").usePreloadedQuery(h!==void 0?h:h=b("IGPresenceUnifiedSetupQuery.graphql"),a);c("useIGInitPresenceUnifiedClient")(e,a);c("usePresenceUnifiedActivityMonitor.react")(e);return null}g["default"]=a}),98);