;/*FB_PKG_DELIM*/

__d("PolarisCountryBlockDeferred.react",["CometPlaceholder.react","PolarisProfileTabContentSpinner.react","deferredLoadComponent","react","react-compiler-runtime","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("deferredLoadComponent")(c("requireDeferred")("PolarisCountryBlock.react").__setRef("PolarisCountryBlockDeferred.react"));function a(){var a=d("react-compiler-runtime").c(1),b;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=i.jsx(c("CometPlaceholder.react"),{fallback:i.jsx(c("PolarisProfileTabContentSpinner.react"),{}),children:i.jsx(j,{})}),a[0]=b):b=a[0];return b}g["default"]=a}),98);
__d("PolarisHttpGatedContentPageLazy.react",["CometPlaceholder.react","IGDSBox.react","IGDSSpinner.react","deferredLoadComponent","react","react-compiler-runtime","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("deferredLoadComponent")(c("requireDeferred")("PolarisHttpGatedContentPage.react").__setRef("PolarisHttpGatedContentPageLazy.react"));function a(a){var b=d("react-compiler-runtime").c(5),e;b[0]!==a?(e=babelHelpers["extends"]({},a),b[0]=a,b[1]=e):e=b[1];b[2]===Symbol["for"]("react.memo_cache_sentinel")?(a=i.jsx(c("IGDSBox.react"),{alignItems:"center",marginTop:10,children:i.jsx(c("IGDSSpinner.react"),{})}),b[2]=a):a=b[2];b[3]!==e?(a=i.jsx(c("CometPlaceholder.react"),{fallback:a,children:i.jsx(j,babelHelpers["extends"]({},e))}),b[3]=e,b[4]=a):a=b[4];return a}g["default"]=a}),98);
__d("PolarisNewsCountryBlockDeferred.react",["CometPlaceholder.react","PolarisProfileTabContentSpinner.react","deferredLoadComponent","react","react-compiler-runtime","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("deferredLoadComponent")(c("requireDeferred")("PolarisNewsCountryBlock.react").__setRef("PolarisNewsCountryBlockDeferred.react"));function a(a){var b=d("react-compiler-runtime").c(4),e=a.isOwnProfile;a=a.userID;var f;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(f=i.jsx(c("PolarisProfileTabContentSpinner.react"),{}),b[0]=f):f=b[0];b[1]!==e||b[2]!==a?(f=i.jsx(c("CometPlaceholder.react"),{fallback:f,children:i.jsx(j,{isOwnProfile:e,userID:a})}),b[1]=e,b[2]=a,b[3]=f):f=b[3];return f}g["default"]=a}),98);
__d("PolarisProfilePagePrivateProfileDeferred.react",["CometPlaceholder.react","PolarisProfileTabContentSpinner.react","deferredLoadComponent","react","react-compiler-runtime","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("deferredLoadComponent")(c("requireDeferred")("PolarisProfilePagePrivateProfile.react").__setRef("PolarisProfilePagePrivateProfileDeferred.react"));function a(a){var b=d("react-compiler-runtime").c(4),e=a.suggestedUsersQuery;a=a.user;var f;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(f=i.jsx(c("PolarisProfileTabContentSpinner.react"),{}),b[0]=f):f=b[0];b[1]!==e||b[2]!==a?(f=i.jsx(c("CometPlaceholder.react"),{fallback:f,children:i.jsx(j,{suggestedUsersQuery:e,user:a})}),b[1]=e,b[2]=a,b[3]=f):f=b[3];return f}g["default"]=a}),98);
__d("PolarisProfilePostsGridInstantModal_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisProfilePostsGridInstantModal_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"code",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"original_height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"original_width",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaOverlayPayloadSchema",kind:"LinkedField",name:"media_overlay_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTButtonSpec",kind:"LinkedField",name:"buttons",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"button_type",storageKey:null}],storageKey:null}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisMediaOverlayCommunityNote_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisProfilePostsGridInstantModal.react",["CometRelay","PolarisInstantPostModal.react","PolarisIsLoggedInDesktop","PolarisMediaOverlayCommunityNote","PolarisMediaOverlayInfoTypes","PolarisPost.react","PolarisPostVariants","PolarisProfilePostsGridInstantModal_media.graphql","PolarisRoutes","isStringNullOrEmpty","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e,f=d("react-compiler-runtime").c(26),g=a.analyticsContext,i=a.initialCarouselIndex,l=a.media,m=a.mediaLinkBuilder,n=a.modalEntryPath,o=a.onClose,p=a.onOpen;a=a.postIDs;l=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisProfilePostsGridInstantModal_media.graphql"),l);if(!a.includes(l.pk))return null;var q;f[0]!==l.original_height||f[1]!==l.original_width?(q=l.original_height!=null&&l.original_width!=null?{height:l.original_height,width:l.original_width}:void 0,f[0]=l.original_height,f[1]=l.original_width,f[2]=q):q=f[2];q=q;if(f[3]!==((e=l.media_overlay_info)==null?void 0:e.buttons)){var r;e=l==null?void 0:(e=l.media_overlay_info)==null?void 0:(e=e.buttons)==null?void 0:e.find(k);f[3]=(r=l.media_overlay_info)==null?void 0:r.buttons;f[4]=e}else e=f[4];r=e;f[5]!==r||f[6]!==l?(e=r?j.jsx(c("PolarisMediaOverlayCommunityNote"),{queryReference:l}):null,f[5]=r,f[6]=l,f[7]=e):e=f[7];r=e;e=c("isStringNullOrEmpty")(n)?d("PolarisRoutes").FEED_PATH:n;n=l.code;var s=l.pk,t=g+"Modal",u;f[8]===Symbol["for"]("react.memo_cache_sentinel")?(u=d("PolarisIsLoggedInDesktop").isLoggedInDesktop(),f[8]=u):u=f[8];f[9]!==r||f[10]!==i||f[11]!==l.code||f[12]!==l.pk||f[13]!==t?(u=j.jsx(c("PolarisPost.react"),{analyticsContext:t,autoplay:u,communityNoteComponent:r,id:l.pk,initialCarouselIndex:i,isVisible:!0,shortcode:l.code,testid:void 0,variant:d("PolarisPostVariants").VARIANTS.flexible,visiblePosition:0}),f[9]=r,f[10]=i,f[11]=l.code,f[12]=l.pk,f[13]=t,f[14]=u):u=f[14];f[15]!==g||f[16]!==q||f[17]!==l.code||f[18]!==l.pk||f[19]!==m||f[20]!==o||f[21]!==p||f[22]!==a||f[23]!==e||f[24]!==u?(r=j.jsx(c("PolarisInstantPostModal.react"),{analyticsContext:g,combinedPostIds:a,dimensions:q,mediaLinkBuilder:m,modalEntryPath:e,onClose:o,onOpen:p,postCode:n,postId:s,children:u}),f[15]=g,f[16]=q,f[17]=l.code,f[18]=l.pk,f[19]=m,f[20]=o,f[21]=p,f[22]=a,f[23]=e,f[24]=u,f[25]=r):r=f[25];return r}function k(a){return a.button_type===d("PolarisMediaOverlayInfoTypes").MEDIA_OVERLAY_BUTTON_TYPES.COMMUNITY_NOTE}g["default"]=a}),98);
__d("PolarisProfileSuggestedUsers_response.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisProfileSuggestedUsers_response",selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"users",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTRelationshipInfoDict",kind:"LinkedField",name:"friendship_status",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"following",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_verified",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisFollowChainingList_suggested_users"},{args:null,kind:"FragmentSpread",name:"PolarisProfileUserListDialog_users"}],storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisDismissChainingUser_response"}],type:"XDTChainingResponse",abstractKey:null};e.exports=a}),null);
__d("usePolarisDismissChainingUserMutation_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="24021983430742179"}),null);
__d("usePolarisDismissChainingUserMutation.graphql",["usePolarisDismissChainingUserMutation_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={defaultValue:null,kind:"LocalArgument",name:"chaining_user_id"},c={defaultValue:null,kind:"LocalArgument",name:"target_id"},d=[{alias:null,args:[{kind:"Variable",name:"chaining_user_id",variableName:"chaining_user_id"},{kind:"Variable",name:"target_id",variableName:"target_id"}],concreteType:"XDTChainingDismissResponse",kind:"LinkedField",name:"xdt_api__v1__discover__chaining_dismiss",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"chaining_user_id",storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:[a,c],kind:"Fragment",metadata:null,name:"usePolarisDismissChainingUserMutation",selections:d,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[c,a],kind:"Operation",name:"usePolarisDismissChainingUserMutation",selections:d},params:{id:b("usePolarisDismissChainingUserMutation_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__discover__chaining_dismiss"]},name:"usePolarisDismissChainingUserMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("usePolarisDismissChainingUser_response.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisDismissChainingUser_response",selections:[{kind:"ClientExtension",selections:[{alias:null,args:null,kind:"ScalarField",name:"__id",storageKey:null}]}],type:"XDTChainingResponse",abstractKey:null};e.exports=a}),null);
__d("usePolarisDismissChainingUser",["CometRelay","FBLogger","Promise","polarisGetXDTUserDict","react","usePolarisDismissChainingUserMutation.graphql","usePolarisDismissChainingUser_response.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=(k||d("react")).useCallback;function a(a){var e=d("CometRelay").useMutation(h!==void 0?h:h=b("usePolarisDismissChainingUserMutation.graphql")),f=e[0],g=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisDismissChainingUser_response.graphql"),a);return l(function(a,d){var e=function(a){var b,e=c("polarisGetXDTUserDict")(a,d);if(e==null){c("FBLogger")("ig_web").warn("Cannot find user to chaining list");return}b=(b=(b=a.get(g.__id))==null?void 0:(b=b.getLinkedRecords("users"))==null?void 0:b.filter(function(a){return a!==e}))!=null?b:[];(a=a.get(g.__id))==null?void 0:a.setLinkedRecords(b,"users")};e={optimisticUpdater:e,updater:e,variables:{chaining_user_id:d,target_id:a}};f(e);return(j||(j=b("Promise"))).resolve()},[g.__id,f])}g["default"]=a}),98);
__d("PolarisProfileSuggestedUsers.next.react",["fbt","CometRelay","JSResourceForInteraction","PolarisConnectionsLogger","PolarisFollowChainingList.next.react","PolarisProfileSuggestedUsers_response.graphql","XPolarisProfileControllerRouteBuilder","promiseDone","react","react-compiler-runtime","useIGDSLazyDialog","usePolarisDismissChainingUser","usePolarisFollowUser","usePolarisIsSmallScreen"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||d("react"),l=c("JSResourceForInteraction")("PolarisProfileUserListDialog.react").__setRef("PolarisProfileSuggestedUsers.next.react"),m=h._(/*BTDS*/"Suggested for you");function a(a){var e=d("react-compiler-runtime").c(24),f=a.chainingResponse,g=a.clickPoint,h=a.userID;a=a.username;var j=c("usePolarisIsSmallScreen")();f=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisProfileSuggestedUsers_response.graphql"),f);var o=c("usePolarisFollowUser")(),p=f.users,q;e[0]!==p?(q=p.map(n),e[0]=p,e[1]=q):q=e[1];q=q;var r=c("usePolarisDismissChainingUser")(f);e[2]!==r||e[3]!==h?(f=function(a){c("promiseDone")(r(h,a))},e[2]=r,e[3]=h,e[4]=f):f=e[4];f=f;var s;e[5]!==o?(s=function(a){c("promiseDone")(o(a,!0))},e[5]=o,e[6]=s):s=e[6];s=s;var t;e[7]!==o?(t=function(a){c("promiseDone")(o(a,!1))},e[7]=o,e[8]=t):t=e[8];t=t;var u;e[9]!==a?(u=c("XPolarisProfileControllerRouteBuilder").buildURL({username:a}),e[9]=a,e[10]=u):u=e[10];a=u;u=c("useIGDSLazyDialog")(l);var v=u[0];e[11]!==v||e[12]!==p?(u=function(){v({hideFollowButton:!1,hideName:!1,hideSocialContext:!0,title:m,users:p})},e[11]=v,e[12]=p,e[13]=u):u=e[13];u=u;var w;e[14]!==q||e[15]!==g||e[16]!==j||e[17]!==s||e[18]!==u||e[19]!==f||e[20]!==t||e[21]!==a||e[22]!==p?(w=k.jsx(c("PolarisFollowChainingList.next.react"),{analyticsContext:d("PolarisConnectionsLogger").CONNECTIONS_CONTAINER_MODULES.profile,chainingSuggestions:q,clickPoint:g,impressionModule:d("PolarisConnectionsLogger").VIEW_MODULES.web_profile_chaining,isSmallScreen:j,onFollowUser:s,onSeeAllClick:u,onSuggestionDismissed:f,onUnfollowUser:t,seeAllHref:a,showDescription:!0,title:m,users:p}),e[14]=q,e[15]=g,e[16]=j,e[17]=s,e[18]=u,e[19]=f,e[20]=t,e[21]=a,e[22]=p,e[23]=w):w=e[23];return w}function n(a){var b;return{fullName:a.full_name,id:a.pk,isFollowedByViewer:((b=a.friendship_status)==null?void 0:b.following)===!0,isVerified:a.is_verified,profilePictureUrl:a.profile_pic_url,username:(b=a.username)!=null?b:""}}g["default"]=a}),226);
__d("PolarisProfileSuggestedUsersPlaceHolder.react",["IGDSSpinner.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(){var a=d("react-compiler-runtime").c(1),b;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=i.jsx("div",babelHelpers["extends"]({className:"x6s0dn4 x78zum5 xpnc28q xl56j7k"},{children:i.jsx(c("IGDSSpinner.react"),{})})),a[0]=b):b=a[0];return b}g["default"]=a}),98);
__d("PolarisProfileSuggestedUsersWithPreloadableQuery.graphql",["PolarisProfileSuggestedUsersWithPreloadableQuery_instagramRelayOperation","relay-runtime"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={defaultValue:null,kind:"LocalArgument",name:"module"},c={defaultValue:null,kind:"LocalArgument",name:"target_id"},d=[{kind:"Variable",name:"module",variableName:"module"},{kind:"Variable",name:"target_id",variableName:"target_id"}];return{fragment:{argumentDefinitions:[a,c],kind:"Fragment",metadata:null,name:"PolarisProfileSuggestedUsersWithPreloadableQuery",selections:[{alias:null,args:d,concreteType:"XDTChainingResponse",kind:"LinkedField",name:"xdt_api__v1__discover__chaining",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisProfileSuggestedUsers_response"}],storageKey:null}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[c,a],kind:"Operation",name:"PolarisProfileSuggestedUsersWithPreloadableQuery",selections:[{alias:null,args:d,concreteType:"XDTChainingResponse",kind:"LinkedField",name:"xdt_api__v1__discover__chaining",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"users",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTRelationshipInfoDict",kind:"LinkedField",name:"friendship_status",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"following",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"blocking",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_feed_favorite",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"outgoing_request",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"followed_by",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"incoming_request",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_restricted",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_bestie",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_verified",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_private",storageKey:null},{alias:null,args:null,concreteType:"XDTSupervisionInfo",kind:"LinkedField",name:"supervision_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"is_guardian_of_viewer",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_supervised_by_viewer",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"social_context",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"live_broadcast_visibility",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"live_broadcast_id",storageKey:null},{alias:null,args:null,concreteType:"XDTProfilePicUrlInfo",kind:"LinkedField",name:"hd_profile_pic_url_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_unpublished",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null}],storageKey:null},{kind:"ClientExtension",selections:[{alias:null,args:null,kind:"ScalarField",name:"__id",storageKey:null}]}],storageKey:null}]},params:{id:b("PolarisProfileSuggestedUsersWithPreloadableQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__discover__chaining"]},name:"PolarisProfileSuggestedUsersWithPreloadableQuery",operationKind:"query",text:null}}}();b("relay-runtime").PreloadableQueryRegistry.set(a.params.id,a);e.exports=a}),null);
__d("PolarisProfileSuggestedUsersWithPreloadable.react",["CometRelay","PolarisProfileSuggestedUsers.next.react","PolarisProfileSuggestedUsersWithPreloadableQuery.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(5),f=a.clickPoint,g=a.query,i=a.userID;a=a.username;g=d("CometRelay").usePreloadedQuery(h!==void 0?h:h=b("PolarisProfileSuggestedUsersWithPreloadableQuery.graphql"),g);var k;e[0]!==f||e[1]!==g.xdt_api__v1__discover__chaining||e[2]!==i||e[3]!==a?(k=j.jsx(c("PolarisProfileSuggestedUsers.next.react"),{chainingResponse:g.xdt_api__v1__discover__chaining,clickPoint:f,userID:i,username:a}),e[0]=f,e[1]=g.xdt_api__v1__discover__chaining,e[2]=i,e[3]=a,e[4]=k):k=e[4];return k}g["default"]=a}),98);
__d("PolarisProfileSuggestedUsersWithPreloadableDeferred.react",["CometPlaceholder.react","CometRelay","PolarisProfileSuggestedUsersErrorBoundary.react","PolarisProfileSuggestedUsersPlaceHolder.react","PolarisProfileSuggestedUsersWithPreloadableQuery.graphql","deferredLoadComponent","react","react-compiler-runtime","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={error:{display:"x78zum5",height:"xpnc28q",$$css:!0}},k=c("deferredLoadComponent")(c("requireDeferred")("PolarisProfileSuggestedUsersWithPreloadable.react").__setRef("PolarisProfileSuggestedUsersWithPreloadableDeferred.react"));function a(a){var b=d("react-compiler-runtime").c(14),e=a.clickPoint,f=a.query,g=a.userID;a=a.username;f=d("CometRelay").useQueryLoader(c("PolarisProfileSuggestedUsersWithPreloadableQuery.graphql"),f);var h=f[0],l=f[1];b[0]!==l||b[1]!==g?(f=function(){l({module:"profile",target_id:g})},b[0]=l,b[1]=g,b[2]=f):f=b[2];f=f;var m;b[3]===Symbol["for"]("react.memo_cache_sentinel")?(m=i.jsx(c("PolarisProfileSuggestedUsersPlaceHolder.react"),{}),b[3]=m):m=b[3];var n;b[4]!==e||b[5]!==h||b[6]!==g||b[7]!==a?(n=h&&i.jsx(k,{clickPoint:e,query:h,userID:g,username:a}),b[4]=e,b[5]=h,b[6]=g,b[7]=a,b[8]=n):n=b[8];b[9]!==n?(e=i.jsx(c("CometPlaceholder.react"),{fallback:m,children:n}),b[9]=n,b[10]=e):e=b[10];b[11]!==f||b[12]!==e?(h=i.jsx(c("PolarisProfileSuggestedUsersErrorBoundary.react"),{errorStaticStyles:j.error,onErrorCountChange:f,children:e}),b[11]=f,b[12]=e,b[13]=h):h=b[13];return h}g["default"]=a}),98);
__d("PolarisQPManagerLazy.react",["CometErrorBoundary.react","CometPlaceholder.react","deferredLoadComponent","emptyFunction","react","react-compiler-runtime","requireDeferred"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("deferredLoadComponent")(c("requireDeferred")("PolarisQPManager.react").__setRef("PolarisQPManagerLazy.react"));function a(a){var b=d("react-compiler-runtime").c(2);a=a.slot;var e;b[0]!==a?(e=i.jsx(c("CometErrorBoundary.react"),{fallback:c("emptyFunction").thatReturnsNull,children:i.jsx(c("CometPlaceholder.react"),{fallback:null,children:i.jsx(j,{slot:a})})}),b[0]=a,b[1]=e):e=b[1];return e}g["default"]=a}),98);