;/*FB_PKG_DELIM*/

__d("BaseBlurryImagePreview.react",["getComputedStyle","react","useSetAttributeRef"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));b=i;var k=b.useId,l=b.useLayoutEffect,m=b.useRef,n=b.useState;function a(a){var b=a.blur,d=a.previewData,e=a.alt,f=a.height,g=a.style,i=a.width;a=babelHelpers.objectWithoutPropertiesLoose(a,["blur","previewData","alt","height","style","width"]);var o=g!=null?g:{},p=o.height,q=o.width;o=b!=null?b:1.2;b=k();var r=c("useSetAttributeRef")("id",b),s="url(#"+b+")",t=c("useSetAttributeRef")("filter",s),u=k(),v=m(null),w=n(f!==void 0||p!==void 0||i!==void 0||q!==void 0),x=w[0],y=w[1];l(function(){var a=v.current;if(!(a instanceof SVGSVGElement))return;if(f!==void 0||p!==void 0||i!==void 0||q!==void 0){y(!0);return}var b=a.style.display,e=(h||(h=c("getComputedStyle")))(a);a.style.display="none";y(e!=null&&(e.height!==d.height+"px"||e.width!==d.width+"px"));a.style.display=b},[f,d.height,d.width,p,q,i]);return j.jsxs("svg",babelHelpers["extends"]({},a,{"aria-labelledby":e!=null?u:null,height:f!=null?f:x?void 0:d.height,preserveAspectRatio:"none",ref:v,role:"img",style:g,viewBox:"0 0 "+d.width+" "+d.height,width:i!=null?i:x?void 0:d.width,xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",children:[e!=null&&j.jsx("title",{id:u,children:e}),j.jsxs("filter",{colorInterpolationFilters:"sRGB",filterUnits:"userSpaceOnUse",id:b,ref:r,suppressHydrationWarning:!0,children:[j.jsx("feGaussianBlur",{edgeMode:"duplicate",stdDeviation:o}),j.jsx("feComponentTransfer",{children:j.jsx("feFuncA",{tableValues:"1 1",type:"discrete"})})]}),j.jsx("image",{filter:s,height:d.height,ref:t,suppressHydrationWarning:!0,width:d.width,x:"0",xlinkHref:d.dataURI,y:"0"})]}))}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("IGDSCircleChevronLeftPanoFilled24Icon.react",["IGDSSVGIconBase.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(3),e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e=i.jsx("path",{d:"M12.005.503a11.5 11.5 0 1 0 11.5 11.5 11.513 11.513 0 0 0-11.5-11.5Zm2.207 15.294a1 1 0 1 1-1.416 1.412l-4.5-4.51a1 1 0 0 1 .002-1.415l4.5-4.489a1 1 0 0 1 1.412 1.416l-3.792 3.783Z"}),b[0]=e):e=b[0];b[1]!==a?(e=i.jsx(c("IGDSSVGIconBase.react"),babelHelpers["extends"]({},a,{viewBox:"0 0 24 24",children:e})),b[1]=a,b[2]=e):e=b[2];return e}b=i.memo(a);g["default"]=b}),98);
__d("IGDSCircleChevronRightPanoFilled24Icon.react",["IGDSSVGIconBase.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(3),e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e=i.jsx("path",{d:"M12.005.503a11.5 11.5 0 1 0 11.5 11.5 11.513 11.513 0 0 0-11.5-11.5Zm3.707 12.22-4.5 4.488A1 1 0 0 1 9.8 15.795l3.792-3.783L9.798 8.21a1 1 0 1 1 1.416-1.412l4.5 4.511a1 1 0 0 1-.002 1.414Z"}),b[0]=e):e=b[0];b[1]!==a?(e=i.jsx(c("IGDSSVGIconBase.react"),babelHelpers["extends"]({},a,{viewBox:"0 0 24 24",children:e})),b[1]=a,b[2]=e):e=b[2];return e}b=i.memo(a);g["default"]=b}),98);
__d("PolarisStoriesV3AdFooterCTA_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null};return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdFooterCTA_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"link_text",storageKey:null},a,{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:a,action:"THROW"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CTAUrl_media"}],type:"XDTMediaDict",abstractKey:null}}();e.exports=a}),null);
__d("useTimeout",["clearTimeout","react","setTimeout","useEffectOnce","useUnsafeRef_DEPRECATED"],(function(a,b,c,d,e,f,g){"use strict";var h,i;b=i||d("react");var j=b.useCallback,k=b.useState;function a(a,b){b===void 0&&(b=!0);var d=(h||(h=c("useUnsafeRef_DEPRECATED")))(null),e=k(b),f=e[0],g=e[1],i=j(function(){g(!1),c("clearTimeout")(d.current)},[]),l=j(function(){g(!0),d.current=c("setTimeout")(function(){g(!1)},a)},[a]);e=j(function(){c("clearTimeout")(d.current),l()},[l]);c("useEffectOnce")(function(){b&&l();return i});return{pending:f,clear:i,restart:e}}g["default"]=a}),98);
__d("usePolarisStoriesV3AdCTADelayActive",["useTimeout"],(function(a,b,c,d,e,f,g){"use strict";var h=3e3;function a(){var a=c("useTimeout")(h);a=a.pending;return!a}g["default"]=a}),98);
__d("usePolarisStoriesV3CTAUrl_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3CTAUrl_media",selections:[{alias:null,args:null,concreteType:"XDTStoryCTADict",kind:"LinkedField",name:"story_cta",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTAdLink",kind:"LinkedField",name:"links",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"webUri",storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"link",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3CTAUrl",["CometRelay","URI","isStringNullOrEmpty","react-compiler-runtime","usePolarisOffsiteTrackingDataURLParams","usePolarisStoriesV3CTAUrl_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a){var e,f=d("react-compiler-runtime").c(6);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3CTAUrl_media.graphql"),a);e=(e=a.story_cta)==null?void 0:(e=e[0])==null?void 0:(e=e.links)==null?void 0:(e=e[0])==null?void 0:e.webUri;var g;f[0]!==e||f[1]!==a.link?(g=c("isStringNullOrEmpty")(e)?a.link:e,f[0]=e,f[1]=a.link,f[2]=g):g=f[2];e=g;a=c("usePolarisOffsiteTrackingDataURLParams")(e);if(e==null)return e;if(f[3]!==a||f[4]!==e){g=new(i||(i=c("URI")))(e);g.setQueryData(babelHelpers["extends"]({},g.getQueryData(),{},a));g=g.toString();f[3]=a;f[4]=e;f[5]=g}else g=f[5];return g}g["default"]=a}),98);
__d("PolarisStoriesV3AdFooterCTA.react",["CometRelay","IGDSChevronRightPanoFilledIcon.react","IGDSTextVariants.react","InstagramODS","PolarisClickEventLoggerProvider.react","PolarisExternalLink.react","PolarisGenericStrings","PolarisStoriesV3AdFooterCTA_media.graphql","PolarisStoriesV3ContainerModule","PolarisTrackingNodeProvider.react","react","react-compiler-runtime","usePolarisIsSmallScreen","usePolarisStoriesV3AdCTADelayActive","usePolarisStoriesV3CTAUrl","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(16),f=a.currSidecarIndex;a=a.media;var g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdFooterCTA_media.graphql"),a),i=c("usePolarisStoriesV3AdCTADelayActive")();a=c("usePolarisStoriesV3ViewerLoggingContext")();var l=g.link_text,m=g.user,n=c("usePolarisStoriesV3CTAUrl")(g),o=c("usePolarisIsSmallScreen")();if(n==null||l==null)return null;var p=k,q;e[0]!==n||e[1]!==i||e[2]!==o||e[3]!==l||e[4]!==g.pk||e[5]!==m.pk?(q=function(a){return j.jsxs("div",babelHelpers["extends"]({className:"x6s0dn4 x78zum5 xdt5ytf xl56j7k"},{ref:a,children:[o&&j.jsx("div",babelHelpers["extends"]({className:"x10l6tqk x12c0h9u x9tu13d"},{children:j.jsx(c("IGDSChevronRightPanoFilledIcon.react"),{alt:d("PolarisGenericStrings").UP_CHEVRON_ALT,size:12})})),j.jsx("div",babelHelpers["extends"]({},{0:{className:"xc26acl x1qjc9v5 x1ybmbna x1gx9kpi xk39tu8 xoeu3cm x10w6328 x1ey2m1c x9f619 x78zum5 x1q0g3np xt7dq6l xl56j7k x6ikm8r x10wlt62 x1n2onr6 x1hl2dhg"},1:{className:"xc26acl x1qjc9v5 x1gx9kpi xk39tu8 xoeu3cm x10w6328 x1ey2m1c x9f619 x78zum5 x1q0g3np xt7dq6l xl56j7k x6ikm8r x10wlt62 x1n2onr6 x1hl2dhg x12peec7 xerrx05"}}[!!i<<0],{children:j.jsx(c("PolarisExternalLink.react"),{author_id:m.pk,className:"x92akz8 xh8yej3","data-testid":void 0,href:n,media_pk:g.pk,onClick:p,page_id:"StoriesPage",target:"_blank",children:j.jsx("div",babelHelpers["extends"]({className:"x1qjc9v5 x1v8p93f x1o3jo1z x16stqrj xv5lvn5 x1ejq31n x18oe1m7 x1sy0etr xstzfhl x972fbf x10w94by x1qhh985 x14e42zd x9f619 x78zum5 xdt5ytf x2lah0s xln7xf2 xk390pu x1s688f xdj266r x14z9mp xat24cr x1lziwak xqy66fx x1ekh9h2 xr1496l xsdj86q x67bb7w x1n2onr6 x2b8uid x11njtxf"},{children:j.jsx(d("IGDSTextVariants.react").IGDSTextBodyEmphasized,{color:i?"webAlwaysBlack":"webAlwaysWhite",children:l})}))})}))]}))},e[0]=n,e[1]=i,e[2]=o,e[3]=l,e[4]=g.pk,e[5]=m.pk,e[6]=q):q=e[6];var r=q;if(a.module===c("PolarisStoriesV3ContainerModule").StoryAdActivity){e[7]!==r?(q=r(),e[7]=r,e[8]=q):q=e[8];return q}e[9]!==f?(a=f.toString(),e[9]=f,e[10]=a):a=e[10];e[11]!==r?(q=function(a){return r(a)},e[11]=r,e[12]=q):q=e[12];e[13]!==a||e[14]!==q?(f=j.jsx(c("PolarisClickEventLoggerProvider.react"),{children:j.jsx(c("PolarisTrackingNodeProvider.react"),{index:a,trackingNode:86,children:q})}),e[13]=a,e[14]=q,e[15]=f):f=e[15];return f}function k(){c("InstagramODS").incr("web.ads.story.link_click")}g["default"]=a}),98);
__d("PolarisStoriesV3AdFooter_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdFooter_ad",selections:[{alias:null,args:null,kind:"ScalarField",name:"ad_id",storageKey:null}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdFooter_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdFooter_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"link_text",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ShareButton_user"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdFooterCTA_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ShareButton_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CanReshare_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CTAUrl_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdSticker_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdFooter_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdFooter_viewer",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CanReshare_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdSticker_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null};return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdSticker_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"link_text",storageKey:null},a,{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:a,action:"THROW"}],storageKey:null},action:"THROW"},{alias:null,args:null,concreteType:"XDTIGAdsCTAStickerInfoDict",kind:"LinkedField",name:"story_ad_cta_sticker",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTCTAStickerCustomPositionInfo",kind:"LinkedField",name:"customPositionInfo",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null}],storageKey:null}],storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CTAUrl_media"}],type:"XDTMediaDict",abstractKey:null}}();e.exports=a}),null);
__d("getPolarisStoriesV3SmallScreenViewerSize",["PolarisDesktopStoriesGalleryConstants"],(function(a,b,c,d,e,f,g){"use strict";var h=68;function a(a,b){return b>a/d("PolarisDesktopStoriesGalleryConstants").STORY_VIEWER_ASPECT_RATIO_W_H+h?{height:a/d("PolarisDesktopStoriesGalleryConstants").STORY_VIEWER_ASPECT_RATIO_W_H+h,width:a}:{height:b,width:Math.min(a,(b-h)*d("PolarisDesktopStoriesGalleryConstants").STORY_VIEWER_ASPECT_RATIO_W_H)}}g["default"]=a}),98);
__d("usePolarisStoriesV3GetSmallScreenViewerSize",["PolarisUA","getPolarisStoriesV3SmallScreenViewerSize","react","react-compiler-runtime","useWindowSize"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useCallback;var i=b.useState;function a(){var a=d("react-compiler-runtime").c(3),b=c("useWindowSize")(),e=b.innerHeight;b=b.innerWidth;var f=i(b),g=f[0];f=f[1];var h=i(e),j=h[0];h=h[1];d("PolarisUA").isMobile()?b!==g&&(f(b),h(e)):(b!==g||e!==j)&&(f(b),h(e));a[0]!==j||a[1]!==g?(f=function(){return c("getPolarisStoriesV3SmallScreenViewerSize")(g,j)},a[0]=j,a[1]=g,a[2]=f):f=a[2];return f}g["default"]=a}),98);
__d("usePolarisStoriesV3GalleryConfig",["PolarisDesktopStoriesGalleryConstants","react","usePolarisIsSmallScreen","usePolarisStoriesV3GetSmallScreenViewerSize","useWindowSize"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useMemo;function a(){var a=c("useWindowSize")(),b=a.innerHeight,e=a.innerWidth,f=c("usePolarisIsSmallScreen")(),g=c("usePolarisStoriesV3GetSmallScreenViewerSize")();return i(function(){if(f){var a=g(),c=a.height;a=a.width;return{fetchDistance:1,height:c,type:"MOBILE",width:a}}a=(c=d("PolarisDesktopStoriesGalleryConstants").ASPECT_RATIOS.find(function(a){return e/b<=a.width/a.height}))!=null?c:d("PolarisDesktopStoriesGalleryConstants").ASPECT_RATIOS[d("PolarisDesktopStoriesGalleryConstants").ASPECT_RATIOS.length-1];c={height:Math.round(Math.min(b,a.height/a.width*e)),width:Math.round(Math.min(e,a.width/a.height*b))};var h=c.height*d("PolarisDesktopStoriesGalleryConstants").STORY_VIEWER_LARGE_HEIGHT_PCT;h={height:Math.round(h),width:Math.round(h*d("PolarisDesktopStoriesGalleryConstants").STORY_VIEWER_ASPECT_RATIO_W_H)};a=a.previewCount;var i=d("PolarisDesktopStoriesGalleryConstants").STORY_GALLERY_ITEM_SCALES.preview,j={height:Math.round(h.height*i),width:Math.round(h.width*i)};return{fetchDistance:Math.ceil(a)*2,gallery:c,player:h,preview:j,previewCount:a,previewScale:i,type:"DESKTOP"}},[g,b,e,f])}g["default"]=a}),98);
__d("PolarisStoriesV3AdSticker.react",["CometRelay","IGDSLinkPanoOutlineIcon.react","InstagramODS","PolarisAdsGatingHelpers","PolarisClickEventLoggerProvider.react","PolarisExternalLink.react","PolarisPostImpressionTrackingNode.react","PolarisSponsoredTextWidthUtils","PolarisStoriesV3AdSticker_media.graphql","PolarisStoriesV3ContainerModule","PolarisTrackingNodeProvider.react","react","react-compiler-runtime","stylex","usePolarisStoriesV3CTAUrl","usePolarisStoriesV3GalleryConfig","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react")),l=j.useRef,m="Instagram Sans Condensed Regular",n=.6,o=32,p=13,q=8,r=5,s=27,t=16,u=400,v="ctaText",w={icon:function(a){return[{color:"xo91s5a",height:a+"px"==null?null:"x1f5funs",width:a+"px"==null?null:"x1bl4301",$$css:!0},{"--height":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(a+"px"),"--width":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(a+"px")}]},sticker:function(a,b,c,d,e){return[{alignContent:"xc26acl",alignItems:"x6s0dn4",backgroundColor:"x12peec7",borderStartStartRadius:d+"px"==null?null:"xgrrwr1",borderStartEndRadius:d+"px"==null?null:"x7o7tq0",borderEndEndRadius:d+"px"==null?null:"x1rs6hn2",borderEndStartRadius:d+"px"==null?null:"xvb4t3y",display:"x78zum5",maxWidth:e==null?null:"x10cxd3o",paddingBottom:c+"px"==null?null:"x18061mc",paddingLeft:b+"px"==null?null:"xklrb59",paddingStart:null,paddingEnd:null,paddingRight:b+"px"==null?null:"x1hb41fa",paddingInlineStart:null,paddingInlineEnd:null,paddingTop:c+"px"==null?null:"xmyip4h",position:"x1n2onr6",top:a*100+"%"==null?null:"x1g9fk1a",$$css:!0},{"--borderRadius":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(d+"px"),"--maxWidth":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(e),"--paddingBottom":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(c+"px"),"--paddingLeft":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(b+"px"),"--paddingRight":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(b+"px"),"--paddingTop":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(c+"px"),"--top":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(a*100+"%")}]},stickerText:function(a,b){return[{color:"xo91s5a",fontFamily:"xyq6ywh",fontSize:a+"px"==null?null:"x6zurak",lineHeight:"xtqhoxe",paddingInlineStart:b+"px"==null?null:"x1yzmnzt",textDecoration:"x92akz8",whiteSpace:"xuxw1ft",$$css:!0},{"--fontSize":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(a+"px"),"--paddingInlineStart":function(a){return typeof a==="number"?a+"px":a!=null?a:void 0}(b+"px")}]}},x=.95;function a(a){var e,f=d("react-compiler-runtime").c(16),g=a.currSidecarIndex;a=a.media;var j=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdSticker_media.graphql"),a);a=c("usePolarisStoriesV3GalleryConfig")();var m=null;a.type==="MOBILE"?m=a.width:a.type==="DESKTOP"&&(m=a.player.width);a=c("usePolarisStoriesV3ViewerLoggingContext")();var A=l(null),B=j.link_text,C=j.user,D=c("usePolarisStoriesV3CTAUrl")(j);if(D==null||B==null)return null;var E=y;if(f[0]!==D||f[1]!==g||f[2]!==B||f[3]!==j.pk||f[4]!==((e=j.story_ad_cta_sticker)==null?void 0:(e=e.customPositionInfo)==null?void 0:e.y)||f[5]!==C.pk||f[6]!==a.module||f[7]!==m){e=m!==null?z(B.toUpperCase(),m*n):{border_radius:t,font_size:o,icon_size:s,max_width:u,padding_horizontal:p,padding_horizontal_internal:r,padding_vertical:q};var F=e.border_radius,G=e.font_size,H=e.icon_size,I=e.max_width,J=e.padding_horizontal,K=e.padding_horizontal_internal,L=e.padding_vertical,M=function(a){return k.jsx("div",{children:k.jsx(c("PolarisExternalLink.react"),{author_id:C.pk,"data-testid":void 0,href:D,media_pk:j.pk,onClick:E,page_id:"StoriesPage",target:"_blank",children:k.jsxs("div",babelHelpers["extends"]({ref:a},(i||(i=c("stylex"))).props(w.sticker((a=j==null?void 0:(a=j.story_ad_cta_sticker)==null?void 0:(a=a.customPositionInfo)==null?void 0:a.y)!=null?a:x,J,L,F,I)),{children:[k.jsx("div",babelHelpers["extends"]({},i.props(w.icon(H)),{children:k.jsx(c("IGDSLinkPanoOutlineIcon.react"),{alt:"",color:{dark:"currentColor",light:"currentColor"},size:H})})),k.jsx("div",babelHelpers["extends"]({},i.props(w.stickerText(G,K)),{children:B.toUpperCase()}))]}))})})};if(a.module===c("PolarisStoriesV3ContainerModule").StoryAdActivity)e=M();else{var N;f[9]!==g?(N=g.toString(),f[9]=g,f[10]=N):N=f[10];var O=function(a){return M(a)},P;f[11]!==N||f[12]!==O?(P=k.jsx("div",{ref:A,children:k.jsx(c("PolarisClickEventLoggerProvider.react"),{children:k.jsx(c("PolarisTrackingNodeProvider.react"),{index:N,trackingNode:86,children:O})})}),f[11]=N,f[12]=O,f[13]=P):P=f[13];e=P}f[0]=D;f[1]=g;f[2]=B;f[3]=j.pk;f[4]=(N=j.story_ad_cta_sticker)==null?void 0:(O=N.customPositionInfo)==null?void 0:O.y;f[5]=C.pk;f[6]=a.module;f[7]=m;f[8]=e}else e=f[8];if(d("PolarisAdsGatingHelpers").enableStoryPostImpressionLogging()){f[14]!==e?(P=k.jsx(c("PolarisPostImpressionTrackingNode.react"),{childrenRef:A,componentType:"text",elementId:v,trackingNode:"CTA",children:e}),f[14]=e,f[15]=P):P=f[15];return P}else return e}function y(){c("InstagramODS").incr("web.ads.story.link_click")}function z(a,b){var c=0,e=o;while(c<=e){var f=Math.floor((c+e)/2),g=Math.floor(f/o*s),h=Math.floor(f/o*p),i=Math.floor(f/o*r);d("PolarisSponsoredTextWidthUtils").getTextWidthForAdPreviewCaption(a,f+"px "+m)+g+i+h*2<=b?c=f+1:e=f-1}g=c-1;return{border_radius:Math.floor(g/o*t),font_size:g,icon_size:Math.floor(g/o*s),max_width:b,padding_horizontal:Math.floor(g/o*p),padding_horizontal_internal:Math.floor(g/o*r),padding_vertical:Math.floor(g/o*q)}}g["default"]=a}),98);
__d("PolarisStoriesV3Footer.react",["react","react-compiler-runtime","usePolarisIsSmallScreen"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(5);a=a.children;var e=c("usePolarisIsSmallScreen")(),f;b[0]!==e?(f={0:{className:"x12svp7l x1ey2m1c x9f619 x78zum5 xdt5ytf x18dl8mb xtijo5x x1o0tod x13a6bvl x1l90r2v xv54qhq xf7dkkf x47corl x10l6tqk"},1:{className:"x1ey2m1c x9f619 x78zum5 xdt5ytf x18dl8mb xtijo5x x1o0tod x13a6bvl x47corl x10l6tqk x18o3ruo xsag5q8 xf159sx xmzvs34"}}[!!e<<0],b[0]=e,b[1]=f):f=b[1];b[2]!==a||b[3]!==f?(e=i.jsx("div",babelHelpers["extends"]({},f,{children:a})),b[2]=a,b[3]=f,b[4]=e):e=b[4];return e}g["default"]=a}),98);
__d("PolarisStoriesV3ShareButton_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ShareButton_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3SharingFriction_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ShareDialogPost_media"},{alias:null,args:null,concreteType:"XDTSharingFrictionInfo",kind:"LinkedField",name:"sharing_friction_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"should_have_sharing_friction",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"bloks_app_url",storageKey:null}],storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ShareButton_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ShareButton_user",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ShareDialogPost_user"}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisDevicePixelRatio",["react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useEffect,j=b.useState;function a(){var a=d("react-compiler-runtime").c(3),b=j(window.devicePixelRatio),c=b[0],e=b[1],f;a[0]!==c?(b=function(){var a=matchMedia("(resolution: "+c+"dppx)");if(!("addEventListener"in a))return;var b=function(){e(window.devicePixelRatio)};a.addEventListener("change",b);return function(){a.removeEventListener("change",b)}},f=[c,e],a[0]=c,a[1]=b,a[2]=f):(b=a[1],f=a[2]);i(b,f);return c}g["default"]=a}),98);
__d("usePolarisStoriesV3ImageSrc_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3ImageSrc_media",selections:[{alias:null,args:null,concreteType:"XDTImageVersion2",kind:"LinkedField",name:"image_versions2",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTImageCandidate",kind:"LinkedField",name:"candidates",plural:!0,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},action:"THROW"}],storageKey:null}],storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3ImageSrc",["CometRelay","last","react","react-compiler-runtime","usePolarisDevicePixelRatio","usePolarisStoriesV3ImageSrc_media.graphql","usePolarisStoriesV3ViewerSizeContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i;(i||d("react")).useMemo;function a(a){var e=d("react-compiler-runtime").c(6);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3ImageSrc_media.graphql"),a);var f=c("usePolarisStoriesV3ViewerSizeContext")(),g=f.width,i=c("usePolarisDevicePixelRatio")();if(e[0]!==((f=a.image_versions2)==null?void 0:f.candidates)){f=(f=(f=a.image_versions2)==null?void 0:f.candidates)!=null?f:[];e[0]=(a=a.image_versions2)==null?void 0:a.candidates;e[1]=f}else f=e[1];a=f;if(e[2]!==i||e[3]!==a||e[4]!==g){f=a.filter(k);f=f.length>0?f:[].concat(a);f==null?void 0:f.sort(j);var l=f==null?void 0:f.find(function(a){return a.width>=g*i});l=(f=(l=l!=null?l:c("last")(f!=null?f:[]))==null?void 0:l.url)!=null?f:"";e[2]=i;e[3]=a;e[4]=g;e[5]=l}else l=e[5];f=l;return f}function j(a,b){return a.width-b.width}function k(a){return a.width!==a.height}g["default"]=a}),98);
__d("usePolarisStoriesV3ShareDialogPost_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3ShareDialogPost_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"audience",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"can_viewer_reshare",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"expiring_at",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"ig_media_sharing_disabled",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"product_type",storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisMediaOverlayInfo_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ImageSrc_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MediaType_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3ShareDialogPost_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3ShareDialogPost_user",selections:[{alias:null,args:null,kind:"ScalarField",name:"is_private",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3ShareDialogPost",["CometRelay","PolarisStoriesV3MediaType","react","react-compiler-runtime","usePolarisMediaOverlayInfo","usePolarisStoriesV3ImageSrc","usePolarisStoriesV3MediaType","usePolarisStoriesV3ShareDialogPost_media.graphql","usePolarisStoriesV3ShareDialogPost_user.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;(j||d("react")).useMemo;function a(a,e){var f,g,j,k,l=d("react-compiler-runtime").c(16);e=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3ShareDialogPost_user.graphql"),e);a=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3ShareDialogPost_media.graphql"),a);var m=c("usePolarisStoriesV3MediaType")(a),n=c("usePolarisMediaOverlayInfo")(a),o=c("usePolarisStoriesV3ImageSrc")(a),p=a.audience,q=Number(a.expiring_at);f=(f=a.ig_media_sharing_disabled)!=null?f:!1;m=m===c("PolarisStoriesV3MediaType").Video;g=(g=e.is_private)!=null?g:void 0;j=(j=e.profile_pic_url)!=null?j:void 0;k=(k=e.username)!=null?k:void 0;var r;l[0]!==g||l[1]!==j||l[2]!==k||l[3]!==e.pk?(r={id:e.pk,isPrivate:g,profilePictureUrl:j,username:k},l[0]=g,l[1]=j,l[2]=k,l[3]=e.pk,l[4]=r):r=l[4];j=(g=a.can_viewer_reshare)!=null?g:void 0;l[5]!==a.audience||l[6]!==a.pk||l[7]!==a.product_type||l[8]!==n||l[9]!==o||l[10]!==q||l[11]!==f||l[12]!==m||l[13]!==r||l[14]!==j?(k={audience:p,expiringAt:q,id:a.pk,igMediaSharingDisabled:f,isVideo:m,mediaOverlayInfo:n,owner:r,productType:a.product_type,src:o,viewerCanReshare:j},l[5]=a.audience,l[6]=a.pk,l[7]=a.product_type,l[8]=n,l[9]=o,l[10]=q,l[11]=f,l[12]=m,l[13]=r,l[14]=j,l[15]=k):k=l[15];e=k;return e}g["default"]=a}),98);
__d("usePolarisStoriesV3SharingFriction_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3SharingFriction_media",selections:[{alias:null,args:null,concreteType:"XDTSharingFrictionInfo",kind:"LinkedField",name:"sharing_friction_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"should_have_sharing_friction",storageKey:null}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3SharingFrictionDialog_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3SharingFriction",["CometRelay","JSResourceForInteraction","react","react-compiler-runtime","useIGDSLazyDialog","usePolarisStoriesV3SharingFriction_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;e=i||d("react");e.useMemo;var j=e.useState,k=c("JSResourceForInteraction")("PolarisStoriesV3SharingFrictionDialog.react").__setRef("usePolarisStoriesV3SharingFriction");function a(a,e){var f=d("react-compiler-runtime").c(8),g=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3SharingFriction_media.graphql"),a);a=j(!1);var i=a[0],l=a[1];a=c("useIGDSLazyDialog")(k);var m=a[0],n=((a=g.sharing_friction_info)==null?void 0:a.should_have_sharing_friction)===!0&&!i;f[0]!==n||f[1]!==g||f[2]!==e||f[3]!==m?(a=function(){n&&m({media:g,onOpenShareSheetClick:function(){l(!0),e()}})},f[0]=n,f[1]=g,f[2]=e,f[3]=m,f[4]=a):a=f[4];f[5]!==n||f[6]!==a?(i={hasSharingFriction:n,showSharingFriction:a},f[5]=n,f[6]=a,f[7]=i):i=f[7];a=i;return a}g["default"]=a}),98);
__d("usePolarisStoryOpenShareV3Dialog",["IGDContactSearchDialogType.flow","PolarisPostTypeUtils","polarisLogAction","react","react-compiler-runtime","useIGDSecureShareSheetInteractionLogger","usePolarisAnalyticsContext","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(a){var b=d("react-compiler-runtime").c(5),e=a.onShow,f=a.post,g=c("usePolarisAnalyticsContext")(),h=d("useIGDSecureShareSheetInteractionLogger").useLogShareSheetInteraction();b[0]!==g||b[1]!==h||b[2]!==e||b[3]!==f?(a=function(){c("polarisLogAction")("shareClick",{source:g,type:d("PolarisPostTypeUtils").getPostType(f)});var a=c("uuidv4")();h("igd_sharesheet_open",{flowInstanceId:a,shareType:d("IGDContactSearchDialogType.flow").IGDContactSearchDialogType.SHARESHEET,source:g});e(a)},b[0]=g,b[1]=h,b[2]=e,b[3]=f,b[4]=a):a=b[4];return a}g["default"]=a}),98);
__d("PolarisStoriesV3ShareButton.react",["CometRelay","IGDSIconButton.react","IGDSMessagesOutlineIcon.react","JSResourceForInteraction","PolarisDirectStrings","PolarisInformTreatmentDialogRootEntrypoint.entrypoint","PolarisInformTreatmentSensitivityType","PolarisShareSheetV3.entrypoint","PolarisStoriesV3ShareButton_media.graphql","PolarisStoriesV3ShareButton_user.graphql","gkx","qex","react","react-compiler-runtime","useIGDSEntryPointDialog","useIGDSLazyDialog","usePolarisStoriesV3ShareDialogPost","usePolarisStoriesV3SharingFriction","usePolarisStoryOpenShareDialog","usePolarisStoryOpenShareV3Dialog"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react"),l=c("JSResourceForInteraction")("PolarisStoriesV3ShareDialog.react").__setRef("PolarisStoriesV3ShareButton.react"),m=c("JSResourceForInteraction")("PolarisInformTreatmentSensitivityDetailsDialog.react").__setRef("PolarisStoriesV3ShareButton.react");function a(a){var e=d("react-compiler-runtime").c(29),f=a.adsLoggingData,g=a.media,j=a.reelId;a=a.user;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ShareButton_user.graphql"),a);g=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ShareButton_media.graphql"),g);a=c("usePolarisStoriesV3ShareDialogPost")(g,a);var n;e[0]!==f||e[1]!==a||e[2]!==j?(n={adsStoryLoggingData:f,dialogResource:l,post:a,reelId:j},e[0]=f,e[1]=a,e[2]=j,e[3]=n):n=e[3];var o=c("usePolarisStoryOpenShareDialog")(n);f=g.id;e[4]!==f?(j=f.split("_"),e[4]=f,e[5]=j):j=e[5];n=j[0];e[6]!==n?(j={media_id:n},e[6]=n,e[7]=j):j=e[7];n=c("useIGDSEntryPointDialog")(c("PolarisInformTreatmentDialogRootEntrypoint.entrypoint"),j);var p=n[0];j=c("useIGDSLazyDialog")(m);var q=j[0];n=c("usePolarisStoriesV3SharingFriction")(g,o);var r=n.hasSharingFriction,s=n.showSharingFriction;e[8]!==f?(j={routeProps:{mediaId:f}},e[8]=f,e[9]=j):j=e[9];n=c("useIGDSEntryPointDialog")(c("PolarisShareSheetV3.entrypoint"),j);var t=n[0];e[10]!==t?(f=function(a){t({flowInstanceId:a})},e[10]=t,e[11]=f):f=e[11];j=f;e[12]!==j||e[13]!==a?(n={onShow:j,post:a},e[12]=j,e[13]=a,e[14]=n):n=e[14];var u=c("usePolarisStoryOpenShareV3Dialog")(n);e[15]!==r||e[16]!==o||e[17]!==u||e[18]!==s?(f=function(){if(r)s();else{var a=c("qex")._("4761")===!0;a?u():o()}},e[15]=r,e[16]=o,e[17]=u,e[18]=s,e[19]=f):f=e[19];j=f;var v=((a=g.sharing_friction_info)==null?void 0:a.should_have_sharing_friction)===!0,w=(n=g.sharing_friction_info)==null?void 0:n.bloks_app_url;e[20]!==w||e[21]!==o||e[22]!==v||e[23]!==p||e[24]!==q?(f=function(){if(v&&w!=null)bb20:switch(w){case"com.instagram.misinformation.sharing_friction.action":p({canShare:!0,shareDialog:o});break bb20;case"com.instagram.sharing_friction.generic_reshare_friction.action":q({canShare:!0,sensitivityType:d("PolarisInformTreatmentSensitivityType").SensitivityType.SENSITIVE,shareDialog:o});break bb20;default:}else o()},e[20]=w,e[21]=o,e[22]=v,e[23]=p,e[24]=q,e[25]=f):f=e[25];a=f;g=c("gkx")("5268")?a:j;e[26]===Symbol["for"]("react.memo_cache_sentinel")?(n=k.jsx(c("IGDSMessagesOutlineIcon.react"),{alt:d("PolarisDirectStrings").DIRECT_BUTTON_ALT_TEXT,color:"ig-text-on-media",size:24}),e[26]=n):n=e[26];e[27]!==g?(f=k.jsx(c("IGDSIconButton.react"),{"data-testid":void 0,onClick:g,children:n}),e[27]=g,e[28]=f):f=e[28];return f}g["default"]=a}),98);
__d("usePolarisStoriesV3AdDirectReshareSendLoggingData",["react","usePolarisStoriesV3GetSharedAdLoggingData","usePolarisStoriesV3GetSharedLoggingData","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useMemo;function a(){var b=c("usePolarisStoriesV3ViewerLoggingContext")(),a=b.module;b=c("usePolarisStoriesV3ReelLoggingContext")();var d=b.authorId,e=c("usePolarisStoriesV3GetSharedAdLoggingData")(),g=c("usePolarisStoriesV3GetSharedLoggingData")();return i(function(){var b=e();b.tray_position;b=babelHelpers.objectWithoutPropertiesLoose(b,["tray_position"]);var c=g();c=c.follow_status;return babelHelpers["extends"]({},b,{a_pk:d,follow_status:c,pigeon_reserved_keyword_module:String(a)})},[d,e,g,a])}g["default"]=a}),98);
__d("usePolarisStoriesV3CanReshare_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3CanReshare_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"can_reshare",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"can_viewer_reshare",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_user"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3IsCloseFriendsStory_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3CanReshare_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3CanReshare_viewer",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3Audience",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum")({Besties:"besties"});c=a;f["default"]=c}),66);
__d("usePolarisStoriesV3IsCloseFriendsStory_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3IsCloseFriendsStory_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"audience",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3IsCloseFriendsStory",["CometRelay","PolarisStoriesV3Audience","usePolarisStoriesV3IsCloseFriendsStory_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3IsCloseFriendsStory_media.graphql"),a);a=c("PolarisStoriesV3Audience").cast(a.audience);a=a===c("PolarisStoriesV3Audience").Besties;return a}g["default"]=a}),98);
__d("usePolarisStoriesV3CanReshare",["CometRelay","usePolarisStoriesV3CanReshare_media.graphql","usePolarisStoriesV3CanReshare_viewer.graphql","usePolarisStoriesV3IsCloseFriendsStory","usePolarisStoriesV3ViewingOwnStory"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,e){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3CanReshare_media.graphql"),a);e=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3CanReshare_viewer.graphql"),e);e=c("usePolarisStoriesV3ViewingOwnStory")(a.user,e);var f=c("usePolarisStoriesV3IsCloseFriendsStory")(a);return!e&&!f&&(a.can_reshare===!0||a.can_viewer_reshare===!0)}g["default"]=a}),98);
__d("PolarisStoriesV3AdFooter.react",["CometRelay","PolarisStoriesV3AdFooterCTA.react","PolarisStoriesV3AdFooter_ad.graphql","PolarisStoriesV3AdFooter_media.graphql","PolarisStoriesV3AdFooter_viewer.graphql","PolarisStoriesV3AdSticker.react","PolarisStoriesV3Footer.react","PolarisStoriesV3ShareButton.react","justknobx","react","react-compiler-runtime","usePolarisIsSmallScreen","usePolarisStoriesV3AdDirectReshareSendLoggingData","usePolarisStoriesV3CTAUrl","usePolarisStoriesV3CanReshare"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||d("react");function a(a){var e=d("react-compiler-runtime").c(24),f=a.ad,g=a.currSidecarIndex,k=a.media,m=a.useAdCTASticker;a=a.viewer;m=m===void 0?!1:m;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdFooter_ad.graphql"),f);k=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3AdFooter_media.graphql"),k);a=d("CometRelay").useFragment(j!==void 0?j:j=b("PolarisStoriesV3AdFooter_viewer.graphql"),a);a=c("usePolarisStoriesV3CanReshare")(k,a);var n=c("usePolarisStoriesV3CTAUrl")(k),o=k.link_text,p=c("usePolarisStoriesV3AdDirectReshareSendLoggingData")(),q=c("usePolarisIsSmallScreen")(),r;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(r={className:"x6s0dn4 x78zum5 x13a6bvl x1gg8mnh x67bb7w x1n2onr6"},e[0]=r):r=e[0];var s;e[1]!==n||e[2]!==g||e[3]!==q||e[4]!==o||e[5]!==k||e[6]!==m?(s=n!=null&&o!=null&&!m&&l.jsx("div",babelHelpers["extends"]({},{0:{className:"x6s0dn4 x78zum5 xl56j7k x1nrll8i x10l6tqk xwa60dl x11lhmoz xh8yej3"},1:{className:"x6s0dn4 x78zum5 xl56j7k x1nrll8i x10l6tqk xwa60dl x11lhmoz xh8yej3 x1g3bxo5"}}[!!(q&&c("justknobx")._("2691"))<<0],{children:l.jsx(c("PolarisStoriesV3AdFooterCTA.react"),{currSidecarIndex:g,media:k})})),e[1]=n,e[2]=g,e[3]=q,e[4]=o,e[5]=k,e[6]=m,e[7]=s):s=e[7];var t;e[8]!==n||e[9]!==g||e[10]!==q||e[11]!==o||e[12]!==k||e[13]!==m?(t=n!=null&&o!=null&&m&&l.jsx("div",babelHelpers["extends"]({},{0:{className:"x6s0dn4 x78zum5 xl56j7k x1nrll8i x10l6tqk xwa60dl x11lhmoz xh8yej3"},1:{className:"x6s0dn4 x78zum5 xl56j7k x1nrll8i x10l6tqk xwa60dl x11lhmoz xh8yej3 x1g3bxo5"}}[!!(q&&c("justknobx")._("2691"))<<0],{children:l.jsx(c("PolarisStoriesV3AdSticker.react"),{currSidecarIndex:g,media:k})})),e[8]=n,e[9]=g,e[10]=q,e[11]=o,e[12]=k,e[13]=m,e[14]=t):t=e[14];e[15]!==f||e[16]!==p||e[17]!==a||e[18]!==k?(n=a&&l.jsx(c("PolarisStoriesV3ShareButton.react"),{adsLoggingData:p,media:k,reelId:f.ad_id,user:k.user}),e[15]=f,e[16]=p,e[17]=a,e[18]=k,e[19]=n):n=e[19];e[20]!==s||e[21]!==t||e[22]!==n?(g=l.jsx(c("PolarisStoriesV3Footer.react"),{children:l.jsxs("div",babelHelpers["extends"]({},r,{children:[s,t,n]}))}),e[20]=s,e[21]=t,e[22]=n,e[23]=g):g=e[23];return g}g["default"]=a}),98);
__d("PolarisStoriesV3AdGalleryPlayer_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdGalleryPlayer_ad",selections:[{alias:null,args:null,kind:"ScalarField",name:"is_cta_sticker_available_on_web_story",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Player_user"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdHeader_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdFooter_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Player_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3PlayerControllerContextProvider_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MarkAdSeen_media"}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdHeader_ad"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdFooter_ad"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MarkAdSeen_ad"}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdGalleryPlayer_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdGalleryPlayer_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdFooter_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdMediaImpressionWrapper_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Player_viewer"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null}],storageKey:null},action:"THROW"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdHeaderOwner_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdHeaderOwner_ad",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdOwnerTitle_ad"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdSponsoredLabel_ad"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3GenAITransparencyLabel_ad"},{alias:null,args:null,kind:"ScalarField",name:"ad_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"tracking_token",storageKey:null},{alias:null,args:null,concreteType:"XDTIGTransparencyAndControlDisclaimerData",kind:"LinkedField",name:"ig_transparency_and_control_disclaimer_data",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTIGAdTransparencyDisclaimerInfoDict",kind:"LinkedField",name:"disclaimers",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"label",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"placement",storageKey:null}],storageKey:null}],storageKey:null}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdHeaderOwner_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdHeaderOwner_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"owner",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdOwnerAvatar_owner"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdOwnerTitle_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdOwnerAvatar_owner.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdOwnerAvatar_owner",selections:[{alias:null,args:null,kind:"ScalarField",name:"is_unpublished",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdOwnerAvatar.react",["CometRelay","PolarisAdsGatingHelpers","PolarisPostImpressionTrackingNode.react","PolarisStoriesV3AdOwnerAvatar_owner.graphql","PolarisUserAvatar.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useRef;function a(a){var e=d("react-compiler-runtime").c(6);a=a.owner;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdOwnerAvatar_owner.graphql"),a);var f=a.is_unpublished,g=a.username,i=k(null);f=f!==!0;var l;e[0]!==f||e[1]!==a.profile_pic_url||e[2]!==g?(l=j.jsx(c("PolarisUserAvatar.react"),{isLink:f,profilePictureUrl:a.profile_pic_url,size:32,username:g}),e[0]=f,e[1]=a.profile_pic_url,e[2]=g,e[3]=l):l=e[3];f=l;if(d("PolarisAdsGatingHelpers").enableStoryPostImpressionLogging()){e[4]!==f?(a=j.jsx(c("PolarisPostImpressionTrackingNode.react"),{childrenRef:i,componentType:"image",elementId:"profileImage",trackingNode:"PROFILE_IMAGE",children:j.jsx("div",{ref:i,children:f})}),e[4]=f,e[5]=a):a=e[5];return a}return f}g["default"]=a}),98);
__d("PolarisStoriesV3AdOwnerTitle_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdOwnerTitle_ad",selections:[{alias:null,args:null,kind:"ScalarField",name:"display_fb_page_name",storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3AdOwnerTitle_ad"}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdOwnerTitle_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdOwnerTitle_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"owner",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"is_unpublished",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3OwnerTitle_owner"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3AdOwnerTitle_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3OwnerTitle_owner.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3OwnerTitle_owner",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"is_verified",storageKey:null}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3OwnerTitle.react",["CometRelay","IGDSTextVariants.react","IGDSVerifiedIcon.react","PolarisFastLink.react","PolarisGenericStrings","PolarisStoriesV3OwnerTitle_owner.graphql","XPolarisProfileControllerRouteBuilder","react","react-compiler-runtime","usePolarisTrackingDataProfileURLParams"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(15),f=a.owner;a=a.title;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3OwnerTitle_owner.graphql"),f);var g;e[0]!==f.username?(g=c("XPolarisProfileControllerRouteBuilder").buildUri({username:f.username}).toString(),e[0]=f.username,e[1]=g):g=e[1];g=g;var i=d("usePolarisTrackingDataProfileURLParams").usePolarisTrackingDataProfileURLParams(!0,"story"),k;e[2]===Symbol["for"]("react.memo_cache_sentinel")?(k="xeuugli",e[2]=k):k=e[2];var l;e[3]===Symbol["for"]("react.memo_cache_sentinel")?(l={className:"x6s0dn4 x78zum5"},e[3]=l):l=e[3];var m;e[4]!==a?(m=j.jsx(d("IGDSTextVariants.react").IGDSTextBody,{color:"textOnMedia",maxLines:1,children:a}),e[4]=a,e[5]=m):m=e[5];e[6]!==f.is_verified?(a=f.is_verified===!0&&j.jsx("span",babelHelpers["extends"]({className:"xdwrcjd"},{children:j.jsx(c("IGDSVerifiedIcon.react"),{alt:d("PolarisGenericStrings").VERIFIED_TEXT,color:"ig-text-on-media",size:12})})),e[6]=f.is_verified,e[7]=a):a=e[7];e[8]!==m||e[9]!==a?(f=j.jsxs("span",babelHelpers["extends"]({},l,{children:[m,a]})),e[8]=m,e[9]=a,e[10]=f):f=e[10];e[11]!==g||e[12]!==f||e[13]!==i?(l=j.jsx(c("PolarisFastLink.react"),{className:k,href:g,params:i,children:f}),e[11]=g,e[12]=f,e[13]=i,e[14]=l):l=e[14];return l}g["default"]=a}),98);
__d("usePolarisStoriesV3AdOwnerTitle_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3AdOwnerTitle_ad",selections:[{alias:null,args:null,kind:"ScalarField",name:"ad_title",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"display_fb_page_name",storageKey:null}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3AdOwnerTitle_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3AdOwnerTitle_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"owner",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_unpublished",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3AdOwnerTitle",["CometRelay","isStringNullOrEmpty","react-compiler-runtime","usePolarisStoriesV3AdOwnerTitle_ad.graphql","usePolarisStoriesV3AdOwnerTitle_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,e){var f=d("react-compiler-runtime").c(3);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3AdOwnerTitle_ad.graphql"),a);e=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3AdOwnerTitle_media.graphql"),e);var g=a.ad_title;a=a.display_fb_page_name;e=e.owner;if(e.is_unpublished===!0){if(g.trim().length!==0)return g;var j;f[0]!==e.full_name||f[1]!==e.username?(j=c("isStringNullOrEmpty")(e.full_name)?e.username:e.full_name,f[0]=e.full_name,f[1]=e.username,f[2]=j):j=f[2];return j}return a===!0&&g.trim().length!==0?g:e.username}g["default"]=a}),98);
__d("PolarisStoriesV3AdOwnerTitle.react",["CometRelay","IGDSTextVariants.react","PolarisAdsGatingHelpers","PolarisPostImpressionTrackingNode.react","PolarisStoriesV3AdOwnerTitle_ad.graphql","PolarisStoriesV3AdOwnerTitle_media.graphql","PolarisStoriesV3OwnerTitle.react","react","react-compiler-runtime","usePolarisStoriesV3AdOwnerTitle"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react")),l=j.useRef;function a(a){var e=d("react-compiler-runtime").c(7),f=a.ad;a=a.media;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdOwnerTitle_ad.graphql"),f);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3AdOwnerTitle_media.graphql"),a);var g=c("usePolarisStoriesV3AdOwnerTitle")(f,a),j=l(null);if(a.owner.is_unpublished===!0||f.display_fb_page_name===!0){e[0]!==g?(f=k.jsx(d("IGDSTextVariants.react").IGDSTextBodyEmphasized,{color:"textOnMedia",maxLines:1,children:g}),e[0]=g,e[1]=f):f=e[1];f=f}else{var m;e[2]!==a.owner||e[3]!==g?(m=k.jsx(c("PolarisStoriesV3OwnerTitle.react"),{owner:a.owner,title:g}),e[2]=a.owner,e[3]=g,e[4]=m):m=e[4];f=m}if(d("PolarisAdsGatingHelpers").enableStoryPostImpressionLogging()){e[5]!==f?(a=k.jsx(c("PolarisPostImpressionTrackingNode.react"),{childrenRef:j,componentType:"text",elementId:"title",trackingNode:"TITLE",children:k.jsxs("div",{ref:j,children:[" ",f]})}),e[5]=f,e[6]=a):a=e[6];return a}else return f}g["default"]=a}),98);
__d("PolarisStoriesV3AdSponsoredLabel_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdSponsoredLabel_ad",selections:[{alias:null,args:null,kind:"ScalarField",name:"label",storageKey:null}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdSponsoredLabel.react",["CometRelay","PolarisSponsoredLabel.react","PolarisStoriesV3AdSponsoredLabel_ad.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={label:{color:"x1g9anri",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(3),f=a.ad;a=a["data-testid"];f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdSponsoredLabel_ad.graphql"),f);var g;e[0]!==f.label||e[1]!==a?(g=j.jsx(c("PolarisSponsoredLabel.react"),{"data-testid":void 0,label:f.label,xstyle:k.label}),e[0]=f.label,e[1]=a,e[2]=g):g=e[2];return g}g["default"]=a}),98);
__d("PolarisStoriesV3GenAITransparencyLabel_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3GenAITransparencyLabel_ad",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3GenAITransparencyZeroClickModal_ad"}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3GenAITransparencyLabel.react",["CometRelay","GHLSVGText.react","IGDSBox.react","IGDSSpinner.react","JSResourceForInteraction","PolarisAdBlockerMitigationGatingHelpers","PolarisGenAITransparencyLoggingUtils","PolarisStoriesV3GenAITransparencyLabel_ad.graphql","react","react-compiler-runtime","stylex","useIGDSLazyDialog"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react"),l={clickable:{cursor:"x1ypdohk",$$css:!0},fallbackContainer:{alignItems:"x6s0dn4",backgroundColor:"xvbhtw8",bottom:"x1ey2m1c",color:"x5n08af",display:"x78zum5",insetInlineEnd:"xtijo5x",insetInlineStart:"x1o0tod",left:null,right:null,justifyContent:"xl56j7k",paddingTop:"xyamay9",paddingInlineEnd:"xv54qhq",paddingBottom:"x1l90r2v",paddingInlineStart:"xf7dkkf",position:"xixxii4",top:"x13vifvy",$$css:!0},root:{color:"x5n08af",fontSize:"x1fhwpqd",lineHeight:"xli6nsh",$$css:!0}},m=c("JSResourceForInteraction")("PolarisStoriesV3GenAITransparencyZeroClickModal.react").__setRef("PolarisStoriesV3GenAITransparencyLabel.react");function a(a){var e=d("react-compiler-runtime").c(19),f=a.ad,g=a.adClientToken,j=a.adId,p=a.label;a=a.xstyle;var q=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3GenAITransparencyLabel_ad.graphql"),f);e[0]!==p?(f=d("PolarisAdBlockerMitigationGatingHelpers").shouldUseSVGTextMitigation()===!0?k.jsx(c("GHLSVGText.react"),{fontSize:"12",text:p.toString()}):p,e[0]=p,e[1]=f):f=e[1];e[2]!==f?(p=k.jsx(k.Fragment,{children:f}),e[2]=f,e[3]=p):p=e[3];f=p;p=c("useIGDSLazyDialog")(m,o);var r=p[0];e[4]!==q||e[5]!==g||e[6]!==j||e[7]!==r?(p=function(){d("PolarisGenAITransparencyLoggingUtils").logTier3LabelEvent({ad_client_token:g,ad_id:j,event_type:"click",source_surface:"story"}),r({ad:q},n)},e[4]=q,e[5]=g,e[6]=j,e[7]=r,e[8]=p):p=e[8];p=p;var s;e[9]!==g||e[10]!==j?(s={ad_client_token:g,ad_id:j,event_type:"impression",source_surface:"story"},e[9]=g,e[10]=j,e[11]=s):s=e[11];s=s;s=d("PolarisGenAITransparencyLoggingUtils").usePartialViewImpressionEvent(s,"genai_transparency.tier_3.label");var t;e[12]!==a?(t=(i||(i=c("stylex"))).props(l.root,a,l.clickable),e[12]=a,e[13]=t):t=e[13];e[14]!==f||e[15]!==p||e[16]!==s||e[17]!==t?(a=k.jsx("span",babelHelpers["extends"]({},t,{onClick:p,ref:s,role:"link",tabIndex:0,children:f})),e[14]=f,e[15]=p,e[16]=s,e[17]=t,e[18]=a):a=e[18];f=a;return f}function n(){}function o(){return k.jsx(c("IGDSBox.react"),{xstyle:l.fallbackContainer,children:k.jsx(c("IGDSSpinner.react"),{animated:!0,size:"medium"})})}o.displayName=o.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("PolarisStoriesV3Owner.react",["PolarisTrackingNodeProvider.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(22),e=a.avatar,f=a.subtitle,g=a.timestamp,h=a.title;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(a={className:"x6s0dn4 x78zum5 xeuugli"},b[0]=a):a=b[0];var j;b[1]!==e?(j=i.jsx(c("PolarisTrackingNodeProvider.react"),{trackingNode:69,children:function(a){return i.jsx("div",{ref:a,children:e})}}),b[1]=e,b[2]=j):j=b[2];var k;b[3]!==f?(k={0:{className:"x78zum5 xdt5ytf x1gnnpzl xl56j7k x13fj5qh xeuugli"},1:{className:"x78zum5 xdt5ytf x1gnnpzl x13fj5qh xeuugli x1qughib"}}[!!(f!=null)<<0],b[3]=f,b[4]=k):k=b[4];var l;b[5]===Symbol["for"]("react.memo_cache_sentinel")?(l={className:"x6s0dn4 x78zum5"},b[5]=l):l=b[5];var m;b[6]!==h?(m=i.jsx(c("PolarisTrackingNodeProvider.react"),{trackingNode:10,children:function(a){return i.jsx("div",{ref:a,children:h})}}),b[6]=h,b[7]=m):m=b[7];var n;b[8]!==g?(n=g!=null&&i.jsx("div",babelHelpers["extends"]({className:"x13fj5qh"},{children:g})),b[8]=g,b[9]=n):n=b[9];b[10]!==m||b[11]!==n?(g=i.jsxs("div",babelHelpers["extends"]({},l,{children:[m,n]})),b[10]=m,b[11]=n,b[12]=g):g=b[12];b[13]!==f?(l=f!=null&&i.jsx("div",babelHelpers["extends"]({className:"x3nfvp2"},{children:f})),b[13]=f,b[14]=l):l=b[14];b[15]!==k||b[16]!==g||b[17]!==l?(m=i.jsxs("div",babelHelpers["extends"]({},k,{children:[g,l]})),b[15]=k,b[16]=g,b[17]=l,b[18]=m):m=b[18];b[19]!==j||b[20]!==m?(n=i.jsxs("div",babelHelpers["extends"]({},a,{children:[j,m]})),b[19]=j,b[20]=m,b[21]=n):n=b[21];return n}g["default"]=a}),98);
__d("PolarisStoriesV3AdHeaderOwner.react",["CometRelay","IGAdTransparencyDisclaimerPlacement","IGDSBox.react","IGDSMiddleDot.react","PolarisStoriesV3AdHeaderOwner_ad.graphql","PolarisStoriesV3AdHeaderOwner_media.graphql","PolarisStoriesV3AdOwnerAvatar.react","PolarisStoriesV3AdOwnerTitle.react","PolarisStoriesV3AdSponsoredLabel.react","PolarisStoriesV3GenAITransparencyLabel.react","PolarisStoriesV3Owner.react","getJSEnumSafe","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react"),l={label:{color:"x1g9anri",$$css:!0}};function a(a){var e,f=d("react-compiler-runtime").c(19),g=a.ad;a=a.media;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdHeaderOwner_ad.graphql"),g);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3AdHeaderOwner_media.graphql"),a);if(f[0]!==(g==null?void 0:(e=g.ig_transparency_and_control_disclaimer_data)==null?void 0:e.disclaimers)){var j;e=g==null?void 0:(e=g.ig_transparency_and_control_disclaimer_data)==null?void 0:(e=e.disclaimers)==null?void 0:e.find(m);f[0]=g==null?void 0:(j=g.ig_transparency_and_control_disclaimer_data)==null?void 0:j.disclaimers;f[1]=e}else e=f[1];j=e;f[2]!==a.owner?(e=k.jsx(c("PolarisStoriesV3AdOwnerAvatar.react"),{owner:a.owner}),f[2]=a.owner,f[3]=e):e=f[3];var n;f[4]!==g?(n=k.jsx(c("PolarisStoriesV3AdSponsoredLabel.react"),{ad:g,"data-testid":void 0}),f[4]=g,f[5]=n):n=f[5];if(f[6]!==g||f[7]!==j){var o;o=j!=null&&k.jsxs(k.Fragment,{children:[k.jsx(c("IGDSMiddleDot.react"),{color:"textOnMedia"}),k.jsx(c("PolarisStoriesV3GenAITransparencyLabel.react"),{ad:g,adClientToken:(o=g.tracking_token)!=null?o:"",adId:(o=g==null?void 0:g.ad_id)!=null?o:"",label:(o=j.label)!=null?o:"AI Info",xstyle:l.label})]});f[6]=g;f[7]=j;f[8]=o}else o=f[8];f[9]!==n||f[10]!==o?(j=k.jsxs(c("IGDSBox.react"),{alignItems:"start",direction:"row",justifyContent:"end",children:[n,o]}),f[9]=n,f[10]=o,f[11]=j):j=f[11];f[12]!==g||f[13]!==a?(n=k.jsx(c("PolarisStoriesV3AdOwnerTitle.react"),{ad:g,media:a}),f[12]=g,f[13]=a,f[14]=n):n=f[14];f[15]!==e||f[16]!==j||f[17]!==n?(o=k.jsx(c("PolarisStoriesV3Owner.react"),{avatar:e,subtitle:j,title:n}),f[15]=e,f[16]=j,f[17]=n,f[18]=o):o=f[18];return o}function m(a){return c("getJSEnumSafe")(c("IGAdTransparencyDisclaimerPlacement"),a==null?void 0:a.placement)===1}g["default"]=a}),98);
__d("PolarisStoriesV3AdHeader_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdHeader_ad",selections:[{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Header_mediaItems"}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdHeaderOwner_ad"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdOptionsButton_ad"}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdHeader_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdHeader_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdHeaderOwner_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdOptionsButton_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Header_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3PlayerControls_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdOptionsButton_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdOptionsButton_ad",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdOptionsDialog_ad"}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdOptionsButton_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdOptionsButton_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdOptionsDialog_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3OptionsButton.react",["fbt","IGDSDialogPlaceholder.react","IGDSIconButton.react","IGDSMoreHorizontalFilledIcon.react","react","react-compiler-runtime","useIGDSLazyDialog"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react"),k={placeholder:{height:"xjp8j0k",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(7),e=a.dialogProps,f=a.dialogResource,g=a.onPress;a=c("useIGDSLazyDialog")(f,l);var i=a[0];b[0]!==e||b[1]!==g||b[2]!==i?(f=function(){g==null?void 0:g(),i(e)},b[0]=e,b[1]=g,b[2]=i,b[3]=f):f=b[3];a=f;b[4]===Symbol["for"]("react.memo_cache_sentinel")?(f=j.jsx(c("IGDSMoreHorizontalFilledIcon.react"),{alt:h._(/*BTDS*/"Menu"),color:"ig-text-on-media",size:24}),b[4]=f):f=b[4];b[5]!==a?(f=j.jsx(c("IGDSIconButton.react"),{"data-testid":void 0,onClick:a,children:f}),b[5]=a,b[6]=f):f=b[6];return f}function l(a){return j.jsx(c("IGDSDialogPlaceholder.react"),{innerContentXStyle:k.placeholder,onClose:a})}l.displayName=l.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("usePolarisStoriesV3LogAdActionMenu",["InstagramAdActionMenuFalcoEvent","react","react-compiler-runtime","usePolarisStoriesV3GetSharedAdLoggingData","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(3),b=c("usePolarisStoriesV3ViewerLoggingContext")(),e=c("usePolarisStoriesV3GetSharedAdLoggingData")(),f;a[0]!==e||a[1]!==b?(f=function(){var a=e();a.ad_inserted_position;var d=babelHelpers.objectWithoutPropertiesLoose(a,["ad_inserted_position"]);c("InstagramAdActionMenuFalcoEvent").log(function(){return babelHelpers["extends"]({},d,{is_coming_from:"story",pigeon_reserved_keyword_module:String(b.module)})})},a[0]=e,a[1]=b,a[2]=f):f=a[2];return f}g["default"]=a}),98);
__d("PolarisStoriesV3AdOptionsButton.react",["CometRelay","JSResourceForInteraction","PolarisStoriesV3AdOptionsButton_ad.graphql","PolarisStoriesV3AdOptionsButton_media.graphql","PolarisStoriesV3OptionsButton.react","react","react-compiler-runtime","usePolarisStoriesV3LogAdActionMenu"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react"),l=c("JSResourceForInteraction")("PolarisStoriesV3AdOptionsDialog.react").__setRef("PolarisStoriesV3AdOptionsButton.react");function a(a){var e=d("react-compiler-runtime").c(8),f=a.ad;a=a.media;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdOptionsButton_ad.graphql"),f);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3AdOptionsButton_media.graphql"),a);var g=c("usePolarisStoriesV3LogAdActionMenu")(),j;e[0]!==g?(j=function(){g()},e[0]=g,e[1]=j):j=e[1];j=j;var m;e[2]!==f||e[3]!==a?(m={ad:f,media:a},e[2]=f,e[3]=a,e[4]=m):m=e[4];e[5]!==j||e[6]!==m?(f=k.jsx(c("PolarisStoriesV3OptionsButton.react"),{dialogProps:m,dialogResource:l,onPress:j}),e[5]=j,e[6]=m,e[7]=f):f=e[7];return f}g["default"]=a}),98);
__d("PolarisStoriesV3Header_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3Header_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3Header_mediaItems.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"PolarisStoriesV3Header_mediaItems",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ProgressBars_mediaItems"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ProgressBars_mediaItems.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"PolarisStoriesV3ProgressBars_mediaItems",selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3PlayingStateContext",["PolarisStoriesV3PlayingState","react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext(c("PolarisStoriesV3PlayingState").NotStarted);g["default"]=b}),98);
__d("usePolarisStoriesV3PlayingState",["PolarisStoriesV3PlayingStateContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3PlayingStateContext"))}g["default"]=a}),98);
__d("PolarisStoriesV3ProgressContext",["emptyFunction","react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({getDuration:c("emptyFunction").thatReturns(0),getProgress:c("emptyFunction").thatReturns(0),setProgress:c("emptyFunction")});g["default"]=b}),98);
__d("usePolarisStoriesV3ProgressContext",["PolarisStoriesV3ProgressContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3ProgressContext"))}g["default"]=a}),98);
__d("PolarisStoriesV3ProgressBars.react",["CometRelay","Locale","PolarisStoriesV3PlayingState","PolarisStoriesV3ProgressBars_mediaItems.graphql","cancelAnimationFrame","react","react-compiler-runtime","requestAnimationFrame","usePolarisStoriesV3PlayingState","usePolarisStoriesV3ProgressContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));e=i;var k=e.useEffect,l=e.useRef;function m(a){return"translateX("+-((1-a)*100)+"%)"}function n(){var a=d("react-compiler-runtime").c(1),b;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=j.jsx("div",babelHelpers["extends"]({className:"xr9e8f9 x1e4oeot x1ui04y5 x6en5u8 x1iyjqo2 x36qwtl x6ikm8r x10wlt62 x1n2onr6 x1caxmr6"},{"data-testid":void 0})),a[0]=b):b=a[0];return b}function o(){var a=d("react-compiler-runtime").c(1),b;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=j.jsx("div",{className:"x1lix1fw xr9e8f9 x1e4oeot x1ui04y5 x6en5u8 x1iyjqo2 x36qwtl x6ikm8r x10wlt62 x1n2onr6"}),a[0]=b):b=a[0];return b}function p(){var a=d("react-compiler-runtime").c(10),b=c("usePolarisStoriesV3ProgressContext")(),e=c("usePolarisStoriesV3PlayingState")(),f=l(),g=l(null),h,i;a[0]!==e||a[1]!==b?(h=function(){var a=f.current;if(a==null)return;if(e===c("PolarisStoriesV3PlayingState").Playing&&g.current==null){var d=null,h=0,i=function e(){var f=b.getProgress();f===d?f=f+(Date.now()-h)/1e3:(d=f,h=Date.now());f=f/b.getDuration();a.style.transform=m(f);g.current=c("requestAnimationFrame")(e)};g.current=c("requestAnimationFrame")(i)}e!==c("PolarisStoriesV3PlayingState").Playing&&(c("cancelAnimationFrame")(g.current),g.current=null)},i=[e,b],a[0]=e,a[1]=b,a[2]=h,a[3]=i):(h=a[2],i=a[3]);k(h,i);h=b.getProgress()/b.getDuration();a[4]!==h?(i=m(h),a[4]=h,a[5]=i):i=a[5];h=i;var n;a[6]===Symbol["for"]("react.memo_cache_sentinel")?(i={0:{className:"x1lix1fw xr9e8f9 x1e4oeot x1ui04y5 x6en5u8 x1iyjqo2 x36qwtl x6ikm8r x10wlt62 x1n2onr6"},1:{className:"x1lix1fw xr9e8f9 x1e4oeot x1ui04y5 x6en5u8 x1iyjqo2 x36qwtl x6ikm8r x10wlt62 x1n2onr6 x19jd1h0"}}[!!c("Locale").isRTL()<<0],n="x5yr21d x10l6tqk xh8yej3 x1caxmr6 x1so62im",a[6]=i,a[7]=n):(i=a[6],n=a[7]);a[8]!==h?(i=j.jsx("div",babelHelpers["extends"]({},i,{children:j.jsx("div",{className:n,ref:f,style:{transform:h}})})),a[8]=h,a[9]=i):i=a[9];return i}function a(a){var c=d("react-compiler-runtime").c(4),e=a.mediaId;a=a.mediaItems;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ProgressBars_mediaItems.graphql"),a);var f=a.findIndex(function(a){return a.pk===e}),g;c[0]===Symbol["for"]("react.memo_cache_sentinel")?(g={className:"x1ned7t2 x78zum5"},c[0]=g):g=c[0];c[1]!==f||c[2]!==a?(g=j.jsx("div",babelHelpers["extends"]({},g,{children:a.map(function(a,b){if(b<f)return j.jsx(n,{},b);else if(b>f)return j.jsx(o,{},b);else return j.jsx(p,{},b)})})),c[1]=f,c[2]=a,c[3]=g):g=c[3];return g}g["default"]=a}),98);
__d("PolarisStoriesV3PageNavigationContext",["emptyFunction","react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({close:c("emptyFunction")});g["default"]=b}),98);
__d("usePolarisStoriesV3PageNavigationContext",["PolarisStoriesV3PageNavigationContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3PageNavigationContext"))}g["default"]=a}),98);
__d("PolarisStoriesV3Header.react",["CometRelay","IGDSIconButton.react","IGDSXPanoFilledIcon.react","PolarisGenericStrings","PolarisStoriesV3Header_media.graphql","PolarisStoriesV3Header_mediaItems.graphql","PolarisStoriesV3ProgressBars.react","react","react-compiler-runtime","usePolarisIsSmallScreen","usePolarisStoriesV3PageNavigationContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a){var e=d("react-compiler-runtime").c(20),f=a.controls,g=a.media,j=a.mediaItems,l=a.options;a=a.owner;j=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3Header_mediaItems.graphql"),j);g=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3Header_media.graphql"),g);var m=c("usePolarisIsSmallScreen")(),n=c("usePolarisStoriesV3PageNavigationContext")(),o;e[0]!==g.pk||e[1]!==j?(o=k.jsx(c("PolarisStoriesV3ProgressBars.react"),{mediaId:g.pk,mediaItems:j}),e[0]=g.pk,e[1]=j,e[2]=o):o=e[2];e[3]===Symbol["for"]("react.memo_cache_sentinel")?(g={className:"x6s0dn4 x78zum5 x1xmf6yo"},e[3]=g):g=e[3];e[4]!==m?(j={0:{className:"x6s0dn4 x78zum5 x1iyjqo2 xs83m0k x1xegmmw x6ikm8r x10wlt62"},1:{className:"x6s0dn4 x78zum5 x1iyjqo2 xs83m0k x1xegmmw x6ikm8r x10wlt62 xmzvs34"}}[!!m<<0],e[4]=m,e[5]=j):j=e[5];var p;e[6]!==a||e[7]!==j?(p=k.jsx("div",babelHelpers["extends"]({},j,{children:a})),e[6]=a,e[7]=j,e[8]=p):p=e[8];e[9]!==m||e[10]!==n?(a=m&&k.jsx(c("IGDSIconButton.react"),{onClick:function(){return n.close()},children:k.jsx(c("IGDSXPanoFilledIcon.react"),{alt:d("PolarisGenericStrings").CLOSE_TEXT,color:"ig-stroke-on-media"})}),e[9]=m,e[10]=n,e[11]=a):a=e[11];e[12]!==f||e[13]!==l||e[14]!==p||e[15]!==a?(j=k.jsxs("div",babelHelpers["extends"]({},g,{children:[p,f,l,a]})),e[12]=f,e[13]=l,e[14]=p,e[15]=a,e[16]=j):j=e[16];e[17]!==o||e[18]!==j?(m=k.jsxs("div",{children:[o,j]}),e[17]=o,e[18]=j,e[19]=m):m=e[19];return m}g["default"]=a}),98);
__d("PolarisStoriesV3PlayerControls_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3PlayerControls_media",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MediaType_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MutingInfo_media"},{alias:null,args:null,kind:"ScalarField",name:"has_audio",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AudioControllerContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext(null);g["default"]=b}),98);
__d("usePolarisStoriesV3AudioController",["FBLogger","PolarisStoriesV3AudioControllerContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){var a=i(c("PolarisStoriesV3AudioControllerContext"));if(a==null)throw c("FBLogger")("ig_web").mustfixThrow("PolarisStoriesV3AudioControllerContext is empty");return a}g["default"]=a}),98);
__d("PolarisStoriesV3AudioStateContext",["PolarisVideoConstants","react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext(d("PolarisVideoConstants").AUDIO_STATES.off);g["default"]=b}),98);
__d("usePolarisStoriesV3AudioState",["PolarisStoriesV3AudioStateContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3AudioStateContext"))}g["default"]=a}),98);
__d("usePolarisStoriesV3MutingInfo_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3MutingInfo_media",selections:[{alias:null,args:null,concreteType:"XDTStoryMusicStickerTappableObject",kind:"LinkedField",name:"story_music_stickers",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTFlattenedMusicInfo",kind:"LinkedField",name:"music_asset_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"should_mute_audio",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"should_mute_audio_reason",storageKey:null}],storageKey:null}],storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3MutingInfo",["CometRelay","react","react-compiler-runtime","usePolarisStoriesV3MutingInfo_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;(i||d("react")).useMemo;function a(a){var c=d("react-compiler-runtime").c(4);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3MutingInfo_media.graphql"),a);if(c[0]!==a.story_music_stickers){var e;e=(e=a.story_music_stickers)==null?void 0:e.find(j);c[0]=a.story_music_stickers;c[1]=e}else e=c[1];a=e;if(c[2]!==a){e=a!=null?{shouldMuteAudio:Boolean((e=a.music_asset_info)==null?void 0:e.should_mute_audio),shouldMuteAudioReason:(e=a.music_asset_info)==null?void 0:e.should_mute_audio_reason}:null;c[2]=a;c[3]=e}else e=c[3];a=e;return a}function j(a){return a.music_asset_info!=null}g["default"]=a}),98);
__d("PolarisStoriesV3PlayerControls.react",["CometRelay","IGDSIconButton.react","IGDSPlayPanoFilledIcon.react","IGDSWebPauseFilledIcon.react","PolarisGenericStrings","PolarisStoriesV3MediaType","PolarisStoriesV3PauseReason","PolarisStoriesV3PlayerControls_media.graphql","PolarisStoriesV3PlayingState","PolarisVideoConstants","PolarisVideoVolumeControls.react","react","react-compiler-runtime","usePolarisIsSmallScreen","usePolarisStoriesV3AudioController","usePolarisStoriesV3AudioState","usePolarisStoriesV3MediaType","usePolarisStoriesV3MutingInfo","usePolarisStoriesV3PlayerController","usePolarisStoriesV3PlayingState"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(19);a=a.media;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3PlayerControls_media.graphql"),a);var f=c("usePolarisStoriesV3MediaType")(a),g=c("usePolarisStoriesV3MutingInfo")(a),i=c("usePolarisStoriesV3AudioController")(),k=c("usePolarisStoriesV3PlayerController")(),l=c("usePolarisStoriesV3AudioState")(),m=c("usePolarisStoriesV3PlayingState")();a=a.has_audio===!0;var n;e[0]!==k||e[1]!==m?(n=function(){m===c("PolarisStoriesV3PlayingState").Paused?k.play():(m===c("PolarisStoriesV3PlayingState").Playing||m===c("PolarisStoriesV3PlayingState").Stalling)&&k.pause(c("PolarisStoriesV3PauseReason").PauseButton)},e[0]=k,e[1]=m,e[2]=n):n=e[2];n=n;var o;e[3]!==i||e[4]!==l?(o=function(){l===d("PolarisVideoConstants").AUDIO_STATES.on?i.mute():l===d("PolarisVideoConstants").AUDIO_STATES.off&&i.unmute()},e[3]=i,e[4]=l,e[5]=o):o=e[5];o=o;var p=m!==c("PolarisStoriesV3PlayingState").Paused,q=c("usePolarisIsSmallScreen")();g=(g=g==null?void 0:g.shouldMuteAudioReason)!=null?g:void 0;var r;e[6]!==l||e[7]!==o||e[8]!==g?(r=j.jsx(c("PolarisVideoVolumeControls.react"),{absolutePositionToast:!1,audioState:l,forceMuteReason:g,isStoryVideo:!0,onClick:o}),e[6]=l,e[7]=o,e[8]=g,e[9]=r):r=e[9];o=r;e[10]!==p?(g=p?j.jsx(c("IGDSWebPauseFilledIcon.react"),{alt:d("PolarisGenericStrings").ASSISTIVE_TEXT_PAUSE_BUTTON,color:"ig-stroke-on-media",size:16}):j.jsx(c("IGDSPlayPanoFilledIcon.react"),{alt:d("PolarisGenericStrings").ASSISTIVE_TEXT_PLAY_BUTTON,color:"ig-stroke-on-media",size:16}),e[10]=p,e[11]=g):g=e[11];e[12]!==n||e[13]!==g?(r=j.jsx(c("IGDSIconButton.react"),{onClick:n,children:g}),e[12]=n,e[13]=g,e[14]=r):r=e[14];p=r;e[15]===Symbol["for"]("react.memo_cache_sentinel")?(n={className:"x78zum5"},e[15]=n):n=e[15];g=a&&(!q||f===c("PolarisStoriesV3MediaType").Video)&&o;r=!q&&p;e[16]!==g||e[17]!==r?(a=j.jsxs("div",babelHelpers["extends"]({},n,{children:[g,r]})),e[16]=g,e[17]=r,e[18]=a):a=e[18];return a}g["default"]=a}),98);
__d("PolarisStoriesV3AdHeader.react",["CometRelay","PolarisClickEventLoggerProvider.react","PolarisProfileClickEventLoggerProvider.react","PolarisStoriesLoggingUtils","PolarisStoriesV3AdHeaderOwner.react","PolarisStoriesV3AdHeader_ad.graphql","PolarisStoriesV3AdHeader_media.graphql","PolarisStoriesV3AdOptionsButton.react","PolarisStoriesV3Header.react","PolarisStoriesV3PlayerControls.react","react","react-compiler-runtime","usePolarisAnalyticsContext","usePolarisSponsoredPostProfileClickLogger","usePolarisStoriesV3AdLoggingContext","usePolarisStoriesV3ReelLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a){var e=d("react-compiler-runtime").c(27),f=a.ad;a=a.media;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdHeader_ad.graphql"),f);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3AdHeader_media.graphql"),a);var g=c("usePolarisAnalyticsContext")(),j=c("usePolarisStoriesV3ReelLoggingContext")(),l=j.authorId,m=j.followStatus,n=j.mediaPk;j=j.mediaType;var o=c("usePolarisStoriesV3AdLoggingContext")(),p=o.adId;o=o.trackingToken;var q;e[0]!==j?(q=d("PolarisStoriesLoggingUtils").mediaTypeEnumToMediaType(j),e[0]=j,e[1]=q):q=e[1];j=q;e[2]!==p||e[3]!==g||e[4]!==m||e[5]!==n||e[6]!==j||e[7]!==l||e[8]!==o?(q={adId:p,adTrackingToken:o,analyticsContext:g,followStatus:m,mpk:n,postMediaType:j,postOwnerId:l},e[2]=p,e[3]=g,e[4]=m,e[5]=n,e[6]=j,e[7]=l,e[8]=o,e[9]=q):q=e[9];p=c("usePolarisSponsoredPostProfileClickLogger")(q);e[10]!==f||e[11]!==a?(g=k.jsx(c("PolarisStoriesV3AdHeaderOwner.react"),{ad:f,media:a}),e[10]=f,e[11]=a,e[12]=g):g=e[12];m=g;e[13]!==a?(n=k.jsx(c("PolarisStoriesV3PlayerControls.react"),{media:a}),e[13]=a,e[14]=n):n=e[14];j=n;e[15]!==f||e[16]!==a?(l=k.jsx(c("PolarisStoriesV3AdOptionsButton.react"),{ad:f,media:a}),e[15]=f,e[16]=a,e[17]=l):l=e[17];o=l;e[18]!==f.items||e[19]!==j||e[20]!==a||e[21]!==o||e[22]!==m?(q=k.jsx(c("PolarisStoriesV3Header.react"),{controls:j,media:a,mediaItems:f.items,options:o,owner:m}),e[18]=f.items,e[19]=j,e[20]=a,e[21]=o,e[22]=m,e[23]=q):q=e[23];e[24]!==p||e[25]!==q?(g=k.jsx(c("PolarisClickEventLoggerProvider.react"),{children:k.jsx(c("PolarisProfileClickEventLoggerProvider.react"),{onProfileClick:p,children:q})}),e[24]=p,e[25]=q,e[26]=g):g=e[26];return g}g["default"]=a}),98);
__d("PolarisStoriesV3AdMediaImpressionWrapper_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdMediaImpressionWrapper_viewer",selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null}],storageKey:null}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdMediaImpressionWrapper.react",["CometRelay","PolarisContainerModuleUtils","PolarisStoriesLoggingUtils","PolarisStoriesV3AdMediaImpressionWrapper_viewer.graphql","qex","react","react-compiler-runtime","useEmptyFunction","useMergeRefs","usePolarisAnalyticsContext","usePolarisSponsoredStoryMediaImpressionLogger","usePolarisSponsoredStoryMediaImpressionSecondChannelLogger","usePolarisSponsoredStoryMediaTimeSpentLogger","usePolarisSponsoredStoryMediaViewabilityLogger","usePolarisSponsoredStoryMediaVpvdImpressionLogger","usePolarisStoriesV3AdLoggingContext","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");e=c("qex")._("367");var k=e===!0?c("usePolarisSponsoredStoryMediaImpressionSecondChannelLogger"):c("useEmptyFunction");function a(a){var e=d("react-compiler-runtime").c(51),f=a.children;a=a.viewer;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdMediaImpressionWrapper_viewer.graphql"),a);var g=c("usePolarisAnalyticsContext")(),i;e[0]!==g?(i=d("PolarisContainerModuleUtils").getContainerModule(g),e[0]=g,e[1]=i):i=e[1];g=i;i=c("usePolarisStoriesV3ReelLoggingContext")();var l=i.authorId,m=i.followStatus,n=i.mediaId,o=i.mediaPk,p=i.mediaType,q=i.reel,r=i.reelPosition,s=i.reelSize;i=i.takenAt;var t=c("usePolarisStoriesV3ViewerLoggingContext")(),u=t.getTrayPosition,v=t.loggingSession,w=t.reelType,x=t.traySessionId;t=t.viewerSessionId;var y=c("usePolarisStoriesV3AdLoggingContext")(),z=y.adId,A=y.gapToLastAd;y=y.trackingToken;var B;e[2]!==u||e[3]!==q.id?(B=u(q.id).toString(),e[2]=u,e[3]=q.id,e[4]=B):B=e[4];u=B;e[5]!==p?(B=d("PolarisStoriesLoggingUtils").mediaTypeEnumToMediaType(p),e[5]=p,e[6]=B):B=e[6];p=B;e[7]!==i?(B=i==null?void 0:i.toString(),e[7]=i,e[8]=B):B=e[8];i=q.id;var C;e[9]!==s?(C=s.toString(),e[9]=s,e[10]=C):C=e[10];a=a==null?void 0:(s=a.user)==null?void 0:s.pk;e[11]!==z||e[12]!==g||e[13]!==m||e[14]!==o||e[15]!==p||e[16]!==n||e[17]!==l||e[18]!==q.id||e[19]!==r||e[20]!==w||e[21]!==u||e[22]!==B||e[23]!==C||e[24]!==a||e[25]!==y||e[26]!==x||e[27]!==t?(s={adId:z,adInsertedPosition:u,containerModule:g,currentReelItemIndex:r,followStatus:m,mediaType:p,mpk:o,postedAt:B,postId:n,postOwnerId:l,reelId:i,reelSize:C,reelType:w,reelViewerPosition:u,trackingToken:y,traySession:x,viewerId:a,viewerSession:t},e[11]=z,e[12]=g,e[13]=m,e[14]=o,e[15]=p,e[16]=n,e[17]=l,e[18]=q.id,e[19]=r,e[20]=w,e[21]=u,e[22]=B,e[23]=C,e[24]=a,e[25]=y,e[26]=x,e[27]=t,e[28]=s):s=e[28];var D=s;e[29]!==A||e[30]!==D||e[31]!==v?(i=function(){return babelHelpers["extends"]({},D,{gapToLastAd:A,sessionReelCounter:v.getViewerSessionReelsConsumed().toString()})},e[29]=A,e[30]=D,e[31]=v,e[32]=i):i=e[32];g=c("usePolarisSponsoredStoryMediaImpressionLogger")(i);e[33]!==z||e[34]!==o||e[35]!==p||e[36]!==n||e[37]!==y?(m={adId:z,mediaType:p,mpk:o,postId:n,trackingToken:y},e[33]=z,e[34]=o,e[35]=p,e[36]=n,e[37]=y,e[38]=m):m=e[38];l=k(m);e[39]!==D||e[40]!==v?(q=function(){return babelHelpers["extends"]({},D,{sessionReelCounter:v.getViewerSessionReelsConsumed().toString()})},e[39]=D,e[40]=v,e[41]=q):q=e[41];r=c("usePolarisSponsoredStoryMediaTimeSpentLogger")(q);e[42]!==D||e[43]!==v?(w=function(){return babelHelpers["extends"]({},D,{sessionReelCounter:v.getViewerSessionReelsConsumed().toString()})},e[42]=D,e[43]=v,e[44]=w):w=e[44];u=c("usePolarisSponsoredStoryMediaViewabilityLogger")(w);e[45]!==D||e[46]!==v?(B=function(){return babelHelpers["extends"]({},D,{sessionReelCounter:v.getViewerSessionReelsConsumed().toString()})},e[45]=D,e[46]=v,e[47]=B):B=e[47];C=c("usePolarisSponsoredStoryMediaVpvdImpressionLogger")(B);a=c("useMergeRefs")(g,l,r,u,C);e[48]!==f||e[49]!==a?(x=j.jsx("div",{ref:a,children:f}),e[48]=f,e[49]=a,e[50]=x):x=e[50];return x}g["default"]=a}),98);
__d("PolarisStoriesV3MediaFormat",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum")({IMAGE:1,VIDEO:2,CAROUSEL:3});f.PolarisStoriesV3MediaFormat=a}),66);
__d("PolarisStoriesV3MediaOverlayBoostButton_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MediaOverlayBoostButton_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"boosted_status",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boost_unavailable_identifier",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boost_unavailable_reason",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"product_type",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MediaOverlayBoostButton.react",["CometRelay","IGDSBox.react","PolarisBoostAcquisitionUtils","PolarisConfig","PolarisPostBoostButtonType","PolarisStoriesV3MediaOverlayBoostButton_media.graphql","deferredLoadComponent","react","react-compiler-runtime","requireDeferred","usePolarisPageID","usePolarisViewer"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k=c("deferredLoadComponent")(c("requireDeferred")("PolarisPostBoostButton.react").__setRef("PolarisStoriesV3MediaOverlayBoostButton.react"));function a(a){var e,f,g=d("react-compiler-runtime").c(7);a=a.media;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3MediaOverlayBoostButton_media.graphql"),a);e=(e=(e=c("usePolarisViewer")())==null?void 0:e.isProfessionalAccount)!=null?e:!1;f=(a==null?void 0:(f=a.user)==null?void 0:f.id)!=null&&(a==null?void 0:(f=a.user)==null?void 0:f.id)===d("PolarisConfig").getViewerId();e=d("PolarisBoostAcquisitionUtils").isEligibleForBoostButtonOverlayOnStoryViewer(e,f);f=a==null?void 0:a.pk;var i=a==null?void 0:a.boosted_status,l=a==null?void 0:a.product_type,m=a==null?void 0:a.boost_unavailable_identifier;a=a==null?void 0:a.boost_unavailable_reason;var n=c("usePolarisPageID")();if(e&&i!=null&&f!=null&&l!=null){g[0]!==n||g[1]!==m||g[2]!==a||g[3]!==i||g[4]!==f||g[5]!==l?(e=j.jsx(c("IGDSBox.react"),{alignItems:"center",direction:"row",justifyContent:"center",width:"100%",children:j.jsx(k,{analyticsContext:n,boostedStatus:i,boostUnavailableIdentifier:m,boostUnavailableReason:a,buttonType:c("PolarisPostBoostButtonType").STORY_VIEWER_OVERLAY,mediaId:f,productType:l})}),g[0]=n,g[1]=m,g[2]=a,g[3]=i,g[4]=f,g[5]=l,g[6]=e):e=g[6];return e}return null}g["default"]=a}),98);
__d("PolarisStoriesV3MediaOverlayInfoFooterCTA_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MediaOverlayInfoFooterCTA_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MediaOverlayBoostButton_media"},{args:null,kind:"FragmentSpread",name:"usePolarisShowFooterCTA_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MediaOverlayInfoFooterCTA.react",["CometRelay","PolarisStoriesV3MediaOverlayBoostButton.react","PolarisStoriesV3MediaOverlayInfoFooterCTA_media.graphql","PolarisStoriesV3PauseReason","PolarisStoriesV3PlayingState","PolarisStoryMediaOverlayInfoFooterCTA.react","react","react-compiler-runtime","usePolarisShowFooterCTA.next.react","usePolarisStoriesV3PlayerController"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));e=i;e.useMemo;var k=e.useRef;function a(a){var e=d("react-compiler-runtime").c(14),f=a.media;a=a.viewerIsOwner;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3MediaOverlayInfoFooterCTA_media.graphql"),f);var g=c("usePolarisStoriesV3PlayerController")(),i=k(!1);if(e[0]!==f.pk){var l;l=(l={},l[f.pk]=!0,l);e[0]=f.pk;e[1]=l}else l=e[1];l=l;l=l;l=c("usePolarisShowFooterCTA.next.react")(f,-1,l,!1);l=l==null?void 0:l.bottomBannerInfo;if(l==null){var m;e[2]!==f?(m=j.jsx(c("PolarisStoriesV3MediaOverlayBoostButton.react"),{media:f}),e[2]=f,e[3]=m):m=e[3];return m}e[4]!==g?(m=function(){g.getPlayingState()===c("PolarisStoriesV3PlayingState").Playing&&(i.current=!0,g.pause(c("PolarisStoriesV3PauseReason").MediaOverlayCTA))},e[4]=g,e[5]=m):m=e[5];m=m;var n;e[6]!==g?(n=function(){i.current&&(i.current=!1,g.play())},e[6]=g,e[7]=n):n=e[7];n=n;var o;e[8]!==l||e[9]!==n||e[10]!==m||e[11]!==f.pk||e[12]!==a?(o=j.jsx(c("PolarisStoryMediaOverlayInfoFooterCTA.react"),{bannerInfo:l,entityID:f.pk,onCloseBloksApp:n,viewerIsOwner:a,onOpenBloksApp:m,trayEntrypoint:"reel_url"}),e[8]=l,e[9]=n,e[10]=m,e[11]=f.pk,e[12]=a,e[13]=o):o=e[13];return o}g["default"]=a}),98);
__d("PolarisStoriesV3CaptionBottomSheet_caption.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3CaptionBottomSheet_caption",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3CaptionModalContent_caption"}],type:"XDTCommentDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3CaptionBottomSheet.react",["fbt","CometRelay","IGDSBottomSheet.react","PolarisStoriesV3CaptionBottomSheet_caption.graphql","PolarisStoriesV3CaptionModalContent.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||d("react"),l=h._(/*BTDS*/"Caption");function a(a){var e=d("react-compiler-runtime").c(5),f=a.caption;a=a.onClose;f=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3CaptionBottomSheet_caption.graphql"),f);var g;e[0]!==f?(g=k.jsx("div",{onPointerDown:m,children:k.jsx(c("PolarisStoriesV3CaptionModalContent.react"),{caption:f})}),e[0]=f,e[1]=g):g=e[1];e[2]!==a||e[3]!==g?(f=k.jsx(c("IGDSBottomSheet.react"),{bottomSheetHeaderText:l,onClose:a,children:g}),e[2]=a,e[3]=g,e[4]=f):f=e[4];return f}function m(a){return a.stopPropagation()}g["default"]=a}),226);
__d("PolarisStoriesV3Caption_caption.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3Caption_caption",selections:[{alias:null,args:null,kind:"ScalarField",name:"background_color",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"background_color_alpha",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"text_color",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"text_size",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3CaptionBottomSheet_caption"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3CaptionModal_caption"}],type:"XDTCommentDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3MediaPlayerSize",["PolarisDesktopStoriesGalleryConstants","react","react-compiler-runtime","usePolarisStoriesV3ViewerSizeContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useMemo;function a(){var a=d("react-compiler-runtime").c(3),b=c("usePolarisStoriesV3ViewerSizeContext")();b=b.width;var e=b/d("PolarisDesktopStoriesGalleryConstants").STORY_VIEWER_ASPECT_RATIO_W_H,f;a[0]!==e||a[1]!==b?(f={height:e,width:b},a[0]=e,a[1]=b,a[2]=f):f=a[2];e=f;return e}g["default"]=a}),98);
__d("PolarisStoriesV3Caption.react",["CometRelay","IGDSDialogBackwardsCompatibilityWrapper.react","IGDSDialogPlaceholder.react","JSResourceForInteraction","PolarisSponsoredStoryCaption.react","PolarisStoriesV3CaptionBottomSheet.react","PolarisStoriesV3Caption_caption.graphql","isStringNullOrEmpty","react","react-compiler-runtime","useIGDSLazyDialog","usePolarisIsSmallScreen","usePolarisStoriesV3MediaPlayerSize"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useState,l=c("JSResourceForInteraction")("PolarisStoriesV3CaptionModal.react").__setRef("PolarisStoriesV3Caption.react"),m={placeholder:{height:"x18dl8mb",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(19),f=a.caption;a=a.maxCaptionHeight;var g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3Caption_caption.graphql"),f);f=c("usePolarisStoriesV3MediaPlayerSize")();f=f.width;var i=c("useIGDSLazyDialog")(l,n),m=i[0];i=k(!1);var o=i[0],p=i[1],q=c("usePolarisIsSmallScreen")();if(g==null||c("isStringNullOrEmpty")(g.text))return null;e[0]!==g||e[1]!==q||e[2]!==m?(i=function(){if(q){p(!0);return}m({caption:g})},e[0]=g,e[1]=q,e[2]=m,e[3]=i):i=e[3];i=i;var r;e[4]!==g.background_color||e[5]!==g.background_color_alpha||e[6]!==g.text||e[7]!==g.text_color||e[8]!==g.text_size||e[9]!==i||e[10]!==a||e[11]!==f?(r=j.jsx(c("PolarisSponsoredStoryCaption.react"),{caption:g.text,maxCaptionHeight:a,maxCaptionWidth:f,onMoreClick:i,textBackgroundColor:g.background_color,textBackgroundColorAlpha:g.background_color_alpha,textColor:g.text_color,textSize:g.text_size}),e[4]=g.background_color,e[5]=g.background_color_alpha,e[6]=g.text,e[7]=g.text_color,e[8]=g.text_size,e[9]=i,e[10]=a,e[11]=f,e[12]=r):r=e[12];e[13]!==g||e[14]!==o?(i=o&&j.jsx(c("IGDSDialogBackwardsCompatibilityWrapper.react"),{children:j.jsx(c("PolarisStoriesV3CaptionBottomSheet.react"),{caption:g,onClose:function(){return p(!1)}})}),e[13]=g,e[14]=o,e[15]=i):i=e[15];e[16]!==r||e[17]!==i?(a=j.jsxs(j.Fragment,{children:[r,i]}),e[16]=r,e[17]=i,e[18]=a):a=e[18];return a}function n(a){return j.jsx(c("IGDSDialogPlaceholder.react"),{innerContentXStyle:m.placeholder,onClose:a})}n.displayName=n.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("PolarisStoriesV3InteractionOverlay_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null};return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3InteractionOverlay_media",selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[a],storageKey:null},a,{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CTAUrl_media"}],type:"XDTMediaDict",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisPointerInteractions",["Locale","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useRef;function a(a){var b=d("react-compiler-runtime").c(23),e=a.longPressThresholdMs,f=a.onLongPressIn,g=a.onLongPressOut,h=a.onPressOut,j=a.onStart,k=a.onSwipeBackward,l=a.onSwipeDown,m=a.onSwipeEnd,n=a.onSwipeForward,o=a.onSwipeLeft,p=a.onSwipeRight,q=a.onSwipeUp,r=a.restraint,s=a.threshold;a=a.isRTL;var t=e===void 0?750:e,u=r===void 0?100:r,v=s===void 0?50:s;b[0]!==a?(e=a===void 0?c("Locale").isRTL():a,b[0]=a,b[1]=e):e=b[1];var w=e,x=i(),y=i();b[2]!==t||b[3]!==f||b[4]!==j?(r=function(a){x.current=a,j(),y.current=window.setTimeout(function(){f()},t)},b[2]=t,b[3]=f,b[4]=j,b[5]=r):r=b[5];s=r;b[6]!==w||b[7]!==t||b[8]!==g||b[9]!==h||b[10]!==k||b[11]!==l||b[12]!==m||b[13]!==n||b[14]!==o||b[15]!==p||b[16]!==q||b[17]!==u||b[18]!==v?(a=function(a){y.current!=null&&window.clearTimeout(y.current);if(x.current==null)return;var b=a.timeStamp-x.current.timeStamp,c=b>=t,d=a.clientX-x.current.clientX,e=a.clientY-x.current.clientY,f=Math.abs(d)>=v&&Math.abs(e)<=u,i=Math.abs(e)>=v&&Math.abs(d)<=u,j=f||i;c?g({duration:b}):j?(f&&d>0?(p==null?void 0:p(),w?k==null?void 0:k():n==null?void 0:n()):f&&d<0?(o==null?void 0:o(),w?n==null?void 0:n():k==null?void 0:k()):i&&e>0?l==null?void 0:l():i&&e<0&&(q==null?void 0:q()),m==null?void 0:m()):h(a);x.current=null},b[6]=w,b[7]=t,b[8]=g,b[9]=h,b[10]=k,b[11]=l,b[12]=m,b[13]=n,b[14]=o,b[15]=p,b[16]=q,b[17]=u,b[18]=v,b[19]=a):a=b[19];e=a;b[20]!==s||b[21]!==e?(r={handlePointerDown:s,handlePointerUp:e},b[20]=s,b[21]=e,b[22]=r):r=b[22];return r}g["default"]=a}),98);
__d("usePolarisStoriesV3LogReelMediaPause",["PolarisStoriesV3LoggingUtils","ReelMediaPauseFalcoEvent","react","usePolarisStoriesV3GetSharedLoggingData","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useCallback;function a(){var b=c("usePolarisStoriesV3ReelLoggingContext")(),e=b.reel;b=c("usePolarisStoriesV3ViewerLoggingContext")();var a=b.module,g=c("usePolarisStoriesV3GetSharedLoggingData")();return i(function(b){c("ReelMediaPauseFalcoEvent").log(function(){return babelHelpers["extends"]({},g(),{media_source:"organic",module_name:String(a),pause_duration:d("PolarisStoriesV3LoggingUtils").msToS(b),reel_id:e.id})})},[g,a,e.id])}g["default"]=a}),98);
__d("PolarisStoriesV3ReplyInputContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({inputRef:a.createRef()});g["default"]=b}),98);
__d("usePolarisStoriesV3ReplyInputContext",["PolarisStoriesV3ReplyInputContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3ReplyInputContext"))}g["default"]=a}),98);
__d("PolarisStoriesV3InteractionOverlay.react",["CometEventListener","CometRelay","PolarisNavigationUtils","PolarisStoriesV3InteractionOverlay_media.graphql","PolarisStoriesV3PauseReason","react","react-compiler-runtime","usePolarisPointerInteractions","usePolarisStoriesV3CTAUrl","usePolarisStoriesV3LogReelMediaPause","usePolarisStoriesV3NavigationContext","usePolarisStoriesV3PageNavigationContext","usePolarisStoriesV3PlayerController","usePolarisStoriesV3ReplyInputContext","useWindowSize"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));e=i;var k=e.useEffect,l=e.useRef;function a(a){var e=d("react-compiler-runtime").c(52),f=a.media;a=a.playerWidth;var g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3InteractionOverlay_media.graphql"),f),i=c("usePolarisStoriesV3PlayerController")(),n=c("usePolarisStoriesV3LogReelMediaPause")(),o=c("usePolarisStoriesV3PageNavigationContext")(),p=c("usePolarisStoriesV3NavigationContext")(),q=c("usePolarisStoriesV3CTAUrl")(g);e[0]!==i?(f=function(){i.pause(c("PolarisStoriesV3PauseReason").Press)},e[0]=i,e[1]=f):f=e[1];f=f;var r;e[2]!==i?(r=function(){var a=i.getPauseReason();a===c("PolarisStoriesV3PauseReason").Press&&i.pause(c("PolarisStoriesV3PauseReason").LongPress)},e[2]=i,e[3]=r):r=e[3];r=r;var s=function(a){a.target===C.current&&p.hasPrev?p.prev("tap_back"):a.target===D.current?p.next("tap_forward"):i.play()},t;e[4]!==n||e[5]!==i?(t=function(a){a=a.duration;n(a);i.play()},e[4]=n,e[5]=i,e[6]=t):t=e[6];t=t;var u;e[7]!==p?(u=function(){p.nextReel("swipe_forward")},e[7]=p,e[8]=u):u=e[8];u=u;var v;e[9]!==p?(v=function(){p.prevReel("swipe_back")},e[9]=p,e[10]=v):v=e[10];v=v;var w=c("usePolarisStoriesV3ReplyInputContext")(),x;e[11]!==q||e[12]!==g||e[13]!==w?(x=function(){if(q!=null){var a;d("PolarisNavigationUtils").openExternalURL(q,"_blank","StoriesPage",(a=g.user)==null?void 0:a.pk,g.pk)}else{(a=w.inputRef)==null?void 0:(a=a.current)==null?void 0:a.focus()}},e[11]=q,e[12]=g,e[13]=w,e[14]=x):x=e[14];x=x;var y;e[15]!==i?(y=function(){i.play()},e[15]=i,e[16]=y):y=e[16];y=y;var z;e[17]!==o?(z=function(){return o.close("swipe_down")},e[17]=o,e[18]=z):z=e[18];var A;e[19]!==f||e[20]!==r||e[21]!==t||e[22]!==s||e[23]!==u||e[24]!==y||e[25]!==v||e[26]!==x||e[27]!==z?(A={onLongPressIn:r,onLongPressOut:t,onPressOut:s,onStart:f,onSwipeBackward:u,onSwipeDown:z,onSwipeEnd:y,onSwipeForward:v,onSwipeUp:x},e[19]=f,e[20]=r,e[21]=t,e[22]=s,e[23]=u,e[24]=y,e[25]=v,e[26]=x,e[27]=z,e[28]=A):A=e[28];f=c("usePolarisPointerInteractions")(A);r=f.handlePointerDown;var B=f.handlePointerUp;e[29]!==B?(t=function(){var a=c("CometEventListener").listen(window,"pointerup",B);return function(){return a.remove()}},s=[B],e[29]=B,e[30]=t,e[31]=s):(t=e[30],s=e[31]);k(t,s);var C=l(),D=l();u=m;y=c("useWindowSize")();v=y.innerWidth;x=a<v?(v-a)/2:0;e[32]!==x?(z=x>0?{marginLeft:-x,marginRight:-x}:null,e[32]=x,e[33]=z):z=e[33];A=z;f=x+a*.2;e[34]!==f?(t={width:f},e[34]=f,e[35]=t):t=e[35];s=t;y=x+a*.8;e[36]!==y?(v={width:y},e[36]=y,e[37]=v):v=e[37];z=v;e[38]===Symbol["for"]("react.memo_cache_sentinel")?(f="x1ey2m1c xtijo5x x1o0tod x10l6tqk x13vifvy x5ve5x3",e[38]=f):f=e[38];e[39]===Symbol["for"]("react.memo_cache_sentinel")?(t="x1ey2m1c x1o0tod x10l6tqk x13vifvy",e[39]=t):t=e[39];e[40]!==C||e[41]!==s?(x=j.jsx("div",{className:t,ref:C,style:s}),e[40]=C,e[41]=s,e[42]=x):x=e[42];e[43]===Symbol["for"]("react.memo_cache_sentinel")?(a="x1ey2m1c xtijo5x x10l6tqk x13vifvy",e[43]=a):a=e[43];e[44]!==D||e[45]!==z?(y=j.jsx("div",{className:a,ref:D,style:z}),e[44]=D,e[45]=z,e[46]=y):y=e[46];e[47]!==r||e[48]!==A||e[49]!==x||e[50]!==y?(v=j.jsxs("div",{className:f,onContextMenu:u,onPointerDown:r,role:"presentation",style:A,children:[x,y]}),e[47]=r,e[48]=A,e[49]=x,e[50]=y,e[51]=v):v=e[51];return v}function m(a){a.preventDefault()}g["default"]=a}),98);
__d("PolarisStoriesV3Image_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3Image_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"accessibility_caption",storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ImageSrc_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3Image.react",["CometImage.react","CometRelay","PolarisStoriesV3Image_media.graphql","react","react-compiler-runtime","usePolarisStoriesV3ImageSrc"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(6),f=a.media,g=a.onLoad,i=a.ref;a=a.xstyle;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3Image_media.graphql"),f);var k=c("usePolarisStoriesV3ImageSrc")(f);f=(f=f.accessibility_caption)!=null?f:void 0;var l;e[0]!==g||e[1]!==i||e[2]!==k||e[3]!==f||e[4]!==a?(l=j.jsx(c("CometImage.react"),{alt:f,draggable:!1,objectFit:"cover",onLoad:g,ref:i,src:k,testid:void 0,xstyle:a}),e[0]=g,e[1]=i,e[2]=k,e[3]=f,e[4]=a,e[5]=l):l=e[5];return l}g["default"]=a}),98);
__d("PolarisStoriesV3ImagePlayer_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ImagePlayer_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Image_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3PauseOnHiddenObserver",["PolarisStoriesV3PauseReason","react","react-compiler-runtime","usePolarisStoriesV3PlayerController","useVisibilityObserver"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(7),b=c("usePolarisStoriesV3PlayerController")(),e;a[0]!==b?(e=function(){b.canPause()&&b.pause(c("PolarisStoriesV3PauseReason").Hidden)},a[0]=b,a[1]=e):e=a[1];e=e;var f;a[2]!==b?(f=function(){b.getPauseReason()===c("PolarisStoriesV3PauseReason").Hidden&&b.play()},a[2]=b,a[3]=f):f=a[3];f=f;var g;a[4]!==e||a[5]!==f?(g={onHidden:e,onVisible:f},a[4]=e,a[5]=f,a[6]=g):g=a[6];return c("useVisibilityObserver")(g)}g["default"]=a}),98);
__d("PolarisStoriesV3PlayerControllerEventType",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum").Mirrored(["Change","Start","End"]);c=a;f["default"]=c}),66);
__d("usePolarisStoriesV3PlayerControllerSubscription",["PolarisStoriesV3PlayerControllerEventType","PolarisStoriesV3PlayingState","react","react-compiler-runtime","usePolarisStoriesV3PlayerController"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useEffect,j=b.useRef;function a(a){var b=d("react-compiler-runtime").c(4),e=c("usePolarisStoriesV3PlayerController")(),f=j(!1),g,h;b[0]!==e||b[1]!==a?(g=function(){var b=e.getPlayingState();b!==c("PolarisStoriesV3PlayingState").NotStarted&&!f.current&&(f.current=!0,a.onStart==null?void 0:a.onStart(e.getState()));var d=e.subscribe(function(b){bb13:switch(b.type){case c("PolarisStoriesV3PlayerControllerEventType").Start:f.current=!0;a.onStart==null?void 0:a.onStart(b.state);break bb13;case c("PolarisStoriesV3PlayerControllerEventType").End:a.onEnd==null?void 0:a.onEnd(b.state);break bb13;case c("PolarisStoriesV3PlayerControllerEventType").Change:a.onChange==null?void 0:a.onChange(b.state)}});return function(){return d.remove()}},h=[e,a,f],b[0]=e,b[1]=a,b[2]=g,b[3]=h):(g=b[2],h=b[3]);i(g,h)}g["default"]=a}),98);
__d("usePolarisStoriesV3PlayerTimer",["PolarisStoriesV3PlayingState","cancelAnimationFrame","react","react-compiler-runtime","requestAnimationFrame","usePolarisStoriesV3PlayerController","usePolarisStoriesV3PlayerControllerSubscription","usePolarisStoriesV3ProgressContext"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useCallback;var i=b.useEffect;b.useMemo;var j=b.useRef;function a(){var a=d("react-compiler-runtime").c(10),b=c("usePolarisStoriesV3PlayerController")(),e=c("usePolarisStoriesV3ProgressContext")(),f=j(0),g=j(),h,k;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(h=function(){return function(){c("cancelAnimationFrame")(g.current)}},k=[g],a[0]=h,a[1]=k):(h=a[0],k=a[1]);i(h,k);a[2]!==b||a[3]!==e?(h=function(){if(g.current!=null)return;var a=Date.now(),d=function d(){var h=Date.now();f.current=f.current+(h-a)/1e3;a=h;e.setProgress(f.current);e.getProgress()>=e.getDuration()&&b.end();g.current=c("requestAnimationFrame")(d)};g.current=c("requestAnimationFrame")(d)},a[2]=b,a[3]=e,a[4]=h):h=a[4];var l=h;a[5]===Symbol["for"]("react.memo_cache_sentinel")?(k=function(){c("cancelAnimationFrame")(g.current),g.current=null},a[5]=k):k=a[5];var m=k;a[6]!==l?(h=function(a){a=a.playingState;var b=g.current!=null;!b&&a===c("PolarisStoriesV3PlayingState").Playing?l():b&&a===c("PolarisStoriesV3PlayingState").Paused&&m()},a[6]=l,a[7]=h):h=a[7];k=h;a[8]!==k?(h={onChange:k,onStart:k},a[8]=k,a[9]=h):h=a[9];k=h;a=k;c("usePolarisStoriesV3PlayerControllerSubscription")(a)}g["default"]=a}),98);
__d("usePolarisStoriesV3StartPlayerOnVisibleCallback",["PolarisStoriesV3PlayingState","react","react-compiler-runtime","usePolarisStoriesV3PlayerController","useVisibilityObserver"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useCallback;var i=b.useRef;function a(){var a=d("react-compiler-runtime").c(12),b=i(!1),e=i(!1),f=i(!1),g=c("usePolarisStoriesV3PlayerController")(),h;a[0]!==g?(h=function(){g.getPlayingState()===c("PolarisStoriesV3PlayingState").NotStarted&&(g.start(),e.current=!0)},a[0]=g,a[1]=h):h=a[1];var j=h;a[2]!==j?(h=function(){!e.current&&b.current?j():f.current||(f.current=!0)},a[2]=j,a[3]=h):h=a[3];h=h;var k;a[4]!==j?(k=function(){b.current=!0,!e.current&&f.current&&j()},a[4]=j,a[5]=k):k=a[5];k=k;var l;a[6]===Symbol["for"]("react.memo_cache_sentinel")?(l=function(){b.current=!1},a[6]=l):l=a[6];l=l;a[7]!==k?(l={onHidden:l,onVisible:k},a[7]=k,a[8]=l):l=a[8];k=c("useVisibilityObserver")(l);a[9]!==k||a[10]!==h?(l=[k,h],a[9]=k,a[10]=h,a[11]=l):l=a[11];return l}g["default"]=a}),98);
__d("PolarisStoriesV3ImagePlayer.react",["CometRelay","PolarisStoriesV3Image.react","PolarisStoriesV3ImagePlayer_media.graphql","react","react-compiler-runtime","useMergeRefs","usePolarisStoriesV3PauseOnHiddenObserver","usePolarisStoriesV3PlayerTimer","usePolarisStoriesV3StartPlayerOnVisibleCallback"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(7),f=a.media;a=a.xstyle;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ImagePlayer_media.graphql"),f);c("usePolarisStoriesV3PlayerTimer")();var g=c("usePolarisStoriesV3StartPlayerOnVisibleCallback")(),i=g[0],k=g[1];g=c("usePolarisStoriesV3PauseOnHiddenObserver")();i=c("useMergeRefs")(i,g);e[0]!==k?(g=function(){k()},e[0]=k,e[1]=g):g=e[1];g=g;var l;e[2]!==g||e[3]!==f||e[4]!==i||e[5]!==a?(l=j.jsx(c("PolarisStoriesV3Image.react"),{media:f,onLoad:g,ref:i,xstyle:a}),e[2]=g,e[3]=f,e[4]=i,e[5]=a,e[6]=l):l=e[6];return l}g["default"]=a}),98);
__d("PolarisStoriesV3Media_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3Media_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ImagePlayer_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3VideoPlayer_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MediaType_media"},{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3VideoPlayerSurface_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3VideoPlayerSurface_media",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3VideoLoggingEffect_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3VideoLoggingEffect_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3VideoLoggingEffect_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"organic_tracking_token",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisDashInfo_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3VideoLoggingEffect",["CometRelay","isStringNullOrEmpty","react-compiler-runtime","usePolarisAnalyticsContext","usePolarisDashInfo","usePolarisStoriesV3AdLoggingContext","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3VideoLoggingEffect_media.graphql","usePolarisVideoXControllerLogger"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var e=d("react-compiler-runtime").c(11);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3VideoLoggingEffect_media.graphql"),a);var f=c("usePolarisAnalyticsContext")(),g=c("usePolarisDashInfo")(a),i=c("usePolarisStoriesV3AdLoggingContext")();i=i.videoLoggerAdInfo;var j=c("usePolarisStoriesV3ReelLoggingContext")(),k=j.authorId;j=j.followStatus;var l;e[0]!==j?(l=c("isStringNullOrEmpty")(j)?void 0:j,e[0]=j,e[1]=l):l=e[1];j=a.pk;var m=Number(a.taken_at);e[2]!==f||e[3]!==k||e[4]!==g||e[5]!==a.organic_tracking_token||e[6]!==a.pk||e[7]!==l||e[8]!==m||e[9]!==i?(j={adInfo:i,analyticsContext:f,carouselParentId:null,dashInfo:g,followingStatus:l,id:j,ownerId:k,postedAt:m,trackingToken:a.organic_tracking_token},e[2]=f,e[3]=k,e[4]=g,e[5]=a.organic_tracking_token,e[6]=a.pk,e[7]=l,e[8]=m,e[9]=i,e[10]=j):j=e[10];c("usePolarisVideoXControllerLogger")(j)}g["default"]=a}),98);
__d("PolarisStoriesV3VideoPlayerSurface.react",["CometRelay","IGDSSpinner.react","PolarisStoriesV3PauseReason","PolarisStoriesV3PlayingState","PolarisStoriesV3VideoPlayerSurface_media.graphql","PolarisVideoConstants","VideoPlayerHooks","VideoPlayerSurface.react","react","react-compiler-runtime","requestAnimationFrame","usePolarisStoriesV3AudioController","usePolarisStoriesV3PlayerController","usePolarisStoriesV3PlayerControllerSubscription","usePolarisStoriesV3ProgressContext","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3VideoLoggingEffect"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=i||(i=d("react"));e=i;e.useCallback;var l=e.useEffect;e.useMemo;function a(a){var e=d("react-compiler-runtime").c(28);a=a.media;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3VideoPlayerSurface_media.graphql"),a);var f=(j||(j=d("VideoPlayerHooks"))).useController(),g=c("usePolarisStoriesV3AudioController")(),i=c("usePolarisStoriesV3PlayerController")(),m=c("usePolarisStoriesV3ProgressContext")(),n=c("usePolarisStoriesV3ReelLoggingContext")();c("usePolarisStoriesV3VideoLoggingEffect")(a);e[0]!==f||e[1]!==i?(a=function(a){a=a.playingState;var b=f.getCurrentState(),d=b.paused;b=b.playing;a===c("PolarisStoriesV3PlayingState").Playing&&!b?c("requestAnimationFrame")(function(){var a=i.getState();a.playingState===c("PolarisStoriesV3PlayingState").Playing&&f.play("user_initiated")}):a===c("PolarisStoriesV3PlayingState").Paused&&!d&&f.pause("user_initiated")},e[0]=f,e[1]=i,e[2]=a):a=e[2];a=a;var o;e[3]!==a?(o={onChange:a,onEnd:a,onStart:a},e[3]=a,e[4]=o):o=e[4];a=o;o=a;c("usePolarisStoriesV3PlayerControllerSubscription")(o);e[5]!==f||e[6]!==i?(a=function(){var a=f.subscribe(function(){var a=i.getPlayingState(),b=f.getCurrentState(),d=b.ended,e=b.paused,g=b.playing;b=b.stalling;d&&a!==c("PolarisStoriesV3PlayingState").Ended?i.end():g&&i.canPlay()?i.play():e&&i.canPause()?i.pause(c("PolarisStoriesV3PauseReason").VideoPlayer):b&&a===c("PolarisStoriesV3PlayingState").Playing&&i.stalling()});return function(){return a.remove()}},o=[f,i],e[5]=f,e[6]=i,e[7]=a,e[8]=o):(a=e[7],o=e[8]);l(a,o);e[9]!==f||e[10]!==m?(a=function(){var a=f.subscribe(function(){var a=f.getPlayheadPosition();m.setProgress(a)});return function(){return a.remove()}},o=[f,m],e[9]=f,e[10]=m,e[11]=a,e[12]=o):(a=e[11],o=e[12]);l(a,o);e[13]!==g||e[14]!==f?(a=function(){var a=function(){var a=g.getAudioState(),b=f.getCurrentState();b=b.muted;b&&a===d("PolarisVideoConstants").AUDIO_STATES.on?f.setMuted(!1,"user_initiated"):!b&&a===d("PolarisVideoConstants").AUDIO_STATES.off&&f.setMuted(!0,"user_initiated")};a();var b=g.subscribe(a);return function(){return b.remove()}},o=[f,g],e[13]=g,e[14]=f,e[15]=a,e[16]=o):(a=e[15],o=e[16]);l(a,o);e[17]!==f||e[18]!==n.reelPosition||e[19]!==n.reelSize?(a=function(){f.setLoggingToSNAPLAdditionalData({story_reel_position:String(n.reelPosition),story_reel_size:String(n.reelSize)})},e[17]=f,e[18]=n.reelPosition,e[19]=n.reelSize,e[20]=a):a=e[20];e[21]!==f||e[22]!==n?(o=[f,n],e[21]=f,e[22]=n,e[23]=o):o=e[23];l(a,o);a=j.useStalling();e[24]!==a?(o=a&&k.jsx(c("IGDSSpinner.react"),{position:"absolute",size:"medium"}),e[24]=a,e[25]=o):o=e[25];e[26]!==o?(a=k.jsx(c("VideoPlayerSurface.react"),{children:o}),e[26]=o,e[27]=a):a=e[27];return a}g["default"]=a}),98);
__d("PolarisStoriesV3VideoPlayer_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3VideoPlayer_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"organic_tracking_token",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"original_width",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"original_height",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3VideoPlayerSurface_media"},{args:null,kind:"FragmentSpread",name:"usePolarisDashInfo_media"},{args:null,kind:"FragmentSpread",name:"usePolarisVideoVersionsSrc_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3StartPlayerOnVisibleEffect",["react","react-compiler-runtime","usePolarisStoriesV3StartPlayerOnVisibleCallback"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(){var a=d("react-compiler-runtime").c(3),b=c("usePolarisStoriesV3StartPlayerOnVisibleCallback")(),e=b[0],f=b[1],g;a[0]!==f?(b=function(){f()},g=[f],a[0]=f,a[1]=b,a[2]=g):(b=a[1],g=a[2]);i(b,g);return e}g["default"]=a}),98);
__d("usePolarisVideoVersionsSrc_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisVideoVersionsSrc_media",selections:[{alias:null,args:null,concreteType:"XDTVideoVersion",kind:"LinkedField",name:"video_versions",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null}],storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisVideoVersionsSrc",["CometRelay","PolarisMediaTypes","react","react-compiler-runtime","usePolarisVideoVersionsSrc_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;(i||d("react")).useMemo;function j(a,b){return a==null?void 0:(a=a.find(function(a){return a.type===b}))==null?void 0:a.url}function a(a){var c=d("react-compiler-runtime").c(7);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisVideoVersionsSrc_media.graphql"),a);a=a.video_versions;var e;c[0]!==a?(e=j(a,d("PolarisMediaTypes").MediaVersionType.VIDEO_640_HIGH),c[0]=a,c[1]=e):e=c[1];if(c[2]!==a){var f;f=(f=j(a,d("PolarisMediaTypes").MediaVersionType.VIDEO_480_HIGH))!=null?f:j(a,d("PolarisMediaTypes").MediaVersionType.VIDEO_480_LOW);c[2]=a;c[3]=f}else f=c[3];c[4]!==e||c[5]!==f?(a={hd:e,sd:f},c[4]=e,c[5]=f,c[6]=a):a=c[6];e=a;return e}g["default"]=a}),98);
__d("PolarisStoriesV3VideoPlayer.react",["CometRelay","PolarisStoriesV3VideoPlayerSurface.react","PolarisStoriesV3VideoPlayer_media.graphql","PolarisVideo.react","react","react-compiler-runtime","stylex","usePolarisDashInfo","usePolarisStoriesV3AdLoggingContext","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3StartPlayerOnVisibleEffect","usePolarisVideoVersionsSrc"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a){var e=d("react-compiler-runtime").c(20),f=a.media;a=a.xstyle;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3VideoPlayer_media.graphql"),f);var g=f.original_height,j=f.original_width,m=f.pk,n=c("usePolarisDashInfo")(f),o=c("usePolarisVideoVersionsSrc")(f),p=c("usePolarisStoriesV3StartPlayerOnVisibleEffect")(),q=c("usePolarisStoriesV3AdLoggingContext")();q=q.videoLoggerAdInfo;var r=c("usePolarisStoriesV3ReelLoggingContext")();r=r.authorId;var s;e[0]!==a?(s=(i||(i=c("stylex"))).props(a),e[0]=a,e[1]=s):s=e[1];a=o.hd;var t=String(m),u;e[2]!==f?(u=k.jsx(c("PolarisStoriesV3VideoPlayerSurface.react"),{media:f}),e[2]=f,e[3]=u):u=e[3];e[4]!==r||e[5]!==n||e[6]!==f.organic_tracking_token||e[7]!==g||e[8]!==j||e[9]!==m||e[10]!==o.hd||e[11]!==o.sd||e[12]!==t||e[13]!==u||e[14]!==q?(a=k.jsx(c("PolarisVideo.react"),{adInfo:q,canAutoplay:!0,dashInfo:n,hdSrc:a,loopCount:0,mediaId:t,originalHeight:g,originalWidth:j,ownerId:r,renderVideoPixelsFit:l,sdSrc:o.sd,trackingToken:f.organic_tracking_token,children:u},m),e[4]=r,e[5]=n,e[6]=f.organic_tracking_token,e[7]=g,e[8]=j,e[9]=m,e[10]=o.hd,e[11]=o.sd,e[12]=t,e[13]=u,e[14]=q,e[15]=a):a=e[15];e[16]!==p||e[17]!==s||e[18]!==a?(r=k.jsx("div",babelHelpers["extends"]({},s,{"data-testid":void 0,ref:p,children:a})),e[16]=p,e[17]=s,e[18]=a,e[19]=r):r=e[19];return r}function l(){return{objectFit:"cover"}}g["default"]=a}),98);
__d("PolarisStoriesV3Media.react",["CometRelay","PolarisPostImpressionTrackingNode.react","PolarisStoriesV3ImagePlayer.react","PolarisStoriesV3MediaFormat","PolarisStoriesV3MediaType","PolarisStoriesV3Media_media.graphql","PolarisStoriesV3VideoPlayer.react","gkx","react","react-compiler-runtime","usePolarisStoriesV3MediaType"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useRef,l={media:{height:"x5yr21d",maxHeight:"xmz0i5r",maxWidth:"x193iq5w",width:"xh8yej3",$$css:!0}},m="adMedia";function a(a){var e=d("react-compiler-runtime").c(16),f=a.adMediaFormat,g=a.media;a=a.sideCarIndex;a=a===void 0?0:a;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3Media_media.graphql"),g);var i=c("usePolarisStoriesV3MediaType")(g),n=k(null),o=null;bb0:switch(i){case c("PolarisStoriesV3MediaType").Image:var p;e[0]!==g?(p=j.jsx(c("PolarisStoriesV3ImagePlayer.react"),{media:g,xstyle:l.media}),e[0]=g,e[1]=p):p=e[1];o=p;break bb0;case c("PolarisStoriesV3MediaType").Video:e[2]!==g?(p=j.jsx(c("PolarisStoriesV3VideoPlayer.react"),{media:g,xstyle:l.media}),e[2]=g,e[3]=p):p=e[3];o=p}if(c("gkx")("8639")){p=i===c("PolarisStoriesV3MediaType").Image?"image":"video";var q=f===d("PolarisStoriesV3MediaFormat").PolarisStoriesV3MediaFormat.CAROUSEL?m+String(a):m,r;e[4]!==f||e[5]!==g||e[6]!==i||e[7]!==a?(r=f===d("PolarisStoriesV3MediaFormat").PolarisStoriesV3MediaFormat.CAROUSEL?{carousel_media_id:g.id,carousel_media_type:String(i),index_of_card:String(a)}:null,e[4]=f,e[5]=g,e[6]=i,e[7]=a,e[8]=r):r=e[8];f=r;e[9]!==o?(g=j.jsx("div",{ref:n,children:o}),e[9]=o,e[10]=g):g=e[10];e[11]!==f||e[12]!==p||e[13]!==q||e[14]!==g?(i=j.jsx(c("PolarisPostImpressionTrackingNode.react"),{carouselInfo:f,childrenRef:n,componentType:p,elementId:q,trackingNode:"MEDIA",children:g}),e[11]=f,e[12]=p,e[13]=q,e[14]=g,e[15]=i):i=e[15];return i}return o}g["default"]=a}),98);
__d("PolarisStoriesV3MediaPlayer_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MediaPlayer_media",selections:[{alias:null,args:null,concreteType:"XDTCommentDict",kind:"LinkedField",name:"caption",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"text",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Caption_caption"}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Media_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Stickers_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3InteractionOverlay_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MediaBackgroundStyle_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MediaSize_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3CommentSticker_sticker.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3CommentSticker_sticker",selections:[{alias:null,args:null,kind:"ScalarField",name:"x",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"rotation",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaVCRTappableData",kind:"LinkedField",name:"vcr_sticker",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3CommentStickerPopover_sticker"}],storageKey:null},action:"THROW"}],type:"XDTMediaVCRTappableObject",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3StickerPopoverTriggerRefContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({current:null});g["default"]=b}),98);
__d("PolarisStoriesV3TappableSticker.react",["CometPressable.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={root:{pointerEvents:"x67bb7w",position:"x10l6tqk",touchAction:"x5ve5x3",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(18),e=a.config,f=a.contextRef,g=a.isPopoverVisible;a=a.onPress;var h=Number(e.height),k=Number(e.rotation),l=Number(e.width),m=Number(e.x);e=Number(e.y);h=h*100+"%";m=m*100+"%";e=e*100+"%";k="translate(-50%, -50%) rotate("+k*Math.PI*2+"rad)";l=l*100+"%";var n;b[0]!==h||b[1]!==m||b[2]!==e||b[3]!==k||b[4]!==l?(n={height:h,left:m,top:e,transform:k,width:l},b[0]=h,b[1]=m,b[2]=e,b[3]=k,b[4]=l,b[5]=n):n=b[5];h=n;b[6]!==f||b[7]!==h?(m=f&&i.jsx("div",{ref:f,style:{left:h.left,position:"absolute",top:h.top}}),b[6]=f,b[7]=h,b[8]=m):m=b[8];b[9]!==a||b[10]!==h?(e=i.jsx(c("CometPressable.react"),{onPress:a,overlayDisabled:!0,style:h,xstyle:j.root}),b[9]=a,b[10]=h,b[11]=e):e=b[11];b[12]!==g?(k=g===!0&&i.jsx("div",{className:"x5yr21d x67bb7w xh8yej3"}),b[12]=g,b[13]=k):k=b[13];b[14]!==m||b[15]!==e||b[16]!==k?(l=i.jsxs(i.Fragment,{children:[m,e,k]}),b[14]=m,b[15]=e,b[16]=k,b[17]=l):l=b[17];return l}g["default"]=a}),98);
__d("PolarisStoriesV3StickerPopoverTrigger.react",["IGDSLazyPopoverTrigger.react","PolarisStoriesV3StickerPopoverTriggerRefContext","PolarisStoriesV3TappableSticker.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useContext;function a(a){var b=d("react-compiler-runtime").c(7),e=a.config,f=a.popoverProps;a=a.popoverResource;var g=j(c("PolarisStoriesV3StickerPopoverTriggerRefContext")),h;b[0]!==e?(h=function(a,b,d,f,g,h,j,k){return i.jsx(c("PolarisStoriesV3TappableSticker.react"),{config:e,contextRef:a,isPopoverVisible:k,onPress:b})},b[0]=e,b[1]=h):h=b[1];var k;b[2]!==f||b[3]!==a||b[4]!==g||b[5]!==h?(k=i.jsx(c("IGDSLazyPopoverTrigger.react"),{align:"middle",imperativeRef:g,popoverProps:f,popoverResource:a,popoverType:"dialog",position:"above",preloadTrigger:"button",children:h}),b[2]=f,b[3]=a,b[4]=g,b[5]=h,b[6]=k):k=b[6];return k}g["default"]=a}),98);
__d("PolarisStoriesV3CommentSticker.react",["CometRelay","JSResourceForInteraction","PolarisStoriesV3CommentSticker_sticker.graphql","PolarisStoriesV3StickerPopoverTrigger.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k=c("JSResourceForInteraction")("PolarisStoriesV3CommentStickerPopover.react").__setRef("PolarisStoriesV3CommentSticker.react");function a(a){var e=d("react-compiler-runtime").c(11);a=a.sticker;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3CommentSticker_sticker.graphql"),a);var f=a.height,g=a.rotation,i=a.vcr_sticker,l=a.width,m=a.x;a=a.y;var n;e[0]!==f||e[1]!==g||e[2]!==l||e[3]!==m||e[4]!==a?(n={height:f,rotation:g,width:l,x:m,y:a},e[0]=f,e[1]=g,e[2]=l,e[3]=m,e[4]=a,e[5]=n):n=e[5];e[6]!==i?(f={comment:i},e[6]=i,e[7]=f):f=e[7];e[8]!==n||e[9]!==f?(g=j.jsx(c("PolarisStoriesV3StickerPopoverTrigger.react"),{config:n,popoverProps:f,popoverResource:k}),e[8]=n,e[9]=f,e[10]=g):g=e[10];return g}g["default"]=a}),98);
__d("PolarisStoriesV3FeedMediaSticker_sticker.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3FeedMediaSticker_sticker",selections:[{alias:null,args:null,kind:"ScalarField",name:"x",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"rotation",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3FeedMediaStickerPopover_feedMedia"}],type:"XDTStoryFeedMediaTappableObject",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3FeedMediaSticker.react",["CometRelay","JSResourceForInteraction","PolarisStoriesV3FeedMediaSticker_sticker.graphql","PolarisStoriesV3StickerPopoverTrigger.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k=c("JSResourceForInteraction")("PolarisStoriesV3FeedMediaStickerPopover.react").__setRef("PolarisStoriesV3FeedMediaSticker.react");function a(a){var e=d("react-compiler-runtime").c(11);a=a.sticker;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3FeedMediaSticker_sticker.graphql"),a);var f=a.height,g=a.rotation,i=a.width,l=a.x,m=a.y,n;e[0]!==f||e[1]!==g||e[2]!==i||e[3]!==l||e[4]!==m?(n={height:f,rotation:g,width:i,x:l,y:m},e[0]=f,e[1]=g,e[2]=i,e[3]=l,e[4]=m,e[5]=n):n=e[5];e[6]!==a?(f={feedMedia:a},e[6]=a,e[7]=f):f=e[7];e[8]!==n||e[9]!==f?(g=j.jsx(c("PolarisStoriesV3StickerPopoverTrigger.react"),{config:n,popoverProps:f,popoverResource:k}),e[8]=n,e[9]=f,e[10]=g):g=e[10];return g}g["default"]=a}),98);
__d("PolarisStoriesV3HashtagSticker_sticker.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3HashtagSticker_sticker",selections:[{alias:null,args:null,kind:"ScalarField",name:"x",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"rotation",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTStoryHashtagDict",kind:"LinkedField",name:"hashtag",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3HashtagStickerPopover_hashtag"}],storageKey:null},action:"THROW"}],type:"XDTStoryHashtagTappableObject",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3HashtagSticker.react",["CometRelay","JSResourceForInteraction","PolarisStoriesV3HashtagSticker_sticker.graphql","PolarisStoriesV3StickerPopoverTrigger.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k=c("JSResourceForInteraction")("PolarisStoriesV3HashtagStickerPopover.react").__setRef("PolarisStoriesV3HashtagSticker.react");function a(a){var e=d("react-compiler-runtime").c(11);a=a.sticker;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3HashtagSticker_sticker.graphql"),a);var f=a.hashtag,g=a.height,i=a.rotation,l=a.width,m=a.x;a=a.y;var n;e[0]!==g||e[1]!==i||e[2]!==l||e[3]!==m||e[4]!==a?(n={height:g,rotation:i,width:l,x:m,y:a},e[0]=g,e[1]=i,e[2]=l,e[3]=m,e[4]=a,e[5]=n):n=e[5];e[6]!==f?(g={hashtag:f},e[6]=f,e[7]=g):g=e[7];e[8]!==n||e[9]!==g?(i=j.jsx(c("PolarisStoriesV3StickerPopoverTrigger.react"),{config:n,popoverProps:g,popoverResource:k}),e[8]=n,e[9]=g,e[10]=i):i=e[10];return i}g["default"]=a}),98);
__d("PolarisStoriesV3LinkSticker_sticker.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3LinkSticker_sticker",selections:[{alias:null,args:null,kind:"ScalarField",name:"x",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"rotation",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTStoryLinkInfoDict",kind:"LinkedField",name:"story_link",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3LinkStickerPopover_link"}],storageKey:null},action:"THROW"}],type:"XDTStoryLinkTappableObject",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3LinkSticker.react",["CometRelay","JSResourceForInteraction","PolarisStoriesV3LinkSticker_sticker.graphql","PolarisStoriesV3StickerPopoverTrigger.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k=c("JSResourceForInteraction")("PolarisStoriesV3LinkStickerPopover.react").__setRef("PolarisStoriesV3LinkSticker.react");function a(a){var e=d("react-compiler-runtime").c(11);a=a.sticker;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3LinkSticker_sticker.graphql"),a);var f=a.height,g=a.rotation,i=a.story_link,l=a.width,m=a.x;a=a.y;var n;e[0]!==f||e[1]!==g||e[2]!==l||e[3]!==m||e[4]!==a?(n={height:f,rotation:g,width:l,x:m,y:a},e[0]=f,e[1]=g,e[2]=l,e[3]=m,e[4]=a,e[5]=n):n=e[5];e[6]!==i?(f={link:i},e[6]=i,e[7]=f):f=e[7];e[8]!==n||e[9]!==f?(g=j.jsx(c("PolarisStoriesV3StickerPopoverTrigger.react"),{config:n,popoverProps:f,popoverResource:k}),e[8]=n,e[9]=f,e[10]=g):g=e[10];return g}g["default"]=a}),98);
__d("PolarisStoriesV3LocationSticker_sticker.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3LocationSticker_sticker",selections:[{alias:null,args:null,kind:"ScalarField",name:"x",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"rotation",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTLocationDict",kind:"LinkedField",name:"location",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3LocationStickerPopover_location"}],storageKey:null},action:"THROW"}],type:"XDTStoryLocationTappableObject",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3LocationSticker.react",["CometRelay","JSResourceForInteraction","PolarisStoriesV3LocationSticker_sticker.graphql","PolarisStoriesV3StickerPopoverTrigger.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k=c("JSResourceForInteraction")("PolarisStoriesV3LocationStickerPopover.react").__setRef("PolarisStoriesV3LocationSticker.react");function a(a){var e=d("react-compiler-runtime").c(11);a=a.sticker;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3LocationSticker_sticker.graphql"),a);var f=a.height,g=a.location,i=a.rotation,l=a.width,m=a.x;a=a.y;var n;e[0]!==f||e[1]!==i||e[2]!==l||e[3]!==m||e[4]!==a?(n={height:f,rotation:i,width:l,x:m,y:a},e[0]=f,e[1]=i,e[2]=l,e[3]=m,e[4]=a,e[5]=n):n=e[5];e[6]!==g?(f={location:g},e[6]=g,e[7]=f):f=e[7];e[8]!==n||e[9]!==f?(i=j.jsx(c("PolarisStoriesV3StickerPopoverTrigger.react"),{config:n,popoverProps:f,popoverResource:k}),e[8]=n,e[9]=f,e[10]=i):i=e[10];return i}g["default"]=a}),98);
__d("PolarisStoriesV3MentionSticker_sticker.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MentionSticker_sticker",selections:[{alias:null,args:null,kind:"ScalarField",name:"x",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"rotation",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTBloksStickerDataDict",kind:"LinkedField",name:"bloks_sticker",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTBloksStickerNativeProps",kind:"LinkedField",name:"sticker_data",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTBloksStickerNativePropsIGMention",kind:"LinkedField",name:"ig_mention",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MentionStickerPopover_mention"}],storageKey:null}],storageKey:null}],storageKey:null},action:"THROW"}],type:"XDTStoryBloksStickerDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MentionSticker.react",["CometRelay","JSResourceForInteraction","PolarisStoriesV3MentionSticker_sticker.graphql","PolarisStoriesV3StickerPopoverTrigger.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k=c("JSResourceForInteraction")("PolarisStoriesV3MentionStickerPopover.react").__setRef("PolarisStoriesV3MentionSticker.react");function a(a){var e=d("react-compiler-runtime").c(11);a=a.sticker;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3MentionSticker_sticker.graphql"),a);var f=a.bloks_sticker,g=a.height,i=a.rotation,l=a.width,m=a.x;a=a.y;f=(f=f.sticker_data)==null?void 0:f.ig_mention;if(f==null)return null;var n;e[0]!==g||e[1]!==i||e[2]!==l||e[3]!==m||e[4]!==a?(n={height:g,rotation:i,width:l,x:m,y:a},e[0]=g,e[1]=i,e[2]=l,e[3]=m,e[4]=a,e[5]=n):n=e[5];e[6]!==f?(g={mention:f},e[6]=f,e[7]=g):g=e[7];e[8]!==n||e[9]!==g?(i=j.jsx(c("PolarisStoriesV3StickerPopoverTrigger.react"),{config:n,popoverProps:g,popoverResource:k}),e[8]=n,e[9]=g,e[10]=i):i=e[10];return i}g["default"]=a}),98);
__d("PolarisStoriesV3Stickers_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3Stickers_media",selections:[{alias:null,args:null,concreteType:"XDTMediaVCRTappableObject",kind:"LinkedField",name:"visual_comment_reply_sticker_info",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3CommentSticker_sticker"}],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryBloksStickerDict",kind:"LinkedField",name:"story_bloks_stickers",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MentionSticker_sticker"}],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryLinkTappableObject",kind:"LinkedField",name:"story_link_stickers",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3LinkSticker_sticker"}],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryHashtagTappableObject",kind:"LinkedField",name:"story_hashtags",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3HashtagSticker_sticker"}],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryLocationTappableObject",kind:"LinkedField",name:"story_locations",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3LocationSticker_sticker"}],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryFeedMediaTappableObject",kind:"LinkedField",name:"story_feed_media",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3FeedMediaSticker_sticker"}],storageKey:null},{alias:null,args:null,concreteType:"XDTTextPostShareToIgStoryStickerTappableObject",kind:"LinkedField",name:"text_post_share_to_ig_story_stickers",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3TextPostShareSticker_sticker"}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3UnsupportedStickers_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3TextPostShareSticker_sticker.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3TextPostShareSticker_sticker",selections:[{alias:null,args:null,kind:"ScalarField",name:"x",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"rotation",storageKey:null},{alias:null,args:null,concreteType:"XDTTextPostShareToIgStoryStickerDict",kind:"LinkedField",name:"text_post_share_to_ig_story_sticker",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3TextPostShareStickerPopover_stickerDict"}],storageKey:null}],type:"XDTTextPostShareToIgStoryStickerTappableObject",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3TextPostShareSticker.react",["CometRelay","JSResourceForInteraction","PolarisStoriesV3StickerPopoverTrigger.react","PolarisStoriesV3TextPostShareSticker_sticker.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k=c("JSResourceForInteraction")("PolarisStoriesV3TextPostShareStickerPopover.react").__setRef("PolarisStoriesV3TextPostShareSticker.react");function a(a){var e=d("react-compiler-runtime").c(11);a=a.sticker;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3TextPostShareSticker_sticker.graphql"),a);var f=a.height,g=a.rotation,i=a.text_post_share_to_ig_story_sticker,l=a.width,m=a.x;a=a.y;var n;e[0]!==f||e[1]!==g||e[2]!==l||e[3]!==m||e[4]!==a?(n={height:f,rotation:g,width:l,x:m,y:a},e[0]=f,e[1]=g,e[2]=l,e[3]=m,e[4]=a,e[5]=n):n=e[5];e[6]!==i?(f={stickerDict:i},e[6]=i,e[7]=f):f=e[7];e[8]!==n||e[9]!==f?(g=j.jsx(c("PolarisStoriesV3StickerPopoverTrigger.react"),{config:n,popoverProps:f,popoverResource:k}),e[8]=n,e[9]=f,e[10]=g):g=e[10];return g}g["default"]=a}),98);
__d("PolarisStoriesV3UnsupportedStickers_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{alias:null,args:null,kind:"ScalarField",name:"x",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"rotation",storageKey:null}];return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3UnsupportedStickers_media",selections:[{alias:null,args:null,concreteType:"XDTStoryCountdownTappableObject",kind:"LinkedField",name:"story_countdowns",plural:!0,selections:a,storageKey:null},{alias:null,args:null,concreteType:"XDTStoryQuestionTappableObject",kind:"LinkedField",name:"story_questions",plural:!0,selections:a,storageKey:null},{alias:null,args:null,concreteType:"XDTStorySliderTappableObject",kind:"LinkedField",name:"story_sliders",plural:!0,selections:a,storageKey:null}],type:"XDTMediaDict",abstractKey:null}}();e.exports=a}),null);
__d("PolarisStoriesV3UnsupportedStickers.react",["CometPlaceholder.react","CometRelay","JSResourceForInteraction","PolarisStoriesV3TappableSticker.react","PolarisStoriesV3UnsupportedStickers_media.graphql","lazyLoadComponent","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useState,l=c("lazyLoadComponent")(c("JSResourceForInteraction")("PolarisStoriesAppInstallDialog.react").__setRef("PolarisStoriesV3UnsupportedStickers.react"));function a(a){var e=d("react-compiler-runtime").c(19);a=a.media;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3UnsupportedStickers_media.graphql"),a);var f=a.story_countdowns,g=a.story_questions;a=a.story_sliders;var i=k(!1),m=i[0],n=i[1];e[0]===Symbol["for"]("react.memo_cache_sentinel")?(i=function(){n(!0)},e[0]=i):i=e[0];var o=i;e[1]===Symbol["for"]("react.memo_cache_sentinel")?(i=function(){n(!1)},e[1]=i):i=e[1];i=i;var p;e[2]!==f?(p=f!=null?f:[],e[2]=f,e[3]=p):p=e[3];e[4]!==g?(f=g!=null?g:[],e[4]=g,e[5]=f):f=e[5];e[6]!==a?(g=a!=null?a:[],e[6]=a,e[7]=g):g=e[7];e[8]!==p||e[9]!==f||e[10]!==g?(a=[].concat(p,f,g),e[8]=p,e[9]=f,e[10]=g,e[11]=a):a=e[11];p=a;e[12]!==p?(f=p==null?void 0:p.map(function(a,b){var d=a.height,e=a.rotation,f=a.width,g=a.x;a=a.y;return j.jsx(c("PolarisStoriesV3TappableSticker.react"),{config:{height:d,rotation:e,width:f,x:g,y:a},onPress:o},b)}),e[12]=p,e[13]=f):f=e[13];e[14]!==m?(g=m&&j.jsx(c("CometPlaceholder.react"),{fallback:null,children:j.jsx(l,{onClose:i})}),e[14]=m,e[15]=g):g=e[15];e[16]!==f||e[17]!==g?(a=j.jsxs(j.Fragment,{children:[f,g]}),e[16]=f,e[17]=g,e[18]=a):a=e[18];return a}g["default"]=a}),98);
__d("usePolarisStoriesV3KeyCommandsContext",["PolarisStoriesV3KeyCommandsContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3KeyCommandsContext"))}g["default"]=a}),98);
__d("PolarisStoriesV3Stickers.react",["CometRelay","FocusGroup.react","FocusWithinHandler.react","PolarisStoriesV3CommentSticker.react","PolarisStoriesV3FeedMediaSticker.react","PolarisStoriesV3HashtagSticker.react","PolarisStoriesV3LinkSticker.react","PolarisStoriesV3LocationSticker.react","PolarisStoriesV3MentionSticker.react","PolarisStoriesV3StickerPopoverTriggerRefContext","PolarisStoriesV3Stickers_media.graphql","PolarisStoriesV3TextPostShareSticker.react","PolarisStoriesV3UnsupportedStickers.react","focusScopeQueries","react","react-compiler-runtime","stylex","usePolarisStoriesV3KeyCommandsContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react"));e=j;e.useCallback;var l=e.useContext,m=e.useEffect,n=e.useRef;e=d("FocusGroup.react").createFocusGroup(d("focusScopeQueries").tabbableScopeQuery);var o=e[0],p=e[1],q={root:{pointerEvents:"x47corl",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(40),f=a.media;a=a.xstyle;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3Stickers_media.graphql"),f);var g=l(c("PolarisStoriesV3StickerPopoverTriggerRefContext")),j;e[0]!==g.current?(j=function(){var a;(a=g.current)==null?void 0:a.hide()},e[0]=g.current,e[1]=j):j=e[1];j=j;var p=c("usePolarisStoriesV3KeyCommandsContext")(),y=p.disableKeyCommands,z=n(null),A;e[2]===Symbol["for"]("react.memo_cache_sentinel")?(p=function(){return function(){return z.current==null?void 0:z.current()}},A=[],e[2]=p,e[3]=A):(p=e[2],A=e[3]);m(p,A);e[4]!==y?(p=function(a){z.current!=null&&(z.current(),z.current=null),a&&(z.current=y())},e[4]=y,e[5]=p):p=e[5];A=p;e[6]!==a?(p=(i||(i=c("stylex"))).props(q.root,a),e[6]=a,e[7]=p):p=e[7];if(e[8]!==f.visual_comment_reply_sticker_info){a=(a=f.visual_comment_reply_sticker_info)==null?void 0:a.map(x);e[8]=f.visual_comment_reply_sticker_info;e[9]=a}else a=e[9];if(e[10]!==f.story_bloks_stickers){var B;B=(B=f.story_bloks_stickers)==null?void 0:B.map(w);e[10]=f.story_bloks_stickers;e[11]=B}else B=e[11];if(e[12]!==f.story_link_stickers){var C;C=(C=f.story_link_stickers)==null?void 0:C.map(v);e[12]=f.story_link_stickers;e[13]=C}else C=e[13];if(e[14]!==f.story_hashtags){var D;D=(D=f.story_hashtags)==null?void 0:D.map(u);e[14]=f.story_hashtags;e[15]=D}else D=e[15];if(e[16]!==f.story_locations){var E;E=(E=f.story_locations)==null?void 0:E.map(t);e[16]=f.story_locations;e[17]=E}else E=e[17];if(e[18]!==f.story_feed_media){var F;F=(F=f.story_feed_media)==null?void 0:F.map(s);e[18]=f.story_feed_media;e[19]=F}else F=e[19];if(e[20]!==f.text_post_share_to_ig_story_stickers){var G;G=(G=f.text_post_share_to_ig_story_stickers)==null?void 0:G.map(r);e[20]=f.text_post_share_to_ig_story_stickers;e[21]=G}else G=e[21];var H;e[22]!==f?(H=k.jsx(c("PolarisStoriesV3UnsupportedStickers.react"),{media:f}),e[22]=f,e[23]=H):H=e[23];e[24]!==E||e[25]!==F||e[26]!==G||e[27]!==H||e[28]!==p||e[29]!==a||e[30]!==B||e[31]!==C||e[32]!==D?(f=k.jsxs("div",babelHelpers["extends"]({},p,{children:[a,B,C,D,E,F,G,H]})),e[24]=E,e[25]=F,e[26]=G,e[27]=H,e[28]=p,e[29]=a,e[30]=B,e[31]=C,e[32]=D,e[33]=f):f=e[33];e[34]!==j||e[35]!==f?(E=k.jsx(o,{onNavigate:j,orientation:"both",tabScopeQuery:d("focusScopeQueries").tabbableScopeQuery,wrap:!0,children:f}),e[34]=j,e[35]=f,e[36]=E):E=e[36];e[37]!==A||e[38]!==E?(F=k.jsx(c("FocusWithinHandler.react"),{onFocusChange:A,children:E}),e[37]=A,e[38]=E,e[39]=F):F=e[39];return F}function r(a,b){b="TextPostShareSticker_"+b;return k.jsx(p,{children:k.jsx(c("PolarisStoriesV3TextPostShareSticker.react"),{sticker:a})},b)}r.displayName=r.name+" [from "+f.id+"]";function s(a,b){b="FeedMediaSticker_"+b;return k.jsx(p,{children:k.jsx(c("PolarisStoriesV3FeedMediaSticker.react"),{sticker:a})},b)}s.displayName=s.name+" [from "+f.id+"]";function t(a,b){b="LocationSticker_"+b;return k.jsx(p,{children:k.jsx(c("PolarisStoriesV3LocationSticker.react"),{sticker:a})},b)}t.displayName=t.name+" [from "+f.id+"]";function u(a,b){b="HashtagSticker_"+b;return k.jsx(p,{children:k.jsx(c("PolarisStoriesV3HashtagSticker.react"),{sticker:a})},b)}u.displayName=u.name+" [from "+f.id+"]";function v(a,b){b="LinkSticker_"+b;return k.jsx(p,{children:k.jsx(c("PolarisStoriesV3LinkSticker.react"),{sticker:a})},b)}v.displayName=v.name+" [from "+f.id+"]";function w(a,b){b="MentionSticker_"+b;return k.jsx(p,{children:k.jsx(c("PolarisStoriesV3MentionSticker.react"),{sticker:a})},b)}w.displayName=w.name+" [from "+f.id+"]";function x(a,b){b="CommentSticker_"+b;return k.jsx(p,{children:k.jsx(c("PolarisStoriesV3CommentSticker.react"),{sticker:a})},b)}x.displayName=x.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("usePolarisStoriesV3MediaBackgroundStyle_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{alias:null,args:null,kind:"ScalarField",name:"background_color",storageKey:null}];return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3MediaBackgroundStyle_media",selections:[{alias:null,args:null,concreteType:"XDTReelMediaBackground",kind:"LinkedField",name:"reel_media_background",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMediaBackgroundColor",kind:"LinkedField",name:"top",plural:!1,selections:a,storageKey:null},{alias:null,args:null,concreteType:"XDTMediaBackgroundColor",kind:"LinkedField",name:"bottom",plural:!1,selections:a,storageKey:null}],storageKey:null}],type:"XDTMediaDict",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisStoriesV3MediaBackgroundStyle",["CometRelay","react-compiler-runtime","usePolarisStoriesV3MediaBackgroundStyle_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var c,e=d("react-compiler-runtime").c(2);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3MediaBackgroundStyle_media.graphql"),a);c=(c=a.reel_media_background)==null?void 0:(c=c.top)==null?void 0:c.background_color;a=(a=a.reel_media_background)==null?void 0:(a=a.bottom)==null?void 0:a.background_color;if(c==null||a==null)return null;c="linear-gradient("+c+", "+a+")";e[0]!==c?(a={background:c},e[0]=c,e[1]=a):a=e[1];return a}g["default"]=a}),98);
__d("usePolarisStoriesV3MediaSize_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3MediaSize_media",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisMediaDimensionsFragment_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3MediaSize",["CometRelay","PolarisGetStoryMediaLayout","react-compiler-runtime","usePolarisIsTallDevice","usePolarisMediaDimensions","usePolarisStoriesV3MediaPlayerSize","usePolarisStoriesV3MediaSize_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var e=d("react-compiler-runtime").c(10);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3MediaSize_media.graphql"),a);var f=c("usePolarisStoriesV3MediaPlayerSize")(),g=f.height;f=f.width;var i;e[0]!==a?(i={mediaQueryReference:a},e[0]=a,e[1]=i):i=e[1];a=c("usePolarisMediaDimensions")(i);i=c("usePolarisIsTallDevice")();var j;e[2]!==a||e[3]!==i||e[4]!==g||e[5]!==f?(j=d("PolarisGetStoryMediaLayout").getStoryMediaLayout(a,i,f,g),e[2]=a,e[3]=i,e[4]=g,e[5]=f,e[6]=j):j=e[6];a=j;i=a[0];g=a[1];e[7]!==i||e[8]!==g?(f={height:i,width:g},e[7]=i,e[8]=g,e[9]=f):f=e[9];return f}g["default"]=a}),98);
__d("usePolarisStoriesV3CreateKeyCommandHandler",["react","react-compiler-runtime","usePolarisStoriesV3KeyCommandsContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(2),b=c("usePolarisStoriesV3KeyCommandsContext")(),e=b.getIsEnabled;a[0]!==e?(b=function(a){return function(){e()&&a()}},a[0]=e,a[1]=b):b=a[1];return b}g["default"]=a}),98);
__d("usePolarisStoriesV3PlayerKeyCommands",["PolarisStoriesV3PauseReason","PolarisVideoConstants","getPolarisKeyCommandConfig","react","react-compiler-runtime","useLayerKeyCommands","usePolarisStoriesV3AudioController","usePolarisStoriesV3CreateKeyCommandHandler","usePolarisStoriesV3PlayerController"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useCallback;b.useMemo;function a(){var a=d("react-compiler-runtime").c(13),b=c("usePolarisStoriesV3CreateKeyCommandHandler")(),e=c("usePolarisStoriesV3PlayerController")(),f=c("usePolarisStoriesV3AudioController")(),g;a[0]!==e?(g=function(){e.canPause()?e.pause(c("PolarisStoriesV3PauseReason").KeyCommand):e.play()},a[0]=e,a[1]=g):g=a[1];g=g;var h;a[2]!==f?(h=function(){var a=f.getAudioState();a===d("PolarisVideoConstants").AUDIO_STATES.on?f.mute():f.unmute()},a[2]=f,a[3]=h):h=a[3];h=h;var i;a[4]!==b||a[5]!==g?(i=d("getPolarisKeyCommandConfig").getPolarisKeyCommandConfig("stories","togglePause",b(g)),a[4]=b,a[5]=g,a[6]=i):i=a[6];a[7]!==b||a[8]!==h?(g=d("getPolarisKeyCommandConfig").getPolarisKeyCommandConfig("stories","toggleSound",b(h)),a[7]=b,a[8]=h,a[9]=g):g=a[9];a[10]!==i||a[11]!==g?(b=[i,g],a[10]=i,a[11]=g,a[12]=b):b=a[12];h=b;i=h;c("useLayerKeyCommands")(i,void 0,!0)}g["default"]=a}),98);
__d("PolarisStoriesV3MediaPlayer.react",["CometRelay","PolarisPostImpressionTrackingNode.react","PolarisSponsoredStoryCaptionUtils","PolarisStoriesV3Caption.react","PolarisStoriesV3InteractionOverlay.react","PolarisStoriesV3Media.react","PolarisStoriesV3MediaFormat","PolarisStoriesV3MediaPlayer_media.graphql","PolarisStoriesV3Stickers.react","gkx","isStringNullOrEmpty","react","react-compiler-runtime","stylex","usePolarisIsSmallScreen","usePolarisStoriesV3MediaBackgroundStyle","usePolarisStoriesV3MediaPlayerSize","usePolarisStoriesV3MediaSize","usePolarisStoriesV3PlayerKeyCommands"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react")),l=j.useRef,m={mediaWrapper:{alignItems:"x6s0dn4",borderStartStartRadius:"x1obq294",borderStartEndRadius:"x5a5i1n",borderEndEndRadius:"xde0f50",borderEndStartRadius:"x15x8krk",display:"x78zum5",flexDirection:"xdt5ytf",height:"x5yr21d",justifyContent:"xl56j7k",overflowX:"x6ikm8r",overflowY:"x10wlt62",position:"x1n2onr6",width:"xh8yej3",$$css:!0},mediaWrapperSmallScreen:{borderStartStartRadius:"xjwep3j",borderStartEndRadius:"x1t39747",borderEndEndRadius:"x1wcsgtt",borderEndStartRadius:"x1pczhz8",overflowX:"x6ikm8r",overflowY:"x10wlt62",$$css:!0},stickers:{bottom:"x1ey2m1c",insetInlineEnd:"xtijo5x",insetInlineStart:"x1o0tod",left:null,right:null,position:"x10l6tqk",top:"x13vifvy",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(43),f=a.adMediaFormat,g=a.currSidecarIndex,j=a.media,n=a.numberOfItems;a=a.showCaption;g=g===void 0?0:g;j=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3MediaPlayer_media.graphql"),j);var o=l(null);c("usePolarisStoriesV3PlayerKeyCommands")();var p=c("usePolarisStoriesV3MediaPlayerSize")(),q=p.height;p=p.width;var r=c("usePolarisIsSmallScreen")(),s=c("usePolarisStoriesV3MediaSize")(j),t=s.height;s=s.width;var u;e[0]!==t||e[1]!==q?(u=d("PolarisSponsoredStoryCaptionUtils").getSponsoredStoryCaptionMediaValues(q,t),e[0]=t,e[1]=q,e[2]=u):u=e[2];u=u;var v=u.captionOffset,w=u.maxCaptionHeight;u=u.mediaOffset;var x=c("usePolarisStoriesV3MediaBackgroundStyle")(j);a=a&&!c("isStringNullOrEmpty")((a=j.caption)==null?void 0:a.text);e[3]!==f||e[4]!==g||e[5]!==j||e[6]!==u||e[7]!==n||e[8]!==a?(o=c("gkx")("8639")&&f===d("PolarisStoriesV3MediaFormat").PolarisStoriesV3MediaFormat.CAROUSEL?k.jsx(c("PolarisPostImpressionTrackingNode.react"),{childrenRef:o,componentType:"carousel",elementId:"carouselMedia",numCarouselCards:n,trackingNode:"MEDIA",children:k.jsx("div",{className:"x5yr21d x1n2onr6 xh8yej3",ref:o,style:{top:a?-u:0},children:k.jsx(c("PolarisStoriesV3Media.react"),{adMediaFormat:f,media:j,sideCarIndex:g})})}):k.jsx("div",{className:"x5yr21d x1n2onr6 xh8yej3",style:{top:a?-u:0},children:k.jsx(c("PolarisStoriesV3Media.react"),{adMediaFormat:f,media:j,sideCarIndex:g})}),e[3]=f,e[4]=g,e[5]=j,e[6]=u,e[7]=n,e[8]=a,e[9]=o):o=e[9];f=o;e[10]===Symbol["for"]("react.memo_cache_sentinel")?(g="x5yr21d x1n2onr6 xh8yej3",e[10]=g):g=e[10];e[11]!==q||e[12]!==p?(u={height:q,width:p},e[11]=q,e[12]=p,e[13]=u):u=e[13];n=r&&m.mediaWrapperSmallScreen;e[14]!==n?(o=(i||(i=c("stylex")))(m.mediaWrapper,n),e[14]=n,e[15]=o):o=e[15];e[16]!==t||e[17]!==s?(q={height:t,width:s},e[16]=t,e[17]=s,e[18]=q):q=e[18];e[19]!==v||e[20]!==w||e[21]!==j||e[22]!==a?(n=a&&k.jsx("div",{className:"x1n2onr6",style:{top:-v},children:k.jsx(c("PolarisStoriesV3Caption.react"),{caption:j.caption,maxCaptionHeight:w})}),e[19]=v,e[20]=w,e[21]=j,e[22]=a,e[23]=n):n=e[23];e[24]!==f||e[25]!==q||e[26]!==n?(t=k.jsxs("div",{style:q,children:[f,n]}),e[24]=f,e[25]=q,e[26]=n,e[27]=t):t=e[27];e[28]!==x||e[29]!==t||e[30]!==o?(s=k.jsx("div",{className:o,style:x,children:t}),e[28]=x,e[29]=t,e[30]=o,e[31]=s):s=e[31];e[32]!==r||e[33]!==j||e[34]!==p?(v=r&&k.jsx(c("PolarisStoriesV3InteractionOverlay.react"),{media:j,playerWidth:p}),e[32]=r,e[33]=j,e[34]=p,e[35]=v):v=e[35];e[36]!==j?(w=k.jsx(c("PolarisStoriesV3Stickers.react"),{media:j,xstyle:m.stickers}),e[36]=j,e[37]=w):w=e[37];e[38]!==s||e[39]!==v||e[40]!==w||e[41]!==u?(a=k.jsxs("div",{className:g,style:u,children:[s,v,w]}),e[38]=s,e[39]=v,e[40]=w,e[41]=u,e[42]=a):a=e[42];return a}g["default"]=a}),98);
__d("PolarisStoriesV3NavigationButtons.react",["fbt","IGDSCircleChevronLeftPanoFilled24Icon.react","IGDSCircleChevronRightPanoFilled24Icon.react","IGDSIconButton.react","Locale","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j=i||d("react");function a(a){var b=d("react-compiler-runtime").c(14),e=a.hasNext,f=a.hasPrev,g=a.onNext,i=a.onPrev;a=e===void 0?!0:e;e=f===void 0?!0:f;b[0]!==g?(f=function(){g("tap_forward")},b[0]=g,b[1]=f):f=b[1];f=f;var k;b[2]!==i?(k=function(){i("tap_back")},b[2]=i,b[3]=k):k=b[3];k=k;var l;b[4]===Symbol["for"]("react.memo_cache_sentinel")?(l={className:"x1ey2m1c xtijo5x x1o0tod xz5rk10 x47corl x10l6tqk x13vifvy x1g2r6go x19991ni x1lizcpb x1o7uuvo"},b[4]=l):l=b[4];var m;b[5]!==k||b[6]!==e?(m=e&&j.jsx("div",babelHelpers["extends"]({},{0:{className:"x1ey2m1c x78zum5 xdt5ytf xl56j7k x67bb7w x10l6tqk x13vifvy x4txaam"},1:{className:"x1ey2m1c x78zum5 xdt5ytf xl56j7k x67bb7w x10l6tqk x13vifvy x4txaam x19jd1h0"}}[!!c("Locale").isRTL()<<0],{children:j.jsx(c("IGDSIconButton.react"),{onClick:k,children:j.jsx(c("IGDSCircleChevronLeftPanoFilled24Icon.react"),{alt:h._(/*BTDS*/"Previous"),color:"ig-stroke-on-media"})})})),b[5]=k,b[6]=e,b[7]=m):m=b[7];b[8]!==f||b[9]!==a?(k=a&&j.jsx("div",babelHelpers["extends"]({},{0:{className:"x1ey2m1c x78zum5 xdt5ytf xl56j7k x67bb7w x10l6tqk x13vifvy x13ts1pm"},1:{className:"x1ey2m1c x78zum5 xdt5ytf xl56j7k x67bb7w x10l6tqk x13vifvy x13ts1pm x19jd1h0"}}[!!c("Locale").isRTL()<<0],{children:j.jsx(c("IGDSIconButton.react"),{onClick:f,children:j.jsx(c("IGDSCircleChevronRightPanoFilled24Icon.react"),{alt:h._(/*BTDS*/"Next"),color:"ig-stroke-on-media"})})})),b[8]=f,b[9]=a,b[10]=k):k=b[10];b[11]!==m||b[12]!==k?(e=j.jsxs("div",babelHelpers["extends"]({},l,{children:[m,k]})),b[11]=m,b[12]=k,b[13]=e):e=b[13];return e}g["default"]=a}),226);
__d("PolarisStoriesV3OverlayType",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum").Mirrored(["None","TapToPlay","SensitivityOverlay","Expired"]);c=a;f["default"]=c}),66);
__d("PolarisStoriesV3Expired.react",["IGDSTextVariants.react","PolarisStoriesStrings","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(){var a=d("react-compiler-runtime").c(1),b;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=i.jsx(d("IGDSTextVariants.react").IGDSTextBody,{color:"textOnMedia",children:d("PolarisStoriesStrings").STORY_EXPIRED_TEXT}),a[0]=b):b=a[0];return b}g["default"]=a}),98);
__d("PolarisStoriesV3PlayerOverlay_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3PlayerOverlay_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3SensitivityOverlay_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3TapToPlayOverlay_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3PlayerOverlay_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3PlayerOverlay_user",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3TapToPlayOverlay_user"}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3BlurryImagePreview_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3BlurryImagePreview_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"preview",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("getPreviewDataFromImagePreviewPayload",["FBLogger","unrecoverableViolation"],(function(a,b,c,d,e,f,g){"use strict";var h="data:image/jpeg;base64,",i=160,j=162,k=42;function l(a,b){b===void 0&&(b=0);a=atob(a);if(a.length<=3)throw c("unrecoverableViolation")("The preview payload was "+a.length+" byte(s) long when it should be 4 or more. The first byte is the version, the second the width, and the third the height. The 4th byte and onward is the image data.","comet_infra");var d=new Uint8Array(a.length+b);for(var e=0;e<a.length;e++)d[b+e]=a.charCodeAt(e);return d}var m;function n(){m==null&&(m=l("/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEABsaGikdKUEmJkFCLy8vQkc/Pj4/R0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0cBHSkpNCY0PygoP0c/NT9HR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR0dHR//AABEIABQAKgMBIgACEQEDEQH/xAGiAAABBQEBAQEBAQAAAAAAAAAAAQIDBAUGBwgJCgsQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+gEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoLEQACAQIEBAMEBwUEBAABAncAAQIDEQQFITEGEkFRB2FxEyIygQgUQpGhscEJIzNS8BVictEKFiQ04SXxFxgZGiYnKCkqNTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqCg4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2dri4+Tl5ufo6ery8/T19vf4+fr/2gAMAwEAAhEDEQA/AA=="));return m}function a(a){var b=n();try{a=l(a,b.length-3);var d=a[b.length-1],e=a[b.length-3],f=a[b.length-2];if(d>k)throw c("unrecoverableViolation")("Tried to decode a payload whose height was "+d+". The maximum "+("is "+k+"."),"comet_infra");if(f>k)throw c("unrecoverableViolation")("Tried to decode a payload whose width was "+f+". The maximum "+("is "+k+"."),"comet_infra");a.set(b,0);a[i]=d;a[j]=f;b=btoa(String.fromCharCode.apply(null,a));return{dataURI:""+h+b,height:d,width:f,version:e}}catch(b){a=c("FBLogger")("images_infra");b instanceof Error&&a.catching(b);a.warn("Failed to decode a preview image payload. Falling back with a blank image.");return null}}g["default"]=a}),98);
__d("PolarisStoriesV3BlurryImagePreview.react",["BaseBlurryImagePreview.react","CometRelay","PolarisStoriesV3BlurryImagePreview_media.graphql","getPreviewDataFromImagePreviewPayload","react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react"),l={root:{bottom:"x1ey2m1c",insetInlineEnd:"xtijo5x",insetInlineStart:"x1o0tod",position:"x10l6tqk",top:"x13vifvy",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(5),f=a.media;a=a.xstyle;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3BlurryImagePreview_media.graphql"),f);if(f.preview==null)return null;var g;e[0]!==f.preview?(g=c("getPreviewDataFromImagePreviewPayload")(f.preview),e[0]=f.preview,e[1]=g):g=e[1];f=g;e[2]!==f||e[3]!==a?(g=f!=null?k.jsx(c("BaseBlurryImagePreview.react"),{className:(i||(i=c("stylex")))(l.root,a),height:"100%",previewData:f,width:"100%"}):null,e[2]=f,e[3]=a,e[4]=g):g=e[4];return g}g["default"]=a}),98);
__d("PolarisStoriesV3SensitivityOverlayBloksApp.react",["JSResourceForInteraction","lazyLoadComponent","react","react-compiler-runtime","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=c("lazyLoadComponent")(c("JSResourceForInteraction")("IGWebBloksApp").__setRef("PolarisStoriesV3SensitivityOverlayBloksApp.react"));function a(a){var b=d("react-compiler-runtime").c(9),e=a.appId,f=a.mediaId;a=a.onClose;var g=c("usePolarisStoriesV3ViewerLoggingContext")();g=g.module;var h;b[0]!==a?(h={onResumeStoryPlayback:a},b[0]=a,b[1]=h):h=b[1];a=String(g);b[2]!==f||b[3]!==a?(g={media_id:f,module:a},b[2]=f,b[3]=a,b[4]=g):g=b[4];b[5]!==e||b[6]!==h||b[7]!==g?(f=i.jsx(j,{appId:e,bridgeOverrides_DEPRECATED:h,params:g}),b[5]=e,b[6]=h,b[7]=g,b[8]=f):f=b[8];return f}g["default"]=a}),98);
__d("PolarisStoriesV3SensitivityOverlayBottomButton.react",["CometPressable.react","IGDSTextVariants.react","isStringNullOrEmpty","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={root:{borderTopColor:"xawl8ia",borderTopStyle:"x13fuv20",borderTopWidth:"x178xt8z",display:"x78zum5",justifyContent:"xl56j7k",paddingTop:"xyamay9",paddingInlineEnd:"xv54qhq",paddingBottom:"x1l90r2v",paddingInlineStart:"xf7dkkf",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(8),e=a.button,f=a.onClick;a=a.xstyle;e=e.text;if(c("isStringNullOrEmpty")(e))return null;var g;b[0]!==a?(g=[j.root,a],b[0]=a,b[1]=g):g=b[1];b[2]!==e?(a=i.jsx(d("IGDSTextVariants.react").IGDSTextBody,{color:"textOnMedia",textAlign:"center",children:e}),b[2]=e,b[3]=a):a=b[3];b[4]!==f||b[5]!==g||b[6]!==a?(e=i.jsx(c("CometPressable.react"),{onPress:f,overlayDisabled:!0,xstyle:g,children:a}),b[4]=f,b[5]=g,b[6]=a,b[7]=e):e=b[7];return e}g["default"]=a}),98);
__d("PolarisStoriesV3SensitivityOverlay_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3SensitivityOverlay_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3BlurryImagePreview_media"},{args:null,kind:"FragmentSpread",name:"usePolarisMediaOverlayMediaCoverInfo_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3SensitivityOverlay.react",["CometRelay","JSResourceForInteraction","PolarisMediaOverlayInfoTypes","PolarisStoriesV3BlurryImagePreview.react","PolarisStoriesV3SensitivityOverlayBloksApp.react","PolarisStoriesV3SensitivityOverlayBottomButton.react","PolarisStoriesV3SensitivityOverlay_media.graphql","PolarisStorySensitivityOverlayMediaOverlayInfoCoverContents.react","react","react-compiler-runtime","useIGDSLazyDialog","usePolarisMediaOverlayMediaCoverInfo","usePolarisStoriesV3PlayerTimer","usePolarisStoriesV3StartPlayerOnVisibleEffect","usePolarisStorySensitivityOverlayLogger"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));e=i;var k=e.useEffect;e.useMemo;var l=e.useRef,m=e.useState,n=c("JSResourceForInteraction")("PolarisStoriesV3SensitivityOverlayConfirmationDialog.react").__setRef("PolarisStoriesV3SensitivityOverlay.react"),o={bottomButton:{bottom:"x1ey2m1c",insetInlineEnd:"xtijo5x",insetInlineStart:"x1o0tod",left:null,right:null,position:"x10l6tqk",$$css:!0},preview:{opacity:"x1ks1olk",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(41),f=a.media,g=a.onDismiss;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3SensitivityOverlay_media.graphql"),f);c("usePolarisStoriesV3PlayerTimer")();var i=l(!1),p=c("usePolarisStorySensitivityOverlayLogger")();f=c("usePolarisMediaOverlayMediaCoverInfo")(a);var q=m(null),r=q[0],s=q[1];q=c("useIGDSLazyDialog")(n);var t=q[0];e[0]!==a.pk||e[1]!==f?(q={containerModule:"unknown",mediaId:a.pk,mediaOverlayCoverInfo:f},e[0]=a.pk,e[1]=f,e[2]=q):q=e[2];q=q;var u=q,v;e[3]!==p||e[4]!==u?(q=function(){i.current||(i.current=!0,p.logImpression(u))},v=[p,u,i],e[3]=p,e[4]=u,e[5]=q,e[6]=v):(q=e[5],v=e[6]);k(q,v);c("usePolarisStoriesV3PlayerTimer")();q=c("usePolarisStoriesV3StartPlayerOnVisibleEffect")();if(f==null)return null;var w=f.bottomButton;e[7]!==p||e[8]!==u||e[9]!==g||e[10]!==t?(v=function(a){var b=function(){p.logConfirmationDialogAccept(babelHelpers["extends"]({},u,{button:a})),g==null?void 0:g()},c=function(){p.logConfirmationDialogDismiss(babelHelpers["extends"]({},u,{button:a}))};t({onCancel:c,onConfirm:b})},e[7]=p,e[8]=u,e[9]=g,e[10]=t,e[11]=v):v=e[11];var x=v;e[12]===Symbol["for"]("react.memo_cache_sentinel")?(v=function(a){s(a.action_url)},e[12]=v):v=e[12];var y=v;e[13]===Symbol["for"]("react.memo_cache_sentinel")?(v=function(){s(null)},e[13]=v):v=e[13];v=v;var z;e[14]!==x||e[15]!==p||e[16]!==u?(z=function(a){bb19:switch(a.action){case d("PolarisMediaOverlayInfoTypes").MEDIA_OVERLAY_BUTTON_ACTIONS.CLEAR_MEDIA_COVER:x(a);break bb19;case d("PolarisMediaOverlayInfoTypes").MEDIA_OVERLAY_BUTTON_ACTIONS.OPEN_BLOKS_APP:y(a);break bb19;default:return}p.logButtonClick(babelHelpers["extends"]({},u,{button:a}))},e[14]=x,e[15]=p,e[16]=u,e[17]=z):z=e[17];var A=z;e[18]!==a?(z=j.jsx(c("PolarisStoriesV3BlurryImagePreview.react"),{media:a,xstyle:o.preview}),e[18]=a,e[19]=z):z=e[19];var B;e[20]===Symbol["for"]("react.memo_cache_sentinel")?(B={className:"x6s0dn4 x78zum5 xdt5ytf"},e[20]=B):B=e[20];var C;e[21]!==A?(C=function(a,b){return A(b)},e[21]=A,e[22]=C):C=e[22];var D;e[23]!==a.pk||e[24]!==f||e[25]!==C?(D=j.jsx(c("PolarisStorySensitivityOverlayMediaOverlayInfoCoverContents.react"),{mediaId:a.pk,mediaOverlayCoverInfo:f,onMediaOverlayInfoButtonClick:C}),e[23]=a.pk,e[24]=f,e[25]=C,e[26]=D):D=e[26];e[27]!==w||e[28]!==A?(f=w&&j.jsx(c("PolarisStoriesV3SensitivityOverlayBottomButton.react"),{button:w,onClick:function(){return A(w)},xstyle:o.bottomButton}),e[27]=w,e[28]=A,e[29]=f):f=e[29];e[30]!==r||e[31]!==a.pk?(C=r!=null&&j.jsx(c("PolarisStoriesV3SensitivityOverlayBloksApp.react"),{appId:r,mediaId:a.pk,onClose:v}),e[30]=r,e[31]=a.pk,e[32]=C):C=e[32];e[33]!==q||e[34]!==D||e[35]!==f||e[36]!==C?(v=j.jsxs("div",babelHelpers["extends"]({},B,{ref:q,children:[D,f,C]})),e[33]=q,e[34]=D,e[35]=f,e[36]=C,e[37]=v):v=e[37];e[38]!==v||e[39]!==z?(r=j.jsxs(j.Fragment,{children:[z,v]}),e[38]=v,e[39]=z,e[40]=r):r=e[40];return r}g["default"]=a}),98);
__d("PolarisStoriesV3TapToPlayOverlay_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3TapToPlayOverlay_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3BlurryImagePreview_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3TapToPlayOverlay_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3TapToPlayOverlay_user",selections:[{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3TapToPlayOverlay.react",["CometRelay","PolarisStoriesV3BlurryImagePreview.react","PolarisStoriesV3TapToPlayOverlay_media.graphql","PolarisStoriesV3TapToPlayOverlay_user.graphql","PolarisStoryTapToPlay.react","react","react-compiler-runtime","usePolarisStoryTapToPlayOverlayLogger"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react")),l=j.useEffect;function a(a){var e=d("react-compiler-runtime").c(14),f=a.media,g=a.onTapToPlay;a=a.user;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3TapToPlayOverlay_user.graphql"),a);f=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3TapToPlayOverlay_media.graphql"),f);var j=c("usePolarisStoryTapToPlayOverlayLogger")(),m,n;e[0]!==j?(m=function(){j.logImpression()},n=[j],e[0]=j,e[1]=m,e[2]=n):(m=e[1],n=e[2]);l(m,n);e[3]!==j||e[4]!==g?(m=function(){j.logTapToPlay(),g==null?void 0:g()},e[3]=j,e[4]=g,e[5]=m):m=e[5];n=m;e[6]!==f?(m=k.jsx(c("PolarisStoriesV3BlurryImagePreview.react"),{media:f}),e[6]=f,e[7]=m):m=e[7];e[8]!==n||e[9]!==a.username?(f=k.jsx(c("PolarisStoryTapToPlay.react"),{onTapToPlay:n,username:a.username}),e[8]=n,e[9]=a.username,e[10]=f):f=e[10];e[11]!==m||e[12]!==f?(n=k.jsxs(k.Fragment,{children:[m,f]}),e[11]=m,e[12]=f,e[13]=n):n=e[13];return n}g["default"]=a}),98);
__d("PolarisStoriesV3PlayerOverlay.react",["CometRelay","PolarisStoriesV3Expired.react","PolarisStoriesV3OverlayType","PolarisStoriesV3PlayerOverlay_media.graphql","PolarisStoriesV3PlayerOverlay_user.graphql","PolarisStoriesV3SensitivityOverlay.react","PolarisStoriesV3TapToPlayOverlay.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a){var e=d("react-compiler-runtime").c(10),f=a.dismissOverlay,g=a.media,j=a.overlay;a=a.user;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3PlayerOverlay_media.graphql"),g);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3PlayerOverlay_user.graphql"),a);switch(j){case c("PolarisStoriesV3OverlayType").TapToPlay:e[0]===Symbol["for"]("react.memo_cache_sentinel")?(j={className:"x78zum5 xdt5ytf x5yr21d xl56j7k xr1yuqi x11t971q x4ii5y1 xvc5jky x2b8uid xg0jo4d"},e[0]=j):j=e[0];e[1]!==f||e[2]!==g||e[3]!==a?(j=k.jsx("div",babelHelpers["extends"]({},j,{children:k.jsx(c("PolarisStoriesV3TapToPlayOverlay.react"),{media:g,onTapToPlay:f,user:a})})),e[1]=f,e[2]=g,e[3]=a,e[4]=j):j=e[4];return j;case c("PolarisStoriesV3OverlayType").Expired:e[5]===Symbol["for"]("react.memo_cache_sentinel")?(a=k.jsx("div",babelHelpers["extends"]({className:"x78zum5 xdt5ytf x5yr21d xl56j7k xr1yuqi x11t971q x4ii5y1 xvc5jky x2b8uid xh8yej3"},{children:k.jsx(c("PolarisStoriesV3Expired.react"),{})})),e[5]=a):a=e[5];return a;case c("PolarisStoriesV3OverlayType").SensitivityOverlay:e[6]===Symbol["for"]("react.memo_cache_sentinel")?(j={className:"x78zum5 xdt5ytf x5yr21d xl56j7k xr1yuqi x11t971q x4ii5y1 xvc5jky x2b8uid xg0jo4d"},e[6]=j):j=e[6];e[7]!==f||e[8]!==g?(a=k.jsx("div",babelHelpers["extends"]({},j,{children:k.jsx(c("PolarisStoriesV3SensitivityOverlay.react"),{media:g,onDismiss:f})})),e[7]=f,e[8]=g,e[9]=a):a=e[9];return a;case c("PolarisStoriesV3OverlayType").None:return null}}g["default"]=a}),98);
__d("PolarisStoriesV3Player_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3Player_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MediaOverlayInfoFooterCTA_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MediaPlayer_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ProgressContextProvider_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3PlayerOverlay_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MediaType_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3OverlayType_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3Player_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3Player_user",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3PlayerOverlay_user"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3OverlayType_user"},{alias:null,args:null,kind:"ScalarField",name:"user_id",storageKey:null}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3Player_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3Player_viewer",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3OverlayType_viewer"},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"user_id",storageKey:null}],storageKey:null}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ProgressContextProvider_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ProgressContextProvider_media",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3Duration_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3Duration_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3Duration_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"video_duration",storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MediaType_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3Duration",["CometRelay","PolarisStoriesV3MediaType","PolarisStoriesV3OverlayType","PolarisStoryConstants","usePolarisStoriesV3Duration_media.graphql","usePolarisStoriesV3MediaType"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,e){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3Duration_media.graphql"),a);var f=c("usePolarisStoriesV3MediaType")(a);return f===c("PolarisStoriesV3MediaType").Image||e===c("PolarisStoriesV3OverlayType").SensitivityOverlay?d("PolarisStoryConstants").STORY_IMAGE_DURATION_S:(f=a.video_duration)!=null?f:0}g["default"]=a}),98);
__d("PolarisStoriesV3ProgressContextProvider.react",["CometRelay","PolarisStoriesV3ProgressContext","PolarisStoriesV3ProgressContextProvider_media.graphql","react","react-compiler-runtime","usePolarisStoriesV3Duration"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));e=i;e.useMemo;var k=e.useRef;function a(a){var e=d("react-compiler-runtime").c(11),f=a.children,g=a.media;a=a.overlay;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ProgressContextProvider_media.graphql"),g);var i=c("usePolarisStoriesV3Duration")(g,a),l=k(0);e[0]!==i?(g=function(){return i},e[0]=i,e[1]=g):g=e[1];e[2]===Symbol["for"]("react.memo_cache_sentinel")?(a=function(){return l.current},e[2]=a):a=e[2];var m;e[3]!==i?(m=function(a){l.current=Math.min(a,i)},e[3]=i,e[4]=m):m=e[4];e[5]!==g||e[6]!==m?(a={getDuration:g,getProgress:a,setProgress:m},e[5]=g,e[6]=m,e[7]=a):a=e[7];g=a;m=g;e[8]!==f||e[9]!==m?(a=j.jsx(c("PolarisStoriesV3ProgressContext").Provider,{value:m,children:f}),e[8]=f,e[9]=m,e[10]=a):a=e[10];return a}g["default"]=a}),98);
__d("usePolarisStoriesV3IsExpired_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3IsExpired_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"expiring_at",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3IsExpired",["CometRelay","usePolarisStoriesV3IsExpired_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3IsExpired_media.graphql"),a);return a.expiring_at!=null&&Number(a.expiring_at)<Date.now()/1e3}g["default"]=a}),98);
__d("usePolarisStoriesV3OverlayType_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3OverlayType_media",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3IsExpired_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3SensitivityOverlayCoverInfo_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3TapToPlay_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3OverlayType_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3OverlayType_user",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3TapToPlay_user"}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3OverlayType_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3OverlayType_viewer",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3TapToPlay_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3SensitivityOverlayCoverInfo_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3SensitivityOverlayCoverInfo_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"usePolarisMediaOverlayMediaCoverInfo_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3SensitivityOverlayCoverInfo",["CometRelay","react","react-compiler-runtime","usePolarisMediaOverlayMediaCoverInfo","usePolarisStoriesV3SensitivityOverlayCoverInfo_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;e=i||d("react");e.useCallback;e.useMemo;var j=e.useState;function a(a){var e=d("react-compiler-runtime").c(6),f=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3SensitivityOverlayCoverInfo_media.graphql"),a);e[0]===Symbol["for"]("react.memo_cache_sentinel")?(a=new Set(),e[0]=a):a=e[0];a=j(a);var g=a[0],i=a[1];a=g.has(f.pk);g=c("usePolarisMediaOverlayMediaCoverInfo")(f);a=a?null:g;e[1]!==f.pk?(g=function(){i(function(a){return new Set([].concat(Array.from(a),[f.pk]))})},e[1]=f.pk,e[2]=g):g=e[2];g=g;var k;e[3]!==g||e[4]!==a?(k=[a,g],e[3]=g,e[4]=a,e[5]=k):k=e[5];g=k;return g}g["default"]=a}),98);
__d("usePolarisStoriesV3TapToPlay_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3TapToPlay_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3TapToPlay_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3TapToPlay_user",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_user"}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3TapToPlay_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3TapToPlay_viewer",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3TapToPlay",["CometRelay","react","usePolarisStoriesV3TapToPlay_media.graphql","usePolarisStoriesV3TapToPlay_user.graphql","usePolarisStoriesV3TapToPlay_viewer.graphql","usePolarisStoriesV3ViewingOwnStory","useRouteReferrer","useStable"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k;e=k||d("react");var l=e.useMemo,m=e.useState,n=!1;function a(a,e,f){var g=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3TapToPlay_media.graphql"),a);a=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3TapToPlay_user.graphql"),e);e=d("CometRelay").useFragment(j!==void 0?j:j=b("usePolarisStoriesV3TapToPlay_viewer.graphql"),f);f=m(n);var k=f[0],o=f[1],p=c("useRouteReferrer")();f=c("useStable")(function(){var a=(p==null?void 0:p.navigationType)==="initial"||(p==null?void 0:p.navigationType)==="reload";return a?g.pk:null});a=c("usePolarisStoriesV3ViewingOwnStory")(a,e);var q=!a&&f===g.pk&&!k;return l(function(){return[q,function(){o(!0),n=!0}]},[q,o])}g["default"]=a}),98);
__d("usePolarisStoriesV3OverlayType",["CometRelay","PolarisStoriesV3OverlayType","react","react-compiler-runtime","usePolarisStoriesV3IsExpired","usePolarisStoriesV3OverlayType_media.graphql","usePolarisStoriesV3OverlayType_user.graphql","usePolarisStoriesV3OverlayType_viewer.graphql","usePolarisStoriesV3SensitivityOverlayCoverInfo","usePolarisStoriesV3TapToPlay"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k;(k||d("react")).useMemo;function a(a,e,f){var g=d("react-compiler-runtime").c(6);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3OverlayType_media.graphql"),a);e=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3OverlayType_user.graphql"),e);f=d("CometRelay").useFragment(j!==void 0?j:j=b("usePolarisStoriesV3OverlayType_viewer.graphql"),f);e=c("usePolarisStoriesV3TapToPlay")(a,e,f);f=e[0];e=e[1];var k=c("usePolarisStoriesV3SensitivityOverlayCoverInfo")(a),l=k[0];k=k[1];l=l!=null;a=c("usePolarisStoriesV3IsExpired")(a);if(f){g[0]!==e?(f=[c("PolarisStoriesV3OverlayType").TapToPlay,e],g[0]=e,g[1]=f):f=g[1];e=f}else if(l){g[2]!==k?(f=[c("PolarisStoriesV3OverlayType").SensitivityOverlay,k],g[2]=k,g[3]=f):f=g[3];e=f}else if(a){g[4]===Symbol["for"]("react.memo_cache_sentinel")?(l=[c("PolarisStoriesV3OverlayType").Expired,null],g[4]=l):l=g[4];e=l}else{g[5]===Symbol["for"]("react.memo_cache_sentinel")?(k=[c("PolarisStoriesV3OverlayType").None,null],g[5]=k):k=g[5];e=k}return e}g["default"]=a}),98);
__d("PolarisStoriesV3PauseReasonContext",["PolarisStoriesV3PauseReason","react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext(c("PolarisStoriesV3PauseReason").None);g["default"]=b}),98);
__d("usePolarisStoriesV3PauseReason",["PolarisStoriesV3PauseReasonContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3PauseReasonContext"))}g["default"]=a}),98);
__d("PolarisStoriesV3Player.react",["CometRelay","CometTransientDialogProvider.react","PolarisStoriesV3MediaOverlayInfoFooterCTA.react","PolarisStoriesV3MediaPlayer.react","PolarisStoriesV3NavigationButtons.react","PolarisStoriesV3OverlayType","PolarisStoriesV3PauseReason","PolarisStoriesV3PlayerOverlay.react","PolarisStoriesV3Player_media.graphql","PolarisStoriesV3Player_user.graphql","PolarisStoriesV3Player_viewer.graphql","PolarisStoriesV3PlayingState","PolarisStoriesV3ProgressContextProvider.react","PolarisTrackingNodeProvider.react","PolarisUA","emptyFunction","gkx","react","usePolarisIsSmallScreen","usePolarisStoriesV3MediaType","usePolarisStoriesV3NavigationContext","usePolarisStoriesV3OverlayType","usePolarisStoriesV3PauseReason","usePolarisStoriesV3PlayerControllerSubscription","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||(k=d("react"));e=k;var m=e.useCallback,n=e.useMemo;function a(a){var e=a.adMediaFormat,f=a.currSideCarIndex,g=f===void 0?0:f,k=a.footer,o=a.header;f=a.media;var p=a.numberOfItems,q=p===void 0?0:p,r=a.onPlayerStart;p=a.ref;var s=a.renderMediaPlayer,t=s===void 0?c("emptyFunction").thatReturnsArgument:s;s=a.showCaption;var u=s===void 0?!1:s;s=a.user;a=a.viewer;var v=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3Player_media.graphql"),f),w=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3Player_user.graphql"),s);f=d("CometRelay").useFragment(j!==void 0?j:j=b("PolarisStoriesV3Player_viewer.graphql"),a);var x=c("usePolarisStoriesV3NavigationContext")();s=c("usePolarisStoriesV3ViewerLoggingContext")();var y=s.loggingSession,z=c("usePolarisStoriesV3MediaType")(v),A=m(function(){y.startMedia(v.pk,z),r==null?void 0:r()},[y,v.pk,z,r]),B=m(function(){x.isNavigating()||x.next("automatic_forward")},[x]),C=m(function(a){a=a.playingState;switch(a){case c("PolarisStoriesV3PlayingState").Playing:y.play();break;case c("PolarisStoriesV3PlayingState").Paused:case c("PolarisStoriesV3PlayingState").Stalling:y.pause();break;default:break}},[y]);a=n(function(){return{onChange:C,onEnd:B,onStart:A}},[C,B,A]);c("usePolarisStoriesV3PlayerControllerSubscription")(a);s=c("usePolarisStoriesV3OverlayType")(v,w,f);var D=s[0],E=s[1],F=c("usePolarisIsSmallScreen")();a=D===c("PolarisStoriesV3OverlayType").TapToPlay||F;s=c("usePolarisStoriesV3PauseReason")();var G=s===c("PolarisStoriesV3PauseReason").LongPress,H=(f==null?void 0:(s=f.user)==null?void 0:s.user_id)===w.user_id;return l.jsxs(l.Fragment,{children:[l.jsx(c("PolarisTrackingNodeProvider.react"),{ref:p,trackingNode:321,children:function(a){return l.jsx("div",babelHelpers["extends"]({},{0:{className:"xyzq4qe x5a5i1n x1obq294 x5yr21d x6ikm8r x10wlt62 x1n2onr6 x87ps6o xh8yej3 x1ja2u2z"},2:{className:"xyzq4qe x5yr21d x6ikm8r x10wlt62 x1n2onr6 x87ps6o xh8yej3 x1ja2u2z x1t39747 xjwep3j"},1:{className:"xyzq4qe x5a5i1n x1obq294 x5yr21d x6ikm8r x10wlt62 x1n2onr6 x87ps6o xh8yej3 x1ja2u2z xpdipgo x1winvzj"},3:{className:"xyzq4qe x5yr21d x6ikm8r x10wlt62 x1n2onr6 x87ps6o xh8yej3 x1ja2u2z x1t39747 xjwep3j xpdipgo x1winvzj"}}[!!F<<1|!!d("PolarisUA").isMobileSafari()<<0],{ref:a,children:l.jsx(c("PolarisTrackingNodeProvider.react"),{trackingNode:15,children:function(a){return l.jsx("div",babelHelpers["extends"]({className:"x5yr21d"},{ref:a,children:l.jsx(c("PolarisStoriesV3ProgressContextProvider.react"),{media:v,overlay:D,children:l.jsxs(c("CometTransientDialogProvider.react"),{children:[l.jsx("div",babelHelpers["extends"]({},{0:{className:"xoqlrxr xtijo5x x1o0tod x6ikm8r x10wlt62 x1cnzs8 xv54qhq x1gan7if xf7dkkf x10l6tqk x13vifvy x1vjfegm x1hc1fzr x1d8287x x19991ni xwji4o3"},2:{className:"xoqlrxr xtijo5x x1o0tod x6ikm8r x10wlt62 x10l6tqk x13vifvy x1vjfegm x1y1aw1k xf159sx x18d9i69 xmzvs34 x1hc1fzr x1d8287x x19991ni xwji4o3"},1:{className:"xoqlrxr xtijo5x x1o0tod x6ikm8r x10wlt62 x1cnzs8 xv54qhq x1gan7if xf7dkkf x10l6tqk x13vifvy x1vjfegm x1d8287x x19991ni xwji4o3 xg01cxk"},3:{className:"xoqlrxr xtijo5x x1o0tod x6ikm8r x10wlt62 x10l6tqk x13vifvy x1vjfegm x1y1aw1k xf159sx x18d9i69 xmzvs34 x1d8287x x19991ni xwji4o3 xg01cxk"}}[!!F<<1|!!G<<0],{children:o})),l.jsx(c("PolarisStoriesV3PlayerOverlay.react"),{dismissOverlay:E,media:v,overlay:D,user:w}),D===c("PolarisStoriesV3OverlayType").None&&l.jsxs(l.Fragment,{children:[t(l.jsx(c("PolarisStoriesV3MediaPlayer.react"),{adMediaFormat:e,currSidecarIndex:g,media:v,numberOfItems:q,showCaption:u})),l.jsx("div",babelHelpers["extends"]({},{0:{className:"x5yr21d x1hc1fzr x1d8287x x19991ni xwji4o3"},1:{className:"x5yr21d x1d8287x x19991ni xwji4o3 xg01cxk"}}[!!G<<0],{children:c("gkx")("5268")?l.jsxs(l.Fragment,{children:[l.jsx(c("PolarisStoriesV3MediaOverlayInfoFooterCTA.react"),{media:v,viewerIsOwner:H}),k]}):l.jsxs(l.Fragment,{children:[k,l.jsx(c("PolarisStoriesV3MediaOverlayInfoFooterCTA.react"),{media:v})]})}))]})]})})}))}})}))}}),!a&&l.jsx(c("PolarisStoriesV3NavigationButtons.react"),{hasNext:x.hasNext,hasPrev:x.hasPrev,onNext:x.next,onPrev:x.prev})]})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("PolarisStoriesV3AudioController",["PolarisVideoConstants","SubscriptionList"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){a===void 0&&(a=d("PolarisVideoConstants").AUDIO_STATES.none),b===void 0&&(b={}),this.$1=a,this.$2=b,this.$3=new(c("SubscriptionList"))()}var b=a.prototype;b.mute=function(){this.$1===d("PolarisVideoConstants").AUDIO_STATES.on&&(this.$1=d("PolarisVideoConstants").AUDIO_STATES.off,this.$4())};b.unmute=function(){this.$1===d("PolarisVideoConstants").AUDIO_STATES.off&&(this.$1=d("PolarisVideoConstants").AUDIO_STATES.on,this.$4())};b.getAudioState=function(){return this.$1};b.subscribe=function(a){return this.$3.add(a)};b.setHandlers=function(a){this.$2=a};b.$4=function(){this.$2.onChange==null?void 0:this.$2.onChange(this.$1),this.$3.fireCallbacks()};return a}();g["default"]=a}),98);
__d("PolarisStoriesV3AudioControllerContextProvider_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AudioControllerContextProvider_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"has_audio",storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MutingInfo_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AudioControllerContextProvider.react",["CometRelay","PolarisStoriesV3AudioController","PolarisStoriesV3AudioControllerContext","PolarisStoriesV3AudioControllerContextProvider_media.graphql","PolarisStoriesV3AudioStateContext","PolarisVideoConstants","react","react-compiler-runtime","usePolarisStoriesV3MutingInfo","useStable"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useState,l=d("PolarisVideoConstants").AUDIO_STATES.off;function a(a){var e=d("react-compiler-runtime").c(8),f=a.children;a=a.media;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AudioControllerContextProvider_media.graphql"),a);var g=c("usePolarisStoriesV3MutingInfo")(a);a=k(a.has_audio===!0&&(g==null?void 0:g.shouldMuteAudio)!==!0?l:d("PolarisVideoConstants").AUDIO_STATES.none);var i=a[0],m=a[1];e[0]!==i?(g=function(){return new(c("PolarisStoriesV3AudioController"))(i,{onChange:function(a){l=a,m(a)}})},e[0]=i,e[1]=g):g=e[1];a=c("useStable")(g);e[2]!==i||e[3]!==f?(g=j.jsx(c("PolarisStoriesV3AudioStateContext").Provider,{value:i,children:f}),e[2]=i,e[3]=f,e[4]=g):g=e[4];e[5]!==a||e[6]!==g?(f=j.jsx(c("PolarisStoriesV3AudioControllerContext").Provider,{value:a,children:g}),e[5]=a,e[6]=g,e[7]=f):f=e[7];return f}g["default"]=a}),98);
__d("PolarisStoriesV3PlayerController",["PolarisStoriesV3PauseReason","PolarisStoriesV3PlayerControllerEventType","PolarisStoriesV3PlayingState","SubscriptionList","shallowEqual"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(){this.$1={pauseReason:c("PolarisStoriesV3PauseReason").None,playingState:c("PolarisStoriesV3PlayingState").NotStarted},this.$2=this.$1,this.$3=new(c("SubscriptionList"))()}var b=a.prototype;b.start=function(){this.$1.playingState===c("PolarisStoriesV3PlayingState").NotStarted&&this.$4(c("PolarisStoriesV3PlayerControllerEventType").Start,{pauseReason:c("PolarisStoriesV3PauseReason").None,playingState:c("PolarisStoriesV3PlayingState").Playing})};b.play=function(){(this.$1.playingState===c("PolarisStoriesV3PlayingState").Paused||this.$1.playingState===c("PolarisStoriesV3PlayingState").Stalling)&&this.$4(c("PolarisStoriesV3PlayerControllerEventType").Change,{pauseReason:c("PolarisStoriesV3PauseReason").None,playingState:c("PolarisStoriesV3PlayingState").Playing})};b.stalling=function(){this.$1.playingState===c("PolarisStoriesV3PlayingState").Playing&&this.$4(c("PolarisStoriesV3PlayerControllerEventType").Change,{pauseReason:c("PolarisStoriesV3PauseReason").None,playingState:c("PolarisStoriesV3PlayingState").Stalling})};b.pause=function(a){(this.canPause()||this.$1.playingState===c("PolarisStoriesV3PlayingState").Paused&&this.$1.pauseReason!==a)&&this.$4(c("PolarisStoriesV3PlayerControllerEventType").Change,{pauseReason:a,playingState:c("PolarisStoriesV3PlayingState").Paused})};b.end=function(){this.$1.playingState!==c("PolarisStoriesV3PlayingState").Ended&&this.$4(c("PolarisStoriesV3PlayerControllerEventType").End,{pauseReason:c("PolarisStoriesV3PauseReason").None,playingState:c("PolarisStoriesV3PlayingState").Ended})};b.getPlayingState=function(){return this.$1.playingState};b.getPauseReason=function(){return this.$1.pauseReason};b.getState=function(){return this.$1};b.canPlay=function(){var a=this.$1.playingState;return a===c("PolarisStoriesV3PlayingState").Paused||a===c("PolarisStoriesV3PlayingState").Stalling};b.canPause=function(){var a=this.$1.playingState;return a===c("PolarisStoriesV3PlayingState").Playing||a===c("PolarisStoriesV3PlayingState").Stalling};b.subscribe=function(a){return this.$3.add(a)};b.$4=function(a,b){c("shallowEqual")(this.$1,b)||(this.$2=this.$1,this.$1=b,this.$5(a))};b.$5=function(a){this.$3.fireCallbacks({prevState:this.$2,state:this.$1,type:a})};return a}();g["default"]=a}),98);
__d("PolarisStoriesV3PlayerControllerContextProvider_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3PlayerControllerContextProvider_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AudioControllerContextProvider_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3PlayerControllerContextProvider.react",["CometRelay","PolarisStoriesV3AudioControllerContextProvider.react","PolarisStoriesV3PauseReasonContext","PolarisStoriesV3PlayerController","PolarisStoriesV3PlayerControllerContext","PolarisStoriesV3PlayerControllerContextProvider_media.graphql","PolarisStoriesV3PlayingStateContext","react","react-compiler-runtime","useStable"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));e=i;var k=e.useEffect,l=e.useState;function a(a){var e=d("react-compiler-runtime").c(19),f=a.children;a=a.media;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3PlayerControllerContextProvider_media.graphql"),a);var g=c("useStable")(m),i;e[0]!==g?(i=g.getPlayingState(),e[0]=g,e[1]=i):i=e[1];i=l(i);var n=i[0],o=i[1];e[2]!==g?(i=g.getPauseReason(),e[2]=g,e[3]=i):i=e[3];i=l(i);var p=i[0],q=i[1],r;e[4]!==g?(i=function(){o(g.getPlayingState());q(g.getPauseReason());var a=g.subscribe(function(){o(g.getPlayingState()),q(g.getPauseReason())});return function(){return a.remove()}},r=[g],e[4]=g,e[5]=i,e[6]=r):(i=e[5],r=e[6]);k(i,r);e[7]!==f||e[8]!==a?(i=j.jsx(c("PolarisStoriesV3AudioControllerContextProvider.react"),{media:a,children:f}),e[7]=f,e[8]=a,e[9]=i):i=e[9];e[10]!==p||e[11]!==i?(r=j.jsx(c("PolarisStoriesV3PauseReasonContext").Provider,{value:p,children:i}),e[10]=p,e[11]=i,e[12]=r):r=e[12];e[13]!==n||e[14]!==r?(f=j.jsx(c("PolarisStoriesV3PlayingStateContext").Provider,{value:n,children:r}),e[13]=n,e[14]=r,e[15]=f):f=e[15];e[16]!==g||e[17]!==f?(a=j.jsx(c("PolarisStoriesV3PlayerControllerContext").Provider,{value:g,children:f}),e[16]=g,e[17]=f,e[18]=a):a=e[18];return a}function m(){return new(c("PolarisStoriesV3PlayerController"))()}g["default"]=a}),98);
__d("usePolarisStoriesV3MarkAdSeen_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3MarkAdSeen_ad",selections:[{alias:null,args:null,kind:"ScalarField",name:"ad_id",storageKey:null}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3MarkAdSeen_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3MarkAdSeen_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MarkSeen_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3MarkSeen_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"};return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3MarkSeen_media",selections:[a,{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[a],storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null}}();e.exports=a}),null);
__d("PolarisStoriesV3SeenMutation_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="24372833149008516"}),null);
__d("PolarisStoriesV3SeenMutation.graphql",["PolarisStoriesV3SeenMutation_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"reelId"},{defaultValue:null,kind:"LocalArgument",name:"reelMediaId"},{defaultValue:null,kind:"LocalArgument",name:"reelMediaOwnerId"},{defaultValue:null,kind:"LocalArgument",name:"reelMediaTakenAt"},{defaultValue:null,kind:"LocalArgument",name:"viewSeenAt"}],c=[{alias:null,args:[{fields:[{kind:"Variable",name:"reelId",variableName:"reelId"},{kind:"Variable",name:"reelMediaId",variableName:"reelMediaId"},{kind:"Variable",name:"reelMediaOwnerId",variableName:"reelMediaOwnerId"},{kind:"Variable",name:"reelMediaTakenAt",variableName:"reelMediaTakenAt"},{kind:"Variable",name:"viewSeenAt",variableName:"viewSeenAt"}],kind:"ObjectValue",name:"_request_data"}],concreteType:"XDTEmptyRecord",kind:"LinkedField",name:"xdt_api__v1__stories__reel__seen",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"PolarisStoriesV3SeenMutation",selections:c,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"PolarisStoriesV3SeenMutation",selections:c},params:{id:b("PolarisStoriesV3SeenMutation_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__stories__reel__seen"]},name:"PolarisStoriesV3SeenMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("PolarisStoriesV3SeenMutation",["PolarisStoriesV3SeenMutation.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h!==void 0?h:h=b("PolarisStoriesV3SeenMutation.graphql");c=a;g["default"]=c}),98);
__d("usePolarisStoriesV3SeenMutation",["CometRelay","FBLogger","PolarisStoriesV3SeenMutation","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(2),b=d("CometRelay").useMutation(c("PolarisStoriesV3SeenMutation")),e=b[0];a[0]!==e?(b=function(a){var b=a.mediaId,c=a.mediaTakenAt,d=a.reelId;a=a.userId;e({onError:i,variables:{reelId:d,reelMediaId:b,reelMediaOwnerId:a,reelMediaTakenAt:c,viewSeenAt:Math.floor(Date.now()/1e3)}})},a[0]=e,a[1]=b):b=a[1];return b}function i(a){c("FBLogger")("ig_web").catching(a).warn("Error marking story as seen")}g["default"]=a}),98);
__d("PolarisStoriesV3SeenStateContext",["emptyFunction","react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({getLastSeenMediaId:c("emptyFunction"),isReelMediaSeen:c("emptyFunction").thatReturnsFalse,isReelSeen:c("emptyFunction").thatReturnsFalse,updateSeen:c("emptyFunction")});g["default"]=b}),98);
__d("usePolarisStoriesV3SeenStateContext",["PolarisStoriesV3SeenStateContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3SeenStateContext"))}g["default"]=a}),98);
__d("usePolarisStoriesV3MarkSeen",["CometRelay","react","usePolarisStoriesV3MarkSeen_media.graphql","usePolarisStoriesV3SeenMutation","usePolarisStoriesV3SeenStateContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(i||d("react")).useCallback;function a(a){var e=a.media,f=a.onAfterMarkSeen,g=a.onMarkSeen,i=a.reelId,k=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3MarkSeen_media.graphql"),e),l=c("usePolarisStoriesV3SeenMutation")(),m=c("usePolarisStoriesV3SeenStateContext")();return j(function(){m.isReelMediaSeen(i,k.pk)||(l({mediaId:k.pk,mediaTakenAt:k.taken_at,reelId:i,userId:k.user.pk}),g==null?void 0:g()),f==null?void 0:f()},[l,k.pk,k.taken_at,k.user.pk,f,g,i,m])}g["default"]=a}),98);
__d("usePolarisStoriesV3MarkAdSeen",["CometRelay","react-compiler-runtime","usePolarisStoriesV3AdsPoolContext","usePolarisStoriesV3MarkAdSeen_ad.graphql","usePolarisStoriesV3MarkAdSeen_media.graphql","usePolarisStoriesV3MarkSeen","usePolarisStoriesV3SeenStateContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,e){var f=d("react-compiler-runtime").c(9),g=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3MarkAdSeen_ad.graphql"),a),j=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3MarkAdSeen_media.graphql"),e),k=c("usePolarisStoriesV3AdsPoolContext")(),l=c("usePolarisStoriesV3SeenStateContext")();f[0]!==g.ad_id||f[1]!==k||f[2]!==j||f[3]!==l?(a=function(){l.updateSeen({isReelFullySeen:!0,mediaId:j.pk,mediaTakenAt:Number(j.taken_at),reelId:g.ad_id}),k.setAdSeen(g.ad_id)},f[0]=g.ad_id,f[1]=k,f[2]=j,f[3]=l,f[4]=a):a=f[4];e=a;f[5]!==g.ad_id||f[6]!==e||f[7]!==j?(a={media:j,onAfterMarkSeen:e,reelId:g.ad_id},f[5]=g.ad_id,f[6]=e,f[7]=j,f[8]=a):a=f[8];return c("usePolarisStoriesV3MarkSeen")(a)}g["default"]=a}),98);
__d("PolarisStoriesV3AdGalleryPlayer.react",["CometRelay","FBLogger","PolarisAdsGatingHelpers","PolarisAdsUtils","PolarisContainerModuleUtils","PolarisLocales","PolarisPCManagerProvider.react","PolarisStoriesLoggingUtils","PolarisStoriesV3AdFooter.react","PolarisStoriesV3AdGalleryPlayer_ad.graphql","PolarisStoriesV3AdGalleryPlayer_viewer.graphql","PolarisStoriesV3AdHeader.react","PolarisStoriesV3AdMediaImpressionWrapper.react","PolarisStoriesV3MediaFormat","PolarisStoriesV3Player.react","PolarisStoriesV3PlayerControllerContextProvider.react","PolarisTrackingCodeProvider.react","qex","react","react-compiler-runtime","useEmptyFunction","useMergeRefs","usePolarisFeedAnalyticsContext","usePolarisSponsoredElTracker","usePolarisSponsoredStoryReelImpressionLogger","usePolarisSponsoredStoryReelImpressionSecondChannelLogger","usePolarisStoriesV3AdLoggingContext","usePolarisStoriesV3MarkAdSeen","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react"));j.useCallback;e=c("qex")._("367");var l=e===!0?c("usePolarisSponsoredStoryReelImpressionSecondChannelLogger"):c("useEmptyFunction");function m(a){var b=d("react-compiler-runtime").c(8),e=a.children,f=a.impressionRefs,g=a.mediaPk,h=a.ref;a=a.trackingToken;f=c("useMergeRefs")(f,h);b[0]===Symbol["for"]("react.memo_cache_sentinel")?(h={className:"x5yr21d xh8yej3"},b[0]=h):h=b[0];b[1]!==e||b[2]!==f?(h=k.jsx("div",babelHelpers["extends"]({},h,{ref:f,children:e})),b[1]=e,b[2]=f,b[3]=h):h=b[3];b[4]!==g||b[5]!==h||b[6]!==a?(e=k.jsx(c("PolarisTrackingCodeProvider.react"),{isSponsored:!0,m_pk:g,trackingToken:a,children:h}),b[4]=g,b[5]=h,b[6]=a,b[7]=e):e=b[7];return e}function a(a){var e=d("react-compiler-runtime").c(82),f=a.ad,g=a.mediaId;a=a.viewer;var j=c("usePolarisFeedAnalyticsContext")();f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdGalleryPlayer_ad.graphql"),f);var n=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3AdGalleryPlayer_viewer.graphql"),a);e[0]!==f.items||e[1]!==g?(a=f.items.find(function(a){return a.pk===g}),e[0]=f.items,e[1]=g,e[2]=a):a=e[2];a=a;var o=f.items.findIndex(function(a){a=a.pk;return a===g});if(a==null)throw c("FBLogger")("ig_web").mustfixThrow("Missing media for story ad");var p=c("usePolarisStoriesV3MarkAdSeen")(f,a),q;e[3]!==p?(q=function(){p()},e[3]=p,e[4]=q):q=e[4];q=q;var r=f.is_cta_sticker_available_on_web_story===!0,s;e[5]!==n?(s=function(a){return k.jsx(c("PolarisStoriesV3AdMediaImpressionWrapper.react"),{viewer:n,children:a})},e[5]=n,e[6]=s):s=e[6];s=s;var t;e[7]!==f||e[8]!==a?(t=k.jsx(c("PolarisStoriesV3AdHeader.react"),{ad:f,media:a}),e[7]=f,e[8]=a,e[9]=t):t=e[9];t=t;var u;e[10]!==f||e[11]!==o||e[12]!==a||e[13]!==r||e[14]!==n?(u=k.jsx(c("PolarisStoriesV3AdFooter.react"),{ad:f,currSidecarIndex:o,media:a,useAdCTASticker:r,viewer:n}),e[10]=f,e[11]=o,e[12]=a,e[13]=r,e[14]=n,e[15]=u):u=e[15];r=u;u=c("usePolarisStoriesV3ReelLoggingContext")();var v=u.authorId,w=u.followStatus,x=u.mediaId,y=u.mediaPk,z=u.mediaType,A=u.reel,B=u.reelPosition,C=u.reelSize;u=u.takenAt;var D=c("usePolarisStoriesV3ViewerLoggingContext")(),E=D.getTrayPosition,F=D.loggingSession,G=D.reelType,H=D.traySessionId;D=D.viewerSessionId;var I=c("usePolarisStoriesV3AdLoggingContext")(),J=I.adId,K=I.trackingToken;e[16]!==E||e[17]!==A.id?(I=E(A.id),e[16]=E,e[17]=A.id,e[18]=I):I=e[18];E=I;e[19]!==z?(I=d("PolarisStoriesLoggingUtils").mediaTypeEnumToMediaType(z),e[19]=z,e[20]=I):I=e[20];z=I;I=A.id;var L;e[21]!==F?(L=F.getViewerSessionReelsConsumed(),e[21]=F,e[22]=L):L=e[22];e[23]!==J||e[24]!==w||e[25]!==y||e[26]!==x||e[27]!==z||e[28]!==v||e[29]!==A.id||e[30]!==B||e[31]!==C||e[32]!==G||e[33]!==E||e[34]!==L||e[35]!==u||e[36]!==K||e[37]!==H||e[38]!==D?(F={adId:J,adInsertedPosition:E,followStatus:w,mpk:y,postedAt:u,postId:x,postMediaType:z,postOwnerId:v,reelId:I,reelPosition:B,reelSize:C,reelType:G,reelViewerPosition:E,sessionReelCounter:L,trackingToken:K,traySession:H,viewerSession:D},e[23]=J,e[24]=w,e[25]=y,e[26]=x,e[27]=z,e[28]=v,e[29]=A.id,e[30]=B,e[31]=C,e[32]=G,e[33]=E,e[34]=L,e[35]=u,e[36]=K,e[37]=H,e[38]=D,e[39]=F):F=e[39];I=c("usePolarisSponsoredStoryReelImpressionLogger")(F);e[40]!==J||e[41]!==K?(w={adId:J,trackingToken:K},e[40]=J,e[41]=K,e[42]=w):w=e[42];x=l(w);e[43]!==A.id||e[44]!==E||e[45]!==K?(v={mediaType:0,position:E,postId:A.id,trackingToken:K},e[43]=A.id,e[44]=E,e[45]=K,e[46]=v):v=e[46];B=c("usePolarisSponsoredElTracker")(v);var M=c("useMergeRefs")(I,x,B);e[47]!==j?(C=d("PolarisContainerModuleUtils").getContainerModule(j),e[47]=j,e[48]=C):C=e[48];G=f.items.length>1?"CAROUSEL":z===d("PolarisAdsUtils").MEDIA_TYPE_IMAGE?"PHOTO":"VIDEO";e[49]!==J||e[50]!==y||e[51]!==C||e[52]!==G||e[53]!==K||e[54]!==n.user.id?(L={adId:J,containerModule:C,locale:c("PolarisLocales").locale,mediaType:G,mpk:y,trackingToken:K,userId:n.user.id},e[49]=J,e[50]=y,e[51]=C,e[52]=G,e[53]=K,e[54]=n.user.id,e[55]=L):L=e[55];u=L;H=f.items.length>1?d("PolarisStoriesV3MediaFormat").PolarisStoriesV3MediaFormat.CAROUSEL:z===d("PolarisAdsUtils").MEDIA_TYPE_IMAGE?d("PolarisStoriesV3MediaFormat").PolarisStoriesV3MediaFormat.IMAGE:d("PolarisStoriesV3MediaFormat").PolarisStoriesV3MediaFormat.VIDEO;e[56]!==f.items.length||e[57]!==r||e[58]!==q||e[59]!==t||e[60]!==o||e[61]!==a||e[62]!==s||e[63]!==H||e[64]!==n?(D=k.jsx(c("PolarisStoriesV3Player.react"),{adMediaFormat:H,currSideCarIndex:o,footer:r,header:t,media:a,numberOfItems:f.items.length,onPlayerStart:q,renderMediaPlayer:s,showCaption:!0,user:a.user,viewer:n}),e[56]=f.items.length,e[57]=r,e[58]=q,e[59]=t,e[60]=o,e[61]=a,e[62]=s,e[63]=H,e[64]=n,e[65]=D):D=e[65];e[66]!==a||e[67]!==D?(F=k.jsx(c("PolarisStoriesV3PlayerControllerContextProvider.react"),{media:a,children:D},a.pk),e[66]=a,e[67]=D,e[68]=F):F=e[68];var N=F;if(d("PolarisAdsGatingHelpers").enableStoryPostImpressionLogging()){e[69]!==y||e[70]!==M||e[71]!==N||e[72]!==K?(w=function(a){return k.jsx(m,{impressionRefs:M,mediaPk:y,ref:a,trackingToken:K,children:N})},e[69]=y,e[70]=M,e[71]=N,e[72]=K,e[73]=w):w=e[73];e[74]!==u||e[75]!==w?(A=k.jsx(c("PolarisPCManagerProvider.react"),{adLoggingFields:u,children:w}),e[74]=u,e[75]=w,e[76]=A):A=e[76];return A}e[77]!==y||e[78]!==M||e[79]!==N||e[80]!==K?(E=k.jsx(m,{impressionRefs:M,mediaPk:y,trackingToken:K,children:N}),e[77]=y,e[78]=M,e[79]=N,e[80]=K,e[81]=E):E=e[81];return E}g["default"]=a}),98);
__d("PolarisStoriesV3AdGalleryPlayerContainer_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdGalleryPlayerContainer_ad",selections:[{alias:null,args:null,kind:"ScalarField",name:"ad_id",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdGalleryPlayer_ad"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdLoggingContextProvider_ad"}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdGalleryPlayerContainer_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdGalleryPlayerContainer_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdGalleryPlayer_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelLoggingContextProvider_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdLoggingContextProvider_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdLoggingContextProvider_ad",selections:[{alias:null,args:null,kind:"ScalarField",name:"ad_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"tracking_token",storageKey:null}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3ReelListItems",["react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useSyncExternalStore;function a(a){return i(a.subscribe,a.getSnapshot)}g["default"]=a}),98);
__d("PolarisStoriesV3AdLoggingContextProvider.react",["CometRelay","PolarisStoriesV3AdLoggingContext","PolarisStoriesV3AdLoggingContextProvider_ad.graphql","PolarisStoriesV3Reel","react","react-compiler-runtime","usePolarisStoriesV3ReelListContext","usePolarisStoriesV3ReelListItems"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));i.useMemo;function a(a){var e=d("react-compiler-runtime").c(10),f=a.ad;a=a.children;var g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdLoggingContextProvider_ad.graphql"),f);f=c("usePolarisStoriesV3ReelListContext")();f=c("usePolarisStoriesV3ReelListItems")(f);var i=f.findIndex(function(a){return a.id===g.ad_id});f=f.slice(0,i).findLastIndex(k);i=f>=0?i-f-1:-1;e[0]!==g.tracking_token?(f={tracking_token:g.tracking_token},e[0]=g.tracking_token,e[1]=f):f=e[1];var l;e[2]!==g.ad_id||e[3]!==g.tracking_token||e[4]!==i||e[5]!==f?(l={adId:g.ad_id,gapToLastAd:i,trackingToken:g.tracking_token,videoLoggerAdInfo:f},e[2]=g.ad_id,e[3]=g.tracking_token,e[4]=i,e[5]=f,e[6]=l):l=e[6];i=l;f=i;e[7]!==a||e[8]!==f?(l=j.jsx(c("PolarisStoriesV3AdLoggingContext").Provider,{value:f,children:a}),e[7]=a,e[8]=f,e[9]=l):l=e[9];return l}function k(a){return a.type===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad}g["default"]=a}),98);
__d("PolarisStoriesV3BaseReelLoggingContextProvider_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3BaseReelLoggingContextProvider_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"viewer_count",storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3Duration_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3IsCloseFriendsStory_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MediaType_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3BaseReelLoggingContextProvider_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3BaseReelLoggingContextProvider_user",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3LoggingFollowStatus_user"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_user"}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3BaseReelLoggingContextProvider_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3BaseReelLoggingContextProvider_viewer",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3LoggingFollowStatus_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[{kind:"RootArgument",name:"is_highlight"}],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3LoggingFollowStatus_user",selections:[{alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null},{condition:"is_highlight",kind:"Condition",passingValue:!1,selections:[{alias:null,args:null,concreteType:"XDTRelationshipInfoDict",kind:"LinkedField",name:"friendship_status",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"following",storageKey:null}],storageKey:null}]}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3LoggingFollowStatus",["CometRelay","usePolarisStoriesV3LoggingFollowStatus_user.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3LoggingFollowStatus_user.graphql"),a);a=(a=a.friendship_status)==null?void 0:a.following;if(a===!0)return"following";else if(a===!1)return"not_following";else return null}g["default"]=a}),98);
__d("PolarisStoriesV3BaseReelLoggingContextProvider.react",["CometRelay","PolarisStoriesV3BaseReelLoggingContextProvider_media.graphql","PolarisStoriesV3BaseReelLoggingContextProvider_user.graphql","PolarisStoriesV3BaseReelLoggingContextProvider_viewer.graphql","PolarisStoriesV3LoggingSourceType","PolarisStoriesV3ReelLoggingContext","react","react-compiler-runtime","usePolarisStoriesV3Duration","usePolarisStoriesV3IsCloseFriendsStory","usePolarisStoriesV3LoggingFollowStatus","usePolarisStoriesV3MediaType","usePolarisStoriesV3ViewingOwnStory","useStable"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||(k=d("react"));k.useMemo;function a(a){var e=d("react-compiler-runtime").c(26),f=a.children,g=a.media,k=a.reel,n=a.reelLogData,o=a.user;a=a.viewer;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3BaseReelLoggingContextProvider_media.graphql"),g);o=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3BaseReelLoggingContextProvider_user.graphql"),o);a=d("CometRelay").useFragment(j!==void 0?j:j=b("PolarisStoriesV3BaseReelLoggingContextProvider_viewer.graphql"),a);var p=c("useStable")(m),q=n.firstView;n=n.mediaIds;var r=c("usePolarisStoriesV3MediaType")(g),s=o.pk,t=c("usePolarisStoriesV3Duration")(g),u=c("usePolarisStoriesV3LoggingFollowStatus")(o),v=c("usePolarisStoriesV3IsCloseFriendsStory")(g),w;e[0]!==g.pk||e[1]!==n?(w=n.indexOf(g.pk),e[0]=g.pk,e[1]=n,e[2]=w):w=e[2];w=w;n=n.length;o=c("usePolarisStoriesV3ViewingOwnStory")(o,a);a=o?1:0;var x=o?c("PolarisStoriesV3LoggingSourceType").SelfReel:c("PolarisStoriesV3LoggingSourceType").Reel;o=o?g.viewer_count:null;u=u!=null?u:"";o=o!=null?o:0;var y;e[3]!==k.id||e[4]!==k.type?(y={id:k.id,type:k.type},e[3]=k.id,e[4]=k.type,e[5]=y):y=e[5];e[6]!==s||e[7]!==q||e[8]!==a||e[9]!==v||e[10]!==g.id||e[11]!==g.pk||e[12]!==g.taken_at||e[13]!==t||e[14]!==r||e[15]!==p||e[16]!==w||e[17]!==n||e[18]!==x||e[19]!==u||e[20]!==o||e[21]!==y?(k={authorId:s,firstView:q,followStatus:u,hasMyReel:a,isAudienceCloseFriend:v,mediaDuration:t,mediaId:g.pk,mediaPk:g.id,mediaType:r,mediaViewers:o,pogImpressions:p,reel:y,reelPosition:w,reelSize:n,source:x,takenAt:g.taken_at},e[6]=s,e[7]=q,e[8]=a,e[9]=v,e[10]=g.id,e[11]=g.pk,e[12]=g.taken_at,e[13]=t,e[14]=r,e[15]=p,e[16]=w,e[17]=n,e[18]=x,e[19]=u,e[20]=o,e[21]=y,e[22]=k):k=e[22];s=k;q=s;e[23]!==f||e[24]!==q?(a=l.jsx(c("PolarisStoriesV3ReelLoggingContext").Provider,{value:q,children:f}),e[23]=f,e[24]=q,e[25]=a):a=e[25];return a}function m(){return new Set()}g["default"]=a}),98);
__d("PolarisStoriesV3ReelLoggingContextProvider_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"PolarisStoriesV3ReelLoggingContextProvider_ad"};e.exports=a}),null);
__d("PolarisStoriesV3ReelLoggingContextProvider_reels.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"PolarisStoriesV3ReelLoggingContextProvider_reels",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3BaseReelLoggingContextProvider_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ReelMediaIds_medias"}],storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3BaseReelLoggingContextProvider_user"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3FirstView_reels"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelLoggingContextProvider_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelLoggingContextProvider_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3BaseReelLoggingContextProvider_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("getPolarisStoriesV3IsReelMediaSeen_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"getPolarisStoriesV3IsReelMediaSeen_media"};e.exports=a}),null);
__d("getPolarisStoriesV3IsReelMediaSeen_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"getPolarisStoriesV3IsReelMediaSeen_reel"};e.exports=a}),null);
__d("getPolarisStoriesV3IsReelMediaSeen",["CometRelay","getPolarisStoriesV3IsReelMediaSeen_media.graphql","getPolarisStoriesV3IsReelMediaSeen_reel.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,c){a=d("CometRelay").readInlineData(h!==void 0?h:h=b("getPolarisStoriesV3IsReelMediaSeen_reel.graphql"),a);c=d("CometRelay").readInlineData(i!==void 0?i:i=b("getPolarisStoriesV3IsReelMediaSeen_media.graphql"),c);return Number((c=c.taken_at)!=null?c:0)<=Number(a.seen)}g["default"]=a}),98);
__d("usePolarisStoriesV3FirstView_reels.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"usePolarisStoriesV3FirstView_reels",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{kind:"InlineDataFragmentSpread",name:"getPolarisStoriesV3IsReelMediaSeen_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null}],args:null,argumentDefinitions:[]}],storageKey:null},action:"THROW"},{kind:"InlineDataFragmentSpread",name:"getPolarisStoriesV3IsReelMediaSeen_reel",selections:[{alias:null,args:null,kind:"ScalarField",name:"seen",storageKey:null}],args:null,argumentDefinitions:[]}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3FirstView",["CometRelay","FBLogger","PolarisStoriesV3Reel","getPolarisStoriesV3IsReelMediaSeen","react","react-compiler-runtime","usePolarisStoriesV3FirstView_reels.graphql","usePolarisStoriesV3SeenStateContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(i||d("react")).useState;function a(a,e,f){var g=d("react-compiler-runtime").c(11),i=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3FirstView_reels.graphql"),f),k=c("usePolarisStoriesV3SeenStateContext")();g[0]!==a.id||g[1]!==a.type||g[2]!==e||g[3]!==i||g[4]!==k?(f=function(){switch(a.type){case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad:return!k.isReelMediaSeen(a.id,e);case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic:var b=i.find(function(b){return b.id===a.id}),f=b==null?void 0:b.items.find(function(a){return a.pk===e});if(b==null||f==null){c("FBLogger")("ig_web").mustfix("Error getting first_view for organic story");return!k.isReelMediaSeen(a.id,e)}return!c("getPolarisStoriesV3IsReelMediaSeen")(b,f)}},g[0]=a.id,g[1]=a.type,g[2]=e,g[3]=i,g[4]=k,g[5]=f):f=g[5];var l=f;g[6]!==l?(f=function(){return l()},g[6]=l,g[7]=f):f=g[7];f=j(f);var m=f[0];f=f[1];var n;g[8]!==a||g[9]!==e?(n={mediaId:e,reel:a},g[8]=a,g[9]=e,g[10]=n):n=g[10];g=j(n);n=g[0];g=g[1];(n.reel.id!==a.id||n.mediaId!==e)&&(g({mediaId:e,reel:a}),f(l()));return m}g["default"]=a}),98);
__d("usePolarisStoriesV3ReelMediaIds_medias.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"usePolarisStoriesV3ReelMediaIds_medias",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3ReelMediaIds",["CometRelay","react","react-compiler-runtime","usePolarisStoriesV3ReelMediaIds_medias.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;(i||d("react")).useMemo;function a(a){var c=d("react-compiler-runtime").c(2);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3ReelMediaIds_medias.graphql"),a);var e;c[0]!==a?(e=a.map(j),c[0]=a,c[1]=e):e=c[1];a=e;return a}function j(a){return String(a.pk)}g["default"]=a}),98);
__d("PolarisStoriesV3ReelLoggingContextProvider.react",["CometRelay","FBLogger","PolarisStoriesV3BaseReelLoggingContextProvider.react","PolarisStoriesV3Reel","PolarisStoriesV3ReelLoggingContextProvider_ad.graphql","PolarisStoriesV3ReelLoggingContextProvider_reels.graphql","PolarisStoriesV3ReelLoggingContextProvider_viewer.graphql","react","react-compiler-runtime","usePolarisStoriesV3FirstView","usePolarisStoriesV3ReelMediaIds"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||(k=d("react"));k.useMemo;function a(a){var e=d("react-compiler-runtime").c(21),f=a.adsPool,g=a.children,k=a.currentReel,m=a.mediaId,n=a.reels;a=a.viewer;n=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelLoggingContextProvider_reels.graphql"),n);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ReelLoggingContextProvider_viewer.graphql"),a);var o,p,q;bb0:switch(k.type){case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad:if(e[0]!==f||e[1]!==k.id||e[2]!==m){var r=f==null?void 0:f.getAd(k.id).data;r=d("CometRelay").readInlineData(j!==void 0?j:j=b("PolarisStoriesV3ReelLoggingContextProvider_ad.graphql"),r);o=r==null?void 0:r.items;r=r==null?void 0:(r=r.items)==null?void 0:r.find(function(a){return a.pk===m});e[0]=f;e[1]=k.id;e[2]=m;e[3]=r;e[4]=o}else r=e[3],o=e[4];p=r;q=(f=p)==null?void 0:f.user;break bb0;case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic:e[5]!==k.id||e[6]!==n?(r=n.find(function(a){return a.id===k.id}),e[5]=k.id,e[6]=n,e[7]=r):r=e[7];f=r;if(f==null)throw c("FBLogger")("ig_web").mustfixThrow("Reel not found");o=f.items;e[8]!==m||e[9]!==f.items?(r=f.items.find(function(a){return a.pk===m}),e[8]=m,e[9]=f.items,e[10]=r):r=e[10];p=r;q=f.user}if(o==null)throw c("FBLogger")("ig_web").mustfixThrow("Missing media items for story");if(p==null)throw c("FBLogger")("ig_web").mustfixThrow("Missing media for story");if(q==null)throw c("FBLogger")("ig_web").mustfixThrow("Missing user for story");r=c("usePolarisStoriesV3FirstView")(k,m,n);f=c("usePolarisStoriesV3ReelMediaIds")(o);e[11]!==r||e[12]!==f?(n={firstView:r,mediaIds:f},e[11]=r,e[12]=f,e[13]=n):n=e[13];r=n;f=r;e[14]!==g||e[15]!==k||e[16]!==p||e[17]!==f||e[18]!==q||e[19]!==a?(n=l.jsx(c("PolarisStoriesV3BaseReelLoggingContextProvider.react"),{media:p,reel:k,reelLogData:f,user:q,viewer:a,children:g}),e[14]=g,e[15]=k,e[16]=p,e[17]=f,e[18]=q,e[19]=a,e[20]=n):n=e[20];return n}g["default"]=a}),98);
__d("PolarisStoriesV3AdGalleryPlayerContainer.react",["CometRelay","PolarisStoriesV3AdGalleryPlayer.react","PolarisStoriesV3AdGalleryPlayerContainer_ad.graphql","PolarisStoriesV3AdGalleryPlayerContainer_viewer.graphql","PolarisStoriesV3AdLoggingContextProvider.react","PolarisStoriesV3Reel","PolarisStoriesV3ReelLoggingContextProvider.react","react","react-compiler-runtime","usePolarisStoriesV3AdsPoolContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a){var e=d("react-compiler-runtime").c(16),f=a.ad,g=a.mediaId;a=a.viewer;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdGalleryPlayerContainer_ad.graphql"),f);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3AdGalleryPlayerContainer_viewer.graphql"),a);var j=c("usePolarisStoriesV3AdsPoolContext")(),l;e[0]!==f.ad_id?(l={id:f.ad_id,type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad},e[0]=f.ad_id,e[1]=l):l=e[1];var m;e[2]===Symbol["for"]("react.memo_cache_sentinel")?(m=[],e[2]=m):m=e[2];var n;e[3]!==f||e[4]!==g||e[5]!==a?(n=k.jsx(c("PolarisStoriesV3AdGalleryPlayer.react"),{ad:f,mediaId:g,viewer:a}),e[3]=f,e[4]=g,e[5]=a,e[6]=n):n=e[6];var o;e[7]!==f||e[8]!==n?(o=k.jsx(c("PolarisStoriesV3AdLoggingContextProvider.react"),{ad:f,children:n}),e[7]=f,e[8]=n,e[9]=o):o=e[9];e[10]!==j||e[11]!==g||e[12]!==l||e[13]!==o||e[14]!==a?(f=k.jsx(c("PolarisStoriesV3ReelLoggingContextProvider.react"),{adsPool:j,currentReel:l,mediaId:g,reels:m,viewer:a,children:o}),e[10]=j,e[11]=g,e[12]=l,e[13]=o,e[14]=a,e[15]=f):f=e[15];return f}g["default"]=a}),98);
__d("PolarisStoriesV3AdGalleryPreview_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdGalleryPreview_ad",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"ad_id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdPreviewCoverImage_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3GalleryPreview_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3AdPreviewTitle_media"}],storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"tracking_token",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdSponsoredLabel_ad"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3AdPreviewLinkProps_ad"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3AdPreviewTitle_ad"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3NextAdMediaId_ad"}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3AdPreviewCoverImage_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AdPreviewCoverImage_media",selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"owner",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null}],storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3PreviewCoverImage.react",["CometImage.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(5);a=a.src;var e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e={className:"xyzq4qe x1c9tyrk xeusxvb x1pahc9y x1ertn4p x972fbf x10w94by x1qhh985 x14e42zd xnnlda6 x6ikm8r x10wlt62 x15yg21f"},b[0]=e):e=b[0];var f;b[1]!==a?(f=a!=null&&i.jsx(c("CometImage.react"),{height:"100%",objectFit:"cover",src:a,width:"100%"}),b[1]=a,b[2]=f):f=b[2];b[3]!==f?(a=i.jsx("div",babelHelpers["extends"]({},e,{children:f})),b[3]=f,b[4]=a):a=b[4];return a}g["default"]=a}),98);
__d("PolarisStoriesV3AdPreviewCoverImage.react",["CometRelay","PolarisStoriesV3AdPreviewCoverImage_media.graphql","PolarisStoriesV3PreviewCoverImage.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(2);a=a.media;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdPreviewCoverImage_media.graphql"),a);a=(a=a.owner)==null?void 0:a.profile_pic_url;var f;e[0]!==a?(f=j.jsx(c("PolarisStoriesV3PreviewCoverImage.react"),{src:a}),e[0]=a,e[1]=f):f=e[1];return f}g["default"]=a}),98);
__d("PolarisStoriesV3GalleryPreviewSeenStateOverlay.react",["react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(4);a=a.isSeen;var c;b[0]!==a?(c={0:{className:"x1ey2m1c xtijo5x x1o0tod x10l6tqk x13vifvy xeab6ia"},1:{className:"x1ey2m1c xtijo5x x1o0tod x10l6tqk x13vifvy x1qnxfwq"}}[!!a<<0],b[0]=a,b[1]=c):c=b[1];b[2]!==c?(a=i.jsx("div",babelHelpers["extends"]({},c)),b[2]=c,b[3]=a):a=b[3];return a}g["default"]=a}),98);
__d("PolarisStoriesV3GalleryPreview_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3GalleryPreview_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3PreviewImage_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3PreviewSensitivityOverlay_media"},{args:null,kind:"FragmentSpread",name:"usePolarisMediaOverlayMediaCoverInfo_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3PreviewImage_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3PreviewImage_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"accessibility_caption",storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ImageSrc_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3PreviewImage.react",["CometImage.react","CometRelay","PolarisStoriesV3PreviewImage_media.graphql","react","react-compiler-runtime","usePolarisStoriesV3ImageSrc"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(3);a=a.media;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3PreviewImage_media.graphql"),a);var f=c("usePolarisStoriesV3ImageSrc")(a);a=(a=a.accessibility_caption)!=null?a:void 0;var g;e[0]!==f||e[1]!==a?(g=j.jsx(c("CometImage.react"),{alt:a,height:"100%",objectFit:"cover",src:f,width:"100%"}),e[0]=f,e[1]=a,e[2]=g):g=e[2];return g}g["default"]=a}),98);
__d("PolarisStoriesV3PreviewSensitivityOverlay_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3PreviewSensitivityOverlay_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3BlurryImagePreview_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3PreviewSensitivityOverlay.react",["CometRelay","IGDSEyeOffOutlineIcon.react","PolarisMisinformationConstants","PolarisStoriesV3BlurryImagePreview.react","PolarisStoriesV3PreviewSensitivityOverlay_media.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(7);a=a.media;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3PreviewSensitivityOverlay_media.graphql"),a);var f;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(f={className:"x78zum5 x5yr21d xl56j7k x10l6tqk xh8yej3"},e[0]=f):f=e[0];var g;e[1]!==a?(g=j.jsx(c("PolarisStoriesV3BlurryImagePreview.react"),{media:a}),e[1]=a,e[2]=g):g=e[2];var i;e[3]===Symbol["for"]("react.memo_cache_sentinel")?(a=j.jsx("div",{className:"x12lpqc0 x5yr21d x10l6tqk xh8yej3"}),i=j.jsx("div",babelHelpers["extends"]({className:"x10l6tqk xxcbz4w"},{children:j.jsx(c("IGDSEyeOffOutlineIcon.react"),{alt:d("PolarisMisinformationConstants").SENSITIVITY_OVERLAY_GLYPH_ALT_TEXT,color:"ig-text-on-media",size:48})})),e[3]=a,e[4]=i):(a=e[3],i=e[4]);e[5]!==g?(f=j.jsxs("div",babelHelpers["extends"]({},f,{children:[g,a,i]})),e[5]=g,e[6]=f):f=e[6];return f}g["default"]=a}),98);
__d("usePolarisStoriesV3LogReelSessionSummary",["PolarisStoriesV3Reel","ReelSessionSummaryFalcoEvent","isStringNullOrEmpty","react","react-compiler-runtime","usePolarisStoriesV3GetSharedLoggingData","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(4),b=c("usePolarisStoriesV3ViewerLoggingContext")(),e=c("usePolarisStoriesV3ReelLoggingContext")(),f=c("usePolarisStoriesV3GetSharedLoggingData")(),g;a[0]!==f||a[1]!==e||a[2]!==b?(g=function(a,g){var h=f(),i=h.follow_status,j=h.reel_size,k=h.reel_type,l=h.session_reel_counter,m=h.tray_position,n=h.tray_session_id,o=h.viewer_session_id,p=b.loggingSession;c("ReelSessionSummaryFalcoEvent").log(function(){return{a_pk:c("isStringNullOrEmpty")(e.authorId)?null:e.authorId,action:a!=null?a:b.getAction(),ad_delivered_count:"0",ad_photos_consumed:"0",ad_videos_consumed:"0",delivery_class:(g==null?void 0:g.type)===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad?"ad":"organic",follow_status:i,is_ad:(g==null?void 0:g.type)===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad,live_videos_consumed:"0",module_name:String(b.module),pause_duration:p.getReelPauseDuration(),photos_consumed:String(p.getPhotosConsumed()),reel_id:g==null?void 0:g.id,reel_size:String(j),reel_type:k,replay_videos_consumed:"0",session_reel_counter:String(l),time_elapsed:p.getReelTimeElapsed(),tray_position:m,tray_session_id:n,videos_consumed:String(p.getVideosConsumed()),viewer_session_ad_media_consumed:"0",viewer_session_ad_reels_consumed:"0",viewer_session_id:o,viewer_session_media_consumed:String(p.getViewerSessionMediaConsumed()),viewer_session_reels_consumed:String(p.getViewerSessionReelsConsumed())}})},a[0]=f,a[1]=e,a[2]=b,a[3]=g):g=a[3];return g}g["default"]=a}),98);
__d("usePolarisStoriesV3LogPreviewImpressionEffect",["react","usePolarisStoriesV3LogReelSessionSummary","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(a){var b=c("usePolarisStoriesV3ViewerLoggingContext")(),d=c("usePolarisStoriesV3LogReelSessionSummary")();i(function(){b.previewImpressions.has(a.id)||(b.previewImpressions.add(a.id),d("web_story_preview",a))},[b,d,a])}g["default"]=a}),98);
__d("PolarisStoriesV3GalleryPreview.react",["CometPressable.react","CometRelay","IGDSTextVariants.react","PolarisStoriesV3GalleryPreviewSeenStateOverlay.react","PolarisStoriesV3GalleryPreview_media.graphql","PolarisStoriesV3PreviewImage.react","PolarisStoriesV3PreviewSensitivityOverlay.react","react","react-compiler-runtime","usePolarisMediaOverlayMediaCoverInfo","usePolarisStoriesV3LogPreviewImpressionEffect"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={root:{borderStartStartRadius:"x1obq294",borderStartEndRadius:"x5a5i1n",borderEndEndRadius:"xde0f50",borderEndStartRadius:"x15x8krk",height:"x5yr21d",overflowX:"x6ikm8r",overflowY:"x10wlt62",position:"x1n2onr6",width:"xh8yej3",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(24),f=a.coverImage,g=a.isSeen,i=a.linkProps,l=a.media,m=a.onPress,n=a.reel,o=a.ref,p=a.subtitle;a=a.title;l=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3GalleryPreview_media.graphql"),l);c("usePolarisStoriesV3LogPreviewImpressionEffect")(n);n=c("usePolarisMediaOverlayMediaCoverInfo")(l);var q;e[0]!==l||e[1]!==n?(q=n!=null?j.jsx(c("PolarisStoriesV3PreviewSensitivityOverlay.react"),{media:l}):j.jsx(c("PolarisStoriesV3PreviewImage.react"),{media:l}),e[0]=l,e[1]=n,e[2]=q):q=e[2];e[3]!==g?(l=j.jsx(c("PolarisStoriesV3GalleryPreviewSeenStateOverlay.react"),{isSeen:g}),e[3]=g,e[4]=l):l=e[4];e[5]===Symbol["for"]("react.memo_cache_sentinel")?(n={className:"x6s0dn4 x1ey2m1c x78zum5 xdt5ytf xtijo5x x1o0tod xl56j7k x10l6tqk x2b8uid x1w80he5 x13vifvy"},g={className:"x1yztbdb"},e[5]=n,e[6]=g):(n=e[5],g=e[6]);e[7]!==f?(g=j.jsx("div",babelHelpers["extends"]({},g,{children:f})),e[7]=f,e[8]=g):g=e[8];e[9]!==a?(f=a!=null&&j.jsx("div",babelHelpers["extends"]({className:"x1e56ztr"},{children:j.jsx(d("IGDSTextVariants.react").IGDSTextBodyEmphasized,{color:"textOnMedia",children:a})})),e[9]=a,e[10]=f):f=e[10];e[11]!==p?(a=p!=null&&j.jsx(d("IGDSTextVariants.react").IGDSTextBody,{color:"textOnMedia",children:p}),e[11]=p,e[12]=a):a=e[12];e[13]!==g||e[14]!==f||e[15]!==a?(p=j.jsxs("div",babelHelpers["extends"]({},n,{children:[g,f,a]})),e[13]=g,e[14]=f,e[15]=a,e[16]=p):p=e[16];e[17]!==i||e[18]!==m||e[19]!==o||e[20]!==q||e[21]!==l||e[22]!==p?(n=j.jsxs(c("CometPressable.react"),{linkProps:i,onPress:m,overlayDisabled:!0,ref:o,xstyle:k.root,children:[q,l,p]}),e[17]=i,e[18]=m,e[19]=o,e[20]=q,e[21]=l,e[22]=p,e[23]=n):n=e[23];return n}g["default"]=a}),98);
__d("getPolarisStoriesV3AdUsername_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"getPolarisStoriesV3AdUsername_ad"};e.exports=a}),null);
__d("getPolarisStoriesV3AdUsername",["CometRelay","FBLogger","getPolarisStoriesV3AdUsername_ad.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){a=d("CometRelay").readInlineData(h!==void 0?h:h=b("getPolarisStoriesV3AdUsername_ad.graphql"),a);a=(a=a.items[0].user)==null?void 0:a.username;if(a==null)throw c("FBLogger")("ig_web").mustfixThrow("Ad username not found");return a}g["default"]=a}),98);
__d("usePolarisStoriesV3AdPreviewLinkProps_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3AdPreviewLinkProps_ad",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"ad_id",storageKey:null},action:"THROW"},{kind:"InlineDataFragmentSpread",name:"getPolarisStoriesV3AdUsername_ad",selections:[{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null}],storageKey:null}],storageKey:null}],args:null,argumentDefinitions:[]}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3RefinePassthroughTrayLoggingData",["refine"],(function(a,b,c,d,e,f,g){"use strict";b=(a=d("refine")).object({newReelCount:a.number(),traySessionId:a.string(),viewedReelCount:a.number()});c=a.assertion(b);g.passthroughTrayLoggingDataChecker=b;g.passthroughTrayLoggingDataAssertion=c}),98);
__d("usePolarisStoriesV3RefinedReelPagePassthroughProps",["FBLogger","PolarisStoriesV3ContainerModule","PolarisStoriesV3RefinePassthroughTrayLoggingData","react","refine","useRoutePassthroughProps"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useMemo,j=d("refine").assertion(d("refine").object({adId:d("refine").optional(d("refine").nullable(d("refine").string())),initialMediaId:d("refine").optional(d("refine").nullable(d("refine").string())),initialReelId:d("refine").optional(d("refine").nullable(d("refine").string())),isSmallScreen:d("refine").optional(d("refine").bool()),loggingModule:d("refine").optional(d("refine").nullable(d("refine").custom(function(a){return c("PolarisStoriesV3ContainerModule").cast(String(a))}))),reelIds:d("refine").optional(d("refine").nullable(d("refine").array(d("refine").string()))),trayLoggingData:d("refine").optional(d("PolarisStoriesV3RefinePassthroughTrayLoggingData").passthroughTrayLoggingDataChecker),viewerSessionId:d("refine").optional(d("refine").string())}));function a(){var a=c("useRoutePassthroughProps")();return i(function(){if(a==null)return null;try{return j(a)}catch(a){c("FBLogger")("ig_web").catching(a).mustfix("Failed to refine passthrough props");return null}},[a])}g["default"]=a}),98);
__d("usePolarisStoriesV3ReelLinkPropsBuilder",["FBLogger","PolarisStoriesV3Reel","react","react-compiler-runtime","useCurrentRoute","usePolarisStoriesV3LogReelSessionSummary","usePolarisStoriesV3RefinedReelPagePassthroughProps","usePolarisStoriesV3UserReelRouteBuilder","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(7),b=c("usePolarisStoriesV3UserReelRouteBuilder")(),e=c("useCurrentRoute")(),f=c("usePolarisStoriesV3RefinedReelPagePassthroughProps")(),g=c("usePolarisStoriesV3ViewerLoggingContext")(),h=g.loggingSession,i=c("usePolarisStoriesV3LogReelSessionSummary")();if(a[0]!==b||a[1]!==(f==null?void 0:f.initialReelId)||a[2]!==(f==null?void 0:f.reelIds)||a[3]!==(e==null?void 0:(g=e.rootView)==null?void 0:(g=g.props)==null?void 0:g.user_id)||a[4]!==i||a[5]!==h){var j;g=function(a,g,j,k){a=b(a,j);j=(j=f==null?void 0:f.initialReelId)!=null?j:e==null?void 0:(j=e.rootView)==null?void 0:(j=j.props)==null?void 0:j.user_id;var l=f==null?void 0:f.reelIds;if(l==null||j==null)throw c("FBLogger")("ig_web").mustfixThrow("Missing passthrough props");var m=g.type===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad?g.id:null;return{onNavigate:function(){i(),h.startReel(g.id),k==null?void 0:k()},passthroughProps:{adId:m,initialReelId:j,reelIds:l},replace:!0,routeTarget:"self",url:a}};a[0]=b;a[1]=f==null?void 0:f.initialReelId;a[2]=f==null?void 0:f.reelIds;a[3]=e==null?void 0:(j=e.rootView)==null?void 0:(j=j.props)==null?void 0:j.user_id;a[4]=i;a[5]=h;a[6]=g}else g=a[6];return g}g["default"]=a}),98);
__d("usePolarisStoriesV3AdPreviewLinkProps",["CometRelay","PolarisStoriesV3Reel","getPolarisStoriesV3AdUsername","react","usePolarisStoriesV3AdPreviewLinkProps_ad.graphql","usePolarisStoriesV3ReelLinkPropsBuilder"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(i||d("react")).useMemo;function a(a,e){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3AdPreviewLinkProps_ad.graphql"),a);var f=c("usePolarisStoriesV3ReelLinkPropsBuilder")(),g=c("getPolarisStoriesV3AdUsername")(a),i=a.ad_id;return j(function(){return f(g,{id:i,type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad},e)},[f,g,e,i])}g["default"]=a}),98);
__d("usePolarisStoriesV3AdPreviewTitle_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3AdPreviewTitle_ad",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3AdOwnerTitle_ad"}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3AdPreviewTitle_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3AdPreviewTitle_media",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3AdOwnerTitle_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3AdPreviewTitle",["CometRelay","usePolarisStoriesV3AdOwnerTitle","usePolarisStoriesV3AdPreviewTitle_ad.graphql","usePolarisStoriesV3AdPreviewTitle_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,e){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3AdPreviewTitle_ad.graphql"),a);e=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3AdPreviewTitle_media.graphql"),e);return c("usePolarisStoriesV3AdOwnerTitle")(a,e)}g["default"]=a}),98);
__d("getPolarisStoriesV3NextAdMediaId_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"getPolarisStoriesV3NextAdMediaId_ad"};e.exports=a}),null);
__d("getPolarisStoriesV3NextMediaId",["FBLogger"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){var d=b.getLastSeenMediaId(a.id);if(d!=null)return d;d=(d=a.items[0])==null?void 0:d.id;if(d==null)throw c("FBLogger")("ig_web").mustfixThrow("No media in reel");if(b.isReelSeen(a.id))return d;if(a.seen!=null&&a.items.every(function(a){return a.takenAt!=null})){b=a.items.find(function(b){return Number(b.takenAt)>Number(a.seen)});return(b=b==null?void 0:b.id)!=null?b:d}return d}g["default"]=a}),98);
__d("getPolarisStoriesV3NextAdMediaId",["CometRelay","FBLogger","getPolarisStoriesV3NextAdMediaId_ad.graphql","getPolarisStoriesV3NextMediaId"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,e){a=d("CometRelay").readInlineData(h!==void 0?h:h=b("getPolarisStoriesV3NextAdMediaId_ad.graphql"),a);var f=a.ad_id;if(f==null)throw c("FBLogger")("ig_web").mustfixThrow("Ad id is null");f={id:f,items:a.items.map(function(a){return{id:a.pk}})};return c("getPolarisStoriesV3NextMediaId")(f,e)}g["default"]=a}),98);
__d("usePolarisStoriesV3NextAdMediaId_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3NextAdMediaId_ad",selections:[{kind:"InlineDataFragmentSpread",name:"getPolarisStoriesV3NextAdMediaId_ad",selections:[{alias:null,args:null,kind:"ScalarField",name:"ad_id",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null}],storageKey:null}],args:null,argumentDefinitions:[]}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3NextAdMediaId",["CometRelay","getPolarisStoriesV3NextAdMediaId","usePolarisStoriesV3NextAdMediaId_ad.graphql","usePolarisStoriesV3SeenStateContext"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3NextAdMediaId_ad.graphql"),a);var e=c("usePolarisStoriesV3SeenStateContext")();return c("getPolarisStoriesV3NextAdMediaId")(a,e)}g["default"]=a}),98);
__d("PolarisStoriesV3AdGalleryPreview.react",["CometRelay","FBLogger","PolarisStoriesV3AdGalleryPreview_ad.graphql","PolarisStoriesV3AdPreviewCoverImage.react","PolarisStoriesV3AdSponsoredLabel.react","PolarisStoriesV3GalleryPreview.react","PolarisStoriesV3Reel","react","react-compiler-runtime","usePolarisSponsoredElTracker","usePolarisStoriesV3AdPreviewLinkProps","usePolarisStoriesV3AdPreviewTitle","usePolarisStoriesV3NextAdMediaId","usePolarisStoriesV3SeenStateContext","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(29),f=a.ad;a=a.onPress;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AdGalleryPreview_ad.graphql"),f);var g=c("usePolarisStoriesV3SeenStateContext")(),i=c("usePolarisStoriesV3NextAdMediaId")(f),k=c("usePolarisStoriesV3AdPreviewLinkProps")(f,i),l=c("usePolarisStoriesV3ViewerLoggingContext")(),m;e[0]!==f.items||e[1]!==i?(m=f.items.find(function(a){return a.pk===i}),e[0]=f.items,e[1]=i,e[2]=m):m=e[2];m=m;if(m==null)throw c("FBLogger")("ig_web").mustfixThrow("Media not found for ad preview");var n=c("usePolarisStoriesV3AdPreviewTitle")(f,m),o;e[3]!==f?(o=j.jsx(c("PolarisStoriesV3AdSponsoredLabel.react"),{ad:f,"data-testid":void 0}),e[3]=f,e[4]=o):o=e[4];o=o;var p;e[5]!==f.ad_id||e[6]!==g?(p=g.isReelSeen(f.ad_id),e[5]=f.ad_id,e[6]=g,e[7]=p):p=e[7];g=p;e[8]!==f.ad_id||e[9]!==l?(p=l.getTrayPosition(f.ad_id),e[8]=f.ad_id,e[9]=l,e[10]=p):p=e[10];e[11]!==f.ad_id||e[12]!==f.tracking_token||e[13]!==p?(l={mediaType:0,position:p,postId:f.ad_id,trackingToken:f.tracking_token},e[11]=f.ad_id,e[12]=f.tracking_token,e[13]=p,e[14]=l):l=e[14];p=c("usePolarisSponsoredElTracker")(l);e[15]!==m?(l=j.jsx(c("PolarisStoriesV3AdPreviewCoverImage.react"),{media:m}),e[15]=m,e[16]=l):l=e[16];var q;e[17]!==f.ad_id?(q={id:f.ad_id,type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad},e[17]=f.ad_id,e[18]=q):q=e[18];e[19]!==g||e[20]!==k||e[21]!==m||e[22]!==a||e[23]!==p||e[24]!==o||e[25]!==l||e[26]!==q||e[27]!==n?(f=j.jsx(c("PolarisStoriesV3GalleryPreview.react"),{coverImage:l,isSeen:g,linkProps:k,media:m,onPress:a,reel:q,ref:p,subtitle:o,title:n}),e[19]=g,e[20]=k,e[21]=m,e[22]=a,e[23]=p,e[24]=o,e[25]=l,e[26]=q,e[27]=n,e[28]=f):f=e[28];return f}g["default"]=a}),98);
__d("PolarisStoriesV3AppAttribution_attribution.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3AppAttribution_attribution",selections:[{alias:null,args:null,kind:"ScalarField",name:"app_action_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"app_icon_url",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AppAttributionPopover_attribution"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3LogReelViewerAttributionClick_attribution"}],type:"XDTStoryAppAttributionDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3LogReelViewerAttributionClick_attribution.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3LogReelViewerAttributionClick_attribution",selections:[{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null}],type:"XDTStoryAppAttributionDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3LogReelViewerAttributionClick",["CometRelay","ReelViewerAppAttributionClickFalcoEvent","react","react-compiler-runtime","usePolarisStoriesV3LogReelViewerAttributionClick_attribution.graphql","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i;(i||d("react")).useCallback;function a(e){var g=d("react-compiler-runtime").c(3),i=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3LogReelViewerAttributionClick_attribution.graphql"),e);e=c("usePolarisStoriesV3ViewerLoggingContext")();var a=e.module;g[0]!==i||g[1]!==a?(e=function(){c("ReelViewerAppAttributionClickFalcoEvent").log(function(){var b;return{app_name:(b=i.name)!=null?b:"",attribution_id:(b=i.id)!=null?b:"",module_name:String(a)}})},g[0]=i,g[1]=a,g[2]=e):e=g[2];return e}g["default"]=a}),98);
__d("PolarisStoriesV3AppAttribution.react",["CometImage.react","CometPressable.react","CometRelay","IGDSChevronRightPanoFilledIcon.react","IGDSLazyPopoverTrigger.react","IGDSTextVariants.react","JSResourceForInteraction","Locale","PolarisGenericStrings","PolarisStoriesV3AppAttribution_attribution.graphql","react","react-compiler-runtime","usePolarisStoriesV3LogReelViewerAttributionClick"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k=c("JSResourceForInteraction")("PolarisStoriesV3AppAttributionPopover.react").__setRef("PolarisStoriesV3AppAttribution.react"),l={icon:{marginInlineEnd:"xf6vk7d",$$css:!0},root:{alignItems:"x6s0dn4",display:"x78zum5",":hover_textDecoration":"x1lku1pv",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(10);a=a.attribution;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3AppAttribution_attribution.graphql"),a);var f=a.app_action_text,g=a.app_icon_url,i;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(i=c("Locale").isRTL(),e[0]=i):i=e[0];var m=i,n=c("usePolarisStoriesV3LogReelViewerAttributionClick")(a);e[1]!==a?(i={attribution:a},e[1]=a,e[2]=i):i=e[2];e[3]!==f||e[4]!==g||e[5]!==n?(a=function(a,b){return j.jsxs(c("CometPressable.react"),{onPress:function(a){n(),b(a)},overlayDisabled:!0,ref:a,xstyle:l.root,children:[g!=null&&j.jsx(c("CometImage.react"),{src:g,width:12,xstyle:l.icon}),j.jsx("div",babelHelpers["extends"]({className:"xnnr8r"},{children:j.jsx(d("IGDSTextVariants.react").IGDSTextFootnote,{color:"textOnMedia",children:f})})),j.jsx("div",babelHelpers["extends"]({},{0:{},1:{className:"x19jd1h0"}}[!!m<<0],{children:j.jsx(c("IGDSChevronRightPanoFilledIcon.react"),{alt:d("PolarisGenericStrings").RIGHT_CHEVRON,color:"ig-stroke-on-media",size:12})}))]})},e[3]=f,e[4]=g,e[5]=n,e[6]=a):a=e[6];var o;e[7]!==i||e[8]!==a?(o=j.jsx(c("IGDSLazyPopoverTrigger.react"),{align:"middle",popoverProps:i,popoverResource:k,position:"below",preloadTrigger:"button",children:a}),e[7]=i,e[8]=a,e[9]=o):o=e[9];return o}g["default"]=a}),98);
__d("PolarisStoriesV3CloseFriendsButton_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3CloseFriendsButton_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3CloseFriendsDialog_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3CloseFriendsButton_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3CloseFriendsButton_user",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3CloseFriendsDialog_user"}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3CloseFriendsButton.react",["CometRelay","IGDSDialogPlaceholder.react","IGDSSpinner.react","JSResourceForInteraction","PolarisCloseFriendsLabel.react","PolarisStoriesV3CloseFriendsButton_media.graphql","PolarisStoriesV3CloseFriendsButton_user.graphql","react","react-compiler-runtime","stylex","useIGDSLazyDialog"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||d("react"),m=c("JSResourceForInteraction")("PolarisStoriesV3CloseFriendsDialog.react").__setRef("PolarisStoriesV3CloseFriendsButton.react"),n={placeholder:{display:"x78zum5",height:"x1luhteg",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(11),f=a.media,g=a.user;a=a.xstyle;var k=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3CloseFriendsButton_media.graphql"),f),n=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3CloseFriendsButton_user.graphql"),g);f=c("useIGDSLazyDialog")(m,o);var p=f[0];e[0]!==k||e[1]!==p||e[2]!==n?(g=function(){p({media:k,user:n})},e[0]=k,e[1]=p,e[2]=n,e[3]=g):g=e[3];f=g;e[4]!==a?(g=(j||(j=c("stylex"))).props(a),e[4]=a,e[5]=g):g=e[5];e[6]!==f?(a=l.jsx(c("PolarisCloseFriendsLabel.react"),{onClick:f,textSize:"small"}),e[6]=f,e[7]=a):a=e[7];e[8]!==g||e[9]!==a?(f=l.jsx("div",babelHelpers["extends"]({},g,{children:a})),e[8]=g,e[9]=a,e[10]=f):f=e[10];return f}function o(a){return l.jsx(c("IGDSDialogPlaceholder.react"),{innerContentXStyle:n.placeholder,onClose:a,children:l.jsx("div",babelHelpers["extends"]({className:"x6s0dn4 x78zum5 x1iyjqo2 xl56j7k"},{children:l.jsx(c("IGDSSpinner.react"),{})}))})}o.displayName=o.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("PolarisStoriesV3DeleteMediaContext",["emptyFunction","react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b={deleteMedia:c("emptyFunction"),isDeleted:c("emptyFunction").thatReturns(!1)};e=a.createContext(b);g["default"]=e}),98);
__d("PolarisStoriesV3DeleteMediaContextProvider_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3DeleteMediaContextProvider_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3DeleteMediaContextProvider_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3DeleteMediaContextProvider_viewer",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3DeleteMediaMutation_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9386325598132428"}),null);
__d("usePolarisStoriesV3DeleteMediaMutation.graphql",["usePolarisStoriesV3DeleteMediaMutation_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"mediaId"}],c=[{alias:null,args:[{kind:"Variable",name:"media_id",variableName:"mediaId"}],concreteType:"XDTDeleteResponse",kind:"LinkedField",name:"xdt_api__v1__create__delete",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"did_delete",storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"usePolarisStoriesV3DeleteMediaMutation",selections:c,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"usePolarisStoriesV3DeleteMediaMutation",selections:c},params:{id:b("usePolarisStoriesV3DeleteMediaMutation_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__create__delete"]},name:"usePolarisStoriesV3DeleteMediaMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("usePolarisStoriesV3DeleteMediaMutation",["CometRelay","FBLogger","PolarisStoryState","igMapTypenameToRelayID","polarisGetXDTMaterialTray","polarisLogAction","react","react-compiler-runtime","usePolarisStoriesV3DeleteMediaMutation.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;(i||d("react")).useCallback;var j=h!==void 0?h:h=b("usePolarisStoriesV3DeleteMediaMutation.graphql");function a(){var a=d("react-compiler-runtime").c(5),b=d("CometRelay").useMutation(j),e=b[0];b=b[1];var f;a[0]!==e?(f=function(a){var b=a.mediaId,f=a.mediaPk,g=a.onComplete,h=a.onError,i=a.reelId;a=function(a,e){if((e==null?void 0:(e=e.xdt_api__v1__create__delete)==null?void 0:e.did_delete)!==!0)return;var f=c("igMapTypenameToRelayID")("XDTReelDict",i),g=c("igMapTypenameToRelayID")("XDTMediaDict",b,null);e=a.get(f);var h=e==null?void 0:e.getLinkedRecords("items");h=(h=h==null?void 0:h.filter(function(a){return(a==null?void 0:a.getDataID())!==g}))!=null?h:[];e==null?void 0:e.setLinkedRecords(h,"items");a["delete"](g);if(h.length===0)for(e of d("PolarisStoryState").PolarisStoryTrayVariants.members()){var j;h=c("polarisGetXDTMaterialTray")(a,e===d("PolarisStoryState").PolarisStoryTrayVariants.FOLLOWING);j=(j=h==null?void 0:(j=h.getLinkedRecords("tray"))==null?void 0:j.filter(function(a){return(a==null?void 0:a.getDataID())!==f}))!=null?j:[];h==null?void 0:h.setLinkedRecords(j,"tray")}};var j=function(a){a.xdt_api__v1__create__delete.did_delete!==!0?(c("FBLogger")("ig_web").warn("Story not deleted"),c("polarisLogAction")("delete_post_failed"),h==null?void 0:h()):(c("polarisLogAction")("delete_post_succeeded"),g==null?void 0:g())},k=function(a){c("FBLogger")("ig_web").catching(a).warn("Error deleting story"),c("polarisLogAction")("delete_post_failed"),h==null?void 0:h()};e({onCompleted:j,onError:k,updater:a,variables:{mediaId:f}})},a[0]=e,a[1]=f):f=a[1];f=f;var g;a[2]!==f||a[3]!==b?(g=[f,b],a[2]=f,a[3]=b,a[4]=g):g=a[4];return g}g["default"]=a}),98);
__d("PolarisStoriesV3DeleteMediaContextProvider.react",["CometRelay","PolarisReactRedux.react","PolarisStoriesV3DeleteMediaContext","PolarisStoriesV3DeleteMediaContextProvider_reel.graphql","PolarisStoriesV3DeleteMediaContextProvider_viewer.graphql","react","usePolarisStoriesV3DeleteMediaMutation"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react"));e=j;var l=e.useCallback,m=e.useMemo,n=e.useState;function a(a){var e=a.children,f=a.reel;a=a.viewer;var g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3DeleteMediaContextProvider_reel.graphql"),f),j=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3DeleteMediaContextProvider_viewer.graphql"),a);f=n([]);var o=f[0],p=f[1];a=c("usePolarisStoriesV3DeleteMediaMutation")();var q=a[0],r=d("PolarisReactRedux.react").useDispatch(),s=l(function(a){var b=a.mediaId,c=a.mediaPk,d=a.onComplete,e=a.onError;a=function(){p(function(a){return[].concat(a,[c])}),r({ownerId:j.user.id,postId:c,type:"DELETE_POST_SUCCEEDED"}),d==null?void 0:d()};var f=function(){e==null?void 0:e()};q({mediaId:b,mediaPk:c,onComplete:a,onError:f,reelId:g.id})},[q,r,g.id,j.user.id]),t=l(function(a){return o.includes(a)},[o]);f=m(function(){return{deleteMedia:s,isDeleted:t}},[s,t]);return k.jsx(c("PolarisStoriesV3DeleteMediaContext").Provider,{value:f,children:e})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("PolarisStoriesV3DesktopAdGalleryItem_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3DesktopAdGalleryItem_ad",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdGalleryPlayerContainer_ad"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdGalleryPreview_ad"}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3DesktopAdGalleryItem_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3DesktopAdGalleryItem_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdGalleryPlayerContainer_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3DesktopGalleryItemVariant",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum").Mirrored(["Player","Preview","Offscreen"]);c=a;f["default"]=c}),66);
__d("PolarisStoriesV3GalleryItemGlimmer.react",["IGDSGlimmer.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={root:{backgroundColor:"x1wunsqr",borderStartStartRadius:"x1obq294",borderStartEndRadius:"x5a5i1n",borderEndEndRadius:"xde0f50",borderEndStartRadius:"x15x8krk",height:"x5yr21d",width:"xh8yej3",$$css:!0}};function a(){var a=d("react-compiler-runtime").c(1),b;a[0]===Symbol["for"]("react.memo_cache_sentinel")?(b=i.jsx(c("IGDSGlimmer.react"),{index:0,xstyle:j.root}),a[0]=b):b=a[0];return b}g["default"]=a}),98);
__d("PolarisStoriesV3DesktopAdGalleryItem.react",["CometRelay","PolarisStoriesV3AdGalleryPlayerContainer.react","PolarisStoriesV3AdGalleryPreview.react","PolarisStoriesV3DesktopAdGalleryItem_ad.graphql","PolarisStoriesV3DesktopAdGalleryItem_viewer.graphql","PolarisStoriesV3DesktopGalleryItemVariant","PolarisStoriesV3GalleryItemGlimmer.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function l(a){var b=d("react-compiler-runtime").c(7),e=a.ad,f=a.mediaId,g=a.onPreviewPress,h=a.variant;a=a.viewer;switch(h){case c("PolarisStoriesV3DesktopGalleryItemVariant").Preview:b[0]!==e||b[1]!==g?(h=e==null?k.jsx(c("PolarisStoriesV3GalleryItemGlimmer.react"),{}):k.jsx(c("PolarisStoriesV3AdGalleryPreview.react"),{ad:e,onPress:g}),b[0]=e,b[1]=g,b[2]=h):h=b[2];return h;case c("PolarisStoriesV3DesktopGalleryItemVariant").Player:b[3]!==e||b[4]!==f||b[5]!==a?(g=e==null?k.jsx(c("PolarisStoriesV3GalleryItemGlimmer.react"),{}):k.jsx(c("PolarisStoriesV3AdGalleryPlayerContainer.react"),{ad:e,mediaId:f,viewer:a}),b[3]=e,b[4]=f,b[5]=a,b[6]=g):g=b[6];return g;case c("PolarisStoriesV3DesktopGalleryItemVariant").Offscreen:return null}}function a(a){var c=d("react-compiler-runtime").c(6),e=a.ad,f=a.mediaId,g=a.onPreviewPress,j=a.variant;a=a.viewer;e=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3DesktopAdGalleryItem_ad.graphql"),e);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3DesktopAdGalleryItem_viewer.graphql"),a);var m;c[0]!==e||c[1]!==f||c[2]!==g||c[3]!==j||c[4]!==a?(m=k.jsx(l,{ad:e,mediaId:f,onPreviewPress:g,variant:j,viewer:a}),c[0]=e,c[1]=f,c[2]=g,c[3]=j,c[4]=a,c[5]=m):m=c[5];return m}g["default"]=a}),98);
__d("usePolarisStoriesV3DesktopGalleryItemAnimation",["Locale","react","react-compiler-runtime","usePrevious"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useLayoutEffect,j=b.useRef,k=650,l="cubic-bezier(0.34, 0.84, 0.17, 1)";function m(a,b){return b===0?a.player:a.preview}function n(a,b){var d=(c("Locale").isRTL()?-1:1)*b,e=a.gallery.width/2,f=(e-a.player.width/2-a.previewCount*a.preview.width)/Math.ceil(a.previewCount+.5);if(b===0)return e;else{b=a.player.width/2+Math.abs(d)*f+(Math.abs(d)-.5)*a.preview.width;return e+(d<0?-1:1)*b}}function a(a,b){var e=d("react-compiler-runtime").c(19),f=j(),g;e[0]!==a||e[1]!==b?(g=m(a,b),e[0]=a,e[1]=b,e[2]=g):g=e[2];g=g;var h=g.height;g=g.width;var o;e[3]!==a||e[4]!==b?(o=Math.round(n(a,b)),e[3]=a,e[4]=b,e[5]=o):o=e[5];var p=o,q=c("usePrevious")(p),r=c("usePrevious")(b),s;e[6]!==a||e[7]!==b||e[8]!==r||e[9]!==q||e[10]!==p?(o=function(){var c=f.current;if(c==null||q==null||r==null||r===b)return;var d=1;r===0?d=1/a.previewScale:r!==0&&b===0&&(d=a.previewScale);d="translateX(calc("+q+"px - 50%)) scale("+d+")";var e="translateX(calc("+p+"px - 50%))";c.animate([{transform:d},{transform:e}],{duration:k,easing:l})},s=[a,q,p,r,b],e[6]=a,e[7]=b,e[8]=r,e[9]=q,e[10]=p,e[11]=o,e[12]=s):(o=e[11],s=e[12]);i(o,s);o="translateX(calc("+p+"px - 50%))";e[13]!==h||e[14]!==o||e[15]!==g?(s={height:h,left:0,position:"absolute",transform:o,width:g},e[13]=h,e[14]=o,e[15]=g,e[16]=s):s=e[16];h=s;e[17]!==h?(o=[h,f],e[17]=h,e[18]=o):o=e[18];return o}g["default"]=a}),98);
__d("PolarisStoriesV3DesktopGalleryItem.react",["react","react-compiler-runtime","usePolarisStoriesV3DesktopGalleryItemAnimation"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(4),e=a.children,f=a.config;a=a.position;f=c("usePolarisStoriesV3DesktopGalleryItemAnimation")(f,a);a=f[0];f=f[1];var g;b[0]!==e||b[1]!==f||b[2]!==a?(g=i.jsx("div",{ref:f,style:a,children:e}),b[0]=e,b[1]=f,b[2]=a,b[3]=g):g=b[3];return g}g["default"]=a}),98);
__d("PolarisStoriesV3DesktopGalleryLayout.react",["PolarisStoriesV3ViewerSizeContext","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));h.useMemo;function a(a){var b=d("react-compiler-runtime").c(13),e=a.children;a=a.config;var f;b[0]!==a.gallery.height||b[1]!==a.gallery.width?(f={height:a.gallery.height,width:a.gallery.width},b[0]=a.gallery.height,b[1]=a.gallery.width,b[2]=f):f=b[2];f=f;var g;b[3]!==a.player.height||b[4]!==a.player.width?(g={height:a.player.height,width:a.player.width},b[3]=a.player.height,b[4]=a.player.width,b[5]=g):g=b[5];a=g;g=a;b[6]===Symbol["for"]("react.memo_cache_sentinel")?(a="x6s0dn4 x78zum5 x1n2onr6",b[6]=a):a=b[6];b[7]!==e||b[8]!==f?(a=i.jsx("div",{className:a,style:f,children:e}),b[7]=e,b[8]=f,b[9]=a):a=b[9];b[10]!==a||b[11]!==g?(e=i.jsx(c("PolarisStoriesV3ViewerSizeContext").Provider,{value:g,children:a}),b[10]=a,b[11]=g,b[12]=e):e=b[12];return e}g["default"]=a}),98);
__d("PolarisStoriesV3DesktopReelGalleryItem_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3DesktopReelGalleryItem_reel",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3GalleryPlayer_reel"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelGalleryPreview_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3DesktopReelGalleryItem_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3DesktopReelGalleryItem_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3GalleryPlayer_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3GalleryPlayer_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3GalleryPlayer_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPlayerContainer_media"}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelLoggingContextProvider_reels"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPlayerContainer_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3GalleryPlayer_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3GalleryPlayer_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelLoggingContextProvider_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPlayerContainer_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MediaImpressionWrapper_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MediaImpressionWrapper_media",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3OnePixelImpression_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3VPVDImpression_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MediaImpressionWrapper_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MediaImpressionWrapper_reel",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3VPVDImpression_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisMediaImpressionLogger_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null};return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisMediaImpressionLogger_media",selections:[a,{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[a],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"inventory_source",storageKey:null}],type:"XDTMediaDict",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisMediaImpressionLogger",["CometRelay","InstagramOrganicImpressionFalcoEvent","PolarisNavChain","react-compiler-runtime","usePolarisMediaImpressionLogger_media.graphql","useSinglePartialViewImpression"],(function(a,b,c,d,e,f,g){"use strict";var h,i=new Set();function a(a,e,f){var g=d("react-compiler-runtime").c(6),j=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisMediaImpressionLogger_media.graphql"),a),k=j.pk,l=(a=j.user)==null?void 0:a.pk;g[0]!==e||g[1]!==j.inventory_source||g[2]!==f||g[3]!==k||g[4]!==l?(a={onImpressionStart:function(){if(k==null||l==null)return;var a=String(e!=null?e:"")+"_"+k;if(i.has(a))return;i.add(a);c("InstagramOrganicImpressionFalcoEvent").log(function(){var a;return babelHelpers["extends"]({inventory_source:j.inventory_source,m_pk:k+"_"+l,module_name:String(e),nav_chain:(a=c("PolarisNavChain").getInstance())==null?void 0:a.getNavChainForSend(),pigeon_reserved_keyword_module:String(e)},f)})}},g[0]=e,g[1]=j.inventory_source,g[2]=f,g[3]=k,g[4]=l,g[5]=a):a=g[5];return c("useSinglePartialViewImpression")(a)}g["default"]=a}),98);
__d("usePolarisStoriesV3OnePixelImpression_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3OnePixelImpression_media",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisMediaImpressionLogger_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3OnePixelImpression",["CometRelay","react-compiler-runtime","usePolarisMediaImpressionLogger","usePolarisStoriesV3OnePixelImpression_media.graphql","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){var e=d("react-compiler-runtime").c(8);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3OnePixelImpression_media.graphql"),a);var f=c("usePolarisStoriesV3ViewerLoggingContext")(),g=c("usePolarisStoriesV3ReelLoggingContext")(),i=f.module,j=g.reel.id,k;e[0]!==g.reel.id||e[1]!==f?(k=f.getTrayPosition(g.reel.id),e[0]=g.reel.id,e[1]=f,e[2]=k):k=e[2];k=String(k);e[3]!==g.reel.id||e[4]!==k||e[5]!==f.traySessionId||e[6]!==f.viewerSessionId?(j={reel_id:j,reel_viewer_position:k,tray_session_id:f.traySessionId,viewer_session_id:f.viewerSessionId},e[3]=g.reel.id,e[4]=k,e[5]=f.traySessionId,e[6]=f.viewerSessionId,e[7]=j):j=e[7];return c("usePolarisMediaImpressionLogger")(a,i,j)}g["default"]=a}),98);
__d("usePolarisStoriesV3VPVDImpression_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3VPVDImpression_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3VPVDImpression_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3VPVDImpression_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ReelMediaIds_medias"}],storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3VPVDImpression",["CometRelay","InstagramOrganicVpvdImpFalcoEvent","PolarisNavChain","react","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3ReelMediaIds","usePolarisStoriesV3VPVDImpression_media.graphql","usePolarisStoriesV3VPVDImpression_reel.graphql","usePolarisStoriesV3ViewerLoggingContext","useStable","useVPVDImpression"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(j||d("react")).useCallback,l=new Set();function a(a,e){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3VPVDImpression_reel.graphql"),a);a=a.items;e=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3VPVDImpression_media.graphql"),e);var g=c("useStable")(function(){var a;return(a=c("PolarisNavChain").getInstance())==null?void 0:a.getNavChainForSend()}),j=c("usePolarisStoriesV3ViewerLoggingContext")(),m=c("usePolarisStoriesV3ReelLoggingContext")();a=c("usePolarisStoriesV3ReelMediaIds")(a);var n=a.indexOf(e.pk);a=k(function(b){var d=b.visibleDuration,a=j.module,e=j.traySessionId,h=j.viewerSessionId;b=m.mediaId;var i=m.mediaPk,k=m.pogImpressions,o=m.reel,p=l.has(b);l.add(b);var q=!k.has(o.id);k.add(o.id);c("InstagramOrganicVpvdImpFalcoEvent").log(function(){return{client_sub_impression:p,is_stories_pog_impression:q,m_pk:i,max_duration_ms:d,module_name:String(a),nav_chain:g,pigeon_reserved_keyword_module:String(a),reel_id:o.id,reel_position:String(n),sum_duration_ms:d,tray_session_id:e,viewer_session_id:h}})},[j,m,n,g]);e=c("useVPVDImpression")({onVPVDEnd:a});a=e[0];return a}g["default"]=a}),98);
__d("PolarisStoriesV3MediaImpressionWrapper.react",["CometRelay","PolarisStoriesV3MediaImpressionWrapper_media.graphql","PolarisStoriesV3MediaImpressionWrapper_reel.graphql","react","react-compiler-runtime","useMergeRefs","usePolarisStoriesV3OnePixelImpression","usePolarisStoriesV3VPVDImpression"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a){var e=d("react-compiler-runtime").c(3),f=a.children,g=a.media;a=a.reel;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3MediaImpressionWrapper_reel.graphql"),a);g=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3MediaImpressionWrapper_media.graphql"),g);var j=c("usePolarisStoriesV3OnePixelImpression")(g);a=c("usePolarisStoriesV3VPVDImpression")(a,g);g=c("useMergeRefs")(j,a);e[0]!==f||e[1]!==g?(j=k.jsx("div",{ref:g,children:f}),e[0]=f,e[1]=g,e[2]=j):j=e[2];return j}g["default"]=a}),98);
__d("PolarisStoriesV3FooterCTA_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"};return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3FooterCTA_media",selections:[a,{alias:null,args:null,concreteType:"XDTStoryCTADict",kind:"LinkedField",name:"story_cta",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTAdLink",kind:"LinkedField",name:"links",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"webUri",storageKey:null}],storageKey:null}],storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[a],storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null}}();e.exports=a}),null);
__d("PolarisStoriesV3FooterCTA.react",["fbt","CometRelay","IGDSTextVariants.react","PolarisExternalLink.react","PolarisStoriesV3FooterCTA_media.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||d("react");function a(a){var e,f=d("react-compiler-runtime").c(6);a=a.media;a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3FooterCTA_media.graphql"),a);e=(e=a.story_cta)==null?void 0:(e=e[0].links)==null?void 0:e[0].webUri;if(e==null)return null;var g=a.user.pk,j;f[0]===Symbol["for"]("react.memo_cache_sentinel")?(j="x1lku1pv",f[0]=j):j=f[0];var l;f[1]===Symbol["for"]("react.memo_cache_sentinel")?(l=k.jsx(d("IGDSTextVariants.react").IGDSTextBody,{color:"textOnMedia",children:h._(/*BTDS*/"See more")}),f[1]=l):l=f[1];f[2]!==a.pk||f[3]!==a.user.pk||f[4]!==e?(g=k.jsx(c("PolarisExternalLink.react"),{author_id:g,className:j,href:e,media_pk:a.pk,page_id:"StoriesPage",children:l}),f[2]=a.pk,f[3]=a.user.pk,f[4]=e,f[5]=g):g=f[5];return g}g["default"]=a}),226);
__d("PolarisStoriesV3LikeButton_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3LikeButton_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"has_liked",storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3LikeMutation_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3LikeMutationLikeMutation_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9867679176603930"}),null);
__d("usePolarisStoriesV3LikeMutationLikeMutation.graphql",["usePolarisStoriesV3LikeMutationLikeMutation_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"mediaId"}],c=[{alias:null,args:[{fields:[{kind:"Variable",name:"media_id",variableName:"mediaId"}],kind:"ObjectValue",name:"data"}],concreteType:"XDTEmptyRecord",kind:"LinkedField",name:"xdt_api__v1__story_interactions__send_story_like",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"usePolarisStoriesV3LikeMutationLikeMutation",selections:c,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"usePolarisStoriesV3LikeMutationLikeMutation",selections:c},params:{id:b("usePolarisStoriesV3LikeMutationLikeMutation_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__story_interactions__send_story_like"]},name:"usePolarisStoriesV3LikeMutationLikeMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("usePolarisStoriesV3LikeMutationUnlikeMutation_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="29195167593432358"}),null);
__d("usePolarisStoriesV3LikeMutationUnlikeMutation.graphql",["usePolarisStoriesV3LikeMutationUnlikeMutation_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"mediaId"}],c=[{alias:null,args:[{fields:[{kind:"Variable",name:"media_id",variableName:"mediaId"}],kind:"ObjectValue",name:"data"}],concreteType:"XDTEmptyRecord",kind:"LinkedField",name:"xdt_api__v1__story_interactions__unsend_story_like",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"usePolarisStoriesV3LikeMutationUnlikeMutation",selections:c,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"usePolarisStoriesV3LikeMutationUnlikeMutation",selections:c},params:{id:b("usePolarisStoriesV3LikeMutationUnlikeMutation_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__story_interactions__unsend_story_like"]},name:"usePolarisStoriesV3LikeMutationUnlikeMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("usePolarisStoriesV3LikeMutation_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3LikeMutation_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"has_liked",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3LikeMutation",["CometRelay","polarisGetXDTMediaDict","react","react-compiler-runtime","usePolarisStoriesV3LikeMutationLikeMutation.graphql","usePolarisStoriesV3LikeMutationUnlikeMutation.graphql","usePolarisStoriesV3LikeMutation_media.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k;(k||d("react")).useCallback;function l(a,b){return function(d){d=c("polarisGetXDTMediaDict")(d,a);d==null?void 0:d.setValue(b,"has_liked")}}function a(a){var c=d("react-compiler-runtime").c(8),e=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3LikeMutation_media.graphql"),a);c[0]===Symbol["for"]("react.memo_cache_sentinel")?(a=i!==void 0?i:i=b("usePolarisStoriesV3LikeMutationLikeMutation.graphql"),c[0]=a):a=c[0];a=d("CometRelay").useMutation(a);var f=a[0];c[1]===Symbol["for"]("react.memo_cache_sentinel")?(a=j!==void 0?j:j=b("usePolarisStoriesV3LikeMutationUnlikeMutation.graphql"),c[1]=a):a=c[1];a=d("CometRelay").useMutation(a);var g=a[0];c[2]!==f||c[3]!==g||c[4]!==e.has_liked||c[5]!==e.id||c[6]!==e.pk?(a=function(){if(e.has_liked!==!0){var a=l(e.id,!0);f({optimisticUpdater:a,updater:a,variables:{mediaId:e.pk}})}else{a=l(e.pk,!1);g({optimisticUpdater:a,updater:a,variables:{mediaId:e.pk}})}},c[2]=f,c[3]=g,c[4]=e.has_liked,c[5]=e.id,c[6]=e.pk,c[7]=a):a=c[7];return a}g["default"]=a}),98);
__d("PolarisStoriesV3LikeButton.react",["CometRelay","PolarisLikeButton.react","PolarisStoriesV3LikeButton_media.graphql","react","react-compiler-runtime","stylex","usePolarisStoriesV3LikeMutation"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a){var e=d("react-compiler-runtime").c(6),f=a.media;a=a.xstyle;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3LikeButton_media.graphql"),f);var g=c("usePolarisStoriesV3LikeMutation")(f),j;e[0]!==a?(j=(i||(i=c("stylex")))(a),e[0]=a,e[1]=j):j=e[1];f=(a=f.has_liked)!=null?a:!1;e[2]!==g||e[3]!==j||e[4]!==f?(a=k.jsx(c("PolarisLikeButton.react"),{className:j,color:"ig-stroke-on-media",isLiked:f,onChange:g}),e[2]=g,e[3]=j,e[4]=f,e[5]=a):a=e[5];return a}g["default"]=a}),98);
__d("PolarisStoriesV3ReelFooter_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelFooter_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3FooterCTA_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3LikeButton_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ViewerListButton_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ShareButton_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CanLike_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CanReply_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CanReshare_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3Replies_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelFooter_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelFooter_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ShareButton_user"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_user"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReplyInput_reel"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3Replies_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelFooter_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelFooter_viewer",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CanLike_viewer"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CanReply_viewer"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CanReshare_viewer"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReplyInput_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReplyInput_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("useIsIGDDisappearingMessagesEnabled",["DisappearingMessagesMode","I64","ReQL","ReQLSuspense","react-compiler-runtime","useReStore"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a){var b=d("react-compiler-runtime").c(12),e=(i||(i=c("useReStore")))(),g;b[0]!==e.tables.ig_contact_info||b[1]!==a?(g=function(){return d("ReQL").fromTableAscending(e.tables.ig_contact_info).filter(function(b){return(b==null?void 0:b.igId)===a})},b[0]=e.tables.ig_contact_info,b[1]=a,b[2]=g):g=b[2];var j;b[3]!==e||b[4]!==a?(j=[e,a],b[3]=e,b[4]=a,b[5]=j):j=b[5];g=d("ReQLSuspense").useFirst(g,j,f.id+":24");var k=(j=g==null?void 0:g.contactId)!=null?j:(h||(h=d("I64"))).zero;b[6]!==e.tables.ig_thread_info||b[7]!==k?(g=function(){return d("ReQL").fromTableAscending(e.tables.ig_thread_info,["igDmSettingsMode","igThreadId"]).getKeyRange(k).take(1)},b[6]=e.tables.ig_thread_info,b[7]=k,b[8]=g):g=b[8];b[9]!==e||b[10]!==k?(j=[e,k],b[9]=e,b[10]=k,b[11]=j):j=b[11];b=d("ReQLSuspense").useFirst(g,j,f.id+":34");j=(h||(h=d("I64"))).to_int32((g=b==null?void 0:b.igDmSettingsMode)!=null?g:(h||(h=d("I64"))).zero);return j===c("DisappearingMessagesMode").ON}g["default"]=a}),98);
__d("PolarisStoriesV3ReplyInput.react",["CometRelay","IGDSButton.react","Keys","PolarisDirectStrings","PolarisStoriesV3ReplyInput_reel.graphql","PolarisStoryStrings","PolarisUA","ReactTextareaAutosizeWWWIG","getTextDirection","react","react-compiler-runtime","useIsIGDDisappearingMessagesEnabled","usePolarisStoriesV3ReplyInputContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={send:{height:"x5kalc8",marginInlineEnd:"x1xegmmw",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(28),f=a.onChange,g=a.onSubmit,i=a.reel,l=a.ref;a=a.value;i=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReplyInput_reel.graphql"),i);var m=a==="",n=a.trim()==="",o=c("useIsIGDDisappearingMessagesEnabled")(i.user.pk),p;e[0]!==i.user.username?(p=d("PolarisUA").isDesktop()?d("PolarisStoryStrings").directReplyPlaceholder(i.user.username):d("PolarisDirectStrings").SEND_MESSAGE_STRING_V2,e[0]=i.user.username,e[1]=p):p=e[1];i=p;e[2]!==f?(p=function(a){f(a.target.value)},e[2]=f,e[3]=p):p=e[3];p=p;var q,r;e[4]!==m||e[5]!==g?(q=function(a){d("PolarisUA").isDesktop()&&a.which===c("Keys").RETURN&&!a.shiftKey&&r(a)},r=function(a){a.preventDefault();if(m)return;g()},e[4]=m,e[5]=g,e[6]=q,e[7]=r):(q=e[6],r=e[7]);var s=c("usePolarisStoriesV3ReplyInputContext")(),t;e[8]!==a?(t=c("getTextDirection")(a),e[8]=a,e[9]=t):t=e[9];t=t;var u;e[10]!==o?(u={0:{className:"x6s0dn4 xl4qmuc xm4i03v xd18jyu xrgb9v1 x1ua1ujl xksyday xshg46c xlej2ay x13fuv20 x18b5jzi x1q0q8m5 x1t7ytsu x178xt8z x1lun4ml xso031l xpilrb4 x9f619 x78zum5 x1gg8mnh xf159sx x1iwz3mf"},2:{className:"x6s0dn4 xl4qmuc xm4i03v xd18jyu xrgb9v1 x13fuv20 x18b5jzi x1q0q8m5 x1t7ytsu x178xt8z x1lun4ml xso031l xpilrb4 x9f619 x78zum5 x1gg8mnh xf159sx x1iwz3mf x1iyc4bg x1rifc88 x916alf x1seylsq"},1:{className:"x6s0dn4 xl4qmuc xm4i03v xd18jyu xrgb9v1 x1ua1ujl xksyday xshg46c xlej2ay x178xt8z x1lun4ml xso031l xpilrb4 x9f619 x78zum5 x1gg8mnh xf159sx x1iwz3mf xlya59e xwy3id5 xpvcztv x11nt7xy"},3:{className:"x6s0dn4 xl4qmuc xm4i03v xd18jyu xrgb9v1 x178xt8z x1lun4ml xso031l xpilrb4 x9f619 x78zum5 x1gg8mnh xf159sx x1iwz3mf x1iyc4bg x1rifc88 x916alf x1seylsq xlya59e xwy3id5 xpvcztv x11nt7xy"}}[!!d("PolarisUA").isDesktop()<<1|!!o<<0],e[10]=o,e[11]=u):u=e[11];e[12]===Symbol["for"]("react.memo_cache_sentinel")?(o="x1i10hfl xjbqb8w x972fbf x10w94by x1qhh985 x14e42zd x7e90pr x2fvf9 x1a2a7pz xw2csxc x1odjw0f x1y1aw1k xrw5ot4 xwib8y2 x7coems xtt52l0 xh8yej3 xomwbyg",e[12]=o):o=e[12];e[13]!==p||e[14]!==q||e[15]!==t||e[16]!==i||e[17]!==s.inputRef||e[18]!==a?(o=j.jsx(c("ReactTextareaAutosizeWWWIG"),{autoComplete:"off",className:o,dir:t,maxRows:5,onChange:p,onKeyDown:q,placeholder:i,ref:s.inputRef,value:a}),e[13]=p,e[14]=q,e[15]=t,e[16]=i,e[17]=s.inputRef,e[18]=a,e[19]=o):o=e[19];e[20]!==r||e[21]!==n?(p=!n&&j.jsx(c("IGDSButton.react"),{label:d("PolarisDirectStrings").SEND_BUTTON_STRING,onClick:r,variant:"white_link",xstyle:k.send}),e[20]=r,e[21]=n,e[22]=p):p=e[22];e[23]!==l||e[24]!==u||e[25]!==o||e[26]!==p?(q=j.jsxs("div",babelHelpers["extends"]({},u,{ref:l,children:[o,p]})),e[23]=l,e[24]=u,e[25]=o,e[26]=p,e[27]=q):q=e[27];return q}g["default"]=a}),98);
__d("PolarisStoriesV3ReplyOverlay.react",["PolarisStoriesV3PauseReason","react","react-compiler-runtime","usePolarisStoriesV3KeyCommandsContext","usePolarisStoriesV3PausePlayer"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react")),j=h.useEffect;function a(){var a=d("react-compiler-runtime").c(3);c("usePolarisStoriesV3PausePlayer")(c("PolarisStoriesV3PauseReason").Reply);var b=c("usePolarisStoriesV3KeyCommandsContext")(),e=b.disableKeyCommands;a[0]!==e?(b=function(){var a=e();return function(){return a()}},a[0]=e,a[1]=b):b=a[1];j(b,void 0);a[2]===Symbol["for"]("react.memo_cache_sentinel")?(b=i.jsx("div",{className:"x1b2oxx8 x1ey2m1c xtijo5x x1o0tod xbyyjgo x10l6tqk x13vifvy"}),a[2]=b):b=a[2];return b}g["default"]=a}),98);
__d("PolarisStoriesV3ViewerListButton_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ViewerListButton_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"viewer_count",storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"viewers",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null}],storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ViewerListButton.react",["fbt","CometPressable.react","CometRelay","IGDSText.react","JSResourceForInteraction","PolarisBigNumber.react","PolarisFacepile.next.react","PolarisStoriesV3ViewerListButton_media.graphql","react","react-compiler-runtime","useIGDSLazyDialog"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||d("react"),l=c("JSResourceForInteraction")("PolarisStoriesV3ViewerListDialog.react").__setRef("PolarisStoriesV3ViewerListButton.react"),m=3,n={root:{alignItems:"x6s0dn4",display:"x78zum5",flexDirection:"xdt5ytf",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(18),f=a.media;a=a.xstyle;var g=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ViewerListButton_media.graphql"),f);f=c("useIGDSLazyDialog")(l);var j=f[0];f=(f=g.viewer_count)!=null?f:0;if(f===0)return null;if(e[0]!==g.viewers){var p;p=((p=g.viewers)!=null?p:[]).slice(0,m).map(o);e[0]=g.viewers;e[1]=p}else p=e[1];p=p;var q;e[2]!==g.pk||e[3]!==j?(q=function(){j({postId:g.pk})},e[2]=g.pk,e[3]=j,e[4]=q):q=e[4];q=q;var r;e[5]!==a?(r=[n.root,a],e[5]=a,e[6]=r):r=e[6];e[7]===Symbol["for"]("react.memo_cache_sentinel")?(a={className:"xzueoph"},e[7]=a):a=e[7];e[8]!==q||e[9]!==p?(a=k.jsx("div",babelHelpers["extends"]({},a,{children:k.jsx(c("PolarisFacepile.next.react"),{border:"transparent",onClick:q,userProfilePicUrls:p})})),e[8]=q,e[9]=p,e[10]=a):a=e[10];e[11]!==f?(p=k.jsx(c("IGDSText.react"),{color:"textOnMedia",size:"miniscule",weight:"light",children:h._(/*BTDS*/"Seen by {count}",[h._param("count",k.jsx(c("PolarisBigNumber.react"),{value:f}))])}),e[11]=f,e[12]=p):p=e[12];e[13]!==q||e[14]!==r||e[15]!==a||e[16]!==p?(f=k.jsxs(c("CometPressable.react"),{onPress:q,overlayDisabled:!0,xstyle:r,children:[a,p]}),e[13]=q,e[14]=r,e[15]=a,e[16]=p,e[17]=f):f=e[17];return f}function o(a){return(a=a.profile_pic_url)!=null?a:""}g["default"]=a}),226);
__d("usePolarisStoriesV3CanLike_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3CanLike_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_user"},{alias:null,args:null,kind:"ScalarField",name:"interop_messaging_user_fbid",storageKey:null}],storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3CanLike_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3CanLike_viewer",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3CanLike",["CometRelay","IGDInstamadilloUtils","usePolarisStoriesV3CanLike_media.graphql","usePolarisStoriesV3CanLike_viewer.graphql","usePolarisStoriesV3ViewingOwnStory","useThread"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,e){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3CanLike_media.graphql"),a);e=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3CanLike_viewer.graphql"),e);e=c("usePolarisStoriesV3ViewingOwnStory")(a.user,e);a=c("useThread")((a=a.user.interop_messaging_user_fbid)!=null?a:"");var f=d("IGDInstamadilloUtils").isInstamadilloHardblockEnabled(a);return!e&&a!=null&&!f}g["default"]=a}),98);
__d("usePolarisStoriesV3CanReply_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3CanReply_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"can_reply",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_user"},{alias:null,args:null,kind:"ScalarField",name:"interop_messaging_user_fbid",storageKey:null}],storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3CanReply_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3CanReply_viewer",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ViewingOwnStory_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3CanReply",["CometRelay","IGDInstamadilloUtils","usePolarisStoriesV3CanReply_media.graphql","usePolarisStoriesV3CanReply_viewer.graphql","usePolarisStoriesV3ViewingOwnStory","useThread"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,e){var f;a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3CanReply_media.graphql"),a);e=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3CanReply_viewer.graphql"),e);e=c("usePolarisStoriesV3ViewingOwnStory")(a.user,e);f=c("useThread")((f=a.user.interop_messaging_user_fbid)!=null?f:"");var g=d("IGDInstamadilloUtils").isInstamadilloHardblockEnabled(f);return!e&&a.can_reply===!0&&f!=null&&!g}g["default"]=a}),98);
__d("usePolarisStoriesV3LogReelComposeMessage",["ReelComposeMessageFalcoEvent","react","react-compiler-runtime","usePolarisStoriesV3GetSharedLoggingData","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var b=d("react-compiler-runtime").c(3),e=c("usePolarisStoriesV3ViewerLoggingContext")(),a=e.module,g=c("usePolarisStoriesV3GetSharedLoggingData")();b[0]!==g||b[1]!==a?(e=function(){c("ReelComposeMessageFalcoEvent").log(function(){return babelHelpers["extends"]({},g(),{module_name:String(a)})})},b[0]=g,b[1]=a,b[2]=e):e=b[2];return e}g["default"]=a}),98);
__d("ReelSendMessageFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("3123");b=d("FalcoLoggerInternal").create("reel_send_message",a);e=b;g["default"]=e}),98);
__d("usePolarisStoriesV3LogReelSendMessage",["ReelSendMessageFalcoEvent","react","react-compiler-runtime","usePolarisStoriesV3GetSharedLoggingData","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var b=d("react-compiler-runtime").c(3),e=c("usePolarisStoriesV3GetSharedLoggingData")(),g=c("usePolarisStoriesV3ViewerLoggingContext")(),a=g.module;b[0]!==e||b[1]!==a?(g=function(){c("ReelSendMessageFalcoEvent").log(function(){return babelHelpers["extends"]({},e(),{module_name:String(a)})})},b[0]=e,b[1]=a,b[2]=g):g=b[2];return g}g["default"]=a}),98);
__d("usePolarisStoriesV3MessageReplyLoggerFactory",["react","react-compiler-runtime","usePolarisStoriesV3LogReelComposeMessage","usePolarisStoriesV3LogReelSendMessage"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(3),b=c("usePolarisStoriesV3LogReelComposeMessage")(),e=c("usePolarisStoriesV3LogReelSendMessage")(),f;a[0]!==b||a[1]!==e?(f=function(){var a=!1;return{logComposerDismiss:function(){a||e(!1)},logComposerOpen:function(){b()},logMessageSend:function(){e(!0),a=!0}}},a[0]=b,a[1]=e,a[2]=f):f=a[2];return f}g["default"]=a}),98);
__d("usePolarisStoriesV3Replies_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3Replies_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3Replies_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3Replies_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"interop_messaging_user_fbid",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3Replies",["CometRelay","IGDDataclassTypes.flow","IGDInstamadilloUtils","IGDThreadTTLCUtils","PolarisDirectActionReactToStory","PolarisDirectActionReplyToStory","PolarisReactRedux.react","react","react-compiler-runtime","replyOrReactToStoryViaInstamadillo","usePolarisStoriesV3Replies_media.graphql","usePolarisStoriesV3Replies_reel.graphql","useThread"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;(j||d("react")).useMemo;function a(a,e){var f=d("react-compiler-runtime").c(18);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3Replies_reel.graphql"),a);var g=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3Replies_media.graphql"),e),j=d("PolarisReactRedux.react").useDispatch(),k=a.user.interop_messaging_user_fbid;e=c("useThread")(k);var l;f[0]!==e?(l=d("IGDThreadTTLCUtils").isInstamadilloTTLCReelShareEnabled(e),f[0]=e,f[1]=l):l=f[1];l=l;var m;f[2]!==l||f[3]!==e?(m=d("IGDInstamadilloUtils").isInstamadilloTransportEnabled(e)||l,f[2]=l,f[3]=e,f[4]=m):m=f[4];var n=m,o=l;f[5]!==e?(m=d("IGDInstamadilloUtils").isIGDDisappearingModeEnabled(e),f[5]=e,f[6]=m):m=f[6];var p=m,q=a.id,r=g.id,s=a.user.pk,t=a.user.username;f[7]!==j||f[8]!==k||f[9]!==p||f[10]!==n||f[11]!==g.pk||f[12]!==r||f[13]!==o||f[14]!==q||f[15]!==s||f[16]!==t?(l={sendMessage:function(a){n?c("replyOrReactToStoryViaInstamadillo")(q,g.pk,s,t,d("IGDDataclassTypes.flow").XmsgIgXmaActionType.Reply,k,p,o,a):j(d("PolarisDirectActionReplyToStory").replyToStory(s,q,r,a))},sendReaction:function(a){n?c("replyOrReactToStoryViaInstamadillo")(q,g.pk,s,t,d("IGDDataclassTypes.flow").XmsgIgXmaActionType.React,k,p,o,a):j(d("PolarisDirectActionReactToStory").reactToStory(s,q,r,a))}},f[7]=j,f[8]=k,f[9]=p,f[10]=n,f[11]=g.pk,f[12]=r,f[13]=o,f[14]=q,f[15]=s,f[16]=t,f[17]=l):l=f[17];e=l;return e}g["default"]=a}),98);
__d("PolarisStoriesV3ReelFooter.react",["CometErrorBoundary.react","CometPlaceholder.react","CometRelay","FocusWithinHandler.react","IGDSGlimmer.react","PolarisDirectStrings","PolarisStoriesV3Footer.react","PolarisStoriesV3FooterCTA.react","PolarisStoriesV3LikeButton.react","PolarisStoriesV3ReelFooter_media.graphql","PolarisStoriesV3ReelFooter_reel.graphql","PolarisStoriesV3ReelFooter_viewer.graphql","PolarisStoriesV3ReplyInput.react","PolarisStoriesV3ReplyOverlay.react","PolarisStoriesV3ShareButton.react","PolarisStoriesV3ViewerListButton.react","PolarisStoryReplyReaction.react","PolarisToastConstants","react","react-compiler-runtime","useIGDSToaster","usePolarisStoriesV3CanLike","usePolarisStoriesV3CanReply","usePolarisStoriesV3CanReshare","usePolarisStoriesV3MessageReplyLoggerFactory","usePolarisStoriesV3Replies","usePolarisStoriesV3ViewingOwnStory"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||(k=d("react"));e=k;e.useCallback;var m=e.useRef,n=e.useState,o={like:{marginInlineStart:"x13fj5qh",$$css:!0},reactionsHidden:{display:"x1s85apg",$$css:!0},viewers:{marginTop:"x1xmf6yo",marginInlineEnd:"x1xegmmw",marginBottom:"x1e56ztr",marginInlineStart:"x13fj5qh",$$css:!0}},p=d("PolarisToastConstants").TOAST_ANIM_DURATION_S*1e3;function a(a){var e=d("react-compiler-runtime").c(54),f=a.media,g=a.reel;a=a.viewer;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelFooter_reel.graphql"),g);f=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ReelFooter_media.graphql"),f);a=d("CometRelay").useFragment(j!==void 0?j:j=b("PolarisStoriesV3ReelFooter_viewer.graphql"),a);var k=n(!1),r=k[0],s=k[1];k=n("");var t=k[0],u=k[1];k=t.trim()==="";k=r&&k;var v=c("usePolarisStoriesV3CanLike")(f,a),w=c("usePolarisStoriesV3CanReply")(f,a),x=c("usePolarisStoriesV3CanReshare")(f,a);a=c("usePolarisStoriesV3ViewingOwnStory")(g.user,a);var y=c("usePolarisStoriesV3Replies")(g,f),z=y.sendMessage,A=y.sendReaction,B=c("usePolarisStoriesV3MessageReplyLoggerFactory")(),C=m(null),D=c("useIGDSToaster")();e[0]!==D?(y=function(){D.add({message:d("PolarisDirectStrings").SENT_TOAST_TEXT,target:"center"},{duration:p})},e[0]=D,e[1]=y):y=e[1];var E=y;e[2]!==E?(y=function(){var a;E();(a=C.current)==null?void 0:a.logMessageSend();(a=document.activeElement)==null?void 0:a.blur()},e[2]=E,e[3]=y):y=e[3];var F=y;e[4]!==B?(y=function(a){s(a);if(a){a=B();a.logComposerOpen();C.current=a}else{(a=C.current)==null?void 0:a.logComposerDismiss();C.current=null}},e[4]=B,e[5]=y):y=e[5];y=y;var G;e[6]!==F||e[7]!==t||e[8]!==z?(G=function(){z(t),u(""),F()},e[6]=F,e[7]=t,e[8]=z,e[9]=G):G=e[9];G=G;var H;e[10]!==F||e[11]!==A?(H=function(a){A(a),F()},e[10]=F,e[11]=A,e[12]=H):H=e[12];H=H;var I;e[13]!==r?(I=r&&l.jsx(c("PolarisStoriesV3ReplyOverlay.react"),{}),e[13]=r,e[14]=I):I=e[14];var J;e[15]===Symbol["for"]("react.memo_cache_sentinel")?(J={className:"x6s0dn4 x78zum5 x67bb7w"},e[15]=J):J=e[15];var K;e[16]!==f||e[17]!==a?(K=a&&l.jsx(c("PolarisStoriesV3ViewerListButton.react"),{media:f,xstyle:o.viewers}),e[16]=f,e[17]=a,e[18]=K):K=e[18];e[19]!==w||e[20]!==y||e[21]!==G||e[22]!==H||e[23]!==t||e[24]!==g||e[25]!==k?(a=w&&l.jsx("div",babelHelpers["extends"]({className:"x78zum5 x3ieub6 x1iyjqo2"},{children:l.jsx(c("CometErrorBoundary.react"),{fallback:q,children:l.jsx(c("CometPlaceholder.react"),{fallback:l.jsx(c("IGDSGlimmer.react"),{index:0}),children:l.jsxs(c("FocusWithinHandler.react"),{onFocusChange:y,children:[l.jsx(c("PolarisStoriesV3ReplyInput.react"),{onChange:u,onSubmit:G,reel:g,value:t}),l.jsx(c("PolarisStoryReplyReaction.react"),{onReactToStory:H,xstyle:!k&&o.reactionsHidden})]})})})})),e[19]=w,e[20]=y,e[21]=G,e[22]=H,e[23]=t,e[24]=g,e[25]=k,e[26]=a):a=e[26];e[27]!==r?(w={0:{className:"x78zum5 xvc5jky"},1:{className:"x78zum5 xvc5jky xnalus7"}}[!!r<<0],e[27]=r,e[28]=w):w=e[28];e[29]!==v||e[30]!==f?(y=v&&l.jsx(c("PolarisStoriesV3LikeButton.react"),{media:f,xstyle:o.like}),e[29]=v,e[30]=f,e[31]=y):y=e[31];e[32]!==x||e[33]!==f||e[34]!==g.id||e[35]!==g.user?(G=x&&l.jsx(c("PolarisStoriesV3ShareButton.react"),{media:f,reelId:g.id,user:g.user}),e[32]=x,e[33]=f,e[34]=g.id,e[35]=g.user,e[36]=G):G=e[36];e[37]!==w||e[38]!==y||e[39]!==G?(H=l.jsxs("div",babelHelpers["extends"]({},w,{children:[y,G]})),e[37]=w,e[38]=y,e[39]=G,e[40]=H):H=e[40];e[41]!==H||e[42]!==K||e[43]!==a?(k=l.jsxs("div",babelHelpers["extends"]({},J,{children:[K,a,H]})),e[41]=H,e[42]=K,e[43]=a,e[44]=k):k=e[44];e[45]===Symbol["for"]("react.memo_cache_sentinel")?(r={className:"x78zum5 xl56j7k x67bb7w"},e[45]=r):r=e[45];e[46]!==f?(v=l.jsx("div",babelHelpers["extends"]({},r,{children:l.jsx(c("PolarisStoriesV3FooterCTA.react"),{media:f})})),e[46]=f,e[47]=v):v=e[47];e[48]!==k||e[49]!==v?(x=l.jsxs(c("PolarisStoriesV3Footer.react"),{children:[k,v]}),e[48]=k,e[49]=v,e[50]=x):x=e[50];e[51]!==x||e[52]!==I?(g=l.jsxs(l.Fragment,{children:[I,x]}),e[51]=x,e[52]=I,e[53]=g):g=e[53];return g}function q(){return l.jsx("div",{})}q.displayName=q.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("PolarisStoriesV3HeaderOwner_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3HeaderOwner_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Timestamp_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3OwnerSubtitle_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3HeaderOwner_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3HeaderOwner_reel",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3OwnerAvatar_reel"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelOwnerTitle_reel"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3OwnerSubtitle_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3OwnerAvatar_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3OwnerAvatar_reel",selections:[{alias:null,args:null,kind:"ScalarField",name:"reel_type",storageKey:null},{alias:null,args:null,concreteType:"XDTReelCoverMediaClientDict",kind:"LinkedField",name:"cover_media",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTReelCoverMediaImageVersionClientDict",kind:"LinkedField",name:"cropped_image_version",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null}],storageKey:null}],storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null}],storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3OwnerAvatar.react",["CometRelay","PolarisStoriesV3OwnerAvatar_reel.graphql","PolarisUserAvatar.react","ReelType","XPolarisProfileControllerRouteBuilder","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e,f=d("react-compiler-runtime").c(9);a=a.reel;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3OwnerAvatar_reel.graphql"),a);var g=a.cover_media,i=a.reel_type;a=a.user;var k;f[0]!==a.username?(k=c("XPolarisProfileControllerRouteBuilder").buildUri({username:a.username}).toString(),f[0]=a.username,f[1]=k):k=f[1];k=k;if(f[2]!==(g==null?void 0:(e=g.cropped_image_version)==null?void 0:e.url)||f[3]!==i||f[4]!==a.profile_pic_url){e=c("ReelType").cast(i)==="highlight_reel"?(e=g==null?void 0:(e=g.cropped_image_version)==null?void 0:e.url)!=null?e:a.profile_pic_url:a.profile_pic_url;f[2]=g==null?void 0:(g=g.cropped_image_version)==null?void 0:g.url;f[3]=i;f[4]=a.profile_pic_url;f[5]=e}else e=f[5];g=e;f[6]!==g||f[7]!==k?(i=j.jsx(c("PolarisUserAvatar.react"),{profilePictureUrl:g,profileUrl:k,size:32}),f[6]=g,f[7]=k,f[8]=i):i=f[8];return i}g["default"]=a}),98);
__d("PolarisStoriesV3ReelOwnerTitle_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelOwnerTitle_reel",selections:[{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3OwnerTitle_owner"}],storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelOwnerTitle.react",["CometRelay","PolarisStoriesV3OwnerTitle.react","PolarisStoriesV3ReelOwnerTitle_reel.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(3);a=a.reel;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelOwnerTitle_reel.graphql"),a);var f=a.title,g=a.user;f=f!=null?f:g.username;e[0]!==a.user||e[1]!==f?(g=j.jsx(c("PolarisStoriesV3OwnerTitle.react"),{owner:a.user,title:f}),e[0]=a.user,e[1]=f,e[2]=g):g=e[2];return g}g["default"]=a}),98);
__d("PolarisStoriesV3Timestamp_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3Timestamp_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3Timestamp.react",["CometRelay","IGDSTextVariants.react","PolarisStoriesV3Timestamp_media.graphql","PolarisTimestamp.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(3);a=a.media;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3Timestamp_media.graphql"),a);var f;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(f="x197sbye xuxw1ft",e[0]=f):f=e[0];a=Number(a.taken_at);e[1]!==a?(f=j.jsx(d("IGDSTextVariants.react").IGDSTextBody,{color:"textOnMedia",children:j.jsx(c("PolarisTimestamp.react"),{className:f,value:a})}),e[1]=a,e[2]=f):f=e[2];return f}g["default"]=a}),98);
__d("PolarisStoriesV3ImmersiveReshareAttribution_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ImmersiveReshareAttribution_user",selections:[{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ImmersiveReshareAttribution.react",["CometRelay","PolarisStoriesImmersiveReshareAttribution.react","PolarisStoriesV3ImmersiveReshareAttribution_user.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e,f=d("react-compiler-runtime").c(3);a=a.user;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ImmersiveReshareAttribution_user.graphql"),a);e=(e=a.profile_pic_url)!=null?e:"";a=(a=a.username)!=null?a:"";var g;f[0]!==e||f[1]!==a?(g=j.jsx(c("PolarisStoriesImmersiveReshareAttribution.react"),{profilePicUrl:e,username:a}),f[0]=e,f[1]=a,f[2]=g):g=f[2];return g}g["default"]=a}),98);
__d("PolarisStoriesV3MusicAttribution_sticker.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MusicAttribution_sticker",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTFlattenedMusicInfo",kind:"LinkedField",name:"music_asset_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"display_artist",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null}],storageKey:null},action:"THROW"}],type:"XDTStoryMusicStickerTappableObject",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MusicWaveform.react",["react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={root:{alignItems:"x6s0dn4",display:"x78zum5",height:"x170jfvy",justifyContent:"x1qughib",width:"x1fsd2vl",$$css:!0}};function l(a){var b=d("react-compiler-runtime").c(4);a=a.index;var c;b[0]!==a?(c={0:{className:"x1m9vv7p xa4qsjk xytl4z6 x4hg4is x1caxmr6 xr9e8f9 x1e4oeot x1ui04y5 x6en5u8 xfo62xy"},1:{className:"x1m9vv7p xa4qsjk xytl4z6 x4hg4is x1caxmr6 xr9e8f9 x1e4oeot x1ui04y5 x6en5u8 xfo62xy x1o4y9o"}}[!!(a%2===0)<<0],b[0]=a,b[1]=c):c=b[1];b[2]!==c?(a=j.jsx("div",babelHelpers["extends"]({},c)),b[2]=c,b[3]=a):a=b[3];return a}function a(a){var b=d("react-compiler-runtime").c(5);a=a.xstyle;var e;b[0]!==a?(e=(h||(h=c("stylex"))).props(k.root,a),b[0]=a,b[1]=e):e=b[1];b[2]===Symbol["for"]("react.memo_cache_sentinel")?(a=[0,1,2].map(m),b[2]=a):a=b[2];b[3]!==e?(a=j.jsx("div",babelHelpers["extends"]({},e,{children:a})),b[3]=e,b[4]=a):a=b[4];return a}function m(a){return j.jsx(l,{index:a},a)}m.displayName=m.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("PolarisStoriesV3MusicAttribution.react",["CometRelay","IGDSTextVariants.react","PolarisStoriesV3MusicAttribution_sticker.graphql","PolarisStoriesV3MusicWaveform.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react"),k={waveform:{flexShrink:"x2lah0s",marginInlineEnd:"xbelrpt",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(12);a=a.sticker;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3MusicAttribution_sticker.graphql"),a);a=a.music_asset_info;var f,g,i;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(f={className:"x6s0dn4 x78zum5"},g=j.jsx(c("PolarisStoriesV3MusicWaveform.react"),{xstyle:k.waveform}),i={className:"xuxw1ft"},e[0]=f,e[1]=g,e[2]=i):(f=e[0],g=e[1],i=e[2]);var l;e[3]===Symbol["for"]("react.memo_cache_sentinel")?(l={className:"xuxw1ft"},e[3]=l):l=e[3];e[4]!==a.display_artist?(l=j.jsxs("span",babelHelpers["extends"]({},l,{children:[a.display_artist," \xb7 "]})),e[4]=a.display_artist,e[5]=l):l=e[5];var m;e[6]===Symbol["for"]("react.memo_cache_sentinel")?(m={className:"xuxw1ft"},e[6]=m):m=e[6];e[7]!==a.title?(m=j.jsx(d("IGDSTextVariants.react").IGDSTextFootnote,{color:"textOnMedia",children:j.jsx("span",babelHelpers["extends"]({},m,{children:a.title}))}),e[7]=a.title,e[8]=m):m=e[8];e[9]!==l||e[10]!==m?(a=j.jsxs("div",babelHelpers["extends"]({},f,{children:[g,j.jsx("span",babelHelpers["extends"]({},i,{children:j.jsxs(d("IGDSTextVariants.react").IGDSTextFootnoteEmphasized,{color:"textOnMedia",children:[l,m]})}))]})),e[9]=l,e[10]=m,e[11]=a):a=e[11];return a}g["default"]=a}),98);
__d("PolarisStoriesV3ReelAttribution_sticker.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelAttribution_sticker",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"media_code",storageKey:null},action:"THROW"}],type:"XDTStoryFeedMediaTappableObject",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelAttribution.react",["fbt","CometRelay","IGDSChevronRightPanoFilledIcon.react","IGDSReelsPanoFilledIcon.react","IGDSTextVariants.react","Locale","PolarisFastLink.react","PolarisGenericStrings","PolarisStoriesV3ReelAttribution_sticker.graphql","XPolarisClipsTabControllerRouteBuilder","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||d("react"),l=h._(/*BTDS*/"Watch full reel"),m=h._(/*BTDS*/"Reels");function a(a){var e=d("react-compiler-runtime").c(24);a=a.sticker;a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ReelAttribution_sticker.graphql"),a);var f;e[0]!==a.media_code?(f=c("XPolarisClipsTabControllerRouteBuilder").buildUri({shortcode:a.media_code}).toString(),e[0]=a.media_code,e[1]=f):f=e[1];a=f;var g,h,j,n,o;if(e[2]!==a){f=c("Locale").isRTL();g=c("PolarisFastLink.react");o=a;e[9]===Symbol["for"]("react.memo_cache_sentinel")?(h={className:"x6s0dn4 x78zum5"},j=k.jsx("div",babelHelpers["extends"]({className:"xbelrpt"},{children:k.jsx(c("IGDSReelsPanoFilledIcon.react"),{alt:m,color:"ig-stroke-on-media",size:12})})),n=k.jsx(d("IGDSTextVariants.react").IGDSTextFootnoteEmphasized,{color:"textOnMedia",children:l}),e[9]=h,e[10]=j,e[11]=n):(h=e[9],j=e[10],n=e[11]);f={0:{className:"xwklpps xvijh9v"},1:{className:"xwklpps xvijh9v x19jd1h0"}}[!!f<<0];e[2]=a;e[3]=g;e[4]=f;e[5]=h;e[6]=j;e[7]=n;e[8]=o}else g=e[3],f=e[4],h=e[5],j=e[6],n=e[7],o=e[8];e[12]===Symbol["for"]("react.memo_cache_sentinel")?(a=k.jsx(c("IGDSChevronRightPanoFilledIcon.react"),{alt:d("PolarisGenericStrings").RIGHT_CHEVRON,color:"ig-stroke-on-media",size:12}),e[12]=a):a=e[12];e[13]!==f?(a=k.jsx("div",babelHelpers["extends"]({},f,{children:a})),e[13]=f,e[14]=a):a=e[14];e[15]!==h||e[16]!==j||e[17]!==n||e[18]!==a?(f=k.jsxs("div",babelHelpers["extends"]({},h,{children:[j,n,a]})),e[15]=h,e[16]=j,e[17]=n,e[18]=a,e[19]=f):f=e[19];e[20]!==g||e[21]!==o||e[22]!==f?(h=k.jsx(g,{href:o,children:f}),e[20]=g,e[21]=o,e[22]=f,e[23]=h):h=e[23];return h}g["default"]=a}),226);
__d("PolarisStoriesV3SeeTranslation_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3SeeTranslation_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3TranslationDialogRootQuery$Parameters",["PolarisStoriesV3TranslationDialogRootQuery_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a={kind:"PreloadableConcreteRequest",params:{id:b("PolarisStoriesV3TranslationDialogRootQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__language__story_translate"]},name:"PolarisStoriesV3TranslationDialogRootQuery",operationKind:"query",text:null}};e.exports=a}),null);
__d("PolarisStoriesV3TranslationDialogRoot.entrypoint",["JSResourceForInteraction","PolarisStoriesV3TranslationDialogRootQuery$Parameters"],(function(a,b,c,d,e,f,g){"use strict";a={getPreloadProps:function(a){return{queries:{queryReference:{parameters:c("PolarisStoriesV3TranslationDialogRootQuery$Parameters"),variables:{mediaId:a.mediaId}}}}},root:c("JSResourceForInteraction")("PolarisStoriesV3TranslationDialogRoot.react").__setRef("PolarisStoriesV3TranslationDialogRoot.entrypoint")};g["default"]=a}),98);
__d("PolarisStoriesV3SeeTranslation.react",["fbt","CometPressable.react","CometRelay","IGDSCommentPanoFilledIcon.react","IGDSDialogPlaceholder.react","IGDSTextVariants.react","PolarisStoriesV3SeeTranslation_media.graphql","PolarisStoriesV3TranslationDialogRoot.entrypoint","react","react-compiler-runtime","useIGDSEntryPointDialog"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k=j||d("react"),l={root:{alignItems:"x6s0dn4",display:"x78zum5",$$css:!0}},m=h._(/*BTDS*/"See translation"),n=h._(/*BTDS*/"Translate");function a(a){var e=d("react-compiler-runtime").c(8);a=a.media;a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3SeeTranslation_media.graphql"),a);var f;e[0]!==a.pk?(f={mediaId:a.pk},e[0]=a.pk,e[1]=f):f=e[1];a=c("useIGDSEntryPointDialog")(c("PolarisStoriesV3TranslationDialogRoot.entrypoint"),f,"button",o);var g=a[0];e[2]!==g?(f=function(){g({})},e[2]=g,e[3]=f):f=e[3];a=f;var h;e[4]===Symbol["for"]("react.memo_cache_sentinel")?(f=k.jsx("div",babelHelpers["extends"]({className:"xbelrpt"},{children:k.jsx(c("IGDSCommentPanoFilledIcon.react"),{alt:n,color:"ig-stroke-on-media",size:12})})),h=k.jsx(d("IGDSTextVariants.react").IGDSTextFootnoteEmphasized,{color:"textOnMedia",children:m}),e[4]=f,e[5]=h):(f=e[4],h=e[5]);e[6]!==a?(f=k.jsxs(c("CometPressable.react"),{onPress:a,overlayDisabled:!0,xstyle:l.root,children:[f,h]}),e[6]=a,e[7]=f):f=e[7];return f}function o(a){return k.jsx(c("IGDSDialogPlaceholder.react"),{onClose:a})}o.displayName=o.name+" [from "+f.id+"]";g["default"]=a}),226);
__d("PolarisStoriesV3WearablesAppAttribution_attribution.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3WearablesAppAttribution_attribution",selections:[{alias:null,args:null,kind:"ScalarField",name:"attribution_title",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"attribution_cta_action_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"attribution_type",storageKey:null}],type:"XDTWearablesAppAttribution",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3WearablesAppAttribution_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3WearablesAppAttribution_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"product_type",storageKey:null}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3WearablesAppAttribution.react",["CometPressable.react","CometRelay","IGDSGlassesOutline24Icon.react","IGDSTextVariants.react","PolarisRoutes","PolarisStoriesV3WearablesAppAttribution_attribution.graphql","PolarisStoriesV3WearablesAppAttribution_media.graphql","gkx","react","react-compiler-runtime","usePartialViewImpression","usePolarisWearablesAppAttributionLogger.react"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react"),l={root:{alignItems:"x6s0dn4",display:"x78zum5",":hover_textDecoration":"x1lku1pv",$$css:!0}};function a(a){var e,f,g,j,m=d("react-compiler-runtime").c(22),n=a.attribution;a=a.media;n=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3WearablesAppAttribution_attribution.graphql"),n);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3WearablesAppAttribution_media.graphql"),a);var o=n.attribution_cta_action_url,p=n.attribution_title;e=c("usePolarisWearablesAppAttributionLogger.react")((e=n.attribution_type)!=null?e:"","attribute_click",(e=a.pk)!=null?e:"",(e=(e=a.user)==null?void 0:e.id)!=null?e:"",(e=a.product_type)!=null?e:"","story","story_viewer");var q=c("usePolarisWearablesAppAttributionLogger.react")((f=n.attribution_type)!=null?f:"","attribute_impression",(f=a.pk)!=null?f:"",(f=(f=a.user)==null?void 0:f.id)!=null?f:"",(f=a.product_type)!=null?f:"","story","story_viewer");m[0]!==q?(f={onImpressionStart:function(){return q()}},m[0]=q,m[1]=f):f=m[1];f=c("usePartialViewImpression")(f);g=(g=(g=a.user)==null?void 0:g.id)!=null?g:"";j=(j=a.pk)!=null?j:"";a=(a=a.product_type)!=null?a:"";n=(n=n.attribution_type)!=null?n:"";var r;m[2]!==g||m[3]!==j||m[4]!==a||m[5]!==n?(r={mediaAuthorId:g,mediaId:j,mediaProductType:a,mediaSubSurface:"story_viewer",mediaSurface:"story",wearableDevice:n},m[2]=g,m[3]=j,m[4]=a,m[5]=n,m[6]=r):r=m[6];g=c("gkx")("14881")?d("PolarisRoutes").WEARABLES_PIVOT_PAGE_PATH:o;m[7]!==r||m[8]!==g?(j={passthroughProps:r,url:g},m[7]=r,m[8]=g,m[9]=j):j=m[9];m[10]===Symbol["for"]("react.memo_cache_sentinel")?(a="x6s0dn4 x78zum5 x1lku1pv",n=k.jsx("div",babelHelpers["extends"]({className:"xf6vk7d"},{children:k.jsx(c("IGDSGlassesOutline24Icon.react"),{alt:"",color:"ig-stroke-on-media",size:12})})),o={className:"xnnr8r"},m[10]=n,m[11]=o,m[12]=a):(n=m[10],o=m[11],a=m[12]);m[13]!==p?(r=k.jsxs("div",{className:a,children:[n,k.jsx("div",babelHelpers["extends"]({},o,{children:k.jsx(d("IGDSTextVariants.react").IGDSTextFootnote,{color:"textOnMedia",children:p})}))]}),m[13]=p,m[14]=r):r=m[14];m[15]!==e||m[16]!==r||m[17]!==j?(g=k.jsx(c("CometPressable.react"),{linkProps:j,onPress:e,overlayDisabled:!0,xstyle:l.root,children:r}),m[15]=e,m[16]=r,m[17]=j,m[18]=g):g=m[18];m[19]!==f||m[20]!==g?(a=k.jsx("div",{ref:f,children:g}),m[19]=f,m[20]=g,m[21]=a):a=m[21];return a}g["default"]=a}),98);
__d("usePolarisStoriesV3OwnerSubtitle_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3OwnerSubtitle_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_paid_partnership",storageKey:null},{alias:null,args:null,concreteType:"XDTSponsorTag",kind:"LinkedField",name:"sponsor_tags",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"is_pending",storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"sponsor",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTWearablesAppAttribution",kind:"LinkedField",name:"wearable_attribution_info",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3WearablesAppAttribution_attribution"}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3WearablesAppAttribution_media"},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"reshared_story_media_author",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ImmersiveReshareAttribution_user"}],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryAppAttributionDict",kind:"LinkedField",name:"story_app_attribution",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AppAttribution_attribution"}],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryFeedMediaTappableObject",kind:"LinkedField",name:"story_feed_media",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"product_type",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelAttribution_sticker"}],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryMusicStickerTappableObject",kind:"LinkedField",name:"story_music_stickers",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTFlattenedMusicInfo",kind:"LinkedField",name:"music_asset_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MusicAttribution_sticker"}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_translation",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3SeeTranslation_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3OwnerSubtitle_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"};return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3OwnerSubtitle_reel",selections:[a,{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[a,{alias:null,args:null,kind:"ScalarField",name:"transparency_label",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"transparency_product",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"transparency_product_enabled",storageKey:null}],storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisStoriesV3OwnerSubtitle",["CometRelay","InstagramMediaProductTypeName","PolarisSponsorsUnit.react","PolarisStoriesV3AppAttribution.react","PolarisStoriesV3ImmersiveReshareAttribution.react","PolarisStoriesV3MusicAttribution.react","PolarisStoriesV3ReelAttribution.react","PolarisStoriesV3SeeTranslation.react","PolarisStoriesV3WearablesAppAttribution.react","PolarisTransparencyLabel.react","PolarisTransparencyUtils.react","filterNulls","isStringNullOrEmpty","react","react-compiler-runtime","usePolarisStoriesV3OwnerSubtitle_media.graphql","usePolarisStoriesV3OwnerSubtitle_reel.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a,e){var f,g,j,m,n=d("react-compiler-runtime").c(37);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3OwnerSubtitle_media.graphql"),a);e=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3OwnerSubtitle_reel.graphql"),e);var o=a.reshared_story_media_author,p=a.story_app_attribution,q=a.wearable_attribution_info;f=(f=a.story_feed_media)==null?void 0:f[0];g=(g=a.story_music_stickers)==null?void 0:g[0];var r=e.user.id;j=(j=e.user.transparency_label)!=null?j:void 0;m=(m=e.user.transparency_product)!=null?m:void 0;var s;if(n[0]!==f||n[1]!==a||n[2]!==g||n[3]!==e.id||n[4]!==e.user.id||n[5]!==e.user.transparency_product_enabled||n[6]!==o||n[7]!==p||n[8]!==j||n[9]!==m||n[10]!==q){s=Symbol["for"]("react.early_return_sentinel");bb0:{r={id:r,transparencyLabel:j,transparencyProduct:m};var t=e.user.transparency_product_enabled;if(a.is_paid_partnership===!0){var u;n[12]===Symbol["for"]("react.memo_cache_sentinel")?(u={className:"x1120s5i"},n[12]=u):u=n[12];var v;n[13]!==a.id?(v={mediaId:a.id},n[13]=a.id,n[14]=v):v=n[14];var w;n[15]!==a?(w=l(a),n[15]=a,n[16]=w):w=n[16];n[17]!==v||n[18]!==w?(u=k.jsx("div",babelHelpers["extends"]({},u,{children:k.jsx(c("PolarisSponsorsUnit.react"),{color:"white",loggingData:v,maxLines:1,source:"story_tray",sponsors:w})})),n[17]=v,n[18]=w,n[19]=u):u=n[19];s=u;break bb0}else if(d("PolarisTransparencyUtils.react").shouldShowTransparencyLabel(r)&&t===!0){n[20]===Symbol["for"]("react.memo_cache_sentinel")?(v="x1pg5gke x152skdk x6ikm8r x10wlt62 xlyipyv xuxw1ft x9bdzbf xxtlk9k x1824c90 xk7hvjd xe81s16",n[20]=v):v=n[20];n[21]!==e.id||n[22]!==r?(w=k.jsx(c("PolarisTransparencyLabel.react"),{className:v,mediaId:e.id,screen:"stories",user:r}),n[21]=e.id,n[22]=r,n[23]=w):w=n[23];s=w;break bb0}else if(o!=null){n[24]!==o?(u=k.jsx(c("PolarisStoriesV3ImmersiveReshareAttribution.react"),{user:o}),n[24]=o,n[25]=u):u=n[25];s=u;break bb0}else if(p!=null){n[26]!==p?(t=k.jsx(c("PolarisStoriesV3AppAttribution.react"),{attribution:p}),n[26]=p,n[27]=t):t=n[27];s=t;break bb0}else if(f!=null&&c("InstagramMediaProductTypeName").cast(f==null?void 0:f.product_type)==="clips"){n[28]!==f?(v=k.jsx(c("PolarisStoriesV3ReelAttribution.react"),{sticker:f}),n[28]=f,n[29]=v):v=n[29];s=v;break bb0}else{if(g!=null&&!c("isStringNullOrEmpty")((r=g.music_asset_info)==null?void 0:r.title)){n[30]!==g?(w=k.jsx(c("PolarisStoriesV3MusicAttribution.react"),{sticker:g}),n[30]=g,n[31]=w):w=n[31];s=w;break bb0}else if(a.has_translation===!0){n[32]!==a?(u=k.jsx(c("PolarisStoriesV3SeeTranslation.react"),{media:a}),n[32]=a,n[33]=u):u=n[33];s=u;break bb0}else if(q!=null){n[34]!==a||n[35]!==q?(t=k.jsx(c("PolarisStoriesV3WearablesAppAttribution.react"),{attribution:q,media:a}),n[34]=a,n[35]=q,n[36]=t):t=n[36];s=t;break bb0}else{s=null;break bb0}}}n[0]=f;n[1]=a;n[2]=g;n[3]=e.id;n[4]=e.user.id;n[5]=e.user.transparency_product_enabled;n[6]=o;n[7]=p;n[8]=j;n[9]=m;n[10]=q;n[11]=s}else s=n[11];if(s!==Symbol["for"]("react.early_return_sentinel"))return s}function l(a){return c("filterNulls")((a.sponsor_tags||[]).filter(function(a){return!a.is_pending}).map(function(a){var b;b=(b=a.sponsor)==null?void 0:b.pk;a=(a=a.sponsor)==null?void 0:a.username;if(b!=null&&a!=null)return{id:b,username:a}}))}g["default"]=a}),98);
__d("PolarisStoriesV3HeaderOwner.react",["CometRelay","PolarisStoriesV3HeaderOwner_media.graphql","PolarisStoriesV3HeaderOwner_reel.graphql","PolarisStoriesV3Owner.react","PolarisStoriesV3OwnerAvatar.react","PolarisStoriesV3ReelOwnerTitle.react","PolarisStoriesV3Timestamp.react","react","react-compiler-runtime","usePolarisStoriesV3OwnerSubtitle"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a){var e=d("react-compiler-runtime").c(11),f=a.media;a=a.reel;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3HeaderOwner_reel.graphql"),a);f=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3HeaderOwner_media.graphql"),f);var g=c("usePolarisStoriesV3OwnerSubtitle")(f,a),j;e[0]!==a?(j=k.jsx(c("PolarisStoriesV3OwnerAvatar.react"),{reel:a}),e[0]=a,e[1]=j):j=e[1];var l;e[2]!==f?(l=k.jsx(c("PolarisStoriesV3Timestamp.react"),{media:f}),e[2]=f,e[3]=l):l=e[3];e[4]!==a?(f=k.jsx(c("PolarisStoriesV3ReelOwnerTitle.react"),{reel:a}),e[4]=a,e[5]=f):f=e[5];e[6]!==g||e[7]!==j||e[8]!==l||e[9]!==f?(a=k.jsx(c("PolarisStoriesV3Owner.react"),{avatar:j,subtitle:g,timestamp:l,title:f}),e[6]=g,e[7]=j,e[8]=l,e[9]=f,e[10]=a):a=e[10];return a}g["default"]=a}),98);
__d("PolarisStoriesV3ReelHeader_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelHeader_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3CloseFriendsButton_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Header_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3HeaderOwner_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3PlayerControls_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelOptionsButton_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3IsCloseFriendsStory_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelHeader_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelHeader_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3CloseFriendsButton_user"}],storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Header_mediaItems"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3HeaderOwner_reel"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelOptionsButton_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelHeader_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelHeader_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelOptionsButton_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelOptionsButton_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelOptionsButton_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelOptionsDialog_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelOptionsButton_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelOptionsButton_reel",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelOptionsDialog_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelOptionsButton_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelOptionsButton_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelOptionsDialog_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelOptionsButton.react",["CometRelay","JSResourceForInteraction","PolarisStoriesV3OptionsButton.react","PolarisStoriesV3ReelOptionsButton_media.graphql","PolarisStoriesV3ReelOptionsButton_reel.graphql","PolarisStoriesV3ReelOptionsButton_viewer.graphql","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||d("react"),m=c("JSResourceForInteraction")("PolarisStoriesV3ReelOptionsDialog.react").__setRef("PolarisStoriesV3ReelOptionsButton.react");function a(a){var e=d("react-compiler-runtime").c(4),f=a.media,g=a.reel;a=a.viewer;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelOptionsButton_reel.graphql"),g);f=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ReelOptionsButton_media.graphql"),f);a=d("CometRelay").useFragment(j!==void 0?j:j=b("PolarisStoriesV3ReelOptionsButton_viewer.graphql"),a);var k;e[0]!==f||e[1]!==g||e[2]!==a?(k=l.jsx(c("PolarisStoriesV3OptionsButton.react"),{dialogProps:{media:f,reel:g,viewer:a},dialogResource:m}),e[0]=f,e[1]=g,e[2]=a,e[3]=k):k=e[3];return k}g["default"]=a}),98);
__d("PolarisStoriesV3ReelHeader.react",["CometRelay","PolarisStoriesV3CloseFriendsButton.react","PolarisStoriesV3Header.react","PolarisStoriesV3HeaderOwner.react","PolarisStoriesV3PlayerControls.react","PolarisStoriesV3ReelHeader_media.graphql","PolarisStoriesV3ReelHeader_reel.graphql","PolarisStoriesV3ReelHeader_viewer.graphql","PolarisStoriesV3ReelOptionsButton.react","react","react-compiler-runtime","usePolarisStoriesV3IsCloseFriendsStory"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||d("react"),m={closeFriends:{flexShrink:"x2lah0s",marginInlineEnd:"x1xegmmw",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(22),f=a.media,g=a.reel;a=a.viewer;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelHeader_reel.graphql"),g);f=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ReelHeader_media.graphql"),f);a=d("CometRelay").useFragment(j!==void 0?j:j=b("PolarisStoriesV3ReelHeader_viewer.graphql"),a);var k=c("usePolarisStoriesV3IsCloseFriendsStory")(f),n;e[0]!==f||e[1]!==g?(n=l.jsx(c("PolarisStoriesV3HeaderOwner.react"),{media:f,reel:g}),e[0]=f,e[1]=g,e[2]=n):n=e[2];n=n;var o;e[3]!==k||e[4]!==f||e[5]!==g.user?(o=k&&l.jsx(c("PolarisStoriesV3CloseFriendsButton.react"),{media:f,user:g.user,xstyle:m.closeFriends}),e[3]=k,e[4]=f,e[5]=g.user,e[6]=o):o=e[6];e[7]!==f?(k=l.jsx(c("PolarisStoriesV3PlayerControls.react"),{media:f}),e[7]=f,e[8]=k):k=e[8];var p;e[9]!==o||e[10]!==k?(p=l.jsxs(l.Fragment,{children:[o,k]}),e[9]=o,e[10]=k,e[11]=p):p=e[11];o=p;e[12]!==f||e[13]!==g||e[14]!==a?(k=l.jsx(c("PolarisStoriesV3ReelOptionsButton.react"),{media:f,reel:g,viewer:a}),e[12]=f,e[13]=g,e[14]=a,e[15]=k):k=e[15];p=k;e[16]!==o||e[17]!==f||e[18]!==p||e[19]!==n||e[20]!==g.items?(a=l.jsx(c("PolarisStoriesV3Header.react"),{controls:o,media:f,mediaItems:g.items,options:p,owner:n}),e[16]=o,e[17]=f,e[18]=p,e[19]=n,e[20]=g.items,e[21]=a):a=e[21];return a}g["default"]=a}),98);
__d("PolarisStoriesV3ReelPlayer_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelPlayer_media",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MediaImpressionWrapper_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Player_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelHeader_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelFooter_media"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MarkReelSeen_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelPlayer_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelPlayer_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Player_user"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MediaImpressionWrapper_reel"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelHeader_reel"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelFooter_reel"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MarkReelSeen_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelPlayer_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelPlayer_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3Player_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelHeader_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelFooter_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3MarkReelSeen_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3MarkReelSeen_media",selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MarkSeen_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3MarkReelSeen_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={alias:null,args:null,kind:"ScalarField",name:"latest_reel_media",storageKey:null},b={alias:null,args:null,kind:"ScalarField",name:"seen",storageKey:null};return{argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3MarkReelSeen_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},a,b,{kind:"InlineDataFragmentSpread",name:"getPolarisStoriesV3IsReelSeen_reel",selections:[a,{alias:null,args:null,kind:"ScalarField",name:"muted",storageKey:null},b],args:null,argumentDefinitions:[]}],type:"XDTReelDict",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisStoriesV3MarkReelSeen",["CometRelay","getPolarisStoriesV3IsReelSeen","igMapTypenameToRelayID","react-compiler-runtime","usePolarisStoriesV3MarkReelSeen_media.graphql","usePolarisStoriesV3MarkReelSeen_reel.graphql","usePolarisStoriesV3MarkSeen","usePolarisStoriesV3SeenStateContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i;function a(a,e){var f=d("react-compiler-runtime").c(14),g=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3MarkReelSeen_reel.graphql"),e),j=d("CometRelay").useFragment(i!==void 0?i:i=b("usePolarisStoriesV3MarkReelSeen_media.graphql"),a),k=d("CometRelay").useRelayEnvironment(),l=c("usePolarisStoriesV3SeenStateContext")();f[0]!==k||f[1]!==j||f[2]!==g.id||f[3]!==g.seen?(e=function(){if(g.seen==null||g.seen>=j.taken_at)return;d("CometRelay").commitLocalUpdate(k,function(a){var b=c("igMapTypenameToRelayID")("XDTReelDict",g.id);a=a.get(b);a==null?void 0:a.setValue(j.taken_at,"seen")})},f[0]=k,f[1]=j,f[2]=g.id,f[3]=g.seen,f[4]=e):e=f[4];a=e;f[5]!==j||f[6]!==g||f[7]!==l?(e=function(){var a=c("getPolarisStoriesV3IsReelSeen")(g)||j.taken_at===g.latest_reel_media;l.updateSeen({isReelFullySeen:a,mediaId:j.pk,mediaTakenAt:Number(j.taken_at),reelId:g.id})},f[5]=j,f[6]=g,f[7]=l,f[8]=e):e=f[8];e=e;var m;f[9]!==e||f[10]!==a||f[11]!==j||f[12]!==g.id?(m={media:j,onAfterMarkSeen:e,onMarkSeen:a,reelId:g.id},f[9]=e,f[10]=a,f[11]=j,f[12]=g.id,f[13]=m):m=f[13];return c("usePolarisStoriesV3MarkSeen")(m)}g["default"]=a}),98);
__d("PolarisStoriesV3ReelPlayer.react",["CometErrorBoundary.react","CometPlaceholder.react","CometRelay","PolarisStoriesV3MediaImpressionWrapper.react","PolarisStoriesV3Player.react","PolarisStoriesV3ReelFooter.react","PolarisStoriesV3ReelHeader.react","PolarisStoriesV3ReelPlayer_media.graphql","PolarisStoriesV3ReelPlayer_reel.graphql","PolarisStoriesV3ReelPlayer_viewer.graphql","react","react-compiler-runtime","usePolarisStoriesV3MarkReelSeen"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||(k=d("react"));k.useCallback;function a(a){var e=d("react-compiler-runtime").c(22),f=a.media,g=a.reel,k=a.ref;a=a.viewer;var n=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelPlayer_reel.graphql"),g),o=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ReelPlayer_media.graphql"),f);g=d("CometRelay").useFragment(j!==void 0?j:j=b("PolarisStoriesV3ReelPlayer_viewer.graphql"),a);var p=c("usePolarisStoriesV3MarkReelSeen")(o,n);e[0]!==p?(f=function(){p()},e[0]=p,e[1]=f):f=e[1];a=f;e[2]!==o||e[3]!==n?(f=function(a){return l.jsx(c("PolarisStoriesV3MediaImpressionWrapper.react"),{media:o,reel:n,children:a})},e[2]=o,e[3]=n,e[4]=f):f=e[4];f=f;var q;e[5]!==o||e[6]!==n||e[7]!==g?(q=l.jsx(c("PolarisStoriesV3ReelHeader.react"),{media:o,reel:n,viewer:g}),e[5]=o,e[6]=n,e[7]=g,e[8]=q):q=e[8];q=q;var r;e[9]!==o||e[10]!==n||e[11]!==g?(r=l.jsx(c("CometErrorBoundary.react"),{fallback:m,children:l.jsx(c("CometPlaceholder.react"),{fallback:null,children:l.jsx(c("PolarisStoriesV3ReelFooter.react"),{media:o,reel:n,viewer:g})})}),e[9]=o,e[10]=n,e[11]=g,e[12]=r):r=e[12];r=r;var s;e[13]!==r||e[14]!==a||e[15]!==q||e[16]!==o||e[17]!==n.user||e[18]!==k||e[19]!==f||e[20]!==g?(s=l.jsx(c("PolarisStoriesV3Player.react"),{footer:r,header:q,media:o,onPlayerStart:a,ref:k,renderMediaPlayer:f,user:n.user,viewer:g}),e[13]=r,e[14]=a,e[15]=q,e[16]=o,e[17]=n.user,e[18]=k,e[19]=f,e[20]=g,e[21]=s):s=e[21];return s}function m(){return l.jsx("div",{})}m.displayName=m.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("PolarisStoriesV3ReelPlayerContainer_media.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelPlayerContainer_media",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3PlayerControllerContextProvider_media"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPlayer_media"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelPlayerContainer_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelPlayerContainer_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3MaybeIssueIGDThreadPointQuery_user"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPlayer_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelPlayerContainer_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelPlayerContainer_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPlayer_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3MaybeIssueIGDThreadPointQuery_user.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3MaybeIssueIGDThreadPointQuery_user",selections:[{alias:null,args:null,kind:"ScalarField",name:"interop_messaging_user_fbid",storageKey:null}],type:"XDTUserDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3MaybeIssueIGDThreadPointQuery",["CometRelay","promiseDone","react","react-compiler-runtime","requireDeferredForDisplay","usePolarisStoriesV3MaybeIssueIGDThreadPointQuery_user.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(i||d("react")).useEffect,k=c("requireDeferredForDisplay")("maybeIssueIGDThreadPointQuery").__setRef("usePolarisStoriesV3MaybeIssueIGDThreadPointQuery");function a(a){var e=d("react-compiler-runtime").c(3),f=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3MaybeIssueIGDThreadPointQuery_user.graphql"),a),g;e[0]!==f.interop_messaging_user_fbid?(a=function(){if(f.interop_messaging_user_fbid!=null){var a=f.interop_messaging_user_fbid;c("promiseDone")(k.load().then(function(b){return b(a)}))}},g=[f.interop_messaging_user_fbid],e[0]=f.interop_messaging_user_fbid,e[1]=a,e[2]=g):(a=e[1],g=e[2]);j(a,g)}g["default"]=a}),98);
__d("PolarisStoriesV3ReelPlayerContainer.react",["CometRelay","PolarisStoriesV3PlayerControllerContextProvider.react","PolarisStoriesV3ReelPlayer.react","PolarisStoriesV3ReelPlayerContainer_media.graphql","PolarisStoriesV3ReelPlayerContainer_reel.graphql","PolarisStoriesV3ReelPlayerContainer_viewer.graphql","react","react-compiler-runtime","usePolarisStoriesV3MaybeIssueIGDThreadPointQuery"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||d("react");function a(a){var e=d("react-compiler-runtime").c(8),f=a.media,g=a.reel;a=a.viewer;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelPlayerContainer_reel.graphql"),g);f=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ReelPlayerContainer_media.graphql"),f);a=d("CometRelay").useFragment(j!==void 0?j:j=b("PolarisStoriesV3ReelPlayerContainer_viewer.graphql"),a);c("usePolarisStoriesV3MaybeIssueIGDThreadPointQuery")(g.user);var k;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(k={className:"x5yr21d x1n2onr6 xh8yej3"},e[0]=k):k=e[0];var m;e[1]!==f||e[2]!==g||e[3]!==a?(m=l.jsx(c("PolarisStoriesV3ReelPlayer.react"),{media:f,reel:g,viewer:a}),e[1]=f,e[2]=g,e[3]=a,e[4]=m):m=e[4];e[5]!==f||e[6]!==m?(g=l.jsx("div",babelHelpers["extends"]({},k,{"data-testid":void 0,children:l.jsx(c("PolarisStoriesV3PlayerControllerContextProvider.react"),{media:f,children:m},f.pk)})),e[5]=f,e[6]=m,e[7]=g):g=e[7];return g}g["default"]=a}),98);
__d("PolarisStoriesV3GalleryPlayer.react",["CometRelay","FBLogger","PolarisStoriesV3GalleryPlayer_reel.graphql","PolarisStoriesV3GalleryPlayer_viewer.graphql","PolarisStoriesV3Reel","PolarisStoriesV3ReelLoggingContextProvider.react","PolarisStoriesV3ReelPlayerContainer.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a){var e=d("react-compiler-runtime").c(17),f=a.mediaId,g=a.reel;a=a.viewer;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3GalleryPlayer_reel.graphql"),g);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3GalleryPlayer_viewer.graphql"),a);if(e[0]!==f||e[1]!==g.items){var j;j=(j=g.items)==null?void 0:j.find(function(a){return a.pk===f});e[0]=f;e[1]=g.items;e[2]=j}else j=e[2];j=j;if(j==null)throw c("FBLogger")("ig_web").mustfixThrow("Media not found for story reel");var l;e[3]!==g.id?(l={id:g.id,type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic},e[3]=g.id,e[4]=l):l=e[4];var m;e[5]!==g?(m=[g],e[5]=g,e[6]=m):m=e[6];var n;e[7]!==j||e[8]!==g||e[9]!==a?(n=k.jsx(c("PolarisStoriesV3ReelPlayerContainer.react"),{media:j,reel:g,viewer:a}),e[7]=j,e[8]=g,e[9]=a,e[10]=n):n=e[10];e[11]!==f||e[12]!==l||e[13]!==m||e[14]!==n||e[15]!==a?(j=k.jsx(c("PolarisStoriesV3ReelLoggingContextProvider.react"),{currentReel:l,mediaId:f,reels:m,viewer:a,children:n}),e[11]=f,e[12]=l,e[13]=m,e[14]=n,e[15]=a,e[16]=j):j=e[16];return j}g["default"]=a}),98);
__d("PolarisStoriesV3ReelGalleryPreview_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelGalleryPreview_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3GalleryPreview_media"}],storageKey:null},action:"THROW"},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPreviewCoverImage_reel"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3IsReelSeen_reel"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3NextReelMediaId_reel"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ReelPreviewLinkProps_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelPreviewCoverImage_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelPreviewCoverImage_reel",selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null}],storageKey:null},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3IsReelSeen_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelPreviewCoverImage.react",["CometRelay","PolarisStoriesV3PreviewCoverImage.react","PolarisStoriesV3ReelPreviewCoverImage_reel.graphql","PolarisStoryRing.react","react","react-compiler-runtime","usePolarisStoriesV3IsReelSeen"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(9);a=a.reel;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelPreviewCoverImage_reel.graphql"),a);var f=c("usePolarisStoriesV3IsReelSeen")(a);a=(a=a.user)==null?void 0:a.profile_pic_url;var g;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(g={className:"x1n2onr6"},e[0]=g):g=e[0];var i;e[1]===Symbol["for"]("react.memo_cache_sentinel")?(i="x1o0tod x10l6tqk x13vifvy",e[1]=i):i=e[1];e[2]!==f?(i=j.jsx(c("PolarisStoryRing.react"),{className:i,isCenterOnAvatar:!0,seen:f,showRing:!0,size:56}),e[2]=f,e[3]=i):i=e[3];e[4]!==a?(f=j.jsx(c("PolarisStoriesV3PreviewCoverImage.react"),{src:a}),e[4]=a,e[5]=f):f=e[5];e[6]!==i||e[7]!==f?(a=j.jsxs("div",babelHelpers["extends"]({},g,{children:[i,f]})),e[6]=i,e[7]=f,e[8]=a):a=e[8];return a}g["default"]=a}),98);
__d("getPolarisStoriesV3NextReelMediaId_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"getPolarisStoriesV3NextReelMediaId_reel"};e.exports=a}),null);
__d("getPolarisStoriesV3NextReelMediaId",["CometRelay","FBLogger","getPolarisStoriesV3NextMediaId","getPolarisStoriesV3NextReelMediaId_reel.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,e){var f;a=d("CometRelay").readInlineData(h!==void 0?h:h=b("getPolarisStoriesV3NextReelMediaId_reel.graphql"),a);var g=a.id;if(g==null)throw c("FBLogger")("ig_web").mustfixThrow("Reel id is null");f=(f=a.items)!=null?f:[];g={id:g,items:f.map(function(a){return{id:a.pk,takenAt:a.taken_at}}),seen:a.seen};return c("getPolarisStoriesV3NextMediaId")(g,e)}g["default"]=a}),98);
__d("usePolarisStoriesV3NextReelMediaId_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3NextReelMediaId_reel",selections:[{kind:"InlineDataFragmentSpread",name:"getPolarisStoriesV3NextReelMediaId_reel",selections:[{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"seen",storageKey:null}],args:null,argumentDefinitions:[]}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3NextReelMediaId",["CometRelay","getPolarisStoriesV3NextReelMediaId","usePolarisStoriesV3NextReelMediaId_reel.graphql","usePolarisStoriesV3SeenStateContext"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a){a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3NextReelMediaId_reel.graphql"),a);var e=c("usePolarisStoriesV3SeenStateContext")();return c("getPolarisStoriesV3NextReelMediaId")(a,e)}g["default"]=a}),98);
__d("usePolarisStoriesV3ReelPreviewLinkProps_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3ReelPreviewLinkProps_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3ReelPreviewLinkProps",["CometRelay","PolarisStoriesV3Reel","react","react-compiler-runtime","usePolarisStoriesV3ReelLinkPropsBuilder","usePolarisStoriesV3ReelPreviewLinkProps_reel.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i;(i||d("react")).useMemo;function a(a,e){var f=d("react-compiler-runtime").c(5);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3ReelPreviewLinkProps_reel.graphql"),a);var g=c("usePolarisStoriesV3ReelLinkPropsBuilder")(),i;f[0]!==g||f[1]!==e||f[2]!==a.id||f[3]!==a.user.username?(i=g(a.user.username,{id:a.id,type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic},e),f[0]=g,f[1]=e,f[2]=a.id,f[3]=a.user.username,f[4]=i):i=f[4];g=i;return g}g["default"]=a}),98);
__d("PolarisStoriesV3ReelGalleryPreview.react",["CometRelay","PolarisStoriesV3GalleryPreview.react","PolarisStoriesV3Reel","PolarisStoriesV3ReelGalleryPreview_reel.graphql","PolarisStoriesV3ReelPreviewCoverImage.react","PolarisTimestamp.react","react","react-compiler-runtime","usePolarisStoriesV3IsReelSeen","usePolarisStoriesV3NextReelMediaId","usePolarisStoriesV3ReelPreviewLinkProps"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||d("react");function a(a){var e=d("react-compiler-runtime").c(18),f=a.onPress;a=a.reel;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelGalleryPreview_reel.graphql"),a);var g=c("usePolarisStoriesV3NextReelMediaId")(a);if(e[0]!==g||e[1]!==a.items){var i;i=(i=a.items.find(function(a){return a.pk===g}))!=null?i:a.items[0];e[0]=g;e[1]=a.items;e[2]=i}else i=e[2];i=i;var k=c("usePolarisStoriesV3ReelPreviewLinkProps")(a,i.pk),l=c("usePolarisStoriesV3IsReelSeen")(a),m;e[3]!==a?(m=j.jsx(c("PolarisStoriesV3ReelPreviewCoverImage.react"),{reel:a}),e[3]=a,e[4]=m):m=e[4];var n;e[5]!==a.id?(n={id:a.id,type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic},e[5]=a.id,e[6]=n):n=e[6];var o=Number(i.taken_at),p;e[7]!==o?(p=j.jsx(c("PolarisTimestamp.react"),{value:o}),e[7]=o,e[8]=p):p=e[8];a=(o=a.user)==null?void 0:o.username;e[9]!==l||e[10]!==k||e[11]!==i||e[12]!==f||e[13]!==m||e[14]!==n||e[15]!==p||e[16]!==a?(o=j.jsx(c("PolarisStoriesV3GalleryPreview.react"),{coverImage:m,isSeen:l,linkProps:k,media:i,onPress:f,reel:n,subtitle:p,title:a}),e[9]=l,e[10]=k,e[11]=i,e[12]=f,e[13]=m,e[14]=n,e[15]=p,e[16]=a,e[17]=o):o=e[17];return o}g["default"]=a}),98);
__d("PolarisStoriesV3DesktopReelGalleryItem.react",["CometRelay","PolarisStoriesV3DesktopGalleryItemVariant","PolarisStoriesV3DesktopReelGalleryItem_reel.graphql","PolarisStoriesV3DesktopReelGalleryItem_viewer.graphql","PolarisStoriesV3GalleryItemGlimmer.react","PolarisStoriesV3GalleryPlayer.react","PolarisStoriesV3ReelGalleryPreview.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function l(a){var b=d("react-compiler-runtime").c(7),e=a.mediaId,f=a.onPreviewPress,g=a.reel,h=a.variant;a=a.viewer;switch(h){case c("PolarisStoriesV3DesktopGalleryItemVariant").Player:b[0]!==e||b[1]!==g||b[2]!==a?(h=g==null?k.jsx(c("PolarisStoriesV3GalleryItemGlimmer.react"),{}):k.jsx(c("PolarisStoriesV3GalleryPlayer.react"),{mediaId:e,reel:g,viewer:a}),b[0]=e,b[1]=g,b[2]=a,b[3]=h):h=b[3];return h;case c("PolarisStoriesV3DesktopGalleryItemVariant").Preview:b[4]!==f||b[5]!==g?(e=g==null?k.jsx(c("PolarisStoriesV3GalleryItemGlimmer.react"),{}):k.jsx(c("PolarisStoriesV3ReelGalleryPreview.react"),{onPress:f,reel:g}),b[4]=f,b[5]=g,b[6]=e):e=b[6];return e;case c("PolarisStoriesV3DesktopGalleryItemVariant").Offscreen:return null}}function a(a){var c=d("react-compiler-runtime").c(6),e=a.mediaId,f=a.onPreviewPress,g=a.reel,j=a.variant;a=a.viewer;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3DesktopReelGalleryItem_reel.graphql"),g);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3DesktopReelGalleryItem_viewer.graphql"),a);var m;c[0]!==e||c[1]!==f||c[2]!==g||c[3]!==j||c[4]!==a?(m=k.jsx(l,{mediaId:e,onPreviewPress:f,reel:g,variant:j,viewer:a}),c[0]=e,c[1]=f,c[2]=g,c[3]=j,c[4]=a,c[5]=m):m=c[5];return m}g["default"]=a}),98);
__d("PolarisStoriesV3DesktopReelGalleryItems_reels.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"PolarisStoriesV3DesktopReelGalleryItems_reels",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3DesktopReelGalleryItem_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3DesktopReelGalleryItems_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3DesktopReelGalleryItems_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3DesktopAdGalleryItem_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3DesktopReelGalleryItem_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3GetReelPlaybackLoggingData",["react","react-compiler-runtime","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(4),b=c("usePolarisStoriesV3ViewerLoggingContext")(),e=b.loggingSession;b=c("usePolarisStoriesV3ReelLoggingContext")();var f=b.mediaViewers,g=b.source;a[0]!==e||a[1]!==f||a[2]!==g?(b=function(){return{media_source:"organic",media_viewers:f.toString(),pause_duration:e.getMediaPauseDuration(),source:String(g)}},a[0]=e,a[1]=f,a[2]=g,a[3]=b):b=a[3];return b}g["default"]=a}),98);
__d("usePolarisStoriesV3LogReelPlaybackNavigation",["PolarisStoriesV3Reel","ReelPlaybackNavigationFalcoEvent","react","react-compiler-runtime","usePolarisStoriesV3GetReelPlaybackLoggingData","usePolarisStoriesV3GetSharedLoggingData","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(5),b=c("usePolarisStoriesV3ViewerLoggingContext")(),e=c("usePolarisStoriesV3ReelLoggingContext")(),f=c("usePolarisStoriesV3GetSharedLoggingData")(),g=c("usePolarisStoriesV3GetReelPlaybackLoggingData")(),h;a[0]!==f||a[1]!==g||a[2]!==e||a[3]!==b?(h=function(a){c("ReelPlaybackNavigationFalcoEvent").log(function(){return babelHelpers["extends"]({},f(),{},g(),{action:b.getAction(),delivery_class:e.reel.type===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad?"ad":"organic",module_name:String(b.module),reel_id:e.reel.id},a)})},a[0]=f,a[1]=g,a[2]=e,a[3]=b,a[4]=h):h=a[4];return h}g["default"]=a}),98);
__d("usePolarisStoriesV3GalleryPreviewPressHandler",["react","react-compiler-runtime","usePolarisStoriesV3LogReelPlaybackNavigation","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;var i={"-2":"click_preview_back_2","-1":"click_preview_back_1",1:"click_preview_forward_1",2:"click_preview_forward_2"};function a(a,b){var e=d("react-compiler-runtime").c(5),f=c("usePolarisStoriesV3ViewerLoggingContext")(),g=c("usePolarisStoriesV3LogReelPlaybackNavigation")(),h;e[0]!==g||e[1]!==b||e[2]!==a||e[3]!==f?(h=function(c){c=i[String(c)];f.setAction(c);var d=a.getIndex(b),e;if(c==="click_preview_back_2"){var h;e=(h=a.get(d-1))==null?void 0:h.id}else if(c==="click_preview_forward_2"){e=(h=a.get(d+1))==null?void 0:h.id}g({skipped_reel_id_on_web:e})},e[0]=g,e[1]=b,e[2]=a,e[3]=f,e[4]=h):h=e[4];return h}g["default"]=a}),98);
__d("usePolarisStoriesV3DesktopGalleryRenderedItems",["PolarisStoriesV3DesktopGalleryItemVariant","react","react-compiler-runtime","usePolarisStoriesV3GalleryPreviewPressHandler"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useMemo;function a(a){var b=d("react-compiler-runtime").c(13),e=a.config,f=a.reel,g=a.reelList,h=c("usePolarisStoriesV3GalleryPreviewPressHandler")(g,f);a=Math.ceil(e.previewCount);var i=g.getIndex(f),j,k;if(b[0]!==h||b[1]!==i||b[2]!==a||b[3]!==g){k=[];j=[];e=function(a){var b=a+1;a=i-b;if(g.has(a)){var d=g.get(a);k.push({index:a,onPreviewPress:function(){return h(-1*b)},position:-1*b,reel:d,variant:c("PolarisStoriesV3DesktopGalleryItemVariant").Preview})}a=i+b;if(g.has(a)){d=g.get(a);j.push({index:a,onPreviewPress:function(){return h(b)},position:b,reel:d,variant:c("PolarisStoriesV3DesktopGalleryItemVariant").Preview})}};for(var l=0;l<a;l++)e(l);for(e=0;e<a;e++){l=a+e+1;var m=i-l;m>=0&&k.push({index:m,position:-1*l,reel:null,variant:c("PolarisStoriesV3DesktopGalleryItemVariant").Offscreen});m=i+l;m<g.getMaxLength()&&j.push({index:m,position:l,reel:null,variant:c("PolarisStoriesV3DesktopGalleryItemVariant").Offscreen})}k.reverse();b[0]=h;b[1]=i;b[2]=a;b[3]=g;b[4]=j;b[5]=k}else j=b[4],k=b[5];b[6]!==i||b[7]!==f?(m={index:i,position:0,reel:f,variant:c("PolarisStoriesV3DesktopGalleryItemVariant").Player},b[6]=i,b[7]=f,b[8]=m):m=b[8];l=m;b[9]!==j||b[10]!==l||b[11]!==k?(e={nextItems:j,playerItem:l,prevItems:k},b[9]=j,b[10]=l,b[11]=k,b[12]=e):e=b[12];a=e;return a}g["default"]=a}),98);
__d("usePolarisStoriesV3GalleryPaginationLoaderEffect",["react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(a){var b=d("react-compiler-runtime").c(20),c=a.fetchDistance,e=a.hasNext,f=a.hasPrevious,g=a.isLoadingNext,h=a.isLoadingPrevious,j=a.loadNext,k=a.loadPrevious,l=a.reel,m=a.reelIds;b[0]!==c||b[1]!==e||b[2]!==f||b[3]!==g||b[4]!==h||b[5]!==j||b[6]!==k||b[7]!==l.id||b[8]!==m?(a=function(){var a=m.indexOf(l.id);f&&!h&&a-c<0&&k(Math.abs(a-c));e&&!g&&a+c>m.length-1&&j(a+c-m.length+1)},b[0]=c,b[1]=e,b[2]=f,b[3]=g,b[4]=h,b[5]=j,b[6]=k,b[7]=l.id,b[8]=m,b[9]=a):a=b[9];var n;b[10]!==c||b[11]!==e||b[12]!==f||b[13]!==g||b[14]!==h||b[15]!==j||b[16]!==k||b[17]!==l||b[18]!==m?(n=[c,e,f,g,h,j,k,l,m],b[10]=c,b[11]=e,b[12]=f,b[13]=g,b[14]=h,b[15]=j,b[16]=k,b[17]=l,b[18]=m,b[19]=n):n=b[19];i(a,n)}g["default"]=a}),98);
__d("PolarisStoriesV3DesktopReelGalleryItems.react",["CometErrorBoundary.react","CometRelay","PolarisStoriesV3DesktopAdGalleryItem.react","PolarisStoriesV3DesktopGalleryItem.react","PolarisStoriesV3DesktopReelGalleryItem.react","PolarisStoriesV3DesktopReelGalleryItems_reels.graphql","PolarisStoriesV3DesktopReelGalleryItems_viewer.graphql","PolarisStoriesV3Reel","react","react-compiler-runtime","usePolarisStoriesV3DesktopGalleryRenderedItems","usePolarisStoriesV3GalleryPaginationLoaderEffect"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react"));j.useMemo;function a(a){var e=d("react-compiler-runtime").c(25),f=a.adsPool,g=a.config,j=a.currentReel,n=a.hasNext,o=a.hasPrevious,p=a.isLoadingNext,q=a.isLoadingPrevious,r=a.loadNext,s=a.loadPrevious,t=a.mediaId,u=a.reelList,v=a.reels;a=a.viewer;var w=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3DesktopReelGalleryItems_reels.graphql"),v),x=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3DesktopReelGalleryItems_viewer.graphql"),a);e[0]!==g||e[1]!==j||e[2]!==u?(v={config:g,reel:j,reelList:u},e[0]=g,e[1]=j,e[2]=u,e[3]=v):v=e[3];a=c("usePolarisStoriesV3DesktopGalleryRenderedItems")(v);u=a.nextItems;v=a.playerItem;a=a.prevItems;var y;e[4]!==w?(y=w.map(m),e[4]=w,e[5]=y):y=e[5];y=y;y=y;var z;e[6]!==g.fetchDistance||e[7]!==j||e[8]!==n||e[9]!==o||e[10]!==p||e[11]!==q||e[12]!==r||e[13]!==s||e[14]!==y?(z={fetchDistance:g.fetchDistance,hasNext:n,hasPrevious:o,isLoadingNext:p,isLoadingPrevious:q,loadNext:r,loadPrevious:s,reel:j,reelIds:y},e[6]=g.fetchDistance,e[7]=j,e[8]=n,e[9]=o,e[10]=p,e[11]=q,e[12]=r,e[13]=s,e[14]=y,e[15]=z):z=e[15];c("usePolarisStoriesV3GalleryPaginationLoaderEffect")(z);e[16]!==f||e[17]!==g||e[18]!==t||e[19]!==u||e[20]!==v||e[21]!==a||e[22]!==w||e[23]!==x?(j=[].concat(a,[v],u).map(function(a){var b=a.index,e=a.onPreviewPress,h=a.position,i=a.reel;a=a.variant;return k.jsx(c("CometErrorBoundary.react"),{fallback:l,children:k.jsx(c("PolarisStoriesV3DesktopGalleryItem.react"),{config:g,position:h,children:i!=null&&i.type===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad?k.jsx(c("PolarisStoriesV3DesktopAdGalleryItem.react"),{ad:f==null?void 0:(h=f.getAd(i.id))==null?void 0:h.data,mediaId:t,onPreviewPress:e,variant:a,viewer:x}):k.jsx(c("PolarisStoriesV3DesktopReelGalleryItem.react"),{mediaId:t,onPreviewPress:e,reel:w.find(function(a){return a.id===(i==null?void 0:i.id)}),variant:a,viewer:x})})},b)}),e[16]=f,e[17]=g,e[18]=t,e[19]=u,e[20]=v,e[21]=a,e[22]=w,e[23]=x,e[24]=j):j=e[24];return j}function l(){return k.jsx("div",{})}l.displayName=l.name+" [from "+f.id+"]";function m(a){return a.id}g["default"]=a}),98);
__d("PolarisStoriesV3GalleryNavigationContext",["emptyFunction","react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({hasNext:!1,hasPrev:!1,next:c("emptyFunction"),prev:c("emptyFunction")});g["default"]=b}),98);
__d("PolarisStoriesV3GalleryNavigationContextProvider_reels.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"PolarisStoriesV3GalleryNavigationContextProvider_reels",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3GalleryNavigationContextProvider.react",["CometRelay","PolarisStoriesV3GalleryNavigationContext","PolarisStoriesV3GalleryNavigationContextProvider_reels.graphql","PolarisStoriesV3Reel","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));e=i;e.useCallback;e.useMemo;function a(a){var e=d("react-compiler-runtime").c(20),f=a.children,g=a.currentReel,i=a.onChange,k=a.reelList;a=a.reels;var l=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3GalleryNavigationContextProvider_reels.graphql"),a),m=k.getIndex(g);a=m>0;g=m<k.getMaxLength()-1;var n;e[0]!==l?(n=function(a){switch(a.type){case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad:return!0;case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic:return l.some(function(b){return b.id===a.id})}},e[0]=l,e[1]=n):n=e[1];var o=n;e[2]!==o||e[3]!==m||e[4]!==i||e[5]!==k?(n=function(a){var b=k.get(m-1);b!=null&&o(b)&&i(b,a)},e[2]=o,e[3]=m,e[4]=i,e[5]=k,e[6]=n):n=e[6];n=n;var p;e[7]!==o||e[8]!==m||e[9]!==i||e[10]!==k?(p=function(a){var b=k.get(m+1);b!=null&&o(b)&&i(b,a)},e[7]=o,e[8]=m,e[9]=i,e[10]=k,e[11]=p):p=e[11];p=p;var q;e[12]!==g||e[13]!==a||e[14]!==p||e[15]!==n?(q={hasNext:g,hasPrev:a,next:p,prev:n},e[12]=g,e[13]=a,e[14]=p,e[15]=n,e[16]=q):q=e[16];g=q;a=g;e[17]!==f||e[18]!==a?(p=j.jsx(c("PolarisStoriesV3GalleryNavigationContext").Provider,{value:a,children:f}),e[17]=f,e[18]=a,e[19]=p):p=e[19];return p}g["default"]=a}),98);
__d("PolarisStoriesV3LoggingReelType",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum")({Story:"story",Highlight:"highlight"});c=a;f["default"]=c}),66);
__d("PolarisStoriesV3MobileAdGalleryItem_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MobileAdGalleryItem_ad",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdGalleryPlayerContainer_ad"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3NextAdMediaId_ad"}],type:"XDTAdMediaItem",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MobileAdGalleryItem_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MobileAdGalleryItem_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3AdGalleryPlayerContainer_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MobileGalleryItemVariant",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum").Mirrored(["Player","Previous","Next"]);c=a;f["default"]=c}),66);
__d("PolarisStoriesV3MobileAdGalleryItem.react",["CometRelay","PolarisStoriesV3AdGalleryPlayerContainer.react","PolarisStoriesV3MobileAdGalleryItem_ad.graphql","PolarisStoriesV3MobileAdGalleryItem_viewer.graphql","PolarisStoriesV3MobileGalleryItemVariant","react","react-compiler-runtime","usePolarisStoriesV3NextAdMediaId"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function a(a){var e=d("react-compiler-runtime").c(4),f=a.ad,g=a.currentMediaId,j=a.variant;a=a.viewer;f=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3MobileAdGalleryItem_ad.graphql"),f);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3MobileAdGalleryItem_viewer.graphql"),a);var l=c("usePolarisStoriesV3NextAdMediaId")(f);j=j===c("PolarisStoriesV3MobileGalleryItemVariant").Player?g:l;e[0]!==f||e[1]!==j||e[2]!==a?(g=k.jsx(c("PolarisStoriesV3AdGalleryPlayerContainer.react"),{ad:f,mediaId:j,viewer:a}),e[0]=f,e[1]=j,e[2]=a,e[3]=g):g=e[3];return g}g["default"]=a}),98);
__d("usePolarisStoriesV3MobileGalleryItemAnimation",["IGDSThemeConstantsHelpers","Locale","PolarisStoriesV3MobileGalleryItemVariant","react","react-compiler-runtime","usePrevious"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useEffect,j=b.useLayoutEffect,k=b.useRef,l=b.useState,m=d("IGDSThemeConstantsHelpers").getNumericValue("story-swap-animation-duration"),n="ease-in-out",o="rotateY(90deg) translateX(50%) rotateY(0deg) translateX(-50%) rotateY(-90deg)",p="rotateY(90deg) translateX(50%) rotateY(-90deg) translateX(-50%) rotateY(-90deg)",q="rotateY(90deg) translateX(50%) rotateY(90deg) translateX(-50%) rotateY(-90deg)",r="rotateY(90deg) translateX(50%) rotateY(0deg) translateX(-50%) rotateY(-90deg)",s="rotateY(-90deg) translateX(-50%) rotateY(0deg) translateX(50%) rotateY(90deg)",t="rotateY(-90deg) translateX(-50%) rotateY(90deg) translateX(50%) rotateY(90deg)",u="rotateY(-90deg) translateX(-50%) rotateY(-90deg) translateX(50%) rotateY(90deg)",v="rotateY(-90deg) translateX(-50%) rotateY(0deg) translateX(50%) rotateY(90deg)";function w(a,b){a=a;b=b;d("Locale").isRTL()&&(a!==c("PolarisStoriesV3MobileGalleryItemVariant").Player&&(a=a===c("PolarisStoriesV3MobileGalleryItemVariant").Previous?c("PolarisStoriesV3MobileGalleryItemVariant").Next:c("PolarisStoriesV3MobileGalleryItemVariant").Previous),b!==c("PolarisStoriesV3MobileGalleryItemVariant").Player&&(b=b===c("PolarisStoriesV3MobileGalleryItemVariant").Previous?c("PolarisStoriesV3MobileGalleryItemVariant").Next:c("PolarisStoriesV3MobileGalleryItemVariant").Previous));if(a===c("PolarisStoriesV3MobileGalleryItemVariant").Previous&&b===c("PolarisStoriesV3MobileGalleryItemVariant").Player)return{end:o,start:p};if(a===c("PolarisStoriesV3MobileGalleryItemVariant").Next&&b===c("PolarisStoriesV3MobileGalleryItemVariant").Player)return{end:s,start:t};if(a===c("PolarisStoriesV3MobileGalleryItemVariant").Player&&b===c("PolarisStoriesV3MobileGalleryItemVariant").Previous)return{end:u,start:v};return a===c("PolarisStoriesV3MobileGalleryItemVariant").Player&&b===c("PolarisStoriesV3MobileGalleryItemVariant").Next?{end:q,start:r}:null}function a(a,b){var e=d("react-compiler-runtime").c(17),f=k(),g=a.height,h=a.width,o=c("usePrevious")(b),p=l(!1),q=p[0],r=p[1];p=l("none");var s=p[0];p=p[1];var t=k(),u,v;e[0]===Symbol["for"]("react.memo_cache_sentinel")?(u=function(){return function(){t.current!=null&&t.current.cancel()}},v=[t],e[0]=u,e[1]=v):(u=e[0],v=e[1]);i(u,v);e[2]!==o||e[3]!==b?(u=function(){var a=f.current;if(a==null||o==null||o===b)return;var c=w(o,b);if(c!=null){r(!0);a=a.animate([{transform:c.start},{transform:c.end}],{duration:m,easing:n});a.onfinish=function(){r(!1),t.current=null};t.current=a}},e[2]=o,e[3]=b,e[4]=u):u=e[4];e[5]!==a||e[6]!==o||e[7]!==b?(v=[a,o,b,p,t,r],e[5]=a,e[6]=o,e[7]=b,e[8]=v):v=e[8];j(u,v);p=q||o===c("PolarisStoriesV3MobileGalleryItemVariant").Player&&b!==c("PolarisStoriesV3MobileGalleryItemVariant").Player;a=p?"none":"auto";e[9]!==g||e[10]!==a||e[11]!==s||e[12]!==h?(u={height:g,pointerEvents:a,position:"absolute",transform:s,width:h},e[9]=g,e[10]=a,e[11]=s,e[12]=h,e[13]=u):u=e[13];v=u;e[14]!==p||e[15]!==v?(q={isAnimating:p,ref:f,style:v},e[14]=p,e[15]=v,e[16]=q):q=e[16];return q}g["default"]=a}),98);
__d("PolarisStoriesV3MobileGalleryItem.react",["PolarisStoriesV3MobileGalleryItemVariant","react","react-compiler-runtime","usePolarisStoriesV3MobileGalleryItemAnimation"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(4),e=a.children,f=a.config;a=a.variant;f=c("usePolarisStoriesV3MobileGalleryItemAnimation")(f,a);var g=f.isAnimating,h=f.ref;f=f.style;if(a!==c("PolarisStoriesV3MobileGalleryItemVariant").Player&&!g)return null;b[0]!==e||b[1]!==h||b[2]!==f?(a=i.jsx("div",{ref:h,style:f,children:e}),b[0]=e,b[1]=h,b[2]=f,b[3]=a):a=b[3];return a}g["default"]=a}),98);
__d("PolarisStoriesV3MobileGalleryItems_reels.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"PolarisStoriesV3MobileGalleryItems_reels",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MobileReelGalleryItem_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MobileGalleryItems_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MobileGalleryItems_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MobileAdGalleryItem_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MobileReelGalleryItem_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MobileReelGalleryItem_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MobileReelGalleryItem_reel",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3GalleryPlayer_reel"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3NextReelMediaId_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MobileReelGalleryItem_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3MobileReelGalleryItem_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3GalleryPlayer_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3MobileReelGalleryItem.react",["CometRelay","PolarisStoriesV3GalleryPlayer.react","PolarisStoriesV3MobileGalleryItemVariant","PolarisStoriesV3MobileReelGalleryItem_reel.graphql","PolarisStoriesV3MobileReelGalleryItem_viewer.graphql","react","react-compiler-runtime","usePolarisStoriesV3NextReelMediaId"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react");function l(a){var e=d("react-compiler-runtime").c(4),f=a.currentMediaId,g=a.reel,j=a.variant;a=a.viewer;g=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3MobileReelGalleryItem_reel.graphql"),g);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3MobileReelGalleryItem_viewer.graphql"),a);var l=c("usePolarisStoriesV3NextReelMediaId")(g);j=j===c("PolarisStoriesV3MobileGalleryItemVariant").Player?f:l;e[0]!==j||e[1]!==g||e[2]!==a?(f=k.jsx(c("PolarisStoriesV3GalleryPlayer.react"),{mediaId:j,reel:g,viewer:a}),e[0]=j,e[1]=g,e[2]=a,e[3]=f):f=e[3];return f}function a(a){var b=d("react-compiler-runtime").c(5),c=a.currentMediaId,e=a.reel,f=a.variant;a=a.viewer;if(e==null)return null;var g;b[0]!==c||b[1]!==e||b[2]!==f||b[3]!==a?(g=k.jsx(l,{currentMediaId:c,reel:e,variant:f,viewer:a}),b[0]=c,b[1]=e,b[2]=f,b[3]=a,b[4]=g):g=b[4];return g}g["default"]=a}),98);
__d("usePolarisStoriesV3MobileGalleryRenderedItems",["PolarisStoriesV3MobileGalleryItemVariant","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useMemo;function a(a){var b=d("react-compiler-runtime").c(13),e=a.reel;a=a.reelList;var f=a.getIndex(e),g;b[0]!==f||b[1]!==e?(g={index:f,reel:e,variant:c("PolarisStoriesV3MobileGalleryItemVariant").Player},b[0]=f,b[1]=e,b[2]=g):g=b[2];e=g;g=f-1;var h;b[3]!==g||b[4]!==a?(h=a.has(g)?{index:g,reel:a.get(g),variant:c("PolarisStoriesV3MobileGalleryItemVariant").Previous}:null,b[3]=g,b[4]=a,b[5]=h):h=b[5];g=h;h=f+1;b[6]!==h||b[7]!==a?(f=a.has(h)?{index:h,reel:a.get(h),variant:c("PolarisStoriesV3MobileGalleryItemVariant").Next}:null,b[6]=h,b[7]=a,b[8]=f):f=b[8];h=f;b[9]!==h||b[10]!==e||b[11]!==g?(a={nextItem:h,playerItem:e,prevItem:g},b[9]=h,b[10]=e,b[11]=g,b[12]=a):a=b[12];f=a;return f}g["default"]=a}),98);
__d("PolarisStoriesV3MobileGalleryItems.react",["CometRelay","FBLogger","PolarisStoriesV3MobileAdGalleryItem.react","PolarisStoriesV3MobileGalleryItem.react","PolarisStoriesV3MobileGalleryItems_reels.graphql","PolarisStoriesV3MobileGalleryItems_viewer.graphql","PolarisStoriesV3MobileReelGalleryItem.react","PolarisStoriesV3Reel","react","react-compiler-runtime","usePolarisStoriesV3GalleryPaginationLoaderEffect","usePolarisStoriesV3MobileGalleryRenderedItems"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react"));j.useMemo;function a(a){var e=d("react-compiler-runtime").c(26),f=a.adsPool,g=a.config,j=a.currentMediaId,m=a.currentReel,n=a.hasNext,o=a.hasPrevious,p=a.isLoadingNext,q=a.isLoadingPrevious,r=a.loadNext,s=a.loadPrevious,t=a.reelList,u=a.reels;a=a.viewer;var v=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3MobileGalleryItems_reels.graphql"),u),w=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3MobileGalleryItems_viewer.graphql"),a);e[0]!==m||e[1]!==t?(u={reel:m,reelList:t},e[0]=m,e[1]=t,e[2]=u):u=e[2];a=c("usePolarisStoriesV3MobileGalleryRenderedItems")(u);t=a.nextItem;u=a.playerItem;a=a.prevItem;var x;e[3]!==v?(x=v.map(l),e[3]=v,e[4]=x):x=e[4];x=x;x=x;var y;e[5]!==g.fetchDistance||e[6]!==m||e[7]!==n||e[8]!==o||e[9]!==p||e[10]!==q||e[11]!==r||e[12]!==s||e[13]!==x?(y={fetchDistance:g.fetchDistance,hasNext:n,hasPrevious:o,isLoadingNext:p,isLoadingPrevious:q,loadNext:r,loadPrevious:s,reel:m,reelIds:x},e[5]=g.fetchDistance,e[6]=m,e[7]=n,e[8]=o,e[9]=p,e[10]=q,e[11]=r,e[12]=s,e[13]=x,e[14]=y):y=e[14];c("usePolarisStoriesV3GalleryPaginationLoaderEffect")(y);e[15]===Symbol["for"]("react.memo_cache_sentinel")?(m={className:"x5yr21d x1h5j8bw xh8yej3"},n={className:"x1oyok0e"},e[15]=m,e[16]=n):(m=e[15],n=e[16]);e[17]!==f||e[18]!==g||e[19]!==j||e[20]!==t||e[21]!==u||e[22]!==a||e[23]!==v||e[24]!==w?(o=k.jsx("div",babelHelpers["extends"]({},m,{children:k.jsx("div",babelHelpers["extends"]({},n,{children:[a,t,u].filter(Boolean).map(function(a){var b=a.index,e=a.reel;a=a.variant;var h;if(e!=null&&e.type===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad){if(f==null)throw c("FBLogger")("ig_web").mustfixThrow("Ads pool is null");h=k.jsx(c("PolarisStoriesV3MobileAdGalleryItem.react"),{ad:f.getAd(e.id).data,currentMediaId:j,variant:a,viewer:w})}else h=k.jsx(c("PolarisStoriesV3MobileReelGalleryItem.react"),{currentMediaId:j,reel:v.find(function(a){return a.id===(e==null?void 0:e.id)}),variant:a,viewer:w});return k.jsx(c("PolarisStoriesV3MobileGalleryItem.react"),{config:g,variant:a,children:h},b)})}))})),e[17]=f,e[18]=g,e[19]=j,e[20]=t,e[21]=u,e[22]=a,e[23]=v,e[24]=w,e[25]=o):o=e[25];return o}function l(a){return a.id}g["default"]=a}),98);
__d("PolarisStoriesV3MobileGalleryLayout.react",["PolarisStoriesV3ViewerSizeContext","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));h.useMemo;function a(a){var b=d("react-compiler-runtime").c(10),e=a.children;a=a.config;var f;b[0]!==a.height||b[1]!==a.width?(f={height:a.height,width:a.width},b[0]=a.height,b[1]=a.width,b[2]=f):f=b[2];a=f;f=a;b[3]===Symbol["for"]("react.memo_cache_sentinel")?(a="x6s0dn4 x78zum5 x1n2onr6",b[3]=a):a=b[3];b[4]!==e||b[5]!==f?(a=i.jsx("div",{className:a,style:f,children:e}),b[4]=e,b[5]=f,b[6]=a):a=b[6];b[7]!==a||b[8]!==f?(e=i.jsx(c("PolarisStoriesV3ViewerSizeContext").Provider,{value:f,children:a}),b[7]=a,b[8]=f,b[9]=e):e=b[9];return e}g["default"]=a}),98);
__d("PolarisStoriesV3NavigationContextProvider_medias.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"PolarisStoriesV3NavigationContextProvider_medias",selections:[{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ReelMediaIds_medias"}],type:"XDTMediaDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3GalleryNavigationContext",["PolarisStoriesV3GalleryNavigationContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3GalleryNavigationContext"))}g["default"]=a}),98);
__d("usePolarisStoriesV3NavigationKeyCommands",["getPolarisKeyCommandConfig","react","react-compiler-runtime","useLayerKeyCommands","usePolarisStoriesV3CreateKeyCommandHandler"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useMemo;function a(a){var b=d("react-compiler-runtime").c(25),e=a.onNext,f=a.onNextReel,g=a.onPrev,h=a.onPrevReel;a=c("usePolarisStoriesV3CreateKeyCommandHandler")();var i;b[0]!==e?(i=function(){return e("tap_forward")},b[0]=e,b[1]=i):i=b[1];var j;b[2]!==a||b[3]!==i?(j=d("getPolarisKeyCommandConfig").getPolarisKeyCommandConfig("stories","nextStory",a(i)),b[2]=a,b[3]=i,b[4]=j):j=b[4];b[5]!==g?(i=function(){return g("tap_back")},b[5]=g,b[6]=i):i=b[6];var k;b[7]!==a||b[8]!==i?(k=d("getPolarisKeyCommandConfig").getPolarisKeyCommandConfig("stories","prevStory",a(i)),b[7]=a,b[8]=i,b[9]=k):k=b[9];b[10]!==f?(i=function(){return f("swipe_forward")},b[10]=f,b[11]=i):i=b[11];var l;b[12]!==a||b[13]!==i?(l=d("getPolarisKeyCommandConfig").getPolarisKeyCommandConfig("stories","nextReel",a(i)),b[12]=a,b[13]=i,b[14]=l):l=b[14];b[15]!==h?(i=function(){return h("swipe_back")},b[15]=h,b[16]=i):i=b[16];var m;b[17]!==a||b[18]!==i?(m=d("getPolarisKeyCommandConfig").getPolarisKeyCommandConfig("stories","prevReel",a(i)),b[17]=a,b[18]=i,b[19]=m):m=b[19];b[20]!==j||b[21]!==k||b[22]!==l||b[23]!==m?(a=[j,k,l,m],b[20]=j,b[21]=k,b[22]=l,b[23]=m,b[24]=a):a=b[24];i=a;j=i;c("useLayerKeyCommands")(j)}g["default"]=a}),98);
__d("PolarisStoriesV3NavigationContextProvider.react",["CometRelay","PolarisStoriesV3NavigationContext","PolarisStoriesV3NavigationContextProvider_medias.graphql","react","react-compiler-runtime","useCometRouterLoadingState","usePolarisStoriesV3GalleryNavigationContext","usePolarisStoriesV3LogReelPlaybackNavigation","usePolarisStoriesV3NavigationKeyCommands","usePolarisStoriesV3PageNavigationContext","usePolarisStoriesV3ReelMediaIds","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react"));e=i;e.useCallback;var k=e.useEffect;e.useMemo;var l=e.useRef;function a(a){var e=d("react-compiler-runtime").c(49),f=a.children,g=a.mediaId,i=a.medias,m=a.onChange;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3NavigationContextProvider_medias.graphql"),i);var n=c("usePolarisStoriesV3PageNavigationContext")(),o=c("usePolarisStoriesV3GalleryNavigationContext")(),p=c("usePolarisStoriesV3ViewerLoggingContext")(),q=c("usePolarisStoriesV3LogReelPlaybackNavigation")(),r=l(!1),s=c("usePolarisStoriesV3ReelMediaIds")(a),t=s.indexOf(g),u=t>0,v=t>=0&&t<s.length-1,w=c("useCometRouterLoadingState")();e[0]!==w?(i=function(){r.current=w},a=[r,w],e[0]=w,e[1]=i,e[2]=a):(i=e[1],a=e[2]);k(i,a);e[3]!==q?(g=function(a){return{onBeforeNavigate:function(){q(),a==null?void 0:a.onBeforeNavigate==null?void 0:a.onBeforeNavigate()},onNavigate:function(){a==null?void 0:a.onNavigate==null?void 0:a.onNavigate()}}},e[3]=q,e[4]=g):g=e[4];var x=g;e[5]===Symbol["for"]("react.memo_cache_sentinel")?(i=function(){return r.current},e[5]=i):i=e[5];a=i;e[6]!==o||e[7]!==x||e[8]!==n||e[9]!==p?(g=function(a,b){p.setAction(a);if(o.hasPrev){b=x(b);o.prev(b)}else n.close(a)},e[6]=o,e[7]=x,e[8]=n,e[9]=p,e[10]=g):g=e[10];var y=g;e[11]!==o||e[12]!==x||e[13]!==n||e[14]!==p?(i=function(a,b){p.setAction(a);if(o.hasNext){b=x(b);o.next(b)}else n.close(a)},e[11]=o,e[12]=x,e[13]=n,e[14]=p,e[15]=i):i=e[15];var z=i;e[16]!==x||e[17]!==u||e[18]!==t||e[19]!==s||e[20]!==m||e[21]!==y||e[22]!==p?(g=function(a){p.setAction(a);if(u){var b=s[t-1],c=x();m(b,c)}else y(a)},e[16]=x,e[17]=u,e[18]=t,e[19]=s,e[20]=m,e[21]=y,e[22]=p,e[23]=g):g=e[23];i=g;e[24]!==x||e[25]!==v||e[26]!==t||e[27]!==s||e[28]!==z||e[29]!==m||e[30]!==p?(g=function(a){p.setAction(a);if(v){var b=s[t+1],c=x();m(b,c)}else z(a)},e[24]=x,e[25]=v,e[26]=t,e[27]=s,e[28]=z,e[29]=m,e[30]=p,e[31]=g):g=e[31];g=g;var A;e[32]!==g||e[33]!==z||e[34]!==i||e[35]!==y?(A={onNext:g,onNextReel:z,onPrev:i,onPrevReel:y},e[32]=g,e[33]=z,e[34]=i,e[35]=y,e[36]=A):A=e[36];c("usePolarisStoriesV3NavigationKeyCommands")(A);A=v||o.hasNext;var B=u||o.hasPrev;e[37]!==o.hasNext||e[38]!==o.hasPrev||e[39]!==g||e[40]!==z||e[41]!==i||e[42]!==y||e[43]!==A||e[44]!==B?(a={hasNext:A,hasNextReel:o.hasNext,hasPrev:B,hasPrevReel:o.hasPrev,isNavigating:a,next:g,nextReel:z,prev:i,prevReel:y},e[37]=o.hasNext,e[38]=o.hasPrev,e[39]=g,e[40]=z,e[41]=i,e[42]=y,e[43]=A,e[44]=B,e[45]=a):a=e[45];g=a;i=g;e[46]!==f||e[47]!==i?(A=j.jsx(c("PolarisStoriesV3NavigationContext").Provider,{value:i,children:f}),e[46]=f,e[47]=i,e[48]=A):A=e[48];return A}g["default"]=a}),98);
__d("usePolarisStoriesV3LogReelPlaybackEntry",["ReelPlaybackEntryFalcoEvent","react","react-compiler-runtime","usePolarisStoriesV3GetSharedLoggingData","usePolarisStoriesV3ReelLoggingContext","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var b=d("react-compiler-runtime").c(6),e=c("usePolarisStoriesV3ViewerLoggingContext")(),g=e.initialNewReelCount,h=e.initialViewedReelCount,a=e.module,i=c("usePolarisStoriesV3ReelLoggingContext")(),j=c("usePolarisStoriesV3GetSharedLoggingData")();b[0]!==j||b[1]!==g||b[2]!==h||b[3]!==a||b[4]!==i?(e=function(){var b=j();c("ReelPlaybackEntryFalcoEvent").log(function(){return{a_pk:i.authorId,has_my_reel:String(i.hasMyReel),is_audience_close_friend:i.isAudienceCloseFriend,m_pk:i.mediaId,module_name:String(a),new_reel_count:String(g),reel_id:i.reel.id,reel_type:b.reel_type,tray_position:b.tray_position,tray_session_id:b.tray_session_id,viewed_reel_count:String(h),viewer_session_id:b.viewer_session_id}})},b[0]=j,b[1]=g,b[2]=h,b[3]=a,b[4]=i,b[5]=e):e=b[5];return e}g["default"]=a}),98);
__d("usePolarisStoriesV3LogReelPlaybackEntryOnce",["react","react-compiler-runtime","usePolarisStoriesV3LogReelPlaybackEntry","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a=d("react-compiler-runtime").c(3),b=c("usePolarisStoriesV3ViewerLoggingContext")(),e=c("usePolarisStoriesV3LogReelPlaybackEntry")(),f;a[0]!==e||a[1]!==b?(f=function(){b.hasLoggedReelPlaybackEntry()||(b.setLoggedReelPlaybackEntry(),e())},a[0]=e,a[1]=b,a[2]=f):f=a[2];return f}g["default"]=a}),98);
__d("usePolarisStoriesV3LogReelSessionSummaryEffect",["react","react-compiler-runtime","useIsMountedRef","usePolarisStoriesV3LogReelSessionSummary"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(){var a=d("react-compiler-runtime").c(4),b=c("useIsMountedRef")(),e=c("usePolarisStoriesV3LogReelSessionSummary")(),f,g;a[0]!==b||a[1]!==e?(f=function(){var a=b;return function(){a.current||e()}},g=[b,e],a[0]=b,a[1]=e,a[2]=f,a[3]=g):(f=a[2],g=a[3]);i(f,g)}g["default"]=a}),98);
__d("usePolarisStoriesV3LogReelPlaybackExit",["ReelPlaybackExitFalcoEvent","react","react-compiler-runtime","usePolarisStoriesV3GetReelPlaybackLoggingData","usePolarisStoriesV3GetSharedLoggingData","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var b=d("react-compiler-runtime").c(5),e=c("usePolarisStoriesV3ViewerLoggingContext")(),g=e.getAction,a=e.module,h=c("usePolarisStoriesV3GetSharedLoggingData")(),i=c("usePolarisStoriesV3GetReelPlaybackLoggingData")();b[0]!==g||b[1]!==h||b[2]!==i||b[3]!==a?(e=function(){c("ReelPlaybackExitFalcoEvent").log(function(){return babelHelpers["extends"]({},h(),{},i(),{action:g(),media_source:"organic",module_name:String(a)})})},b[0]=g,b[1]=h,b[2]=i,b[3]=a,b[4]=e):e=b[4];return e}g["default"]=a}),98);
__d("usePolarisStoriesV3LogReelViewerCloseOnce",["react","react-compiler-runtime","usePolarisStoriesV3LogReelPlaybackExit"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useCallback;var i=b.useRef;function a(){var a=d("react-compiler-runtime").c(2),b=i(!1),e=c("usePolarisStoriesV3LogReelPlaybackExit")(),f;a[0]!==e?(f=function(){b.current||(b.current=!0,e())},a[0]=e,a[1]=f):f=a[1];return f}g["default"]=a}),98);
__d("usePolarisStoriesV3OnStoryViewerClose",["PolarisStoriesV3OnStoryViewerCloseContext","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");var i=b.useContext,j=b.useEffect;function a(a){var b=d("react-compiler-runtime").c(4),e=i(c("PolarisStoriesV3OnStoryViewerCloseContext")),f,g;b[0]!==e||b[1]!==a?(f=function(){var b=e.add(a);return function(){return b.remove()}},g=[e,a],b[0]=e,b[1]=a,b[2]=f,b[3]=g):(f=b[2],g=b[3]);j(f,g)}g["default"]=a}),98);
__d("usePolarisStoriesV3PageKeyCommands",["getPolarisKeyCommandConfig","react","react-compiler-runtime","useLayerKeyCommands","usePolarisStoriesV3CreateKeyCommandHandler"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useMemo;function a(a){var b=d("react-compiler-runtime").c(7),e=a.onExit;a=c("usePolarisStoriesV3CreateKeyCommandHandler")();var f;b[0]!==e?(f=function(){return e("swipe_down")},b[0]=e,b[1]=f):f=b[1];var g;b[2]!==a||b[3]!==f?(g=d("getPolarisKeyCommandConfig").getPolarisKeyCommandConfig("stories","exit",a(f)),b[2]=a,b[3]=f,b[4]=g):g=b[4];b[5]!==g?(a=[g],b[5]=g,b[6]=a):a=b[6];f=a;g=f;c("useLayerKeyCommands")(g)}g["default"]=a}),98);
__d("PolarisStoriesV3PageNavigationContextProvider.react",["PolarisStoriesV3PageNavigationContext","react","react-compiler-runtime","usePolarisPopPushView","usePolarisStoriesV3LogReelPlaybackEntryOnce","usePolarisStoriesV3LogReelSessionSummaryEffect","usePolarisStoriesV3LogReelViewerCloseOnce","usePolarisStoriesV3OnStoryViewerClose","usePolarisStoriesV3PageKeyCommands","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));b=h;b.useCallback;var j=b.useEffect;b.useMemo;function a(a){var b=d("react-compiler-runtime").c(17);a=a.children;var e=c("usePolarisStoriesV3ViewerLoggingContext")(),f=c("usePolarisPopPushView")(),g=c("usePolarisStoriesV3LogReelPlaybackEntryOnce")(),h,k;b[0]!==g?(h=function(){g()},k=[g],b[0]=g,b[1]=h,b[2]=k):(h=b[1],k=b[2]);j(h,k);var l=c("usePolarisStoriesV3LogReelViewerCloseOnce")();c("usePolarisStoriesV3LogReelSessionSummaryEffect")();b[3]!==l||b[4]!==e?(h=function(){e.setAction("tap_exit"),l()},b[3]=l,b[4]=e,b[5]=h):h=b[5];k=h;c("usePolarisStoriesV3OnStoryViewerClose")(k);b[6]!==l||b[7]!==f||b[8]!==e?(h=function(a){a=a===void 0?"tap_exit":a;e.setAction(a);l();f()},b[6]=l,b[7]=f,b[8]=e,b[9]=h):h=b[9];k=h;b[10]!==k?(h={onExit:k},b[10]=k,b[11]=h):h=b[11];c("usePolarisStoriesV3PageKeyCommands")(h);b[12]!==k?(h={close:k},b[12]=k,b[13]=h):h=b[13];k=h;h=k;b[14]!==a||b[15]!==h?(k=i.jsx(c("PolarisStoriesV3PageNavigationContext").Provider,{value:h,children:a}),b[14]=a,b[15]=h,b[16]=k):k=b[16];return k}g["default"]=a}),98);
__d("PolarisStoriesV3ReelGallery_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"PolarisStoriesV3ReelGallery_ad"};e.exports=a}),null);
__d("PolarisStoriesV3ReelGallery_reels.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"PolarisStoriesV3ReelGallery_reels",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3NavigationContextProvider_medias"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3DesktopReelGalleryItems_reels"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3GalleryNavigationContextProvider_reels"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MobileGalleryItems_reels"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelLoggingContextProvider_reels"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect_reels"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3ReelChangeHandler_reels"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelGallery_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelGallery_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3DesktopReelGalleryItems_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3MobileGalleryItems_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelLoggingContextProvider_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3LogOverallAdsSessionLogger",["PolarisAdsSessionDataLogger","react","react-compiler-runtime","usePolarisStoriesV3OnStoryViewerClose","usePolarisStoriesV3ViewerLoggingContext","usePrevious"],(function(a,b,c,d,e,f,g){"use strict";var h;b=h||d("react");b.useCallback;var i=b.useEffect;function a(b,e){var g=d("react-compiler-runtime").c(13),h=c("usePolarisStoriesV3ViewerLoggingContext")(),a=h.module,j=h.traySessionId,k=h.viewerSessionId,l=c("usePrevious")(e);g[0]!==b||g[1]!==a||g[2]!==j||g[3]!==k?(h=function(){var c=b.getSessionLoggingData();d("PolarisAdsSessionDataLogger").logOverallAdSessionData(c,a,j,k)},g[0]=b,g[1]=a,g[2]=j,g[3]=k,g[4]=h):h=g[4];var m=h;g[5]!==m||g[6]!==(l==null?void 0:l.id)||g[7]!==e.id?(h=function(){(l==null?void 0:l.id)!==e.id&&m()},g[5]=m,g[6]=l==null?void 0:l.id,g[7]=e.id,g[8]=h):h=g[8];var n;g[9]!==m||g[10]!==l||g[11]!==e?(n=[m,l,e],g[9]=m,g[10]=l,g[11]=e,g[12]=n):n=g[12];i(h,n);c("usePolarisStoriesV3OnStoryViewerClose")(m)}g["default"]=a}),98);
__d("XPolarisStoriesMediaControllerParamsRefineValidator",["coerceRouteParams","refine"],(function(a,b,c,d,e,f,g){e=(b=d("refine")).or(b.literal(null),b.string());f=b.or(b.literal(null),b.number());d=b.object({initial_media_id:e,r:f,username:b.string()});var h=b.coercion(d),i=Object.freeze({username:{legacyNames:[],"default":null,coercibleType:"STRING"},initial_media_id:{legacyNames:[],"default":null,coercibleType:"STRING"},r:{legacyNames:[],"default":null,coercibleType:"INT"}});function a(a){return h(c("coerceRouteParams")(a,i))}g.refineXPolarisStoriesMediaControllerParams=a}),98);
__d("usePolarisStoriesV3RouteBuilder",["CometRouteParams","XPolarisStoriesMediaControllerParamsRefineValidator","XPolarisStoriesMediaControllerRouteBuilder","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;var i=1;function a(){var a=d("react-compiler-runtime").c(2),b=d("CometRouteParams").useCometRefinedRouteParams(d("XPolarisStoriesMediaControllerParamsRefineValidator").refineXPolarisStoriesMediaControllerParams),e=b==null?void 0:b.username;a[0]!==e?(b=function(a){return c("XPolarisStoriesMediaControllerRouteBuilder").buildUri({initial_media_id:a,r:i,username:String(e)}).toString()},a[0]=e,a[1]=b):b=a[1];return b}g["default"]=a}),98);
__d("usePolarisStoriesV3MediaChangeHandler",["CometRouteParams","XPolarisStoriesMediaControllerParamsRefineValidator","react","react-compiler-runtime","useCometRouterDispatcher","useCurrentRoute","usePolarisStoriesV3RefinedReelPagePassthroughProps","usePolarisStoriesV3RouteBuilder"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(){var a,b=d("react-compiler-runtime").c(6),e=c("usePolarisStoriesV3RouteBuilder")(),f=c("useCometRouterDispatcher")(),g=d("CometRouteParams").useCometRefinedRouteParams(d("XPolarisStoriesMediaControllerParamsRefineValidator").refineXPolarisStoriesMediaControllerParams),h=c("useCurrentRoute")(),i=c("usePolarisStoriesV3RefinedReelPagePassthroughProps")();if(b[0]!==e||b[1]!==(h==null?void 0:(a=h.rootView)==null?void 0:(a=a.props)==null?void 0:a.user_id)||b[2]!==f||b[3]!==i||b[4]!==(g==null?void 0:g.initial_media_id)){var j;a=function(a,b){var c;a=e(a);var d=i!=null?i.initialMediaId:g==null?void 0:g.initial_media_id;c=(c=i==null?void 0:i.initialReelId)!=null?c:h==null?void 0:(c=h.rootView)==null?void 0:(c=c.props)==null?void 0:c.user_id;var j=i==null?void 0:i.reelIds;b==null?void 0:b.onBeforeNavigate==null?void 0:b.onBeforeNavigate();f==null?void 0:f.go(a,{onNavigate:b==null?void 0:b.onNavigate,passthroughProps:{adId:i==null?void 0:i.adId,initialMediaId:d,initialReelId:c,reelIds:j},replace:!0,target:"self"})};b[0]=e;b[1]=h==null?void 0:(j=h.rootView)==null?void 0:(j=j.props)==null?void 0:j.user_id;b[2]=f;b[3]=i;b[4]=g==null?void 0:g.initial_media_id;b[5]=a}else a=b[5];return a}g["default"]=a}),98);
__d("usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect_ad"};e.exports=a}),null);
__d("usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect_reels.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect_reels",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3RoutePrefetcher",["react","useCometRouterDispatcher","usePolarisStoriesV3UserReelRouteBuilder","useStable"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useMemo;function a(){var a=c("useStable")(function(){return new Set()}),b=c("usePolarisStoriesV3UserReelRouteBuilder")(),d=c("useCometRouterDispatcher")();return i(function(){return{hasPrefetched:function(b){return a.has(b)},prefetch:function(c,e,f){if(!a.has(c)){f=[b(e)].concat(f.map(function(a){return b(e,a)}));f.forEach(function(a){return d==null?void 0:d.prefetchRouteDefinition(a)});a.add(c)}}}},[d,a,b])}g["default"]=a}),98);
__d("usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect",["CometRelay","PolarisStoriesV3Reel","getPolarisStoriesV3AdUsername","react","react-compiler-runtime","usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect_ad.graphql","usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect_reels.graphql","usePolarisStoriesV3ReelListItems","usePolarisStoriesV3RoutePrefetcher"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=(j||d("react")).useEffect;function a(a,e,f){var g=d("react-compiler-runtime").c(6),j=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect_reels.graphql"),e),n=c("usePolarisStoriesV3ReelListItems")(f),o=c("usePolarisStoriesV3RoutePrefetcher")();g[0]!==a||g[1]!==n||g[2]!==j||g[3]!==o?(e=function(){n.filter(function(a){return!o.hasPrefetched(a.id)}).forEach(function(e){bb5:switch(e.type){case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad:var f=a.getAd(e.id).data;f=d("CometRelay").readInlineData(i!==void 0?i:i=b("usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect_ad.graphql"),f);var g=c("getPolarisStoriesV3AdUsername")(f);f=f.items.map(m);o.prefetch(e.id,g,f);break bb5;case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic:g=j.find(function(a){return a.id===e.id});if(g!=null){f=g.user.username;g=g.items.map(l);o.prefetch(e.id,f,g)}}})},f=[a,j,n,o],g[0]=a,g[1]=n,g[2]=j,g[3]=o,g[4]=e,g[5]=f):(e=g[4],f=g[5]);k(e,f)}function l(a){return a.pk}function m(a){return a.pk}g["default"]=a}),98);
__d("usePolarisStoriesV3ReelChangeHandler_ad.graphql",[],(function(a,b,c,d,e,f){"use strict";a={kind:"InlineDataFragment",name:"usePolarisStoriesV3ReelChangeHandler_ad"};e.exports=a}),null);
__d("usePolarisStoriesV3ReelChangeHandler_reels.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null};return{argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"usePolarisStoriesV3ReelChangeHandler_reels",selections:[{kind:"RequiredField",field:a,action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"},{kind:"InlineDataFragmentSpread",name:"getPolarisStoriesV3NextReelMediaId_reel",selections:[a,{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"seen",storageKey:null}],args:null,argumentDefinitions:[]}],type:"XDTReelDict",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisStoriesV3ReelChangeHandler",["CometRelay","FBLogger","PolarisStoriesV3Reel","getPolarisStoriesV3AdUsername","getPolarisStoriesV3NextAdMediaId","getPolarisStoriesV3NextReelMediaId","react","react-compiler-runtime","useCometRouterDispatcher","usePolarisStoriesV3ReelChangeHandler_ad.graphql","usePolarisStoriesV3ReelChangeHandler_reels.graphql","usePolarisStoriesV3ReelLinkPropsBuilder","usePolarisStoriesV3SeenStateContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j;(j||d("react")).useCallback;function a(a,e){var f=d("react-compiler-runtime").c(6),g=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3ReelChangeHandler_reels.graphql"),e),j=c("useCometRouterDispatcher")(),k=c("usePolarisStoriesV3ReelLinkPropsBuilder")(),l=c("usePolarisStoriesV3SeenStateContext")();f[0]!==a||f[1]!==k||f[2]!==j||f[3]!==g||f[4]!==l?(e=function(e,f){var h;bb2:switch(e.type){case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad:var m=a.getAd(e.id).data;m=d("CometRelay").readInlineData(i!==void 0?i:i=b("usePolarisStoriesV3ReelChangeHandler_ad.graphql"),m);h={mediaId:c("getPolarisStoriesV3NextAdMediaId")(m,l),username:c("getPolarisStoriesV3AdUsername")(m)};break bb2;case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic:m=g.find(function(a){return a.id===e.id});if(m==null)throw c("FBLogger")("ig_web").mustfixThrow("Reel not found");h={mediaId:c("getPolarisStoriesV3NextReelMediaId")(m,l),username:m.user.username}}if(h==null)throw c("FBLogger")("ig_web").mustfixThrow("Missing args");m=k(h.username,e,h.mediaId,f==null?void 0:f.onNavigate);var n=m.onNavigate,o=m.passthroughProps,p=m.replace,q=m.routeTarget;m=m.url;f==null?void 0:f.onBeforeNavigate==null?void 0:f.onBeforeNavigate();j==null?void 0:j.go(m,{onNavigate:n,passthroughProps:o,replace:p,target:q})},f[0]=a,f[1]=k,f[2]=j,f[3]=g,f[4]=l,f[5]=e):e=f[5];return e}g["default"]=a}),98);
__d("PolarisStoriesV3ReelGallery.react",["CometRelay","FBLogger","PolarisStoriesV3AdsPoolContext","PolarisStoriesV3DesktopGalleryLayout.react","PolarisStoriesV3DesktopReelGalleryItems.react","PolarisStoriesV3GalleryNavigationContextProvider.react","PolarisStoriesV3MobileGalleryItems.react","PolarisStoriesV3MobileGalleryLayout.react","PolarisStoriesV3NavigationContextProvider.react","PolarisStoriesV3PageNavigationContextProvider.react","PolarisStoriesV3Reel","PolarisStoriesV3ReelGallery_ad.graphql","PolarisStoriesV3ReelGallery_reels.graphql","PolarisStoriesV3ReelGallery_viewer.graphql","PolarisStoriesV3ReelListContext","PolarisStoriesV3ReelLoggingContextProvider.react","react","react-compiler-runtime","usePolarisStoriesV3GalleryConfig","usePolarisStoriesV3LogOverallAdsSessionLogger","usePolarisStoriesV3MediaChangeHandler","usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect","usePolarisStoriesV3ReelChangeHandler"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k,l=k||d("react");function a(a){var e=d("react-compiler-runtime").c(44),f=a.adsPool,g=a.currentReel,k=a.hasNext,m=a.hasPrevious,n=a.isLoadingNext,o=a.isLoadingPrevious,p=a.loadNext,q=a.loadPrevious,r=a.mediaId,s=a.reelList,t=a.reels;a=a.viewer;t=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelGallery_reels.graphql"),t);a=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ReelGallery_viewer.graphql"),a);c("usePolarisStoriesV3PrefetchReelsRouteDefinitionsEffect")(f,t,s);c("usePolarisStoriesV3LogOverallAdsSessionLogger")(f,g);var u=c("usePolarisStoriesV3GalleryConfig")(),v=c("usePolarisStoriesV3ReelChangeHandler")(f,t),w=c("usePolarisStoriesV3MediaChangeHandler")(),x;bb0:switch(g.type){case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad:if(e[0]!==f||e[1]!==g.id){var y=f==null?void 0:f.getAd(g.id).data;y=d("CometRelay").readInlineData(j!==void 0?j:j=b("PolarisStoriesV3ReelGallery_ad.graphql"),y);e[0]=f;e[1]=g.id;e[2]=y}else y=e[2];y=y;x=y.items;break bb0;case d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic:e[3]!==g.id||e[4]!==t?(y=t.find(function(a){return a.id===g.id}),e[3]=g.id,e[4]=t,e[5]=y):y=e[5];y=y;if(y==null)throw c("FBLogger")("ig_web").mustfixThrow("Reel not found in story gallery");x=y.items}if(x==null)throw c("FBLogger")("ig_web").mustfixThrow("Missing media items for story");e[6]!==f||e[7]!==u||e[8]!==g||e[9]!==k||e[10]!==m||e[11]!==n||e[12]!==o||e[13]!==p||e[14]!==q||e[15]!==r||e[16]!==s||e[17]!==t||e[18]!==a?(y=u.type==="DESKTOP"?l.jsx(c("PolarisStoriesV3DesktopGalleryLayout.react"),{config:u,children:l.jsx(c("PolarisStoriesV3DesktopReelGalleryItems.react"),{adsPool:f,config:u,currentReel:g,hasNext:k,hasPrevious:m,isLoadingNext:n,isLoadingPrevious:o,loadNext:p,loadPrevious:q,mediaId:r,reelList:s,reels:t,viewer:a})}):l.jsx(c("PolarisStoriesV3MobileGalleryLayout.react"),{config:u,children:l.jsx(c("PolarisStoriesV3MobileGalleryItems.react"),{adsPool:f,config:u,currentMediaId:r,currentReel:g,hasNext:k,hasPrevious:m,isLoadingNext:n,isLoadingPrevious:o,loadNext:p,loadPrevious:q,reelList:s,reels:t,viewer:a})}),e[6]=f,e[7]=u,e[8]=g,e[9]=k,e[10]=m,e[11]=n,e[12]=o,e[13]=p,e[14]=q,e[15]=r,e[16]=s,e[17]=t,e[18]=a,e[19]=y):y=e[19];e[20]!==w||e[21]!==r||e[22]!==x||e[23]!==y?(u=l.jsx(c("PolarisStoriesV3NavigationContextProvider.react"),{mediaId:r,medias:x,onChange:w,children:y}),e[20]=w,e[21]=r,e[22]=x,e[23]=y,e[24]=u):u=e[24];e[25]!==g||e[26]!==v||e[27]!==s||e[28]!==t||e[29]!==u?(k=l.jsx(c("PolarisStoriesV3PageNavigationContextProvider.react"),{children:l.jsx(c("PolarisStoriesV3GalleryNavigationContextProvider.react"),{currentReel:g,onChange:v,reelList:s,reels:t,children:u})}),e[25]=g,e[26]=v,e[27]=s,e[28]=t,e[29]=u,e[30]=k):k=e[30];e[31]!==f||e[32]!==g||e[33]!==r||e[34]!==t||e[35]!==k||e[36]!==a?(m=l.jsx(c("PolarisStoriesV3ReelLoggingContextProvider.react"),{adsPool:f,currentReel:g,mediaId:r,reels:t,viewer:a,children:k}),e[31]=f,e[32]=g,e[33]=r,e[34]=t,e[35]=k,e[36]=a,e[37]=m):m=e[37];e[38]!==f||e[39]!==m?(n=l.jsx(c("PolarisStoriesV3AdsPoolContext").Provider,{value:f,children:m}),e[38]=f,e[39]=m,e[40]=n):n=e[40];e[41]!==s||e[42]!==n?(o=l.jsx(c("PolarisStoriesV3ReelListContext").Provider,{value:s,children:n}),e[41]=s,e[42]=n,e[43]=o):o=e[43];return o}g["default"]=a}),98);
__d("PolarisStoriesV3ReelList",["PolarisStoriesV3AdsPoolAdState","PolarisStoriesV3Reel","SubscriptionList"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){var b=this,e=a.adsPool,f=a.initialReelId,g=a.initialReelIds,h=a.traySessionId;a=a.viewerSessionId;this.getSnapshot=function(){return b.$8};this.subscribe=function(a){var c=b.$7.add(a);return function(){c.remove()}};this.$1=e;this.$2=f;this.$3=g;this.$9=h;this.$10=a;e=g.indexOf(f);this.$5=e;this.$6=g.slice(0,e+1).map(function(a){return{id:a,type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic}});this.$4=g.slice(e+1);this.$7=new(c("SubscriptionList"))();this.$8=[].concat(this.$6)}var b=a.prototype;b.getIndex=function(a){return this.$6.findIndex(function(b){return b.id===a.id&&b.type===a.type})};b.getIndexById=function(a){return this.$6.findIndex(function(b){return b.id===a})};b.get=function(a){if(a<this.$6.length)return this.$6[a];var b=this.$1,c=this.$11();while(this.$6.length<=a&&this.$4.length>0){var e;if(b!=null&&c!=null&&((e=c)==null?void 0:e.index)===this.$6.length){e=c.ad.id;var f=new Set(this.$6.filter(function(a){return a.type===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad}).map(function(a){return a.id}));f=b.validateOnInject(e,f,this.$9,this.$10);if(f){(f=this.$1)==null?void 0:f.setAdInjected(e);this.$6.push({id:e,type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad})}c=this.$11()}else this.$6.push({id:this.$4.shift(),type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic})}this.$12();return this.$6[a]};b.has=function(a){return a>=0&&a<this.getMaxLength()};b.getMaxLength=function(){return this.$6.length+this.$4.length};b.getEntryPointIndex=function(){return this.$5};b.getInitialReelIds=function(){return this.$3};b.remove=function(a){this.$6=this.$6.filter(function(b){return!(b.id===a.id&&b.type===a.type)}),this.$12()};b.reset=function(a){var b;this.$13();a=this.getIndexById(a);var c=this.$6.findLastIndex(function(a){return a.type===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad});a=Math.max(a,c)+1;c=this.$6.slice(a).map(function(a){return a.id});(b=this.$4).unshift.apply(b,c);this.$6=this.$6.slice(0,a)};b.$11=function(){var a=this.$1;if(a==null)return null;var b=this.$6.findLastIndex(function(a){return a.type===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad});a=a.getNextAdWithIndex(this.$5,b);if(a==null)return null;b=Math.max(this.$6.length,a.index);return babelHelpers["extends"]({},a,{index:b})};b.$13=function(){var a=this,b=this.$1;if(b==null)return;var e=this.$6.filter(function(a){if(a.type===d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad){a=b.getAd(a.id);if(a.state===c("PolarisStoriesV3AdsPoolAdState").Injected)return!0}return!1}).reverse();e.forEach(function(c){b.returnAdToPool(c.id),a.remove(c)})};b.$12=function(){this.$6.length!==this.$8.length&&(this.$8=[].concat(this.$6),this.$7.fireCallbacks())};return a}();g["default"]=a}),98);
__d("PolarisStoriesV3ReelPageGalleryQuery.graphql",["PolarisStoriesV3ReelPageGalleryQuery_instagramRelayOperation","relay-runtime"],(function(a,b,c,d,e,f){"use strict";a=function(){var a={defaultValue:null,kind:"LocalArgument",name:"after"},c={defaultValue:null,kind:"LocalArgument",name:"before"},d={defaultValue:null,kind:"LocalArgument",name:"first"},e={defaultValue:null,kind:"LocalArgument",name:"initial_reel_id"},f={defaultValue:!1,kind:"LocalArgument",name:"is_highlight"},g={defaultValue:null,kind:"LocalArgument",name:"last"},h={defaultValue:null,kind:"LocalArgument",name:"reel_ids"},i={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},j={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},k={alias:null,args:null,kind:"ScalarField",name:"user_id",storageKey:null},l=[{kind:"Variable",name:"after",variableName:"after"},{kind:"Variable",name:"before",variableName:"before"},{kind:"Variable",name:"first",variableName:"first"},{kind:"Variable",name:"initial_reel_id",variableName:"initial_reel_id"},{kind:"Variable",name:"last",variableName:"last"},{kind:"Variable",name:"reel_ids",variableName:"reel_ids"}],m={alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null},n={alias:null,args:null,kind:"ScalarField",name:"interop_messaging_user_fbid",storageKey:null},o={alias:null,args:null,kind:"ScalarField",name:"product_type",storageKey:null},p={alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null},q={alias:null,args:null,concreteType:"XDTIconSpec",kind:"LinkedField",name:"icon",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"icon_glyph",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"icon_type",storageKey:null},p,{alias:null,args:null,kind:"ScalarField",name:"src",storageKey:null}],storageKey:null},r=[{alias:null,args:null,kind:"ScalarField",name:"dark",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"light",storageKey:null}],s={alias:null,args:null,kind:"ScalarField",name:"text",storageKey:null};r=[{alias:null,args:null,kind:"ScalarField",name:"action",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"action_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"button_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_chevron",storageKey:null},q,{alias:null,args:null,kind:"ScalarField",name:"is_text_centered",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"secondary_text",storageKey:null},{alias:null,args:null,concreteType:"XDTTextColorSpec",kind:"LinkedField",name:"secondary_text_color",plural:!1,selections:r,storageKey:null},s,{alias:null,args:null,concreteType:"XDTTextColorSpec",kind:"LinkedField",name:"text_color",plural:!1,selections:r,storageKey:null}];r={alias:null,args:null,concreteType:"XDTMediaOverlayPayloadSchema",kind:"LinkedField",name:"media_overlay_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTButtonSpec",kind:"LinkedField",name:"banner",plural:!1,selections:r,storageKey:null},{alias:null,args:null,concreteType:"XDTBloksRenderResponse",kind:"LinkedField",name:"bloks_data",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"layout",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTButtonSpec",kind:"LinkedField",name:"buttons",plural:!0,selections:r,storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"description",storageKey:null},q,{alias:null,args:null,kind:"ScalarField",name:"misinformation_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_applied_timestamp",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_layout",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"session_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"sub_category",storageKey:null},m],storageKey:null};q={alias:null,args:null,kind:"ScalarField",name:"original_height",storageKey:null};var t={alias:null,args:null,kind:"ScalarField",name:"original_width",storageKey:null},u={alias:null,args:null,kind:"ScalarField",name:"background_color",storageKey:null},v={alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},w={alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null},x={alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},y={alias:null,args:null,kind:"ScalarField",name:"x",storageKey:null},z={alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null},A={alias:null,args:null,kind:"ScalarField",name:"rotation",storageKey:null},B={alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},C=[w],D=[y,z,x,v,A,j],E={alias:null,args:null,kind:"ScalarField",name:"link",storageKey:null},F=[u],G={alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},H={alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null};return{fragment:{argumentDefinitions:[a,c,d,e,f,g,h],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelPageGalleryQuery",selections:[{alias:null,args:null,concreteType:"XDTViewer",kind:"LinkedField",name:"xdt_viewer",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelGallery_viewer"}],storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPageGallery_query"}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:[e,h,d,g,a,c,f],kind:"Operation",name:"PolarisStoriesV3ReelPageGalleryQuery",selections:[{alias:null,args:null,concreteType:"XDTViewer",kind:"LinkedField",name:"xdt_viewer",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[i,j,k,{alias:null,args:null,kind:"ScalarField",name:"can_see_organic_insights",storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:l,concreteType:"XDTReelsMediaConnection",kind:"LinkedField",name:"xdt_api__v1__feed__reels_media__connection",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTReelsMediaEdge",kind:"LinkedField",name:"edges",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTReelDict",kind:"LinkedField",name:"node",plural:!1,selections:[j,{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[i,j,{alias:null,args:null,kind:"ScalarField",name:"has_audio",storageKey:null},{alias:null,args:null,concreteType:"XDTStoryMusicStickerTappableObject",kind:"LinkedField",name:"story_music_stickers",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTFlattenedMusicInfo",kind:"LinkedField",name:"music_asset_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"should_mute_audio",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"should_mute_audio_reason",storageKey:null},m,{alias:null,args:null,kind:"ScalarField",name:"display_artist",storageKey:null}],storageKey:null},j],storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[i,j,n],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"inventory_source",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boosted_status",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boost_unavailable_identifier",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boost_unavailable_reason",storageKey:null},o,{alias:null,args:null,kind:"ScalarField",name:"carousel_media_count",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"carousel_media",plural:!0,selections:[i,r,j,q,t],storageKey:null},r,{alias:null,args:null,concreteType:"XDTCommentDict",kind:"LinkedField",name:"caption",plural:!1,selections:[s,u,{alias:null,args:null,kind:"ScalarField",name:"background_color_alpha",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"text_color",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"text_size",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"accessibility_caption",storageKey:null},{alias:null,args:null,concreteType:"XDTImageVersion2",kind:"LinkedField",name:"image_versions2",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTImageCandidate",kind:"LinkedField",name:"candidates",plural:!0,selections:[v,w,x],storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"organic_tracking_token",storageKey:null},t,q,{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_dash_eligible",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"number_of_qualities",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"video_dash_manifest",storageKey:null},{alias:null,args:null,concreteType:"XDTVideoVersion",kind:"LinkedField",name:"video_versions",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"type",storageKey:null},w],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"media_type",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaVCRTappableObject",kind:"LinkedField",name:"visual_comment_reply_sticker_info",plural:!0,selections:[y,z,x,v,A,{alias:null,args:null,concreteType:"XDTMediaVCRTappableData",kind:"LinkedField",name:"vcr_sticker",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"original_comment_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"original_media_code",storageKey:null}],storageKey:null},j],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryBloksStickerDict",kind:"LinkedField",name:"story_bloks_stickers",plural:!0,selections:[y,z,x,v,A,{alias:null,args:null,concreteType:"XDTBloksStickerDataDict",kind:"LinkedField",name:"bloks_sticker",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTBloksStickerNativeProps",kind:"LinkedField",name:"sticker_data",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTBloksStickerNativePropsIGMention",kind:"LinkedField",name:"ig_mention",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},B],storageKey:null}],storageKey:null},j],storageKey:null},j],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryLinkTappableObject",kind:"LinkedField",name:"story_link_stickers",plural:!0,selections:[y,z,x,v,A,{alias:null,args:null,concreteType:"XDTStoryLinkInfoDict",kind:"LinkedField",name:"story_link",plural:!1,selections:C,storageKey:null},j],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryHashtagTappableObject",kind:"LinkedField",name:"story_hashtags",plural:!0,selections:[y,z,x,v,A,{alias:null,args:null,concreteType:"XDTStoryHashtagDict",kind:"LinkedField",name:"hashtag",plural:!1,selections:[p,j],storageKey:null},j],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryLocationTappableObject",kind:"LinkedField",name:"story_locations",plural:!0,selections:[y,z,x,v,A,{alias:null,args:null,concreteType:"XDTLocationDict",kind:"LinkedField",name:"location",plural:!1,selections:[i],storageKey:null},j],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryFeedMediaTappableObject",kind:"LinkedField",name:"story_feed_media",plural:!0,selections:[y,z,x,v,A,{alias:null,args:null,kind:"ScalarField",name:"media_code",storageKey:null},j,o],storageKey:null},{alias:null,args:null,concreteType:"XDTTextPostShareToIgStoryStickerTappableObject",kind:"LinkedField",name:"text_post_share_to_ig_story_stickers",plural:!0,selections:[y,z,x,v,A,{alias:null,args:null,concreteType:"XDTTextPostShareToIgStoryStickerDict",kind:"LinkedField",name:"text_post_share_to_ig_story_sticker",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"attribution_url",storageKey:null}],storageKey:null},j],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryCountdownTappableObject",kind:"LinkedField",name:"story_countdowns",plural:!0,selections:D,storageKey:null},{alias:null,args:null,concreteType:"XDTStoryQuestionTappableObject",kind:"LinkedField",name:"story_questions",plural:!0,selections:D,storageKey:null},{alias:null,args:null,concreteType:"XDTStorySliderTappableObject",kind:"LinkedField",name:"story_sliders",plural:!0,selections:D,storageKey:null},{alias:null,args:null,concreteType:"XDTStoryCTADict",kind:"LinkedField",name:"story_cta",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTAdLink",kind:"LinkedField",name:"links",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"webUri",storageKey:null}],storageKey:null}],storageKey:null},E,{alias:null,args:null,concreteType:"XDTReelMediaBackground",kind:"LinkedField",name:"reel_media_background",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMediaBackgroundColor",kind:"LinkedField",name:"top",plural:!1,selections:F,storageKey:null},{alias:null,args:null,concreteType:"XDTMediaBackgroundColor",kind:"LinkedField",name:"bottom",plural:!1,selections:F,storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"video_duration",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"preview",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"expiring_at",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_paid_partnership",storageKey:null},{alias:null,args:null,concreteType:"XDTSponsorTag",kind:"LinkedField",name:"sponsor_tags",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"is_pending",storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"sponsor",plural:!1,selections:[B,i,j],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTWearablesAppAttribution",kind:"LinkedField",name:"wearable_attribution_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"attribution_title",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"attribution_cta_action_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"attribution_type",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"reshared_story_media_author",plural:!1,selections:[G,B,j],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryAppAttributionDict",kind:"LinkedField",name:"story_app_attribution",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"app_action_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"app_icon_url",storageKey:null},p,{alias:null,args:null,kind:"ScalarField",name:"content_url",storageKey:null},E,j],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_translation",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"can_see_insights_as_brand",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"audience",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_liked",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"viewer_count",storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"viewers",plural:!0,selections:[G,j],storageKey:null},{alias:null,args:null,concreteType:"XDTSharingFrictionInfo",kind:"LinkedField",name:"sharing_friction_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"should_have_sharing_friction",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"bloks_app_url",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"can_viewer_reshare",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"ig_media_sharing_disabled",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"can_reply",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"can_reshare",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[i,H,{condition:"is_highlight",kind:"Condition",passingValue:!1,selections:[{alias:null,args:null,concreteType:"XDTRelationshipInfoDict",kind:"LinkedField",name:"friendship_status",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"following",storageKey:null}],storageKey:null}]},j,n,B,k,G,{alias:null,args:null,kind:"ScalarField",name:"is_verified",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"transparency_label",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"transparency_product",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"transparency_product_enabled",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_private",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"seen",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"reel_type",storageKey:null},{alias:null,args:null,concreteType:"XDTReelCoverMediaClientDict",kind:"LinkedField",name:"cover_media",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTReelCoverMediaImageVersionClientDict",kind:"LinkedField",name:"cropped_image_version",plural:!1,selections:C,storageKey:null},{alias:null,args:null,concreteType:"XDTReelCoverMediaImageVersionClientDict",kind:"LinkedField",name:"full_image_version",plural:!1,selections:C,storageKey:null}],storageKey:null},m,{alias:null,args:null,kind:"ScalarField",name:"latest_reel_media",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"muted",storageKey:null},H],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"cursor",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTPageInfo",kind:"LinkedField",name:"page_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"end_cursor",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_next_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_previous_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"start_cursor",storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:l,filters:["initial_reel_id","reel_ids"],handle:"connection",key:"PolarisStoriesV3ReelPageGalleryPaginationQuery__xdt_api__v1__feed__reels_media__connection",kind:"LinkedHandle",name:"xdt_api__v1__feed__reels_media__connection"}]},params:{id:b("PolarisStoriesV3ReelPageGalleryQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_viewer","xdt_api__v1__feed__reels_media__connection"]},name:"PolarisStoriesV3ReelPageGalleryQuery",operationKind:"query",text:null}}}();b("relay-runtime").PreloadableQueryRegistry.set(a.params.id,a);e.exports=a}),null);
__d("PolarisStoriesV3ReelPageGalleryPaginationQuery_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9770804383006416"}),null);
__d("PolarisStoriesV3ReelPageGalleryPaginationQuery.graphql",["PolarisStoriesV3ReelPageGalleryPaginationQuery_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"after"},{defaultValue:null,kind:"LocalArgument",name:"before"},{defaultValue:null,kind:"LocalArgument",name:"first"},{defaultValue:null,kind:"LocalArgument",name:"initial_reel_id"},{defaultValue:null,kind:"LocalArgument",name:"is_highlight"},{defaultValue:null,kind:"LocalArgument",name:"last"},{defaultValue:null,kind:"LocalArgument",name:"reel_ids"}],c=[{kind:"Variable",name:"after",variableName:"after"},{kind:"Variable",name:"before",variableName:"before"},{kind:"Variable",name:"first",variableName:"first"},{kind:"Variable",name:"initial_reel_id",variableName:"initial_reel_id"},{kind:"Variable",name:"last",variableName:"last"},{kind:"Variable",name:"reel_ids",variableName:"reel_ids"}],d={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},e={alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},f={alias:null,args:null,kind:"ScalarField",name:"title",storageKey:null},g={alias:null,args:null,kind:"ScalarField",name:"interop_messaging_user_fbid",storageKey:null},h={alias:null,args:null,kind:"ScalarField",name:"product_type",storageKey:null},i={alias:null,args:null,kind:"ScalarField",name:"name",storageKey:null},j={alias:null,args:null,concreteType:"XDTIconSpec",kind:"LinkedField",name:"icon",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"icon_glyph",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"icon_type",storageKey:null},i,{alias:null,args:null,kind:"ScalarField",name:"src",storageKey:null}],storageKey:null},k=[{alias:null,args:null,kind:"ScalarField",name:"dark",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"light",storageKey:null}],l={alias:null,args:null,kind:"ScalarField",name:"text",storageKey:null};k=[{alias:null,args:null,kind:"ScalarField",name:"action",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"action_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"button_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_chevron",storageKey:null},j,{alias:null,args:null,kind:"ScalarField",name:"is_text_centered",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"secondary_text",storageKey:null},{alias:null,args:null,concreteType:"XDTTextColorSpec",kind:"LinkedField",name:"secondary_text_color",plural:!1,selections:k,storageKey:null},l,{alias:null,args:null,concreteType:"XDTTextColorSpec",kind:"LinkedField",name:"text_color",plural:!1,selections:k,storageKey:null}];k={alias:null,args:null,concreteType:"XDTMediaOverlayPayloadSchema",kind:"LinkedField",name:"media_overlay_info",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTButtonSpec",kind:"LinkedField",name:"banner",plural:!1,selections:k,storageKey:null},{alias:null,args:null,concreteType:"XDTBloksRenderResponse",kind:"LinkedField",name:"bloks_data",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"layout",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTButtonSpec",kind:"LinkedField",name:"buttons",plural:!0,selections:k,storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"description",storageKey:null},j,{alias:null,args:null,kind:"ScalarField",name:"misinformation_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_applied_timestamp",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_layout",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"overlay_type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"session_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"sub_category",storageKey:null},f],storageKey:null};j={alias:null,args:null,kind:"ScalarField",name:"original_height",storageKey:null};var m={alias:null,args:null,kind:"ScalarField",name:"original_width",storageKey:null},n={alias:null,args:null,kind:"ScalarField",name:"background_color",storageKey:null},o={alias:null,args:null,kind:"ScalarField",name:"height",storageKey:null},p={alias:null,args:null,kind:"ScalarField",name:"url",storageKey:null},q={alias:null,args:null,kind:"ScalarField",name:"width",storageKey:null},r={alias:null,args:null,kind:"ScalarField",name:"x",storageKey:null},s={alias:null,args:null,kind:"ScalarField",name:"y",storageKey:null},t={alias:null,args:null,kind:"ScalarField",name:"rotation",storageKey:null},u={alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},v=[p],w=[r,s,q,o,t,d],x={alias:null,args:null,kind:"ScalarField",name:"link",storageKey:null},y=[n],z={alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},A={alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null};return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelPageGalleryPaginationQuery",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPageGallery_query"}],type:"Query",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"PolarisStoriesV3ReelPageGalleryPaginationQuery",selections:[{alias:null,args:c,concreteType:"XDTReelsMediaConnection",kind:"LinkedField",name:"xdt_api__v1__feed__reels_media__connection",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTReelsMediaEdge",kind:"LinkedField",name:"edges",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTReelDict",kind:"LinkedField",name:"node",plural:!1,selections:[d,{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[e,d,{alias:null,args:null,kind:"ScalarField",name:"has_audio",storageKey:null},{alias:null,args:null,concreteType:"XDTStoryMusicStickerTappableObject",kind:"LinkedField",name:"story_music_stickers",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTFlattenedMusicInfo",kind:"LinkedField",name:"music_asset_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"should_mute_audio",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"should_mute_audio_reason",storageKey:null},f,{alias:null,args:null,kind:"ScalarField",name:"display_artist",storageKey:null}],storageKey:null},d],storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[e,d,g],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"inventory_source",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boosted_status",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boost_unavailable_identifier",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"boost_unavailable_reason",storageKey:null},h,{alias:null,args:null,kind:"ScalarField",name:"carousel_media_count",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"carousel_media",plural:!0,selections:[e,k,d,j,m],storageKey:null},k,{alias:null,args:null,concreteType:"XDTCommentDict",kind:"LinkedField",name:"caption",plural:!1,selections:[l,n,{alias:null,args:null,kind:"ScalarField",name:"background_color_alpha",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"text_color",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"text_size",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"accessibility_caption",storageKey:null},{alias:null,args:null,concreteType:"XDTImageVersion2",kind:"LinkedField",name:"image_versions2",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTImageCandidate",kind:"LinkedField",name:"candidates",plural:!0,selections:[o,p,q],storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"organic_tracking_token",storageKey:null},m,j,{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_dash_eligible",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"number_of_qualities",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"video_dash_manifest",storageKey:null},{alias:null,args:null,concreteType:"XDTVideoVersion",kind:"LinkedField",name:"video_versions",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"type",storageKey:null},p],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"media_type",storageKey:null},{alias:null,args:null,concreteType:"XDTMediaVCRTappableObject",kind:"LinkedField",name:"visual_comment_reply_sticker_info",plural:!0,selections:[r,s,q,o,t,{alias:null,args:null,concreteType:"XDTMediaVCRTappableData",kind:"LinkedField",name:"vcr_sticker",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"original_comment_id",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"original_media_code",storageKey:null}],storageKey:null},d],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryBloksStickerDict",kind:"LinkedField",name:"story_bloks_stickers",plural:!0,selections:[r,s,q,o,t,{alias:null,args:null,concreteType:"XDTBloksStickerDataDict",kind:"LinkedField",name:"bloks_sticker",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTBloksStickerNativeProps",kind:"LinkedField",name:"sticker_data",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTBloksStickerNativePropsIGMention",kind:"LinkedField",name:"ig_mention",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},u],storageKey:null}],storageKey:null},d],storageKey:null},d],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryLinkTappableObject",kind:"LinkedField",name:"story_link_stickers",plural:!0,selections:[r,s,q,o,t,{alias:null,args:null,concreteType:"XDTStoryLinkInfoDict",kind:"LinkedField",name:"story_link",plural:!1,selections:v,storageKey:null},d],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryHashtagTappableObject",kind:"LinkedField",name:"story_hashtags",plural:!0,selections:[r,s,q,o,t,{alias:null,args:null,concreteType:"XDTStoryHashtagDict",kind:"LinkedField",name:"hashtag",plural:!1,selections:[i,d],storageKey:null},d],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryLocationTappableObject",kind:"LinkedField",name:"story_locations",plural:!0,selections:[r,s,q,o,t,{alias:null,args:null,concreteType:"XDTLocationDict",kind:"LinkedField",name:"location",plural:!1,selections:[e],storageKey:null},d],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryFeedMediaTappableObject",kind:"LinkedField",name:"story_feed_media",plural:!0,selections:[r,s,q,o,t,{alias:null,args:null,kind:"ScalarField",name:"media_code",storageKey:null},d,h],storageKey:null},{alias:null,args:null,concreteType:"XDTTextPostShareToIgStoryStickerTappableObject",kind:"LinkedField",name:"text_post_share_to_ig_story_stickers",plural:!0,selections:[r,s,q,o,t,{alias:null,args:null,concreteType:"XDTTextPostShareToIgStoryStickerDict",kind:"LinkedField",name:"text_post_share_to_ig_story_sticker",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"attribution_url",storageKey:null}],storageKey:null},d],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryCountdownTappableObject",kind:"LinkedField",name:"story_countdowns",plural:!0,selections:w,storageKey:null},{alias:null,args:null,concreteType:"XDTStoryQuestionTappableObject",kind:"LinkedField",name:"story_questions",plural:!0,selections:w,storageKey:null},{alias:null,args:null,concreteType:"XDTStorySliderTappableObject",kind:"LinkedField",name:"story_sliders",plural:!0,selections:w,storageKey:null},{alias:null,args:null,concreteType:"XDTStoryCTADict",kind:"LinkedField",name:"story_cta",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTAdLink",kind:"LinkedField",name:"links",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"webUri",storageKey:null}],storageKey:null}],storageKey:null},x,{alias:null,args:null,concreteType:"XDTReelMediaBackground",kind:"LinkedField",name:"reel_media_background",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTMediaBackgroundColor",kind:"LinkedField",name:"top",plural:!1,selections:y,storageKey:null},{alias:null,args:null,concreteType:"XDTMediaBackgroundColor",kind:"LinkedField",name:"bottom",plural:!1,selections:y,storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"video_duration",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"preview",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"expiring_at",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_paid_partnership",storageKey:null},{alias:null,args:null,concreteType:"XDTSponsorTag",kind:"LinkedField",name:"sponsor_tags",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"is_pending",storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"sponsor",plural:!1,selections:[u,e,d],storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTWearablesAppAttribution",kind:"LinkedField",name:"wearable_attribution_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"attribution_title",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"attribution_cta_action_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"attribution_type",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"reshared_story_media_author",plural:!1,selections:[z,u,d],storageKey:null},{alias:null,args:null,concreteType:"XDTStoryAppAttributionDict",kind:"LinkedField",name:"story_app_attribution",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"app_action_text",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"app_icon_url",storageKey:null},i,{alias:null,args:null,kind:"ScalarField",name:"content_url",storageKey:null},x,d],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_translation",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"can_see_insights_as_brand",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"audience",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_liked",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"viewer_count",storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"viewers",plural:!0,selections:[z,d],storageKey:null},{alias:null,args:null,concreteType:"XDTSharingFrictionInfo",kind:"LinkedField",name:"sharing_friction_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"should_have_sharing_friction",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"bloks_app_url",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"can_viewer_reshare",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"ig_media_sharing_disabled",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"can_reply",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"can_reshare",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[e,A,{condition:"is_highlight",kind:"Condition",passingValue:!1,selections:[{alias:null,args:null,concreteType:"XDTRelationshipInfoDict",kind:"LinkedField",name:"friendship_status",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"following",storageKey:null}],storageKey:null}]},d,g,u,{alias:null,args:null,kind:"ScalarField",name:"user_id",storageKey:null},z,{alias:null,args:null,kind:"ScalarField",name:"is_verified",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"transparency_label",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"transparency_product",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"transparency_product_enabled",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_private",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"seen",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"reel_type",storageKey:null},{alias:null,args:null,concreteType:"XDTReelCoverMediaClientDict",kind:"LinkedField",name:"cover_media",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTReelCoverMediaImageVersionClientDict",kind:"LinkedField",name:"cropped_image_version",plural:!1,selections:v,storageKey:null},{alias:null,args:null,concreteType:"XDTReelCoverMediaImageVersionClientDict",kind:"LinkedField",name:"full_image_version",plural:!1,selections:v,storageKey:null}],storageKey:null},f,{alias:null,args:null,kind:"ScalarField",name:"latest_reel_media",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"muted",storageKey:null},A],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"cursor",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTPageInfo",kind:"LinkedField",name:"page_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"end_cursor",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_next_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_previous_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"start_cursor",storageKey:null}],storageKey:null}],storageKey:null},{alias:null,args:c,filters:["initial_reel_id","reel_ids"],handle:"connection",key:"PolarisStoriesV3ReelPageGalleryPaginationQuery__xdt_api__v1__feed__reels_media__connection",kind:"LinkedHandle",name:"xdt_api__v1__feed__reels_media__connection"}]},params:{id:b("PolarisStoriesV3ReelPageGalleryPaginationQuery_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__feed__reels_media__connection"]},name:"PolarisStoriesV3ReelPageGalleryPaginationQuery",operationKind:"query",text:null}}}();e.exports=a}),null);
__d("PolarisStoriesV3ReelPageGallery_query.graphql",["PolarisStoriesV3ReelPageGalleryPaginationQuery.graphql"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=["xdt_api__v1__feed__reels_media__connection"];return{argumentDefinitions:[{kind:"RootArgument",name:"after"},{kind:"RootArgument",name:"before"},{kind:"RootArgument",name:"first"},{kind:"RootArgument",name:"initial_reel_id"},{kind:"RootArgument",name:"is_highlight"},{kind:"RootArgument",name:"last"},{kind:"RootArgument",name:"reel_ids"}],kind:"Fragment",metadata:{connection:[{count:null,cursor:null,direction:"bidirectional",path:a}],refetch:{connection:{forward:{count:"first",cursor:"after"},backward:{count:"last",cursor:"before"},path:a},fragmentPathInResult:[],operation:b("PolarisStoriesV3ReelPageGalleryPaginationQuery.graphql")}},name:"PolarisStoriesV3ReelPageGallery_query",selections:[{alias:"xdt_api__v1__feed__reels_media__connection",args:[{kind:"Variable",name:"initial_reel_id",variableName:"initial_reel_id"},{kind:"Variable",name:"reel_ids",variableName:"reel_ids"}],concreteType:"XDTReelsMediaConnection",kind:"LinkedField",name:"__PolarisStoriesV3ReelPageGalleryPaginationQuery__xdt_api__v1__feed__reels_media__connection_connection",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTReelsMediaEdge",kind:"LinkedField",name:"edges",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTReelDict",kind:"LinkedField",name:"node",plural:!1,selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelGallery_reels"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CurrentReelMedia_reels"},{alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"cursor",storageKey:null}],storageKey:null},{alias:null,args:null,concreteType:"XDTPageInfo",kind:"LinkedField",name:"page_info",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"end_cursor",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_next_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"has_previous_page",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"start_cursor",storageKey:null}],storageKey:null}],storageKey:null}],type:"Query",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisStoriesV3AdsPoolFetcherEffect",["CometRelay","PolarisIGTheme.react","react","react-compiler-runtime","usePolarisStoriesV3ViewerLoggingContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect;function a(a){var b=d("react-compiler-runtime").c(9),e=a.adsPool,f=a.currentReel,g=a.reelList,h=d("CometRelay").useRelayEnvironment();a=d("PolarisIGTheme.react").useTheme();var j=a.getTheme()===d("PolarisIGTheme.react").IGTheme.Dark;a=c("usePolarisStoriesV3ViewerLoggingContext")();var k=a.traySessionId,l=a.viewerSessionId,m;b[0]!==e||b[1]!==f||b[2]!==h||b[3]!==j||b[4]!==g||b[5]!==k||b[6]!==l?(a=function(){if(e==null)return;var a=g.getIndex(f);e.shouldFetch(a,l)&&e.fetch(h,{adRequestIndex:a,entryPointIndex:g.getEntryPointIndex(),isDarkMode:j,isPrefetch:!1,traySessionId:k,trayUserIds:g.getInitialReelIds(),viewerSessionId:l})},m=[e,g,f,h,j,k,l],b[0]=e,b[1]=f,b[2]=h,b[3]=j,b[4]=g,b[5]=k,b[6]=l,b[7]=a,b[8]=m):(a=b[7],m=b[8]);i(a,m)}g["default"]=a}),98);
__d("usePolarisStoriesV3CurrentReelMedia_reels.graphql",[],(function(a,b,c,d,e,f){"use strict";a=function(){var a={alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null};return{argumentDefinitions:[],kind:"Fragment",metadata:{plural:!0},name:"usePolarisStoriesV3CurrentReelMedia_reels",selections:[{kind:"RequiredField",field:a,action:"THROW"},{kind:"InlineDataFragmentSpread",name:"getPolarisStoriesV3NextReelMediaId_reel",selections:[a,{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"taken_at",storageKey:null}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"seen",storageKey:null}],args:null,argumentDefinitions:[]}],type:"XDTReelDict",abstractKey:null}}();e.exports=a}),null);
__d("usePolarisStoriesV3CurrentReelMedia",["CometRelay","FBLogger","PolarisStoriesV3Reel","getPolarisStoriesV3NextReelMediaId","react","react-compiler-runtime","usePolarisStoriesV3CurrentReelMedia_reels.graphql","usePolarisStoriesV3SeenStateContext"],(function(a,b,c,d,e,f,g){"use strict";var h,i;(i||d("react")).useMemo;function a(a){var e=d("react-compiler-runtime").c(17),f=a.adId,g=a.mediaId,i=a.reelId;a=a.reels;a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3CurrentReelMedia_reels.graphql"),a);var j=c("usePolarisStoriesV3SeenStateContext")();bb0:{if(f!=null){if(g==null)throw c("FBLogger")("ig_web").mustfixThrow("Missing media id for story ad");var k;e[0]!==f?(k={id:f,type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Ad},e[0]=f,e[1]=k):k=e[1];e[2]!==g||e[3]!==k?(f={mediaId:g,reel:k},e[2]=g,e[3]=k,e[4]=f):f=e[4];k=f;break bb0}e[5]!==i||e[6]!==a?(f=a.find(function(a){return a.id===i}),e[5]=i,e[6]=a,e[7]=f):f=e[7];a=f;if(a==null)throw c("FBLogger")("ig_web").mustfixThrow("Reel not found for user");e[8]!==g||e[9]!==a||e[10]!==j?(f=g!=null?g:c("getPolarisStoriesV3NextReelMediaId")(a,j),e[8]=g,e[9]=a,e[10]=j,e[11]=f):f=e[11];e[12]!==i?(g={id:i,type:d("PolarisStoriesV3Reel").PolarisStoriesV3ReelType.Organic},e[12]=i,e[13]=g):g=e[13];e[14]!==f||e[15]!==g?(a={mediaId:f,reel:g},e[14]=f,e[15]=g,e[16]=a):a=e[16];k=a}return k}g["default"]=a}),98);
__d("PolarisStoriesV3ReelPageGallery.react",["CometRelay","PolarisStoriesV3ReelGallery.react","PolarisStoriesV3ReelPageGalleryQuery.graphql","PolarisStoriesV3ReelPageGallery_query.graphql","react","react-compiler-runtime","usePolarisStoriesV3AdsPoolFetcherEffect","usePolarisStoriesV3CurrentReelMedia"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react"));j.useMemo;function a(a){var e=d("react-compiler-runtime").c(24),f=a.adId,g=a.adsPool,j=a.mediaId,m=a.query,n=a.reelList;a=a.userId;m=d("CometRelay").usePreloadedQuery(h!==void 0?h:h=b("PolarisStoriesV3ReelPageGalleryQuery.graphql"),m);var o=d("CometRelay").usePaginationFragment(i!==void 0?i:i=b("PolarisStoriesV3ReelPageGallery_query.graphql"),m),p=o.data,q=o.hasNext,r=o.hasPrevious,s=o.isLoadingNext,t=o.isLoadingPrevious,u=o.loadNext;o=o.loadPrevious;p=p.xdt_api__v1__feed__reels_media__connection.edges;var v;e[0]!==p?(v=p.map(l),e[0]=p,e[1]=v):v=e[1];p=v;v=p;e[2]!==f||e[3]!==j||e[4]!==v||e[5]!==a?(p={adId:f,mediaId:j,reelId:a,reels:v},e[2]=f,e[3]=j,e[4]=v,e[5]=a,e[6]=p):p=e[6];f=c("usePolarisStoriesV3CurrentReelMedia")(p);j=f.mediaId;a=f.reel;e[7]!==g||e[8]!==a||e[9]!==n?(p={adsPool:g,currentReel:a,reelList:n},e[7]=g,e[8]=a,e[9]=n,e[10]=p):p=e[10];c("usePolarisStoriesV3AdsPoolFetcherEffect")(p);e[11]!==g||e[12]!==a||e[13]!==q||e[14]!==r||e[15]!==s||e[16]!==t||e[17]!==u||e[18]!==o||e[19]!==j||e[20]!==m.xdt_viewer||e[21]!==n||e[22]!==v?(f=k.jsx(c("PolarisStoriesV3ReelGallery.react"),{adsPool:g,currentReel:a,hasNext:q,hasPrevious:r,isLoadingNext:s,isLoadingPrevious:t,loadNext:u,loadPrevious:o,mediaId:j,reelList:n,reels:v,viewer:m.xdt_viewer}),e[11]=g,e[12]=a,e[13]=q,e[14]=r,e[15]=s,e[16]=t,e[17]=u,e[18]=o,e[19]=j,e[20]=m.xdt_viewer,e[21]=n,e[22]=v,e[23]=f):f=e[23];return f}function l(a){return a.node}g["default"]=a}),98);
__d("PolarisStoriesV3ReelPageStandaloneQuery",["PolarisStoriesV3ReelPageStandaloneQuery.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h!==void 0?h:h=b("PolarisStoriesV3ReelPageStandaloneQuery.graphql");g["default"]=a}),98);
__d("PolarisStoriesV3ReelStandalone_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelStandalone_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3NavigationContextProvider_medias"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPlayerContainer_media"}],storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelLoggingContextProvider_reels"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPlayerContainer_reel"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3CurrentReelMedia_reels"},{args:null,kind:"FragmentSpread",name:"usePolarisStoriesV3PrefetchReelRouteDefinitionsEffect_reel"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3ReelStandalone_viewer.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisStoriesV3ReelStandalone_viewer",selections:[{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelLoggingContextProvider_viewer"},{args:null,kind:"FragmentSpread",name:"PolarisStoriesV3ReelPlayerContainer_viewer"}],type:"XDTViewer",abstractKey:null};e.exports=a}),null);
__d("PolarisStoriesV3StandaloneLayout.react",["PolarisSizing","PolarisStoriesV3ViewerSizeContext","react","react-compiler-runtime","usePolarisIsSmallScreen","usePolarisStoriesV3GetSmallScreenViewerSize","useWindowSize"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));h.useMemo;function j(){var a=d("react-compiler-runtime").c(4),b=c("usePolarisIsSmallScreen")(),e=c("useWindowSize")();e=e.innerHeight;var f=c("usePolarisStoriesV3GetSmallScreenViewerSize")();if(b){a[0]!==f?(b=f(),a[0]=f,a[1]=b):b=a[1];return b}a[2]!==e?(f=d("PolarisSizing").getViewerDimensionsFromHeightV2(e),a[2]=e,a[3]=f):f=a[3];return f}function a(a){var b=d("react-compiler-runtime").c(12);a=a.children;var e=j(),f=e.height;e=e.width;var g;b[0]!==f||b[1]!==e?(g={height:f,width:e},b[0]=f,b[1]=e,b[2]=g):g=b[2];g=g;g=g;var h;b[3]!==f||b[4]!==e?(h={height:f,width:e},b[3]=f,b[4]=e,b[5]=h):h=b[5];b[6]!==a||b[7]!==h?(f=i.jsx("div",{style:h,children:a}),b[6]=a,b[7]=h,b[8]=f):f=b[8];b[9]!==f||b[10]!==g?(e=i.jsx(c("PolarisStoriesV3ViewerSizeContext").Provider,{value:g,children:f}),b[9]=f,b[10]=g,b[11]=e):e=b[11];return e}g["default"]=a}),98);
__d("usePolarisStoriesV3DeleteMediaContext",["PolarisStoriesV3DeleteMediaContext","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useContext;function a(){return i(c("PolarisStoriesV3DeleteMediaContext"))}g["default"]=a}),98);
__d("usePolarisStoriesV3PrefetchReelRouteDefinitionsEffect_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3PrefetchReelRouteDefinitionsEffect_reel",selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"id",storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null}],storageKey:null},action:"THROW"},{kind:"RequiredField",field:{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{kind:"RequiredField",field:{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},action:"THROW"}],storageKey:null},action:"THROW"}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3PrefetchReelRouteDefinitionsEffect",["CometRelay","react","react-compiler-runtime","usePolarisStoriesV3PrefetchReelRouteDefinitionsEffect_reel.graphql","usePolarisStoriesV3RoutePrefetcher"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(i||d("react")).useEffect;function a(a){var e=d("react-compiler-runtime").c(8),f=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3PrefetchReelRouteDefinitionsEffect_reel.graphql"),a),g=c("usePolarisStoriesV3RoutePrefetcher")();e[0]!==f.id||e[1]!==f.items||e[2]!==f.user||e[3]!==g?(a=function(){if(!g.hasPrefetched(f.id)){var a=f.user.username,b=f.items.map(k);g.prefetch(f.id,a,b)}},e[0]=f.id,e[1]=f.items,e[2]=f.user,e[3]=g,e[4]=a):a=e[4];var i;e[5]!==f||e[6]!==g?(i=[f,g],e[5]=f,e[6]=g,e[7]=i):i=e[7];j(a,i)}function k(a){return a.pk}g["default"]=a}),98);
__d("PolarisStoriesV3ReelStandalone.react",["CometRelay","FBLogger","PolarisStoriesV3NavigationContextProvider.react","PolarisStoriesV3PageNavigationContextProvider.react","PolarisStoriesV3ReelLoggingContextProvider.react","PolarisStoriesV3ReelPlayerContainer.react","PolarisStoriesV3ReelStandalone_reel.graphql","PolarisStoriesV3ReelStandalone_viewer.graphql","PolarisStoriesV3StandaloneLayout.react","XPolarisProfileControllerRouteBuilder","react","react-compiler-runtime","usePolarisPopPushView","usePolarisStoriesV3CurrentReelMedia","usePolarisStoriesV3DeleteMediaContext","usePolarisStoriesV3MediaChangeHandler","usePolarisStoriesV3PrefetchReelRouteDefinitionsEffect"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||(j=d("react")),l=j.useEffect;function a(a){var e=d("react-compiler-runtime").c(39),f=a.mediaId,g=a.reel;a=a.viewer;var j=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisStoriesV3ReelStandalone_reel.graphql"),g);g=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisStoriesV3ReelStandalone_viewer.graphql"),a);e[0]!==j?(a=[j],e[0]=j,e[1]=a):a=e[1];var m;e[2]!==f||e[3]!==j.id||e[4]!==a?(m={mediaId:f,reelId:j.id,reels:a},e[2]=f,e[3]=j.id,e[4]=a,e[5]=m):m=e[5];f=c("usePolarisStoriesV3CurrentReelMedia")(m);var n=f.mediaId;a=f.reel;e[6]!==n||e[7]!==j.items?(m=j.items.find(function(a){return a.pk===n}),e[6]=n,e[7]=j.items,e[8]=m):m=e[8];var o=m;f=c("usePolarisStoriesV3DeleteMediaContext")();m=f.isDeleted;e[9]!==m||e[10]!==n?(f=m(n),e[9]=m,e[10]=n,e[11]=f):f=e[11];var p=f,q=c("usePolarisPopPushView")();c("usePolarisStoriesV3PrefetchReelRouteDefinitionsEffect")(j);e[12]!==p||e[13]!==o||e[14]!==q||e[15]!==j.user?(m=function(){if(o==null&&!p){c("FBLogger")("ig_web","stories").warn("Media ID for story not found");var a=c("XPolarisProfileControllerRouteBuilder").buildUri({username:j.user.username}).toString();q(a,{replace:!0})}},e[12]=p,e[13]=o,e[14]=q,e[15]=j.user,e[16]=m):m=e[16];e[17]!==p||e[18]!==o||e[19]!==q||e[20]!==j?(f=[j,o,p,q],e[17]=p,e[18]=o,e[19]=q,e[20]=j,e[21]=f):f=e[21];l(m,f);m=c("usePolarisStoriesV3MediaChangeHandler")();if(o==null)return null;e[22]!==j?(f=[j],e[22]=j,e[23]=f):f=e[23];var r;e[24]!==o||e[25]!==j||e[26]!==g?(r=k.jsx(c("PolarisStoriesV3StandaloneLayout.react"),{children:k.jsx(c("PolarisStoriesV3ReelPlayerContainer.react"),{media:o,reel:j,viewer:g})}),e[24]=o,e[25]=j,e[26]=g,e[27]=r):r=e[27];var s;e[28]!==m||e[29]!==n||e[30]!==j.items||e[31]!==r?(s=k.jsx(c("PolarisStoriesV3PageNavigationContextProvider.react"),{children:k.jsx(c("PolarisStoriesV3NavigationContextProvider.react"),{mediaId:n,medias:j.items,onChange:m,children:r})}),e[28]=m,e[29]=n,e[30]=j.items,e[31]=r,e[32]=s):s=e[32];e[33]!==a||e[34]!==n||e[35]!==f||e[36]!==s||e[37]!==g?(m=k.jsx(c("PolarisStoriesV3ReelLoggingContextProvider.react"),{currentReel:a,mediaId:n,reels:f,viewer:g,children:s}),e[33]=a,e[34]=n,e[35]=f,e[36]=s,e[37]=g,e[38]=m):m=e[38];return m}g["default"]=a}),98);
__d("usePolarisStoriesV3RedirectOnEmptyReelEffect_reel.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisStoriesV3RedirectOnEmptyReelEffect_reel",selections:[{alias:null,args:null,concreteType:"XDTMediaDict",kind:"LinkedField",name:"items",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"__typename",storageKey:null}],storageKey:null}],type:"XDTReelDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisStoriesV3RedirectOnEmptyReelEffect",["CometRelay","react","react-compiler-runtime","usePolarisPopPushView","usePolarisStoriesV3RedirectOnEmptyReelEffect_reel.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=(i||d("react")).useEffect;function a(a,e){var f=d("react-compiler-runtime").c(5);a=d("CometRelay").useFragment(h!==void 0?h:h=b("usePolarisStoriesV3RedirectOnEmptyReelEffect_reel.graphql"),a);var g=((a=a==null?void 0:a.items)!=null?a:[]).length>0,i=c("usePolarisPopPushView")(),k;f[0]!==e||f[1]!==g||f[2]!==i?(a=function(){g||i(e,{replace:!0})},k=[e,g,i],f[0]=e,f[1]=g,f[2]=i,f[3]=a,f[4]=k):(a=f[3],k=f[4]);j(a,k);return!g}g["default"]=a}),98);
__d("PolarisStoriesV3ReelPageStandalone.react",["CometRelay","PolarisStoriesV3DeleteMediaContextProvider.react","PolarisStoriesV3ReelPageStandaloneQuery","PolarisStoriesV3ReelStandalone.react","XPolarisProfileControllerRouteBuilder","react","react-compiler-runtime","usePolarisStoriesV3RedirectOnEmptyReelEffect"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(10),e=a.mediaId,f=a.query;a=a.username;f=d("CometRelay").usePreloadedQuery(c("PolarisStoriesV3ReelPageStandaloneQuery"),f);var g=f.xdt_api__v1__feed__reels_media.reels_media[0],h;b[0]!==a?(h=c("XPolarisProfileControllerRouteBuilder").buildUri({username:a}).toString(),b[0]=a,b[1]=h):h=b[1];a=h;c("usePolarisStoriesV3RedirectOnEmptyReelEffect")(g,a);if(g==null)return null;h=f.xdt_viewer;b[2]!==e||b[3]!==g||b[4]!==h?(a=i.jsx(c("PolarisStoriesV3ReelStandalone.react"),{mediaId:e,reel:g,viewer:h}),b[2]=e,b[3]=g,b[4]=h,b[5]=a):a=b[5];b[6]!==g||b[7]!==a||b[8]!==h?(f=i.jsx(c("PolarisStoriesV3DeleteMediaContextProvider.react"),{reel:g,viewer:h,children:a}),b[6]=g,b[7]=a,b[8]=h,b[9]=f):f=b[9];return f}g["default"]=a}),98);
__d("PolarisStoriesV3SeenStateContextProvider.react",["PolarisReactRedux.react","PolarisStoriesV3SeenStateContext","react","useStable"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));b=h;var j=b.useCallback,k=b.useMemo;function a(a){a=a.children;var b=c("useStable")(function(){return new Map()}),e=c("useStable")(function(){return new Map()}),f=c("useStable")(function(){return new Set()}),g=d("PolarisReactRedux.react").useStore(),h=j(function(a){return e.get(a)},[e]),l=j(function(a){if(f.has(a))return!0;var b=g.getState();b=b.stories.reels.get(a);return(b==null?void 0:b.seen)!=null&&(b==null?void 0:b.latestReelMedia)!=null&&b.seen>=b.latestReelMedia},[f,g]),m=j(function(a,c){return(a=(a=b.get(a))==null?void 0:a.has(c))!=null?a:!1},[b]),n=j(function(a){var c=a.isReelFullySeen,d=a.mediaId,h=a.mediaTakenAt;a=a.reelId;var i=g.getState();i.stories.reels.has(a)&&g.dispatch({reelId:a,reelMediaLastSeen:h,type:"STORY_REELS_ITEM_SEEN"});i=b.get(a);i==null&&(i=new Set());b.set(a,i);i.add(d);e.set(a,d);c&&f.add(a)},[g,b,e,f]),o=k(function(){return{getLastSeenMediaId:h,isReelMediaSeen:m,isReelSeen:l,updateSeen:n}},[h,m,l,n]);return i.jsx(c("PolarisStoriesV3SeenStateContext").Provider,{value:o,children:a})}a.displayName=a.name+" [from "+f.id+"]";g["default"]=a}),98);
__d("usePolarisStoriesV3PersistedStable",["react","react-compiler-runtime","useCometRouterState","useStable"],(function(a,b,c,d,e,f,g){"use strict";var h,i=(h||d("react")).useEffect,j=new Map();function k(){var a=d("react-compiler-runtime").c(4),b=c("useCometRouterState")();b=(b=b==null?void 0:b.routeKey)!=null?b:"";var e;a[0]!==b?(e=j.get(b),a[0]=b,a[1]=e):e=a[1];e=e;e==null&&(a[2]!==b?(e=new Map(),j.set(b,e),a[2]=b,a[3]=e):e=a[3]);return e}function a(a,b){var d=k(),e=c("useStable")(function(){if(d.has(a))return d.get(a);else{var c=b();d.set(a,c);return c}});i(function(){d.set(a,e)},[a,d,e]);return e}g["default"]=a}),98);
__d("usePolarisStoriesV3StableLoggingSession",["PolarisStoriesV3LoggingSession","react-compiler-runtime","usePolarisStoriesV3PersistedStable"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=d("react-compiler-runtime").c(2),e;b[0]!==a?(e=function(){var b=new(c("PolarisStoriesV3LoggingSession"))();b.startReel(a);return b},b[0]=a,b[1]=e):e=b[1];return c("usePolarisStoriesV3PersistedStable")("loggingSession",e)}g["default"]=a}),98);
__d("usePolarisStoriesV3StablePreviewImpressions",["usePolarisStoriesV3PersistedStable"],(function(a,b,c,d,e,f,g){"use strict";function a(){return c("usePolarisStoriesV3PersistedStable")("previewImpressions",h)}function h(){return new Set()}g["default"]=a}),98);
__d("usePolarisStoriesV3StableTrayLoggingData",["react-compiler-runtime","usePolarisStoriesV3PersistedStable"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=d("react-compiler-runtime").c(2),e;b[0]!==a?(e=function(){return a},b[0]=a,b[1]=e):e=b[1];return c("usePolarisStoriesV3PersistedStable")("trayLoggingData",e)}g["default"]=a}),98);
__d("PolarisStoriesV3ViewerLoggingContextProvider.react",["PolarisStoriesV3ViewerLoggingContext","react","react-compiler-runtime","usePolarisStoriesV3StableLoggingSession","usePolarisStoriesV3StablePreviewImpressions","usePolarisStoriesV3StableTrayLoggingData"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));b=h;b.useCallback;b.useMemo;var j=b.useRef;function a(b){var e,g,h=d("react-compiler-runtime").c(19),k=b.children,l=b.initialReelId,a=b.module,m=b.reelList,n=b.reelType,o=b.trayLoggingData;b=b.viewerSessionId;o=c("usePolarisStoriesV3StableTrayLoggingData")(o);e=(e=o==null?void 0:o.traySessionId)!=null?e:"";var p=c("usePolarisStoriesV3StablePreviewImpressions")();l=c("usePolarisStoriesV3StableLoggingSession")(l);var q=j(!1),r;h[0]===Symbol["for"]("react.memo_cache_sentinel")?(r=function(){return q.current},h[0]=r):r=h[0];r=r;var s;h[1]===Symbol["for"]("react.memo_cache_sentinel")?(s=function(){q.current=!0},h[1]=s):s=h[1];s=s;var t=j("unknown"),u;h[2]===Symbol["for"]("react.memo_cache_sentinel")?(u=function(){return t.current},h[2]=u):u=h[2];u=u;var v;h[3]===Symbol["for"]("react.memo_cache_sentinel")?(v=function(a){t.current=a},h[3]=v):v=h[3];v=v;var w;h[4]!==m?(w=function(a){return m.getIndexById(a)},h[4]=m,h[5]=w):w=h[5];w=w;g=(g=o==null?void 0:o.newReelCount)!=null?g:-1;o=(o=o==null?void 0:o.viewedReelCount)!=null?o:-1;n=n;h[6]!==w||h[7]!==g||h[8]!==o||h[9]!==l||h[10]!==a||h[11]!==p||h[12]!==n||h[13]!==e||h[14]!==b?(u={getAction:u,getTrayPosition:w,hasLoggedReelPlaybackEntry:r,initialNewReelCount:g,initialViewedReelCount:o,loggingSession:l,module:a,previewImpressions:p,reelType:n,setAction:v,setLoggedReelPlaybackEntry:s,traySessionId:e,viewerSessionId:b},h[6]=w,h[7]=g,h[8]=o,h[9]=l,h[10]=a,h[11]=p,h[12]=n,h[13]=e,h[14]=b,h[15]=u):u=h[15];r=u;v=r;h[16]!==k||h[17]!==v?(s=i.jsx(c("PolarisStoriesV3ViewerLoggingContext").Provider,{value:v,children:k}),h[16]=k,h[17]=v,h[18]=s):s=h[18];return s}g["default"]=a}),98);
__d("usePolarisStoriesV3StableContainerModule",["react-compiler-runtime","usePolarisStoriesV3PersistedStable"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=d("react-compiler-runtime").c(2),e;b[0]!==a?(e=function(){return a},b[0]=a,b[1]=e):e=b[1];return c("usePolarisStoriesV3PersistedStable")("containerModule",e)}g["default"]=a}),98);
__d("usePolarisStoriesV3StableReelList",["PolarisStoriesV3ReelList","react","react-compiler-runtime","usePolarisStoriesV3OnStoryViewerClose","usePolarisStoriesV3PersistedStable"],(function(a,b,c,d,e,f,g){"use strict";var h;(h||d("react")).useCallback;function a(a){var b=d("react-compiler-runtime").c(9),e=a.adsPool,f=a.currentReelId,g=a.initialReelId,h=a.initialReelIds,i=a.traySessionId,j=a.viewerSessionId;b[0]!==e||b[1]!==g||b[2]!==h||b[3]!==i||b[4]!==j?(a=function(){return new(c("PolarisStoriesV3ReelList"))({adsPool:e,initialReelId:g,initialReelIds:h,traySessionId:i,viewerSessionId:j})},b[0]=e,b[1]=g,b[2]=h,b[3]=i,b[4]=j,b[5]=a):a=b[5];var k=c("usePolarisStoriesV3PersistedStable")("reelList",a);b[6]!==f||b[7]!==k?(a=function(){k.reset(f)},b[6]=f,b[7]=k,b[8]=a):a=b[8];b=a;c("usePolarisStoriesV3OnStoryViewerClose")(b);return k}g["default"]=a}),98);
__d("usePolarisStoriesV3StableViewerSessionId",["react-compiler-runtime","usePolarisStoriesV3PersistedStable","uuidv4"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=d("react-compiler-runtime").c(2),e;b[0]!==a?(e=function(){return a!=null?a:c("uuidv4")()},b[0]=a,b[1]=e):e=b[1];return c("usePolarisStoriesV3PersistedStable")("viewerSessionId",e)}g["default"]=a}),98);
__d("PolarisStoriesV3ReelPage.react",["PolarisStoriesV3AdsPool","PolarisStoriesV3ContainerModule","PolarisStoriesV3LoggingReelType","PolarisStoriesV3ReelPageGallery.react","PolarisStoriesV3ReelPageStandalone.react","PolarisStoriesV3SeenStateContextProvider.react","PolarisStoriesV3ViewerLoggingContextProvider.react","react","react-compiler-runtime","usePolarisStoriesV3RefinedReelPagePassthroughProps","usePolarisStoriesV3StableContainerModule","usePolarisStoriesV3StableReelList","usePolarisStoriesV3StableViewerSessionId"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b,e,f,g=d("react-compiler-runtime").c(30),h=a.mediaId,j=a.query,k=a.userId;a=a.username;var l=c("usePolarisStoriesV3RefinedReelPagePassthroughProps")();b=c("usePolarisStoriesV3StableContainerModule")((b=l==null?void 0:l.loggingModule)!=null?b:c("PolarisStoriesV3ContainerModule").ReelUrl);var m;g[0]===Symbol["for"]("react.memo_cache_sentinel")?(m=c("PolarisStoriesV3AdsPool").getInstance(),g[0]=m):m=g[0];m=m;e=(e=l==null?void 0:l.initialReelId)!=null?e:k;if(g[1]!==(l==null?void 0:l.reelIds)||g[2]!==k){var n;n=(n=l==null?void 0:l.reelIds)!=null?n:[k];g[1]=l==null?void 0:l.reelIds;g[2]=k;g[3]=n}else n=g[3];n=n;var o=l==null?void 0:l.adId,p=o!=null?o:k,q=c("usePolarisStoriesV3StableViewerSessionId")(l==null?void 0:l.viewerSessionId),r=j.gallery!=null?m:null;f=(f=l==null?void 0:(f=l.trayLoggingData)==null?void 0:f.traySessionId)!=null?f:"";var s;g[4]!==p||g[5]!==e||g[6]!==n||g[7]!==r||g[8]!==f||g[9]!==q?(s={adsPool:r,currentReelId:p,initialReelId:e,initialReelIds:n,traySessionId:f,viewerSessionId:q},g[4]=p,g[5]=e,g[6]=n,g[7]=r,g[8]=f,g[9]=q,g[10]=s):s=g[10];p=c("usePolarisStoriesV3StableReelList")(s);if(j.standalone!=null){g[11]!==h||g[12]!==j.standalone||g[13]!==a?(n=i.jsx(c("PolarisStoriesV3ReelPageStandalone.react"),{mediaId:h,query:j.standalone,username:a}),g[11]=h,g[12]=j.standalone,g[13]=a,g[14]=n):n=g[14];r=n}else{g[15]!==o||g[16]!==h||g[17]!==j.gallery||g[18]!==p||g[19]!==k?(f=i.jsx(c("PolarisStoriesV3ReelPageGallery.react"),{adId:o,adsPool:m,mediaId:h,query:j.gallery,reelList:p,userId:k}),g[15]=o,g[16]=h,g[17]=j.gallery,g[18]=p,g[19]=k,g[20]=f):f=g[20];r=f}s=l==null?void 0:l.trayLoggingData;g[21]!==r?(a=i.jsx(c("PolarisStoriesV3SeenStateContextProvider.react"),{children:r}),g[21]=r,g[22]=a):a=g[22];g[23]!==e||g[24]!==b||g[25]!==p||g[26]!==s||g[27]!==a||g[28]!==q?(n=i.jsx(c("PolarisStoriesV3ViewerLoggingContextProvider.react"),{initialReelId:e,module:b,reelList:p,reelType:c("PolarisStoriesV3LoggingReelType").Story,trayLoggingData:s,viewerSessionId:q,children:a}),g[23]=e,g[24]=b,g[25]=p,g[26]=s,g[27]=a,g[28]=q,g[29]=n):n=g[29];return n}g["default"]=a}),98);