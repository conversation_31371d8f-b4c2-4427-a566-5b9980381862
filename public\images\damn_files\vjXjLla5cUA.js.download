;/*FB_PKG_DELIM*/

__d("LSHasPollAttachmentNewerThan",[],(function(a,b,c,d,e,f){function a(){var a=arguments,b=a[a.length-1],c=[],d=[];return b.sequence([function(e){return b.sequence([function(d){return b.count(b.filter(b.db.table(16).fetch(),function(c){return c.attachmentFbid===a[0]&&b.i64.gt(c.timestampMs,a[1])})).then(function(a){return c[0]=a})},function(a){return b.i64.gt(c[0],b.i64.cast([0,0]))?c[1]=!0:c[1]=!1,d[0]=c[1]}])},function(a){return b.resolve(d)}])}a.__sproc_name__="LSMailboxHasPollAttachmentNewerThanStoredProcedure";a.__tables__=["attachments"];e.exports=a}),null);
__d("PolarisAPIDismissAysfSuggestion",["PolarisInstapi"],(function(a,b,c,d,e,f,g){"use strict";function a(a){a={target_id:a};return d("PolarisInstapi").apiPost("/api/v1/web/discover/aysf_dismiss/",{body:a})}g.dismissAysfSuggestion=a}),98);
__d("PolarisFeedActionDismissFeedAysfSuggestion",["PolarisAPIDismissAysfSuggestion"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return function(b){b({dismissedId:a,type:"FEED_AYSF_DISMISSED_SUGGESTION"});return d("PolarisAPIDismissAysfSuggestion").dismissAysfSuggestion(a)}}g.dismissFeedAysfSuggestion=a}),98);
__d("PolarisNetEgoLogger",["PolarisDeviceOrMachineId","PolarisLocales","PolarisLogger","PolarisPigeonLogger","PolarisUA","keyMirror"],(function(a,b,c,d,e,f,g){"use strict";b=c("keyMirror")({feed:null});function a(a){var b=a.containerModule,e=a.eventName,f=a.isFromNeedyUser,g=a.position,h=a.trackingToken;a=a.type;f={device_model:d("PolarisUA").getBrowserString(),device_os:"Web",deviceid:d("PolarisDeviceOrMachineId").getDeviceOrMachineId(),gap_to_last_netego:-1,is_from_needy_user:Boolean(f),position:g,primary_locale:c("PolarisLocales").locale,tracking_token:h,type:a};d("PolarisLogger").logPigeonEvent(d("PolarisPigeonLogger").createEvent(e,d("PolarisLogger").getExtra(f),{module:b}))}g.NETEGO_CONTAINER_MODULES=b;g.logNetEgoEvent=a}),98);
__d("PolarisSuggestedUserDescriptionStackedAvatars_items.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisSuggestedUserDescriptionStackedAvatars_items",selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"social_context_facepile_users",plural:!0,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null}],storageKey:null}],type:"XDTSuggestedUserTypedDict",abstractKey:null};e.exports=a}),null);
__d("PolarisSuggestedUserDescriptionStackedAvatars.next.react",["CometRelay","PolarisSuggestedUserDescriptionStackedAvatars_items.graphql","PolarisUserAvatar.react","react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k=j||d("react"),l=18,m={bottomAvatar:{alignSelf:"xqcrz7y",position:"x10l6tqk",$$css:!0},bottomAvatarSingle:{alignSelf:"xamitd3",position:"x1n2onr6",$$css:!0},topAvatar:{insetInlineStart:"xrr41r3",position:"x1n2onr6",top:"x1atx4j1",$$css:!0}};function a(a){var e=d("react-compiler-runtime").c(6);a=a.fragmentKey;a=d("CometRelay").useFragment(h!==void 0?h:h=b("PolarisSuggestedUserDescriptionStackedAvatars_items.graphql"),a);a=a.social_context_facepile_users;var f=null;if(a!=null&&a.length>0){var g=a.length,j;e[0]!==g||e[1]!==a?(j=a.map(function(a,b){var d=a.pk,e=a.profile_pic_url;a=a.username;return e!=null&&k.createElement("div",babelHelpers["extends"]({},(i||(i=c("stylex"))).props(g===1?m.bottomAvatarSingle:b===0?m.bottomAvatar:m.topAvatar),{key:d}),k.jsx(c("PolarisUserAvatar.react"),{canTabFocus:!1,isLink:!1,profilePictureUrl:e,size:l,username:a!=null?a:""}))}),e[0]=g,e[1]=a,e[2]=j):j=e[2];f=j}e[3]===Symbol["for"]("react.memo_cache_sentinel")?(a={className:"x78zum5 xxk0z11 xl56j7k"},e[3]=a):a=e[3];e[4]!==f?(j=k.jsx("div",babelHelpers["extends"]({},a,{children:f})),e[4]=f,e[5]=j):j=e[5];return j}g["default"]=a}),98);
__d("PolarisSuggestedUserFeedUnit_suggested_users.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"PolarisSuggestedUserFeedUnit_suggested_users",selections:[{alias:null,args:null,kind:"ScalarField",name:"type",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"tracking_token",storageKey:null},{alias:null,args:null,concreteType:"XDTSuggestedUserTypedDict",kind:"LinkedField",name:"suggestions",plural:!0,selections:[{alias:null,args:null,concreteType:"XDTUserDict",kind:"LinkedField",name:"user",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"pk",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"profile_pic_url",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"full_name",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"is_verified",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"username",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisFollowChainingList_suggested_users"}],storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"followed_by",storageKey:null},{alias:null,args:null,kind:"ScalarField",name:"social_context",storageKey:null},{args:null,kind:"FragmentSpread",name:"PolarisSuggestedUserDescriptionStackedAvatars_items"},{args:null,kind:"FragmentSpread",name:"usePolarisFollowSuggestedUser_updatable"}],storageKey:null}],type:"XDTSuggestedUsers",abstractKey:null};e.exports=a}),null);
__d("usePolarisFollowSuggestedUserFollowMutation_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9660047674090784"}),null);
__d("usePolarisFollowSuggestedUserFollowMutation.graphql",["usePolarisFollowSuggestedUserFollowMutation_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"target_user_id"}],c=[{alias:null,args:[{kind:"Literal",name:"_request_data",value:{}},{kind:"Variable",name:"target_user_id",variableName:"target_user_id"}],concreteType:"XDTFriendshipStatusResponse",kind:"LinkedField",name:"xdt_api__v1__friendships__create__target_user_id",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTRelationshipInfoDict",kind:"LinkedField",name:"friendship_status",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"following",storageKey:null}],storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"usePolarisFollowSuggestedUserFollowMutation",selections:c,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"usePolarisFollowSuggestedUserFollowMutation",selections:c},params:{id:b("usePolarisFollowSuggestedUserFollowMutation_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__friendships__create__target_user_id"]},name:"usePolarisFollowSuggestedUserFollowMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("usePolarisFollowSuggestedUserUnfollowMutation_instagramRelayOperation",[],(function(a,b,c,d,e,f){e.exports="29455769757404931"}),null);
__d("usePolarisFollowSuggestedUserUnfollowMutation.graphql",["usePolarisFollowSuggestedUserUnfollowMutation_instagramRelayOperation"],(function(a,b,c,d,e,f){"use strict";a=function(){var a=[{defaultValue:null,kind:"LocalArgument",name:"target_user_id"}],c=[{alias:null,args:[{kind:"Literal",name:"_request_data",value:{}},{kind:"Variable",name:"target_user_id",variableName:"target_user_id"}],concreteType:"XDTFriendshipStatusResponse",kind:"LinkedField",name:"xdt_api__v1__friendships__destroy__target_user_id",plural:!1,selections:[{alias:null,args:null,concreteType:"XDTRelationshipInfoDict",kind:"LinkedField",name:"friendship_status",plural:!1,selections:[{alias:null,args:null,kind:"ScalarField",name:"following",storageKey:null}],storageKey:null}],storageKey:null}];return{fragment:{argumentDefinitions:a,kind:"Fragment",metadata:null,name:"usePolarisFollowSuggestedUserUnfollowMutation",selections:c,type:"Mutation",abstractKey:null},kind:"Request",operation:{argumentDefinitions:a,kind:"Operation",name:"usePolarisFollowSuggestedUserUnfollowMutation",selections:c},params:{id:b("usePolarisFollowSuggestedUserUnfollowMutation_instagramRelayOperation"),metadata:{is_distillery:!0,root_field_name:["xdt_api__v1__friendships__destroy__target_user_id"]},name:"usePolarisFollowSuggestedUserUnfollowMutation",operationKind:"mutation",text:null}}}();e.exports=a}),null);
__d("usePolarisFollowSuggestedUser_updatable.graphql",[],(function(a,b,c,d,e,f){"use strict";a={argumentDefinitions:[],kind:"Fragment",metadata:null,name:"usePolarisFollowSuggestedUser_updatable",selections:[{alias:null,args:null,kind:"ScalarField",name:"followed_by",storageKey:null}],type:"XDTSuggestedUserTypedDict",abstractKey:null};e.exports=a}),null);
__d("usePolarisFollowSuggestedUser",["CometRelay","react","react-compiler-runtime","usePolarisFollowSuggestedUserFollowMutation.graphql","usePolarisFollowSuggestedUserUnfollowMutation.graphql","usePolarisFollowSuggestedUser_updatable.graphql"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j,k;(k||d("react")).useCallback;var l=h!==void 0?h:h=b("usePolarisFollowSuggestedUserFollowMutation.graphql"),m=i!==void 0?i:i=b("usePolarisFollowSuggestedUserUnfollowMutation.graphql");function a(){var a=d("react-compiler-runtime").c(3),c=d("CometRelay").useMutation(l),e=c[0];c=d("CometRelay").useMutation(m);var f=c[0];a[0]!==e||a[1]!==f?(c=function(a,c,d){var g=function(c){c=c.readUpdatableFragment(j!==void 0?j:j=b("usePolarisFollowSuggestedUser_updatable.graphql"),a);c=c.updatableData;c!=null&&(c.followed_by=d)};g={optimisticUpdater:g,updater:g,variables:{target_user_id:c}};d?e(g):f(g)},a[0]=e,a[1]=f,a[2]=c):c=a[2];return c}g["default"]=a}),98);
__d("PolarisSuggestedUserFeedUnit.react",["fbt","CometErrorBoundary.react","CometRelay","PolarisConnectionsLogger","PolarisFeedActionDismissFeedAysfSuggestion","PolarisFollowChainingList.next.react","PolarisNetEgoLogger","PolarisPostVariants","PolarisReactRedux.react","PolarisRoutes","PolarisSuggestedUserDescriptionStackedAvatars.next.react","PolarisSuggestedUserFeedUnit_suggested_users.graphql","react","react-compiler-runtime","stylex","usePolarisFollowSuggestedUser"],(function(a,b,c,d,e,f,g,h){"use strict";var i,j,k,l=k||(k=d("react"));e=k;e.useCallback;var m=e.useEffect;e.useMemo;var n=e.useState,o=h._(/*BTDS*/"Suggestions for you");function p(a){var b=d("react-compiler-runtime").c(5),e=a.data;a=a.position;var f;b[0]!==e.suggestions||b[1]!==a?(f=e.suggestions!=null&&l.jsx(c("PolarisSuggestedUserDescriptionStackedAvatars.next.react"),{fragmentKey:e.suggestions[a]}),b[0]=e.suggestions,b[1]=a,b[2]=f):f=b[2];b[3]!==f?(e=l.jsx(l.Fragment,{children:f}),b[3]=f,b[4]=e):e=b[4];return e}function a(a){var e=d("react-compiler-runtime").c(36),f=a.fragmentKey,g=a.impressionModule,h=a.position,k=a.variant;a=a.xstyle;g=g===void 0?d("PolarisConnectionsLogger").VIEW_MODULES.hscroll_feed:g;var s=d("CometRelay").useFragment(i!==void 0?i:i=b("PolarisSuggestedUserFeedUnit_suggested_users.graphql"),f),t=s.tracking_token,u=String(s.type);if(e[0]!==s.suggestions){f=(f=s.suggestions)==null?void 0:f.map(r).filter(Boolean);e[0]=s.suggestions;e[1]=f}else f=e[1];f=f;f=n(f);var v=f[0],w=f[1],x=d("PolarisReactRedux.react").useDispatch();e[2]!==x||e[3]!==v?(f=function(a){x(d("PolarisFeedActionDismissFeedAysfSuggestion").dismissFeedAysfSuggestion(a)),w(v==null?void 0:v.filter(function(b){return b.id!==a}))},e[2]=x,e[3]=v,e[4]=f):f=e[4];f=f;var y=c("usePolarisFollowSuggestedUser")(),z;e[5]!==y||e[6]!==s.suggestions||e[7]!==v?(z=function(a,b){var c;c=(c=s.suggestions)==null?void 0:c.find(function(b){return((b=b.user)==null?void 0:b.pk)===a});if(c==null)return;y(c,a,b);w(v==null?void 0:v.map(function(c){return a===c.id?babelHelpers["extends"]({},c,{isFollowedByViewer:b}):c}))},e[5]=y,e[6]=s.suggestions,e[7]=v,e[8]=z):z=e[8];var A=z;e[9]!==A?(z=function(a){A(a,!1)},e[9]=A,e[10]=z):z=e[10];z=z;var B;e[11]!==A?(B=function(a){A(a,!0)},e[11]=A,e[12]=B):B=e[12];B=B;var C,D;e[13]!==h||e[14]!==t||e[15]!==u?(C=function(){h!=null&&d("PolarisNetEgoLogger").logNetEgoEvent({containerModule:d("PolarisNetEgoLogger").NETEGO_CONTAINER_MODULES.feed,eventName:"instagram_netego_impression",isFromNeedyUser:!0,position:h,trackingToken:t,type:u})},D=[h,t,u],e[13]=h,e[14]=t,e[15]=u,e[16]=C,e[17]=D):(C=e[16],D=e[17]);m(C,D);if(e[18]!==s.suggestions){C=(D=(C=s.suggestions)==null?void 0:C.map(q).filter(Boolean))!=null?D:[];e[18]=s.suggestions;e[19]=C}else C=e[19];D=C;C=D;e[20]!==s?(D=function(a){return l.jsx(p,{data:s,position:a})},e[20]=s,e[21]=D):D=e[21];D=D;var E;e[22]!==a?(E=(j||(j=c("stylex"))).props(a),e[22]=a,e[23]=E):E=e[23];a=k===d("PolarisPostVariants").VARIANTS.narrow;e[24]!==g||e[25]!==B||e[26]!==f||e[27]!==z||e[28]!==D||e[29]!==a||e[30]!==v||e[31]!==C?(k=l.jsx(c("PolarisFollowChainingList.next.react"),{analyticsContext:d("PolarisConnectionsLogger").CONNECTIONS_CONTAINER_MODULES.feed_timeline,chainingSuggestions:v,clickPoint:"feed_h_scroll_suggested_user_list",impressionModule:g,isSmallScreen:a,onFollowUser:B,onSuggestionDismissed:f,onUnfollowUser:z,renderItemFooter:D,seeAllHref:d("PolarisRoutes").DISCOVER_PEOPLE_PATH,showDescription:!0,title:o,users:C}),e[24]=g,e[25]=B,e[26]=f,e[27]=z,e[28]=D,e[29]=a,e[30]=v,e[31]=C,e[32]=k):k=e[32];e[33]!==E||e[34]!==k?(g=l.jsx(c("CometErrorBoundary.react"),{children:l.jsx("div",babelHelpers["extends"]({},E,{children:k}))}),e[33]=E,e[34]=k,e[35]=g):g=e[35];return g}function q(a){return a.user}function r(a){var b;if(((b=a.user)==null?void 0:b.pk)==null)return null;b=a.user.full_name;var c=a.user.is_verified,d=a.user.pk,e=a.user.profile_pic_url,f=a.user.username;return{fullName:b!=null?b:"",id:d,isFollowedByViewer:a.followed_by===!0,isVerified:c!=null?c:!1,profilePictureUrl:e!=null?e:void 0,suggestionDescription:(b=a.social_context)!=null?b:"",username:f!=null?f:""}}g["default"]=a}),226);