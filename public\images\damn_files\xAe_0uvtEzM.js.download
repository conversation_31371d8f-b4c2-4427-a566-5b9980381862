;/*FB_PKG_DELIM*/

__d("PolarisNotesTypes",["$InternalEnum"],(function(a,b,c,d,e,f){"use strict";a=b("$InternalEnum")({MUTUAL_FOLLOWS:0,BESTIES:1,INTERNAL:2});c=b("$InternalEnum")({EMPTY:7});d=[a.MUTUAL_FOLLOWS,a.BESTIES,a.INTERNAL];e=[a.MUTUAL_FOLLOWS,a.BESTIES];b=b("$InternalEnum")({NEW_NOTE_CTA:"new_note_cta",REPLACE_EXISTING_SELF_NOTE_CTA:"replace_existing_self_note_cta"});f.NoteAudienceOptionValues=a;f.NoteStyle=c;f.NOTE_AUDIENCE_OPTIONS_INTERNAL=d;f.NOTE_AUDIENCE_OPTIONS=e;f.PolarisInboxTrayComposerEntrypoint=b}),66);
__d("hasNotesOnDirectDesktop",["PolarisIsLoggedIn","PolarisUA"],(function(a,b,c,d,e,f,g){"use strict";function a(){return d("PolarisUA").isDesktop()&&d("PolarisIsLoggedIn").isLoggedIn()}g["default"]=a}),98);
__d("IGDInboxTrayContextProvider",["PolarisNotesTypes","QPLUserFlow","emptyFunction","hasNotesOnDirectDesktop","qpl","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));e=h;f=e.createContext;e.useCallback;var j=e.useContext;e.useMemo;var k=e.useState;e={closeComposer:c("emptyFunction"),composerEntrypoint:d("PolarisNotesTypes").PolarisInboxTrayComposerEntrypoint.NEW_NOTE_CTA,isComposerActive:!1,openComposer:c("emptyFunction")};var l=f(e);function a(a){var b=d("react-compiler-runtime").c(8);a=a.children;var e=k(!1),f=e[0],g=e[1];e=k(d("PolarisNotesTypes").PolarisInboxTrayComposerEntrypoint.NEW_NOTE_CTA);var h=e[0],j=e[1];b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e=function(a){j(a),g(c("hasNotesOnDirectDesktop")()),c("QPLUserFlow").start(c("qpl")._(379203828,"2170"))},b[0]=e):e=b[0];e=e;var m;b[1]===Symbol["for"]("react.memo_cache_sentinel")?(m=function(){g(!1)},b[1]=m):m=b[1];m=m;b[2]!==h||b[3]!==f?(m={closeComposer:m,composerEntrypoint:h,isComposerActive:f,openComposer:e},b[2]=h,b[3]=f,b[4]=m):m=b[4];e=m;h=e;b[5]!==a||b[6]!==h?(f=i.jsx(l.Provider,{value:h,children:a}),b[5]=a,b[6]=h,b[7]=f):f=b[7];return f}function b(){return j(l)}g.IGDInboxTrayContextProvider=a;g.usePolarisInboxTrayContext=b}),98);
__d("IGDInboxTrayMobileContextProvider",["PolarisNotesTypes","QPLUserFlow","emptyFunction","qpl","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));e=h;f=e.createContext;e.useCallback;var j=e.useContext;e.useMemo;var k=e.useState;e={closeMobileComposer:c("emptyFunction"),isMobileComposerActive:!1,mobileComposerEntrypoint:d("PolarisNotesTypes").PolarisInboxTrayComposerEntrypoint.NEW_NOTE_CTA,openMobileComposer:c("emptyFunction")};var l=f(e);function a(a){var b=d("react-compiler-runtime").c(8);a=a.children;var e=k(!1),f=e[0],g=e[1];e=k(d("PolarisNotesTypes").PolarisInboxTrayComposerEntrypoint.NEW_NOTE_CTA);var h=e[0],j=e[1];b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e=function(a){j(a),g(!0),c("QPLUserFlow").start(c("qpl")._(379203828,"2170"))},b[0]=e):e=b[0];e=e;var m;b[1]===Symbol["for"]("react.memo_cache_sentinel")?(m=function(){return g(!1)},b[1]=m):m=b[1];m=m;b[2]!==f||b[3]!==h?(m={closeMobileComposer:m,isMobileComposerActive:f,mobileComposerEntrypoint:h,openMobileComposer:e},b[2]=f,b[3]=h,b[4]=m):m=b[4];e=m;f=e;b[5]!==a||b[6]!==f?(h=i.jsx(l.Provider,{value:f,children:a}),b[5]=a,b[6]=f,b[7]=h):h=b[7];return h}function b(){return j(l)}g.IGDInboxTrayMobileContextProvider=a;g.usePolarisInboxTrayMobileContext=b}),98);
__d("IGDSExplicitEFilledIcon.react",["IGDSSVGIconBase.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a){var b=d("react-compiler-runtime").c(3),e;b[0]===Symbol["for"]("react.memo_cache_sentinel")?(e=i.jsx("path",{d:"M23.953 20.164c0 1.89-.988 2.836-2.96 2.836H3.007c-1.973 0-2.961-.945-2.961-2.836V2.852C.047.949 1.035 0 3.007 0h17.985c1.973 0 2.961.95 2.961 2.852ZM16.06 17.79v-1.637h-5.7v-4.226h5.399v-1.578H10.36V6.703h5.7V5.066H8.242V17.79Zm0 0"}),b[0]=e):e=b[0];b[1]!==a?(e=i.jsx(c("IGDSSVGIconBase.react"),babelHelpers["extends"]({},a,{viewBox:"0 0 24 24",children:e})),b[1]=a,b[2]=e):e=b[2];return e}b=i.memo(a);g["default"]=b}),98);
__d("PolarisInboxTrayItemLayout.react",["react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g){"use strict";var h,i,j=i||(i=d("react")),k=i.useContext,l={base:{alignItems:"x6s0dn4",display:"x78zum5",flexDirection:"xdt5ytf",justifyContent:"x13a6bvl",position:"x1n2onr6",$$css:!0},large:{width:"x3smdqs",$$css:!0},medium:{width:"xrostsh",$$css:!0},mediumMobile:{width:"xrostsh",$$css:!0},small:{width:"x13oubkp",$$css:!0}},m=j.createContext();function a(){return k(m)}function b(a){var b=d("react-compiler-runtime").c(12),e=a.innerSlot,f=a.rootSlot;a=a.size;if(f==null)return e;var g;b[0]!==a?(g=(h||(h=c("stylex"))).props(l.base,l[a||"small"]),b[0]=a,b[1]=g):g=b[1];var i;b[2]===Symbol["for"]("react.memo_cache_sentinel")?(i={className:"x6s0dn4 x78zum5 xdt5ytf xh8yej3"},b[2]=i):i=b[2];b[3]!==e?(i=j.jsx("div",babelHelpers["extends"]({},i,{children:e})),b[3]=e,b[4]=i):i=b[4];b[5]!==f||b[6]!==g||b[7]!==i?(e=j.jsxs("div",babelHelpers["extends"]({},g,{children:[f,i]})),b[5]=f,b[6]=g,b[7]=i,b[8]=e):e=b[8];b[9]!==a||b[10]!==e?(f=j.jsx(m.Provider,{value:a,children:e}),b[9]=a,b[10]=e,b[11]=f):f=b[11];return f}g.usePolarisInboxTrayItemLayoutSize=a;g.PolarisInboxTrayItemLayout=b}),98);
__d("PolarisInboxTrayItemGlimmer.react",["IGDSGlimmer.react","PolarisInboxTrayItemLayout.react","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j={glimmer:{backgroundColor:"x1lynahi",$$css:!0}},k={large:{borderStartStartRadius:"x1c9tyrk",borderStartEndRadius:"xeusxvb",borderEndEndRadius:"x1pahc9y",borderEndStartRadius:"x1ertn4p",height:"x1ymw6g",width:"xq1dxzn",$$css:!0},small:{borderStartStartRadius:"x1c9tyrk",borderStartEndRadius:"xeusxvb",borderEndEndRadius:"x1pahc9y",borderEndStartRadius:"x1ertn4p",height:"x14z7g9a",width:"x7mnju",$$css:!0}},l={large:{height:"xlrawln",marginTop:"x1gslohp",width:"xq1dxzn",$$css:!0},small:{height:"xlup9mm",marginTop:"x1gslohp",width:"x1exxlbk",$$css:!0}},m={large:{borderStartStartRadius:"x12ol6y4",borderStartEndRadius:"x180vkcf",borderEndEndRadius:"x1khw62d",borderEndStartRadius:"x709u02",height:"xnxb3zj",marginBottom:"xbvv5uc",width:"x1so1ns2",$$css:!0},small:{borderStartStartRadius:"x12l2aii",borderStartEndRadius:"x1mbk4o",borderEndEndRadius:"x14vvt0a",borderEndStartRadius:"x1w3ol1v",height:"x5kalc8",marginBottom:"xh3wvx0",width:"x13oubkp",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(12);a=a.size;a=a===void 0?"small":a;var e=k[a],f;b[0]!==e?(f=i.jsx(c("IGDSGlimmer.react"),{index:0,xstyle:[j.glimmer,e]}),b[0]=e,b[1]=f):f=b[1];e=l[a];var g;b[2]!==e?(g=i.jsx(c("IGDSGlimmer.react"),{index:0,xstyle:[j.glimmer,e]}),b[2]=e,b[3]=g):g=b[3];b[4]!==f||b[5]!==g?(e=i.jsxs(i.Fragment,{children:[f,g]}),b[4]=f,b[5]=g,b[6]=e):e=b[6];f=m[a];b[7]!==f?(g=i.jsx(c("IGDSGlimmer.react"),{index:0,xstyle:[j.glimmer,f]}),b[7]=f,b[8]=g):g=b[8];b[9]!==e||b[10]!==g?(a=i.jsx(d("PolarisInboxTrayItemLayout.react").PolarisInboxTrayItemLayout,{innerSlot:e,rootSlot:g}),b[9]=e,b[10]=g,b[11]=a):a=b[11];return a}g["default"]=a}),98);
__d("PolarisMarquee.react",["react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||(h=d("react"));b=h;var j=b.useEffect,k=b.useLayoutEffect,l=b.useRef,m=b.useState;function n(a){var b=d("react-compiler-runtime").c(4),c=m(null),e=c[0],f=c[1];b[0]!==a.current?(c=function(){if(a.current==null)return;f(a.current.clientWidth)},b[0]=a.current,b[1]=c):c=b[1];var g;b[2]!==a?(g=[a],b[2]=a,b[3]=g):g=b[3];k(c,g);return e}function o(a,b){var c=d("react-compiler-runtime").c(3);b=b===void 0?75:b;if(a==null)return 0;var e;c[0]!==b||c[1]!==a?(e=Math.round(a*b),c[0]=b,c[1]=a,c[2]=e):e=c[2];return Number(e)}function p(a,b){if(a==null||b==null)return!1;return matchMedia("(prefers-reduced-motion: reduce)").matches?!1:b>a}function q(a,b,c,e){var f=d("react-compiler-runtime").c(20),g=n(a),h=n(b),i=p(g,h);e==="always"?i=!0:e==="none"&&(i=!1);var k=o(h),l;f[0]!==c.current||f[1]!==b.current||f[2]!==h||f[3]!==k||f[4]!==a||f[5]!==g||f[6]!==e||f[7]!==i?(l=function(){var d;if(!i){var f;(f=b.current)==null?void 0:(f=f.getAnimations()[0])==null?void 0:f.cancel();(f=c.current)==null?void 0:(f=f.getAnimations()[0])==null?void 0:f.cancel();if(e==="none"&&g!=null&&h!=null&&h>g){(f=b.current)==null?void 0:f.style.setProperty("overflow","hidden");(f=b.current)==null?void 0:f.style.setProperty("text-overflow","ellipsis")}return}(f=b.current)==null?void 0:f.style.removeProperty("overflow");(f=b.current)==null?void 0:f.style.removeProperty("text-overflow");if(e==="always"&&h!=null){(f=a.current)==null?void 0:f.style.setProperty("max-width",h+"px")}f=[{transform:"translateX(0)"},{transform:"translateX(-100%)"}];(d=b.current)==null?void 0:d.animate(f,{duration:k,iterations:Infinity});(d=c.current)==null?void 0:d.animate(f,{duration:k,iterations:Infinity})},f[0]=c.current,f[1]=b.current,f[2]=h,f[3]=k,f[4]=a,f[5]=g,f[6]=e,f[7]=i,f[8]=l):l=f[8];var m;f[9]!==c||f[10]!==b||f[11]!==h||f[12]!==k||f[13]!==a||f[14]!==g||f[15]!==e||f[16]!==i?(m=[c,b,h,k,a,g,e,i],f[9]=c,f[10]=b,f[11]=h,f[12]=k,f[13]=a,f[14]=g,f[15]=e,f[16]=i,f[17]=m):m=f[17];j(l,m);f[18]!==i?(l={willAnimate:i},f[18]=i,f[19]=l):l=f[19];return l}function a(a){var b=d("react-compiler-runtime").c(16),c=a.children,e=a.onAnimate;a=a.scroll;a=a===void 0?"auto":a;var f=l(),g=l(),h=l();a=q(f,g,h,a);var j=a.willAnimate,m;b[0]!==e||b[1]!==j?(a=function(){j&&(e==null?void 0:e())},m=[e,j],b[0]=e,b[1]=j,b[2]=a,b[3]=m):(a=b[2],m=b[3]);k(a,m);b[4]===Symbol["for"]("react.memo_cache_sentinel")?(a={className:"x6s0dn4 x78zum5 x1nhvcw1 x6ikm8r x10wlt62"},b[4]=a):a=b[4];b[5]!==j?(m={0:{className:"xf159sx xuxw1ft"},1:{className:"xuxw1ft xyri2b"}}[!!!j<<0],b[5]=j,b[6]=m):m=b[6];b[7]!==c||b[8]!==m?(g=i.jsx("div",babelHelpers["extends"]({},m,{ref:g,children:c})),b[7]=c,b[8]=m,b[9]=g):g=b[9];b[10]!==c||b[11]!==j?(m=j&&i.jsx("div",babelHelpers["extends"]({className:"xf159sx xuxw1ft"},{ref:h,children:c})),b[10]=c,b[11]=j,b[12]=m):m=b[12];b[13]!==g||b[14]!==m?(h=i.jsxs("div",babelHelpers["extends"]({},a,{ref:f,children:[g,m]})),b[13]=g,b[14]=m,b[15]=h):h=b[15];return h}g["default"]=a}),98);