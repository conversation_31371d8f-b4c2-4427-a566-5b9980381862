/* Navigation Styles
   Contains styles for the navbar and navigation elements
*/

/* Navbar styles */
.navbar {
    padding: 15px 20px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1000;
}


/* Brand text styles */
.cook {
    color: #FF9933;
    font-weight: 700;
    font-size: 1.5rem;
    font-family: 'Times New Roman', Times, serif;
    margin-right: 5px;
}

.dashboard {
    color: #22bbea;
    font-weight: 700;
    font-size: 1.5rem;
    font-family: 'Times New Roman', Times, serif;
}
.cook, .dashboard{
    display: inline-block; 
    margin: 0; 
    padding: 0; 
    line-height: 1.5; 
}
/* Navigation toggle button */
.navbar-toggler {
    border: none;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    outline: none;
    box-shadow: none;
}

/* Navigation collapse */
.navbar-collapse {
    background-color: #fff;
    padding: 1rem;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
} 