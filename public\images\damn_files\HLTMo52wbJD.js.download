;/*FB_PKG_DELIM*/

__d("XAdsCMAccountSettingsPageControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/ads/manager/account/settings/",Object.freeze({tab:"information",highlight_tax_id:!1}),new Set(["highlight_tax_id"]));b=a;g["default"]=b}),98);
__d("XAdsCMControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/ads/manager/{?page}/{?tab}/",Object.freeze({help_tray:!1,no_redirect:!1,pixel_conversion_dialog:!1,show_edit_modal:!1,is_split_test:!1,m2w:!1,ads_manager_read_regions:!1}),void 0);b=a;g["default"]=b}),98);
__d("XAdsPEControllerRouteBuilder",["jsRouteBuilder"],(function(a,b,c,d,e,f,g){a=c("jsRouteBuilder")("/adsmanager/",Object.freeze({_fb_noscript:!1,breakdown_regrouping:!1,is_reload_from_account_change:!1,is_split_test:!1,launch_quick_creation:!1,show_view_history:!1,show_inbox_re_tos:!1,from_ads_ai:!1,ads_manager_read_regions:!1,ads_manager_write_regions:!1,show_review:!1,duplicate_campaign:!1,duplicate_ad:!1,show_shops_messaging_opt_out_modal:!1,is_mfr_model_shown_by_default:!1,show_inline_verification:!1}),void 0);b=a;g["default"]=b}),98);
__d("AdsManagerConstURIUtils",["AdsManagerReadRegions","XAdsCMAccountSettingsPageControllerRouteBuilder","XAdsCMControllerRouteBuilder","XAdsPEControllerRouteBuilder","gkx"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,d){d=c("XAdsPEControllerRouteBuilder").buildUri(babelHelpers["extends"]({},d,{nav_entry_point:b!=null?b:void 0,nav_source:a}));return j(d)}function b(a,b,d){d=c("XAdsCMControllerRouteBuilder").buildUri(babelHelpers["extends"]({},d,{nav_entry_point:b!=null?b:void 0,nav_source:a}));return j(d)}function d(a,b,d){d=c("XAdsCMAccountSettingsPageControllerRouteBuilder").buildUri(babelHelpers["extends"]({},d,{nav_entry_point:b!=null?b:void 0,nav_source:a}));return j(d)}function h(a){return window.location.host.includes("adsmanager")&&!c("AdsManagerReadRegions").excluded_endpoints.some(function(b){return a.includes(b)})&&c("gkx")("1221")?!0:!1}function i(a,b){b===void 0&&(b=!0);return window.location.host.includes("adsmanager")&&c("gkx")("13382")&&(c("AdsManagerReadRegions").excluded_endpoints.some(function(b){return a.includes(b)})||!b)?!0:!1}function j(a){h(a.toString())?a.addQueryParam("ads_manager_read_regions","true"):i(a.toString())?a.addQueryParam("ads_manager_write_regions","true"):(a.getQueryParam("ads_manager_read_regions")!==null||a.getQueryParam("ads_manager_write_regions")!==null)&&(a.removeQueryParam("ads_manager_read_regions"),a.removeQueryParam("ads_manager_write_regions"));return a}g.getAdsManagerURI=a;g.getXAdsCMControllerURI=b;g.getXAdsCMAccountSettingsPageURI=d;g.shouldRouteToAMReadRegions=h;g.shouldRouteToAMWriteRegions=i;g.getAMReadOrWriteRegionsURI=j}),98);
__d("ArbiterMixin",["Arbiter","guid"],(function(a,b,c,d,e,f,g){var h="arbiter$"+c("guid")(),i=Object.prototype.hasOwnProperty;a={_getArbiterInstance:function(){return i.call(this,h)?this[h]:this[h]=new(c("Arbiter"))()},inform:function(a,b,c){return this._getArbiterInstance().inform(a,b,c)},subscribe:function(a,b,c){return this._getArbiterInstance().subscribe(a,b,c)},subscribeOnce:function(a,b,c){return this._getArbiterInstance().subscribeOnce(a,b,c)},unsubscribe:function(a){this._getArbiterInstance().unsubscribe(a)},unsubscribeCurrentSubscription:function(){this._getArbiterInstance().unsubscribeCurrentSubscription()},releaseCurrentPersistentEvent:function(){this._getArbiterInstance().releaseCurrentPersistentEvent()},registerCallback:function(a,b){return this._getArbiterInstance().registerCallback(a,b)},query:function(a){return this._getArbiterInstance().query(a)}};b=a;g["default"]=b}),98);
__d("TrustedTypesIEFixDOMPolicy",["TrustedTypes"],(function(a,b,c,d,e,f,g){"use strict";a={createHTML:function(a){return a}};b=c("TrustedTypes").createPolicy("dom-ie-fix",a);d=b;g["default"]=d}),98);
__d("UserAgent_DEPRECATED",[],(function(a,b,c,d,e,f){var g=!1,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w;function x(){if(g)return;g=!0;var a=navigator.userAgent,b=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(a),c=/(Mac OS X)|(Windows)|(Linux)/.exec(a);s=/\b(iPhone|iP[ao]d)/.exec(a);t=/\b(iP[ao]d)/.exec(a);q=/Android/i.exec(a);u=/FBAN\/\w+;/i.exec(a);v=/FBAN\/mLite;/i.exec(a);w=/Mobile/i.exec(a);r=!!/Win64/.exec(a);if(b){h=b[1]?parseFloat(b[1]):b[5]?parseFloat(b[5]):NaN;h&&document&&document.documentMode&&(h=document.documentMode);var d=/(?:Trident\/(\d+.\d+))/.exec(a);m=d?parseFloat(d[1])+4:h;i=b[2]?parseFloat(b[2]):NaN;j=b[3]?parseFloat(b[3]):NaN;k=b[4]?parseFloat(b[4]):NaN;k?(b=/(?:Chrome\/(\d+\.\d+))/.exec(a),l=b&&b[1]?parseFloat(b[1]):NaN):l=NaN}else h=i=j=l=k=NaN;if(c){if(c[1]){d=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(a);n=d?parseFloat(d[1].replace("_",".")):!0}else n=!1;o=!!c[2];p=!!c[3]}else n=o=p=!1}function y(){return x()||h}function a(){return x()||m>h}function b(){return y()&&r}function c(){return x()||i}function d(){return x()||j}function z(){return x()||k}function e(){return z()}function A(){return x()||l}function B(){return x()||o}function C(){return x()||n}function D(){return x()||p}function E(){return x()||s}function F(){return x()||s||t||q||w}function G(){return x()||v!=null?null:u}function H(){return x()||q}function I(){return x()||t}f.ie=y;f.ieCompatibilityMode=a;f.ie64=b;f.firefox=c;f.opera=d;f.webkit=z;f.safari=e;f.chrome=A;f.windows=B;f.osx=C;f.linux=D;f.iphone=E;f.mobile=F;f.nativeApp=G;f.android=H;f.ipad=I}),66);
__d("isScalar",[],(function(a,b,c,d,e,f){function a(a){return/string|number|boolean/.test(typeof a)}f["default"]=a}),66);
__d("DOM",["$","DOMQuery","Event","FBLogger","FbtResultBase","HTML","TrustedTypesIEFixDOMPolicy","UserAgent_DEPRECATED","createArrayFromMixed","fb-error","isNode","isScalar","isTextNode"],(function(a,b,c,d,e,f,g){a=function(a,b,c){a=document.createElement(a);b&&h.setAttributes(a,b);c!=null&&h.setContent(a,c);return a};var h={find:(b=d("DOMQuery")).find,findPushSafe:b.findPushSafe,scry:b.scry,getSelection:b.getSelection,contains:b.contains,getRootElement:b.getRootElement,isNodeOfType:b.isNodeOfType,isInputNode:b.isInputNode,create:a,setAttributes:function(a,b){b.type&&(a.type=b.type);for(var d in b){var e=b[d],f=/^on/i.test(d);f&&typeof e!=="function"&&c("FBLogger")("dom").warn("Handlers passed to DOM.setAttributes must be functions. Handler passed for %s was %s",d,typeof e);if(d=="type")continue;else d=="style"?typeof e==="string"?a.style.cssText=e:Object.assign(a.style,e):f?c("Event").listen(a,d.substr(2),e):d in a?a[d]=e:a.setAttribute&&a.setAttribute(d,e)}},prependContent:function(a,b){if(!a)throw c("fb-error").TAAL.blameToPreviousFile(new Error("reference element is not a node"));return j(b,a,function(b){a.firstChild?a.insertBefore(b,a.firstChild):a.appendChild(b)})},insertAfter:function(a,b){if(!a||!a.parentNode)throw c("fb-error").TAAL.blameToPreviousFile(new Error("reference element does not have a parent"));var d=a.parentNode;return j(b,d,function(b){a.nextSibling?d.insertBefore(b,a.nextSibling):d.appendChild(b)})},insertBefore:function(a,b){if(!a||!a.parentNode)throw c("fb-error").TAAL.blameToPreviousFile(new Error("reference element does not have a parent"));var d=a.parentNode;return j(b,d,function(b){d.insertBefore(b,a)})},setContent:function(a,b){if(!a)throw c("fb-error").TAAL.blameToPreviousFile(new Error("reference element is not a node"));while(a.firstChild)i(a.firstChild);return h.appendContent(a,b)},appendContent:function(a,b){if(!a)throw c("fb-error").TAAL.blameToPreviousFile(new Error("reference element is not a node"));return j(b,a,function(b){a.appendChild(b)})},replace:function(a,b){if(!a||!a.parentNode)throw c("fb-error").TAAL.blameToPreviousFile(new Error("reference element does not have a parent"));var d=a.parentNode;return j(b,d,function(b){d.replaceChild(b,a)})},remove:function(a){i(typeof a==="string"?c("$")(a):a)},empty:function(a){a=typeof a==="string"?c("$")(a):a;while(a.firstChild)i(a.firstChild)}};function i(a){a.parentNode&&a.parentNode.removeChild(a)}function j(a,b,e){a=c("HTML").replaceJSONWrapper(a);if(a instanceof c("HTML")&&b.firstChild===null&&-1===a.toString().indexOf("<script")){var f=d("UserAgent_DEPRECATED").ie();if(!f||f>7&&!d("DOMQuery").isNodeOfType(b,["table","tbody","thead","tfoot","tr","select","fieldset"])){var g=f?'<em style="display:none;">&nbsp;</em>':"";b.innerHTML=c("TrustedTypesIEFixDOMPolicy").createHTML(g+a);f&&b.removeChild(b.firstChild);return Array.from(b.childNodes)}}else if(c("isTextNode")(b)){b.data=a;return[a]}g=document.createDocumentFragment();var h;f=[];b=[];var i=!1;a=c("createArrayFromMixed")(a);a.length===1&&a[0]instanceof c("FbtResultBase")&&(a=a[0].getContents());for(var j=0;j<a.length;j++){h=c("HTML").replaceJSONWrapper(a[j]);if(h instanceof c("HTML")){b.push(h.getAction());var k=h.getNodes();!i&&h.hasInlineJs()&&(c("FBLogger")("staticresources").warn("DOM: adding HTML which contains inline JS"),i=!0);for(var l=0;l<k.length;l++)f.push(k[l]),g.appendChild(k[l])}else if(c("isScalar")(h)||h instanceof c("FbtResultBase")){l=document.createTextNode(h);f.push(l);g.appendChild(l)}else c("isNode")(h)?(f.push(h),g.appendChild(h)):(Array.isArray(h)&&c("FBLogger")("dom").warn("Nest arrays not supported"),h!==null&&c("FBLogger")("dom").warn("No way to set content %s",h))}e(g);b.forEach(function(a){a()});return f}e=h;g["default"]=e}),98);
__d("AsyncDOM",["CSS","DOM","FBLogger"],(function(a,b,c,d,e,f){a={invoke:function(a,c){for(var d=0;d<a.length;++d){var e=a[d],f=e[0],g=e[1],h=e[2];e=e[3];h=h&&c||null;g&&(h=b("DOM").scry(h||document.documentElement,g)[0]);h||b("FBLogger")("async_dom").warn("Could not find relativeTo element for %s AsyncDOM operation based on selector: %s",f,g);switch(f){case"hide":b("CSS").hide(h);break;case"show":b("CSS").show(h);break;case"setContent":b("DOM").setContent(h,e);break;case"appendContent":b("DOM").appendContent(h,e);break;case"prependContent":b("DOM").prependContent(h,e);break;case"insertAfter":b("DOM").insertAfter(h,e);break;case"insertBefore":b("DOM").insertBefore(h,e);break;case"remove":b("DOM").remove(h);break;case"replace":b("DOM").replace(h,e);break;default:b("FBLogger")("async_dom").warn("Received invalid command %s for AsyncDOM operation",f)}}}};e.exports=a}),null);
__d("AsyncResponse",["invariant","Bootloader","FBLogger","HTML","WebDriverConfig"],(function(a,b,c,d,e,f,g,h){"use strict";a=function(){function a(a,b){this.error=0,this.errorSummary=null,this.errorDescription=null,this.errorMid=null,this.onload=null,this.replay=!1,this.payload=b,this.request=a,this.silentError=!1,this.transientError=!1,this.blockedAction=!1,this.is_last=!0,this.responseHeaders=null}var b=a.prototype;b.getRequest=function(){return this.request};b.getPayload=function(){return this.payload};b.toError=function(){this.error!==0||h(0,5599);var a=this.errorSummary||"",b=this.getErrorDescriptionString()||"",c=new Error(a.toString()+": "+b);Object.assign(c,{code:this.error,description:this.errorDescription||"",descriptionString:b,response:this,summary:a,isSilent:this.silentError,isTransient:this.transientError,mid:this.errorMid});return c};b.getError=function(){return this.error};b.getErrorSummary=function(){return this.errorSummary};b.setErrorSummary=function(a){a=a===void 0?null:a;this.errorSummary=a;return this};b.getErrorDescription=function(){return this.errorDescription};b.getErrorDescriptionString=function(){var a=this.getErrorDescription();if(a==null)return null;if(c("HTML").isHTML(a)){var b=new(c("HTML"))(a);return b.getRootNode().textContent}return a.toString()};b.getErrorIsWarning=function(){return!!this.errorIsWarning};b.isSilent=function(){return!!this.silentError};b.isTransient=function(){return!!this.transientError};b.isBlockedAction=function(){return!!this.blockedAction};b.getResponseHeader=function(a){var b=this.responseHeaders;if(!b)return null;b=b.replace(/^\n/,"");a=a.toLowerCase();b=b.split("\r\n");for(var c=0;c<b.length;++c){var d=b[c],e=d.indexOf(": ");if(e<=0)continue;var f=d.substring(0,e).toLowerCase();if(f===a)return d.substring(e+2)}return null};a.defaultErrorHandler=function(b){try{!b.silentError?a.verboseErrorHandler(b):c("FBLogger")("async_response").catching(b.toError()).warn("default error handler called")}catch(a){alert(b)}};a.verboseErrorHandler=function(a,b){c("Bootloader").loadModules(["ExceptionDialog"],function(c){return c.showAsyncError(a,b)},"AsyncResponse")};return a}();g["default"]=a}),98);
__d("FetchStreamConfig",[],(function(a,b,c,d,e,f){a=Object.freeze({delim:"/*<!-- fetch-stream -->*/"});f["default"]=a}),66);
__d("StreamBlockReader",[],(function(a,b,c,d,e,f){a=function(){function a(a){var b=this;if(!a.getReader)throw new Error("No getReader method found on given object");this.$3=a.getReader();this.$1="";this.$2=null;this.$4=!1;this.$5="utf-8";this.$6="";this.$9=!1;this.$8=function(){return Promise.reject("Sorry, you are somehow using this too early.")};this.$7=new Promise(function(a,c){b.$8=a})}var b=a.prototype;b.changeEncoding=function(a){if(this.$2)throw new Error("Decoder already in use, encoding cannot be changed");this.$5=a};b.$10=function(){if(!self.TextDecoder)throw new Error("TextDecoder is not supported here");this.$2||(this.$2=new self.TextDecoder(this.$5));return this.$2};b.$11=function(){if(this.$9)throw new Error("Something else is already reading from this reader");this.$9=!0};b.$12=function(){this.$9=!1};b.isDone=function(){return this.$4};b.$13=async function(){if(this.$6!==""){var a=this.$6;this.$6="";return a}if(this.isDone())throw new Error("You cannot read from a stream that is done");a=await this.$3.read();var b=a.done;a=a.value;this.$4=b;b&&this.$8();return a?this.$10().decode(a,{stream:!b}):""};b.readNextBlock=async function(){this.$11();var a=this.$13();this.$12();return a};b.readUntilStringOrEnd=async function(a){return await this.readUntilOneOfStringOrEnd_DO_NOT_USE([a])};b.readUntilStringOrThrow=async function(a){if(!a)throw new Error("cannot read empty string");this.$11();var b="",c=0;while(!this.isDone()){b+=await this.$13();if(b.length<a.length)continue;var d=b.substring(c).indexOf(a);if(d!==-1){d+=c;this.$6=b.substring(d+a.length);this.$12();return b.substring(0,d)}else c=b.length-a.length+1}this.$6=b;this.$12();throw new Error("Breakpoint not found")};b.readUntilOneOfStringOrEnd_DO_NOT_USE=async function(a){this.$11();var b="";while(!this.isDone()){b+=await this.$13();for(var c=0;c<a.length;c++){var d=a[c],e=b.indexOf(d);if(e!==-1){this.$6=b.substring(e+d.length);this.$12();return b.substring(0,e)}}}this.$12();return b};b.waitUntilDone=async function(){return this.$7};return a}();f.default=a}),66);
__d("mixin",[],(function(a,b,c,d,e,f){function a(){var a=function(){},b=0,c;for(var d=arguments.length,e=new Array(d),f=0;f<d;f++)e[f]=arguments[f];while(e[b]){c=e[b];for(var g in c)Object.prototype.hasOwnProperty.call(c,g)&&(a.prototype[g]=c[g]);b+=1}return a}f["default"]=a}),66);
__d("FetchStreamTransport",["ArbiterMixin","FetchStreamConfig","StreamBlockReader","TimeSlice","URI","mixin","nullthrows"],(function(a,b,c,d,e,f,g){var h,i=0;a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var c;if(!self.ReadableStream||!self.fetch||!Request||!TextDecoder)throw new Error("fetch stream transport is not supported here");c=a.call(this)||this;c.$FetchStreamTransport$p_6=null;c.$FetchStreamTransport$p_1=b;c.$FetchStreamTransport$p_3=!1;c.$FetchStreamTransport$p_4=!1;c.$FetchStreamTransport$p_5=!1;c.$FetchStreamTransport$p_2=++i;return babelHelpers.assertThisInitialized(c)||babelHelpers.assertThisInitialized(c)}var d=b.prototype;d.hasFinished=function(){return this.$FetchStreamTransport$p_5};d.getRequestURI=function(){return new(h||(h=c("URI")))(this.$FetchStreamTransport$p_1).addQueryData({__a:1,__adt:this.$FetchStreamTransport$p_2,__req:"fetchstream_"+this.$FetchStreamTransport$p_2,ajaxpipe_fetch_stream:1})};d.send=function(){if(this.$FetchStreamTransport$p_3)throw new Error("FetchStreamTransport instances cannot be re-used.");this.$FetchStreamTransport$p_3=!0;var a=new Request(this.getRequestURI().toString(),{mode:"same-origin",credentials:"include"});this.$FetchStreamTransport$p_6=c("TimeSlice").getGuardedContinuation("FetchStreamTransport: waiting on first response");a=self.fetch(a,{redirect:"follow"});this.$FetchStreamTransport$p_7(a)};d.$FetchStreamTransport$p_7=async function(a){var b=this,d;try{d=await a}catch(a){this.abort()}if(!d||!d.body||!d.ok){this.abort();return}var e=new(c("StreamBlockReader"))(d.body);a=async function(){var a=await e.readUntilStringOrEnd(c("FetchStreamConfig").delim);if(b.$FetchStreamTransport$p_4)return"break";c("nullthrows")(b.$FetchStreamTransport$p_6)(function(){var d=JSON.parse(a);e.isDone()||d.finished?b.$FetchStreamTransport$p_5=!0:b.$FetchStreamTransport$p_6=c("TimeSlice").getGuardedContinuation("FetchStreamTransport: waiting on next response");b.inform("response",d.content)})};while(!this.$FetchStreamTransport$p_5&&!this.$FetchStreamTransport$p_4){var f=await a();if(f==="break")break}};d.abort=function(){var a=this;if(this.$FetchStreamTransport$p_4||this.$FetchStreamTransport$p_5)return;this.$FetchStreamTransport$p_4=!0;this.$FetchStreamTransport$p_5=!0;if(this.$FetchStreamTransport$p_6){var b=this.$FetchStreamTransport$p_6;b(function(){a.inform("abort")})}else this.inform("abort")};return b}(c("mixin")(c("ArbiterMixin")));g.default=a}),98);
__d("HTTPErrors",["emptyFunction"],(function(a,b,c,d,e,f,g){function a(a){return{summary:"HTTP Error",description:"Unknown HTTP error #"+a}}b={get:a,getAll:c("emptyFunction").thatReturns(new Map())};d=b;g["default"]=d}),98);
__d("SessionName",["SessionNameConfig"],(function(a,b,c,d,e,f){e.exports={getName:function(){return b("SessionNameConfig").seed}}}),null);
__d("bind",[],(function(a,b,c,d,e,f){function a(a,b){var c=Array.prototype.slice.call(arguments,2);if(typeof b!=="string")return Function.prototype.bind.apply(b,[a].concat(c));function d(){var d=c.concat(Array.prototype.slice.call(arguments));if(a[b])return a[b].apply(a,d)}d.toString=function(){return"bound lazily: "+a[b]};return d}e.exports=a}),null);
__d("executeAfter",[],(function(a,b,c,d,e,f){function a(a,b,c){return function(){a.apply(c||this,arguments),b.apply(c||this,arguments)}}e.exports=a}),null);
__d("goURI",["cr:8906"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:8906")}),98);
__d("isSparkDotMetaDotComURI",[],(function(a,b,c,d,e,f){var g=new RegExp("(^|\\.)spark\\.meta\\.com$","i"),h=["https"];function a(a){if(a.isEmpty()&&a.toString()!=="#")return!1;return!a.getDomain()&&!a.getProtocol()?!1:h.indexOf(a.getProtocol())!==-1&&g.test(a.getDomain())}f["default"]=a}),66);
__d("AsyncRequest",["errorCode","fbt","invariant","AdsManagerConstURIUtils","Arbiter","AsyncDOM","AsyncRequestConfig","AsyncResponse","Bootloader","CSS","DTSG","DTSG_ASYNC","Deferred","Env","ErrorGuard","Event","FBLogger","FetchStreamTransport","HTTPErrors","HasteResponse","PHPQuerySerializer","Parent","Promise","ResourceTimingsStore","ResourceTypes","Run","ScriptPath","ServerJS","SessionName","TimeSlice","URI","UserAgent_DEPRECATED","ZeroRewrites","bind","clearTimeout","emptyFunction","executeAfter","fb-error","ge","getAsyncHeaders","getAsyncParams","gkx","goURI","isArDotMetaDotComURI","isEmpty","isFacebookURI","isHorizonDotMetaDotComURI","isInternalFBURI","isMessengerDotComURI","isSparkDotMetaDotComURI","isWorkDotMetaDotComURI","isWorkplaceDotComURI","isWorkroomsDotComURI","performanceAbsoluteNow","promiseDone","replaceTransportMarkers","setTimeout","setTimeoutAcrossTransitions","unrecoverableViolation","uriIsRelativePath"],(function(a,b,c,d,e,f,g,h,i,j){"use strict";var k,l,m,n,o,p,q;e=19e3;f=500;var r=1006,s=1004,t=1010,u=new Set([f,t,s,r]),v=!1;d("Run").onAfterUnload(function(){v=!0});function w(){return v}function x(a){return"onprogress"in a}function y(a){return"upload"in a&&"onprogress"in a.upload}function z(a){return"withCredentials"in a}function A(a){return a.status in{0:1,12029:1,12030:1,12031:1,12152:1}}function B(a){a=!a||typeof a==="function";a||c("FBLogger")("asyncresponse").mustfix("AsyncRequest response handlers must be functions. Pass a function, or use bind() to build one.");return a}var C=2,D=C,E=!1;c("Arbiter").subscribe("page_transition",function(a,b){!E?D=b.id:E=!1});var F="for (;;);",G=F.length,H=function(){function a(b){var e=this,f;this._allowIrrelevantRequests=!1;this._delayPreDisplayJS=!1;this._shouldReplaceTransportMarkers=!1;this._dispatchErrorResponse=function(a,b){var d=a.getError();e.clearStatusIndicator();if(!e._isRelevant()||d===t){e.abort();return}if(e._isServerDialogErrorCode(d)){var f=d==1357008||d==1357007;e.interceptHandler(a);d==1357007?e._displayServerDialog(a,f,!0):e._displayServerDialog(a,f)}else if(e.initialHandler(a)!==!1){c("clearTimeout")(e.timer);try{b(a)}catch(b){e.finallyHandler(a);throw b}e.finallyHandler(a)}};this._onStateChange=function(){var b=e.transport;if(!b)return;try{a._inflightCount--;d("ResourceTimingsStore").measureResponseReceived(c("ResourceTypes").XHR,e.resourceTimingStoreUID);try{b.getResponseHeader("X-FB-Debug")&&(e._xFbServer=b.getResponseHeader("X-FB-Debug"),c("fb-error").ErrorXFBDebug.add(e._xFbServer))}catch(a){}if(b.status>=200&&b.status<300)a.lastSuccessTime=Date.now(),e._handleXHRResponse(b);else if(d("UserAgent_DEPRECATED").webkit()&&typeof b.status==="undefined")e._invokeErrorHandler(1002);else if(c("AsyncRequestConfig").retryOnNetworkError&&A(b)&&e.remainingRetries>0&&!e._requestTimeout){e.remainingRetries--;delete e.transport;e.send(!0);return}else e._invokeErrorHandler();e.getOption("asynchronous_DEPRECATED")!==!1&&delete e.transport}catch(a){if(w())return;delete e.transport;e.remainingRetries>0?(e.remainingRetries--,e.send(!0)):(e.getOption("suppressErrorAlerts")||c("FBLogger")("AsyncRequest").catching(a).mustfix("AsyncRequest exception when attempting to handle a state change"),e._invokeErrorHandler(1007))}};this._handleTimeout=function(){e.continuation.last(function(){e._requestTimeout=!0;var a=e.timeoutHandler;e.abandon();a&&a(e);c("setTimeout")(function(){c("Arbiter").inform("AsyncRequest/timeout",{request:e})},0)})};this.continuation=c("TimeSlice").getPlaceholderReusableContinuation();this.transport=null;this.method="POST";this.uri="";this.timeout=null;this.timer=null;this.initialHandler=f=c("emptyFunction");this.handler=null;this.uploadProgressHandler=null;this.errorHandler=c("AsyncResponse").defaultErrorHandler;this.transportErrorHandler=null;this.timeoutHandler=null;this.interceptHandler=f;this.finallyHandler=f;this.abortHandler=f;this.serverDialogCancelHandler=null;this.relativeTo=null;this.statusElement=null;this.statusClass="";this.data={};this.headers={};this.file=null;this.context={};this.readOnly=!1;this.writeRequiredParams=[];this.remainingRetries=0;this.userActionID="-";this.resourceTimingStoreUID=d("ResourceTimingsStore").getUID(c("ResourceTypes").XHR,b!=null?b.toString():"");this.flushedResponseTextParseIndex=0;this.option={asynchronous_DEPRECATED:!0,suppressErrorHandlerWarning:!1,suppressEvaluation:!1,suppressErrorAlerts:!1,retries:0,bundle:!1,handleErrorAfterUnload:!1,useFetchTransport:!1};this.transportErrorHandler=c("bind")(this,"errorHandler");b!==void 0&&this.setURI(b);this.setAllowCrossPageTransition(c("AsyncRequestConfig").asyncRequestsSurviveTransitionsDefault||!1)}var e=a.prototype;e._dispatchResponse=function(a){this.clearStatusIndicator();if(!this._isRelevant()){this._invokeErrorHandler(t);return}if(this.initialHandler(a)===!1)return;c("clearTimeout")(this.timer);var b,d=this.getHandler();if(d)try{b=this._shouldSuppressJS(d(a))}catch(b){a.is_last&&this.finallyHandler(a);throw b}b||this._handleJSResponse(a);a.is_last&&this.finallyHandler(a)};e._shouldSuppressJS=function(b){return b===a.suppressOnloadToken};e._handlePreDisplayServerJS=function(a,b){var d=!1,e=[],f=function(){if(d){c("FBLogger")("AsyncResponse").warn("registerToBlockDisplayUntilDone_DONOTUSE called after AsyncResponse display started. This is a no-op.");return function(){}}var a,b=new(c("Deferred"))();e.push(b.getPromise());return c("TimeSlice").guard(function(){a&&c("clearTimeout")(a),b.resolve()},"AsyncRequestDisplayBlockingEvent",{propagationType:c("TimeSlice").PropagationType.EXECUTION})};a.handle(b,{bigPipeContext:{registerToBlockDisplayUntilDone_DONOTUSE:f}});d=!0;return e};e._hasEvalDomOp=function(a){return a&&a.length?a.some(function(a){return a[0]==="eval"}):!1};e._handleJSResponse=function(a){var b=this.getRelativeTo(),e=a.domops,f=a.dtsgToken,g=a.dtsgAsyncGetToken,h=a.jsmods,i=a.savedServerJSInstance;i&&i instanceof c("ServerJS")?i=i:i=new(c("ServerJS"))();i.setRelativeTo(b);if(h){var j={define:h.define,instances:h.instances,markup:h.markup};delete h.define;delete h.instances;delete h.markup;this._hasEvalDomOp(e)&&(j.elements=h.elements,delete h.elements);i.handle(j)}j=new(m||(m=c("URI")))(this.uri);(!j.getDomain()&&!j.getProtocol()||document.location.origin===j.getOrigin())&&(f&&d("DTSG").setToken(f),g&&d("DTSG_ASYNC").setToken(g));e&&(q||(q=c("ErrorGuard"))).applyWithGuard(function(){return d("AsyncDOM").invoke(e,b)},null,[],{errorType:"warn"});h&&i.handle(h);this._handleJSRegisters(a,"onload");this._handleJSRegisters(a,"onafterload")};e._handleJSRegisters=function(a,b){a=a[b];if(a)for(b=0;b<a.length;b++){var d=null,e=a[b],f=e.match(/^\"caller:([^\"]+?)\";(.*)/);f!=null&&(d=f[1],e=f[2]);(q||(q=c("ErrorGuard"))).applyWithGuard(new Function(e),this,[]);c("FBLogger")("comet_infra").info("Detected dynamic new Function(...) call in AsyncRequest._handleJSRegisters(...).",new(m||(m=c("URI")))(this.uri).getPath(),d)}};e.invokeResponseHandler=function(a){var e=this;if(typeof a.redirect!=="undefined"){c("setTimeout")(function(){e.setURI(a.redirect,!0).send()},0);return}if(a.bootloadOnly!==void 0){var f=typeof a.bootloadOnly==="string"?JSON.parse(a.bootloadOnly):a.bootloadOnly,g=function(a){c("TimeSlice").guard(function(){c("Bootloader").loadPredictedResourceMap(a)},"Bootloader.loadPredictedResourceMap",{root:!0})()};for(f of f)g(f);return}if(!this.handler&&!this.errorHandler&&!this.transportErrorHandler&&!this.preBootloadHandler&&this.initialHandler===c("emptyFunction")&&this.finallyHandler===c("emptyFunction"))return;var h=a.asyncResponse;if(typeof h!=="undefined"){if(!this._isRelevant()){this._invokeErrorHandler(t);return}h.updateScriptPath&&c("ScriptPath").set(h.updateScriptPath.path,h.updateScriptPath.token,h.updateScriptPath.extra_info);h.lid&&(this._responseTime=Date.now(),this.lid=h.lid);d("HasteResponse").handleSRPayload((g=h.hsrp)!=null?g:{});var i,j;if(h.getError()&&!h.getErrorIsWarning()){f=this.getErrorHandler().bind(this);i=(q||(q=c("ErrorGuard"))).guard(this._dispatchErrorResponse,{name:"AsyncRequest#_dispatchErrorResponse for "+this.getURI()});i=i.bind(this,h,f);j="error"}else{i=(q||(q=c("ErrorGuard"))).guard(this._dispatchResponse.bind(this),{name:"AsyncRequest#_dispatchResponse for "+this.getURI()});i=i.bind(this,h);j="response";g=h.domops;if(!this._delayPreDisplayJS&&h.jsmods&&h.jsmods.pre_display_requires&&!this._hasEvalDomOp(g)){f=h.jsmods;g={define:f.define,instances:f.instances,markup:f.markup};delete f.define;delete f.instances;delete f.markup;g.pre_display_requires=f.pre_display_requires;delete f.pre_display_requires;f=new(c("ServerJS"))();f.setRelativeTo(this.getRelativeTo());h.savedServerJSInstance=f;var k=this._handlePreDisplayServerJS(f,g);if(k&&k.length){var m=i;i=function(){c("promiseDone")((l||(l=b("Promise"))).all(k).then(m))}}}}var o=(n||(n=c("performanceAbsoluteNow")))();i=c("executeAfter")(i,function(){c("Arbiter").inform("AsyncRequest/"+j,{request:e,response:h,ts:o})});this.preBootloadHandler&&this.preBootloadHandler(h);c("Bootloader").loadResources((f=h.allResources)!=null?f:[],{onAll:c("AsyncRequestConfig").immediateDispatch?i:function(){c("setTimeout")(i,0)}})}else typeof a.transportError!=="undefined"?this._xFbServer?this._invokeErrorHandler(1008):this._invokeErrorHandler(1012):this._invokeErrorHandler(1007)};e._invokeErrorHandler=function(a){var b=this,d=this.transport;if(!d)return;var e;if(this.responseText==="")e=1002;else if(this._requestAborted)e=1011;else{try{e=a||d.status||s}catch(a){e=1005}!1===navigator.onLine&&(e=r)}var f,g;a=!0;if(e===r)g=i._(/*BTDS*/"No Network Connection"),f=i._(/*BTDS*/"Your browser appears to be offline. Please check your internet connection and try again.");else if(e>=300&&e<=399){g=i._(/*BTDS*/"Redirection");f=i._(/*BTDS*/"Your access to Facebook was redirected or blocked by a third party at this time, please contact your ISP or reload.");var h=d.getResponseHeader("Location");h&&c("goURI")(h,!0);a=!0}else g=i._(/*BTDS*/"Oops"),f=i._(/*BTDS*/"Something went wrong. We're working on getting this fixed as soon as we can. You may be able to try again.");var j=new(c("AsyncResponse"))(this,d);Object.assign(j,{error:e,errorSummary:g,errorDescription:f,silentError:a,errorMid:d.getResponseHeader("Error-Mid")});c("setTimeout")(function(){c("Arbiter").inform("AsyncRequest/error",{request:b,response:j})},0);if(w()&&!this.getOption("handleErrorAfterUnload"))return;if(!this.transportErrorHandler){c("FBLogger")("asyncresponse").mustfix("Async request to %s failed with a %d error, but there was no error handler available to deal with it.",this.getURI(),e);return}h=this.getTransportErrorHandler().bind(this);!(this.getOption("suppressErrorAlerts")||u.has(e))?c("FBLogger")("asyncresponse").addToCategoryKey(String(e)).mustfix("Async request failed with error %s: %s when requesting %s",e,f.toString(),this.getURI()):u.has(e)&&c("FBLogger")("asyncresponse").addToCategoryKey(String(e)).warn("Async request failed with error %s: %s when requesting %s",e,f.toString(),this.getURI());(q||(q=c("ErrorGuard"))).applyWithGuard(this._dispatchErrorResponse,this,[j,h])};e._isServerDialogErrorCode=function(a){return a==1357008||a==1357007||a==1357041||a==1442002||a==1357001};e._displayServerDialog=function(a,b,d){var e=this;d===void 0&&(d=!1);var f=a.getPayload();if(f.__dialog!==void 0){this._displayServerLegacyDialog(a,b);return}b=f.__dialogx;new(c("ServerJS"))().handle(b);if(f.__should_use_mwa_reauth===!0){c("Bootloader").loadModules(["MWADeveloperReauthBarrier"],function(b){b.registerRequest(f.__dialogID,e,a)},"AsyncRequest");return}c("Bootloader").loadModules(["ConfirmationDialog"],function(b){b.setupConfirmation(a,e,d)},"AsyncRequest")};e._displayServerLegacyDialog=function(a,b){var d=this,e=a.getPayload().__dialog;if(c("gkx")("20935")){var f;c("FBLogger")("comet_infra").addMetadata("COMET_INFRA","ERROR_CODE",a.getError().toString()).addMetadata("COMET_INFRA","ERROR_URL",(f=(f=a.request)==null?void 0:f.getURI())!=null?f:"unknown").mustfix("AsyncRequest._displayServerLegacyDialog called in Comet")}c("Bootloader").loadModules(["Dialog"],function(c){c=new c(e);b&&c.setHandler(d._displayConfirmationHandler.bind(d,c));c.setCancelHandler(function(){var b=d.getServerDialogCancelHandler();try{b&&b(a)}catch(a){throw a}finally{d.finallyHandler(a)}}).setCausalElement(d.relativeTo).show()},"AsyncRequest")};e._displayConfirmationHandler=function(a){this.data.confirmed=1,Object.assign(this.data,a.getFormData()),this.send()};e.$1=function(a){a.subscribe("response",this._handleJSONPResponse.bind(this)),a.subscribe("abort",this._handleJSONPAbort.bind(this)),this.transport=a};e._handleJSONPResponse=function(a,b){a=this.transport;if(!a)return;b.bootloadOnly||(this.is_first=this.is_first===void 0);b=this._interpretResponse(b);b.asyncResponse&&(b.asyncResponse.is_first=this.is_first,b.asyncResponse.is_last=a.hasFinished());this.invokeResponseHandler(b);a.hasFinished()&&delete this.transport};e._handleJSONPAbort=function(){this._invokeErrorHandler(),delete this.transport};e._handleXHRResponse=function(a){var b;if(this.getOption("suppressEvaluation"))b={asyncResponse:new(c("AsyncResponse"))(this,a)};else try{this._handleFlushedResponse();a=a.responseText;a=this._filterOutFlushedText(a);a=this._unshieldResponseText(a);a=JSON.parse(a);b=this._interpretResponse(a)}catch(a){b=a.message,c("FBLogger")("async_request").catching(a).warn("Failed to handle response")}this.invokeResponseHandler(b)};e._handleFlushedResponse=function(){var a=this.flushedResponseHandler,b=this.transport;if(a&&b){var c=b.responseText.indexOf(F);c=c===-1?b.responseText.length:c;a(b.responseText.substring(this.flushedResponseTextParseIndex,c));this.flushedResponseTextParseIndex=c}};e._unshieldResponseText=function(a){if(a.length<=G)throw new Error("Response too short on async");var b=0;while(a.charAt(b)==" "||a.charAt(b)=="\n")b++;b&&a.substring(b,b+G)==F;return a.substring(b+G)};e._filterOutFlushedText=function(a){if(!this.flushedResponseHandler)return a;var b=a.indexOf(F);return b<0?a:a.substr(b)};e._interpretResponse=function(a){if(a.redirect)return{redirect:a.redirect};if(a.bootloadOnly)return{bootloadOnly:a.bootloadOnly};var b=a.error&&this._isServerDialogErrorCode(a.error);this._shouldReplaceTransportMarkers&&a.payload&&!b&&c("replaceTransportMarkers")({relativeTo:this.getRelativeTo(),bigPipeContext:null},a.payload);b=new(c("AsyncResponse"))(this);if(a.__ar!=1)c("FBLogger")("AsyncRequest").warn("AsyncRequest to endpoint %s returned a JSON response, but it is not properly formatted. The endpoint needs to provide a response using the AsyncResponse class in PHP.",this.getURI()),b.payload=a;else{Object.assign(b,a);a=this.transport;a&&a.getAllResponseHeaders!==void 0&&(b.responseHeaders=a.getAllResponseHeaders())}return{asyncResponse:b}};e._isMultiplexable=function(){if(this.getOption("useFetchTransport")){c("FBLogger")("AsyncRequest").mustfix("You cannot bundle AsyncRequest that uses iframe transport.");return!1}if(!c("isFacebookURI")(new(m||(m=c("URI")))(this.uri))){c("FBLogger")("AsyncRequest").mustfix("You can not bundle AsyncRequest sent to non-facebook URIs.  Uri: %s",this.getURI());return!1}if(!this.getOption("asynchronous_DEPRECATED")){c("FBLogger")("AsyncRequest").mustfix("We cannot bundle synchronous AsyncRequests");return!1}return!0};e.handleResponse=function(a){a=this._interpretResponse(a);this.invokeResponseHandler(a)};e.setMethod=function(a){this.method=a.toString().toUpperCase();return this};e.getMethod=function(){return this.method};e.setData=function(a){this.data=a;return this};e.setRequestHeader=function(a,b){this.headers[a]=b;return this};e.setRawData=function(a){this.rawData=a;return this};e.getData=function(){return this.data};e.setContextData=function(a,b,c){c=c===void 0?!0:c;c&&(this.context["_log_"+a]=b);return this};e._setUserActionID=function(){this.userActionID=(d("SessionName").getName()||"-")+"/-"};e.setURI=function(a,b){b===void 0&&(b=!1);typeof a==="string"&&a.match(/^\/?u_\d+_\d+/)&&c("FBLogger")("asyncrequest").warn("Invalid URI %s",a);var e=new(m||(m=c("URI")))(a);if(this.getOption("useFetchTransport")&&!c("isFacebookURI")(e)){b&&j(0,45284);return this}if(!this._allowCrossOrigin&&!this.getOption("useFetchTransport")&&!e.isSameOrigin()&&!c("uriIsRelativePath")(e)){b&&j(0,45285);return this}this._setUserActionID();if(!a||e.isEmpty()){c("FBLogger")("async_request").mustfix("URI cannot be empty");return this}this.uri=d("ZeroRewrites").rewriteURI(e);return this};e.getURI=function(){return this.uri.toString()};e.delayPreDisplayJS=function(a){a===void 0&&(a=!0);this._delayPreDisplayJS=a;return this};e.setInitialHandler=function(a){this.initialHandler=a;return this};e.setPayloadHandler=function(a){this.setHandler(function(b){a(b.payload)});return this};e.setHandler=function(a){B(a)&&(this.handler=a);return this};e.setFlushedResponseHandler=function(a){B(a)&&(this.flushedResponseHandler=a);return this};e.getHandler=function(){return this.handler||c("emptyFunction")};e.setProgressHandler=function(a){B(a)&&(this.progressHandler=a);return this};e.setUploadProgressHandler=function(a){B(a)&&(this.uploadProgressHandler=a);return this};e.setErrorHandler=function(a){B(a)&&(this.errorHandler=a);return this};e.setTransportErrorHandler=function(a){this.transportErrorHandler=a;return this};e.getErrorHandler=function(){return this.errorHandler||c("emptyFunction")};e.getTransportErrorHandler=function(){return this.transportErrorHandler||c("emptyFunction")};e.setTimeoutHandler=function(a,b){B(b)&&(this.timeout=a,this.timeoutHandler=b);return this};e.resetTimeout=function(a){if(!(this.timeoutHandler===null))if(a===null)this.timeout=null,c("clearTimeout")(this.timer),this.timer=null;else{var b=!this._allowCrossPageTransition;this.timeout=a;c("clearTimeout")(this.timer);b?this.timer=c("setTimeout")(this._handleTimeout.bind(this),this.timeout):this.timer=c("setTimeoutAcrossTransitions")(this._handleTimeout.bind(this),this.timeout)}return this};e.setNewSerial=function(){this.id=++C;return this};e.setInterceptHandler=function(a){this.interceptHandler=a;return this};e.setFinallyHandler=function(a){this.finallyHandler=a;return this};e.setAbortHandler=function(a){this.abortHandler=a;return this};e.getServerDialogCancelHandler=function(){return this.serverDialogCancelHandler};e.setServerDialogCancelHandler=function(a){this.serverDialogCancelHandler=a;return this};e.setPreBootloadHandler=function(a){this.preBootloadHandler=a;return this};e.setReadOnly=function(a){typeof a!=="boolean"||(this.readOnly=a);return this};e.getReadOnly=function(){return this.readOnly};e.setRelativeTo=function(a){this.relativeTo=a;return this};e.getRelativeTo=function(){return this.relativeTo};e.setStatusClass=function(a){this.statusClass=a;return this};e.setStatusElement=function(a){this.statusElement=a;return this};e.getStatusElement=function(){return c("ge")(this.statusElement)};e._isRelevant=function(){if(this._allowCrossPageTransition)return!0;return!this.id?!0:this.id>D};e.clearStatusIndicator=function(){var a=this.getStatusElement();a&&(d("CSS").removeClass(a,"async_saving"),d("CSS").removeClass(a,this.statusClass))};e._addStatusIndicator=function(){var a=this.getStatusElement();a&&(d("CSS").addClass(a,"async_saving"),d("CSS").addClass(a,this.statusClass))};e.specifiesWriteRequiredParams=function(){var a=this;return this.writeRequiredParams.every(function(b){a.data[b]=a.data[b]||(o||(o=c("Env")))[b]||(c("ge")(b)||{}).value;return a.data[b]!==void 0?!0:!1})};e.setOption=function(a,b){typeof this.option[a]!=="undefined"&&(this.option[a]=b);return this};e.getOption=function(a){typeof this.option[a]==="undefined";return this.option[a]};e.abort=function(){var a=this;this.continuation.last(function(){var b=a.transport;if(b){var d=a.getTransportErrorHandler();a.setOption("suppressErrorAlerts",!0);a.setTransportErrorHandler(c("emptyFunction"));a._requestAborted=!0;b.abort();a.setTransportErrorHandler(d)}a.abortHandler();K.unschedule(a)})};e.abandon=function(){var a=this;this.continuation.last(function(){var b;c("clearTimeout")(a.timer);a.setOption("suppressErrorAlerts",!0).setHandler(b=c("emptyFunction")).setErrorHandler(b).setTransportErrorHandler(b).setProgressHandler(b).setUploadProgressHandler(b);b=a.transport;b&&(a._requestAborted=!0,x(b)&&delete b.onprogress,y(b)&&delete b.upload.onprogress,b.abort());a.abortHandler();K.unschedule(a)})};e.setNectarModuleDataSafe=function(a){var b=this.setNectarModuleData;b&&b.call(this,a);return this};e.setAllowCrossPageTransition=function(a){this._allowCrossPageTransition=!!a;this.timer&&this.resetTimeout(this.timeout);return this};e.getAllowIrrelevantRequests=function(){return this._allowIrrelevantRequests};e.setAllowIrrelevantRequests=function(a){this._allowIrrelevantRequests=a;return this};e.setAllowCrossOrigin=function(a){this._allowCrossOrigin=a;return this};e.setAllowCredentials=function(a){this._allowCredentials=a;return this};e.setIsBackgroundRequest=function(a){this._isBackgroundRequest=a;return this};e.setReplaceTransportMarkers=function(a){a===void 0&&(a=!0);this._shouldReplaceTransportMarkers=a;return this};e.sendAndReturnAbortHandler=function(){var a=this;this.send();return function(){return a.abort()}};e.send=function(b){var e=this;b=b||!1;if(!this.uri)return!1;this.errorHandler||!this.getOption("suppressErrorHandlerWarning");this.getOption("useFetchTransport")&&this.method!="GET"&&this.setMethod("GET");this.timeoutHandler!==null&&this.getOption("useFetchTransport");if(!this.getReadOnly()){this.specifiesWriteRequiredParams();if(this.method!="POST")return!1}if(document.location.search.toString().includes(this.uri.toString()))return!1;if(this.uri.toString().includes("/../")||this.uri.toString().includes("\\../")||this.uri.toString().includes("/..\\")||this.uri.toString().includes("\\..\\"))return!1;Object.assign(this.data,c("getAsyncParams")(this.method));(p||(p=c("isEmpty")))(this.context)||(Object.assign(this.data,this.context),this.data.ajax_log=1);(o||(o=c("Env"))).force_param&&Object.assign(this.data,(o||(o=c("Env"))).force_param);this._setUserActionID();if(this.getOption("bundle")&&this._isMultiplexable()){K.schedule(this);return!0}this.setNewSerial();this.getOption("asynchronous_DEPRECATED")||this.uri.addQueryData({__sjax:1});c("Arbiter").inform("AsyncRequest/send",{request:this,ts:(n||(n=c("performanceAbsoluteNow")))()});var f,g;this.method=="GET"&&this.uri.addQueryData({fb_dtsg_ag:d("DTSG_ASYNC").getToken()});d("AdsManagerConstURIUtils").shouldRouteToAMReadRegions(this.uri.toString())&&this.getReadOnly()&&this.method.toLowerCase()==="get"?this.uri.addQueryData({ads_manager_read_regions:!0}):d("AdsManagerConstURIUtils").shouldRouteToAMWriteRegions(this.uri.toString(),this.getReadOnly())&&this.uri.addQueryData({ads_manager_write_regions:!0});this.method=="GET"||this.rawData?(f=this.uri.addQueryData(this.data).toString(),g=this.rawData||""):(this._allowCrossOrigin&&this.uri.addQueryData({__a:1}),f=this.uri.toString(),g=(k||(k=d("PHPQuerySerializer"))).serialize(this.data));if(this.transport)return!1;if(this.getOption("useFetchTransport"))try{var h=new(c("FetchStreamTransport"))(this.uri);this.$1(h);this._markRequestSent();h.send();return!0}catch(a){this.setOption("useFetchTransport",!1)}this.flushedResponseHandler&&(this.flushedResponseTextParseIndex=0);var i;try{i=d("ZeroRewrites").getTransportBuilderForURI(this.uri)()}catch(a){throw c("unrecoverableViolation")(a.message,"comet_infra",{},{blameToPreviousFrame:1})}if(!i)return!1;this.schedule("AsyncRequest.send");i.onreadystatechange=function(){var a=e.transport;a&&a.readyState>=2&&a.readyState<=3&&e._handleFlushedResponse();i.readyState===4&&e.continuation.last(e._onStateChange)};this.progressHandler&&x(i)&&(i.onprogress=function(){for(var a=arguments.length,b=new Array(a),c=0;c<a;c++)b[c]=arguments[c];e.continuation(function(){e.progressHandler&&e.progressHandler.apply(e,b)})});this.uploadProgressHandler&&y(i)&&(i.upload.onprogress=function(){for(var a=arguments.length,b=new Array(a),c=0;c<a;c++)b[c]=arguments[c];e.continuation(function(){e.uploadProgressHandler&&e.uploadProgressHandler.apply(e,b)})});b||(this.remainingRetries=this.getOption("retries"));this.transport=i;try{i.open(this.method,f,c("gkx")("25571")?!0:this.getOption("asynchronous_DEPRECATED"))}catch(a){return!1}if(!this.uri.isSameOrigin()&&!c("uriIsRelativePath")(this.uri)&&!this.getOption("useFetchTransport")){if(!z(i))return!1;this._canSendCredentials()&&(i.withCredentials=!0)}this.method=="POST"&&!this.rawData&&i.setRequestHeader("Content-Type","application/x-www-form-urlencoded");this._isBackgroundRequest&&i.setRequestHeader("X-FB-BACKGROUND-STATE","1");var j=c("getAsyncHeaders")(this.uri);Object.keys(j).forEach(function(a){i&&i.setRequestHeader(a,j[a])});c("Arbiter").inform("AsyncRequest/will_send",{request:this});if(i)for(h in this.headers)Object.prototype.hasOwnProperty.call(this.headers,h)&&i.setRequestHeader(h,this.headers[h]);this._addStatusIndicator();this._markRequestSent();i.send(g);this.timeout!==null&&this.resetTimeout(this.timeout);a._inflightCount++;return!0};e.schedule=function(a){this.continuation=c("TimeSlice").getReusableContinuation(a)};e._canSendCredentials=function(){if(this._allowCredentials===!1)return!1;var a=new(m||(m=c("URI")))(this.uri);return c("isFacebookURI")(a)||c("isInternalFBURI")(a)||c("isMessengerDotComURI")(a)||c("isWorkplaceDotComURI")(a)||c("isWorkroomsDotComURI")(a)||c("isWorkDotMetaDotComURI")(a)||c("isHorizonDotMetaDotComURI")(a)||c("isSparkDotMetaDotComURI")(a)||c("isArDotMetaDotComURI")(a)};e._markRequestSent=function(){var a=new(m||(m=c("URI")))(this.getURI()).getQualifiedURI().toString();d("ResourceTimingsStore").updateURI(c("ResourceTypes").XHR,this.resourceTimingStoreUID,a);d("ResourceTimingsStore").annotate(c("ResourceTypes").XHR,this.resourceTimingStoreUID).addStringAnnotation("uri",a);d("ResourceTimingsStore").measureRequestSent(c("ResourceTypes").XHR,this.resourceTimingStoreUID)};e.promisePayload=function(a){return this.exec().then(function(a){return a.payload},function(a){throw a.toError()})};e.exec=function(a){var d=this;if(this.getHandler()!==c("emptyFunction")||this.getErrorHandler()!==c("AsyncResponse").defaultErrorHandler)throw new Error("exec is an async function and does not allow previously set handlers");return new(l||(l=b("Promise")))(function(b,c){d.setHandler(b).setErrorHandler(c).send(a)})};a.bootstrap=function(b,e,f){var g="GET",h=!0,i={};(f||e&&e.rel=="async-post")&&(g="POST",h=!1,b&&(b=new(m||(m=c("URI")))(b),i=b.getQueryData(),b.setQueryData({})));f=d("Parent").byClass(e,"stat_elem")||e;if(f&&d("CSS").hasClass(f,"async_saving"))return!1;b=new a(b).setReadOnly(h).setMethod(g).setData(i).setNectarModuleDataSafe(e).setRelativeTo(e);e&&(b.setHandler(function(a){c("Event").fire(e,"success",{response:a})}),b.setErrorHandler(function(a){c("Event").fire(e,"error",{response:a})!==!1&&c("AsyncResponse").defaultErrorHandler(a)}));if(f instanceof HTMLElement){b.setStatusElement(f);h=f.getAttribute("data-status-class");h&&b.setStatusClass(h)}b.send();return!1};a.bootstrap_UNSAFE_LET_ANYONE_IMPERSONATE_THE_USER_FOR_THESE_WRITES=function(b,c,d){a.bootstrap(b,c,d)};a.post=function(b,c){new a(b).setReadOnly(!1).setMethod("POST").setData(c).send();return!1};a.post_UNSAFE_LET_ANYONE_IMPERSONATE_THE_USER_FOR_THESE_WRITES=function(b,c){a.post(b,c)};a.getLastID=function(){return C};a.ignoreUpdate=function(){E=!0};a.getInflightCount=function(){return this._inflightCount};return a}();H._inflightCount=0;var I,J=[],K=function(){function a(){this._requests=[]}var b=a.prototype;b.add=function(a){this._requests.push(a)};b.remove=function(a){var b=this._requests,c=this._requestsSent;for(var d=0,e=b.length;d<e;d++)b[d]===a&&(c?b[d]=null:b.splice(d,1))};b.send=function(){this._requestsSent&&j(0,4390);this._requestsSent=!0;this._wrapperRequest=null;var a=this._requests;if(!a.length)return;var b;if(a.length===1)b=a[0];else{a=a.filter(Boolean).map(function(a){return[a.uri.getPath(),(k||(k=d("PHPQuerySerializer"))).serialize(a.data)]});b=this._wrapperRequest=new H("/ajax/proxy.php").setAllowCrossPageTransition(!0).setData({data:a}).setHandler(this._handler.bind(this)).setTransportErrorHandler(this._transportErrorHandler.bind(this))}b&&b.setOption("bundle",!1).send()};b._handler=function(a){var b=this,c=a.getPayload().responses;if(c.length!==this._requests.length)return;a=function(a){var d=b._requests[a];if(!d)return"continue";var e=d.uri.getPath();b._wrapperRequest&&(d.id=b._wrapperRequest.id);if(c[a][0]!==e){d.continuation.last(function(){d.invokeResponseHandler({transportError:"Wrong response order in bundled request to "+e})});return"continue"}d.continuation.last(function(){d.handleResponse(c[a][1])})};for(var d=0;d<this._requests.length;d++){var e=a(d);if(e==="continue")continue}J.splice(J.indexOf(this,1))};b._transportErrorHandler=function(a){var b=this,c={transportError:a.errorDescription};a=this._requests.filter(Boolean).map(function(a){b._wrapperRequest&&(a.id=b._wrapperRequest.id);a.invokeResponseHandler(c);return a.uri.getPath()})};a.schedule=function(b){b.schedule("AsyncMultiplex.schedule");I||(I=new a(),J.push(I),c("TimeSlice").guard(function(){c("setTimeoutAcrossTransitions")(function(){I&&(I.send(),I=null)},0)},"AsyncMultiplex.schedule",{propagationType:c("TimeSlice").PropagationType.ORPHAN})());I.add(b);return I};a.unschedule=function(a){J.forEach(function(b){b.remove(a)})};return a}();H.suppressOnloadToken={};a.AsyncRequest=H;g["default"]=H}),226);
__d("ClientServiceWorkerMessage",[],(function(a,b,c,d,e,f){a=function(){function a(a,b,c){this.$1=a,this.$2=b,this.$3=c}var b=a.prototype;b.sendViaController=function(){if(!navigator.serviceWorker||!navigator.serviceWorker.controller)return;var a=new self.MessageChannel();this.$3&&(a.port1.onmessage=this.$3);navigator.serviceWorker.controller.postMessage({command:this.$1,data:this.$2},[a.port2])};return a}();f["default"]=a}),66);
__d("goURIWWW",["URI"],(function(a,b,c,d,e,f,g){"use strict";var h;g["default"]=(h||c("URI")).go}),98);