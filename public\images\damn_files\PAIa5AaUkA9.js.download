;/*FB_PKG_DELIM*/

__d("oz-player/shims/ozReportUnexpectedError",["oz-player/shims/www/ozReportUnexpectedErrorWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/ozReportUnexpectedErrorWWW")}),98);
__d("oz-player/loggings/OzLoggingUtils",["oz-player/shims/ozReportUnexpectedError"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c){a=a.getOperationLogger(b).start();try{return c(a)}catch(b){a.setError(b);throw b}finally{a.log()}}function b(a,b,d,e,f){e===void 0&&(e=function(){});f===void 0&&(f=function(){});var g=b.getOperationLogger(d).start();e(g);a.then(function(a){f(g),g.log()},function(a){f(g),g.setError(a),g.log()})["catch"](function(a){c("oz-player/shims/ozReportUnexpectedError")(a,d+" logger")})}g.executeOperationAndLog=a;g.monitorPromiseAndLogOperation=b}),98);
__d("oz-player/loggings/OzOperationLoggerBase",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a){this.$52=new Map(),this.$1=a}var b=a.prototype;b.start=function(){var a=Date.now();this.setClientTimeBegin(a);this.setClientTimeEnd(null);this.setClientTimeDuration(null);return this};b.log=function(){var a=Date.now(),b=this.getClientTimeBegin(),c=this.getClientTimeEnd();(b==null||b===0)&&(b=a,this.setClientTimeBegin(b));(c==null||c===0)&&(c=a,this.setClientTimeEnd(c));this.setClientTimeDuration(c-b)};b.setError=function(a){this.$2=a;return this};b.setPerSessionSampleRate=function(a){this.$3=a;return this};b.setAppendTarget=function(a){this.$47=a;return this};b.setOneObserved=function(a){this.$44=a;return this};b.setOneReqWave=function(a){this.$45=a;return this};b.setOneResWave=function(a){this.$46=a;return this};b.setIsP2pPlayback=function(a){this.$43=a;return this};b.setResult=function(a){this.$4=a;return this};b.setType=function(a){this.$5=a;return this};b.setClientTimeBegin=function(a){this.$6=a;return this};b.setClientTimeDuration=function(a){this.$7=a;return this};b.setClientTimeEnd=function(a){this.$8=a;return this};b.setSegmentCount=function(a){this.$15=a;return this};b.setTimeToFirstByte=function(a){this.$9=a;return this};b.setTimeToLastByte=function(a){this.$10=a;return this};b.setTimeToRequestStart=function(a){this.$11=a;return this};b.setTimeToRequestSent=function(a){this.$12=a;return this};b.setReason=function(a){this.$13=a;return this};b.setResource=function(a){this.$14=a;return this};b.setSegmentStartTime=function(a){this.$16=a;return this};b.setSegmentEndTime=function(a){this.$17=a;return this};b.setLength=function(a){this.$18=a;return this};b.setLiveheadPosition=function(a){this.$19=a;return this};b.setLiveheadSeqNumHeader=function(a){this.$20=a;return this};b.setLiveheadSeqNumMpd=function(a){this.$21=a;return this};b.setManifestType=function(a){this.$22=a;return this};b.setMediaSourceNewDuration=function(a){this.$23=a;return this};b.setMediaSourcePreviousDuration=function(a){this.$24=a;return this};b.setPriorityFloat=function(a){this.$25=a;return this};b.setAppendedBufferMs=function(a){this.$26=a;return this};b.setInitiator=function(a){this.$27=a;return this};b.setPreloadTime=function(a){this.$28=a;return this};b.setConcluder=function(a){this.$29=a;return this};b.setPreviousRepresentationID=function(a){this.$30=a;return this};b.setRepresentationID=function(a){this.$31=a;return this};b.setStreamSwitchReason=function(a){this.$32=a;return this};b.setState=function(a){this.$33=a;return this};b.setContentLengthHeader=function(a){this.$34=a;return this};b.setOriginHitHeader=function(a){this.$36=a;return this};b.setEdgeHitHeader=function(a){this.$37=a;return this};b.setFNAHitHeader=function(a){this.$38=a;return this};b.setCode=function(a){this.$35=a;return this};b.setResponseTimeMsHeader=function(a){this.$39=a;return this};b.setIsTemplatedManifest=function(a){this.$40=a;return this};b.setIsLatencyCachupEnabled=function(a){this.$41=a;return this};b.setPlayerFormat=function(a){this.$42=a;return this};b.setIsRingBufferSample=function(a){this.$48=a;return this};b.setIsOnline=function(a){this.$49=a;return this};b.setProxyStatusHeader=function(a){this.$50=a;return this};b.setPlaybackFbmsParam=function(a){return this};b.setPreferredEdgeLatency=function(a){this.$51=a;return this};b.setUserInfo=function(a){return this};b.setDynamicStatusHeader=function(a){return this};b.setIsMixedCodecManifest=function(a){this.$53=a;return this};b.getPerSessionSampleRate=function(){return this.$3};b.setMediaSourceSourceBuffer=function(a,b){this.$52.set(a,b);return this};b.unsetMediaSourceSourceBuffers=function(){this.$52.clear();return this};b.getError=function(){return this.$2};b.getResult=function(){return this.$4};b.getType=function(){return this.$5};b.getClientTimeBegin=function(){return this.$6};b.getClientTimeEnd=function(){return this.$7!=null?this.$6!=null?this.$6+this.$7:null:this.$8};b.getTimeToRequestStart=function(){return this.$11};b.getTimeToRequestSent=function(){return this.$12};b.getReason=function(){return this.$13};b.getResource=function(){return this.$14};b.getOperationName=function(){return this.$1};b.getSegmentStartTime=function(){return this.$16};b.getSegmentEndTime=function(){return this.$17};b.getLength=function(){return this.$18};b.getLiveheadPosition=function(){return this.$19};b.getManifestType=function(){return this.$22};b.getIsMixedCodecManifest=function(){return this.$53};b.getPriorityFloat=function(){return this.$25};b.getAppendedBufferMs=function(){return this.$26};b.getInitiator=function(){return this.$27};b.getPreloadTime=function(){return this.$28};b.getConcluder=function(){return this.$29};b.getContentLengthHeader=function(){return this.$34};b.getOriginHitHeader=function(){return this.$36};b.getEdgeHitHeader=function(){return this.$37};b.getFNAHitHeader=function(){return this.$38};b.getCode=function(){return this.$35};b.getResponseTimeMsHeader=function(){return this.$39};b.getIsTemplatedManifest=function(){return this.$40};b.getOneObserved=function(){return this.$44};b.getOneReqWave=function(){return this.$45};b.getOneResWave=function(){return this.$46};b.getAppendTarget=function(){return this.$47};b.getIsRingBufferSample=function(){return this.$48};b.getIsOnline=function(){return this.$49};b.getProxyStatusHeader=function(){return this.$50};b.getMediaSourceSourceBuffers=function(){return this.$52};return a}();f["default"]=a}),66);
__d("oz-player/shims/www/OzMaybeNativePromiseWWW",["cr:3014"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=b("cr:3014")}),98);
__d("oz-player/shims/OzMaybeNativePromise",["oz-player/shims/www/OzMaybeNativePromiseWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/OzMaybeNativePromiseWWW")}),98);
__d("oz-player/utils/OzErrorEmitter",["oz-player/shims/OzEventEmitter","oz-player/shims/ozvariant"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var d;b===void 0&&(b=!1);d=a.call(this)||this;d.$OzErrorEmitter$p_1=!1;d.$OzErrorEmitter$p_2=!1;d.$OzErrorEmitter$p_3=[];d.emitError=function(a){!d.$OzErrorEmitter$p_1?d.$OzErrorEmitter$p_2?d.$OzErrorEmitter$p_3.push(a):d.$OzErrorEmitter$p_1||c("oz-player/shims/ozvariant")(0,14038):d.emit("error",a)};d.$OzErrorEmitter$p_2=b;return d}var d=b.prototype;d.onError=function(a){this.$OzErrorEmitter$p_1=!0;a=this.addListener("error",a);this.$OzErrorEmitter$p_2&&this.$OzErrorEmitter$p_3.length>0&&this.$OzErrorEmitter$p_3.forEach(this.emitError);return a};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/drm/OzDrmManager",["oz-player/drm/OzDrmUtils","oz-player/loggings/OzLoggingUtils","oz-player/loggings/OzOperationLoggerBase","oz-player/shims/OzDOMEventListener","oz-player/shims/OzMaybeNativePromise","oz-player/shims/OzSubscriptionsHandler","oz-player/utils/OzErrorEmitter","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,e){var f=this;e===void 0&&(e=null);this.$1=null;this.$2=new Map();this.$4=!1;this.$6=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$7=new(c("oz-player/utils/OzErrorEmitter"))();this.$8=!1;this.$9=null;this.$10=0;this.$11=[];this.$12=[];this.$22=function(a,b){a instanceof c("oz-player/loggings/OzOperationLoggerBase")&&a.getError()&&a.setResult("failed"),b!=null&&a.setReason(b)};this.$19=function(a){if(f.$4)return c("oz-player/shims/OzMaybeNativePromise").reject(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DRM_MANAGER",description:"OzDrmManager destroyed before call to mediaKeySystemAccess.createMediaKeys()."}));var b=f.$5.mediaKeys!=null?c("oz-player/shims/OzMaybeNativePromise").resolve(f.$5.mediaKeys):a.createMediaKeys();b=b.then(function(b){return{mediaKeySystemAccess:a,mediaKeys:b}});d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(b,f.$3,"drm_create_media_keys",function(){},f.$22);return b};this.$20=function(a){if(f.$4)return c("oz-player/shims/OzMaybeNativePromise").reject(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DRM_MANAGER",description:"OzDrmManager destroyed before call to setMediaKeys()"}));var b=a.mediaKeySystemAccess,e=a.mediaKeys;if(!e)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DRM_MANAGER",description:"No mediaKeys for mediaKeySystemAccess"});a=f.$5.setMediaKeys(e).then(function(){if(f.$4)return c("oz-player/shims/OzMaybeNativePromise").reject(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DRM_MANAGER",description:"OzDrmManager destroyed after setMediaKeys() called."}));var a=Array.from(f.$2.values()).find(function(a){return a.getKeySystem()===b.keySystem});if(!a)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DRM_MANAGER",description:"Can't find OzDrmProvider for keySystem "+b.keySystem});var g=a.getServerCertificate();return g?e.setServerCertificate(g).then(function(){return{mediaKeys:e,provider:a}}):{mediaKeys:e,provider:a}});d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(a,f.$3,"drm_set_media_keys",function(){},f.$22);return a};this.$21=function(a){var b=a.mediaKeys,e=a.provider;a=e.getInitDatas();if(a.length===0){f.$6.addSubscriptions(c("oz-player/shims/OzDOMEventListener").listenDOMEvent(f.$5,"encrypted",function(a){f.$23(b,e,a)["catch"](function(a){f.$14(d("oz-player/utils/OzErrorUtils").convertPromiseRejectionReasonToOzError(a,{type:"OZ_DRM_MANAGER",description:"DRM encrypted rejection"}))})}));return c("oz-player/shims/OzMaybeNativePromise").resolve([])}a=a.map(function(a){return f.$23(b,e,{initData:a.data,initDataType:a.type})});a=c("oz-player/shims/OzMaybeNativePromise").all(a);d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(a,f.$3,"drm_request_license",function(){},f.$22);return a};this.$5=b;this.$1=e;this.$3=a.cloneContext().setType("drm_manager")}var b=a.prototype;b.hasContentProtections=function(){return this.$9};b.onError=function(a){return this.$7.onError(a)};b.$13=function(a){this.$7.emitError(a)};b.$14=function(a){var b;b=(b=(b=this.$1)==null?void 0:b.maxStartEMEAttempts)!=null?b:-1;this.$10<b?this.$15():this.$13(a)};b.addProvider=function(a){this.$2.set(a.getSchemeId(),a)};b.getProviderForSchemeId=function(a){return this.$2.get(a)};b.$16=function(a){if(!a)return null;var b=[];for(var c=0;c<a.length;c++){var e=a[c],f=e.$.schemeIdUri;f=this.getProviderForSchemeId(f);if(f){var g=f.getInitDatas();if(g.length==0&&e["cenc:pssh"]&&e["cenc:pssh"].length===1){e=(e=e["cenc:pssh"][0]._)!=null?e:"";e=d("oz-player/drm/OzDrmUtils").base64ToUint8Array(e.replace(/-/g,"+").replace(/_/g,"/"));g=[{data:e,type:"cenc"}];f.setInitDatas(g)}b.push({provider:f,initDatas:g})}}return b};b.$15=function(){try{this.$8=!1,this.startEME(this.$11,this.$12)}catch(b){var a=b;this.$13(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DRM_MANAGER",description:"Restart EME failed with: "+String(a),extra:{}}))}};b.startEME=function(a,b){var c=this;if(this.$8)return;this.$8=!0;this.$10+=1;this.$11=a;this.$12=b;var e=new Map();this.$17(b,e,"audio");this.$17(a,e,"video");var f;e.forEach(function(a,b){!f?f=c.$18(b,a):f=f["catch"](function(){return c.$18(b,a)})});if(!f){this.$9=!1;return}this.$9=!0;var g=this.$3.getOperationLogger("drm_setup").start();f.then(this.$19).then(this.$20).then(this.$21).then(function(){return g.log()},function(a){a=d("oz-player/utils/OzErrorUtils").convertPromiseRejectionReasonToOzError(a,{type:"OZ_DRM_MANAGER",description:"DRM keyPromiseChain rejection"});c.$13(a);g.setResult("failed").setError(a).log()})};b.$17=function(a,b,c){var d=this;a.forEach(function(a){var e=d.$16(a.getContentProtectionXml()),f=a.getMimeCodecs();e!=null&&e.forEach(function(a){a=a.provider;var d=a.getKeySystem(),e=b.get(d);e||(e={audioCapabilities:new Map(),videoCapabilities:new Map(),distinctiveIdentifier:a.getRequireDistinctiveIdentifier(),persistentState:a.getRequirePersistentState(),sessionTypes:a.getDrmSessionTypes(),initDataTypes:a.getInitDataTypes()},b.set(d,e));c==="audio"&&!e.audioCapabilities.has(f)&&e.audioCapabilities.set(f,{contentType:f,robustness:a.getAudioRobustness()});c==="video"&&!e.videoCapabilities.has(f)&&e.videoCapabilities.set(f,{contentType:f,robustness:a.getVideoRobustness()})})})};b.destroy=function(){this.$6.release(),this.$4=!0};b.$18=function(a,b){var e=this,f={audioCapabilities:Array.from(b.audioCapabilities.values()),videoCapabilities:Array.from(b.videoCapabilities.values()),distinctiveIdentifier:b.distinctiveIdentifier,persistentState:b.persistentState,sessionTypes:b.sessionTypes,initDataTypes:b.initDataTypes};b=window.navigator.requestMediaKeySystemAccess(a,[f]);d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(b,this.$3,"drm_request_media_key_system_access",function(){},function(b){var d=null;b instanceof c("oz-player/loggings/OzOperationLoggerBase")&&b.getError()&&(d="keySystem: "+a+";domConfig: "+JSON.stringify(f));return e.$22(b,d)});return b};b.$23=function(a,b,e){var f=this,g=a.createSession();this.$6.addSubscriptions(c("oz-player/shims/OzDOMEventListener").listenDOMEvent(g,"message",function(a){f.$24(g,b,a)["catch"](function(a){f.$14(d("oz-player/utils/OzErrorUtils").convertPromiseRejectionReasonToOzError(a,{type:"OZ_DRM_MANAGER",description:"DRM message rejection"}))})}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(g,"keystatuseschange",function(a){f.$25(g)["catch"](function(a){f.$14(d("oz-player/utils/OzErrorUtils").convertPromiseRejectionReasonToOzError(a,{type:"OZ_DRM_MANAGER",description:"DRM keystatuseschange rejection"}))})}));return g.generateRequest(e.initDataType,e.initData)};b.$24=function(a,b,c){var e=this;c=b.getLicenseRequestInfo(c.message.slice(0));var f=this.$3.getOperationLogger("drm_fetch_license").setResource(c.url).start();return window.fetch(c.url,c).then(function(c){c.ok?f.setResult("success").setCode(c.status).log():f.setResult("failed").setCode(c.status).setError(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DRM_MANAGER",description:"DRM license fetch HTTP status not OK ("+c.status+")",extra:{code:c.status.toString()}})).log();return c.arrayBuffer().then(function(f){var g=new Uint8Array(f);g=b.parseLicenseResponse(g);if(g==null||g.byteLength===0){var h=g==null?"License is null or undefined.":"License is 0 bytes.";!c.ok?h+=" HTTP status not OK ("+c.status+")":f.byteLength===0&&(h+=" HTTP status OK ("+c.status+"), but response body is 0 bytes");f=d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DRM_MANAGER",description:"No license for "+b.getKeySystem()+" - "+h,extra:{code:c.status.toString(),headers:c.headers,url:c.url}});h=(h=(h=e.$1)==null?void 0:h.throwNoLicenseError)!=null?h:!1;if(h)throw f;else e.$13(f)}else a.update(g)})})};b.$25=function(a){return a.expiration<Date.now()?a.close():c("oz-player/shims/OzMaybeNativePromise").resolve()};return a}();g.OzDrmManager=a}),98);
__d("oz-player/loggings/OzPerfLoggerProviderBase",["oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(){}var b=a.prototype;b.cloneContext=function(){var a=this.createLoggerProvider();this.$11(a);return a};b.setType=function(a){this.$1=a;return this};b.setInitiator=function(a){this.$2=a;return this};b.setResource=function(a){this.$4=a;return this};b.setRepresentationID=function(a){this.$3=a;return this};b.setSegmentStartTime=function(a){this.$5=a;return this};b.setSegmentEndTime=function(a){this.$6=a;return this};b.setStreamSwitchReason=function(a){this.$7=a;return this};b.setIsP2pPlayback=function(a){this.$8=a;return this};b.setIsRingBufferSample=function(a){this.$9=a;return this};b.setPerSessionSampleRate=function(a){this.$10=a;return this};b.getType=function(){return this.$1};b.getInitiator=function(){return this.$2};b.getRepresentationID=function(){return this.$3};b.getStreamSwitchReason=function(){return this.$7};b.getResource=function(){return this.$4};b.getSegmentStartTime=function(){return this.$5};b.getSegmentEndTime=function(){return this.$6};b.getIsP2pPlayback=function(){return this.$8};b.getIsRingBufferSample=function(){return this.$9};b.getOperationLogger=function(a){a=this.createOperationLogger(a);return this.setOperationContext(a)};b.setOperationContext=function(a){this.$11(a);return a};b.createOperationLogger=function(a){throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_NOT_IMPLEMENTED_ERROR",description:"Not implemented: createOperationLogger"})};b.createLoggerProvider=function(){throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_NOT_IMPLEMENTED_ERROR",description:"Not implemented: createLoggerProvider"})};b.$11=function(a){this.getType()&&a.setType(this.getType()),this.getInitiator()&&a.setInitiator(this.getInitiator()),this.getResource()&&a.setResource(this.getResource()),this.getRepresentationID()&&a.setRepresentationID(this.getRepresentationID()),this.getStreamSwitchReason()&&a.setStreamSwitchReason(this.getStreamSwitchReason()),typeof this.getSegmentStartTime()==="number"&&a.setSegmentStartTime(this.getSegmentStartTime()),typeof this.getSegmentEndTime()==="number"&&a.setSegmentEndTime(this.getSegmentEndTime()),typeof this.getIsP2pPlayback()==="boolean"&&a.setIsP2pPlayback(this.getIsP2pPlayback()),typeof this.getIsRingBufferSample()==="boolean"&&a.setIsRingBufferSample(this.getIsRingBufferSample())};return a}();g["default"]=a}),98);
__d("oz-player/loggings/OzDevConsolePerfLogger",["oz-player/loggings/OzOperationLoggerBase","oz-player/loggings/OzPerfLoggerProviderBase"],(function(a,b,c,d,e,f,g){"use strict";function h(){var a;for(var b=arguments.length,c=new Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=console).debug.apply(a,["[oz]"].concat(c))}function i(){var a;for(var b=arguments.length,c=new Array(b),d=0;d<b;d++)c[d]=arguments[d];(a=console).error.apply(a,["[oz]"].concat(c))}function j(a,b){return Math.floor(a).toString().padStart(b,"0")}function k(a,b){b===void 0&&(b=null);var c=function(a){return j(a.getHours(),2)+":"+j(a.getMinutes(),2)+":"+j(a.getSeconds(),2)+" "+j(a.getMilliseconds(),3)},d=b?b.getTime()-a.getTime():0;return"["+c(a)+(b?" - "+c(b):"")+" ("+d+" ms)]"}function l(a){return a==="failed"?i:h}function m(a){return a==null||a===0?null:a.toFixed(2)}a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}var c=b.prototype;c.createOperationLogger=function(a){return new n(a)};c.createLoggerProvider=function(){return new b()};return b}(c("oz-player/loggings/OzPerfLoggerProviderBase"));var n=function(b){babelHelpers.inheritsLoose(a,b);function a(){return b.apply(this,arguments)||this}var c=a.prototype;c.log=function(){b.prototype.log.call(this);var a=this.getClientTimeBegin()||0,c=this.getClientTimeEnd();c=c!=null&&c!==0?new Date(c):null;var d=this.getResult()||"success",e=this.getType(),f=this.getInitiator(),g=this.getResource(),h=m(this.getSegmentStartTime()),i=m(this.getSegmentEndTime());l(d)("["+d+"]",this.getOperationName()+": "+k(new Date(a),c),e!=null&&e!==""?"["+e+"]":"",h!=null&&h!==""&&i!=null&&i!==""?"[segment time range: "+h+" - "+i+"]":"",f||"",g||"")};return a}(c("oz-player/loggings/OzOperationLoggerBase"));g.OzDevConsolePerfLoggerProvider=a;g.OzDevConsoleOperationLogger=n}),98);
__d("oz-player/loggings/OzMultiDestinationPerfLogger",["oz-player/loggings/OzOperationLoggerBase","oz-player/loggings/OzPerfLoggerProviderBase"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var c;c=a.call(this)||this;c.$OzMultiDestinationPerfLoggerProvider$p_1=b;return c}var c=b.prototype;c.createOperationLogger=function(a){return new h(a,this.$OzMultiDestinationPerfLoggerProvider$p_1.map(function(b){return b.getOperationLogger(a)}))};c.createLoggerProvider=function(){return new b(this.$OzMultiDestinationPerfLoggerProvider$p_1)};return b}(c("oz-player/loggings/OzPerfLoggerProviderBase"));var h=function(b){babelHelpers.inheritsLoose(a,b);function a(a,c){a=b.call(this,a)||this;a.$OzMultiDestinationOperationLogger$p_1=c;return a}var c=a.prototype;c.start=function(){b.prototype.start.call(this);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(a){return a.start()});return this};c.setPerSessionSampleRate=function(a){b.prototype.setPerSessionSampleRate.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setPerSessionSampleRate(a)});return this};c.setResult=function(a){b.prototype.setResult.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setResult(a)});return this};c.setError=function(a){b.prototype.setError.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setError(a)});return this};c.setType=function(a){b.prototype.setType.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setType(a)});return this};c.setMediaSourcePreviousDuration=function(a){b.prototype.setMediaSourcePreviousDuration.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setMediaSourcePreviousDuration(a)});return this};c.setMediaSourceNewDuration=function(a){b.prototype.setMediaSourceNewDuration.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setMediaSourceNewDuration(a)});return this};c.setClientTimeBegin=function(a){b.prototype.setClientTimeBegin.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setClientTimeBegin(a)});return this};c.setClientTimeDuration=function(a){b.prototype.setClientTimeDuration.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setClientTimeDuration(a)});return this};c.setClientTimeEnd=function(a){b.prototype.setClientTimeEnd.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setClientTimeEnd(a)});return this};c.setSegmentCount=function(a){b.prototype.setSegmentCount.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setSegmentCount(a)});return this};c.setTimeToFirstByte=function(a){b.prototype.setTimeToFirstByte.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setTimeToFirstByte(a)});return this};c.setTimeToLastByte=function(a){b.prototype.setTimeToLastByte.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setTimeToLastByte(a)});return this};c.setTimeToRequestStart=function(a){b.prototype.setTimeToRequestStart.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setTimeToRequestStart(a)});return this};c.setTimeToRequestSent=function(a){b.prototype.setTimeToRequestSent.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setTimeToRequestSent(a)});return this};c.setReason=function(a){b.prototype.setReason.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setReason(a)});return this};c.setResource=function(a){b.prototype.setResource.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setResource(a)});return this};c.setSegmentStartTime=function(a){b.prototype.setSegmentStartTime.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setSegmentStartTime(a)});return this};c.setSegmentEndTime=function(a){b.prototype.setSegmentEndTime.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setSegmentEndTime(a)});return this};c.setAppendedBufferMs=function(a){b.prototype.setAppendedBufferMs.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setAppendedBufferMs(a)});return this};c.setLength=function(a){b.prototype.setLength.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setLength(a)});return this};c.setLiveheadPosition=function(a){b.prototype.setLiveheadPosition.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setLiveheadPosition(a)});return this};c.setLiveheadSeqNumHeader=function(a){b.prototype.setLiveheadSeqNumHeader.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setLiveheadSeqNumHeader(a)});return this};c.setLiveheadSeqNumMpd=function(a){b.prototype.setLiveheadSeqNumMpd.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setLiveheadSeqNumMpd(a)});return this};c.setManifestType=function(a){b.prototype.setManifestType.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setManifestType(a)});return this};c.setPriorityFloat=function(a){b.prototype.setPriorityFloat.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setPriorityFloat(a)});return this};c.setInitiator=function(a){b.prototype.setInitiator.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setInitiator(a)});return this};c.setPreloadTime=function(a){b.prototype.setPreloadTime.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setPreloadTime(a)});return this};c.setConcluder=function(a){b.prototype.setConcluder.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setConcluder(a)});return this};c.setPreviousRepresentationID=function(a){b.prototype.setPreviousRepresentationID.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setPreviousRepresentationID(a)});return this};c.setRepresentationID=function(a){b.prototype.setRepresentationID.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setRepresentationID(a)});return this};c.setState=function(a){b.prototype.setState.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setState(a)});return this};c.setContentLengthHeader=function(a){b.prototype.setContentLengthHeader.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setContentLengthHeader(a)});return this};c.setEdgeHitHeader=function(a){b.prototype.setEdgeHitHeader.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setEdgeHitHeader(a)});return this};c.setFNAHitHeader=function(a){b.prototype.setFNAHitHeader.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setFNAHitHeader(a)});return this};c.setOriginHitHeader=function(a){b.prototype.setOriginHitHeader.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setOriginHitHeader(a)});return this};c.setCode=function(a){b.prototype.setCode.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setCode(a)});return this};c.setResponseTimeMsHeader=function(a){b.prototype.setResponseTimeMsHeader.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setResponseTimeMsHeader(a)});return this};c.setIsTemplatedManifest=function(a){b.prototype.setIsTemplatedManifest.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setIsTemplatedManifest(a)});return this};c.setIsLatencyCachupEnabled=function(a){b.prototype.setIsLatencyCachupEnabled.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setIsLatencyCachupEnabled(a)});return this};c.setPlayerFormat=function(a){b.prototype.setPlayerFormat.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setPlayerFormat(a)});return this};c.setIsP2pPlayback=function(a){b.prototype.setIsP2pPlayback.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setIsP2pPlayback(a)});return this};c.setOneObserved=function(a){b.prototype.setOneObserved.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setOneObserved(a)});return this};c.setOneReqWave=function(a){b.prototype.setOneReqWave.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setOneReqWave(a)});return this};c.setOneResWave=function(a){b.prototype.setOneResWave.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setOneResWave(a)});return this};c.setAppendTarget=function(a){b.prototype.setAppendTarget.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setAppendTarget(a)});return this};c.setIsRingBufferSample=function(a){b.prototype.setIsRingBufferSample.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setIsRingBufferSample(a)});return this};c.setIsOnline=function(a){b.prototype.setIsOnline.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setIsOnline(a)});return this};c.setIsMixedCodecManifest=function(a){b.prototype.setIsMixedCodecManifest.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setIsMixedCodecManifest(a)});return this};c.setProxyStatusHeader=function(a){b.prototype.setProxyStatusHeader.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setProxyStatusHeader(a)});return this};c.setPlaybackFbmsParam=function(a){b.prototype.setPlaybackFbmsParam.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setPlaybackFbmsParam(a)});return this};c.setPreferredEdgeLatency=function(a){b.prototype.setPreferredEdgeLatency.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setPreferredEdgeLatency(a)});return this};c.setUserInfo=function(a){b.prototype.setUserInfo.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setUserInfo(a)});return this};c.setDynamicStatusHeader=function(a){b.prototype.setDynamicStatusHeader.call(this,a);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setDynamicStatusHeader(a)});return this};c.setMediaSourceSourceBuffer=function(a,c){b.prototype.setMediaSourceSourceBuffer.call(this,a,c);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(b){return b.setMediaSourceSourceBuffer(a,c)});return this};c.unsetMediaSourceSourceBuffers=function(){b.prototype.unsetMediaSourceSourceBuffers.call(this);this.$OzMultiDestinationOperationLogger$p_1.forEach(function(a){return a.unsetMediaSourceSourceBuffers()});return this};c.log=function(){b.prototype.log.call(this),this.$OzMultiDestinationOperationLogger$p_1.forEach(function(a){return a.log()})};return a}(c("oz-player/loggings/OzOperationLoggerBase"));g.OzMultiDestinationPerfLoggerProvider=a}),98);
__d("oz-player/loggings/OzOperationLoggerObserver",["oz-player/shims/OzSubscriptionsHandler"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(){this.$1=new(c("oz-player/shims/OzSubscriptionsHandler"))()}var b=a.prototype;b.observe=function(a,b){var c;a=a.map(function(a){var c=a.setOperationLoggedListener(function(a){var c=a.getClientTimeBegin()||0,d=a.getClientTimeEnd()||0;b.getOperationLogger(a.getOperationName()).setClientTimeBegin(c).setClientTimeDuration(d-c).setClientTimeEnd(d).setLength(a.getLength()).setResult(a.getResult()||"success").setInitiator(a.getInitiator()).setType(a.getType()).log()});a.activate();return c});(c=this.$1).addSubscriptions.apply(c,a)};b.destroy=function(){this.$1.release()};return a}();g["default"]=a}),98);
__d("oz-player/shims/www/OzNetworkDiagnosticsWWW",["VideoPlayerOzWWWGlobalConfig"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b){if(!a)return null;a=parseInt(a.get(b),10);return!isNaN(a)&&isFinite(a)?a:null}function i(a,b,c){if(!a||b===""||c==="")return null;b=(a=a.get(b))!=null?a:"";a=b.split(";");for(b=0;b<a.length;b++){var d=a[b].split(":");if(d.length===2&&d[0]===c){d=parseInt(d[1],10);return isFinite(d)?d:null}}return null}a={getNextValidSegmentId:function(a){return h(a,"x-fb-next-valid-segment-id")},getResponseTimeMs:function(a){return h(a,"x-fb-response-time-ms")},getDvlSegmentPTS:function(a){return h(a,"x-fb-segment-pts-start")},getDvlStatus:function(a){return a==null?void 0:a.get("x-fb-dynamic-status")},getUsableResponseSizeForBandwidthEstimation:function(a){return h(a,"x-fb-dynamic-predictive-response-chunk-size")},getBandwidthMeanEstimate:function(a){var b=c("VideoPlayerOzWWWGlobalConfig").getString("bandwidth_estimate_header_key",""),d=c("VideoPlayerOzWWWGlobalConfig").getString("bandwidth_estimate_key","");return i(a,b,d)},getBandwidthStandardDeviationEstimate:function(a){return h(a,"x-bwe-std-dev")},getTimeToFirstByteMsEstimate:function(a){return h(a,"x-mrtt-ms")}};b=a;g["default"]=b}),98);
__d("oz-player/shims/OzNetworkDiagnostics",["oz-player/shims/www/OzNetworkDiagnosticsWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/OzNetworkDiagnosticsWWW")}),98);
__d("oz-player/manifests/OzDynamicVideoLibrary",["oz-player/shims/OzNetworkDiagnostics"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){this.$4=null;this.$5=null;this.$6=new RegExp(/(.*)\.(m4v|m4a)(\?|$)/);this.$7={m4a:{time:0},m4v:{time:0}};this.$8={audio:"m4a",video:"m4v"};this.$9={};a=a.config;this.$3=a}var b=a.prototype;b.getLastSegmentStartPTS=function(){return this.$1};b.getLast200RequestedTime=function(){return this.$5};b.getLast200RequestedUrlDecisionMinTime=function(){var a=[];for(var b in this.$7)a.push(this.$7[b].time);return Math.min.apply(Math,a)};b.getLast200RequestedUrlDecisionTimeByMimeType=function(a){return(a=this.$7[this.$8[a]])==null?void 0:a.time};b.updateWithResponse=function(a,b,d,e){var f;if(!e.headers||e.status!=200)return;this.$5=b;this.$1=(f=c("oz-player/shims/OzNetworkDiagnostics").getDvlSegmentPTS(e.headers))!=null?f:this.$1;this.$2=c("oz-player/shims/OzNetworkDiagnostics").getDvlStatus(e.headers);this.$10(a,d);(this.$2!=null||this.$2==="200")&&(this.$4=b)};b.$10=function(a,b){a=this.$6.exec(a);if(a!=null){a=a[2];b!=null&&(this.$7[a]={time:b})}};b.shouldRequestDynamicInfo=function(a){if(a!=="video")return!1;this.$11(a);a=this.$12(a);return a!=null&&a<=this.$3.getNumber("dvl_initial_segment_ignore_count")?!1:this.$3.getNumber("dvl_update_interval_ms")==0||this.$4==null||this.$4+this.$3.getNumber("dvl_update_interval_ms")<=Date.now()};b.$11=function(a){this.$9[a]==null&&(this.$9[a]={segmentCount:0});return this.$9[a].segmentCount=this.$9[a].segmentCount!=null?this.$9[a].segmentCount+1:0};b.$12=function(a){return this.$9[a].segmentCount};return a}();g["default"]=a}),98);
__d("oz-player/manifests/OzRepresentationBase",["oz-player/parsers/getMIMECodecs","oz-player/shims/ozvariant","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,c,d,e,f,g,h,i,j,k,l,m){this.$1=a,this.$2=b,this.$11=c,this.$6=g,this.$3=d,this.$4=e,this.$5=f,this.$7=h,this.$8=i,this.$9=j,this.$10=k,this.$12=l,this.$13=m}var b=a.prototype;b.getContentProtectionXml=function(){return this.$2};b.getID=function(){return this.$11};b.getBandwidth=function(){return this.$10};b.getMimeCodecs=function(){return c("oz-player/parsers/getMIMECodecs")(this.$3,this.$4)};b.getMimeType=function(){return this.$3};b.getCodecs=function(){return this.$4};b.getVariantKey=function(){return this.$5};b.getLang=function(){return this.$6};b.getRole=function(){return this.$7};b.getDisplayLabel=function(){return this.$11};b.getInitSegment=function(){return this.$8};b.getCustomFieldFirstSegment=function(){return this.$12};b.getCustomField=function(a){if(this.$13==null||!this.$13[a])throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_REPRESENTATION_PARSER",description:"Custom field "+a+" is not specified by caller",extra:{code:"OZ_RP-8"}});a=this.$13[a];try{return a(this.$1)}catch(a){return null}};b.updateWith=function(b){b instanceof a||c("oz-player/shims/ozvariant")(0,229),this.$9.updateWith(b.$9)};b.getSegmentByTime=function(a){return this.$9.getSegmentByTime(a)};b.getSegment=function(a){return this.$9.getSegment(a)};b.getPredictedSegmentAfter=function(a){return this.$9.getPredictedSegmentAfter(a)};b.canPredict=function(){return this.$9.canPredict()};b.canApproximateId=function(){return this.$9.canApproximateId()};b.getSegmentAfter=function(a){return this.$9.getSegmentAfter(a)};b.isEndingSegment=function(a){return this.$9.isEndingSegment(a)};b.addUpdateListener=function(a){return this.$9.addUpdateListener(a)};b.getTimeRanges=function(){return this.$9.getTimeRanges()};b.blockTimeRange=function(a){this.$9.blockTimeRange(a)};b.getEndingSegment=function(){return this.$9.getEndingSegment()};b.getMaxGopSec=function(){return this.$9.getMaxGopSec()};return a}();g["default"]=a}),98);
__d("oz-player/manifests/OzVideoRepresentation",["oz-player/manifests/OzRepresentationBase"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d,e,f,g,h,i,j,k,l,m,n,o,p){c=a.call(this,b,c,d,e,f,g,h,i,j,k,l,m,n)||this;c.$OzVideoRepresentation$p_5=null;c.$OzVideoRepresentation$p_6=null;c.$OzVideoRepresentation$p_7=null;c.$OzVideoRepresentation$p_1=o;c.$OzVideoRepresentation$p_2=p;c.$OzVideoRepresentation$p_4=n;c.$OzVideoRepresentation$p_3=b;if(n!=null){if(n.playbackResolutionMos!=null){d=c.getCustomField("playbackResolutionMos");c.$OzVideoRepresentation$p_7=d!=null?String(d):null}if(n.playbackResolutionCsvqm!=null){e=c.getCustomField("playbackResolutionCsvqm");c.$OzVideoRepresentation$p_6=e!=null?String(e):null}}return c}var c=b.prototype;c.getQualityScoreCurveString=function(a){if(a==="csvqm")return this.$OzVideoRepresentation$p_6;else return this.$OzVideoRepresentation$p_7};c.getWidth=function(){return this.$OzVideoRepresentation$p_1};c.getHeight=function(){return this.$OzVideoRepresentation$p_2};c.getDisplayLabel=function(){if(this.$OzVideoRepresentation$p_5!=null)return this.$OzVideoRepresentation$p_5;var a=this.$OzVideoRepresentation$p_4?this.$OzVideoRepresentation$p_4.qualityLabel:null;a&&(this.$OzVideoRepresentation$p_5=a(this.$OzVideoRepresentation$p_3));(this.$OzVideoRepresentation$p_5==null||this.$OzVideoRepresentation$p_5=="")&&(this.$OzVideoRepresentation$p_5=this.getHeight().toString()+"p");return this.$OzVideoRepresentation$p_5||""};return b}(c("oz-player/manifests/OzRepresentationBase"));g["default"]=a}),98);
__d("oz-player/utils/OzMimeUtil",[],(function(a,b,c,d,e,f){"use strict";function a(a){return(a.split("/")[0]||"").trim()}function b(a){a=(a.split(";")[0]||"").trim();a=(a.split("/")[1]||"").trim();return a}function c(a){a=a.split('codecs="').pop();return a.split(".")[0].trim()}f.getMimeType=a;f.getContainerType=b;f.getParsedCodecFamily=c}),66);
__d("oz-player/media_source/OzMSESourceBufferImpl",["oz-player/shims/OzDOMEventListener","oz-player/utils/OzErrorUtils","oz-player/utils/OzMimeUtil"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,c,d){this.$1=a,this.$2=c,this.$3=b,this.$4=d}var b=a.prototype;b.registerOnUpdateStartListener=function(a){return c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"updatestart",a)};b.registerOnUpdateEndListener=function(a){return c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"updateend",a)};b.registerOnUpdateListener=function(a){return c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"update",a)};b.registerOnErrorListener=function(a){return c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"error",a)};b.getIsUpdating=function(){return this.$1.updating};b.getBuffered=function(){return this.$1.buffered};b.setAppendWindowEnd=function(a){this.$1.appendWindowEnd=a};b.appendBuffer=function(a,b){var c,e=(c=d("oz-player/utils/OzMimeUtil")).getMimeType(b)!==c.getMimeType(this.$3),f=c.getContainerType(b)!==c.getContainerType(this.$3);c=c.getParsedCodecFamily(b)!==c.getParsedCodecFamily(this.$3);if(e||f||c)if(this.$4){try{this.$1.changeType(b)}catch(a){throw d("oz-player/utils/OzErrorUtils").createOzError({description:'SourceBufferChangeTypeError: Failed to changeType("'+b+'"): '+String(a)+" current MIME Codecs: "+this.$3,type:"OZ_SOURCE_BUFFER_CHANGE_TYPE"})}this.$3=b}else throw d("oz-player/utils/OzErrorUtils").createOzError({description:"SourceBufferChangeTypeError: attempted to call changeType() on a SourceBuffer that does not support it.",type:"OZ_SOURCE_BUFFER_CHANGE_TYPE_UNAVAILABLE"});this.$1.appendBuffer(a)};b.abort=function(){this.$1.abort()};b.remove=function(a,b){this.$1.remove(a,b)};return a}();g["default"]=a}),98);
__d("oz-player/media_source/OzMediaErrorProvider",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a){this.$1=a}var b=a.prototype;b.hasError=function(){return this.$1.error!==null};b.getMediaErrorName=function(){var a=this.$1.error;return a&&a.message?this.$2(a.message):null};b.getErrorCode=function(){var a=this.$1.error;return a&&a.code?a.code:null};b.getVideoNode=function(){return this.$1};b.$2=function(a){return a.replace(/([0-9]{2,})/g,function(a){var b="";while(b.length<a.length)b+="#";return b})};return a}();f["default"]=a}),66);
__d("oz-player/utils/OzSourceBufferUtil",["oz-player/utils/OzNumericalRangeUtil"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=0;for(var c=0;c<a.getBuffered().length;c++)b+=a.getBuffered().end(c)-a.getBuffered().start(c);return b}function b(a){return a.reduce(function(a,b){return a+(b.endTime-b.startTime)},0)}function c(a){return a.getBuffered().length==0?0:a.getBuffered().start(0)}function e(a){var b=null;try{b=a.getBuffered()}catch(a){return 0}return b.length==0?0:b.end(b.length-1)}function f(a){var b=[];for(var c=0;c<a.length;c++)b.push({startTime:a.start(c),endTime:a.end(c)});return b}function h(a,b,c){c=d("oz-player/utils/OzNumericalRangeUtil").findCurrentRangeIndex(c,b,a.map(function(a){return{rangeStart:a.startTime,rangeEnd:a.endTime}}));b=c>=0?parseFloat((a[c].endTime-b).toFixed(3)):0;a=c>=0?a.length-1-c:-1;return{bufferAheadSec:b,bufferedOffset:a}}function i(a,b,c){var e=a.map(function(a){return{rangeStart:a.startTime,rangeEnd:a.endTime}});c=d("oz-player/utils/OzNumericalRangeUtil").findCurrentRangeIndex(c,b,e);e=parseFloat(e.reduce(function(a,c){var e=c.rangeEnd;return e==null?a:a+(d("oz-player/utils/OzNumericalRangeUtil").isWithin(b,c)?e-b:d("oz-player/utils/OzNumericalRangeUtil").isLargerThan(c.rangeStart,b)?e-c.rangeStart:0)},0).toFixed(3));a=c>=0?a.length-1-c:-1;return{bufferAheadSec:e,bufferedOffset:a}}g.getTotalBufferedTime=a;g.getTotalInBufferedRanges=b;g.getStartBufferedTime=c;g.getEndBufferedTime=e;g.convertToBufferedTimeRangeArray=f;g.getBufferAheadInBufferedRanges=h;g.getTotalBufferAheadOfPosition=i}),98);
__d("oz-player/media_source/SourceBufferManagerUtils",["oz-player/utils/OzSourceBufferUtil"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,c){var e=d("oz-player/utils/OzSourceBufferUtil").getEndBufferedTime(b);return function(){var a=d("oz-player/utils/OzSourceBufferUtil").getEndBufferedTime(b);return{startTime:e,endTime:a}}}g.startMeasuringAppendedBuffer=a}),98);
__d("oz-player/shims/www/OzDeferredWWW",["Deferred"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("Deferred")}),98);
__d("oz-player/shims/OzDeferred",["oz-player/shims/www/OzDeferredWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/OzDeferredWWW")}),98);
__d("oz-player/utils/OzFakeEventTarget",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){this.$1={}}var b=a.prototype;b.dispatchEvent=function(a){if(this.$1[a]==null)return;this.$1[a].forEach(function(a){return a()})};b.addEventListener=function(a,b){var c=this;this.$1[a]===void 0&&(this.$1[a]=new Set());this.$1[a].add(b);return{remove:function(){c.removeEventListener(a,b)}}};b.removeEventListener=function(a,b){if(!this.$1[a])return;this.$1[a]["delete"](b)};b.destroy=function(){this.$1={}};return a}();f["default"]=a}),66);
__d("oz-player/states/OzObservedSourceBufferState",["oz-player/shims/OzSubscriptionsHandler","oz-player/utils/OzFakeEventTarget"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,d){var e;e=a.call(this)||this;e.$OzObservedSourceBufferState$p_2=new(c("oz-player/shims/OzSubscriptionsHandler"))();e.$OzObservedSourceBufferState$p_3=[];e.$OzObservedSourceBufferState$p_6=function(){var a;try{a=e.$OzObservedSourceBufferState$p_1.getBuffered()}catch(a){if(a.name==="InvalidStateError"){e.$OzObservedSourceBufferState$p_3=[];return}else throw a}var b=[];for(var c=0;c<a.length;c++)b.push({startTime:a.start(c),endTime:a.end(c)});e.$OzObservedSourceBufferState$p_3=b};e.$OzObservedSourceBufferState$p_1=b;e.$OzObservedSourceBufferState$p_4=d;e.$OzObservedSourceBufferState$p_5();return e}var d=b.prototype;d.$OzObservedSourceBufferState$p_5=function(){var a=this;this.$OzObservedSourceBufferState$p_2.release();this.$OzObservedSourceBufferState$p_2=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$OzObservedSourceBufferState$p_2.addSubscriptions(this.$OzObservedSourceBufferState$p_1.registerOnUpdateEndListener(function(){a.$OzObservedSourceBufferState$p_6(),a.dispatchEvent("buffer_updated")}),this.$OzObservedSourceBufferState$p_1.registerOnErrorListener(this.$OzObservedSourceBufferState$p_6))};d.setSourceBuffer=function(a){this.$OzObservedSourceBufferState$p_1=a,this.$OzObservedSourceBufferState$p_5(),this.$OzObservedSourceBufferState$p_6()};d.getBufferedRanges=function(){return this.$OzObservedSourceBufferState$p_3};d.destroy=function(){a.prototype.destroy.call(this),this.$OzObservedSourceBufferState$p_2.release(),this.$OzObservedSourceBufferState$p_2.engage()};return b}(c("oz-player/utils/OzFakeEventTarget"));g["default"]=a}),98);
__d("oz-player/utils/OzCustomErrorCode",[],(function(a,b,c,d,e,f){"use strict";a={APPEND_BUFFER_UNKNOWN_ERROR:"21539",APPEND_BUFFER_INVALID_STATE_ERROR_SOURCE_BUFFER_HAS_BEEN_REMOVED:"21540",APPEND_BUFFER_MEDIA_ERROR_ATTRIBUTE_NOT_NULL:"21541",APPEND_BUFFER_SOURCE_BUFFER_UPDATING_ATTRIBUTE_IS_TRUE:"21542",SOURCE_BUFFER_MANAGER_ADD_SOURCE_BUFFER_ERROR:"21543",SOURCE_BUFFER_MANAGER_CREATE_ERROR:"21544",SETUP_STREAMS_PROMISE_REJECTION:"21545",APPEND_BUFFER_QUOTA_EXCEEDED_ERROR:"21546",APPEND_BUFFER_INVALID_STATE_ERROR:"21547",SOURCE_BUFFER_MANAGER_UPDATEEND_UNKNOWN_ERROR:"21548",NETWORK_REQUEST_STREAM_RETRY_HANDLER_ERROR:"21549",STREAM_APPEND_QUOTA_EXCEEDED_HANDLER_ERROR:"21550",SOURCE_BUFFER_MANAGER_CLEAR_RANGE_FAILED:"21551",WEBVTT_CAPTION_PARSE_ERROR:"21552"};b=a;f["default"]=b}),66);
__d("oz-player/media_source/SourceBufferManager",["Promise","oz-player/loggings/OzLoggingUtils","oz-player/media_source/SourceBufferManagerUtils","oz-player/shims/OzDOMEventListener","oz-player/shims/OzDeferred","oz-player/shims/OzMaybeNativePromise","oz-player/shims/OzStreams","oz-player/shims/OzSubscriptionsHandler","oz-player/states/OzObservedSourceBufferState","oz-player/utils/OzCustomErrorCode","oz-player/utils/OzErrorUtils","oz-player/utils/ozConcatUint8Arrays"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(){function a(a,b,e,f,g,h){var i=this;h===void 0&&(h=null);this.$4=null;this.$5=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$7=!1;this.$8=[];this.$12=!1;this.$13=!1;this.$14=0;this.$26=function(){if(i.$10.hasError()){i.$13=!1;i.$12=!1;var a=i.$10.getMediaErrorName();a=a!=null?a:"An unknown source buffer error occurred.";var b=i.$10.getErrorCode();b=b!=null?String(b):c("oz-player/utils/OzCustomErrorCode").SOURCE_BUFFER_MANAGER_UPDATEEND_UNKNOWN_ERROR;a=d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SOURCE_BUFFER",description:a,extra:{code:b}});i.$4&&i.$4.reject(a);i.$4=null;i.cancelOperationAndCleanQueue()}else if(!i.$13){i.$13=!0;b=i.$10.getVideoNode();a=i.$26;b=c("oz-player/shims/OzDOMEventListener").listenDOMEvent(b,"error",a);i.$5.addSubscriptions(b)}};this.$17=function(){var a=i.$2;if(a!=null){a.getOperationLogger("source_buffer_updateend").setError((a=i.$10.getMediaErrorName())!=null?a:void 0).setCode(i.$10.getErrorCode()).setResult(i.$12?"failed":"success").log()}a=i.$4;a&&(i.$12?i.$26():(a.resolve(),i.$4&&(i.$4=null)))};this.$18=function(){var a=i.$2;if(a!=null){a.getOperationLogger("source_buffer_error").setError((a=i.$10.getMediaErrorName())!=null?a:void 0).setCode(i.$10.getErrorCode()).setResult("failed").log()}i.$12=!0};this.$1=a;this.$2=h;this.$3=b;this.$11=e;this.$16();this.$9=new(c("oz-player/states/OzObservedSourceBufferState"))(this.$3,this.$11);this.$10=f;this.$15=g}var e=a.prototype;e.$16=function(){this.$5.release(),this.$5=new(c("oz-player/shims/OzSubscriptionsHandler"))(),this.$5.addSubscriptions(this.$3.registerOnUpdateEndListener(this.$17),this.$3.registerOnErrorListener(this.$18))};e.setSourceBuffer=function(a){if(a==null)return;this.cancelOperationAndCleanQueue();this.$3=a;this.$16();this.$9.setSourceBuffer(a)};e.queueData=function(a,b,d,e,f){e===void 0&&(e=null);f===void 0&&(f=0);var g=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise"));this.$8.push({data:a,deferred:g,loggerProvider:e,appendTarget:f,mimeCodecs:b,clearSourceBufferRange:d});a=g.getPromise();this.$7||this.$19()["catch"](function(a){});return a};e.cancelOperationAndCleanQueue=function(a){a===void 0&&(a=null);a&&a.cloneContext().getOperationLogger("source_buffer_cancel_and_clean").log();var b=d("oz-player/utils/OzErrorUtils").createOzCancelledError("SBM cancel and clean");this.$8.forEach(function(a){a=a.deferred;a.reject(b)});this.$8=[];try{this.$11.getBool("enable_alternative_audio_tracks")?this.$15()==="open"&&(this.$10.hasError()||this.$3.abort()):this.$15()!=="closed"&&!0&&(this.$10.hasError()||this.$3.abort())}catch(b){var c=b;a&&a.cloneContext().getOperationLogger("cancel_operation_abort_failed").setError(c).setReason(this.$15?this.$15():null).log()}a=this.$4;a&&a.reject(b);this.$6&&(this.$6.reject(b),this.$6=null)};e.getSourceBufferState=function(){return this.$9};e.$20=function(a,b){a=Math.max(a,0);b>0&&b>a&&this.$3.remove(a,b)};e.$21=function(a,b){a=Math.max(a,0);if(b>0&&b>a){var d=this.$22();this.$20(a,b);return d}return c("oz-player/shims/OzMaybeNativePromise").resolve()};e.clearRangeWithWait=function(a){var c=this;this.cancelOperationAndCleanQueue();return a.reduce(function(a,b){var d=b.fromTime,e=b.toTime;return a.then(function(){return c.$21(d,e)})},(h||(h=b("Promise"))).resolve())};e.$22=function(a){var b=this.$4=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise"));b=b.getPromise();a&&d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(b,a,"wait_for_source_buffer");return b};e.$23=function(a,b,e,f){var g=this,h=function(){var h=g.$4=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise")),i=!1,j=!0;try{j=!!g.$3.getBuffered()}catch(a){j=!1}try{i=g.$3.getIsUpdating();if(j)g.$3.appendBuffer(a,b);else{h.resolve();return h.getPromise()}}catch(a){j=a;var k=c("oz-player/utils/OzCustomErrorCode").APPEND_BUFFER_UNKNOWN_ERROR,l=j.name,m=j.message;l==="QuotaExceededError"?k=c("oz-player/utils/OzCustomErrorCode").APPEND_BUFFER_QUOTA_EXCEEDED_ERROR:g.$10.hasError()?k=c("oz-player/utils/OzCustomErrorCode").APPEND_BUFFER_MEDIA_ERROR_ATTRIBUTE_NOT_NULL:i?k=c("oz-player/utils/OzCustomErrorCode").APPEND_BUFFER_SOURCE_BUFFER_UPDATING_ATTRIBUTE_IS_TRUE:l==="InvalidStateError"&&typeof m==="string"&&/SourceBuffer has been removed/.test(m)?k=c("oz-player/utils/OzCustomErrorCode").APPEND_BUFFER_INVALID_STATE_ERROR_SOURCE_BUFFER_HAS_BEEN_REMOVED:l==="InvalidStateError"&&(k=c("oz-player/utils/OzCustomErrorCode").APPEND_BUFFER_INVALID_STATE_ERROR);i=d("oz-player/utils/OzErrorUtils").createOzError({type:d("oz-player/utils/OzErrorUtils").isOzError(j)?j.getType():l==="QuotaExceededError"?"OZ_SOURCE_BUFFER_QUOTA_EXCEEDED":l==="OZ_SOURCE_BUFFER_CHANGE_TYPE"?"OZ_SOURCE_BUFFER_CHANGE_TYPE":"OZ_SOURCE_BUFFER",description:"SBM#"+g.$1+" appendBuffer("+b+") failed: "+String(m),extra:d("oz-player/utils/OzErrorUtils").isOzError(j)?j.getExtra():{originalError:j,code:k}});g.$4=null;h.reject(i)}l=h.getPromise();e&&d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(l,e,"append",function(b){var c;b.setLength(a.byteLength);c=(c=f==null?void 0:f.appendTarget)!=null?c:null;b.setAppendTarget(c)});return l};if(this.$3.getIsUpdating())return this.$22(e).then(h)["catch"](function(a){throw a});this.$14+=a.byteLength;return h()};e.$19=function(){var a=this;if(this.$7)return c("oz-player/shims/OzMaybeNativePromise").reject(this.$24("Another unit of queued data is being appended."));var b=this.$8.shift();if(!b){this.$7=!1;return c("oz-player/shims/OzMaybeNativePromise").resolve()}var e=b.data,f=b.mimeCodecs,g=b.deferred,h=b.loggerProvider,i=b.appendTarget,j=b.clearSourceBufferRange;this.$7=!0;var k;b=function(){return e instanceof d("oz-player/shims/OzStreams").OzReadableStream?a.$25(e,f,h,i):a.$23(e,f,h,{appendTarget:i})};j?(k=this.$3.getIsUpdating()?this.$22(h):c("oz-player/shims/OzMaybeNativePromise").resolve(),k=k.then(function(){return a.clearRangeWithWait([{fromTime:j[0],toTime:j[1]}])}).then(b)):k=b();var l=d("oz-player/media_source/SourceBufferManagerUtils").startMeasuringAppendedBuffer(this.$11,this.$3,this.$9);return k["catch"](function(a){g.reject(a)}).then(function(){var b=l(),c=b.startTime;b=b.endTime;g.resolve({startTime_UNSAFE:c,endTime_UNSAFE:b,appendedSec:b-c});a.$7=!1;a.$19()["catch"](function(a){})})};e.$25=function(a,b,e,f){var g=this,h=[],i=0,j=a.getReader();a=function a(){var k=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise"));g.$6=k;j.read().then(function(a){k.resolve(a),g.$6=null})["catch"](function(a){k.reject(a),g.$6=null});return k.getPromise().then(function(d){if(d.done){var j=c("oz-player/utils/ozConcatUint8Arrays")(h);j=j.buffer;h.length=0;i=0;return j.byteLength>0?g.$23(j,b,e,{appendTarget:f}):c("oz-player/shims/OzMaybeNativePromise").resolve()}j=d.value;d=j;if(f>0){j=j instanceof Uint8Array?j:new Uint8Array(j);h.push(j);i+=j.byteLength;if(i>=f){j=c("oz-player/utils/ozConcatUint8Arrays")(h);j=j.buffer;h.length=0;i=0;d=j}}return d&&d.byteLength>=f?g.$23(d,b,e,{appendTarget:f}).then(function(){return a()}):a()},function(a){if(g.$11.getBool("enable_alternative_audio_tracks"))try{g.$15()==="open"&&(g.$10.hasError()||g.$3.abort())}catch(a){a}else try{g.$3.abort()}catch(a){a}if(typeof a==="object"&&d("oz-player/utils/OzErrorUtils").isOzError(a))return c("oz-player/shims/OzMaybeNativePromise").reject(a);if(typeof a==="object"&&a!=null&&typeof a.name==="string"&&a.name==="TypeError"&&typeof a.message==="string"&&a.message.includes("network error")){var e=d("oz-player/utils/OzErrorUtils").convertPromiseRejectionReasonToOzError(a,{description:"SBM#"+g.$1+" appendReadableStream("+b+") failed: Stream read is interrupted (previously TypeError network error)}",extra:{originalError:a},type:"OZ_NETWORK"});return c("oz-player/shims/OzMaybeNativePromise").reject(e)}e=d("oz-player/utils/OzErrorUtils").convertPromiseRejectionReasonToOzError(a,{description:"SBM#"+g.$1+" appendReadableStream("+b+") failed: Unexpected error while reading from stream: "+String(a),extra:{originalError:a},type:"OZ_NETWORK"});return c("oz-player/shims/OzMaybeNativePromise").reject(e)})};return a()};e.$24=function(a){var b=this.$2;b!=null&&b.getOperationLogger("source_buffer_invariant").setError(a).setResult("failed").log();return d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SOURCE_BUFFER",description:"SBM invariant: "+a})};e.destroy=function(){var a=this,b=this.$2;b&&b.cloneContext().getOperationLogger("source_buffer_destroy").log();b=this.$4;b&&(!this.$13?(b.reject(d("oz-player/utils/OzErrorUtils").createOzCancelledError("SBM destroyed")),this.$4=null,this.$5.release(),this.$5=new(c("oz-player/shims/OzSubscriptionsHandler"))()):b.getPromise()["catch"](function(){a.$4=null,a.$5.release(),a.$5=new(c("oz-player/shims/OzSubscriptionsHandler"))()}));this.$9.destroy()};e.getDebug=function(){return{SourceBuffer:this.$3}};return a}();g["default"]=a}),98);
__d("oz-player/shims/www/getOzVTTSourceBufferImplLazyWWW",["JSResourceForInteraction"],(function(a,b,c,d,e,f,g){"use strict";var h=c("JSResourceForInteraction")("OzVTTSourceBufferImpl").__setRef("oz-player/shims/www/getOzVTTSourceBufferImplLazyWWW");function a(){return h.load()}g["default"]=a}),98);
__d("oz-player/shims/www/getOzVTTSourceBufferImplWWW",["oz-player/shims/www/getOzVTTSourceBufferImplLazyWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/getOzVTTSourceBufferImplLazyWWW")}),98);
__d("oz-player/shims/getOzVTTSourceBufferImpl",["oz-player/shims/www/getOzVTTSourceBufferImplWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/getOzVTTSourceBufferImplWWW")}),98);
__d("oz-player/shims/www/ozClearTimeoutWWW",["clearTimeout"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("clearTimeout")}),98);
__d("oz-player/shims/ozClearTimeout",["oz-player/shims/www/ozClearTimeoutWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/ozClearTimeoutWWW")}),98);
__d("oz-player/shims/www/ozSetTimeoutAcrossTransitionsWWW",["setTimeoutAcrossTransitions"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("setTimeoutAcrossTransitions")}),98);
__d("oz-player/shims/ozSetTimeoutAcrossTransitions",["oz-player/shims/www/ozSetTimeoutAcrossTransitionsWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/ozSetTimeoutAcrossTransitionsWWW")}),98);
__d("oz-player/utils/ozGetErrorNameFromMediaErrorCode",[],(function(a,b,c,d,e,f){"use strict";function a(a){switch(a){case 1:return"MEDIA_ERR_ABORTED";case 2:return"MEDIA_ERR_NETWORK";case 3:return"MEDIA_ERR_DECODE";case 4:return"MEDIA_ERR_SRC_NOT_SUPPORTED";default:}return"MEDIA_ERR_UNKNOWN"}f["default"]=a}),66);
__d("oz-player/media_source/MediaSourceManager",["oz-player/loggings/OzMultiDestinationPerfLogger","oz-player/media_source/OzMSESourceBufferImpl","oz-player/media_source/OzMediaErrorProvider","oz-player/media_source/SourceBufferManager","oz-player/shims/OzDOMEventListener","oz-player/shims/OzDeferred","oz-player/shims/OzMaybeNativePromise","oz-player/shims/OzSubscriptionsHandler","oz-player/shims/getOzVTTSourceBufferImpl","oz-player/shims/ozClearTimeout","oz-player/shims/ozReportUnexpectedError","oz-player/shims/ozSetTimeoutAcrossTransitions","oz-player/shims/ozThrottle","oz-player/utils/OzCustomErrorCode","oz-player/utils/OzErrorEmitter","oz-player/utils/OzErrorUtils","oz-player/utils/OzMimeUtil","oz-player/utils/ozGetErrorNameFromMediaErrorCode"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){var b=this,e=a.videoNode,f=a.config,g=a.perfLoggerProvider;g=g===void 0?new(d("oz-player/loggings/OzMultiDestinationPerfLogger").OzMultiDestinationPerfLoggerProvider)([]):g;var h=a.isChangeTypeSupported,i=a.useManagedMediaSource,j=a.handleVttCaptionsUpdated,k=a.onClearVideoNodeError;a=a.onRetryVideoElementError;this.$3=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$4=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$6=[];this.$7=new Map();this.$8=[];this.$10=!1;this.$12=!1;this.$13=!1;this.$14=!1;this.$15=!1;this.$18=null;this.$19=null;this.$20=new(c("oz-player/utils/OzErrorEmitter"))();this.$22="undetected";this.$23=!1;this.$24=!1;this.$31=function(){b.$34()||(b.$10=!1);if(!b.$35())return;var a=b.$8.shift();a&&(a(b.$2),b.$10=!0)};this.$5=g;this.$11=f;this.$1=e;this.$21=j;this.$16=k;this.$17=a;this.$23=h;this.$24=i;this.$9=new(c("oz-player/media_source/OzMediaErrorProvider"))(this.$1);this.$3.addSubscriptions(c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"error",function(){var a;b.$5.getOperationLogger("media_element_error").setError((a=b.$9.getMediaErrorName())!=null?a:void 0).setCode(b.$9.getErrorCode()).setResult("failed").setUserInfo({av1HardwareSupport:b.$22}).log();b.$8=[];b.$6=[];b.$4.release();b.$4=new(c("oz-player/shims/OzSubscriptionsHandler"))();b.$15=b.$15||b.$14||b.$13;a=b.$1.error;var e=(a==null?void 0:a.code)||0,f=!a&&b.$1.poster!=null&&b.$1.poster!=="",g=d("oz-player/utils/OzErrorUtils").createOzError({type:f?"OZ_POSSIBLE_POSTER_LOAD_FAILURE":c("oz-player/utils/ozGetErrorNameFromMediaErrorCode")(e),description:(a==null?void 0:a.message)!=null?a.message:"",extra:{originalError:a,code:String(e)}});if(b.$19!=null){b.$5.getOperationLogger("media_element_error").setError(a).setCode(e).setReason("mitigation_failed").setResult("failed").setUserInfo({av1HardwareSupport:b.$22}).log();b.$20.emitError(g);return}b.$19=b.$1.currentTime;b.$18==null&&(b.$18=c("oz-player/shims/ozSetTimeoutAcrossTransitions")(function(){b.$18=null,b.$11.getBool("retry_video_element_error")&&(b.$17!=null&&b.$17(g),b.$25())},0))}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"play",function(){b.$14=!0}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"playing",function(){b.$14=!1,b.$15=!1,b.$19=null,b.$13=!0}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"pause",function(){b.$14=!1,b.$13=!1,b.$15=!!b.$1.error}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"ended",function(){b.$14=!1,b.$13=!1}));this.$2=this.$26();this.$5.getOperationLogger("media_source_new").log();this.$3.addSubscriptions(c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$2,"sourceopen",function(){b.$5.getOperationLogger("media_source_open").log()}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$2,"sourceended",function(){b.$5.getOperationLogger("media_source_ended").log()}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$2,"sourceclose",function(){b.$5.getOperationLogger("media_source_close").log()}));this.$27(this.$2)}var b=a.prototype;b.$26=function(){return this.$24?new ManagedMediaSource():new MediaSource()};b.$27=function(a){var b=this.$1,c=b.src;c!==""&&URL.revokeObjectURL(c);a?(this.$24&&(b.disableRemotePlayback=!0),b.src=URL.createObjectURL(a)):(b.removeAttribute("src"),b.removeAttribute("srcObject"))};b.$25=function(){var a=this;this.$7.forEach(function(a){a.cancelOperationAndCleanQueue()});this.$2=this.$26();this.$27(this.$2);this.$7.forEach(function(a,b){a.setSourceBuffer(null)});var b=this.$28().then(function(){var b=[];a.$7.forEach(function(c,d){var e=a.$29(d).then(function(b){c.setSourceBuffer(b),a.$30(b,d)});b.push(e)});return c("oz-player/shims/OzMaybeNativePromise").all(b)});b.then(function(){a.$16&&a.$16();a.$19!=null&&(a.$1.currentTime=a.$19);if(a.$15){var b=a.$1.play();return b==null?void 0:b["catch"](function(){})}})["catch"](function(b){a.$20.emitError(b)})};b.$28=function(){var a=this;if(this.$11.getBool("msm_refactor_wait_for_sourceopen")){if(this.$2.readyState==="open")return c("oz-player/shims/OzMaybeNativePromise").resolve();var b=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise")),d=c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$2,"sourceopen",function(){d.remove(),b.resolve(),a.$31()});this.$3.addSubscriptions(d);return b.getPromise()}else{if(this.$2.readyState==="open")return c("oz-player/shims/OzMaybeNativePromise").resolve();var e=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise"));this.$3.addSubscriptions(c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$2,"sourceopen",function(){e.resolve(),a.$31()}));return e.getPromise()}};b.$29=function(a){var b=this;if(this.$11.getBool("msm_refactor_wait_for_sourceopen"))return c("oz-player/shims/OzMaybeNativePromise").resolve().then(function(){return b.$32(a)})["catch"](function(b){b=d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SOURCE_BUFFER",description:'Failed to addSourceBuffer("'+a+'"): '+String(b),extra:{originalError:b,code:c("oz-player/utils/OzCustomErrorCode").SOURCE_BUFFER_MANAGER_ADD_SOURCE_BUFFER_ERROR.toString()}});throw b});else try{return this.$32(a)}catch(b){var e=d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SOURCE_BUFFER",description:'Failed to addSourceBuffer("'+a+'"): '+(b.message||String(b)),extra:{originalError:b,code:c("oz-player/utils/OzCustomErrorCode").SOURCE_BUFFER_MANAGER_ADD_SOURCE_BUFFER_ERROR.toString()}});throw e}};b.$32=function(a){var b=this,e;a.includes("vtt")?e=this.$33(a):e=this.$28().then(function(){return new(c("oz-player/media_source/OzMSESourceBufferImpl"))(b.$2.addSourceBuffer(a),a,b.$11,b.$23)});return e.then(function(c){b.$6.push(c);c.registerOnUpdateListener(function(){b.$5.cloneContext().getOperationLogger("source_buffer_update_start").setType(d("oz-player/utils/OzMimeUtil").getMimeType(a)).setMediaSourceSourceBuffer(d("oz-player/utils/OzMimeUtil").getMimeType(a),c).setType(d("oz-player/utils/OzMimeUtil").getMimeType(a)).log()});b.$5.getOperationLogger("add_source_buffer").setMediaSourceSourceBuffer(d("oz-player/utils/OzMimeUtil").getMimeType(a),c).log();return c})};b.$30=function(a,b){var c=this;this.$4.addSubscriptions(a.registerOnUpdateEndListener(function(){c.$31()}))};b.$33=function(a){var b=this;return c("oz-player/shims/getOzVTTSourceBufferImpl")().then(function(e){if(b.$21!=null){var f=new e({onCaptionsChanged:b.$21,config:b.$11}),g=c("oz-player/shims/ozThrottle")(function(){f.setCurrentVideoTime(b.$1.currentTime)},200);b.$3.addSubscriptions(c("oz-player/shims/OzDOMEventListener").listenDOMEvent(b.$1,"timeupdate",function(){g()}),{remove:function(){g=function(){}}});return f}else throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SOURCE_BUFFER",description:'Failed to addSourceBuffer("'+a+'"): No handleVttCaptionsUpdated callback provided',extra:{code:c("oz-player/utils/OzCustomErrorCode").SOURCE_BUFFER_MANAGER_ADD_SOURCE_BUFFER_ERROR.toString()}})})};b.createSourceBufferManager=function(a,b,e,f){var g=this;return(this.$11.getBool("msm_refactor_wait_for_sourceopen")?this.$29(b).then(function(a){return[void 0,a]}):c("oz-player/shims/OzMaybeNativePromise").all([this.$28(),this.$29(b)])).then(function(e){e[0];e=e[1];var f=new(c("oz-player/media_source/SourceBufferManager"))(a,e,g.$11,g.$9,function(){return g.$2.readyState},g.$5.cloneContext().setType(d("oz-player/utils/OzMimeUtil").getMimeType(b)));g.$30(e,b);g.$7.set(b,f);return f})["catch"](function(b){var e=d("oz-player/utils/OzErrorUtils").isOzError(b)?b.getExtra():void 0,f=e==null?void 0:e.code;f=d("oz-player/utils/OzErrorUtils").isOzError(b)?""+b.getType()+(f!=null?"#"+f:"")+": "+b.getDescription():String(b);f=d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SOURCE_BUFFER",description:"Failed to create SBM#"+a+": "+f+", support AAC-LC="+(g.$2.constructor.isTypeSupported("audio/mp4; codecs=mp4a.40.2")?"true":"false")+", readyState="+g.$2.readyState,extra:babelHelpers["extends"]({},e,{originalError:b,code:c("oz-player/utils/OzCustomErrorCode").SOURCE_BUFFER_MANAGER_CREATE_ERROR.toString()})});throw f})};b.getReadyState=function(){return this.$2.readyState};b.getMediaSource=function(){return this.$2};b.notifyEndOfStream=function(a,b){var d=this,e=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise"));this.$3.addSubscriptions(c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$2,"sourceended",function(){e.resolve()}));this.$8.push(function(c){if(c.readyState==="open"){var e=d.$5.getOperationLogger("end_of_stream");e.setInitiator(a).setReason(b);e.log();c.endOfStream()}});this.$31();return e.getPromise()};b.updateDuration=function(a,b){var e=this,f=this.$2.duration;if(Number.isFinite(f)&&(!b||f>=a))return;this.$8.push(function(b){b.duration!==f&&!(isNaN(b.duration)&&isNaN(f))&&c("oz-player/shims/ozReportUnexpectedError")(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SOURCE_BUFFER",description:"mediaSource duration discrepancy: "+b.duration+" != "+f}),"MediaSourceManager updateDuration","warn");var g=e.$11.getBool("msm_refactor_wait_for_sourceopen")?b.duration!==Infinity&&a<b.duration:a<b.duration;if(g){e.$5.getOperationLogger("media_source_update_duration").setMediaSourcePreviousDuration(b.duration).setMediaSourceNewDuration(a).setResult("failed").log();return}e.$5.getOperationLogger("media_source_update_duration").setMediaSourcePreviousDuration(b.duration).setMediaSourceNewDuration(a).log();b.duration=a});this.$31();return};b.detach=function(){this.$18&&(c("oz-player/shims/ozClearTimeout")(this.$18),this.$18=null);this.$5.getOperationLogger("source_buffer_detach").unsetMediaSourceSourceBuffers();this.$27(null);for(var a=0;a<this.$2.sourceBuffers.length;a++){var b=this.$2.sourceBuffers[a];this.$2.removeSourceBuffer(b)}this.$3.release();this.$3.engage();this.$4.release();this.$4.engage();this.$8=[];this.$10=!1;this.$12=!0};b.$35=function(){return!this.$34()&&this.$2.readyState==="open"&&!this.$10&&!this.$12};b.$34=function(){return this.$6.some(function(a){return a.getIsUpdating()})};b.onError=function(a){return this.$20.onError(a)};return a}();g["default"]=a}),98);
__d("oz-player/media_source/OzMediaSeekableRangeManager",["oz-player/shims/OzSubscriptionsHandler"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){var d=this;this.$5=function(a){a===void 0&&(a=!0);var b=d.$2;if(!b)return;b=b.getTimeRanges();if(!b.length)return;b=b[b.length-1];d.$1.updateDuration(b.endTime,a)};this.$1=a;this.$3=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$4=b}var b=a.prototype;b.setTimeRangeProvider=function(a){this.$3.release(),this.$3.engage(),this.$3.addSubscriptions(a.addUpdateListener(this.$5)),this.$2=a};b.listenToMediaStreamInitAppended=function(a){var b=this;return a.addListener("initAppended",function(){b.$5(!1)})};b.destroy=function(){this.$3.release()};return a}();g["default"]=a}),98);
__d("oz-player/networks/OzCreateErrorStream",["oz-player/shims/OzStreams"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return new(d("oz-player/shims/OzStreams").OzReadableStream)({pull:function(b){b.error(a)}})}g["default"]=a}),98);
__d("oz-player/shims/OzFetchAPI",[],(function(a,b,c,d,e,f){"use strict";b=function(b,c){return a.fetch(b,c)};c=function(b,c){return new a.Response(b,c)};f.fetch=b;f.Response=c}),66);
__d("oz-player/networks/FetchWithTimeout",["oz-player/shims/OzFetchAPI","oz-player/shims/ozClearTimeout","oz-player/shims/ozSetTimeoutAcrossTransitions","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";function b(b,e,f,g){if(!d("oz-player/shims/OzFetchAPI").fetch)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_INITIALIZATION",description:"fetch api is missing"});var h=null;if("AbortController"in a&&f!=null&&f!=0){var i=new AbortController();h=c("oz-player/shims/ozSetTimeoutAcrossTransitions")(function(){return i.abort()},f);f=babelHelpers["extends"]({},e,{signal:i.signal})}else f=babelHelpers["extends"]({},e);g!=null&&(f=babelHelpers["extends"]({},f,{referrer:g}));return d("oz-player/shims/OzFetchAPI").fetch(b,f).then(function(a){c("oz-player/shims/ozClearTimeout")(h);return a})}g["default"]=b}),98);
__d("oz-player/networks/OzHTTPHeaders",["oz-player/shims/ozvariant"],(function(a,b,c,d,e,f,g){"use strict";d=function(){function a(){this.$1=new Map()}var b=a.prototype;b.append=function(a,b){a=a.toLowerCase();var c=this.$1.get(a);c!=null?this.$1.set(a,c+", "+b):this.$1.set(a,b)};b.get=function(a){a=a.toLowerCase();a=this.$1.get(a);return a!=null?a:null};b.entries=function(){return this.$1.entries()};b.has=function(a){return this.get(a)!==null};return a}();e=a.Headers;f=e&&e.prototype;var h=e&&f&&typeof f.append==="function"&&typeof f.entries==="function"&&typeof f.get==="function"&&typeof f.has==="function"?e:d;function b(a){if(a==null)return null;if(typeof a==="object"&&typeof a.append==="function"&&typeof a.get==="function"&&typeof a.has==="function")if(typeof a.entries==="function")return a;else if(typeof a.forEach==="function"){var b=new h();a.forEach(function(a,c){b.append(c,a)});return b}c("oz-player/shims/ozvariant")(!1,"Malformed Headers object: %s %s",typeof a,String(a));return null}g.OzHTTPHeaders=h;g.maybeConvertHeadersToOzHTTPHeaders=b}),98);
__d("oz-player/utils/arrayBuffer2OzReadableStream",["oz-player/shims/OzStreams"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return new(d("oz-player/shims/OzStreams").OzReadableStream)({start:function(b){var c=new Uint8Array(a);b.enqueue(c);b.close()}})}g["default"]=a}),98);
__d("oz-player/utils/maybeConvertReadableStreamToOzReadableStream",["asyncToGeneratorRuntime","oz-player/shims/OzStreams"],(function(a,b,c,d,e,f,g){"use strict";function a(a){if(!d("oz-player/shims/OzStreams").OzReadableStreamIsPolyfilled)return a;var c=a.getReader();return new(d("oz-player/shims/OzStreams").OzReadableStream)({start:function(){var a=b("asyncToGeneratorRuntime").asyncToGenerator(function*(a){while(!0){var b=(yield c.read());if(b.done)break;a.enqueue(b.value)}a.close()});function d(b){return a.apply(this,arguments)}return d}()})}g["default"]=a}),98);
__d("oz-player/utils/processFetchResponse",["oz-player/networks/OzHTTPHeaders","oz-player/shims/OzMaybeNativePromise","oz-player/utils/arrayBuffer2OzReadableStream","oz-player/utils/maybeConvertReadableStreamToOzReadableStream"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=a.body,e=a.ok,f=a.status,g=a.headers;return b!=null?c("oz-player/shims/OzMaybeNativePromise").resolve({ok:e,status:f,headers:d("oz-player/networks/OzHTTPHeaders").maybeConvertHeadersToOzHTTPHeaders(g),body:c("oz-player/utils/maybeConvertReadableStreamToOzReadableStream")(b),arrayBuffer:function(){return a.arrayBuffer()}}):a.arrayBuffer().then(function(a){return{ok:e,status:f,headers:d("oz-player/networks/OzHTTPHeaders").maybeConvertHeadersToOzHTTPHeaders(g),body:c("oz-player/utils/arrayBuffer2OzReadableStream")(a),arrayBuffer:function(){return c("oz-player/shims/OzMaybeNativePromise").resolve(a)}}})}g["default"]=a}),98);
__d("oz-player/networks/OzFetchRequestImplementation",["oz-player/networks/FetchWithTimeout","oz-player/utils/processFetchResponse"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b,d,e,f,g,h,i){f=d?d.networkTimeoutMs:null;g=c("oz-player/networks/FetchWithTimeout")(a,b,f);return g.then(function(a){var b=a.ok,d=a.status,f=a.headers;return e!=null&&!b&&typeof a.text==="function"?a.text().then(function(c){return{ok:b,status:d,headers:f,body:c,arrayBuffer:function(){return a.arrayBuffer()}}}):c("oz-player/utils/processFetchResponse")(a)})}b=a;d=b;g["default"]=d}),98);
__d("oz-player/networks/withRetries",["Promise","asyncToGeneratorRuntime","oz-player/shims/ozSetTimeoutAcrossTransitions"],(function(a,b,c,d,e,f,g){"use strict";var h;function a(a,d,e){return new(h||(h=b("Promise")))(function(f,g){function h(a){return i.apply(this,arguments)}function i(){i=b("asyncToGeneratorRuntime").asyncToGenerator(function*(b){try{f(yield a())}catch(a){if(b>0){var i=typeof e==="function"?e(d-b):e;c("oz-player/shims/ozSetTimeoutAcrossTransitions")(function(){h(b-1)},i)}else g(a)}});return i.apply(this,arguments)}h(d)})}g["default"]=a}),98);
__d("oz-player/networks/OzFetchWithCache",["oz-player/networks/OzFetchRequestImplementation","oz-player/networks/withRetries","oz-player/shims/OzMaybeNativePromise","oz-player/utils/OzErrorUtils","oz-player/utils/arrayBuffer2OzReadableStream","oz-player/utils/processFetchResponse"],(function(a,b,c,d,e,f,g){"use strict";function a(a){var b=a.config,e=a.url,f=a.http,g=a.overrideOzRequestImplementation,h=a.options,i=a.prefetchCache,j=a.mediaStreamType,k=a.onPerfEntryAvailable,l=a.dataAppendedCallback,m=a.dataAppendedErrorCallback,n=g!=null?function(){return g.apply(void 0,arguments)}:c("oz-player/networks/OzFetchRequestImplementation");a=i?i.getCacheValue(e):null;i=a;a=!1;if(i)a=!0,i=i.then(function(a){if(a.initiator==="FETCH"){var b=a.response,e=b.ok,f=b.status,g=b.headers;return!e&&typeof a.response.text==="function"?a.response.text().then(function(b){return{ok:!1,status:f,headers:g,body:b,arrayBuffer:function(){return a.response.arrayBuffer()}}}):c("oz-player/utils/processFetchResponse")(a.response)}else if(a.initiator==="XHR_REQUEST"){b=c("oz-player/utils/arrayBuffer2OzReadableStream")(a.response);return{body:b,status:200,headers:null,ok:!0,arrayBuffer:function(){return c("oz-player/shims/OzMaybeNativePromise").resolve(a.response)}}}else throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_UNEXPECTED_CACHE_INITIATOR",description:"Unable to handle request initiator: "+a.initiator})})["catch"](function(a){return n(e,f,null,null,j,k,l,m)});else{var o;o=(o=h==null?void 0:h.retryAttempts_SIDX_USE_ONLY)!=null?o:0;if(o>0){var p;p=(p=h==null?void 0:h.retryTimeoutMs_SIDX_USE_ONLY)!=null?p:100;i=c("oz-player/networks/withRetries")(function(){return n(e,f,h,b,j,k,l,m)},o,p)}else i=n(e,f,h,b,j,k,l,m)}return{promise:i,retrievedFromCache:a}}g["default"]=a}),98);
__d("oz-player/utils/OzVideoUrlUtils",[],(function(a,b,c,d,e,f){"use strict";c=1<<31;var g=~c,h=c+1;function i(a){a=a|0;return Math.max(h,Math.min(a,g))}function j(a){a=Number.parseInt(a,16);return Number.isFinite(a)?new Date(i(a)*1e3):null}function k(){return i(Date.now()/1e3)}var l={OE:"oe",OE2:"oe2",ODM:"odm",USS:"uss"};function m(a){a=a instanceof URL?a.searchParams:new URL(a).searchParams;a=a.get(l.OE);a=a==null?void 0:j(a);return{expirationDate:a}}function a(a){a=a instanceof URL?a.searchParams:new URL(a).searchParams;return a.get(l.OE2)!==null||a.get(l.ODM)!==null||a.get(l.USS)!==null}function b(a){a=m(a);a=a.expirationDate;return a!=null&&a<=new Date(k()*1e3)}f.DEFAULT_UNIXTIME=c;f.MAX_INT=g;f.parseCdnUrlParams=m;f.isShortenedExpiryTimestampCdnUrl=a;f.isCdnUrlExpired=b}),66);
__d("oz-player/networks/OzNetworkRequestStream",["Promise","oz-player/networks/OzCreateErrorStream","oz-player/networks/OzFetchWithCache","oz-player/shims/OzURI","oz-player/shims/ozvariant","oz-player/utils/OzCustomErrorCode","oz-player/utils/OzErrorUtils","oz-player/utils/OzResourceTimingUtils","oz-player/utils/OzVideoUrlUtils"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(){function a(a){this.$8=!1;this.$10="auto";var b=a.debugName,c=a.config,d=a.baseUri,e=a.baseUriDecisionTime,f=a.options,g=a.requestParamCreator,h=a.networkRequestStreamHandlers,i=a.overrideOzRequestImplementation,j=a.prefetchCache,k=a.networkRequestFetchPriority,l=a.networkRequestStreamRetryHandler,m=a.networkRequestUrlRefreshHandler,n=a.dynamicVideoLibrary,o=a.mediaStreamType,p=a.dataAppendedCallback;a=a.dataAppendedErrorCallback;this.$1=b;this.$2=d;this.$3=f;this.$4=g;this.$11=h!=null?h:[];this.$10=k;this.$12=l;this.$13=m;this.$9=i;this.$14=j;this.$15=c;this.$16=n;this.$17=e;this.$18=o;this.$19=p;this.$20=a}var e=a.prototype;e.$21=function(a){var b=this;a=this.$4.createRequestParam(this.$2,a,this.$10);var d=a.uri;a=a.http;d=d.toString();this.$5=d;a=c("oz-player/networks/OzFetchWithCache")({config:this.$15,url:d,http:a,overrideOzRequestImplementation:this.$9,options:this.$3,prefetchCache:this.$14,mediaStreamType:this.$18,onPerfEntryAvailable:function(a){b.$22(a)},dataAppendedCallback:this.$19,dataAppendedErrorCallback:this.$20});var e=a.promise;a=a.retrievedFromCache;this.$8=a;return{requestPromise:e,requestUrl:d}};e.startStream=function(a){var b=this;return this.$23().then(function(){if(b.$15.getBool("video_cdn_url_refresh")&&!d("oz-player/utils/OzVideoUrlUtils").isShortenedExpiryTimestampCdnUrl(b.$2.toString())&&d("oz-player/utils/OzVideoUrlUtils").isCdnUrlExpired(b.$2.toString()))return c("oz-player/networks/OzCreateErrorStream")(d("oz-player/utils/OzErrorUtils").createOzNetworkError({description:"OzNetworkRequestStream("+b.$1+") CDN URL expired.",responseStatus:0,responseHeaders:null,requestUrl:b.$2.toString()}));var e=b.$21(a),f=e.requestPromise,g=e.requestUrl,h=g,i=Date.now();return f.then(function(e){var f;(f=b.$16)==null?void 0:f.updateWithResponse(g,i,b.$17,e);b.$7=e;if(b.$12!=null&&e.ok===!1)return b.$12(e,function(){var d=b.$21(a),c=d.requestPromise;d=d.requestUrl;h=d;return c},h)["catch"](function(a){throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_NETWORK_REQUEST_STREAM_RETRY_HANDLER_ERROR",description:String(a),extra:{originalError:a,code:c("oz-player/utils/OzCustomErrorCode").NETWORK_REQUEST_STREAM_RETRY_HANDLER_ERROR,url:h}})});else return e}).then(function(a){var e=[];for(var f=0;f<b.$11.length;f++)try{var g=b.$11[f].onResponse(a,h);g&&e.push(g)}catch(a){g=a;return c("oz-player/networks/OzCreateErrorStream")(g)}g=a.body;f=a.headers;var i=a.ok;a=a.status;if(!i)return c("oz-player/networks/OzCreateErrorStream")(d("oz-player/utils/OzErrorUtils").createOzNetworkError({description:"OzNetworkRequestStream("+b.$1+") HTTP status not OK",responseStatus:a,responseHeaders:f,requestUrl:h,responseBody:typeof g==="string"?g:void 0}));typeof g!=="string"&&typeof g.getReader==="function"||c("oz-player/shims/ozvariant")(0,3287);i=g;for(a=0;a<e.length;a++)i=i.pipeThrough(e[a]);return i},function(a){var e=[];for(var f=0;f<b.$11.length;f++)try{var g=b.$11[f].onError(a,h);g&&e.push(g)}catch(a){g=a;return c("oz-player/networks/OzCreateErrorStream")(g)}if(a instanceof TypeError){g=c("oz-player/networks/OzCreateErrorStream")(d("oz-player/utils/OzErrorUtils").createOzNetworkError({description:"OzNetworkRequestStream("+b.$1+") TypeError: "+a.message,responseStatus:0,responseHeaders:null,requestUrl:h}));for(f=0;f<e.length;f++)g=g.pipeThrough(e[f]);return g}throw a})["catch"](function(a){if(d("oz-player/utils/OzErrorUtils").isOzError(a)&&a.getType()==="OZ_NETWORK_REQUEST_STREAM_RETRY_HANDLER_ERROR")throw a;if(a.name==="AbortError"){var b=a.message;return c("oz-player/networks/OzCreateErrorStream")({type:a.name,status:a.code,url:h,message:b})}return c("oz-player/networks/OzCreateErrorStream")({type:"stream_processing",status:0,url:h,message:"Stream processing error. "+a})})})};e.$22=function(a){this.$6=a};e.getLastPerformanceEntry=function(){if(this.$6!=null)return this.$6;return this.$5==null?null:d("oz-player/utils/OzResourceTimingUtils").getLatestResourceTimingEntry(this.$5)};e.getLastRequestUrl=function(){return this.$5};e.getLastResponse=function(){return this.$7};e.retrievedFromCache=function(){return this.$8};e.$23=function(){var a=this;if(this.$15.getBool("video_cdn_url_refresh")&&!d("oz-player/utils/OzVideoUrlUtils").isShortenedExpiryTimestampCdnUrl(this.$2.toString())&&d("oz-player/utils/OzVideoUrlUtils").isCdnUrlExpired(this.$2.toString())&&this.$13)return this.$13(this.$2.toString()).then(function(b){b.refreshedUrl!=null&&b.reason==="OK"&&(a.$2=new(c("oz-player/shims/OzURI"))(b.refreshedUrl));return});else return(h||(h=b("Promise"))).resolve()};return a}();g["default"]=a}),98);
__d("oz-player/manifests/OzByteRange",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){return a.startByte===b.startByte}function b(a,b){return b.endByte===null?!1:a.startByte===b.endByte+1}function c(a,b){return a.startByte<b.startByte?!1:b.endByte==null||b.endByte>=a.startByte}function d(a,b){a=a;b=b;if(b.startByte<a.startByte){var c=a;a=b;b=c}if(a.endByte==null)return{startByte:a.startByte,endByte:null};if(b.startByte>a.endByte+1)return null;c=b.endByte==null||b.endByte>a.endByte?b.endByte:a.endByte;return{startByte:a.startByte,endByte:c}}function e(a,b){if(b.endByte==null)return null;if(a.startByte>b.endByte)return{startByte:a.startByte,endByte:a.endByte};return a.endByte!=null&&a.endByte<=b.endByte?null:{startByte:b.endByte+1,endByte:a.endByte}}function g(a){return a.endByte==null?null:a.endByte-a.startByte+1}f.startsAtSame=a;f.startsImmediateAfter=b;f.startsDuring=c;f.union=d;f.disjoinAfter=e;f.getLength=g}),66);
__d("oz-player/networks/OzProducerInterruptedError",[],(function(a,b,c,d,e,f){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}return b}(babelHelpers.wrapNativeSuper(Error));f["default"]=a}),66);
__d("oz-player/networks/OzDeferredBuffer",["oz-player/networks/OzProducerInterruptedError","oz-player/shims/OzDeferred","oz-player/shims/OzMaybeNativePromise","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){a===void 0&&(a={});this.$2=[];this.$3=!1;this.$5=0;a=a;a=a.disableArrayShift;this.$1=!!a}var b=a.prototype;b.produce=function(a){this.$6(a)};b.signalProducerInterruption=function(){this.$6(new(c("oz-player/networks/OzProducerInterruptedError"))("producer interrupted"))};b.consume=function(a){var b=this;if(this.$3)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DEFERRED_BUFFER",description:"A buffer can only be consumed by one client at a time"});this.$3=!0;var e=c("oz-player/shims/OzMaybeNativePromise").resolve();this.isEmpty()&&(this.$4=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise")),e=this.$4.getPromise());return e.then(function(){if(b.$1){if(b.$2[b.$5]===void 0)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DEFERRED_BUFFER",description:"buffer has no value at position "+b.$5})}else if(b.$2.length===0)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_DEFERRED_BUFFER",description:"buffer length must not be 0"});b.$4=null;b.$3=!1;var c=b.$1?b.$7(a):b.$8(a);if(c instanceof Uint8Array)return c;throw c})};b.$6=function(a){this.$2.push(a),this.$4&&this.$4.resolve()};b.$7=function(a){var b=this.$2[this.$5];if(b===void 0)return new Uint8Array([]);b=b;if(!(b instanceof Uint8Array)){this.$2[this.$5]=void 0;this.$5++;return b}if(a!==void 0&&b.length>a){var c=b.slice(a);b=b.slice(0,a);this.$2[this.$5]=c}else this.$2[this.$5]=void 0,this.$5++;return b};b.$8=function(a){if(this.$2.length===0)return new Uint8Array([]);var b=this.$2[0];if(!(b instanceof Uint8Array)){this.$2.shift();return b}if(a!==void 0&&b.length>a){var c=b.slice(a);b=b.slice(0,a);this.$2[0]=c}else this.$2.shift();return b};b.isEmpty=function(){return this.$1?this.$2[this.$5]===void 0:this.$2.length===0};return a}();g["default"]=a}),98);
__d("oz-player/utils/ozPipeErrorTo",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){try{a==null?void 0:a.error(b)}catch(a){}}f["default"]=a}),66);
__d("oz-player/networks/OzPausableRangeStream",["oz-player/manifests/OzByteRange","oz-player/networks/OzDeferredBuffer","oz-player/shims/OzDeferred","oz-player/shims/OzMaybeNativePromise","oz-player/utils/OzErrorUtils","oz-player/utils/ozPipeErrorTo"],(function(a,b,c,d,e,f,g){"use strict";function h(){return d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_STREAM",description:"Upstream has an inconsistent range"})}var i=function(){function a(){this.$1=0}var b=a.prototype;b.setBytesToSkip=function(a){this.$1=a};b.setBytesSkipped=function(a){this.$1-=a};b.getBytesToSkip=function(){return this.$1};b.hasMoreBytesToSkip=function(){return this.$1>0};return a}();a=function(){function a(b,d,e,f,g){var j=this;this.$5=!1;this.$7=0;this.$8=0;this.$13=!1;this.$14=new i();this.$15=!1;this.$16=!1;this.$17=!1;this.startStream=function(){j.$13=!0;var b={startByte:j.$1.startByte,endByte:j.$1.endByte};!j.$15?b.startByte+=j.$8:j.$8&&(j.$3.produce("skip_buffered_bytes"),j.$8=0);var d=j.$4;return j.$2.startStream(b).then(function(b){b.pipeTo(j.$16?d:j.$4).then(function(){j.$12&&j.$12.resolve("stream_done")})["catch"](function(b){j.$12&&!j.$12.isSettled()&&(j.$16&&b===a.STREAM_PAUSED?j.$12.resolve("stream_paused"):j.$12.reject(b))});j.$12=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise"));return{statusPromise:j.$12.getPromise()}})};this.$18=b;this.$19=d;this.$1=e;this.$2=f;b=g||{};d=b.fixStreamingUndefinedEndByte;e=b.disableDeferredBufferArrayShift;f=b.enablePausableStreamResumeFromStartDangerously;g=b.fixPausePreReadableStream;b=b.throwErrorWhenAborted;this.$11=!!d;this.$15=!!f;this.$16=!!g;this.$17=!!b;this.$3=new(c("oz-player/networks/OzDeferredBuffer"))({disableArrayShift:!!e});this.$6=new this.$18({start:function(a){j.$10=a},pull:function(a){if((j.$11&&j.$1.endByte===null&&j.$5||j.$7===j.$20())&&j.$3.isEmpty()){a.close();return c("oz-player/shims/OzMaybeNativePromise").resolve()}var b=function b(){var d=j.$14.hasMoreBytesToSkip()?j.$14.getBytesToSkip():void 0;return j.$3.consume(d).then(function(d){if(j.$14.hasMoreBytesToSkip()){j.$14.setBytesSkipped(d.length);return b()}j.$7+=d.length;var e=j.$20();e!==null&&j.$7>(e||0)&&c("oz-player/utils/ozPipeErrorTo")(j.$10,h());a.enqueue(d)})["catch"](function(a){if(a==="skip_buffered_bytes"){j.$14.setBytesToSkip(j.$7);return b()}throw a})};return b()},cancel:function(a){j.$12&&j.$12.resolve("stream_cancelled"),c("oz-player/utils/ozPipeErrorTo")(j.$9,a)}});this.$4=this.$21()}var b=a.prototype;b.$20=function(){var a=this.$1,b=a.startByte;a=a.endByte;return a!=null?a-b+1:null};b.$21=function(){var a=this;return new this.$19({start:function(b){a.$9=b},write:function(b){a.$3.produce(b),a.$8+=b.length},close:function(){a.$5=!0;var b=a.$1,d=b.endByte;b=b.startByte;a.$11&&d===null&&a.$3.produce(new Uint8Array([]));d!==null&&a.$8!==(d||0)-b+1&&c("oz-player/utils/ozPipeErrorTo")(a.$10,h())},abort:function(b){a.$12&&(a.$17?a.$12.reject(b):a.$12.resolve("stream_aborted")),c("oz-player/utils/ozPipeErrorTo")(a.$10,b)}})};b.getStream=function(){return this.$6};b.pauseStream=function(){c("oz-player/utils/ozPipeErrorTo")(this.$9,a.STREAM_PAUSED),this.$12&&this.$12.resolve("stream_paused"),this.$4=this.$21()};b.getByteRange=function(){return this.$1};b.getBytesStreamed=function(){return this.$7};b.tryConcatByteRange=function(a){if(this.$13||!d("oz-player/manifests/OzByteRange").startsImmediateAfter(a,this.$1))return!1;this.$1={startByte:this.$1.startByte,endByte:a.endByte};return!0};return a}();a.STREAM_PAUSED="streamPaused";g["default"]=a}),98);
__d("oz-player/networks/RequestParamCreator",["oz-player/shims/OzURI"],(function(a,b,c,d,e,f,g){"use strict";a=function(a,b){var d=this;this.createRequestParam=function(a,b,e){var f=new(c("oz-player/shims/OzURI"))(a.toString());if(b){a=b.startByte;var g=b.endByte;a===0&&g==null||(f.addQueryData({bytestart:b.startByte}),g!=null&&f.addQueryData({byteend:g}))}if(d.$2){var h=d.$2(f);h&&Object.keys(h).forEach(function(a){var b=h[a];f.addQueryData(a,b)})}a=d.$1&&d.$1(f)?"include":"same-origin";return{uri:f,http:{credentials:a,priority:e}}};this.$1=a;this.$2=b};g["default"]=a}),98);
__d("oz-player/networks/getOzSegmentStreamableRange",[],(function(a,b,c,d,e,f){"use strict";function a(a){if(!a.length)return null;var b=a[0].getURI().toString(),c=a[0].getByteRange();if(!c)return null;var d=c;for(var e=1;e<a.length;e++){var f=a[e];if(f.getURI().toString()!==b)return null;f=f.getByteRange();if(!d||!f)return null;if(d.endByte===null||f.startByte!==d.endByte+1)return null;d=f}return{startByte:c.startByte,endByte:d.endByte}}f["default"]=a}),66);
__d("oz-player/utils/OzNetworkRequestLoggingUtils",["oz-player/shims/OzNetworkDiagnostics","oz-player/utils/OzResourceTimingUtils"],(function(a,b,c,d,e,f,g){"use strict";var h=function(a){return a?parseInt(a.get("content-length"),10):null},i=function(a){return a?a.get("x-fb-fna-hit"):null},j=function(a){return a?a.get("x-fb-edge-hit"):null},k=function(a){return a?a.get("x-fb-origin-hit"):null},l=function(a){return a?parseInt(a.get("x-fb-dynamic-latest-segment-id"),10):null},m=function(a,b){b=d("oz-player/utils/OzResourceTimingUtils").getLatestResourceTimingEntry(b);b&&a.setTimeToFirstByte(Math.round(b.responseStart-b.startTime)).setTimeToLastByte(Math.round(b.responseEnd-b.startTime)).setTimeToRequestStart(Math.round(b.requestStart-b.startTime))};a=function(a,b,d,e,f,g){d.length&&a.setSegmentCount(d.length),b!=null&&b!==""&&m(a,String(b)),a.setResource(b!=null&&b!==""?b:null).setInitiator("fetch").setTimeToRequestSent(Math.round(Math.floor(e))).setLiveheadSeqNumHeader(l(g==null?void 0:g.headers)).setContentLengthHeader(h(g==null?void 0:g.headers)).setEdgeHitHeader(j(g==null?void 0:g.headers)).setOriginHitHeader(k(g==null?void 0:g.headers)).setFNAHitHeader(i(g==null?void 0:g.headers)).setResponseTimeMsHeader(c("oz-player/shims/OzNetworkDiagnostics").getResponseTimeMs(g==null?void 0:g.headers)).setUserInfo({segmentPTSHeader:String(c("oz-player/shims/OzNetworkDiagnostics").getDvlSegmentPTS(g==null?void 0:g.headers))}).setCode(g==null?void 0:g.status).setLength(f)};b=function(a,b,d,e,f,g,i){b!=null&&b!==""&&m(a,String(b)),a.setResource(b!=null&&b!==""?b:null).setLiveheadSeqNumHeader(l(e==null?void 0:e.headers)).setContentLengthHeader(h(e==null?void 0:e.headers)).setEdgeHitHeader(j(e==null?void 0:e.headers)).setOriginHitHeader(k(e==null?void 0:e.headers)).setResponseTimeMsHeader(c("oz-player/shims/OzNetworkDiagnostics").getResponseTimeMs(e==null?void 0:e.headers)).setCode(e==null?void 0:e.status).setLength(d).setClientTimeBegin(f).setClientTimeDuration(g-f).setClientTimeEnd(g).setUserInfo(i!=null&&i>0?{chunkSize:String(i)}:null)};g.setPerformanceLoggingAttributes=m;g.setFetchStreamLoggingAttributes=a;g.setBandwidthSampledLoggingAttributes=b}),98);
__d("oz-player/networks/OzNetworkManager",["oz-player/loggings/OzLoggingUtils","oz-player/networks/OzNetworkRequestStream","oz-player/networks/OzPausableRangeStream","oz-player/networks/RequestParamCreator","oz-player/networks/getOzSegmentStreamableRange","oz-player/shims/OzStreams","oz-player/shims/OzURI","oz-player/shims/ozvariant","oz-player/utils/OzNetworkRequestLoggingUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){this.$10=null;this.$15="auto";var b=a.config,d=a.prefetchCache,e=a.networkRequestStreamHandlers,f=a.getOverrideOzRequestImplementation,g=a.getShouldIncludeCredentials,h=a.getCustomRequestParametersForURI,i=a.networkRequestFetchPriority;i=i===void 0?"auto":i;var j=a.networkRequestStreamRetryHandler,k=a.networkRequestUrlRefreshHandler,l=a.setCustomFetchStreamLoggingAttributes,m=a.dynamicVideoLibrary,n=a.configureCustomRequestParametersForSegment,o=a.onResourceTimingBufferFull;a=a.bandwidthEstimator;this.$2=b;this.$8=g;this.$9=h;this.$1=new(c("oz-player/networks/RequestParamCreator"))(this.$8,this.$9);this.$3=d;this.$4=e;this.$5=j;this.$6=k;this.$7=f;this.$10=l;this.$11=m;this.$12=n;this.$13=o;this.$14=a;this.$15=i}var b=a.prototype;b.destroy=function(){};b.request=function(a){var b=a.debugName,d=a.segments,e=a.pipeThroughRangeStreamProviders,f=a.loggerProvider,g=a.requestOptions,h=a.mediaStreamType,i=a.dataAppendedCallback;a=a.dataAppendedErrorCallback;d.length>0||c("oz-player/shims/ozvariant")(0,212);var j=c("oz-player/networks/getOzSegmentStreamableRange")(d)||{startByte:0,endByte:null},k=this.$7?this.$7():null,l=this.$16(d[0]);b=new(c("oz-player/networks/OzNetworkRequestStream"))({debugName:b,config:this.$2,baseUri:l,baseUriDecisionTime:null,options:g,requestParamCreator:this.$1,networkRequestFetchPriority:this.$15,networkRequestStreamHandlers:this.$4,networkRequestUrlRefreshHandler:this.$6,overrideOzRequestImplementation:k,prefetchCache:this.$3,networkRequestStreamRetryHandler:this.$5,mediaStreamType:h,dataAppendedCallback:i,dataAppendedErrorCallback:a});return this.$17(j,b,e,f,d)};b.createPausableStream=function(a){var b=a.debugName,d=a.segments,e=a.pipeThroughRangeStreamProviders,f=a.loggerProvider,g=a.mediaStreamType,h=a.dataAppendedCallback;a=a.dataAppendedErrorCallback;d.length>0||c("oz-player/shims/ozvariant")(0,212);var i=c("oz-player/networks/getOzSegmentStreamableRange")(d)||{startByte:0,endByte:null},j=null,k=this.$2.getNumber("network_seg_timeout_ms");k>0&&(j={networkTimeoutMs:k});k=this.$7?this.$7():null;var l=new(c("oz-player/networks/OzNetworkRequestStream"))({debugName:b,config:this.$2,baseUri:this.$16(d[0]),baseUriDecisionTime:d[0].getOptions().getSegmentNumDecisionTime(),options:j,requestParamCreator:this.$1,networkRequestFetchPriority:this.$15,networkRequestStreamHandlers:this.$4,networkRequestUrlRefreshHandler:this.$6,overrideOzRequestImplementation:k,prefetchCache:this.$3,networkRequestStreamRetryHandler:this.$5,dynamicVideoLibrary:this.$11,mediaStreamType:g,dataAppendedCallback:h,dataAppendedErrorCallback:a});return{pausableStream:this.$18(i,l,e||null,f),loggingPayloads:{getRequestUrl:function(){return l.getLastRequestUrl()||null},segments:d,getResponse:function(){return l.getLastResponse()}}}};b.requestRawUrl=function(a){var b=a.debugName,d=a.url,e=a.options;a=a.loggerProvider;var f=this.$7?this.$7():null;b=new(c("oz-player/networks/OzNetworkRequestStream"))({debugName:b,config:this.$2,baseUri:new(c("oz-player/shims/OzURI"))(d),baseUriDecisionTime:null,options:e,networkRequestFetchPriority:this.$15,requestParamCreator:this.$1,networkRequestStreamHandlers:this.$4,networkRequestUrlRefreshHandler:this.$6,overrideOzRequestImplementation:f,prefetchCache:this.$3,networkRequestStreamRetryHandler:this.$5,dynamicVideoLibrary:this.$11,mediaStreamType:null,dataAppendedCallback:null,dataAppendedErrorCallback:null});d={startByte:0,endByte:null};return this.$17(d,b,[],a)};b.$18=function(a,b,e,f){b=b;if(e!=null)for(var g=0;g<e.length;g++){var h=e[g];b=h(this.$2,b,f,this.$13)}return new(c("oz-player/networks/OzPausableRangeStream"))(d("oz-player/shims/OzStreams").OzReadableStream,d("oz-player/shims/OzStreams").OzWritableStream,a,b,{fixStreamingUndefinedEndByte:!0,enablePausableStreamResumeFromStartDangerously:!0,fixPausePreReadableStream:!0,throwErrorWhenAborted:!0})};b.$17=function(a,b,c,e,f){var g=this;f===void 0&&(f=[]);var h=this.$18(a,b,c,e);a=h.startStream();var i=this.$19(a);if(e){c=function(a){d("oz-player/utils/OzNetworkRequestLoggingUtils").setFetchStreamLoggingAttributes(a,b.getLastRequestUrl(),f,0,h.getBytesStreamed(),b.getLastResponse());var c=g.$10;c&&c(a,b.getLastRequestUrl(),b.getLastResponse())};d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(i,e,"fetch_stream",c,c)}return{getStream:function(){return h.getStream()},cancel:function(){return h.pauseStream()},getStatusChangePromise:function(){return i}}};b.$19=function(a){return a.then(function(a){a=a.statusPromise;return a.then(function(a){return"done"})})};b.$16=function(a){var b=this.$12,d=new(c("oz-player/shims/OzURI"))(a.getURI().toString());if(b){var e=b(a);e&&Object.keys(e).forEach(function(a){var b=e[a];d.addQueryData(a,b)})}return d};b.getBandwidthEstimator=function(){return this.$14};a.getStreamableSegmentsRange=function(a){if(!a.length)return[];return!c("oz-player/networks/getOzSegmentStreamableRange")(a)?[a[0]]:a.slice(0)};return a}();g["default"]=a}),98);
__d("oz-player/manifests/Mpd",["oz-player/shims/OzEventEmitter","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d,e,f,g,h,i){var j;j=a.call(this)||this;j.$Mpd$p_9=!1;j.$Mpd$p_1=b;j.$Mpd$p_5=g;j.$Mpd$p_2=c;j.$Mpd$p_3=d;j.$Mpd$p_4=e;j.$Mpd$p_6=f;j.$Mpd$p_7=h;j.$Mpd$p_8=i;return j}var c=b.prototype;c.markRefreshed=function(){this.$Mpd$p_10=Date.now()};c.getRefreshDate=function(){return this.$Mpd$p_10};c.updateLocation=function(a){this.$Mpd$p_6=a,this.$Mpd$p_9=!0,this.emit("locationUpdated")};c.getLocation=function(){return this.$Mpd$p_6};c.getMinimumUpdatePeriod=function(){return this.$Mpd$p_5};c.getVideoRepresentations=function(){return this.$Mpd$p_2};c.getVideoRepresentationsByVariant=function(a){return a.lang===null?this.$Mpd$p_2.filter(function(b){return b.getLang()===a.lang&&b.getRole()===a.role}):this.$Mpd$p_2.filter(function(b){return b.getLang()===a.lang&&b.getRole()===a.role})};c.getAudioRepresentationsByVariant=function(a){return a.lang===null?this.$Mpd$p_3.filter(function(b){return b.getLang()===a.lang&&b.getRole()===a.role}):this.$Mpd$p_3.filter(function(b){return b.getLang()===a.lang&&b.getRole()===a.role})};c.getAudioRepresentations=function(){return this.$Mpd$p_3};c.getApplicationRepresentations=function(){return this.$Mpd$p_4};c.getCustomField=function(a){var b=this.$Mpd$p_7.get(a);if(!b)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_MPD_PARSER",description:"Custom parser not specified for field "+a});try{return b(this.$Mpd$p_1)}catch(a){return null}};c.isStaticMpd=function(){return this.$Mpd$p_8};c.updateWith=function(a){this.$Mpd$p_9&&(this.$Mpd$p_9=!1,this.$Mpd$p_2.splice(0,this.$Mpd$p_2.length),this.$Mpd$p_3.splice(0,this.$Mpd$p_3.length),this.$Mpd$p_4.splice(0,this.$Mpd$p_4.length)),this.$Mpd$p_1=a.$Mpd$p_1,this.$Mpd$p_11(this.$Mpd$p_2,a.getVideoRepresentations()),this.$Mpd$p_11(this.$Mpd$p_3,a.getAudioRepresentations()),this.$Mpd$p_11(this.$Mpd$p_4,a.getApplicationRepresentations()),this.$Mpd$p_8=a.isStaticMpd(),this.$Mpd$p_5=a.getMinimumUpdatePeriod(),this.emit("updated")};c.blockTimeRange=function(a){this.$Mpd$p_2.forEach(function(b){return b.blockTimeRange(a)}),this.$Mpd$p_3.forEach(function(b){return b.blockTimeRange(a)}),this.$Mpd$p_4.forEach(function(b){return b.blockTimeRange(a)})};c.unblockTimeRange=function(){this.blockTimeRange({startTime:0,endTime:0})};c.$Mpd$p_11=function(a,b){for(var c=0;c<b.length;c++){var d=b[c],e=!1;for(var f=0;f<a.length;f++){var g=a[f];if(d.getID()===g.getID()){g.updateWith(d);e=!0;break}}e||a.push(d)}};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/manifests/OzApplicationRepresentation",["oz-player/manifests/OzRepresentationBase"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(){return a.apply(this,arguments)||this}return b}(c("oz-player/manifests/OzRepresentationBase"));g["default"]=a}),98);
__d("oz-player/parsers/OzSegmentTemplateParser",["oz-player/manifests/OzSegmentOptions","oz-player/parsers/OzDefaultSegmentTimelineParser","oz-player/shims/OzURI","oz-player/utils/OzUrlHelper"],(function(a,b,c,d,e,f,g){"use strict";var h=function(){function a(a,b){this.$3=new(c("oz-player/manifests/OzSegmentOptions"))(),this.$2=a,this.$1=b}var b=a.prototype;b.getData=function(){return this.$1};b.getTimeRange=function(){return{startTime:0,endTime:0}};b.getByteRange=function(){return null};b.getURI=function(){return this.$2};b.getSequenceNumber=function(){return null};b.getOptions=function(){return this.$3};return a}();a=function(){function a(a,b,d,e,f){this.$2=d,this.$1=b,this.$3=a,e?this.$4=e:this.$4=new(c("oz-player/parsers/OzDefaultSegmentTimelineParser"))(),this.$5=f}var b=a.prototype;b.parseInitializationSegment=function(){var a=d("oz-player/utils/OzUrlHelper").joinUrlPaths(this.$2.baseUrl,this.$1.$.initialization);return new h(new(c("oz-player/shims/OzURI"))(a),this.$5)};b.parseSegmentsContainer=function(){return this.$4.parseSegmentsContainer(this.$3,this.$2,this.$1)};return a}();g["default"]=a}),98);
__d("oz-player/manifests/OzZeroTimeRangeSegment",["oz-player/manifests/OzSegmentOptions"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,d){this.$4=new(c("oz-player/manifests/OzSegmentOptions"))(),this.$1=a,this.$2=b,this.$3=d}var b=a.prototype;b.getData=function(){return null};b.getURI=function(){return this.$1};b.getTimeRange=function(){return{startTime:0,endTime:0}};b.getByteRange=function(){return{startByte:this.$2,endByte:this.$3}};b.getSequenceNumber=function(){return null};b.getOptions=function(){return this.$4};return a}();g["default"]=a}),98);
__d("oz-player/networks/OzNetworkRequestStreamBandwidthReporter",["oz-player/networks/OzTransformStream","oz-player/shims/OzNetworkDiagnostics","oz-player/utils/OzNetworkRequestLoggingUtils"],(function(a,b,c,d,e,f,g){"use strict";b=function(){function b(b){var d=this;this.$8=Date.now();this.$9=Date.now();this.$10=0;this.$11=0;this.$12=function(){};this.$13=function(){};this.$14=function(){};this.$16=new(c("oz-player/networks/OzTransformStream"))();this.$19=!1;this.$20=!1;this.$21=null;this.$22=function(a){d.$25();a.length!=null&&(d.$11+=a.length);d.$26();if(d.$18)return;d.$27()};this.$23=function(){if(d.$21!=null&&d.$21>0)return;var b=Date.now()-d.$8,c=d.$1.getLastPerformanceEntry();c&&d.$13(c.responseStart-c.requestStart,c.responseEnd-c.requestStart,d.$11);!c&&a.__isresourcetimingbufferfull&&d.$15&&d.$15();d.$3>0&&b>0&&d.$10===0&&d.$11>=d.$3&&d.$28(d.$11,b)};var e=b.stream,f=b.loggerProvider,g=b.byteCountPerSample,h=b.minimumBytesToSampleOnClose,i=b.minimumSampleDuration,j=b.maximumSampleBandwidth,k=b.useResponseTimeMs,l=b.responseTimeHandicapMs,m=b.ignoreOnStreamWriteSamples;b=b.onResourceTimingBufferFull;this.$1=e;this.$17=f;this.$2=g;this.$3=h;this.$4=i;this.$5=j;this.$6=k;this.$7=l;this.$18=m;this.$16.addListener("writableWrite",this.$22);this.$16.addListener("writableClose",this.$23);this.$15=b}var e=b.prototype;e.getTransformStream=function(){return this.$16};e.onBandwidthSample=function(a){this.$12=a};e.onNavigationTimingSample=function(a){this.$13=a};e.onEstimateFromHeaders=function(a){this.$14=a};e.$24=function(a,b,c){b=b-c;c=this.$17?this.$17.getOperationLogger("bandwidth_sampled"):null;if(c){var e=Date.now(),f=e-b;d("oz-player/utils/OzNetworkRequestLoggingUtils").setBandwidthSampledLoggingAttributes(c,this.$1.getLastRequestUrl(),a,this.$1.getLastResponse(),f,e,this.$21);c.log()}this.$12(a,b)};e.$29=function(a,b,c){if(b-c<=0)return!0;c=a*8/(b/1e3);return b<this.$4&&c>this.$5};e.$28=function(a,b){var d=this.$1.getLastResponse();d=this.$6?c("oz-player/shims/OzNetworkDiagnostics").getResponseTimeMs(d==null?void 0:d.headers)||0:0;this.$29(a,b,d)||this.$24(a,b,Math.max(d-this.$7,0))};e.$25=function(){var a;a=(a=this.$1.getLastResponse())==null?void 0:a.headers;if(this.$20||a==null)return;this.$20=!0;a=c("oz-player/shims/OzNetworkDiagnostics").getBandwidthMeanEstimate(a);a!=null&&this.$14(a)};e.$26=function(){if(this.$19)return;if(this.$21==null){var a=this.$1.getLastResponse();this.$21=c("oz-player/shims/OzNetworkDiagnostics").getUsableResponseSizeForBandwidthEstimation(a==null?void 0:a.headers)||0}this.$21!=null&&this.$21>0&&this.$11>=this.$21&&(this.$19=!0,this.$28(this.$11,Date.now()-this.$9))};e.$27=function(){var a=Date.now(),b=a-this.$8;this.$2>0&&this.$11>=this.$10+this.$2&&!this.$29(this.$11,b,0)&&(this.$24(this.$11-this.$10,b,0),this.$8=a,this.$10=this.$11)};return b}();g["default"]=b}),98);
__d("oz-player/networks/bandwidth/pipeStreamThroughBandwidthEstimator",["oz-player/networks/OzNetworkRequestStreamBandwidthReporter"],(function(a,b,c,d,e,f,g){"use strict";var h=2e5;function a(a,b,d,e,f){return{startStream:function(g){var i=new(c("oz-player/networks/OzNetworkRequestStreamBandwidthReporter"))({stream:b,loggerProvider:d,byteCountPerSample:a.getNumber("byte_count_per_sample",h),minimumBytesToSampleOnClose:a.getNumber("minimum_bytes_to_sample_on_close",25e3),minimumSampleDuration:a.getNumber("minimum_bandwidth_sample_duration",10),maximumSampleBandwidth:a.getNumber("maximum_bandwidth_sample_bandwidth",1e8),useResponseTimeMs:a.getBool("bandwidth_use_response_time_adjustment",!1),responseTimeHandicapMs:a.getNumber("bandwidth_response_time_handicap",0),ignoreOnStreamWriteSamples:a.getBool("bandwidth_ignore_on_stream_write_samples",!1),onResourceTimingBufferFull:f});i.onBandwidthSample(function(a,b){e.addBandwidthSample(a,b)});i.onNavigationTimingSample(function(b,c,d){var f=a.getNumber("time_to_first_byte_ignore_above_threshold_ms",0);(f===0||b<=f)&&e.addNavigationTimingSample(b,c,d)});i.onEstimateFromHeaders(function(a){e.setBandwidthEstimateFromHeaders({meanEstimate:a})});return b.startStream(g).then(function(a){return b.retrievedFromCache()?a:a.pipeThrough(i.getTransformStream())})}}}g.pipeStreamThroughBandwidthEstimator=a}),98);
__d("oz-player/networks/getOzBandwidthEstimatorPipeThroughReporter",["oz-player/networks/OzNetworkRequestStream","oz-player/networks/bandwidth/pipeStreamThroughBandwidthEstimator"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){return function(e,f,g,h){var i=a==="video";return f instanceof c("oz-player/networks/OzNetworkRequestStream")&&i?d("oz-player/networks/bandwidth/pipeStreamThroughBandwidthEstimator").pipeStreamThroughBandwidthEstimator(e.getLegacyConfig(),f,g,b,h):f}}g["default"]=a}),98);
__d("oz-player/manifests/OzDataSegment",["oz-player/manifests/OzSegmentOptions"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,d,e,f){this.$6=new(c("oz-player/manifests/OzSegmentOptions"))(),this.$1=a,this.$2=b,this.$3=d,this.$4=e,this.$5=f}var b=a.prototype;b.getData=function(){return null};b.getURI=function(){return this.$1};b.getTimeRange=function(){return{startTime:this.$2,endTime:this.$3}};b.getByteRange=function(){return{startByte:this.$4,endByte:this.$5}};b.getSequenceNumber=function(){return null};b.getOptions=function(){return this.$6};return a}();g["default"]=a}),98);
__d("oz-player/shims/www/OzDataViewReaderWWW",["DataViewReader"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("DataViewReader")}),98);
__d("oz-player/shims/OzDataViewReader",["oz-player/shims/www/OzDataViewReaderWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/OzDataViewReaderWWW")}),98);
/**
 * License: https://www.facebook.com/legal/license/k5cmJzUOK4Y/
 */
__d("oz-player/parsers/OzMp4SidxParser",["oz-player/manifests/OzDataSegment","oz-player/manifests/OzSegmentOptions","oz-player/shims/OzDataViewReader","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";var h=**********;function a(a,b,e,f){var g=new(c("oz-player/shims/OzDataViewReader"))(new DataView(f.buffer));f=g.readUint32();var i=g.readUint32();if(i!==h)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SIDX_PARSER",description:'Invalid box type, expected "sidx".'});f==1&&(f=g.readUint64());i=g.readUint8();g.skip(3);g.skip(4);var j=g.readUint32();if(!j)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SIDX_PARSER",description:"Invalid timescale."});var k;i==0?(i=g.readUint32(),k=g.readUint32()):(i=g.readUint64(),k=g.readUint64());g.skip(2);var l=g.readUint16(),m=i,n=e+f+k,o=0,p=a.getBool("sidx_parser_memory_optimization"),q=[];i=function(a){var e=g.readUint32(),f=(e&**********)>>>31;e=e&**********;var h=g.readUint32();g.readUint32();if(f==1)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SIDX_PARSER",description:"Heirarchical SIDXs are not supported."});var i=m/j,k=(m+h)/j;a===0&&(o=i);var l=n,r=l+e-1,s=new(c("oz-player/manifests/OzSegmentOptions"))();q.push(p?new(c("oz-player/manifests/OzDataSegment"))(b,i-o,k-o,l,r):{getData:function(){return null},getURI:function(){return b},getTimeRange:function(){return{startTime:i-o,endTime:k-o}},getByteRange:function(){return{startByte:l,endByte:r}},getSequenceNumber:function(){return null},getOptions:function(){return s}});m+=h;n+=e};for(e=0;e<l;e++)i(e);return q}g.parse=a}),98);
__d("oz-player/parsers/OzSidxSegmentsContainer",["oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){this.$1=a,this.$2=b}var b=a.prototype;b.getSegmentByTime=function(a){var b=this.$1();for(var c=0;c<b.length;c++){var d=b[c],e=d.getTimeRange();if(e.startTime<=a&&e.endTime>a)return d}return null};b.getSegmentAfter=function(a){var b=this.$1(),c=b.findIndex(function(b){return b.getTimeRange().startTime===a.getTimeRange().startTime&&b.getTimeRange().endTime===a.getTimeRange().endTime});return c>=0&&c+1<b.length?b[c+1]:null};b.getPredictedSegmentAfter=function(a){return null};b.canPredict=function(){return!1};b.canApproximateId=function(){return!1};b.isEndingSegment=function(a){var b=this.$1();if(!b||!b.length)return!1;b=b[b.length-1].getTimeRange();a=a.getTimeRange();return b.startTime===a.startTime&&b.endTime===a.endTime};b.getSegment=function(a){return this.$1()[a]||null};b.updateWith=function(a){throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_NOT_IMPLEMENTED_ERROR",description:"Not implemented: updatedWith"})};b.addUpdateListener=function(a){return this.$2.addListener("segment_updated",a)};b.getTimeRanges=function(){var a=this.$1();if(a.length===0)return[];else{var b=a[0];a=a[a.length-1];return[{startTime:b.getTimeRange().startTime,endTime:a.getTimeRange().endTime}]}};b.blockTimeRange=function(){};b.getEndingSegment=function(){var a=this.$1();return a.length>0?a[a.length-1]:null};b.getMaxGopSec=function(){return null};return a}();g["default"]=a}),98);
/**
 * License: https://www.facebook.com/legal/license/k5cmJzUOK4Y/
 */
__d("oz-player/parsers/OzWebmSidxParser",["oz-player/manifests/OzDataSegment","oz-player/shims/OzDataViewReader","oz-player/shims/ozvariant"],(function(a,b,c,d,e,f,g){"use strict";var h=440786851,i=408125543,j=357149030,k=2807729,l=17545,m=475249515,n=187,o=179,p=183,q=241,r=[new Uint8Array([255]),new Uint8Array([127,255]),new Uint8Array([63,255,255]),new Uint8Array([31,255,255,255]),new Uint8Array([15,255,255,255,255]),new Uint8Array([7,255,255,255,255,255]),new Uint8Array([3,255,255,255,255,255,255]),new Uint8Array([1,255,255,255,255,255,255,255])];function s(a,b){if(!a&&!b)return!0;if(!a||!b)return!1;if(a.length!=b.length)return!1;for(var c=0;c<a.length;++c)if(a[c]!=b[c])return!1;return!0}var t=function(){function a(a,b){this.id=a,this.$1=b}var b=a.prototype;b.getOffset=function(){return this.$1.byteOffset};b.createParser=function(){return new u(this.$1)};b.getUint=function(){if(this.$1.byteLength>8)throw new RangeError("EbmlElement: Unsigned integer has too many bytes.");if(this.$1.byteLength==8&&this.$1.getUint8(0)&224)throw new RangeError("EbmlParser: Unsigned integer must be at most 53 bits.");var a=0;for(var b=0;b<this.$1.byteLength;b++){var c=this.$1.getUint8(b);a=256*a+c}return a};b.getFloat=function(){if(this.$1.byteLength==4)return this.$1.getFloat32(0);else if(this.$1.byteLength==8)return this.$1.getFloat64(0);else throw new RangeError("EbmlElement: floating point numbers must be 4 or 8 bytes.")};return a}(),u=function(){function a(a){this.$1=a,this.$2=new(c("oz-player/shims/OzDataViewReader"))(this.$1)}var b=a.prototype;b.hasMoreData=function(){return this.$2.hasMoreData()};b.parseElement=function(){var b=this.$3(),c=this.$4(),d;a.$5(c)?d=this.$1.byteLength-this.$2.getCursor():d=a.$6(c);c=this.$2.getCursor()+d<=this.$1.byteLength?d:this.$1.byteLength-this.$2.getCursor();d=new DataView(this.$1.buffer,this.$1.byteOffset+this.$2.getCursor(),c);this.$2.skip(c);return new t(b,d)};b.$3=function(){var a=this.$4();if(a.length>7)throw new RangeError("EbmlParser: EBML ID must be at most 7 bytes.");var b=0;for(var c=0;c<a.length;c++)b=256*b+a[c];return b};b.$4=function(){var a=this.$2.readUint8(),b;for(b=1;b<=8;b++){var c=1<<8-b;if(a&c)break}if(b>8)throw new RangeError("EbmlParser: Variable sized integer must fit within 8 bytes.");c=new Uint8Array(b);c[0]=a;for(a=1;a<b;a++)c[a]=this.$2.readUint8();return c};a.$6=function(a){if(a.length==8&&a[1]&224)throw new RangeError("EbmlParser: Variable sized integer value must be at most 53 bits.");var b=1<<8-a.length;b=a[0]&b-1;for(var c=1;c<a.length;c++)b=256*b+a[c];return b};a.$5=function(a){for(var b=0;b<r.length;b++)if(s(a,r[b]))return!0;return!1};return a}();function v(a){a=a.createParser();var b=1e6,c=null;while(a.hasMoreData()){var d=a.parseElement();d.id==k?b=d.getUint():d.id==l&&(c=d.getFloat())}if(c==null)return null;d=b/1e9;a=c*d;return{timecodeScale:d,duration:a}}function w(a,b,d,e,f,g){a=[];b=b.createParser();var h=-1,i=-1;while(b.hasMoreData()){var j=b.parseElement();if(j.id!=n)continue;j=x(j);if(!j)continue;var k=e*j.unscaledTime;j=d+j.relativeOffset;h>=0&&(i>=0||c("oz-player/shims/ozvariant")(0,4010),a.push(new(c("oz-player/manifests/OzDataSegment"))(g,h,k,i,j-1)));h=k;i=j}if(h>=0){i>=0||c("oz-player/shims/ozvariant")(0,4010);k=f;a.push(new(c("oz-player/manifests/OzDataSegment"))(g,h,k,i,null))}return a}function x(a){a=a.createParser();var b=a.parseElement();if(b.id!=o)return null;b=b.getUint();a=a.parseElement();if(a.id!=p)return null;a=a.createParser();var c=0;while(a.hasMoreData()){var d=a.parseElement();if(d.id!=q)continue;c=d.getUint();break}return{unscaledTime:b,relativeOffset:c}}function y(a){a=a.createParser();var b=null;while(a.hasMoreData()){var c=a.parseElement();if(c.id!=j)continue;b=c;break}return!b?null:v(b)}function z(a){a=new u(a);var b=a.parseElement();if(b.id!=h)return null;b=a.parseElement();if(b.id!=i)return null;a=b.getOffset();b=y(b);return!b?null:{segmentOffset:a,timecodeScale:b.timecodeScale,duration:b.duration}}function a(a,b,c,d){d=new DataView(d.buffer);c=new DataView(c.buffer);d=z(d);if(!d)return[];c=new u(c);c=c.parseElement();return c.id!=m?[]:w(a,c,d.segmentOffset,d.timecodeScale,d.duration,b)}g.parse=a}),98);
__d("oz-player/parsers/OzSidxSegmentsParser",["oz-player/loggings/OzLoggingUtils","oz-player/manifests/OzSegmentOptions","oz-player/manifests/OzZeroTimeRangeSegment","oz-player/networks/getOzBandwidthEstimatorPipeThroughReporter","oz-player/parsers/OzMp4SidxParser","oz-player/parsers/OzSidxSegmentsContainer","oz-player/parsers/OzWebmSidxParser","oz-player/shims/OzEventEmitter","oz-player/shims/OzMaybeNativePromise","oz-player/shims/OzURI","oz-player/utils/OzErrorEmitter","oz-player/utils/OzReadableStreamUtils"],(function(a,b,c,d,e,f,g){"use strict";function h(a){a=a.split("-");return{startByte:Number.parseInt(a[0],10),endByte:Number.parseInt(a[1],10)}}a=function(){function a(a,b,d,e,f,g,h){this.$6=new(c("oz-player/utils/OzErrorEmitter"))(),this.$7=[],this.$9=new(c("oz-player/shims/OzEventEmitter"))(),this.$1=b,this.$2=new(c("oz-player/shims/OzURI"))(d),this.$3=e,this.$5=f,this.$4=g,this.$8=h.cloneContext().setType(f==="video"||f==="audio"?f+";sidx":"sidx"),this.$10=a}var b=a.prototype;b.onError=function(a){return this.$6.onError(a)};b.parseInitializationSegment=function(){var a=this,b=this.$1.Initialization[0].$.range,d=h(b),e=new(c("oz-player/manifests/OzSegmentOptions"))();return this.$10.getBool("sidx_parser_memory_optimization")?new(c("oz-player/manifests/OzZeroTimeRangeSegment"))(this.$2,d.startByte,d.endByte):{getData:function(){return null},getURI:function(){return a.$2},getTimeRange:function(){return{startTime:0,endTime:0}},getByteRange:function(){return d},getSequenceNumber:function(){return null},getOptions:function(){return e}}};b.parseSegmentsContainer=function(){var a=this,b=this.$11();d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(b,this.$8,"process_sidx");b["catch"](function(b){a.$6.emitError(b)});return new(c("oz-player/parsers/OzSidxSegmentsContainer"))(function(){return a.$7},this.$9)};b.$12=function(a,b,e){a=new(c("oz-player/manifests/OzZeroTimeRangeSegment"))(a,b.startByte,b.endByte);b=this.$5+";sidx";a=this.$4.request({debugName:"OzSidxSegmentsParser/sidx/"+this.$5,segments:[a],pipeThroughRangeStreamProviders:[c("oz-player/networks/getOzBandwidthEstimatorPipeThroughReporter")(b,this.$4.getBandwidthEstimator())],loggerProvider:this.$8,requestOptions:{networkTimeoutMs:null,retryAttempts_SIDX_USE_ONLY:this.$10.getNumber("sidx_segment_retry_attempts"),retryTimeoutMs_SIDX_USE_ONLY:this.$10.getNumber("sidx_segment_retry_interval_ms")},mediaStreamType:e});b=a.getStream().getReader();return d("oz-player/utils/OzReadableStreamUtils").pumpAllData(b)};b.$11=function(){var a=this,b=this.$1.$.indexRange,e=h(b);b=[this.$12(this.$2,e,null)];if(this.$3==="webm"){var f=h(this.$1.Initialization[0].$.range);b.push(this.$12(this.$2,f,null));f=c("oz-player/shims/OzMaybeNativePromise").all(b).then(function(b){var c=b[0];b=b[1];a.$7=d("oz-player/parsers/OzWebmSidxParser").parse(a.$10,a.$2,c,b)})}else f=b[0].then(function(b){a.$7=d("oz-player/parsers/OzMp4SidxParser").parse(a.$10,a.$2,e.startByte,b)});return f.then(function(){a.$9.emit("segment_updated")})};return a}();g["default"]=a}),98);
__d("oz-player/parsers/OzVideoContainerType",[],(function(a,b,c,d,e,f){"use strict";function a(a){return(a.split("/")[1]||"").trim()}f.parse=a}),66);
__d("oz-player/parsers/OzRepresentationParserBase",["oz-player/manifests/OzRepresentationBase","oz-player/parsers/OzSegmentTemplateParser","oz-player/parsers/OzSidxSegmentsParser","oz-player/parsers/OzVideoContainerType","oz-player/utils/OzErrorEmitter","oz-player/utils/OzErrorUtils","oz-player/utils/OzMimeUtil"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,d,e,f,g,h,i,j,k,l,m,n,o,p){this.$9=new(c("oz-player/utils/OzErrorEmitter"))();this.$1=b;this.$2=d;this.$10=f;this.$11=g;this.$13=e;this.$14=a;this.$15=h;this.$3=i;this.$4=j;this.$5=k;this.$6=l!=null?l:null;this.$7=m!=null?m:null;this.$8=n!=null?n:null;this.$16=o;this.$17=p;try{this.$11=this.$11.cloneContext().setRepresentationID(this.parseID())}catch(a){}}var b=a.prototype;b.$18=function(){var a=this,b=this.$12;if(b)return b;if(this.$1.SegmentBase){var e=this.$1.SegmentBase[0],f=this.$1.BaseURL[0]._;if(typeof f!=="string")throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_REPRESENTATION_PARSER",description:"Missing representation BaseURL",extra:{code:"OZ_RP-9"}});b=new(c("oz-player/parsers/OzSidxSegmentsParser"))(this.$14,e,f,d("oz-player/parsers/OzVideoContainerType").parse(this.$1.$.mimeType),d("oz-player/utils/OzMimeUtil").getMimeType(this.$1.$.mimeType),this.$10,this.$11);b.onError(function(b){var c;if((c=a.$17)==null?void 0:c.handleError(a.parseID(),b))return;return a.$9.emitError(b)})}else if(this.$1.SegmentTemplate)b=this.$19(this.$1.SegmentTemplate[0],{isShared:!1});else if(this.$3)b=this.$19(this.$3[0],{isShared:!0});else throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_REPRESENTATION_PARSER",description:"Unrecognized representation type",extra:{code:"OZ_RP-7"}});this.$12=b;return b};b.$19=function(a,b){var e;a=a;b.isShared&&(a=babelHelpers["extends"]({},a,{$:babelHelpers["extends"]({},a.$,{initialization:a.$.initialization.replace("$RepresentationID$",this.$1.$.id),media:a.$.media.replace("$RepresentationID$",this.$1.$.id)})}));b=this.$13.mpdUrl;var f=this.$1.BaseURL&&this.$1.BaseURL[0]?this.$1.BaseURL[0]._:null;f=f!=null?f:b;if(f==null)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_REPRESENTATION_PARSER",description:"Missing mpd url for template manifest",extra:{code:"OZ_RP-6"}});var g=this.$13.customSegmentTimelineParser;e=(e=this.$13.customRepresentationParsers)==null?void 0:e.initializationBinary;e=e?e(this.$1):null;return new(c("oz-player/parsers/OzSegmentTemplateParser"))(this.$14,a,{baseUrl:f,mpdUrl:b,isStaticMpd:this.$15.manifestType==="static",isTemplatedMpd:this.$15.manifestIsTemplated},g,e)};b.onError=function(a){return this.$9.onError(a)};b.parseInitSegment=function(){return this.$18().parseInitializationSegment()};b.parseBandwidth=function(){return Number(this.$1.$.bandwidth)};b.parseMimeType=function(){var a;return(a=(a=this.$1.$.mimeType)!=null?a:this.$4)!=null?a:""};b.parseCodecs=function(){var a;return(a=(a=this.$1.$.codecs)!=null?a:this.$5)!=null?a:""};b.parseVariantKey=function(){return this.$6};b.parseLang=function(){return this.$7};b.parseRole=function(){return this.$8};b.parseSegmentsContainer=function(){return this.$18().parseSegmentsContainer()};b.parseID=function(){return""+this.$1.$.id};b.parseCustomFieldFirstSegment=function(){var a=this.$13.customRepresentationParsers?this.$13.customRepresentationParsers.firstSegmentParser:null;return a?a(this.$1):null};b.parse=function(){return new(c("oz-player/manifests/OzRepresentationBase"))(this.$1,this.$2,this.parseID(),this.parseMimeType(),this.parseCodecs(),this.parseVariantKey(),this.parseLang(),this.parseRole(),this.parseInitSegment(),this.parseSegmentsContainer(),this.parseBandwidth(),this.parseCustomFieldFirstSegment(),this.$13.customRepresentationParsers)};return a}();g["default"]=a}),98);
__d("oz-player/parsers/OzApplicationRepresentationParser",["oz-player/manifests/OzApplicationRepresentation","oz-player/parsers/OzRepresentationParserBase"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d,e,f,g,h,i,j,k,l,m,n,o){b=a.call(this,b,c,d,e,f,g,h,i,j,k,l,m,n,o)||this;b.$OzApplicationRepresentationParser$p_1=c;b.$OzApplicationRepresentationParser$p_3=e;return b}var d=b.prototype;d.parse=function(){var a=this.$OzApplicationRepresentationParser$p_3.customRepresentationParsers;return new(c("oz-player/manifests/OzApplicationRepresentation"))(this.$OzApplicationRepresentationParser$p_1,this.$OzApplicationRepresentationParser$p_2,this.parseID(),this.parseMimeType(),this.parseCodecs(),this.parseVariantKey(),this.parseLang(),this.parseRole(),this.parseInitSegment(),this.parseSegmentsContainer(),this.parseBandwidth(),this.parseCustomFieldFirstSegment(),a)};return b}(c("oz-player/parsers/OzRepresentationParserBase"));g["default"]=a}),98);
__d("oz-player/manifests/OzAudioRepresentation",["oz-player/manifests/OzRepresentationBase"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d,e,f,g,h,i,j,k,l,m,n){b=a.call(this,b,c,d,e,f,g,h,i,j,k,l,m,n)||this;b.$OzAudioRepresentation$p_1=d;b.$OzAudioRepresentation$p_2=l;b.$OzAudioRepresentation$p_3={id:d,displayLabel:b.getDisplayLabel(),mimeCodecs:b.getMimeCodecs(),variantKey:g,lang:h,role:i};return b}var c=b.prototype;c.getBitrateKbps=function(){var a=1024;return(this.$OzAudioRepresentation$p_2/a).toFixed(1)+"Kbps"};c.getDisplayLabel=function(){var b=a.prototype.getLang.call(this),c=a.prototype.getRole.call(this);b=b==null?"Default":b;return c==null?b:b+" - "+c};c.getAudioTrack=function(){return this.$OzAudioRepresentation$p_3};return b}(c("oz-player/manifests/OzRepresentationBase"));g["default"]=a}),98);
__d("oz-player/parsers/OzAudioRepresentationParser",["oz-player/manifests/OzAudioRepresentation","oz-player/parsers/OzRepresentationParserBase"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d,e,f,g,h,i,j,k,l,m,n,o){b=a.call(this,b,c,d,e,f,g,h,i,j,k,l,m,n,o)||this;b.$OzAudioRepresentationParser$p_1=c;b.$OzAudioRepresentationParser$p_3=e;return b}var d=b.prototype;d.parse=function(){var a=this.$OzAudioRepresentationParser$p_3.customRepresentationParsers;return new(c("oz-player/manifests/OzAudioRepresentation"))(this.$OzAudioRepresentationParser$p_1,this.$OzAudioRepresentationParser$p_2,this.parseID(),this.parseMimeType(),this.parseCodecs(),this.parseVariantKey(),this.parseLang(),this.parseRole(),this.parseInitSegment(),this.parseSegmentsContainer(),this.parseBandwidth(),this.parseCustomFieldFirstSegment(),a)};return b}(c("oz-player/parsers/OzRepresentationParserBase"));g["default"]=a}),98);
__d("oz-player/parsers/OzVideoRepresentationParser",["oz-player/manifests/OzVideoRepresentation","oz-player/parsers/OzRepresentationParserBase"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c,d,e,f,g,h,i,j,k,l,m,n,o,p){b=a.call(this,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p)||this;b.$OzVideoRepresentationParser$p_1=c;b.$OzVideoRepresentationParser$p_2=d;b.$OzVideoRepresentationParser$p_3=e;return b}var d=b.prototype;d.parse=function(){var a=Number.parseInt(this.$OzVideoRepresentationParser$p_1.$.height,10),b=Number.parseInt(this.$OzVideoRepresentationParser$p_1.$.width,10),d=this.$OzVideoRepresentationParser$p_3.customRepresentationParsers;return new(c("oz-player/manifests/OzVideoRepresentation"))(this.$OzVideoRepresentationParser$p_1,this.$OzVideoRepresentationParser$p_2,this.parseID(),this.parseMimeType(),this.parseCodecs(),this.parseVariantKey(),this.parseLang(),this.parseRole(),this.parseInitSegment(),this.parseSegmentsContainer(),this.parseBandwidth(),this.parseCustomFieldFirstSegment(),d,b,a)};return b}(c("oz-player/parsers/OzRepresentationParserBase"));g["default"]=a}),98);
__d("oz-player/utils/OzIbrUtils",["oz-player/networks/OzBandwidthEstimator"],(function(a,b,c,d,e,f,g){"use strict";a=function(a,b,d){var e=c("oz-player/networks/OzBandwidthEstimator").getAdjustedBandwidth(b.getLegacyConfig());a=a.slice(0);a.sort(function(a,b){return a.getBandwidth()-b.getBandwidth()});b=b.getNumber("live_audio_ibr_bandwidth_percentage");b=e*b;for(var f=a.length-1;f>=0;f--)if(a[f].getBandwidth()<=b){d.getOperationLogger("audio_ibr_success").setRepresentationID(a[f].getID()).setReason(e.toString()).log();return a[f]}d.getOperationLogger("audio_ibr_no_op").setReason(e.toString()).log();return null};g.getInitialAudioRepresentation=a}),98);
__d("oz-player/parsers/OzMpdParser",["oz-player/manifests/Mpd","oz-player/parsers/OzApplicationRepresentationParser","oz-player/parsers/OzAudioRepresentationParser","oz-player/parsers/OzVideoRepresentationParser","oz-player/utils/OzErrorEmitter","oz-player/utils/OzErrorUtils","oz-player/utils/OzIbrUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){var b=a.blockedRepresentationsManager,d=a.config,e=a.drmManager,f=a.enableAlternativeAudioTracks,g=a.initialRepresentationIDs,h=a.networkManager,i=a.ozParserContext,j=a.perfLoggerProvider;a=a.useManagedMediaSource;this.$2=new(c("oz-player/utils/OzErrorEmitter"))();this.$5=d;this.$1=h;this.$3=j;this.$4=i;this.$6=e;this.$7=g!=null?g:[];this.$8=b!=null?b:null;this.$9=f;this.$10=a!=null?a:!1;this.$11=!0}var b=a.prototype;b.onError=function(a){return this.$2.onError(a)};b.parse=function(a){var b=this,e=[];a=a;var f=this.$4.createXmlParser().parse(a);f=f.MPD&&f.MPD.length>0?f.MPD[0]:null;if(!f){var g=2e3;throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_MPD_PARSER",description:"parsed manifest XML missing MPD node; truncated xml ("+Math.min(g,a.length)+"/"+a.length+"): "+a.substring(0,g)})}g=f.Period&&f.Period.length>0?f.Period[0]:null;if(!g){var h=2e3;throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_MPD_PARSER",description:"parsed manifest XML missing Period node; truncated xml ("+Math.min(h,a.length)+"/"+a.length+"): "+a.substring(0,h)})}a=g.AdaptationSet||[];h=this.$12(f);this.$11=h==="static";g=this.$13(f);var i=this.$14(this.$15(a,"video"),{manifestType:h,manifestIsTemplated:g},e),j;this.$9?j=this.$16(this.$15(a,"audio"),{manifestType:h,manifestIsTemplated:g},e).sort(function(a,b){return a.getBandwidth()-b.getBandwidth()}):j=this.$17(this.$18(a,"audio"),c("oz-player/parsers/OzAudioRepresentationParser"),{manifestType:h,manifestIsTemplated:g},e).sort(function(a,b){return a.getBandwidth()-b.getBandwidth()});var k=[];if(this.$5.getBool("vtt_caption_representation")){a=this.$15(a,"application");k=this.$17(a[0],c("oz-player/parsers/OzApplicationRepresentationParser"),{manifestType:h,manifestIsTemplated:g},e)}a=this.$4.mpdUrl;h=new(c("oz-player/manifests/Mpd"))(f,i,j,k,a,this.$19(f),this.$4.customParsers||new Map(),this.$11);if(this.$6){g=this.$6;g.startEME(i,j)}e.forEach(function(a){a.onError(b.$2.emitError)});return h};b.$19=function(a){a=a.$.minimumUpdatePeriod||"";a=/\d+/g.exec(a);if(a&&a.length){a=a[0];return a?Number.parseInt(a,10):null}return null};b.$12=function(a){return a.$.type||"static"};b.$13=function(a){var b;b=(b=this.$4.customParsers)==null?void 0:b.get("isLiveTemplated");if(!b)return!1;b=b(a);return typeof b==="boolean"&&b};b.$18=function(a,b){return a.find(this.$20(b))};b.$15=function(a,b){return a.filter(this.$20(b))};b.$20=function(a){return function(b){if(b.$.mimeType!=null)return b.$.mimeType.indexOf(a)!==-1;b=b.Representation;return b&&b.length>0&&b[0].$.mimeType.indexOf(a)!==-1}};b.$17=function(a,b,c,d){var e=this;if(!a)return[];var f=a.Role;f=f!=null?f:[];f=f[0];var g=f?f.$.value:null;f=a.$.lang;var h=f==="und"?null:f;f=a.$.FBVariantKey;var i=f==="und"?null:f!=null?f:null;return a.Representation.map(function(f){f=new b(e.$5,f,(f=f.ContentProtection)!=null?f:a.ContentProtection,e.$4,e.$1,e.$3,c,a.SegmentTemplate,a.$.mimeType,a.$.codecs,i,h,g,e.$7,e.$8);var j=f.parse();d.push(f);return j})};b.$21=function(a){return this.$10?ManagedMediaSource.isTypeSupported(a):MediaSource.isTypeSupported(a)};b.$14=function(a,b,d){var e=this,f=[],g=new Set();a.filter(Boolean).forEach(function(a,h,i){h=h===i.length-1;for(i of a.Representation){var j,k;if(((j=i.$.FBAbrPolicyTags)==null?void 0:j.includes("avoid_on_abr"))&&!h)continue;j=a.Role;j=j!=null?j:[];j=j[0];j=j?j.$.value:null;var l=a.$.lang;l=l==="und"?null:l;var m=a.$.FBVariantKey;m=m==="und"?null:m!=null?m:null;k=new(c("oz-player/parsers/OzVideoRepresentationParser"))(e.$5,i,(k=i.ContentProtection)!=null?k:a.ContentProtection,e.$4,e.$1,e.$3,b,a.SegmentTemplate,a.$.mimeType,a.$.codecs,m,l,j,e.$7,e.$8);m=k.parse();d.push(k);if(!e.$21(m.getMimeCodecs())){g.add(m.getMimeCodecs());continue}f.push(m)}});return f.filter(Boolean)};b.$16=function(a,b,e){var f=this,g=[],h=new Set();a=a.filter(Boolean).map(function(a){var d=a.Role;d=d!=null?d:[];d=d[0];d=d?d.$.value:null;var g=a.$.lang;g=g==="und"?null:g;var i=a.$.FBVariantKey;i=i==="und"?null:i!=null?i:null;var j=[];for(var k of a.Representation){var l;l=new(c("oz-player/parsers/OzAudioRepresentationParser"))(f.$5,k,(l=k.ContentProtection)!=null?l:a.ContentProtection,f.$4,f.$1,f.$3,b,a.SegmentTemplate,a.$.mimeType,a.$.codecs,i,g,d,f.$7);var m=l.parse();e.push(l);if(!f.$21(m.getMimeCodecs())){h.add(m.getMimeCodecs());continue}j.push(m)}return j});a.forEach(function(a){if(a.length>1){var b=d("oz-player/utils/OzIbrUtils").getInitialAudioRepresentation(a,f.$5,f.$3);g.push(b!=null?b:a[0])}else g.push.apply(g,a)});return g.filter(Boolean)};return a}();g["default"]=a}),98);
__d("oz-player/utils/OzMpdUtils",[],(function(a,b,c,d,e,f){"use strict";function g(a){var b=null;a.forEach(function(a){if(a){var c=a.endTime;c<((c=(c=b)==null?void 0:c.endTime)!=null?c:Infinity)&&(b=a)}});return b}function a(a,b){b=[];b.push.apply(b,a.getVideoRepresentations().concat(a.getAudioRepresentations()));if(b.length){a=b.map(function(a){a=a.getEndingSegment();return a?a.getTimeRange():null});return g(a)}return null}function b(a){var b=[];b.push.apply(b,a.getVideoRepresentations().concat(a.getAudioRepresentations()));if(b.length){a=b[0].getEndingSegment();if(a)return a.getSequenceNumber()}return null}f.getLeastEndTimeTimeRange=g;f.getMpdLastTimeRange=a;f.getMpdLastSequenceNumber=b}),66);
__d("oz-player/utils/OzStreamInterruptChecker",["oz-player/shims/OzEventEmitter","oz-player/utils/OzMpdUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;d=a.call(this)||this;d.$OzStreamInterruptChecker$p_4=0;d.$OzStreamInterruptChecker$p_5=null;d.$OzStreamInterruptChecker$p_6=!1;d.$OzStreamInterruptChecker$p_2=b;d.$OzStreamInterruptChecker$p_3=c;d.$OzStreamInterruptChecker$p_1=d.$OzStreamInterruptChecker$p_3.getNumber("live_numerical_error_epsilon");return d}var c=b.prototype;c.notifyMpdUpdated=function(){var a=d("oz-player/utils/OzMpdUtils").getMpdLastTimeRange(this.$OzStreamInterruptChecker$p_2,this.$OzStreamInterruptChecker$p_3),b=a?a.endTime:null,c=!1;b!==null&&(c=b===this.$OzStreamInterruptChecker$p_5);c?this.$OzStreamInterruptChecker$p_4++:(this.$OzStreamInterruptChecker$p_4=0,this.$OzStreamInterruptChecker$p_6&&this.$OzStreamInterruptChecker$p_5!==null&&this.$OzStreamInterruptChecker$p_5!==void 0&&a!==null&&a!==void 0&&this.emit("streamResumedAt",a.startTime),this.$OzStreamInterruptChecker$p_6=!1);c=this.$OzStreamInterruptChecker$p_3.getNumber("stream_interrupt_check_mpd_stale_count_threshold");!this.$OzStreamInterruptChecker$p_6&&c>0&&this.$OzStreamInterruptChecker$p_4>=c&&b!==null&&b!==void 0&&(this.emit("streamInterruptAt",b),this.$OzStreamInterruptChecker$p_6=!0);this.$OzStreamInterruptChecker$p_5=b};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/parsers/OzMpdUpdater",["oz-player/parsers/OzMpdParser","oz-player/shims/OzEventEmitter","oz-player/shims/OzSubscriptionsHandler","oz-player/shims/OzURI","oz-player/shims/ozClearTimeout","oz-player/shims/ozSetTimeoutAcrossTransitions","oz-player/utils/OzErrorEmitter","oz-player/utils/OzErrorUtils","oz-player/utils/OzMpdUtils","oz-player/utils/OzReadableStreamUtils","oz-player/utils/OzStreamInterruptChecker"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,e,f,g,h,i,j,k,l){var m;m=a.call(this)||this;m.$OzMpdUpdater$p_8=new(c("oz-player/utils/OzErrorEmitter"))();m.$OzMpdUpdater$p_10=new(c("oz-player/shims/OzSubscriptionsHandler"))();m.$OzMpdUpdater$p_11=!1;m.$OzMpdUpdater$p_16=0;m.$OzMpdUpdater$p_18=0;m.$OzMpdUpdater$p_19=null;m.$OzMpdUpdater$p_21=!0;m.$OzMpdUpdater$p_22=0;m.$OzMpdUpdater$p_26=function(){var a,b=m.$OzMpdUpdater$p_2.getLocation();m.$OzMpdUpdater$p_21=!1;if(b==null||b==="")return;var e=new(c("oz-player/shims/OzURI"))(b),f=String((a=(a=e.getQueryData().ms)!=null?a:e.getQueryData().msdev)!=null?a:e.getQueryData().mstest);if(!m.$OzMpdUpdater$p_12.isPlaying()){m.setupUpdateLoop();return}var g=m.$OzMpdUpdater$p_5.getOperationLogger("update_manifest").start();m.$OzMpdUpdater$p_28(b).then(function(a){var e,h;m.$OzMpdUpdater$p_18=0;m.$OzMpdUpdater$p_29();m.$OzMpdUpdater$p_7=0;if(m.$OzMpdUpdater$p_21)return;var i=babelHelpers["extends"]({},m.$OzMpdUpdater$p_13,{mpdUrl:b});i=new(c("oz-player/parsers/OzMpdParser"))({config:m.$OzMpdUpdater$p_6,ozParserContext:i,networkManager:m.$OzMpdUpdater$p_1,perfLoggerProvider:m.$OzMpdUpdater$p_5,drmManager:m.$OzMpdUpdater$p_14,initialRepresentationIDs:[],blockedRepresentationsManager:null,enableAlternativeAudioTracks:!1,useManagedMediaSource:m.$OzMpdUpdater$p_23});i.onError(function(a){m.$OzMpdUpdater$p_8.emitError(a),g.setError(a).log()});i=i.parse(a);a=m.$OzMpdUpdater$p_2.isStaticMpd();m.$OzMpdUpdater$p_2.updateWith(i);m.$OzMpdUpdater$p_2.markRefreshed();i.isStaticMpd()&&!a&&m.emit("streamTransitionToStatic");i=d("oz-player/utils/OzMpdUtils").getMpdLastTimeRange(m.$OzMpdUpdater$p_2,m.$OzMpdUpdater$p_6);a=i&&i.endTime!=0?i.endTime:null;i=String((i=(i=m.$OzMpdUpdater$p_2)==null?void 0:i.getCustomField("currentServerTimeMs"))!=null?i:"");e=String((e=(e=m.$OzMpdUpdater$p_2)==null?void 0:e.getCustomField("lastVideoFrameTs"))!=null?e:"");i={currentServerTimeMs:i,ingestLastVideoFrameTs:e,nowMs:String(Date.now())};e=new Set(((e=(e=m.$OzMpdUpdater$p_2)==null?void 0:e.getVideoRepresentations())!=null?e:[]).map(function(a){return a.getCodecs()})).size>1;h=new Set(((h=(h=m.$OzMpdUpdater$p_2)==null?void 0:h.getAudioRepresentations())!=null?h:[]).map(function(a){return a.getCodecs()})).size>1;g.setLiveheadPosition(a).setLiveheadSeqNumMpd(d("oz-player/utils/OzMpdUtils").getMpdLastSequenceNumber(m.$OzMpdUpdater$p_2)).setManifestType(m.$OzMpdUpdater$p_2.isStaticMpd()?"static":"dynamic").setIsMixedCodecManifest(e||h).setIsTemplatedManifest(Boolean(m.$OzMpdUpdater$p_2.getCustomField("isLiveTemplated"))).setPlaybackFbmsParam(f).setUserInfo(i).log();m.$OzMpdUpdater$p_9.notifyMpdUpdated();m.setupUpdateLoop()})["catch"](function(a){m.$OzMpdUpdater$p_29();g.setError(a).log();var c=null;if(d("oz-player/utils/OzErrorUtils").isOzError(a))c=a;else{var e,f=null,h="Caught an unknown error when fetching the manifest.";if(a!=null)if(m.$OzMpdUpdater$p_6.getBool("normalize_mpd_fetch_errors")){var i=d("oz-player/utils/OzErrorUtils").getNormalizedErrorAndCode(a),j=i[0];i[1];c=j}else e=a,f=e.code!=null?String(e.code):null,h=e.name+"; "+e.message+"; "+e.description+";";if(c==null){i={originalError:e,url:b};f!==null&&(i.code=f);c=d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_NETWORK",description:h,extra:i})}}m.$OzMpdUpdater$p_27(c,b)})};var n=e.getMinimumUpdatePeriod();m.$OzMpdUpdater$p_7=0;m.$OzMpdUpdater$p_1=f;m.$OzMpdUpdater$p_2=e;m.$OzMpdUpdater$p_12=g;m.$OzMpdUpdater$p_3=n;m.$OzMpdUpdater$p_5=h.cloneContext().setType("manifest");m.$OzMpdUpdater$p_6=b;m.$OzMpdUpdater$p_13=i;m.$OzMpdUpdater$p_14=j;m.$OzMpdUpdater$p_15=k;m.$OzMpdUpdater$p_23=l;m.$OzMpdUpdater$p_2.addListener("locationUpdated",function(){m.$OzMpdUpdater$p_24(),m.setupUpdateLoop()});m.$OzMpdUpdater$p_9=new(c("oz-player/utils/OzStreamInterruptChecker"))(m.$OzMpdUpdater$p_2,m.$OzMpdUpdater$p_6);m.$OzMpdUpdater$p_10.addSubscriptions(m.$OzMpdUpdater$p_9.addListener("streamResumedAt",function(a){m.emit("streamResumedAt",a)}),m.$OzMpdUpdater$p_9.addListener("streamInterruptAt",function(a){m.emit("streamInterruptAt",a)}));return m}var e=b.prototype;e.$OzMpdUpdater$p_24=function(){c("oz-player/shims/ozClearTimeout")(this.$OzMpdUpdater$p_4),this.$OzMpdUpdater$p_4=null,this.$OzMpdUpdater$p_25(),this.$OzMpdUpdater$p_21=!0};e.setupUpdateLoop=function(){var a,b=!this.$OzMpdUpdater$p_11;b&&(b=!this.$OzMpdUpdater$p_2.isStaticMpd(),this.$OzMpdUpdater$p_3=this.$OzMpdUpdater$p_2.getMinimumUpdatePeriod());a=this.$OzMpdUpdater$p_17==null&&this.$OzMpdUpdater$p_6.getNumber("manifest_initial_update_delay_ms")>0?this.$OzMpdUpdater$p_6.getNumber("manifest_initial_update_delay_ms"):this.$OzMpdUpdater$p_6.getNumber("manifest_update_frequency_ms")||((a=this.$OzMpdUpdater$p_3)!=null?a:0)*1e3;b&&a&&this.$OzMpdUpdater$p_2.getLocation()&&(this.$OzMpdUpdater$p_4=c("oz-player/shims/ozSetTimeoutAcrossTransitions")(this.$OzMpdUpdater$p_26,a))};e.onError=function(a){return this.$OzMpdUpdater$p_8.onError(a)};e.$OzMpdUpdater$p_27=function(a,b){var c=this;b=!0;var d=this.$OzMpdUpdater$p_18-1,e=a.getExtra().code;this.$OzMpdUpdater$p_19!==e?(this.$OzMpdUpdater$p_19=e,this.$OzMpdUpdater$p_16=1):this.$OzMpdUpdater$p_16++;var f=function(){b=!1};e={endStream:function(){f(),c.$OzMpdUpdater$p_11=!0,c.emit("streamGone")},consecutiveFailuresForErrorCode:this.$OzMpdUpdater$p_16,error:a,isInitialRequest:!1,retry:function(b){c.emit("manifestFetchErrorRetry",a),f(),c.setupUpdateLoop()},retryAttemptCount:d};this.emit("manifestFetchError",e);b&&this.$OzMpdUpdater$p_8.emitError(a)};e.$OzMpdUpdater$p_30=function(a,b){a=new(c("oz-player/shims/OzURI"))(a);a.addQueryData("_nc_expid",b);return a.toString()};e.$OzMpdUpdater$p_31=function(a){if(!this.$OzMpdUpdater$p_15)return a;a=new(c("oz-player/shims/OzURI"))(a);a.addQueryData("_nc_tsid","mpd-"+this.$OzMpdUpdater$p_22++);return a.toString()};e.$OzMpdUpdater$p_28=function(a){this.$OzMpdUpdater$p_25();this.$OzMpdUpdater$p_18++;var b=this.$OzMpdUpdater$p_6.getString("cdn_experiment_id");a=b.length===0?a:this.$OzMpdUpdater$p_30(a,b);a=this.$OzMpdUpdater$p_31(a);b=this.$OzMpdUpdater$p_1.requestRawUrl({debugName:"OzMpdUpdater/requestMpd/mpd",url:a,options:{networkTimeoutMs:this.$OzMpdUpdater$p_6.getNumber("mpd_updater_network_request_timeout_ms")},loggerProvider:this.$OzMpdUpdater$p_5});this.$OzMpdUpdater$p_17=Date.now();this.$OzMpdUpdater$p_20=b;a=b.getStream().getReader();return d("oz-player/utils/OzReadableStreamUtils").pumpString(a)};e.$OzMpdUpdater$p_25=function(){this.$OzMpdUpdater$p_20&&(this.$OzMpdUpdater$p_20.cancel(),this.$OzMpdUpdater$p_18=0),this.$OzMpdUpdater$p_29()};e.$OzMpdUpdater$p_29=function(){this.$OzMpdUpdater$p_20=null};e.destroy=function(){this.$OzMpdUpdater$p_4&&c("oz-player/shims/ozClearTimeout")(this.$OzMpdUpdater$p_4),this.$OzMpdUpdater$p_10.release()};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/shims/www/OzXmlParserTrustedTypesHTMLPolicyWWW",["TrustedTypes","err"],(function(a,b,c,d,e,f,g){"use strict";b={createHTML:function(a){if(a.startsWith('<?xml version="1.0"?>\n<')||a.startsWith('<?xml version="1.0" encoding="UTF-8"?>\n<')||a.startsWith('\n<MPD xmlns="urn:mpeg:dash:schema:mpd:2011"'))return a;throw c("err")("Violating Trusted Type policies. Only works for XML.")}};var h=c("TrustedTypes").createPolicy("oz-player-xml",b);function a(a){for(var b=arguments.length,c=new Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];return h.createHTML.apply(h,[a].concat(c))}g.createTrustedHTMLForDOMParser=a}),98);
__d("oz-player/shims/OzXmlParserTrustedTypesHTMLPolicy",["oz-player/shims/www/OzXmlParserTrustedTypesHTMLPolicyWWW"],(function(a,b,c,d,e,f,g){"use strict";g.createTrustedHTMLForDOMParser=d("oz-player/shims/www/OzXmlParserTrustedTypesHTMLPolicyWWW").createTrustedHTMLForDOMParser}),98);
__d("oz-player/parsers/OzXmlParserImplDOMParser",["oz-player/shims/OzXmlParserTrustedTypesHTMLPolicy","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){function h(){return{$:{}}}function i(a){var b=h();for(var c=a.attributes,d=0;d<c.length;++d){var e=c[d];b.$[e.name]=e.value}for(e=a.childNodes,c=0;c<e.length;++c){d=e[c];if(d instanceof Element){a=d.nodeName;Object.prototype.hasOwnProperty.call(b,a)||(b[a]=[]);b[a].push(i(d))}else{a=d.nodeValue.trim();a&&(b._=a)}}return b}a=function(){function a(){this.$1=new DOMParser()}var b=a.prototype;b.parse=function(a){try{var b=h(),c=d("oz-player/shims/OzXmlParserTrustedTypesHTMLPolicy").createTrustedHTMLForDOMParser(a);c=this.$1.parseFromString(c,"application/xml");var e=c.querySelector("parsererror");if(e){var f=e.querySelector("h3");f=f?f.nextElementSibling:null;f=f?f.innerHTML:e.innerHTML;e=2e3;throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_XML_PARSER",description:"DOMParser parsererror: "+String(f).trim()+"; truncated xml ("+Math.min(e,a.length)+"/"+a.length+"): "+a.substring(0,e),extra:{}})}f=c.documentElement;f!=null&&(b[f.nodeName]=[i(f)]);return b}catch(b){if(d("oz-player/utils/OzErrorUtils").isOzError(b))throw b;else{e=2e3;throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_XML_PARSER",description:"XML parse exception: "+String(b)+"; truncated xml ("+Math.min(e,a.length)+"/"+a.length+"): "+a.substring(0,e),extra:{originalError:b}})}}};return a}();g["default"]=a}),98);
__d("oz-player/playback_controls/OzClearSourceBufferOnSeekManager",["oz-player/shims/OzDeferred","oz-player/shims/OzMaybeNativePromise","oz-player/utils/OzCustomErrorCode","oz-player/utils/OzErrorEmitter","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,d,e){e===void 0&&(e=null),this.$3=new(c("oz-player/utils/OzErrorEmitter"))(),this.$4=new Map(),this.$5=!1,this.$6=!1,this.$7=!1,this.$8=null,this.$9=[],this.$10=null,this.$11=null,this.$1=a,this.$2=b,this.$4=d,this.$11=e}var b=a.prototype;b.$12=function(a){var b;this.$5=!0;return(b=this.$8)!=null?b:a.call(this.$1)};b.$13=function(a,b){this.$6=!0,this.$9.push({t:b}),this.$8=b,this.$9.length===1&&this.$14(a)};b.$15=function(a){this.$7=!0;return this.$8!=null?!0:a.call(this.$1)};b.$16=function(a,b){var d=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise")),e=[];this.$4.forEach(function(c,d){d=c.clearSourceBufferRange(a,b);e.push(d)});var f=c("oz-player/shims/OzMaybeNativePromise").all(e);f.then(function(){d.resolve()})["catch"](function(a){d.reject(a)});return d.getPromise()};b.$14=function(a){var b=this,e=this.$9.length>0?this.$9[0]:null,f=this.$2.getNumber("clear_buffer_on_seek_epsilon_s");if(e){var g=e.t;e=this.$1.buffered;var h=e.length,i=e.length<=0,j=-1,k=!1;for(var l=0;j<0&&l<h;++l)j=g-f>=e.start(l)&&g+f<e.end(l)?l:j,k=g<e.start(l)-f;l=j>-1;var m=this.$2.getNumber("clear_buffer_on_seek_nudge_s");l&&j>=0&&j<h&&m>0&&(g=Math.min(g,e.end(j)-m),g=Math.max(g,e.start(j)+m));if(i||l||!k)this.$11&&this.$11.getOperationLogger("seek_to_next_time").setLength(Math.round(g*1e3)).setInitiator("no_clear").log(),a.call(this.$1,g),this.$9.shift(),this.$14(a);else{var n=null;this.$11&&(n=this.$11.getOperationLogger("clear_buffer_before_seek").setLength(Math.round(g*1e3)).setUserInfo({bufferRangesLength:String(h),nudgeSec:String(m)}).start());j=this.$16(Math.max(g-f,0),e.end(h-1)+f);j.then(function(){n&&n.log(),b.$11&&b.$11.getOperationLogger("seek_to_next_time").setLength(Math.round(g*1e3)).setInitiator("after_clear").log(),a.call(b.$1,g),b.$9.shift(),b.$14(a)})["catch"](function(a){n&&n.setError(a).log(),b.$3.emitError(d("oz-player/utils/OzErrorUtils").isOzError(a)?a:d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SOURCE_BUFFER",description:a?a.name+": "+a.message:"Unknown error while clearing the buffer.",extra:{code:c("oz-player/utils/OzCustomErrorCode").SOURCE_BUFFER_MANAGER_CLEAR_RANGE_FAILED,originalError:a}}))})}}else this.$8=null};b.maybeOverwriteVideoCurrentTimeProperty=function(){var a,b,c=this,d=Object.getOwnPropertyDescriptor&&Object.getOwnPropertyDescriptor(this.$1,"currentTime");d=(a=d)!=null?a:Object.getOwnPropertyDescriptor&&Object.getOwnPropertyDescriptor(HTMLMediaElement.prototype,"currentTime");a=Object.getOwnPropertyDescriptor&&Object.getOwnPropertyDescriptor(this.$1,"seeking");a=(b=a)!=null?b:Object.getOwnPropertyDescriptor&&Object.getOwnPropertyDescriptor(HTMLMediaElement.prototype,"seeking");var e=d&&typeof d.get==="function"?d.get:null,f=d&&typeof d.set==="function"?d.set:null,g=a&&typeof a.get==="function"?a.get:null;if(e&&f&&g&&Object.defineProperty){try{Object.defineProperty(this.$1,"currentTime",{get:function(){return c.$12(e)},set:function(a){c.$13(f,a)},configurable:!0,enumerable:!0});Object.defineProperty(this.$1,"seeking",{get:function(){return c.$15(g)},configurable:!0,enumerable:!0});b=this.$1.currentTime;this.$1.currentTime=b;this.$1.seeking}catch(a){}this.$17(function(){c.getCurrentTimePropertyOverwriteSuccess()&&Object.defineProperty(c.$1,"currentTime",{get:e,set:f,configurable:!0,enumerable:!0}),c.getSeekingPropertyOverwriteSuccess()&&Object.defineProperty(c.$1,"seeking",{get:g,configurable:!0,enumerable:!0})})}};b.getCurrentTimePropertyOverwriteSuccess=function(){return this.$5&&this.$6};b.getSeekingPropertyOverwriteSuccess=function(){return this.$7};b.$17=function(a){this.$10=a};b.onError=function(a){return this.$3.onError(a)};b.destroy=function(){try{this.$10&&this.$10()}catch(a){}};return a}();g["default"]=a}),98);
__d("oz-player/playback_controls/OzSteadyStateManager",["oz-player/shims/ozvariant"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){this.$1=[],this.$2=a,this.$3=b}var b=a.prototype;b.addSample=function(a){var b,c=Math.floor(Date.now()/(1e3*this.$3)),d=c%this.$2;((b=this.$1[d])==null?void 0:b.bucketNumber)!=c&&(this.$1[d]={bucketNumber:0,val:null});this.$1[d]={bucketNumber:c,val:Math.min(a,(b=this.$1[d].val)!=null?b:a)}};b.min=function(){var a=this,b=Date.now(),d=this.$1.filter(function(c){return c!=null&&c.val!=null&&b-a.$2*1e3*a.$3<c.bucketNumber*1e3*a.$3});if(d.length<this.$2)return null;d=d.reduce(function(a,b){if(a==null)return b;var d=b.val;d!=null||c("oz-player/shims/ozvariant")(0,56011);var e=a.val;e!=null||c("oz-player/shims/ozvariant")(0,56011);return d<e?b:a},null);return d==null?void 0:d.val};b.reset=function(){this.$1=[]};return a}();g["default"]=a}),98);
__d("oz-player/playback_controls/OzLiveLatencyManager",["oz-player/playback_controls/OzSteadyStateManager","oz-player/shims/OzDOMEventListener","oz-player/shims/OzSubscriptionsHandler","oz-player/shims/ozThrottle","oz-player/utils/OzSourceBufferUtil"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){var b=this;this.$8=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$9=null;this.$10=!1;this.$11=null;this.$12=null;this.$13=null;this.$14=!0;var d=a.config,e=a.mpd,f=a.playbackState,g=a.bufferingDetector,h=a.video,i=a.loggerProvider;a=a.getUserSelectedPlaybackRate;this.$1=d;this.$2=e;this.$3=f;this.$15=a;d.getNumber("steadystate_minbuffer_buckets")>0&&(this.$7=new(c("oz-player/playback_controls/OzSteadyStateManager"))(this.$1.getNumber("steadystate_minbuffer_buckets"),this.$1.getNumber("steadystate_minbuffer_buckets_sec")));g!=null&&(this.$8.addSubscriptions(g.addListener("enterBuffering",function(a){if(a==="in_play"){b.$9=0;(a=b.$7)==null?void 0:a.reset()}b.$10=!1;(a=b.$6)==null?void 0:a.log();b.$6=null})),this.$8.addSubscriptions(g.addListener("leaveBuffering",function(){b.$9===0&&(b.$9=Date.now()),b.$10=!1})));this.$4=h;this.$5=i;if(this.$16()){this.$8.addSubscriptions((e=c("oz-player/shims/OzDOMEventListener")).listenDOMEvent(this.$4,"timeupdate",c("oz-player/shims/ozThrottle")(function(){b.$17(),b.$18()},this.$1.getNumber("playhead_manager_timeupdate_throttle_ms"))),e.listenDOMEvent(this.$4,"waiting",function(){b.$17()}),e.listenDOMEvent(this.$4,"durationchange",function(){b.$17()}),e.listenDOMEvent(this.$4,"seeking",function(){var a;b.$13=Date.now();b.$11=null;(a=b.$7)==null?void 0:a.reset()}),e.listenDOMEvent(this.$4,"playing",function(){b.$13=Date.now(),b.$11=null}),e.listenDOMEvent(h,"pause",function(){var a;return(a=b.$7)==null?void 0:a.reset()}))}}var b=a.prototype;b.enabled=function(){var a=this.$1.getNumber("catchup_timeout_after_buffering_sec");return a===0||this.$9==null||this.$9!==0&&this.$9+a*1e3<=Date.now()};b.isBehindPreferredLiveHeadLatencyWithTolerance=function(){return this.$3.getCurrentTime()<this.getInitialPlayHeadPosition()-this.getPreferredLiveHeadLatencyToleranceSec()};b.$19=function(){return this.$3.getCurrentTime()<this.getInitialPlayHeadPosition()};b.$20=function(){var a;a=(a=this.$7)==null?void 0:a.min();return!this.$21()&&a!=null&&a>this.$1.getNumber("steadystate_minbuffer_sec")};b.getInitialPlayHeadPosition=function(){return this.getLiveHeadTimeSec()-this.getPreferredLiveHeadLatencySec()};b.getLiveHeadTimeSec=function(){var a=this.$22();return(a==null?void 0:(a=a.getEndingSegment())==null?void 0:a.getTimeRange().endTime)||0};b.$21=function(){return this.$9!=null&&this.$1.getBool("latencymanager_stalled_edgelatency_sec_on")};b.getPreferredLiveHeadLatencySec=function(){var a=this.$21()?this.$1.getNumber("latencymanager_stalled_edgelatency_sec"):this.$1.getNumber("pdash_download_cursor_catchup_threshold_sec");a=this.$23(a);this.$5.getOperationLogger("latency_manager").setPreferredEdgeLatency(a*1e3);return a};b.getPreferredLiveHeadLatencyToleranceSec=function(){return this.$1.getNumber("pdash_download_cursor_catchup_tolerance_sec")};b.shouldEnableManifestTimeRangeCatchup=function(){return!this.$1.getNumber("pdash_download_cursor_catchup_threshold_sec")};b.shouldEnableCursorBasedCatchup=function(){var a=this.$2.getVideoRepresentations()[0];a=a&&a.canPredict();return a&&this.$1.getNumber("pdash_download_cursor_catchup_threshold_sec")>0};b.setEnableCatchup=function(a){var b=this.$14;!this.$14&&a===!0&&(this.$9=null);this.$14=a;this.$1.getBool("fix_live_rewind_user_selected_playback_speed")&&a!==b&&this.$18()};b.$24=function(a,b,c){var d=this.$1.getNumber("playback_speed_min_sharpness_factor");c=a*2/(1+Math.pow(Math.E,-(c-b)*d));return 1-a+c};b.$16=function(){return!this.$1.getBool("playback_speed_latency_adjustment_disabled")&&(this.$1.getNumber("playback_speed_latency_adjustment_rate")>0||this.$1.getNumber("playback_speed_latency_slowdown_adjustment_rate")>0||this.$1.getNumber("playback_speed_latency_speedup_adjustment_rate")>0)};b.$17=function(){var a,b=d("oz-player/utils/OzSourceBufferUtil").getBufferAheadInBufferedRanges(d("oz-player/utils/OzSourceBufferUtil").convertToBufferedTimeRangeArray(this.$4.buffered),this.$3.getCurrentTime(),this.$1).bufferAheadSec;(a=this.$7)==null?void 0:a.addSample(b)};b.$18=function(){var a=d("oz-player/utils/OzSourceBufferUtil").getBufferAheadInBufferedRanges(d("oz-player/utils/OzSourceBufferUtil").convertToBufferedTimeRangeArray(this.$4.buffered),this.$3.getCurrentTime(),this.$1),b=a.bufferAheadSec;a=a.bufferedOffset;this.$25(b,a)};b.$25=function(a,b){var c=this.$1.getNumber("playback_speed_enabled_delay_sec"),d=this.$1.getNumber("playback_speed_min_buffer_sec");a>d&&(this.$10=!0);var e=this.$1.getNumber("playback_speed_min_duration_sec"),f=this.$1.getNumber("playback_speed_restore_min_duration_sec"),g=this.$1.getNumber("playback_speed_latency_adjustment_rate"),h=this.$1.getNumber("playback_speed_latency_slowdown_adjustment_rate"),i=this.$1.getNumber("playback_speed_latency_speedup_adjustment_rate");if(this.$4.playbackRate===0){var j;(j=this.$6)==null?void 0:j.log();this.$6=null}else if(this.$14&&this.$10&&a<d&&(g>0||h>0)&&b===0&&(c===0||this.$13!=null&&this.$13+c*1e3<Date.now())){j=h>0?1-h:this.$24(g,d,a);this.$6==null&&(this.$6=this.$5.getOperationLogger("latency_manager").start().setInitiator("speed_adjustment").setReason("slow").setUserInfo({newPlaybackRate:String(j),oldPlaybackRate:String(this.$4.playbackRate)}));this.$26(j)}else if(this.$14&&this.$10&&(this.$19()||this.$20())&&i>0&&b===0&&(this.$12==null||this.$12+f*1e3<Date.now())&&(c===0||this.$13!=null&&this.$13+c*1e3<Date.now())){h=1+i;this.$6==null&&(this.$6=this.$5.getOperationLogger("latency_manager").start().setInitiator("speed_adjustment").setReason("fast").setUserInfo({newPlaybackRate:String(h),oldPlaybackRate:String(this.$4.playbackRate)}));this.$26(h)}else if(this.$14===!1||this.$11==null||this.$11+e*1e3<Date.now()){g=this.$4.playbackRate;this.$1.getBool("fix_live_rewind_user_selected_playback_speed")?this.$4.playbackRate=this.$14===!1?this.$15():1:this.$4.playbackRate=1;(d=this.$6)==null?void 0:d.log();this.$6=null;g!==this.$4.playbackRate&&(this.$12=Date.now())}};b.$26=function(a){try{this.$4.playbackRate=a,this.$11=Date.now()}catch(b){(a=this.$6)==null?void 0:a.setError(b).setResult("failed").log();this.$6=null}};b.$22=function(){var a=this.$2.getVideoRepresentations().concat(this.$2.getAudioRepresentations());return a.length===0?null:a.reduce(function(a,b){var c;return(((c=a.getEndingSegment())==null?void 0:c.getTimeRange().endTime)||0)>(((c=b.getEndingSegment())==null?void 0:c.getTimeRange().endTime)||0)?a:b})};b.$23=function(a){var b;if(a<=0)return a;var c=this.$22();b=c==null?void 0:(b=c.getEndingSegment())==null?void 0:b.getTimeRange();if(!b)return a;c=c==null?void 0:(c=c.getSegmentByTime((b==null?void 0:b.endTime)-a))==null?void 0:c.getTimeRange();return!c?a:(b==null?void 0:b.endTime)-(c==null?void 0:c.startTime)};b.destroy=function(){this.$8.release()};return a}();g["default"]=a}),98);
__d("oz-player/playback_controls/OzPlaybackTimeRangeManager",["oz-player/shims/OzSubscriptionsHandler","oz-player/utils/OzBufferingUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){var b=this;this.$4=null;this.$5=0;this.$6=0;this.$7=!0;this.$8=0;this.$9=0;this.$10=!1;this.$12=0;this.$13=0;this.$15=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$17=function(){b.$10!=b.$14.isPlaying()&&(b.$14.isPlaying()&&(b.$11=Date.now()),b.$10=b.$14.isPlaying())};var d=a.video,e=a.config,f=a.mpd,g=a.liveheadFallBehindBlockThreshold,h=a.liveheadFallBehindBlockMargin,i=a.playbackState;a=a.perfLoggerProvider;this.$1=e;this.$2=d;this.$3=f;this.$8=g;this.$9=h;this.$14=i;this.$16=a;this.$1.getNumber("catchup_timeout_after_play_sec")!==0&&this.$15.addSubscriptions(this.$14.addEventListener("play",this.$17),this.$14.addEventListener("pause",this.$17))}var b=a.prototype;b.setTimelineBlockingEnabled=function(a){this.$7=a,this.$7||this.$3.unblockTimeRange()};b.setTimeRangeProvider=function(a){var b=this;this.$4!=null&&this.$4.remove();this.$4=a.addUpdateListener(function(){var c=a.getTimeRanges(),d=0;if(c.length!==0){c=c[c.length-1];d=c.endTime}b.$18(d)})};b.$18=function(a){var b;if(!this.$7)return;if(this.$5===a)return;this.$5=a;if(this.$2.currentTime<=this.$6)return;a=this.$1.getBool("catchup_use_timeline_range_end_time_as_end")?a:this.$2.duration;var c=this.$1.getNumber("overwrite_livehead_fall_behind_block_threshold")!==0?this.$1.getNumber("overwrite_livehead_fall_behind_block_threshold"):this.$8,e=this.$1.getNumber("overwrite_live_time_range_block_margin")!==0?this.$1.getNumber("overwrite_live_time_range_block_margin"):this.$9;this.$17();this.$1.getBool("reset_catchup_timeout_after_play_sec_on_overwrite")&&(this.$13!=e||this.$12!=c)&&(this.$13=e,this.$12=c,this.$11=Date.now());b=this.$1.getNumber("catchup_timeout_after_play_sec")===0||Date.now()-((b=this.$11)!=null?b:0)<this.$1.getNumber("catchup_timeout_after_play_sec")*1e3;if(b&&this.$1.getNumber("pdash_download_cursor_catchup_threshold_sec")===0&&e!==0&&c!==0&&a-this.$2.currentTime>c){b=[];for(c=0;c<this.$2.buffered.length;c++)b.push({startTime:this.$2.buffered.start(c),endTime:this.$2.buffered.end(c)});c=d("oz-player/utils/OzBufferingUtils").getBufferAheadFromCurrentTime(this.$2.currentTime,b);this.$6=this.$2.currentTime+Math.min(c,e);b={startTime:this.$6,endTime:a-e};this.$16.getOperationLogger("latency_manager").setInitiator("time_range_manager").setReason("blocked_range").setLength(b.endTime-b.startTime).setUserInfo({blockedRangeStart:String(b.startTime),blockedRangeEnd:String(b.endTime)}).log();this.$3.blockTimeRange(b)}};b.destroy=function(){this.$4!=null&&(this.$4.remove(),this.$4=null),this.$15.release()};return a}();g["default"]=a}),98);
__d("oz-player/strategies/LiveheadSeekaheadStrategy",[],(function(a,b,c,d,e,f){"use strict";var g=.05;function a(a,b){b=Math.max.apply(Math,b.map(function(a){return a.startTime}));return b-a>0?b-a+g:0}f.computeSeekAhead=a}),66);
__d("oz-player/utils/OzTimelineGapUtil",["oz-player/utils/OzNumericalHelper","oz-player/utils/OzNumericalRangeUtil"],(function(a,b,c,d,e,f,g){"use strict";var h=1.5;function a(a,b,c,e){b=b.map(function(a){return{rangeStart:a.startTime,rangeEnd:a.endTime}});return d("oz-player/utils/OzNumericalRangeUtil").findCurrentRangeIndex(c,a,b,e)===-1}function b(a,b,c,e,f){f===void 0&&(f=h);var g=b.map(function(a){return{rangeStart:a.startTime,rangeEnd:a.endTime}});c=d("oz-player/utils/OzNumericalRangeUtil").findCurrentRangeIndex(c,a,g,e);if(c!==-1){g=b[c];return d("oz-player/utils/OzNumericalHelper").lessThanOrEqual(a,g.endTime)&&g.endTime-a<f}return!1}g.isInGap=a;g.isNearGap=b}),98);
__d("oz-player/playback_controls/OzPlayheadManager",["oz-player/shims/OzDOMEventListener","oz-player/shims/OzEventEmitter","oz-player/shims/OzSubscriptionsHandler","oz-player/shims/ozThrottle","oz-player/shims/ozvariant","oz-player/strategies/LiveheadSeekaheadStrategy","oz-player/utils/OzNumericalRangeUtil","oz-player/utils/OzSourceBufferUtil","oz-player/utils/OzTimelineGapUtil"],(function(a,b,c,d,e,f,g){"use strict";var h=function(){function a(a){this.$1=a.perfLoggerProvider,this.$2=a.video,this.$4=a.config}var b=a.prototype;b.markState=function(a,b){var c=d("oz-player/utils/OzSourceBufferUtil").convertToBufferedTimeRangeArray(this.$2.buffered),e=this.$3;if(e==null){this.$3=c;return}var f=d("oz-player/utils/OzSourceBufferUtil").getBufferAheadInBufferedRanges(c,b.getCurrentTime(),this.$4);e=d("oz-player/utils/OzSourceBufferUtil").getBufferAheadInBufferedRanges(e,b.getCurrentTime(),this.$4);e.bufferedOffset!==-1&&f.bufferedOffset===-1&&this.$1.getOperationLogger("playhead_adjustment").setError("buffer_deleted").setResult("failed").setUserInfo({vidSeeking:String(this.$2.seeking),vidErrCode:this.$2.error?String(this.$2.error.code):null}).setInitiator(a).log();this.$3=c};return a}(),i=function(){function a(a){this.$5=!1,this.$6={},this.$1=a.perfLoggerProvider,this.$2=a.video}var b=a.prototype;b.markState=function(a,b){var c=this;if(this.$2.paused){this.$3=null;this.$4=null;this.$5=!1;this.$6={};return}(this.$3==null||this.$2.currentTime>this.$3)&&(this.$4=Date.now(),this.$3=this.$2.currentTime,this.$5=!1,this.$6={});this.$6[a]=this.$6[a]==null?1:this.$6[a]+1;var d=this.$4;if(!this.$5&&!this.$2.paused&&d!=null&&d+1e4<Date.now()){this.$5=!0;var e={};Object.keys(this.$6).forEach(function(a){e[a]=String(c.$6[a])});this.$1.getOperationLogger("playhead_adjustment").setError("not_advancing").setResult("failed").setLength(Date.now()-d).setUserInfo(babelHelpers["extends"]({},e,{},b,{lastCurrentTime:String(this.$3),lastCurrentTimeChanged:String(d-Date.now()),vidSeeking:String(this.$2.seeking),vidErrCode:this.$2.error?String(this.$2.error.code):null})).setInitiator(a).log()}};return a}();a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var e;e=a.call(this)||this;e.$OzPlayheadManager$p_2=new(c("oz-player/shims/OzSubscriptionsHandler"))();e.$OzPlayheadManager$p_3=new(c("oz-player/shims/OzSubscriptionsHandler"))();e.$OzPlayheadManager$p_4=new Set();e.$OzPlayheadManager$p_10=new Set();e.$OzPlayheadManager$p_12=function(a){e.$OzPlayheadManager$p_1.currentTime=a};e.$OzPlayheadManager$p_14=!1;e.$OzPlayheadManager$p_15=!1;e.$OzPlayheadManager$p_16=!0;e.$OzPlayheadManager$p_17=!1;e.$OzPlayheadManager$p_23=function(a,b){b===void 0&&(b=null);if(e.$OzPlayheadManager$p_20!=null?!e.$OzPlayheadManager$p_20.shouldEnableManifestTimeRangeCatchup():!1)return;var c=e.$OzPlayheadManager$p_9;if(c&&c.isEnabled(e.$OzPlayheadManager$p_1,e.$OzPlayheadManager$p_8)){c=c.computeNewPlayheadPosition(e.$OzPlayheadManager$p_1,e.$OzPlayheadManager$p_8);if(typeof c==="number"){e.$OzPlayheadManager$p_27(c,"live_catch_up_strategy",a,b);return}}c=e.$OzPlayheadManager$p_6.getNumber("auto_seek_playhead_slack");if(c===0)return;var f=e.$OzPlayheadManager$p_5;if(!f)return;var g=e.$OzPlayheadManager$p_7.getCurrentTime(),h=f.getTimeRanges().map(function(a){return{rangeStart:a.startTime,rangeEnd:a.endTime}}),i=d("oz-player/utils/OzNumericalRangeUtil").findNextRangeIndex(g,h);if((d("oz-player/utils/OzTimelineGapUtil").isNearGap(g,f.getTimeRanges(),e.$OzPlayheadManager$p_6)||d("oz-player/utils/OzTimelineGapUtil").isInGap(g,f.getTimeRanges(),e.$OzPlayheadManager$p_6))&&i!==-1){g=h[i];e.$OzPlayheadManager$p_27(g.rangeStart+c,"jump_over_timeline_gap",a,b)}};var f=b.video,g=b.mpd,j=b.playheadCatchup,k=b.playbackState,l=b.config,m=b.perfLoggerProvider,n=b.seekHandler,o=b.initialPlaybackPosition,p=b.canSupportSkipVideobufferGaps,q=b.liveLatencyManager;b=b.dynamicVideoLibrary;e.$OzPlayheadManager$p_1=f;e.$OzPlayheadManager$p_6=l;e.$OzPlayheadManager$p_7=k;e.$OzPlayheadManager$p_8=g;e.$OzPlayheadManager$p_9=j;e.$OzPlayheadManager$p_11=m;n&&(e.$OzPlayheadManager$p_12=n);e.$OzPlayheadManager$p_17=p;e.$OzPlayheadManager$p_20=q;e.$OzPlayheadManager$p_13=o;e.$OzPlayheadManager$p_21=b;e.$OzPlayheadManager$p_18=new i({video:f,perfLoggerProvider:m});e.$OzPlayheadManager$p_19=new h({video:f,perfLoggerProvider:m,config:l});e.$OzPlayheadManager$p_2.addSubscriptions(c("oz-player/shims/OzDOMEventListener").listenDOMEvent(e.$OzPlayheadManager$p_1,"durationchange",function(){e.setPlaybackPositionOnFirstDurationChange("durationchange",e.$OzPlayheadManager$p_1.duration),e.$OzPlayheadManager$p_23("durationchange"),e.$OzPlayheadManager$p_24("durationchange")}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(e.$OzPlayheadManager$p_1,"pause",function(){e.$OzPlayheadManager$p_23("pause")}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(e.$OzPlayheadManager$p_1,"play",function(){e.$OzPlayheadManager$p_23("play")}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(e.$OzPlayheadManager$p_1,"playing",function(){e.$OzPlayheadManager$p_23("playing")}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(e.$OzPlayheadManager$p_1,"waiting",function(){e.$OzPlayheadManager$p_23.bind(babelHelpers.assertThisInitialized(e),"waiting"),e.$OzPlayheadManager$p_24("waiting")}),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(e.$OzPlayheadManager$p_1,"timeupdate",c("oz-player/shims/ozThrottle")(function(){e.$OzPlayheadManager$p_23("timeupdate"),e.$OzPlayheadManager$p_6.getBool("playhead_manager_buffered_timerange_update_on_timeupdate")&&e.$OzPlayheadManager$p_24("timeupdate")},e.$OzPlayheadManager$p_6.getNumber("playhead_manager_timeupdate_throttle_ms"))),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(e.$OzPlayheadManager$p_1,"seeking",function(){e.$OzPlayheadManager$p_22=Date.now()}));return e}var e=b.prototype;e.$OzPlayheadManager$p_25=function(){return this.$OzPlayheadManager$p_1.playbackRate===0};e.observeSourceBufferState=function(a,b){var d=this;this.$OzPlayheadManager$p_4.has(a)&&c("oz-player/shims/ozvariant")(0,4179);this.$OzPlayheadManager$p_4.add(a);this.$OzPlayheadManager$p_2.addSubscriptions(a.addEventListener("buffer_updated",function(){d.$OzPlayheadManager$p_26(a,b),d.$OzPlayheadManager$p_6.getBool("skip_videobuffer_gaps_on_buffer_updated")&&d.$OzPlayheadManager$p_17&&d.$OzPlayheadManager$p_24("buffer_updated")}))};e.$OzPlayheadManager$p_26=function(a,b){var c=[];this.$OzPlayheadManager$p_4.forEach(function(a){a=a.getBufferedRanges();a.length&&c.push(a[0])});c.length>0&&this.$OzPlayheadManager$p_10.add(a);a=this.$OzPlayheadManager$p_1.currentTime;a=d("oz-player/strategies/LiveheadSeekaheadStrategy").computeSeekAhead(a,c);var e=this.$OzPlayheadManager$p_6.getNumber("timeline_offset_threshold");a>0&&(e===0||a<e)&&this.$OzPlayheadManager$p_27(this.$OzPlayheadManager$p_1.currentTime+a,"first_available_buffer","source_buffer",b)};e.setTimeRangeProvider=function(a){var b=this;this.$OzPlayheadManager$p_3.release();this.$OzPlayheadManager$p_3.engage();this.$OzPlayheadManager$p_3.addSubscriptions(a.addUpdateListener(function(){b.$OzPlayheadManager$p_23("time_range_updated")}));this.$OzPlayheadManager$p_5=a;this.$OzPlayheadManager$p_23("time_range_set")};e.resetCurrentTime=function(){this.$OzPlayheadManager$p_15=!1,this.setPlaybackPositionOnFirstDurationChange("reset",this.$OzPlayheadManager$p_1.duration)};e.setPlaybackPositionOnFirstDurationChange=function(a,b){if(this.$OzPlayheadManager$p_13!==0&&!this.$OzPlayheadManager$p_15&&!isNaN(b)&&b!==Infinity){b=this.$OzPlayheadManager$p_13>0?this.$OzPlayheadManager$p_13:b+this.$OzPlayheadManager$p_13;if(this.$OzPlayheadManager$p_20!=null){var c;b=(c=this.$OzPlayheadManager$p_20)==null?void 0:c.getInitialPlayHeadPosition()}this.$OzPlayheadManager$p_15=!0;if(this.$OzPlayheadManager$p_6.getBool("playhead_manager_clamp_initial_playback_position")){c=(c=this.$OzPlayheadManager$p_5)==null?void 0:c.getTimeRanges();c!=null&&c.length>0&&c[0].startTime>b&&(b=c[0].startTime)}this.$OzPlayheadManager$p_27(b,"initial_playback_position",a);this.emit("initialPlaybackPositionSet",b)}};e.$OzPlayheadManager$p_24=function(a){var b,c,e=this;if(!this.$OzPlayheadManager$p_6.getBool("skip_videobuffer_gaps"))return;if(!this.$OzPlayheadManager$p_17)return;var f=this.$OzPlayheadManager$p_6.getNumber("playhead_manager_buffered_auto_seek_playhead_slack");if(f===0)return;var g=this.$OzPlayheadManager$p_7.getCurrentTime(),h=[];for(var i=0;i<this.$OzPlayheadManager$p_1.buffered.length;i++)h.push({startTime:this.$OzPlayheadManager$p_1.buffered.start(i),endTime:this.$OzPlayheadManager$p_1.buffered.end(i)});i=h.map(function(a){return{rangeStart:a.startTime,rangeEnd:a.endTime}});this.$OzPlayheadManager$p_19&&this.$OzPlayheadManager$p_19.markState(a,this.$OzPlayheadManager$p_7);var j=d("oz-player/utils/OzNumericalRangeUtil").findNextRangeIndex(g,i),k=d("oz-player/utils/OzNumericalRangeUtil").findCurrentRangeIndex(this.$OzPlayheadManager$p_6,g,i),l=this.$OzPlayheadManager$p_16?0:this.$OzPlayheadManager$p_6.getNumber("skip_videobuffer_gaps_max_gap_size_sec"),m=this.$OzPlayheadManager$p_6.getNumber("playhead_manager_buffered_is_near_gap_threshold"),n=this.$OzPlayheadManager$p_6.getNumber("playhead_manager_buffered_numerical_error");b=(b=this.$OzPlayheadManager$p_21)==null?void 0:b.getLastSegmentStartPTS();var o=b!=null?b/1e3:null,p=(c=this.$OzPlayheadManager$p_21)==null?void 0:c.getLast200RequestedUrlDecisionMinTime();c=o!=null?d("oz-player/utils/OzNumericalRangeUtil").findCurrentRangeIndex(this.$OzPlayheadManager$p_6,o,i):-1;var q=c>=0?i[c]:null,r=function(b){var c=e.$OzPlayheadManager$p_22==null||p==null||e.$OzPlayheadManager$p_22<p;!c&&b!=null&&e.$OzPlayheadManager$p_11.getOperationLogger("playhead_adjustment").setError("ignore_before_seek_main_"+b).setResult("failed").setInitiator(a).setUserInfo({last200RequestedUrlDecisionMinTime:String(p),lastSeekTime:String(e.$OzPlayheadManager$p_22)}).log();return c};if(o!=null&&this.$OzPlayheadManager$p_25()&&this.$OzPlayheadManager$p_16==!1&&r("buf")){if(o<g&&q!=null&&q.rangeStart<=o&&q.rangeEnd!=null&&q.rangeEnd>o){this.$OzPlayheadManager$p_27(o,"reverse_jump_over_buffer_gap",a,null,{lastSegmentStartPTS:String(b),last200RequestedUrlDecisionMinTime:String(p),lastSeekTime:String(this.$OzPlayheadManager$p_22)});return}if(q!=null&&c!=k){this.$OzPlayheadManager$p_27(q.rangeStart,"jump_over_buffer_gap_last_dl_range",a,null,{lastSegmentStartPTS:String(b),last200RequestedUrlDecisionMinTime:String(p),lastSeekTime:String(this.$OzPlayheadManager$p_22)});return}}if((d("oz-player/utils/OzTimelineGapUtil").isNearGap(g,h,this.$OzPlayheadManager$p_6,n,m)||d("oz-player/utils/OzTimelineGapUtil").isInGap(g,h,this.$OzPlayheadManager$p_6,n))&&j!==-1&&(l===0||i[j].rangeStart-g<l)&&(this.$OzPlayheadManager$p_16==!0||r("main"))){o=i[j];this.$OzPlayheadManager$p_6.getBool("enable_alternative_audio_tracks")&&this.$OzPlayheadManager$p_8.isStaticMpd()&&o.rangeStart!==0&&o.rangeEnd===this.$OzPlayheadManager$p_7.getDuration()?this.$OzPlayheadManager$p_27(0,"do_not_jump_over_buffer_gap",a,null,{lastSegmentStartPTS:String(b),last200RequestedUrlDecisionMinTime:String(p),lastSeekTime:String(this.$OzPlayheadManager$p_22)}):this.$OzPlayheadManager$p_27(o.rangeStart+f,"jump_over_buffer_gap",a,null,{lastSegmentStartPTS:String(b),last200RequestedUrlDecisionMinTime:String(p),lastSeekTime:String(this.$OzPlayheadManager$p_22)});return}this.$OzPlayheadManager$p_18&&this.$OzPlayheadManager$p_18.markState(a,{nextBufferedRangeIndex:String(j),limitMaxGapSize:String(l),rangeStart:j!==-1?String(i[j].rangeStart):null,isNearGapThreshold:String(m),numericalError:String(n)})};e.setEnableLiveheadCatchup=function(a){this.$OzPlayheadManager$p_16=a};e.destroy=function(){this.$OzPlayheadManager$p_2.release(),this.$OzPlayheadManager$p_3.release(),this.$OzPlayheadManager$p_4.clear()};e.$OzPlayheadManager$p_27=function(a,b,c,d,e){var f=a-this.$OzPlayheadManager$p_1.currentTime,g=this.$OzPlayheadManager$p_6.getNumber("seek_ahead_epsilon");if(g>0&&Math.abs(f)<=g)return;this.$OzPlayheadManager$p_11.getOperationLogger("playhead_adjustment").setLength(Math.round(a*1e3)).setReason(b).setInitiator(c).setType(d).setUserInfo(babelHelpers["extends"]({},e,{seekAdjustment:String(Math.round(f*1e3))})).log();this.$OzPlayheadManager$p_12(a);b==="initial_playback_position"&&(this.$OzPlayheadManager$p_14=!0)};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/shims/www/OzUuidWWW",["uuidv4"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("uuidv4")}),98);
__d("oz-player/shims/OzUuid",["oz-player/shims/www/OzUuidWWW"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/shims/www/OzUuidWWW")}),98);
__d("oz-player/states/OzObservedPlaybackState",["oz-player/loggings/OzMultiDestinationPerfLogger","oz-player/shims/OzDOMEventListener","oz-player/shims/OzSubscriptionsHandler"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,e){var f=this;e===void 0&&(e=new(d("oz-player/loggings/OzMultiDestinationPerfLogger").OzMultiDestinationPerfLoggerProvider)([]));this.$2=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$4=function(){f.$3.getOperationLogger("pause").setState("paused").log()};this.$5=function(){f.$3.getOperationLogger("playing").setState("playing").log()};this.$1=b;this.$3=e;this.$2.addSubscriptions(c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"pause",this.$4),c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,"playing",this.$5))}var b=a.prototype;b.isPlaying=function(){return!this.$1.paused};b.getCurrentTime=function(){return this.$1.currentTime};b.getCurrentTimeUpdateTime=function(){return Date.now()};b.getDuration=function(){return this.$1.duration};b.addEventListener=function(a,b){return c("oz-player/shims/OzDOMEventListener").listenDOMEvent(this.$1,a,b)};b.destroy=function(){this.$2.release()};return a}();g["default"]=a}),98);
__d("oz-player/utils/OzPositionToViewportUtils",[],(function(a,b,c,d,e,f){"use strict";function a(a,b,c){b=b.filter(function(a){a=a.position;if(a==null)return!1;var b=a.y-window.scrollY;return b>=0||(a.height+b)/a.height>c}).sort(function(a,b){return a.position==null||b.position==null?0:a.position.y-b.position.y});return b.findIndex(function(b){return b.video===a})}function b(a){return a.isIntersecting!=null?a.isIntersecting:a.intersectionRatio>0||a.intersectionRect&&(a.intersectionRect.height>0||a.intersectionRect.width>0)}f.determinePositionIndexOfVideoFromViewportVertically=a;f.intersectionObserverEntryIsIntersecting=b}),66);
__d("oz-player/states/OzPositionToViewportTracker",["oz-player/shims/OzEventEmitter","oz-player/utils/OzPositionToViewportUtils"],(function(a,b,c,d,e,f,g){"use strict";var h=null,i=.5;a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var c;c=a.call(this)||this;c.$OzPositionToViewportTracker$p_1=[];c.$OzPositionToViewportTracker$p_5=function(a){a.forEach(function(a){var b=c.$OzPositionToViewportTracker$p_1.find(function(b){return a.target===b.video});b!=null&&(d("oz-player/utils/OzPositionToViewportUtils").intersectionObserverEntryIsIntersecting(a)?b.position=c.$OzPositionToViewportTracker$p_7(a):c.$OzPositionToViewportTracker$p_1.map(function(b){a.target===b.video&&(b.position=null)}));c.$OzPositionToViewportTracker$p_1.forEach(function(a){a.positionIndexToViewportVertically=c.$OzPositionToViewportTracker$p_6(a.video)})}),c.emit("state_changed")};c.$OzPositionToViewportTracker$p_2=b;return c}var c=b.prototype;c.$OzPositionToViewportTracker$p_4=function(){if(this.$OzPositionToViewportTracker$p_3!=null)return;this.$OzPositionToViewportTracker$p_3=new IntersectionObserver(this.$OzPositionToViewportTracker$p_5,{rootMargin:this.$OzPositionToViewportTracker$p_2.getNumber("pixels_above_viewport_to_observe")+"px 0px "+this.$OzPositionToViewportTracker$p_2.getNumber("pixels_below_viewport_to_observe")+"px 0px",threshold:0})};c.registerVideo=function(a){this.$OzPositionToViewportTracker$p_1.push({video:a,position:null,positionIndexToViewportVertically:-1}),this.$OzPositionToViewportTracker$p_4(),this.$OzPositionToViewportTracker$p_3!=null&&this.$OzPositionToViewportTracker$p_3.observe(a)};c.getPositionIndexOfVideoFromViewportVertically=function(a){var b=this.$OzPositionToViewportTracker$p_1.find(function(b){return b.video===a});return b!=null?b.positionIndexToViewportVertically:-1};c.$OzPositionToViewportTracker$p_6=function(a){return d("oz-player/utils/OzPositionToViewportUtils").determinePositionIndexOfVideoFromViewportVertically(a,this.$OzPositionToViewportTracker$p_1,i)};c.$OzPositionToViewportTracker$p_7=function(a){a=a.boundingClientRect;return{x:a.x+window.scrollX,y:a.y+window.scrollY,width:a.width,height:a.height}};b.getInstance=function(a){h==null&&(h=new b(a));return h};c.unregisterVideo=function(a){var b=this,c=[];this.$OzPositionToViewportTracker$p_1.forEach(function(d){d.video===a&&b.$OzPositionToViewportTracker$p_3!=null?b.$OzPositionToViewportTracker$p_3.unobserve(a):c.push(d)});this.$OzPositionToViewportTracker$p_1=c;this.$OzPositionToViewportTracker$p_1.length===0&&this.$OzPositionToViewportTracker$p_3!=null&&(this.$OzPositionToViewportTracker$p_3.disconnect(),this.$OzPositionToViewportTracker$p_3=null)};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/states/OzPositionToViewport",["oz-player/shims/OzEventEmitter","oz-player/shims/OzSubscriptionsHandler","oz-player/states/OzPositionToViewportTracker"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,d){var e;e=a.call(this)||this;e.$OzPositionToViewport$p_1=new(c("oz-player/shims/OzSubscriptionsHandler"))();e.$OzPositionToViewport$p_3=b;e.$OzPositionToViewport$p_2=c("oz-player/states/OzPositionToViewportTracker").getInstance(d);e.$OzPositionToViewport$p_2.registerVideo(b);e.$OzPositionToViewport$p_1.addSubscriptions(e.$OzPositionToViewport$p_2.addListener("state_changed",function(){e.emit("state_changed")}));return e}var d=b.prototype;d.getPositionIndexOfVideoFromViewportVertically=function(){return this.$OzPositionToViewport$p_3!=null?this.$OzPositionToViewport$p_2.getPositionIndexOfVideoFromViewportVertically(this.$OzPositionToViewport$p_3):-1};d.destroy=function(){this.$OzPositionToViewport$p_3!=null&&this.$OzPositionToViewport$p_2.unregisterVideo(this.$OzPositionToViewport$p_3),this.$OzPositionToViewport$p_3=null};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/strategies/OzPlaybackConfidenceUtils",[],(function(a,b,c,d,e,f){"use strict";var g=.8;function a(a,b,c,d,e,f){f=Math.min(1,c/d);c=1+(1-f)*e;d=a.getBandwidth()*c;return d>b?g*(b/d):1-(1-g)*d/b}f.getPlaybackConfidence=a}),66);
__d("oz-player/strategies/OzAbrManager",["oz-player/networks/OzBandwidthEstimator","oz-player/strategies/OzPlaybackConfidenceUtils","oz-player/utils/OzAbrUtils","oz-player/utils/OzBufferingUtils","oz-player/utils/OzPlaybackRestrictionsUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,c,d,e,f,g,h,i){this.$2=0,this.$3=Date.now(),this.$4=!1,this.$10=null,this.$1=a,this.$5=b,this.$6=this.$15(b,c),this.$7=d,this.$8=e,this.$11=f,this.$12=g,this.$13=h,this.$14=i,this.$1.getBool("enable_abr_logging")&&this.$14.getOperationLogger("abr_initialized").setResult("success").setUserInfo({newRepresentationIds:c.map(function(a){return a.getID()}).join(","),newCodecs:c.map(function(a){return a.getCodecs()}).join("|"),newDisplayLabels:c.map(function(a){return a.getDisplayLabel()}).join(",")}).setRepresentationID(b.getID()).setType("video").setInitiator("OzAbrManager").log()}var b=a.prototype;b.setSourceBuffer=function(a){this.$9=a};b.$15=function(a,b){var c=this.$1.getNumber("abr_restrict_from_index"),d=this.$1.getNumber("abr_restrict_to_index");if(d===0&&c===0)return b;var e=b.findIndex(function(b){return a.getID()===b.getID()});return e===-1?b:b.filter(function(a,b){return b-e>=c&&b-e<=d})};b.$16=function(a){a=d("oz-player/utils/OzAbrUtils").excludeLargeRepresentations(a,this.$12(),this.$11,{resolutionConstraintFactor:this.$1.getNumber("resolution_constraint_factor")});a=d("oz-player/utils/OzPlaybackRestrictionsUtils").applyVideoPlaybackRestrictions(this.$7,this.$12(),a);return a};b.getBestRepresentation=function(a){var b;a=(a=a==null?void 0:a.length)!=null?a:null;b=(b=(b=this.$9)==null?void 0:b.getSourceBufferState().getBufferedRanges())!=null?b:null;var e=this.$8.getCurrentTime();e=d("oz-player/utils/OzBufferingUtils").getBufferAheadFromCurrentTime(e,b);b=d("oz-player/utils/OzAbrUtils").getReasonToPreventEvaluation(this.$1,this.$4,e,c("oz-player/networks/OzBandwidthEstimator").getSampleCount(),this.$2,this.$3);if(b!=null){this.$10=b;this.$1.getBool("enable_abr_logging")&&this.$14.getOperationLogger("abr_best_representation_selected").setResult("failed").setReason(this.$10).setType("video").setInitiator("OzAbrManager").log();return this.$5}b=Date.now();this.$2=b;var f=c("oz-player/networks/OzBandwidthEstimator").getAdjustedBandwidth(this.$1.getLegacyConfig()),g=this.$16(this.$6);f=this.$17(g,f,e,a);a=g[0];f=f||a;if(this.$18(e,f)){this.$10="high_buffer";this.$1.getBool("enable_abr_logging")&&this.$14.getOperationLogger("abr_best_representation_selected").setResult("failed").setUserInfo({filteredRepresentationIds:g.map(function(a){return a.getID()}).join(",")}).setPreviousRepresentationID(this.$5.getID()).setReason(this.$10).setType("video").setInitiator("OzAbrManager").log();return this.$5}a=this.$5;a!==f&&(this.$4=!0,this.$3=b,this.$5=f);this.$10="bandwidth";this.$1.getBool("enable_abr_logging")&&this.$14.getOperationLogger("abr_best_representation_selected").setResult("success").setUserInfo({filteredRepresentationIds:g.map(function(a){return a.getID()}).join(",")}).setRepresentationID(f.getID()).setPreviousRepresentationID(a==null?void 0:a.getID()).setReason(this.$10).setType("video").setInitiator("OzAbrManager").log();return f};b.$17=function(a,b,c,e){e===void 0&&(e=null);var f=null;for(var g=0;g<a.length;g++){var h=d("oz-player/strategies/OzPlaybackConfidenceUtils").getPlaybackConfidence(a[g],b,c,this.$1.getNumber("low_buffer_bandwidth_target_threshold"),this.$1.getNumber("low_buffer_bandwidth_target_increase_factor"),e);h>this.$1.getNumber("abr_confidence_threshold")&&(!f||f.getBandwidth()<a[g].getBandwidth())&&(f=a[g])}return f};b.$18=function(a,b){var c=this.$1.getNumber("abr_prevent_down_switch_buffer_threshold");return c>0&&a>=c&&this.$5.getBandwidth()>b.getBandwidth()?!0:!1};b.getLastEvaluationReason=function(){return this.$10};b.updateRepresentations=function(a){var b=this,c=this.$6;this.$6=a;var d=this.$6.find(function(a){return a.getDisplayLabel()===b.$5.getDisplayLabel()}),e=this.$5;this.$5=d!=null?d:this.$6[0];this.$1.getBool("enable_abr_logging")&&this.$14.getOperationLogger("abr_representations_updated").setResult("success").setUserInfo({previousRepresentationIds:c.map(function(a){return a.getID()}).join(","),newRepresentationIds:a.map(function(a){return a.getID()}).join(","),newCodecs:a.map(function(a){return a.getCodecs()}).join("|"),newDisplayLabels:a.map(function(a){return a.getDisplayLabel()}).join(",")}).setRepresentationID(this.$5.getID()).setPreviousRepresentationID(e==null?void 0:e.getID()).setReason(this.$10).setType("video").setInitiator("OzAbrManager").log()};return a}();g["default"]=a}),98);
__d("oz-player/strategies/getSegmentsCount",[],(function(a,b,c,d,e,f){"use strict";function a(a,b,c,d){var e=0,f=a.getTimeRange().startTime;a=a;var g=a.getTimeRange().startTime;while(a!==null&&g<c+f){var h=g;e++;a=b.getSegmentAfter(a);if(a)g=a.getTimeRange().startTime;else break;if(g<=h)return d}return Math.max(e,1)}f["default"]=a}),66);
__d("oz-player/strategies/OzStaticStreamSegmentsStrategy",["oz-player/strategies/getSegmentsCount"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a){this.$1=a}var b=a.prototype;b.isActive=function(){return!0};b.getSegmentsCount=function(a,b,d){a=this.$1.getNumber("seconds_to_stream");var e=this.$1.getNumber("segments_to_stream");a>0&&(e=c("oz-player/strategies/getSegmentsCount")(b,d,a,e));return e};return a}();g["default"]=a}),98);
__d("oz-player/strategies/OzBandwidthBoundaryStreamSegmentsCountStrategy",["oz-player/networks/OzBandwidthEstimator","oz-player/strategies/OzStaticStreamSegmentsStrategy","oz-player/strategies/getSegmentsCount"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){this.$1=a,this.$2=b,this.$3=new(c("oz-player/strategies/OzStaticStreamSegmentsStrategy"))(a)}var b=a.prototype;b.isActive=function(a){return!0};b.getSegmentsCount=function(a,b,d){var e=c("oz-player/networks/OzBandwidthEstimator").getBandwidth(this.$1.getLegacyConfig()),f=c("oz-player/networks/OzBandwidthEstimator").getStandardDeviationOfBandwidth(),g=this.$1.getNumber("bandwidth_boundary_standard_deviation_factor"),h=this.$2.some(function(a){a=a.getBandwidth();return Math.abs(a-e)<f*g});h=h?this.$4(a,b,d):this.$3.getSegmentsCount(a,b,d);a=this.$5(b,e);return Math.min(h,a)};b.$4=function(a,b,d){a=this.$1.getNumber("seconds_to_stream_near_bandwidth_boundary");var e=this.$1.getNumber("segments_to_stream_near_bandwidth_boundary");a>0&&(e=c("oz-player/strategies/getSegmentsCount")(b,d,a,e));return e};b.$5=function(a,b){var c=Infinity;a=a.getByteRange();if(a){var d=a.endByte;if(d!=null){var e=this.$1.getNumber("per_stream_duration_target");d=d-a.startByte;e>1e-5&&d!==0&&(c=Math.ceil(e*b/(d*8)))}}return c};return a}();g["default"]=a}),98);
__d("oz-player/strategies/OzBlockedRepresentationsManager",["oz-player/shims/OzEventEmitter","oz-player/shims/ozClearTimeout","oz-player/shims/ozSetTimeoutAcrossTransitions","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){this.$2=new Map(),this.$3=new Set(),this.$4=new Set(),this.$5=new(c("oz-player/shims/OzEventEmitter"))(),this.$6=b===!0,this.$7(a),this.$8(a)}var b=a.prototype;b.isBlocked=function(a){return this.$3.has(a)};b.handleError=function(a,b){if(this.$1!=null&&this.$3.size===this.$1-1)return!1;if(d("oz-player/utils/OzErrorUtils").isOzError(b)&&b.getType()==="OZ_NETWORK"){b=Number(b.getExtra().code);if(!isNaN(b)&&this.$2.has(b)){this.$3.has(a)||this.$9(a,b);return!0}}return!1};b.$9=function(a,b){var d,e=this;this.$3.add(a);(d=this.$5)==null?void 0:d.emit("representation_blocked",a);d=this.$2.get(b)||0;if(d>0){var f=c("oz-player/shims/ozSetTimeoutAcrossTransitions")(function(){e.$3["delete"](a),e.$4["delete"](f)},d);this.$4.add(f)}};b.applyRestriction=function(a){var b=this;return a.filter(function(a){return!b.$3.has(a.getID())})};b.onRepresentationBlocked=function(a){return this.$5?this.$5.addListener("representation_blocked",a):{remove:function(){}}};b.setAvailableRepresentationsCount=function(a){this.$1=a};b.destroy=function(){var a;(a=this.$5)==null?void 0:a.removeAllListeners();this.$5=null;for(a of this.$4)c("oz-player/shims/ozClearTimeout")(a);this.$4.clear()};b.$7=function(a){var b=this;try{a=JSON.parse(a.getString("block_representation_status_codes_json"));if(Array.isArray(a)){a.forEach(function(a){a=parseInt(a,10);isNaN(a)||b.$2.set(a,-1)});if(this.$6)for(a=500;a<=599;a++)this.$2.set(a,-1)}}catch(a){}};b.$8=function(a){var b=this;try{a=JSON.parse(a.getString("block_representation_status_codes_temporarily_json"));Object.entries(a).forEach(function(a){var c=a[0];a=a[1];c=parseInt(c,10);a=parseInt(a,10);!isNaN(c)&&!isNaN(a)&&b.$2.set(c,a)})}catch(a){}};return a}();g["default"]=a}),98);
__d("oz-player/strategies/OzBufferAheadPriorityStrategy",["oz-player/shims/OzEventEmitter","oz-player/shims/OzSubscriptionsHandler","oz-player/shims/ozThrottle","oz-player/utils/OzBufferingUtils"],(function(a,b,c,d,e,f,g){"use strict";var h=6,i=1e3;a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,d){var e;e=a.call(this)||this;e.$OzBufferAheadPriorityStrategy$p_3=new(c("oz-player/shims/OzSubscriptionsHandler"))();e.$OzBufferAheadPriorityStrategy$p_5=function(){e.emit("state_changed")};e.$OzBufferAheadPriorityStrategy$p_4=b;e.$OzBufferAheadPriorityStrategy$p_1=d;e.$OzBufferAheadPriorityStrategy$p_3.addSubscriptions(e.$OzBufferAheadPriorityStrategy$p_1.addEventListener("progress",c("oz-player/shims/ozThrottle")(e.$OzBufferAheadPriorityStrategy$p_5,i)),e.$OzBufferAheadPriorityStrategy$p_1.addEventListener("timeupdate",c("oz-player/shims/ozThrottle")(e.$OzBufferAheadPriorityStrategy$p_5,i)));return e}var e=b.prototype;e.getName=function(){return"buffer_ahead"};e.setSourceBufferState=function(a){if(this.$OzBufferAheadPriorityStrategy$p_2)return;this.$OzBufferAheadPriorityStrategy$p_2=a;this.$OzBufferAheadPriorityStrategy$p_3.addSubscriptions(a.addEventListener("buffer_updated",c("oz-player/shims/ozThrottle")(this.$OzBufferAheadPriorityStrategy$p_5,i)))};e.isActive=function(){return!0};e.getPriority=function(){var a=this.$OzBufferAheadPriorityStrategy$p_2;if(!a)return 1;var b=this.$OzBufferAheadPriorityStrategy$p_1.getCurrentTime();a=a.getBufferedRanges();b=d("oz-player/utils/OzBufferingUtils").getBufferAheadFromCurrentTime(b,a);return b>=h?0:1};e.destroy=function(){this.$OzBufferAheadPriorityStrategy$p_3.release()};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/strategies/OzBufferAheadTargetStrategy",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a){this.$1=a}var b=a.prototype;b.isActive=function(){return!0};b.getBufferTarget=function(){return this.$1.getNumber("buffer_ahead_target")};return a}();f["default"]=a}),66);
__d("oz-player/strategies/OzBufferEndLimitStrategy",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.$1=a,this.$2=b}var b=a.prototype;b.isActive=function(){return!0};b.getBufferTarget=function(){var a=this.$3();return this.$2>=a?this.$2-a:0};b.$3=function(){return this.$1.buffered.length===0?0:this.$1.buffered.end(0)};return a}();f["default"]=a}),66);
__d("oz-player/strategies/OzBufferTargetBoundedStreamSegmentsCountStrategy",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.$1=a,this.$2=b}var b=a.prototype;b.isActive=function(a){return!a.isPlaying()};b.getSegmentsCount=function(a,b,c){a=a.getCurrentTime();var d=this.$1.computeBufferTarget(),e=1,f=b;b=b.getTimeRange().startTime;while(f!==null&&b<d+a){f=c.getSegmentAfter(f);if(!f)break;b=f.getTimeRange().startTime;e++}return e};return a}();f["default"]=a}),66);
__d("oz-player/strategies/OzBufferTargetCalculator",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.$1=0,this.$2=a,this.$3=b}var b=a.prototype;b.computeBufferTarget=function(){var a=this.$1;for(var b of this.$2)if(b.isActive()){a=b.getBufferTarget();break}for(b of this.$3)a=b.adjustBufferTargetInSeconds(a);this.$1=a;return a};b.handleEvent=function(a){for(var b of this.$3)b.handleEvent(a)};return a}();f["default"]=a}),66);
__d("oz-player/strategies/OzBufferTargetCalculatorQuotaExceededConstraint",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a){var b=a.config;a=a.minimumBufferTargetSec;this.$2=1;this.$1=b;this.$3=a}var b=a.prototype;b.adjustBufferTargetInSeconds=function(a){var b;return Math.max((b=this.$3)!=null?b:this.$1.getNumber("buffer_target_constraint_minimum_sec"),a*this.$2)};b.handleEvent=function(a){var b=this.$2;switch(a){case"append_succeeded":b=Math.max(0,Math.min(1,b+this.$1.getNumber("buffer_target_constraint_append_succeeded_reward")));break;case"append_quota_exceeded":b=Math.max(0,Math.min(1,b-this.$1.getNumber("buffer_target_constraint_quota_exceeded_penalty")));break;default:a}this.$2=b};return a}();f["default"]=a}),66);
__d("oz-player/strategies/OzPausedStreamSegmentsCountStrategy",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.$1=a,this.$2=b}var b=a.prototype;b.isActive=function(){return!this.$1.isPlaying()};b.getSegmentsCount=function(a,b,c){return this.$2.getNumber("paused_stream_segments_count")};return a}();f["default"]=a}),66);
__d("oz-player/strategies/OzPlayheadAtInterruptionDetector",["oz-player/shims/OzDOMEventListener","oz-player/shims/OzEventEmitter","oz-player/shims/OzSubscriptionsHandler"],(function(a,b,c,d,e,f,g){"use strict";var h=1;a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,d){var e;e=a.call(this)||this;e.$OzPlayheadAtInterruptionDetector$p_2=null;e.$OzPlayheadAtInterruptionDetector$p_3=new(c("oz-player/shims/OzSubscriptionsHandler"))();e.$OzPlayheadAtInterruptionDetector$p_4=null;e.$OzPlayheadAtInterruptionDetector$p_1=b;b=d?d.addListener("enterBuffering",function(){e.$OzPlayheadAtInterruptionDetector$p_5()}):c("oz-player/shims/OzDOMEventListener").listenDOMEvent(e.$OzPlayheadAtInterruptionDetector$p_1,"waiting",function(){e.$OzPlayheadAtInterruptionDetector$p_5()});e.$OzPlayheadAtInterruptionDetector$p_3.addSubscriptions(b);e.$OzPlayheadAtInterruptionDetector$p_4=d;return e}var d=b.prototype;d.notifyStreamInterrupted=function(a){this.$OzPlayheadAtInterruptionDetector$p_2=a,this.$OzPlayheadAtInterruptionDetector$p_5()};d.notifyStreamResumed=function(){this.$OzPlayheadAtInterruptionDetector$p_2=null};d.$OzPlayheadAtInterruptionDetector$p_5=function(){if(this.$OzPlayheadAtInterruptionDetector$p_2!=null){var a=this.$OzPlayheadAtInterruptionDetector$p_2||0;Math.abs(this.$OzPlayheadAtInterruptionDetector$p_1.currentTime-a)<h&&this.emit("playheadAtInterruption",a)}};d.destroy=function(){this.$OzPlayheadAtInterruptionDetector$p_3.release(),this.$OzPlayheadAtInterruptionDetector$p_4=null};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/strategies/OzPlayingStatePriorityStrategy",["oz-player/shims/OzEventEmitter","oz-player/shims/OzSubscriptionsHandler"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var d;d=a.call(this)||this;d.$OzPlayingStatePriorityStrategy$p_1=new(c("oz-player/shims/OzSubscriptionsHandler"))();d.$OzPlayingStatePriorityStrategy$p_3=function(){d.emit("state_changed")};d.$OzPlayingStatePriorityStrategy$p_2=b;d.$OzPlayingStatePriorityStrategy$p_1.addSubscriptions(d.$OzPlayingStatePriorityStrategy$p_2.addEventListener("playing",d.$OzPlayingStatePriorityStrategy$p_3),d.$OzPlayingStatePriorityStrategy$p_2.addEventListener("play",d.$OzPlayingStatePriorityStrategy$p_3),d.$OzPlayingStatePriorityStrategy$p_2.addEventListener("pause",d.$OzPlayingStatePriorityStrategy$p_3));return d}var d=b.prototype;d.isActive=function(){return!0};d.getName=function(){return"playing_state"};d.setSourceBufferState=function(a){};d.getPriority=function(){return this.$OzPlayingStatePriorityStrategy$p_2.isPlaying()?1:0};d.destroy=function(){this.$OzPlayingStatePriorityStrategy$p_1.release()};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/strategies/OzPositionToViewportPriorityStrategy",["oz-player/shims/OzEventEmitter","oz-player/shims/OzSubscriptionsHandler"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,d){var e;e=a.call(this)||this;e.$OzPositionToViewportPriorityStrategy$p_3=new(c("oz-player/shims/OzSubscriptionsHandler"))();e.$OzPositionToViewportPriorityStrategy$p_4=function(){e.emit("state_changed")};e.$OzPositionToViewportPriorityStrategy$p_2=b;e.$OzPositionToViewportPriorityStrategy$p_1=d;e.$OzPositionToViewportPriorityStrategy$p_3.addSubscriptions(b.addListener("state_changed",e.$OzPositionToViewportPriorityStrategy$p_4));return e}var d=b.prototype;d.isActive=function(){return!0};d.getName=function(){return"position_to_viewport"};d.setSourceBufferState=function(a){};d.getPriority=function(){var a=this.$OzPositionToViewportPriorityStrategy$p_1.getNumber("prioritize_by_viewport_static_penalty"),b=this.$OzPositionToViewportPriorityStrategy$p_2.getPositionIndexOfVideoFromViewportVertically();return a>0?b===-1?a*-1:0:b===-1?-Infinity:-1*b};d.destroy=function(){this.$OzPositionToViewportPriorityStrategy$p_3.release()};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/strategies/OzPriorityCalculator",["oz-player/shims/OzEventEmitter","oz-player/shims/OzSubscriptionsHandler"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var d;d=a.call(this)||this;d.$OzPriorityCalculator$p_2=new(c("oz-player/shims/OzSubscriptionsHandler"))();d.$OzPriorityCalculator$p_1=b;(b=d.$OzPriorityCalculator$p_2).addSubscriptions.apply(b,d.$OzPriorityCalculator$p_1.map(function(a){return a.addListener("state_changed",function(){d.emit("state_changed",d.compute(),a.getName())})}));return d}var d=b.prototype;d.setSourceBufferState=function(a){this.$OzPriorityCalculator$p_1.forEach(function(b){return b.setSourceBufferState(a)}),this.emit("state_changed",this.compute(),"source_buffer")};d.compute=function(){var a=this.$OzPriorityCalculator$p_1.reduce(function(a,b){b=b.isActive()?b.getPriority():0;return a+b},0);return Math.max(a,0)};d.destroy=function(){this.$OzPriorityCalculator$p_2.release()};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/strategies/OzSingleCodecRestriction",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(){}var b=a.prototype;b.applyRestriction=function(a){if(a.length<=0)return a.slice();var b=new Set(a.map(function(a){return g(a)})),c="av01",d=b.has(c)?c:b.values().next().value;return d==null?a.slice():a.filter(function(a){return g(a)===d})};return a}();function g(a){return a.getMimeCodecs().replace(/\..*$/,"")}f["default"]=a}),66);
__d("oz-player/strategies/OzStaleManifestBufferTargetStrategy",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.$1=b,this.$2=a}var b=a.prototype;b.isActive=function(){return this.$2.getRefreshDate()==null&&this.$1.getNumber("stale_mpd_buffer_ahead_target")>0};b.getBufferTarget=function(){return this.$1.getNumber("stale_mpd_buffer_ahead_target")};return a}();f["default"]=a}),66);
__d("oz-player/strategies/OzStartupBufferTargetStrategy",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a,b){this.$1=a,this.$3=b}var b=a.prototype;b.isActive=function(){var a=this.$1.isPlaying();a&&(this.$2=!0);return!this.$2};b.getBufferTarget=function(){return this.$3.getNumber("pre_start_buffer_ahead_target")};return a}();f["default"]=a}),66);
__d("oz-player/strategies/OzStreamSegmentsCountCalculator",[],(function(a,b,c,d,e,f){"use strict";var g=1;a=function(){function a(a){this.$1=a}var b=a.prototype;b.computeMin=function(a,b,c){var d=Number.MAX_VALUE;for(var e of this.$1)e.isActive(a)&&(d=Math.min(d,e.getSegmentsCount(a,b,c)));return d===Number.MAX_VALUE?g:Math.max(d,g)};return a}();f["default"]=a}),66);
__d("oz-player/strategies/OzSupportedMimeCodecsRestriction",[],(function(a,b,c,d,e,f){"use strict";a=function(){function a(a){this.$1=a}var b=a.prototype;b.applyRestriction=function(a){var b=this;return a.filter(function(a){a=b.$1?ManagedMediaSource.isTypeSupported(a.getMimeCodecs()):MediaSource.isTypeSupported(a.getMimeCodecs());return!!a})};return a}();f["default"]=a}),66);
__d("oz-player/streams/OzEndOfStreamWatcher",["oz-player/shims/OzEventEmitter","oz-player/shims/ozvariant"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;d=a.call(this)||this;d.$OzEndOfStreamWatcher$p_1=new Map();d.$OzEndOfStreamWatcher$p_4=!1;d.$OzEndOfStreamWatcher$p_2=b;d.$OzEndOfStreamWatcher$p_3=c;return d}var d=b.prototype;d.registerMediaStream=function(a){this.$OzEndOfStreamWatcher$p_1.has(a)&&c("oz-player/shims/ozvariant")(0,5191),this.$OzEndOfStreamWatcher$p_1.set(a,!1)};d.notifyRepresentationSwitched=function(a){this.$OzEndOfStreamWatcher$p_1.has(a)||c("oz-player/shims/ozvariant")(0,5192),this.$OzEndOfStreamWatcher$p_1.get(a)===!0&&this.$OzEndOfStreamWatcher$p_1.set(a,!1),this.$OzEndOfStreamWatcher$p_5("end_of_stream_watcher","representation_switched")};d.notifyMediaStreamEnded=function(a,b){this.$OzEndOfStreamWatcher$p_1.has(a)||c("oz-player/shims/ozvariant")(0,5192),this.$OzEndOfStreamWatcher$p_1.set(a,!0),this.$OzEndOfStreamWatcher$p_5("end_of_stream_watcher","media_stream_ended:"+b)};d.$OzEndOfStreamWatcher$p_5=function(a,b){var c=this;if(this.$OzEndOfStreamWatcher$p_3.getBool("do_not_end_stream")){!this.$OzEndOfStreamWatcher$p_4&&this.$OzEndOfStreamWatcher$p_6()&&(this.$OzEndOfStreamWatcher$p_4=!0,this.emit("streamEnd"));return}var d=this.$OzEndOfStreamWatcher$p_2.getReadyState();d==="open"&&this.$OzEndOfStreamWatcher$p_6()&&this.$OzEndOfStreamWatcher$p_2.notifyEndOfStream(a,b).then(function(){c.emit("streamEnd")})["catch"](function(){c.emit("streamEnd")})};d.$OzEndOfStreamWatcher$p_6=function(){if(this.$OzEndOfStreamWatcher$p_1.size===0)return!1;for(var a of this.$OzEndOfStreamWatcher$p_1.values())if(!a)return!1;return!0};d.destroy=function(){this.$OzEndOfStreamWatcher$p_1.clear()};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/streams/OzHandleUserRepresentationSwitch",["oz-player/shims/OzSubscriptionsHandler","oz-player/shims/ozReportUnexpectedError","oz-player/utils/OzErrorEmitter"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,d,e,f,g){var h=this;this.$2=new(c("oz-player/utils/OzErrorEmitter"))();this.$5=null;this.$6=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$1=a;this.$3=b;this.$4=d;this.$7=e;this.$8=g;f.then(function(a){h.$5=a},function(){})["catch"](function(a){c("oz-player/shims/ozReportUnexpectedError")(a,"OzHandleUserRepresentationSwitch SBM promise then - create")});this.$6.addSubscriptions(e.addListener("switchRepresentation",function(a,b){h.$9(a,b)}))}var b=a.prototype;b.destroy=function(){this.$6.release(),this.$6.engage()};b.onError=function(a){return this.$2.onError(a)};b.$9=function(a,b){if(a!=="user")return;a=this.$5;if(a==null)return;var d=this.$7.getRepresentationIDAtTime(this.$3.getCurrentTime()),e;for(var f=0;f<this.$4.length;f++)this.$4[f].getID()===d&&(e=this.$4[f]);if(e&&b&&(this.$8||e.getBandwidth()<b.getBandwidth())){d=e.getSegmentByTime(this.$3.getCurrentTime());d&&a.clearRangeWithWait([{fromTime:this.$3.getCurrentTime(),toTime:this.$3.getDuration()}])["catch"](function(a){c("oz-player/shims/ozReportUnexpectedError")(a,"OzHandleUserRepresentationSwitch clearRangeWithWait catch")})}};return a}();g["default"]=a}),98);
__d("oz-player/manifests/IOzSegment",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){return((a==null?void 0:a.getSequenceNumber())||0)===((b==null?void 0:b.getSequenceNumber())||0)}function b(a,b){return((a==null?void 0:a.getSequenceNumber())||0)>=((b==null?void 0:b.getSequenceNumber())||0)}f.isSequenceNumberEqual=a;f.isSequenceNumberGreaterOrEqual=b}),66);
__d("oz-player/networks/OzReadableStreamDataReader",["oz-player/networks/OzTransformStream","oz-player/utils/ozConcatUint8Arrays"],(function(a,b,c,d,e,f,g){"use strict";a=function(a){babelHelpers.inheritsLoose(b,a);function b(b,c){var d;c===void 0&&(c=null);d=a.call(this)||this;d.$OzReadableStreamDataReader$p_1=[];d.$OzReadableStreamDataReader$p_3=Infinity;d.$OzReadableStreamDataReader$p_4=0;d.$OzReadableStreamDataReader$p_2=b;if(c!=null){b=c.getNumber("stream_reader_max_buffer_len");d.$OzReadableStreamDataReader$p_3=b>0?b:Infinity}return d}var d=b.prototype;d.onDataWritten=function(a){if(this.$OzReadableStreamDataReader$p_4+a.byteLength>=this.$OzReadableStreamDataReader$p_3){var b=c("oz-player/utils/ozConcatUint8Arrays")(this.$OzReadableStreamDataReader$p_1);b=b.buffer;this.$OzReadableStreamDataReader$p_2(b);this.$OzReadableStreamDataReader$p_1.length=0;this.$OzReadableStreamDataReader$p_4=0}this.$OzReadableStreamDataReader$p_1.push(a);this.$OzReadableStreamDataReader$p_4+=a.byteLength};d.onClose=function(){var a=this.$OzReadableStreamDataReader$p_1,b=c("oz-player/utils/ozConcatUint8Arrays")(a);b=b.buffer;a.length=0;this.$OzReadableStreamDataReader$p_4=0;this.$OzReadableStreamDataReader$p_2(b)};return b}(c("oz-player/networks/OzTransformStream"));g["default"]=a}),98);
__d("oz-player/networks/OzStreamingTask",["oz-player/loggings/OzLoggingUtils","oz-player/shims/OzDeferred","oz-player/shims/OzMaybeNativePromise","oz-player/shims/OzPerformance","oz-player/utils/OzErrorUtils","oz-player/utils/OzNetworkRequestLoggingUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b,d){this.$3=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise")),this.$5=!1,this.$9=null,this.$1=this.$2=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise")),this.$6=c("oz-player/shims/OzPerformance").now(),this.$7=b,this.$8=a,this.$9=d}var b=a.prototype;b.run=function(){var a=this.$3.getPromise(),b=this.$7;if(b){var e=c("oz-player/shims/OzPerformance").now()-this.$6;d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(a,b,"streaming_task",function(){},function(a){a.setTimeToRequestSent(Math.floor(e))})}this.$2.resolve();return a};b.getPromise=function(){return this.$3.getPromise()};b.cancel=function(){this.$3.getPromise()["catch"](function(){}),this.$3.reject(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_STREAMING_TASK",description:"task cancelled",extra:{code:"OZ_ST-1"}})),this.$5=!0};b.finish=function(){this.$3.resolve()};b.isCancelled=function(){return this.$5};b.addStreamDeferred=function(a,b){var d=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise")),e=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise"));this.$10({createStreamResult:a,changeStatusDeferred:d,createStreamDeferred:e,loggerProviderWithStreamContext:b});b=function(){return e.getPromise()};return{genStream:b,cancel:function(){if(a){var b=a.pausableStream;return b.pauseStream()}},getStatusChangePromise:function(){return d.getPromise()}}};b.$10=function(a){var b=this,e=a.createStreamResult,f=a.changeStatusDeferred,g=a.createStreamDeferred,h=a.loggerProviderWithStreamContext,i=a.loggingPayloads;a=this.$1.getPromise();var j=c("oz-player/shims/OzPerformance").now(),k=this.$1=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise"));k.getPromise()["catch"](function(){});a.then(function(){if(e){var a=e.pausableStream;e.loggingPayloads&&(i=e.loggingPayloads);g.resolve(a.getStream());b.$4=a;var f=c("oz-player/shims/OzPerformance").now(),k=b.$11(a.startStream());if(h){var l=function(c){var e=i;if(e){d("oz-player/utils/OzNetworkRequestLoggingUtils").setFetchStreamLoggingAttributes(c,e.getRequestUrl(),e.segments,f-j,a.getBytesStreamed(),e.getResponse());var g=b.$9;g&&g(c,e.getRequestUrl(),e.getResponse())}};d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(k,h,"fetch_stream",l,l)}return k}return null}).then(function(a){b.$4=null,k.resolve(),f.resolve(a||"cancelled")})["catch"](function(a){b.$4=null,b.$3.reject(a),k.reject(a),f.reject(a)})};b.$11=function(a){return a.then(function(a){a=a.statusPromise;return a.then(function(a){return a==="stream_done"?"done":"error"})})};return a}();g["default"]=a}),98);
__d("oz-player/scheduling/OzRoundRobinPriorityTaskQueue",["oz-player/shims/ozReportUnexpectedError"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){b===void 0&&(b=-1),this.$3=function(){},this.$4=[],this.$5=new Map(),this.$1=a,this.$2=b}var b=a.prototype;b.getHighestPriority=function(){return Math.max.apply(Math,this.$4.map(function(a){return a.queue.length?a.priority:-Infinity}))};b.enqueue=function(a,b){var d=this;b=b;b=this.$6(b);var e=this.$5.get(a);if(e===b)return;this.$5.has(a)&&this.remove(a);e=this.$7(b);var f=this.$4[e];f&&f.priority===b||(f={priority:b,queue:[]},this.$4.splice(e,0,f));this.$5.set(a,b);f.queue.push(a);a.getPromise().then(function(){d.remove(a)},function(){d.remove(a)})["catch"](function(a){c("oz-player/shims/ozReportUnexpectedError")(a,"OzRoundRobinPriorityTaskQueue remove after task run")});this.$3(a,b>=this.$1?"immediate":"normal")};b.updatePriority=function(a,b){if(!this.$5.has(a))return;this.enqueue(a,b)};b.dequeue=function(){for(var a=0;a<this.$4.length;a++){var b=this.$4[a];if(b.queue.length){b=b.queue.shift();this.$5["delete"](b);return b}}return null};b.remove=function(a){var b=this.$5.get(a);if(b!==void 0){b=this.$7(b);b=this.$4[b];if(b&&b.queue){var c=b.queue.findIndex(function(b){return b===a});c>-1&&b.queue.splice(c,1)}this.$5["delete"](a)}};b.setOnTaskUpdated=function(a){this.$3=a};b.clearOnTaskUpdated=function(){this.setOnTaskUpdated(function(){})};b.getLength=function(){return this.$5.size};b.test_isEmpty=function(){return this.$5.size===0&&this.$4.every(function(a){return a.queue.length===0})};b.$7=function(a){a=a;a=this.$6(a);var b;for(b=0;b<this.$4.length;b++)if(a>=this.$4[b].priority)return b;return b};b.$6=function(a){a=a;this.$2>=0&&(a=Number.parseFloat(a.toFixed(this.$2)));return a};return a}();g["default"]=a}),98);
__d("oz-player/scheduling/OzSequentialTaskScheduler",["oz-player/shims/OzMaybeNativePromise","oz-player/shims/ozReportUnexpectedError","oz-player/shims/ozSetTimeoutAcrossTransitions"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){this.$3=0;this.$1=a;a=b||{};b=a.taskTimeout;this.$4=b||0}var b=a.prototype;b.start=function(){this.$1.setOnTaskUpdated(this.$5.bind(this));var a=this.$1.dequeue();a&&this.$6(a,"immediate")};b.destroy=function(){this.$2&&this.$2.cancel(),this.$1.clearOnTaskUpdated()};b.$5=function(a,b){b=this.$6(a,b);b&&this.$1.remove(a)};b.$6=function(a,b){var d=this;if(this.$2&&b!=="immediate")return!1;this.$2&&this.$2.cancel();b=a.run()["catch"](function(a){});this.$4>0&&(b=c("oz-player/shims/OzMaybeNativePromise").race([b,new(c("oz-player/shims/OzMaybeNativePromise"))(function(a,b){c("oz-player/shims/ozSetTimeoutAcrossTransitions")(a,d.$4)})]));this.$3++;this.$2=a;b=b.then(function(){d.$3--;d.$2===a&&(d.$2=null);if(d.$3===0){var b=d.$1.dequeue();b&&d.$6(b,"immediate")}});b["catch"](function(a){c("oz-player/shims/ozReportUnexpectedError")(a,"OzSequentialTaskScheduler task complete")});return!0};return a}();g["default"]=a}),98);
__d("oz-player/networks/OzStreamingTaskQueueProvider",["oz-player/scheduling/OzRoundRobinPriorityTaskQueue","oz-player/scheduling/OzSequentialTaskScheduler"],(function(a,b,c,d,e,f,g){"use strict";var h=new Map(),i=new Map(),j=2,k=2,l=6e4;function a(a){var b=h.get(a);if(!b){b=new(c("oz-player/scheduling/OzRoundRobinPriorityTaskQueue"))(j,k);var d=new(c("oz-player/scheduling/OzSequentialTaskScheduler"))(b,{taskTimeout:l});h.set(a,b);i.set(a,d);d.start()}return b}g.OZ_QUEUE_MAX_PRIORITY=j;g.getQueue=a}),98);
__d("oz-player/networks/OzStreamingTaskStateManager",["Promise","oz-player/networks/OzStreamingTask","oz-player/networks/OzStreamingTaskQueueProvider","oz-player/networks/getOzBandwidthEstimatorPipeThroughReporter","oz-player/shims/OzSubscriptionsHandler"],(function(a,b,c,d,e,f,g){"use strict";var h;a=function(){function a(a){var b=this;this.$3=new(c("oz-player/shims/OzSubscriptionsHandler"))();this.$4=0;this.$9=!1;this.$10=null;var e=a.mimeType,f=a.priorityCalculator,g=a.loggerProvider,h=a.setCustomFetchStreamLoggingAttributes,i=a.config;a=a.bandwidthEstimator;this.$5=e;this.$1=f;this.$6=d("oz-player/networks/OzStreamingTaskQueueProvider").getQueue(this.$5);this.$7=g;this.$8=i;this.$11=a!=null?a:null;this.$10=h;this.$3.addSubscriptions(this.$1.addListener("state_changed",function(a,c){b.$12(a,c),b.$2&&b.$6.updatePriority(b.$2,b.$4)}))}var e=a.prototype;e.$13=function(){var a=[];a.push(c("oz-player/networks/getOzBandwidthEstimatorPipeThroughReporter")(this.$5,this.$11));return a};e.startStreamDeferred=function(a){var b=a.debugName,c=a.segments,d=a.networkManager,e=a.segmentsLoggerProvider,f=a.mediaStreamType,g=a.dataAppendedCallback;a=a.dataAppendedErrorCallback;var h=this.$14(e),i=this.$13();c.length>0?d=d.createPausableStream({debugName:b,segments:c,pipeThroughRangeStreamProviders:i,loggerProvider:e,mediaStreamType:f,dataAppendedCallback:g,dataAppendedErrorCallback:a}):d=null;this.$9=!0;b=h.addStreamDeferred(d,e);return{genStream:b.genStream,cancel:b.cancel,getStatusChangePromise:this.$15(b.getStatusChangePromise)}};e.$14=function(a){var b=this.$2;this.$12(this.$1.compute(),"start_stream");!b||b.isCancelled()?(b=this.$2=new(c("oz-player/networks/OzStreamingTask"))(this.$8,a,this.$10),this.$6.enqueue(b,this.$4)):this.$6.updatePriority(b,this.$4);return b};e.$15=function(a){var c=this;return function(){return a().then(function(a){c.$16();return a})["catch"](function(a){c.$16();return(h||(h=b("Promise"))).reject(a)})}};e.finishTaskIfNoOngoingStream=function(){this.$9||this.$17()};e.destroy=function(){this.$3.release();var a=this.$2;this.$2=null;a&&this.$6.remove(a)};e.$16=function(){this.$9=!1,this.$12(this.$1.compute(),"end_stream"),this.$4<d("oz-player/networks/OzStreamingTaskQueueProvider").OZ_QUEUE_MAX_PRIORITY&&this.$17()};e.$17=function(){this.$2&&(this.$2.finish(),this.$2=null)};e.$12=function(a,b){if(a===this.$4)return;this.$4=a;this.$7&&this.$7.getOperationLogger("priority_changed").setPriorityFloat(this.$4).setInitiator(b).log()};return a}();g["default"]=a}),98);
__d("oz-player/strategies/OzStreamLengthStrategy",[],(function(a,b,c,d,e,f){"use strict";function a(a,b,c,d,e){b=b;e=e;var f=a.getNumber("segments_to_stream_under_playhead_threshold");a=a.getNumber("low_segment_stream_playhead_threshold");f>0&&d.getCurrentTime()<a&&(b=f);d=[];d.push(e);for(a=1;a<b;a++){f=c.getSegmentAfter(e);if(!f)break;e=f;d.push(e)}return d}f.getStreamingSegments=a}),66);
__d("oz-player/streams/OzMediaStreamLoopDriver",["oz-player/shims/ozClearTimeout","oz-player/shims/ozSetTimeoutAcrossTransitions","oz-player/utils/OzErrorUtils"],(function(a,b,c,d,e,f,g){"use strict";a=function(){function a(a,b){var d=this;this.$2=null;this.$3=!1;this.$6=function(){var a;d.$7();if(d.$3)return;(a=d.$5)==null?void 0:a.start();a=d.$1.execute();var b=d.$1.getLoopInterval();b&&(d.$2=c("oz-player/shims/ozSetTimeoutAcrossTransitions")(function(){d.$6()},b));a&&a.then(d.$6)["catch"](function(b){d.$7();var a=d.$1.handleError(b);if(a)d.$2=c("oz-player/shims/ozSetTimeoutAcrossTransitions")(d.$6,a);else{(a=d.$5)==null?void 0:a.setError(b);(a=d.$5)==null?void 0:a.log();d.$5=null;d.$3=!0}})};this.$1=a;this.$4=b}var b=a.prototype;b.start=function(){if(this.$3)throw d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_NOT_IMPLEMENTED_ERROR",description:"restart is not implemented"});this.$5=this.$4.getOperationLogger("media_stream").start();this.$6()};b.stop=function(){var a;this.$7();this.$3=!0;(a=this.$5)==null?void 0:a.log();this.$5=null};b.$7=function(){this.$2&&(c("oz-player/shims/ozClearTimeout")(this.$2),this.$2=null)};return a}();g["default"]=a}),98);
__d("oz-player/utils/OzRangeUtils",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){b=b.filter(function(b){return b.startTime<=a&&b.endTime>=a});return b!=null&&b.length>0?b[0]:null}f.getRangeForTime=a}),66);
__d("oz-player/streams/OzSegmentLocator",["oz-player/utils/OzNumericalRangeUtil","oz-player/utils/OzRangeUtils"],(function(a,b,c,d,e,f,g){"use strict";function a(a){a=a.getEndingSegment();return a!=null&&a.getSequenceNumber()!=null?a.getSequenceNumber():0}var h=function(a,b){a=a.getCurrentTime();return b?d("oz-player/utils/OzRangeUtils").getRangeForTime(a,b.getBufferedRanges()):null},i=function(a,b,c){b=h(a,b);c=c?c.getTimeRange().endTime:0;a=a.getCurrentTime()||0;c?a=c:b&&(a=b.endTime);return a};b=function(a,b,c,e,f,g,h,j){h=a.getPredictedSegmentAfter(g);if(h&&b.isPlaying())return h;j=i(b,e,g);h=f.getTimeRanges();e=d("oz-player/utils/OzNumericalRangeUtil").findDiffCoveredByRanges(b.getCurrentTime(),j,h.map(function(a){return{rangeStart:a.startTime,rangeEnd:a.endTime}}));if(e>c)return null;g=a.getSegment(0);g&&g.getTimeRange().startTime>j&&(j=g.getTimeRange().startTime);return a.getSegmentByTime(j)};g.getEndingSequenceNumber=a;g.getTimeToQuery=i;g.getSegment=b}),98);
__d("oz-player/streams/OzSegmentUtils",["oz-player/manifests/OzSegmentOptions","oz-player/shims/OzURI"],(function(a,b,c,d,e,f,g){"use strict";function a(a){return a.reduce(function(a,b){var c;return((c=b==null?void 0:b.getSequenceNumber())!=null?c:0)>((c=a==null?void 0:a.getSequenceNumber())!=null?c:0)?b:a})}function b(a,b){var d=new(c("oz-player/manifests/OzSegmentOptions"))();return a.getPredictedSegmentAfter({getData:function(){return null},getByteRange:function(){return null},getSequenceNumber:function(){return b-1},getTimeRange:function(){return{endTime:0,startTime:0}},getURI:function(){return new(c("oz-player/shims/OzURI"))("")},getOptions:function(){return d}})}function d(a){a.getOptions().setSegmentNumDecisionTime(Date.now());return a}g.getMaxSegment=a;g.getSegmentForSequenceNumber=b;g.markSegmentCreateTime=d}),98);
__d("oz-player/utils/OzTaggedTimeRanges",[],(function(a,b,c,d,e,f){a=function(){function a(a){this.$1=[],this.$2=0,this.$3=0,this.$4=a?a:function(a,b){return a===b}}var b=a.prototype;b.add=function(a,b,c){if(b<a)return;if(b===a)return;if(this.$1.length===0){this.$1.push({startTime:a,endTime:b,tag:c});return}var d=0,e=0;for(var f=0;f<this.$1.length;f++){if(a===this.$1[f].startTime&&b===this.$1[f].endTime){d=f;e=f+1;break}a>=this.$1[f].startTime&&(d=f+1);b<=this.$1[f].endTime&&(e=f);if(b<this.$1[f].startTime)break}this.$1.splice(d,e-d,{startTime:a,endTime:b,tag:c});this.$5(d)};b.$5=function(a){a=a;var b=this.$1[a],c=null;a!==0&&(c=this.$1[a-1]);c!==null&&(this.$4(c.tag,b.tag)?c.endTime>=b.startTime&&(this.$1.splice(a-1,2,{startTime:c.startTime,endTime:Math.max(b.endTime,c.endTime),tag:b.tag}),a--):(c.endTime>b.startTime&&c.startTime===b.startTime?(this.$1.splice(a-1,1),a--):c.endTime>b.startTime&&this.$1.splice(a-1,1,{startTime:c.startTime,endTime:b.startTime,tag:c.tag}),c.endTime>b.endTime&&this.$1.splice(a+1,0,{startTime:b.endTime,endTime:c.endTime,tag:c.tag})));c=null;a!==this.$1.length-1&&(c=this.$1[a+1]);c&&(this.$4(c.tag,b.tag)?c.startTime<=b.endTime&&this.$1.splice(a,2,{startTime:b.startTime,endTime:Math.max(c.endTime,b.endTime),tag:b.tag}):c.startTime<=b.endTime&&(c.endTime<b.endTime?(this.$1.splice(a,1,{startTime:b.startTime,endTime:c.startTime,tag:b.tag}),this.$1.push({startTime:c.endTime,endTime:b.endTime,tag:b.tag})):this.$1.splice(a+1,1,{startTime:b.endTime,endTime:c.endTime,tag:c.tag})))};b.get=function(a){var b=a>=this.$3?this.$2:0,c=null;for(b=b;b<this.$1.length;b++)if(this.$1[b].startTime<=a&&a<this.$1[b].endTime){c=b;break}this.$2=c===null?0:c;this.$3=a;return c===null?null:this.$1[c].tag};b.getLength=function(){return this.$1.length};return a}();f["default"]=a}),66);
__d("oz-player/streams/OzMediaStream",["oz-player/manifests/IOzSegment","oz-player/networks/OzNetworkManager","oz-player/networks/OzReadableStreamDataReader","oz-player/networks/OzStreamingTaskStateManager","oz-player/networks/getOzBandwidthEstimatorPipeThroughReporter","oz-player/shims/OzDeferred","oz-player/shims/OzEventEmitter","oz-player/shims/OzMaybeNativePromise","oz-player/shims/OzNetworkDiagnostics","oz-player/shims/OzSubscriptionsHandler","oz-player/shims/ozClearTimeout","oz-player/shims/ozReportUnexpectedError","oz-player/shims/ozSetTimeoutAcrossTransitions","oz-player/shims/ozvariant","oz-player/strategies/OzStreamLengthStrategy","oz-player/streams/OzMediaStreamLoopDriver","oz-player/streams/OzSegmentLocator","oz-player/streams/OzSegmentUtils","oz-player/utils/OzBufferingUtils","oz-player/utils/OzCustomErrorCode","oz-player/utils/OzErrorEmitter","oz-player/utils/OzErrorUtils","oz-player/utils/OzMimeUtil","oz-player/utils/OzTaggedTimeRanges"],(function(a,b,c,d,e,f,g){"use strict";var h=1e3,i=.2,j={AUDIO:1,CAPTION:3,VIDEO:0};a=function(a){babelHelpers.inheritsLoose(b,a);function b(b){var e;e=a.call(this)||this;e.$OzMediaStream$p_2=0;e.$OzMediaStream$p_3=0;e.$OzMediaStream$p_4=null;e.$OzMediaStream$p_13=null;e.$OzMediaStream$p_15=!1;e.$OzMediaStream$p_16=null;e.$OzMediaStream$p_17=new(c("oz-player/shims/OzSubscriptionsHandler"))();e.$OzMediaStream$p_19=[];e.$OzMediaStream$p_22=0;e.$OzMediaStream$p_29=!0;e.$OzMediaStream$p_31=!1;e.$OzMediaStream$p_32=null;e.$OzMediaStream$p_33=new(c("oz-player/utils/OzErrorEmitter"))();e.$OzMediaStream$p_35=!0;e.$OzMediaStream$p_38=0;e.$OzMediaStream$p_39=null;e.$OzMediaStream$p_40=null;var f=b.config,g=b.sourceBufferManagerPromise,h=b.networkManager,i=b.playbackState,j=b.endOfStreamWatcher,k=b.bufferTargetCalculator,l=b.priorityCalculator,m=b.streamSegmentsCountCalculator,n=b.abrManager,o=b.representation,p=b.loggerProvider,q=b.segmentLocator,r=b.setCustomFetchStreamLoggingAttributes,s=b.blockedRepresentationsManager,t=b.dynamicVideoLibrary,u=b.shouldAppendOncePerStream,v=b.mediaStreamType,w=b.warningEmitter;b=b.mpd;e.$OzMediaStream$p_1=f;e.$OzMediaStream$p_5=g;e.$OzMediaStream$p_5.then(function(a){e.$OzMediaStream$p_6=a,e.$OzMediaStream$p_16&&e.$OzMediaStream$p_16.setSourceBuffer(a),e.$OzMediaStream$p_27.setSourceBufferState(a.getSourceBufferState()),e.$OzMediaStream$p_47()},function(){})["catch"](function(a){c("oz-player/shims/ozReportUnexpectedError")(a,"OzMediaStream SBM promise then - create")});e.$OzMediaStream$p_12=h;e.$OzMediaStream$p_7=i;e.$OzMediaStream$p_14=j;(f=e.$OzMediaStream$p_14)==null?void 0:f.registerMediaStream(babelHelpers.assertThisInitialized(e));e.$OzMediaStream$p_26=k;e.$OzMediaStream$p_27=l;e.$OzMediaStream$p_28=m;e.$OzMediaStream$p_16=n;e.$OzMediaStream$p_18=p.cloneContext();e.$OzMediaStream$p_36=q;e.$OzMediaStream$p_25=new(c("oz-player/utils/OzTaggedTimeRanges"))(function(a,b){return a.id===b.id});e.$OzMediaStream$p_39=r;e.$OzMediaStream$p_40=s;e.$OzMediaStream$p_42=t;e.$OzMediaStream$p_43=u;e.$OzMediaStream$p_44=v;e.$OzMediaStream$p_45=w;e.$OzMediaStream$p_46=b;e.$OzMediaStream$p_17.addSubscriptions(e.$OzMediaStream$p_7.addEventListener("seeking",function(){var a=i.getCurrentTime(),b=null;e.$OzMediaStream$p_1.getBool("ignore_reset_after_seek_if_bufferahead")&&e.$OzMediaStream$p_8.canPredict()&&(e.$OzMediaStream$p_6?d("oz-player/utils/OzBufferingUtils").getBufferAheadFromCurrentTime(a,e.$OzMediaStream$p_6.getSourceBufferState().getBufferedRanges())>0:!1)?b="no_reset_anchor":(b="reset_anchor",e.$OzMediaStream$p_41=Date.now(),e.$OzMediaStream$p_9!=null&&(e.$OzMediaStream$p_48(),e.$OzMediaStream$p_31=!1,e.$OzMediaStream$p_24&&e.$OzMediaStream$p_24.resolve()));e.$OzMediaStream$p_18.cloneContext().getOperationLogger("media_element_event").setInitiator("seeking").setLength(a*1e3).setReason(b).setUserInfo({streamAnchorSegmentNumber:String((a=e.$OzMediaStream$p_9)==null?void 0:a.getSequenceNumber())}).log()}));e.$OzMediaStream$p_40!=null&&e.$OzMediaStream$p_17.addSubscriptions(e.$OzMediaStream$p_40.onRepresentationBlocked(function(a){return e.$OzMediaStream$p_49(a)}));e.$OzMediaStream$p_21=d("oz-player/utils/OzMimeUtil").getMimeType(o.getMimeCodecs());e.$OzMediaStream$p_30=new(c("oz-player/networks/OzStreamingTaskStateManager"))({mimeType:e.$OzMediaStream$p_21,priorityCalculator:e.$OzMediaStream$p_27,loggerProvider:e.$OzMediaStream$p_18,setCustomFetchStreamLoggingAttributes:e.$OzMediaStream$p_39,config:e.$OzMediaStream$p_1,bandwidthEstimator:e.$OzMediaStream$p_12.getBandwidthEstimator()});e.$OzMediaStream$p_23=new(c("oz-player/streams/OzMediaStreamLoopDriver"))(babelHelpers.assertThisInitialized(e),e.$OzMediaStream$p_18);e.$OzMediaStream$p_50(o);return e}var e=b.prototype;e.start=function(){this.$OzMediaStream$p_23.start()};e.getCurrentRepresentation=function(){return this.$OzMediaStream$p_8};e.switchRepresentation=function(a,b){b===void 0&&(b=null),this.$OzMediaStream$p_50(a,"user",b)};e.onError=function(a){return this.$OzMediaStream$p_33.onError(a)};e.updateRunTimeConfigs=function(a){var b=a.streamDataHandler;a=a.resetStreamAnchor;b!==void 0&&(this.$OzMediaStream$p_34=b);a===!0&&this.$OzMediaStream$p_8.canApproximateId()&&(this.$OzMediaStream$p_48(),this.$OzMediaStream$p_10=null)};e.$OzMediaStream$p_50=function(a,b,e){var f=this;b===void 0&&(b="internal");e===void 0&&(e=null);if(a==null){if(this.$OzMediaStream$p_1.getBool("no_rep_to_switch_fallback_progressive")){var g=d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_NO_AVAILABLE_REP_TO_SWITCH",description:"No available representation to switch to because all are blocked"});this.$OzMediaStream$p_33.emitError(g)}return}b==="user"&&this.$OzMediaStream$p_48();(g=this.$OzMediaStream$p_20)==null?void 0:g.remove();this.$OzMediaStream$p_20=null;g=this.$OzMediaStream$p_18.cloneContext().setType(this.$OzMediaStream$p_21+";init");var h=g.cloneContext().setRepresentationID(a.getID()),i=a.getInitSegment(),j=i.getData(),k=g.getOperationLogger("stream_switch").setPreviousRepresentationID(this.$OzMediaStream$p_8?this.$OzMediaStream$p_8.getID():null).setInitiator(b).setRepresentationID(a.getID()).setReason(b==="internal"?this.$OzMediaStream$p_16&&this.$OzMediaStream$p_16.getLastEvaluationReason():b).start();this.$OzMediaStream$p_8=a;g=function(){var a;k.log();f.$OzMediaStream$p_26.handleEvent("append_succeeded");f.emit("initAppended");(a=f.$OzMediaStream$p_14)==null?void 0:a.notifyRepresentationSwitched(f)};var l=function(b){var c;k.setError(b).log();f.$OzMediaStream$p_51(b);(c=f.$OzMediaStream$p_40)==null?void 0:c.handleError(a.getID(),b);if(a.getID()!==f.$OzMediaStream$p_8.getID()){(c=f.$OzMediaStream$p_24)==null?void 0:c.resolve()}};if(j==null){i=this.$OzMediaStream$p_12.request({debugName:"OzMediaStream/init/"+this.$OzMediaStream$p_44,segments:[i],pipeThroughRangeStreamProviders:[c("oz-player/networks/getOzBandwidthEstimatorPipeThroughReporter")(this.$OzMediaStream$p_21,this.$OzMediaStream$p_12.getBandwidthEstimator())],loggerProvider:h,requestOptions:null,mediaStreamType:this.$OzMediaStream$p_44,dataAppendedCallback:g,dataAppendedErrorCallback:l});j=i.getStream()}this.emit("switchRepresentation",b,a);this.$OzMediaStream$p_52({data:j,mimeCodecs:a.getMimeCodecs(),clearSourceBufferRange:e,dataAppendedCallback:g,dataAppendedErrorCallback:l,loggerProvider:h.cloneContext(),appendTarget:0});if(e){i=a.getSegmentByTime(e[0]);if(i){b=this.$OzMediaStream$p_12.request({debugName:"OzMediaStream/startingSegment/"+this.$OzMediaStream$p_44,segments:[i],pipeThroughRangeStreamProviders:[c("oz-player/networks/getOzBandwidthEstimatorPipeThroughReporter")(this.$OzMediaStream$p_21,this.$OzMediaStream$p_12.getBandwidthEstimator())],loggerProvider:h,requestOptions:null,mediaStreamType:this.$OzMediaStream$p_44,dataAppendedCallback:g,dataAppendedErrorCallback:l});j=b.getStream();this.$OzMediaStream$p_52({data:j,mimeCodecs:a.getMimeCodecs(),clearSourceBufferRange:null,dataAppendedCallback:g,dataAppendedErrorCallback:l,loggerProvider:h.cloneContext(),appendTarget:1})}}};e.getLoopInterval=function(){return this.$OzMediaStream$p_22};e.execute=function(){var a=this.$OzMediaStream$p_53();if(a){var b=this.$OzMediaStream$p_24=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise"));a.then(function(){return b.resolve()})["catch"](function(a){return b.reject(a)});return b.getPromise()}return null};e.$OzMediaStream$p_54=function(a){if(this.$OzMediaStream$p_16&&this.$OzMediaStream$p_29&&this.$OzMediaStream$p_15){a=this.$OzMediaStream$p_16.getBestRepresentation(a);if(this.$OzMediaStream$p_8!==a)return a}return null};e.$OzMediaStream$p_55=function(a){var b=this.$OzMediaStream$p_6?this.$OzMediaStream$p_6.getSourceBufferState():null,c=this.$OzMediaStream$p_26.computeBufferTarget();if(c<=0)return null;var e=null;a&&(e=this.$OzMediaStream$p_8.getCustomFieldFirstSegment());if(!e&&this.$OzMediaStream$p_8.canPredict()&&this.$OzMediaStream$p_36!=null){a=this.$OzMediaStream$p_35&&this.$OzMediaStream$p_7.isPlaying();return this.$OzMediaStream$p_36.getSegment(this.$OzMediaStream$p_8,this.$OzMediaStream$p_7,c,b,this.$OzMediaStream$p_9,this.$OzMediaStream$p_10,this.$OzMediaStream$p_1,a,this.$OzMediaStream$p_21)}else{e||(e=d("oz-player/streams/OzSegmentLocator").getSegment(this.$OzMediaStream$p_8,this.$OzMediaStream$p_7,c,b,this.$OzMediaStream$p_8,this.$OzMediaStream$p_9,this.$OzMediaStream$p_1,this.$OzMediaStream$p_32));if(this.$OzMediaStream$p_8.canPredict()){a=this.$OzMediaStream$p_10&&this.$OzMediaStream$p_10.getSequenceNumber()||0;c=e&&e.getSequenceNumber()||0;if(e&&this.$OzMediaStream$p_10&&a>=c){c=d("oz-player/streams/OzSegmentLocator").getEndingSequenceNumber(this.$OzMediaStream$p_8);return(c||0)>=a?this.$OzMediaStream$p_10:null}}}if(!e&&!this.$OzMediaStream$p_56()){c=this.$OzMediaStream$p_8.getEndingSegment();if(c&&this.$OzMediaStream$p_8.isEndingSegment(c)){a=c.getTimeRange().endTime;b=d("oz-player/streams/OzSegmentLocator").getTimeToQuery(this.$OzMediaStream$p_7,b,this.$OzMediaStream$p_9);b>=a&&!this.$OzMediaStream$p_56()&&(e=c)}}return e};e.$OzMediaStream$p_57=function(){return!this.$OzMediaStream$p_15};e.$OzMediaStream$p_58=function(a,b){var e;e=(e=b.fetchSingleSegment)!=null?e:!1;b=(b=b.preventRepresentationSwitch)!=null?b:!1;var f=[];if(e)f=[a];else{e=this.$OzMediaStream$p_28.computeMin(this.$OzMediaStream$p_7,a,this.$OzMediaStream$p_8);f=d("oz-player/strategies/OzStreamLengthStrategy").getStreamingSegments(this.$OzMediaStream$p_1,e,this.$OzMediaStream$p_8,this.$OzMediaStream$p_7,a);f=c("oz-player/networks/OzNetworkManager").getStreamableSegmentsRange(f);if(!b){e=this.$OzMediaStream$p_54(f);if(e){this.$OzMediaStream$p_50(e);a=this.$OzMediaStream$p_55(!1);if(!a)return[];else return this.$OzMediaStream$p_58(a,{fetchSingleSegment:!1,preventRepresentationSwitch:!0})}}}return f};e.$OzMediaStream$p_59=function(){var a=this,b=function(b){b==null?void 0:b.remove(),a.$OzMediaStream$p_20===b&&(a.$OzMediaStream$p_20=null)};b(this.$OzMediaStream$p_20);var d=null;return new(c("oz-player/shims/OzMaybeNativePromise"))(function(c,e){d=a.getCurrentRepresentation().addUpdateListener(function(){b(d),c()}),a.$OzMediaStream$p_20=d})["catch"](function(a){b(d);throw a})};e.$OzMediaStream$p_53=function(){var a=this,b=this.$OzMediaStream$p_6?this.$OzMediaStream$p_6.getSourceBufferState():null;if(this.$OzMediaStream$p_8.canPredict()&&this.$OzMediaStream$p_10&&this.$OzMediaStream$p_8.isEndingSegment(this.$OzMediaStream$p_10)){this.endStream("pdash_ending_segment_had_error");this.$OzMediaStream$p_18.cloneContext().getOperationLogger("media_loop_end").setInitiator("end1").setRepresentationID(this.$OzMediaStream$p_8.getID()).log();return}if(this.$OzMediaStream$p_8.canPredict()&&this.$OzMediaStream$p_9&&this.$OzMediaStream$p_8.isEndingSegment(this.$OzMediaStream$p_9)){this.endStream("pdash_anchor_segment_is_ending_segment");this.$OzMediaStream$p_18.cloneContext().getOperationLogger("media_loop_end").setInitiator("end2").setRepresentationID(this.$OzMediaStream$p_8.getID()).log();return}var e=this.$OzMediaStream$p_57(),f=this.$OzMediaStream$p_55(e);if(this.$OzMediaStream$p_8.canPredict()&&this.$OzMediaStream$p_11!=null&&d("oz-player/manifests/IOzSegment").isSequenceNumberGreaterOrEqual(f,this.$OzMediaStream$p_11)){this.$OzMediaStream$p_18.cloneContext().getOperationLogger("media_loop_end").setInitiator("segment_end").setRepresentationID(this.$OzMediaStream$p_8.getID()).log();return}d("oz-player/manifests/IOzSegment").isSequenceNumberEqual(f,this.$OzMediaStream$p_37)?this.$OzMediaStream$p_38++:this.$OzMediaStream$p_38=0;this.$OzMediaStream$p_37=f;this.$OzMediaStream$p_7.isPlaying()&&this.$OzMediaStream$p_38>0&&this.$OzMediaStream$p_18.cloneContext().getOperationLogger("get_segment_to_stream_same_segment").setReason((f==null?void 0:f.getSequenceNumber())!=null?String(f==null?void 0:f.getSequenceNumber()):null).setLength(this.$OzMediaStream$p_38).setRepresentationID(this.$OzMediaStream$p_8.getID()).setSegmentStartTime(f==null?void 0:f.getTimeRange().startTime).setSegmentEndTime(f==null?void 0:f.getTimeRange().endTime).log();if(this.$OzMediaStream$p_31){var g;this.$OzMediaStream$p_22=h;this.$OzMediaStream$p_18.cloneContext().getOperationLogger("media_loop_end").setInitiator("ongoing_stream").setReason(((g=this.$OzMediaStream$p_9)==null?void 0:g.getSequenceNumber())!=null?String((g=this.$OzMediaStream$p_9)==null?void 0:g.getSequenceNumber()):null).setRepresentationID(this.$OzMediaStream$p_8.getID()).log();return null}if(!f||this.$OzMediaStream$p_31){if(this.$OzMediaStream$p_8.canPredict()&&this.$OzMediaStream$p_1.getBool("ms_promise_for_null")){var i=this.$OzMediaStream$p_1.getNumber("ms_promise_for_null_ms");return new(c("oz-player/shims/OzMaybeNativePromise"))(function(b,d){var e=c("oz-player/shims/ozSetTimeoutAcrossTransitions")(function(){c("oz-player/shims/ozClearTimeout")(e),b()},i?i:(a.$OzMediaStream$p_8.getMaxGopSec()||1)*1e3)})}this.$OzMediaStream$p_22=h;this.$OzMediaStream$p_31||this.$OzMediaStream$p_30.finishTaskIfNoOngoingStream();return this.$OzMediaStream$p_59()}this.$OzMediaStream$p_22=0;var j=this.$OzMediaStream$p_58(f,{fetchSingleSegment:e});if(j.length===0){this.$OzMediaStream$p_18.cloneContext().getOperationLogger("media_loop_end").setInitiator("no_stream_segments").setReason(((g=this.$OzMediaStream$p_9)==null?void 0:g.getSequenceNumber())!=null?String((f=this.$OzMediaStream$p_9)==null?void 0:f.getSequenceNumber()):null).setRepresentationID(this.$OzMediaStream$p_8.getID()).log();return this.$OzMediaStream$p_59()}this.$OzMediaStream$p_15=!0;var k=j[j.length-1]||null,l=this.$OzMediaStream$p_8,m=function(){return l.isEndingSegment(k)};j.length!==0||c("oz-player/shims/ozvariant")(0,23148);g=j[0];f=g.getTimeRange().startTime;var n=k.getTimeRange().endTime,o={startTime:f,endTime:n,tag:{id:this.$OzMediaStream$p_8.getID()}},p=function(b){v.setAppendedBufferMs(Math.round(b.appendedSec*1e3)),v.log(),a.$OzMediaStream$p_26.handleEvent("append_succeeded"),j.forEach(function(b){a.emit("SegmentAppended",a.$OzMediaStream$p_60(b,o))}),m()&&a.endStream("appended_ending_segment"),a.$OzMediaStream$p_61(o,b)},q=function(b){v.setError(b).log();var c=t||s;c&&c.cancel();a.$OzMediaStream$p_51(b)},r=this.$OzMediaStream$p_18.cloneContext();r.setRepresentationID(this.$OzMediaStream$p_8.getID());r.setSegmentStartTime(f).setSegmentEndTime(n);var s,t;!e?(t=this.$OzMediaStream$p_30.startStreamDeferred({debugName:"OzMediaStream/streamableSegments/"+this.$OzMediaStream$p_44,segments:j,networkManager:this.$OzMediaStream$p_12,sourceBufferState:b,playbackState:this.$OzMediaStream$p_7,segmentsLoggerProvider:r.cloneContext(),mediaStreamType:this.$OzMediaStream$p_44,dataAppendedCallback:p,dataAppendedErrorCallback:q}),this.$OzMediaStream$p_13=s):(s=this.$OzMediaStream$p_12.request({debugName:"OzMediaStream/streamableSegments/"+this.$OzMediaStream$p_44+"/prefetch",segments:j,pipeThroughRangeStreamProviders:[c("oz-player/networks/getOzBandwidthEstimatorPipeThroughReporter")(this.$OzMediaStream$p_21,this.$OzMediaStream$p_12.getBandwidthEstimator())],loggerProvider:r.cloneContext(),requestOptions:null,mediaStreamType:this.$OzMediaStream$p_44,dataAppendedCallback:p,dataAppendedErrorCallback:q}),this.$OzMediaStream$p_13=s);this.$OzMediaStream$p_31=!0;var u=this.$OzMediaStream$p_62(g,k,j.length),v=r.getOperationLogger("queued_append");v.start().setReason(this.$OzMediaStream$p_16&&this.$OzMediaStream$p_16.getLastEvaluationReason());var w=function(c,b){a.$OzMediaStream$p_52({data:c,mimeCodecs:b,clearSourceBufferRange:null,dataAppendedCallback:p,dataAppendedErrorCallback:q,loggerProvider:r.cloneContext(),appendTarget:u})},x;!!t||!!s||c("oz-player/shims/ozvariant")(0,23147);var y=this.$OzMediaStream$p_8.getMimeCodecs();t?(t.genStream().then(function(a){return w(a,y)})["catch"](function(b){return a.$OzMediaStream$p_33.emitError(b)}),x=t.getStatusChangePromise()):s&&(w(s.getStream(),y),x=s.getStatusChangePromise());!x&&c("oz-player/shims/ozvariant")(0,51928);f=x.then(function(b){a.$OzMediaStream$p_63(b,k);return a.$OzMediaStream$p_64()}).then(function(){a.$OzMediaStream$p_2=0});return f["catch"](function(b){var c,d=a.$OzMediaStream$p_9;a.$OzMediaStream$p_65(k);(c=a.$OzMediaStream$p_40)==null?void 0:c.handleError(l.getID(),b);return a.$OzMediaStream$p_66(b,k,d)})};e.handleError=function(a){var b=this.$OzMediaStream$p_18.getOperationLogger("media_stream_loop_error");b.setError(a).setResult("failed").log();return this.$OzMediaStream$p_1.getNumber("loop_body_handle_error_interval_ms")};e.$OzMediaStream$p_66=function(a,b,e){var f=this;this.$OzMediaStream$p_31=!1;var g=new(c("oz-player/shims/OzDeferred"))(c("oz-player/shims/OzMaybeNativePromise")),h=d("oz-player/utils/OzErrorUtils").getNormalizedErrorAndCode(a),i=h[0],j=h[1],k=d("oz-player/utils/OzErrorUtils").getOzErrorWithMIMEType(i,this.$OzMediaStream$p_21),l=function(b){f.$OzMediaStream$p_2++;f.$OzMediaStream$p_18.getOperationLogger("media_stream").setInitiator("fetch_stream_"+(b.behavior!=null?b.behavior:"default")).setLength(b.timeoutMs).setReason(""+k.getDescription()).setError(k).setCode(j.length>0?Number.parseInt(j,10):null).setType(f.$OzMediaStream$p_21).log();if(b.behavior==="retry_failed_request")f.$OzMediaStream$p_9=e;else if(b.behavior==="recover_failed_request"&&d("oz-player/utils/OzErrorUtils").isOzError(a)){var h;h=(h=a.getExtra())==null?void 0:h.headers;h=c("oz-player/shims/OzNetworkDiagnostics").getNextValidSegmentId(h);h!=null&&(f.$OzMediaStream$p_9=d("oz-player/streams/OzSegmentUtils").getSegmentForSequenceNumber(f.$OzMediaStream$p_8,h-1))}c("oz-player/shims/ozSetTimeoutAcrossTransitions")(function(){f.emit("streamErrorRetry",k);return g.resolve()},b.timeoutMs)};this.$OzMediaStream$p_4!==j?(this.$OzMediaStream$p_4=j,this.$OzMediaStream$p_3=1):this.$OzMediaStream$p_3++;h=!0;var m=function(){h=!1};i={consecutiveFailuresForErrorCode:this.$OzMediaStream$p_3,endStream:function(){m(),f.$OzMediaStream$p_10=b,f.$OzMediaStream$p_11=b,f.endStream("streamError_event_object"),g.resolve()},error:k,isInitialRequest:!1,retry:function(a){var b;m();l({timeoutMs:(b=a==null?void 0:a.waitMs)!=null?b:0,behavior:a==null?void 0:a.behavior})},retryAttemptCount:this.$OzMediaStream$p_2};this.emit("streamError",i);h&&(this.$OzMediaStream$p_33.emitError(k),this.$OzMediaStream$p_2=0,g.resolve());return g.getPromise()};e.$OzMediaStream$p_65=function(a){this.$OzMediaStream$p_48();this.$OzMediaStream$p_10=a;this.$OzMediaStream$p_30.finishTaskIfNoOngoingStream();a=this.$OzMediaStream$p_6;a&&a.cancelOperationAndCleanQueue(this.$OzMediaStream$p_18)};e.$OzMediaStream$p_63=function(a,b){a==="done"&&(this.$OzMediaStream$p_9=b)};e.$OzMediaStream$p_64=function(){this.$OzMediaStream$p_31=!1};e.$OzMediaStream$p_52=function(a){this.$OzMediaStream$p_19.push(a),this.$OzMediaStream$p_6&&this.$OzMediaStream$p_47()};e.$OzMediaStream$p_61=function(a,b){if(a==null)return;this.$OzMediaStream$p_8.canPredict()&&b.startTime_UNSAFE&&b.endTime_UNSAFE?this.$OzMediaStream$p_25.add(b.startTime_UNSAFE,b.endTime_UNSAFE,a.tag):this.$OzMediaStream$p_25.add(a.startTime,a.endTime,a.tag)};e.$OzMediaStream$p_47=function(){var a=this;this.$OzMediaStream$p_19.forEach(function(b){var d=b.data,e=b.mimeCodecs,f=b.clearSourceBufferRange,g=b.dataAppendedCallback,h=b.dataAppendedErrorCallback,i=b.loggerProvider;b=b.appendTarget;d=d instanceof Uint8Array||a.$OzMediaStream$p_34==null?d:d.pipeThrough(new(c("oz-player/networks/OzReadableStreamDataReader"))(a.$OzMediaStream$p_34.bind(a,a.$OzMediaStream$p_8),a.$OzMediaStream$p_1));a.$OzMediaStream$p_6&&a.$OzMediaStream$p_6.queueData(d,e,f,i,b).then(function(a){if(!a)return;g(a)})["catch"](function(a){h(a)})});this.$OzMediaStream$p_19=[]};e.$OzMediaStream$p_67=function(){var a=this.$OzMediaStream$p_6;if(!a)return c("oz-player/shims/OzMaybeNativePromise").resolve();var b=this.$OzMediaStream$p_7.getCurrentTime(),d=[{fromTime:0,toTime:b}],e=this.$OzMediaStream$p_1.getNumber("clear_buffer_around_playhead_boundary_ms");if(e>0){e=e/1e3;var f=a.getSourceBufferState();f=f.getBufferedRanges();f=f.length>=1?f[f.length-1].endTime:b;d=[{fromTime:0,toTime:Math.max(0,b-e)}];b+e<f&&d.push({fromTime:b+e,toTime:f})}return a.clearRangeWithWait(d)};e.$OzMediaStream$p_68=function(a){return!1};e.$OzMediaStream$p_51=function(a){var b=this;if(d("oz-player/utils/OzErrorUtils").isOzError(a)&&a.getType()==="OZ_SOURCE_BUFFER_QUOTA_EXCEEDED"){this.emit("streamErrorRetry",d("oz-player/utils/OzErrorUtils").getOzErrorWithMIMEType(a,this.$OzMediaStream$p_21));this.$OzMediaStream$p_26.handleEvent("append_quota_exceeded");var e=this.$OzMediaStream$p_67();e.then(function(){b.restartLoopBody()})["catch"](function(a){a=d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_STREAM_APPEND_QUOTA_EXCEEDED_HANDLER_ERROR",description:"Stream append quota exceeded: "+String(a),extra:{originalError:a,code:c("oz-player/utils/OzCustomErrorCode").STREAM_APPEND_QUOTA_EXCEEDED_HANDLER_ERROR}});b.$OzMediaStream$p_33.emitError(a)})}else if(d("oz-player/utils/OzErrorUtils").isOzError(a)&&a.getType()==="OZ_CANCELLED")return;else if(d("oz-player/utils/OzErrorUtils").isOzError(a)&&a.getType()==="OZ_SOURCE_BUFFER_CHANGE_TYPE")this.$OzMediaStream$p_33.emitError(a);else if(d("oz-player/utils/OzErrorUtils").isOzError(a)&&a.getType()==="OZ_SOURCE_BUFFER_CHANGE_TYPE_UNAVAILABLE")this.$OzMediaStream$p_33.emitError(a);else if(d("oz-player/utils/OzErrorUtils").isOzError(a)&&a.getType()==="OZ_WEBVTT_CAPTION_PARSE_ERROR"){c("oz-player/shims/ozReportUnexpectedError")(a,"OzMediaStream append error callback: OZ_WEBVTT_CAPTION_PARSE_ERROR branch","warn");(e=this.$OzMediaStream$p_45)==null?void 0:e.emitError(a)}else c("oz-player/shims/ozReportUnexpectedError")(a,"OzMediaStream append error callback","warn"),this.$OzMediaStream$p_68(a)&&this.$OzMediaStream$p_33.emitError(a)};e.$OzMediaStream$p_62=function(a,b,c){var d=0;if(this.$OzMediaStream$p_43)return Infinity;var e=this.$OzMediaStream$p_1.getNumber("appends_per_segment");if(e>0){a=a.getByteRange();b=b.getByteRange();a&&b&&b.endByte!=null&&(d=Math.ceil((b.endByte-a.startByte+1)/(c*e)))}d===0&&(d=this.$OzMediaStream$p_1.getNumber("append_byte_target_without_range"));return d};e.getIsAdaptationEnabled=function(){return this.$OzMediaStream$p_29};e.enableAdaptation=function(){this.$OzMediaStream$p_29=!0};e.disableAdaptation=function(){this.$OzMediaStream$p_29=!1};e.destroy=function(){this.$OzMediaStream$p_23.stop(),this.$OzMediaStream$p_5.then(function(a){a.destroy()},function(){})["catch"](function(a){c("oz-player/shims/ozReportUnexpectedError")(a,"OzMediaStream SBM promise then - destroy")}),this.$OzMediaStream$p_30.destroy(),this.$OzMediaStream$p_17.release(),this.$OzMediaStream$p_17.engage(),this.removeAllListeners(),this.$OzMediaStream$p_20&&(this.$OzMediaStream$p_20.remove(),this.$OzMediaStream$p_20=null)};e.getRepresentationIDAtTime=function(a){var b=this.$OzMediaStream$p_25.get(a);if(!b){var c=this.$OzMediaStream$p_8.getTimeRanges();c=c[c.length-1];var d=.001;c&&a>=c.endTime&&a<=this.$OzMediaStream$p_7.getDuration()+d&&(b=this.$OzMediaStream$p_25.get(c.endTime-d))}return b?b.id:null};e.endStream=function(a){var b;(b=this.$OzMediaStream$p_14)==null?void 0:b.notifyMediaStreamEnded(this,a);this.$OzMediaStream$p_69()};e.endStreamIfBufferedToEndTime=function(){var a=this.$OzMediaStream$p_8.getTimeRanges();if(a.length>0){a=a[a.length-1].endTime;if(this.$OzMediaStream$p_6){var b=this.$OzMediaStream$p_6.getSourceBufferState().getBufferedRanges();b.length>0&&(Math.abs(b[b.length-1].endTime-a)<i&&this.endStream("buffered_to_end_time"))}}};e.$OzMediaStream$p_60=function(a,b){return{type:this.$OzMediaStream$p_21.indexOf("audio")===0?j.AUDIO:this.$OzMediaStream$p_21.indexOf("video")===0?j.VIDEO:j.CAPTION,segment:a,sourceBuffer:(a=(a=this.$OzMediaStream$p_6)==null?void 0:(a=a.getDebug())==null?void 0:a.SourceBuffer)!=null?a:null,timeRange:b}};e.$OzMediaStream$p_48=function(){this.$OzMediaStream$p_9=null};e.restartLoopBody=function(){this.$OzMediaStream$p_48(),this.$OzMediaStream$p_31=!1,this.$OzMediaStream$p_13&&this.$OzMediaStream$p_13.cancel(),this.$OzMediaStream$p_22!==0&&this.$OzMediaStream$p_24&&this.$OzMediaStream$p_24.resolve()};e.$OzMediaStream$p_69=function(){if(this.$OzMediaStream$p_6){var a=this.$OzMediaStream$p_6.getSourceBufferState().getBufferedRanges();a.length>0&&(this.$OzMediaStream$p_32=a[a.length-1].endTime)}};e.$OzMediaStream$p_56=function(){return this.$OzMediaStream$p_32!==null};e.$OzMediaStream$p_49=function(a){this.$OzMediaStream$p_8.getID()===a&&this.$OzMediaStream$p_16!=null&&this.$OzMediaStream$p_50(this.$OzMediaStream$p_16.getBestRepresentation())};e.setEnableLiveheadCatchup=function(a){this.$OzMediaStream$p_35=a};e.clearSourceBufferRange=function(a,b){var d=this.$OzMediaStream$p_6;return d?d.clearRangeWithWait([{fromTime:a,toTime:b}]):c("oz-player/shims/OzMaybeNativePromise").resolve()};e.getDebug=function(){return{SourceBufferManager:this.$OzMediaStream$p_6,MediaStreamAbrManager:this.$OzMediaStream$p_16}};return b}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player/streams/OzPredictedSegmentLocator",["oz-player/streams/OzSegmentLocator","oz-player/streams/OzSegmentUtils","oz-player/utils/OzSourceBufferUtil"],(function(a,b,c,d,e,f,g){"use strict";function h(a,b){var c=0;for(var d in a){var e;if(d===b)continue;c=Math.max(c,(e=a[d].lastSkippedToSegmentNumber)!=null?e:0)}return c}function i(a,b){var c=null;for(var d in a){if(d===b)continue;a[d].inBufferAheadTimeSince!=null&&(c=c!=null?Math.min(c,a[d].inBufferAheadTimeSince):a[d].inBufferAheadTimeSince)}return c}a=function(){function a(a){this.$5={};var b=a.videoNode,c=a.liveLatencyManager,d=a.loggerProvider;a=a.dynamicVideoLibrary;this.$1=b;this.$2=c;this.$3=d;this.$4=a}var b=a.prototype;b.getSegment=function(a,b,c,d,e,f,g,h,i){var j;this.$5[i]==null&&(this.$5[i]={lastSkippedToSegmentNumber:null,inBufferAheadTimeSince:null});(j=this.$2)==null?void 0:j.setEnableCatchup(h);j=this.$6(a,b,c,d,e,f,g,h,i);(a=j.segment)==null?void 0:a.getOptions().setSegmentNumDecisionTime(Date.now());j.segment===null&&this.$3.getOperationLogger("segment_locator").setInitiator(j.initiator).setReason(j.reason).setType(i).setUserInfo(j.userInfo).log();b=this.$4;if(j.segment&&b!=null){c=j.segment.getOptions();c.setIsDVLEnabled(b.shouldRequestDynamicInfo(i))}return j.segment};b.$6=function(a,b,c,e,f,g,j,k,l){var m,n=b.getCurrentTime(),o=this.$2==null?!1:this.$2.shouldEnableCursorBasedCatchup(),p=e!=null?d("oz-player/utils/OzSourceBufferUtil").getTotalBufferAheadOfPosition(e.getBufferedRanges(),b.getCurrentTime(),j):{bufferAheadSec:0,bufferedOffset:0},q=p.bufferAheadSec;p=p.bufferedOffset;var r=null,s=null,t=this.$2?this.$2.enabled():!0,u=a.getPredictedSegmentAfter(f),v=d("oz-player/streams/OzSegmentUtils").getSegmentForSequenceNumber(a,j.getNumber("pdash_download_cursor_between_catchups_seg")>0?h(this.$5):h(this.$5,l)),w=!0,x=null;if(o&&k&&((m=u)==null?void 0:m.getSequenceNumber())!=null&&((m=(m=u)==null?void 0:m.getSequenceNumber())!=null?m:0)<=((m=v==null?void 0:v.getSequenceNumber())!=null?m:0)){u=v;r="paired_catchup";this.$3.getOperationLogger("segment_locator").setInitiator(r).setUserInfo({bufferAheadSec:String(q),bufferedOffset:String(p)}).setLength(((m=v==null?void 0:v.getSequenceNumber())!=null?m:0)-((m=(m=u)==null?void 0:m.getSequenceNumber())!=null?m:0)).setType(l).log();w=!1;x=r}else if(o&&k&&t&&u!=null&&((m=this.$2)==null?void 0:m.isBehindPreferredLiveHeadLatencyWithTolerance())){if(this.$2&&q>this.$2.getPreferredLiveHeadLatencySec()&&p==0)return{segment:null,initiator:"catchup_pause",userInfo:{bufferAheadSec:String(q),bufferedOffset:String(p)}};o=a.getEndingSegment();k=d("oz-player/streams/OzSegmentUtils").getMaxSegment([o,u]);m=j.getNumber("pdash_download_cursor_between_catchups_seg")===0||((m=v==null?void 0:v.getSequenceNumber())!=null?m:0)+j.getNumber("pdash_download_cursor_between_catchups_seg")<((v=o==null?void 0:o.getSequenceNumber())!=null?v:0);if(m){if(((o=k==null?void 0:k.getSequenceNumber())!=null?o:0)>((m=(v=u)==null?void 0:v.getSequenceNumber())!=null?m:0)){this.$5[l].lastSkippedToSegmentNumber=(o=k==null?void 0:k.getSequenceNumber())!=null?o:0;this.$3.getOperationLogger("segment_locator").setInitiator("catchup").setUserInfo({bufferAheadSec:String(q),bufferedOffset:String(p)}).setLength(((v=k==null?void 0:k.getSequenceNumber())!=null?v:0)-((o=(m=u)==null?void 0:m.getSequenceNumber())!=null?o:0)).setType(l).log()}u=k;r="catchup";w=!1;x=r}}else if(f==null){u=a.getSegmentByTime(n);r="1st_run";v=a.getTimeRanges();s={approx:String(a.canApproximateId()),ranges:String(v.length),mpdStartTime:v.length>0?String(v[v.length-1].startTime):null,mpdEndTime:v.length>0?String(v[v.length-1].endTime):null};this.$5[l].lastSkippedToSegmentNumber=(o=(m=u)==null?void 0:m.getSequenceNumber())!=null?o:0}w&&(w=t,x="stream_state");if(w&&q>c){this.$5[l].inBufferAheadTimeSince==null&&(this.$5[l].inBufferAheadTimeSince=Date.now());return{segment:null,initiator:"buffer_ahead_rule",userInfo:{bufferAheadSec:String(q),bufferedOffset:String(p),lastMaxSegNum:String(h(this.$5,l)),segment:u?String(u.getSequenceNumber()):null,computedBufferAheadTarget:String(c)}}}else!w&&q>c&&this.$3.getOperationLogger("segment_locator").setUserInfo({bufferAheadSec:String(q),bufferedOffset:String(p),computedBufferAheadTarget:String(c)}).setReason("disabled_buffer_ahead_rule:"+(x||"")).setType(l).log();k=g&&g.getSequenceNumber()||0;f=u&&u.getSequenceNumber()||0;if(u&&g&&k>=f){n=d("oz-player/streams/OzSegmentLocator").getEndingSequenceNumber(a);return(n||0)>=k?{segment:g}:{segment:null,initiator:"mpd_refresh",userInfo:{leSegNum:String(k)}}}v=e?d("oz-player/utils/OzSourceBufferUtil").getTotalInBufferedRanges(e.getBufferedRanges()):0;m=e?d("oz-player/utils/OzSourceBufferUtil").getTotalBufferAheadOfPosition(e.getBufferedRanges(),b.getCurrentTime(),j).bufferAheadSec:0;o=i(this.$5,l);t=Date.now();if(j.getNumber("download_cursor_total_buffer_max_sec")!=0&&j.getNumber("download_cursor_total_buffer_max_sec")<m||j.getNumber("download_cursor_buffer_ahead_time_max_sec")!=0&&o!=null&&j.getNumber("download_cursor_buffer_ahead_time_max_sec")<(t-o)/1e3){this.$5[l].lastSkippedToSegmentNumber=(x=(w=u)==null?void 0:w.getSequenceNumber())!=null?x:0;this.$3.getOperationLogger("segment_locator").setUserInfo({bufferAheadSec:String(q),bufferedOffset:String(p),totalBufferedSec:String(v),computedBufferAheadTarget:String(c),inBufferAheadElapsedTime:String((t-(o!=null?o:0))/1e3)}).setReason("force_paired_catchup").setType(l).log()}this.$5[l].inBufferAheadTimeSince=null;return{segment:u,initiator:r?r:void 0,userInfo:s?s:void 0}};return a}();g.OzPredictedSegmentLocator=a}),98);
__d("oz-player/utils/getRepresentationInCache",["oz-player/networks/RequestParamCreator"],(function(a,b,c,d,e,f,g){"use strict";var h=new(c("oz-player/networks/RequestParamCreator"))();function a(a,b){b===void 0&&(b=null);return a.find(function(a){a=a.getInitSegment();var c=a&&a.getByteRange();return a&&c&&b?b.hasCacheValue(h.createRequestParam(a.getURI(),c,"auto").uri.toString()):!1})||null}g["default"]=a}),98);
__d("oz-player/Player",["Promise","oz-player/configs/OzPlayerConfig","oz-player/drm/OzDrmManager","oz-player/loggings/OzDevConsolePerfLogger","oz-player/loggings/OzLoggingUtils","oz-player/loggings/OzMultiDestinationPerfLogger","oz-player/loggings/OzOperationLoggerObserver","oz-player/manifests/OzDynamicVideoLibrary","oz-player/manifests/OzVideoRepresentation","oz-player/media_source/MediaSourceManager","oz-player/media_source/OzMediaSeekableRangeManager","oz-player/networks/OzBandwidthEstimator","oz-player/networks/OzClientClock","oz-player/networks/OzNetworkManager","oz-player/parsers/OzMpdParser","oz-player/parsers/OzMpdUpdater","oz-player/parsers/OzXmlParserImplDOMParser","oz-player/playback_controls/OzClearSourceBufferOnSeekManager","oz-player/playback_controls/OzLiveLatencyManager","oz-player/playback_controls/OzPlaybackTimeRangeManager","oz-player/playback_controls/OzPlayheadManager","oz-player/shims/OzEventEmitter","oz-player/shims/OzMaybeNativePromise","oz-player/shims/OzPerformance","oz-player/shims/OzSubscriptionsHandler","oz-player/shims/OzUuid","oz-player/shims/ozClearTimeout","oz-player/shims/ozReportUnexpectedError","oz-player/shims/ozSetTimeoutAcrossTransitions","oz-player/shims/ozvariant","oz-player/states/OzObservedPlaybackState","oz-player/states/OzPositionToViewport","oz-player/strategies/OzAbrManager","oz-player/strategies/OzBandwidthBoundaryStreamSegmentsCountStrategy","oz-player/strategies/OzBlockedRepresentationsManager","oz-player/strategies/OzBufferAheadPriorityStrategy","oz-player/strategies/OzBufferAheadTargetStrategy","oz-player/strategies/OzBufferEndLimitStrategy","oz-player/strategies/OzBufferTargetBoundedStreamSegmentsCountStrategy","oz-player/strategies/OzBufferTargetCalculator","oz-player/strategies/OzBufferTargetCalculatorQuotaExceededConstraint","oz-player/strategies/OzBufferingDetector","oz-player/strategies/OzPausedStreamSegmentsCountStrategy","oz-player/strategies/OzPlayheadAtInterruptionDetector","oz-player/strategies/OzPlayingStatePriorityStrategy","oz-player/strategies/OzPositionToViewportPriorityStrategy","oz-player/strategies/OzPriorityCalculator","oz-player/strategies/OzSingleCodecRestriction","oz-player/strategies/OzStaleManifestBufferTargetStrategy","oz-player/strategies/OzStartupBufferTargetStrategy","oz-player/strategies/OzStaticStreamSegmentsStrategy","oz-player/strategies/OzStreamSegmentsCountCalculator","oz-player/strategies/OzSupportedMimeCodecsRestriction","oz-player/streams/OzEndOfStreamWatcher","oz-player/streams/OzHandleUserRepresentationSwitch","oz-player/streams/OzMediaStream","oz-player/streams/OzPredictedSegmentLocator","oz-player/utils/OzCustomErrorCode","oz-player/utils/OzErrorEmitter","oz-player/utils/OzErrorUtils","oz-player/utils/OzIbrUtils","oz-player/utils/OzMimeUtil","oz-player/utils/OzMpdUtils","oz-player/utils/OzPlaybackRestrictionsUtils","oz-player/utils/OzReadableStreamUtils","oz-player/utils/getRepresentationInCache"],(function(a,b,c,d,e,f,g){"use strict";var h;function i(a){a||c("oz-player/shims/ozvariant")(0,21876);return a}var j="av01",k="vp09",l="avc1";a=function(a){babelHelpers.inheritsLoose(e,a);function e(b){var e;e=a.call(this)||this;e.$Player$p_5=new Map();e.$Player$p_6=new Map();e.$Player$p_9=void 0;e.$Player$p_10=new(c("oz-player/shims/OzSubscriptionsHandler"))();e.$Player$p_11=new(c("oz-player/utils/OzErrorEmitter"))();e.$Player$p_12=new(c("oz-player/utils/OzErrorEmitter"))();e.$Player$p_19=void 0;e.$Player$p_25=[];e.$Player$p_26=void 0;e.$Player$p_27=void 0;e.$Player$p_32=null;e.$Player$p_33=null;e.$Player$p_43=null;e.$Player$p_51=!1;e.$Player$p_54=0;e.$Player$p_55=null;e.$Player$p_56=null;e.$Player$p_57=0;e.$Player$p_58=null;e.$Player$p_59=null;e.$Player$p_60=!1;e.$Player$p_63=null;e.$Player$p_64=null;e.$Player$p_70=!1;e.$Player$p_71=!1;e.$Player$p_76=!1;e.$Player$p_77=null;e.$Player$p_78=!1;e.$Player$p_89=function(){var a=new(c("oz-player/parsers/OzXmlParserImplDOMParser"))();return a};e.$Player$p_91=function(){e.$Player$p_5.forEach(function(a){return a.endStreamIfBufferedToEndTime()})};e.$Player$p_92=function(){e.$Player$p_5.forEach(function(a){return a.endStream("streamGone")})};var f=b.audioOnly;f=f===void 0?!1:f;var g=b.bufferingDetector,h=b.videoNode,i=b.config,j=b.configMap,k=b.rawMpdXml,l=b.mpdUrl;l=l===void 0?null:l;var m=b.loggerConfig;m=m===void 0?{perfLoggerProviders:[],observedOperationLoggers:[],isOzDevConsoleEnabled:!1}:m;var n=b.bandwidthEstimatorOverride;n=n===void 0?null:n;var o=b.customVTTBufferTargetStrategies;o=o===void 0?null:o;var p=b.shouldBlockStatusCode5xx;p=p===void 0?!1:p;var q=b.prefetchCache;q=q===void 0?null:q;var r=b.getOverrideOzRequestImplementation;r=r===void 0?null:r;var s=b.getUserSelectedPlaybackRate,t=b.networkRequestStreamHandlers,u=b.networkRequestStreamRetryHandler,v=b.networkRequestUrlRefreshHandler,w=b.getShouldIncludeCredentials;w=w===void 0?null:w;var x=b.getCustomRequestParametersForURI;x=x===void 0?null:x;var y=b.initialRepresentationIDs;y=y===void 0?[]:y;var z=b.debugCreateInitiator;z=z===void 0?null:z;var A=b.customParsers;A=A===void 0?new Map():A;var B=b.videoPlaybackRestrictions;B=B===void 0?[]:B;var C=b.customSegmentTimelineParser;C=C===void 0?null:C;var D=b.customRepresentationParsers;D=D===void 0?{}:D;var E=b.networkRequestFetchPriority;E=E===void 0?"auto":E;var F=b.videoAbrManagerFactory;F=F===void 0?null:F;var G=b.seekHandler;G=G===void 0?null:G;var H=b.initialPlaybackPositionForDynamicMpd;H=H===void 0?-20:H;var I=b.liveheadFallBehindBlockThreshold;I=I===void 0?30:I;var J=b.liveheadFallBehindBlockMargin;J=J===void 0?5.9:J;var K=b.startTimeStamp;K=K===void 0?0:K;var L=b.drmProviders;L=L===void 0?[]:L;var M=b.getVideoDimensions;M=M===void 0?null:M;var N=b.setCustomFetchStreamLoggingAttributes;N=N===void 0?null:N;var O=b.bufferEndLimit;O=O===void 0?null:O;var P=b.configureCustomRequestParametersForSegment;P=P===void 0?null:P;var Q=b.isClientTriggeredTraceEnabled;Q=Q===void 0?!1:Q;var R=b.enableImmediateQualityDownSwitch;R=R===void 0?!1:R;b=b.userPreferenceLang;b=b===void 0?null:b;e.$Player$p_1=o;e.$Player$p_49=z;o=m.perfLoggerProviders;z=m.observedOperationLoggers;m=m.isOzDevConsoleEnabled;e.$Player$p_17=m||!1;e.$Player$p_16=new(d("oz-player/loggings/OzMultiDestinationPerfLogger").OzMultiDestinationPerfLoggerProvider)(e.$Player$p_79().concat(o));e.$Player$p_2=f;e.$Player$p_4=i!=null?i:new(c("oz-player/configs/OzPlayerConfig"))(j!=null?j:{});e.$Player$p_78=e.$Player$p_4.getBool("enable_managed_media_source")&&!!window.ManagedMediaSource;e.$Player$p_75=e.$Player$p_78?"changeType"in window.ManagedSourceBuffer.prototype:"changeType"in window.SourceBuffer.prototype;e.$Player$p_12.onError(function(){});e.$Player$p_22=new(c("oz-player/loggings/OzOperationLoggerObserver"))();e.$Player$p_22.observe(z,e.$Player$p_16);e.$Player$p_16.getOperationLogger("player_initialization").setInitiator("player-"+c("oz-player/shims/OzUuid")()).log();e.$Player$p_53=r;e.$Player$p_28=A;e.$Player$p_67=new(c("oz-player/strategies/OzBlockedRepresentationsManager"))(e.$Player$p_4,p);e.$Player$p_10.addSubscriptions(e.$Player$p_67.onRepresentationBlocked(function(a){e.emit("representationBlocked",a)}));e.$Player$p_31=[e.$Player$p_67,new(c("oz-player/strategies/OzSupportedMimeCodecsRestriction"))(e.$Player$p_78)].concat(B);e.$Player$p_75||e.$Player$p_31.push(new(c("oz-player/strategies/OzSingleCodecRestriction"))());e.$Player$p_29=D;e.$Player$p_30=C;e.$Player$p_35=F;e.$Player$p_36=G;e.$Player$p_37=K;e.$Player$p_38=H;e.$Player$p_41=s!=null?s:function(){return 1};e.$Player$p_39=I;e.$Player$p_42=J;e.$Player$p_66=O;e.$Player$p_69=Q;e.$Player$p_71=R;e.$Player$p_47=L;e.$Player$p_63=N;e.$Player$p_3=h;e.$Player$p_7=new(c("oz-player/media_source/MediaSourceManager"))({videoNode:e.$Player$p_3,config:e.$Player$p_4,perfLoggerProvider:e.$Player$p_16,isChangeTypeSupported:e.$Player$p_75,useManagedMediaSource:e.$Player$p_78,handleVttCaptionsUpdated:function(a){var b;b=(b=e.$Player$p_5.get("caption"))==null?void 0:(b=b.getCurrentRepresentation())==null?void 0:b.getLang();e.emit("vttCaptionsUpdated",a,b)},onClearVideoNodeError:function(){e.$Player$p_80()},onRetryVideoElementError:function(a){e.emit("videoNodeErrorRetry",a)}});e.$Player$p_7.onError(function(a){e.$Player$p_11.emitError(a)});if(e.$Player$p_4.getBool("clear_on_seek")){m=new(c("oz-player/playback_controls/OzClearSourceBufferOnSeekManager"))(e.$Player$p_3,e.$Player$p_4,e.$Player$p_5,e.$Player$p_16);m.maybeOverwriteVideoCurrentTimeProperty();e.$Player$p_10.addSubscriptions(m.onError(e.$Player$p_11.emitError));e.$Player$p_64=m}e.$Player$p_24=q;o=e.$Player$p_24&&e.$Player$p_24.getCachedRepresentations();e.$Player$p_25=o||y;e.$Player$p_65=new(c("oz-player/manifests/OzDynamicVideoLibrary"))({config:e.$Player$p_4});e.$Player$p_44=t;e.$Player$p_45=w;e.$Player$p_46=x;e.$Player$p_68=P;e.$Player$p_77=n!=null?n:c("oz-player/networks/OzBandwidthEstimator");e.$Player$p_13=new(c("oz-player/networks/OzNetworkManager"))({config:e.$Player$p_4,prefetchCache:e.$Player$p_24,networkRequestStreamHandlers:e.$Player$p_44,getOverrideOzRequestImplementation:e.$Player$p_53,getShouldIncludeCredentials:e.$Player$p_45,getCustomRequestParametersForURI:e.$Player$p_46,networkRequestFetchPriority:E,networkRequestStreamRetryHandler:u,networkRequestUrlRefreshHandler:v,setCustomFetchStreamLoggingAttributes:e.$Player$p_63,dynamicVideoLibrary:e.$Player$p_65,configureCustomRequestParametersForSegment:e.$Player$p_68,onResourceTimingBufferFull:function(){e.$Player$p_81()},bandwidthEstimator:e.$Player$p_77});e.$Player$p_18=l;e.$Player$p_8=k;e.$Player$p_14=new(c("oz-player/states/OzObservedPlaybackState"))(e.$Player$p_4,e.$Player$p_3,e.$Player$p_16);e.$Player$p_52=M;e.$Player$p_15=new(c("oz-player/streams/OzEndOfStreamWatcher"))(e.$Player$p_7,e.$Player$p_4);e.$Player$p_10.addSubscriptions(e.$Player$p_15.addListener("streamEnd",function(){e.$Player$p_51=!0,e.emit("streamEnd")}));e.$Player$p_50=g!=null?g:e.$Player$p_82();e.$Player$p_4.getNumber("pixels_below_viewport_to_observe")>0&&(e.$Player$p_61=new(c("oz-player/states/OzPositionToViewport"))(e.$Player$p_3,e.$Player$p_4));e.$Player$p_72=b;e.$Player$p_73=null;e.$Player$p_74={lang:null,role:null};return e}var f=e.prototype;f.$Player$p_81=function(){if(this.$Player$p_70)return;c("oz-player/shims/ozReportUnexpectedError")(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_JAVASCRIPT_NATIVE",description:"ResourceTiming buffer is full"}),"ResourceTiming buffer is full","warn");this.$Player$p_70=!0};f.$Player$p_83=function(a){var b=this;this.$Player$p_84();var c=d("oz-player/loggings/OzLoggingUtils").executeOperationAndLog(this.$Player$p_16.cloneContext().setInitiator(this.$Player$p_49),"parse_manifest",function(e){var f,g,c=b.$Player$p_85(a);f=String((f=c.getCustomField("currentServerTimeMs"))!=null?f:"");g=String((g=c.getCustomField("lastVideoFrameTs"))!=null?g:"");f={currentServerTimeMs:f,ingestLastVideoFrameTs:g,nowMs:String(Date.now())};g=d("oz-player/utils/OzMpdUtils").getMpdLastTimeRange(c,b.$Player$p_4);g=g&&g.endTime!=0?g.endTime:null;e.setLiveheadPosition(g).setLiveheadSeqNumMpd(d("oz-player/utils/OzMpdUtils").getMpdLastSequenceNumber(c)).setManifestType(c.isStaticMpd()?"static":"dynamic").setIsMixedCodecManifest(b.$Player$p_76).setIsTemplatedManifest(Boolean(c.getCustomField("isLiveTemplated"))).setUserInfo(f);return c});this.$Player$p_86(c)};f.$Player$p_86=function(a){var b=this;this.$Player$p_9=i(a);this.$Player$p_4.getBool("player_emit_mpdparsed_early")&&this.emit("mpdParsed",a);this.$Player$p_26=this.$Player$p_87();this.$Player$p_27=this.$Player$p_88();this.$Player$p_67.setAvailableRepresentationsCount(a.getVideoRepresentations().length);if(this.$Player$p_18!=null){var e=new(c("oz-player/parsers/OzMpdUpdater"))(this.$Player$p_4,a,this.$Player$p_13,this.$Player$p_14,this.$Player$p_16,{mpdUrl:null,createXmlParser:this.$Player$p_89,customParsers:this.$Player$p_28,customRepresentationParsers:this.$Player$p_29,customSegmentTimelineParser:this.$Player$p_30},this.$Player$p_48,this.$Player$p_69,this.$Player$p_78);this.$Player$p_90(e);this.$Player$p_10.addSubscriptions(e.onError(this.$Player$p_11.emitError),e.addListener("manifestFetchError",function(a){return b.emit("manifestFetchError",a)}),e.addListener("manifestFetchErrorRetry",function(a){return b.emit("manifestFetchErrorRetry",a)}),e.addListener("streamTransitionToStatic",this.$Player$p_91),e.addListener("streamGone",this.$Player$p_92));e.setupUpdateLoop();this.$Player$p_21=e}this.$Player$p_4.getBool("use_live_latency_manager")?!a.isStaticMpd()?(this.$Player$p_40=new(c("oz-player/playback_controls/OzLiveLatencyManager"))({config:this.$Player$p_4,mpd:a,playbackState:this.$Player$p_14,bufferingDetector:this.$Player$p_50,video:this.$Player$p_3,loggerProvider:this.$Player$p_16,getUserSelectedPlaybackRate:this.$Player$p_41}),this.$Player$p_37=this.$Player$p_40.getPreferredLiveHeadLatencySec()*-1):this.$Player$p_37=this.$Player$p_37:this.$Player$p_37=a.isStaticMpd()?this.$Player$p_37:this.$Player$p_93(a);this.$Player$p_19=new(c("oz-player/playback_controls/OzPlayheadManager"))({video:this.$Player$p_3,mpd:a,dynamicVideoLibrary:this.$Player$p_65,playheadCatchup:this.$Player$p_34,playbackState:this.$Player$p_14,config:this.$Player$p_4,perfLoggerProvider:this.$Player$p_16,seekHandler:this.$Player$p_36,initialPlaybackPosition:this.$Player$p_37,canSupportSkipVideobufferGaps:(e=(e=this.$Player$p_64)==null?void 0:e.getCurrentTimePropertyOverwriteSuccess())!=null?e:!1,liveLatencyManager:this.$Player$p_40});this.$Player$p_20=new(c("oz-player/playback_controls/OzPlaybackTimeRangeManager"))({config:this.$Player$p_4,video:this.$Player$p_3,mpd:a,liveheadFallBehindBlockThreshold:this.$Player$p_39,liveheadFallBehindBlockMargin:this.$Player$p_42,playbackState:this.$Player$p_14,perfLoggerProvider:this.$Player$p_16});this.$Player$p_94()["catch"](function(a){var e=d("oz-player/utils/OzErrorUtils").isOzError(a)?a:null;b.$Player$p_11.emitError(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_INITIALIZATION",description:"Failed to setup streams: "+String(a),extra:babelHelpers["extends"]({},e==null?void 0:e.getExtra(),{originalError:a,code:c("oz-player/utils/OzCustomErrorCode").SETUP_STREAMS_PROMISE_REJECTION.toString()})}))});this.emit("mpdReady",a)};f.load=function(a){var b=this;this.$Player$p_18=a;try{var c=this.$Player$p_8;c!=null?this.$Player$p_83(c):a!=null?this.$Player$p_95(a).then(function(a){if(b.$Player$p_60){b.$Player$p_60=!1;b.load(b.$Player$p_18);return}b.$Player$p_8=a;b.$Player$p_54=0;b.$Player$p_83(a)})["catch"](function(c){if(b.$Player$p_60){b.$Player$p_60=!1;b.load(b.$Player$p_18);return}var e=d("oz-player/utils/OzErrorUtils").isOzError(c)?c:null;e=d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_INITIALIZATION",description:"Failed to setup player with fetched MPD: "+String(c),extra:babelHelpers["extends"]({},e==null?void 0:e.getExtra(),{originalError:c,url:a})});b.$Player$p_96(e,a)}):this.$Player$p_11.emitError(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_INITIALIZATION",description:"Cannot start Oz without a raw mpd xml or mpd url"}))}catch(a){c=d("oz-player/utils/OzErrorUtils").isOzError(a)?a:null;this.$Player$p_11.emitError(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_INITIALIZATION",description:"Failed to setup player: "+String(a),extra:babelHelpers["extends"]({},c==null?void 0:c.getExtra(),{originalError:a})}))}};f.$Player$p_93=function(a){var b;b=(b=a.getVideoRepresentations()[0].getMaxGopSec())!=null?b:0;a=(a=a.getVideoRepresentations()[0].getEndingSegment())==null?void 0:a.getTimeRange();a=a!=null?a.endTime-a.startTime:0;a>0&&(b=a);return Math.min(this.$Player$p_38+this.$Player$p_4.getNumber("dynamic_mpd_initial_playback_position_offset_modifier"),-1*b*this.$Player$p_4.getNumber("pdash_download_cursor_catchup_threshold_gop_multiplier"))};f.$Player$p_95=function(a){this.$Player$p_54++;a=this.$Player$p_13.requestRawUrl({debugName:"OzPlayer/fetchMpdXml/mpd",url:a,options:null,loggerProvider:this.$Player$p_16});a=a.getStream().getReader();return d("oz-player/utils/OzReadableStreamUtils").pumpString(a)};f.$Player$p_97=function(){this.$Player$p_55!=null&&(c("oz-player/shims/ozClearTimeout")(this.$Player$p_55),this.$Player$p_55=null)};f.$Player$p_96=function(a,b){var d=this,e=!0,f=this.$Player$p_54-1,g=a.getExtra().code;this.$Player$p_56!==g?(this.$Player$p_56=g,this.$Player$p_57=1):this.$Player$p_57++;var h=function(){e=!1};g={endStream:function(){h(),d.$Player$p_51=!0,d.emit("streamGoneBeforeStart")},consecutiveFailuresForErrorCode:this.$Player$p_57,error:a,isInitialRequest:!0,retry:function(a){h(),d.$Player$p_97(),d.$Player$p_55=c("oz-player/shims/ozSetTimeoutAcrossTransitions")(function(){d.$Player$p_97(),d.load(b)},(a==null?void 0:a.waitMs)!=null?a==null?void 0:a.waitMs:1e3)},retryAttemptCount:f};this.$Player$p_4.getNumber("initial_manifest_request_retry_count")>0&&this.emit("manifestFetchError",g);e&&this.$Player$p_11.emitError(a)};f.updatePlayerRunTimeConfig=function(a){var b=a.manifestUrl,c=a.audioStreamDataHandler,d=a.videoStreamDataHandler,e=a.applicationStreamDataHandler;a=a.resetStreamAnchor;if(d!==void 0){var f=this.$Player$p_5.get("video");f&&f.updateRunTimeConfigs({streamDataHandler:d})}if(a!==void 0){f=this.$Player$p_5.get("video");f&&f.updateRunTimeConfigs({resetStreamAnchor:a});d=this.$Player$p_5.get("audio");d&&d.updateRunTimeConfigs({resetStreamAnchor:a});(f=this.$Player$p_19)==null?void 0:f.resetCurrentTime()}if(c!==void 0){d=this.$Player$p_5.get("audio");d&&d.updateRunTimeConfigs({streamDataHandler:c})}if(e!==void 0){f=this.$Player$p_5.get("caption");f&&f.updateRunTimeConfigs({streamDataHandler:e,resetStreamAnchor:a})}b!=null&&b!==""&&this.$Player$p_98(b)};f.$Player$p_99=function(a,b){var c=this,e=a.getMimeCodecs();e=this.$Player$p_7.createSourceBufferManager(b+"/"+a.getMimeType()+"/"+a.getCodecs(),e,b,a.getInitSegment().getData());this.$Player$p_100(e,b,a);a=e.then(function(a){c.$Player$p_50&&b!=="caption"&&c.$Player$p_50.setSourceBufferState(b,a.getSourceBufferState());return a});d("oz-player/loggings/OzLoggingUtils").monitorPromiseAndLogOperation(a,this.$Player$p_16,"source_attach",function(a){a.setType(b)});return a.then(function(a){if(b!=="caption"){var d;(d=c.$Player$p_19)==null?void 0:d.observeSourceBufferState(a.getSourceBufferState(),b)}})};f.isDrm=function(){var a=this.$Player$p_48;if(a!=null){a=a.hasContentProtections();return a!=null?a:!1}return!1};f.$Player$p_84=function(){var a=this.$Player$p_47;if(a.length>0){var b=this.$Player$p_48=new(d("oz-player/drm/OzDrmManager").OzDrmManager)(this.$Player$p_16,this.$Player$p_3,{maxStartEMEAttempts:this.$Player$p_4.getNumber("max_start_eme_attempts"),throwNoLicenseError:!0});a.forEach(function(a){b.addProvider(a)});this.$Player$p_10.addSubscriptions(b.onError(this.$Player$p_11.emitError))}};f.$Player$p_101=function(a){var b=this,d=i(this.$Player$p_9);a=[this.$Player$p_4,a,this.$Player$p_102(),this.$Player$p_31,this.$Player$p_14,window.devicePixelRatio||1,function(){return b.$Player$p_103()},d.isStaticMpd()?"static":"dynamic",this.$Player$p_16];d=this.$Player$p_35&&this.$Player$p_35.apply(this,a);return d!=null?d:babelHelpers.construct(c("oz-player/strategies/OzAbrManager"),a)};f.$Player$p_104=function(a){return a==="caption"?!0:!1};f.$Player$p_100=function(a,b,e){var f=this,g=i(this.$Player$p_9),h=this.$Player$p_6.get(b),j=new(c("oz-player/streams/OzMediaStream"))({config:this.$Player$p_4,sourceBufferManagerPromise:a,networkManager:this.$Player$p_13,playbackState:this.$Player$p_14,endOfStreamWatcher:b!=="caption"?this.$Player$p_15:null,bufferTargetCalculator:this.$Player$p_105(b),priorityCalculator:this.$Player$p_106(),streamSegmentsCountCalculator:this.$Player$p_107(b),abrManager:h,representation:e,loggerProvider:this.$Player$p_16.cloneContext().setType(b),segmentLocator:this.$Player$p_62,setCustomFetchStreamLoggingAttributes:this.$Player$p_63,blockedRepresentationsManager:this.$Player$p_67,dynamicVideoLibrary:this.$Player$p_65,shouldAppendOncePerStream:this.$Player$p_104(b),mediaStreamType:b,warningEmitter:this.$Player$p_12,mpd:g});this.$Player$p_37===0?j.start():(i(this.$Player$p_9),this.$Player$p_19&&this.$Player$p_10.addSubscriptions(this.$Player$p_19.addListener("initialPlaybackPositionSet",function(){j.start()})));this.$Player$p_10.addSubscriptions(j.onError(this.$Player$p_11.emitError));this.$Player$p_23&&this.$Player$p_10.addSubscriptions(this.$Player$p_23.listenToMediaStreamInitAppended(j));b==="video"&&(this.$Player$p_33=new(c("oz-player/streams/OzHandleUserRepresentationSwitch"))(this.$Player$p_4,this.$Player$p_14,d("oz-player/utils/OzPlaybackRestrictionsUtils").applyVideoPlaybackRestrictions(this.$Player$p_31,this.$Player$p_103(),this.$Player$p_102()),j,a,this.$Player$p_71),this.$Player$p_10.addSubscriptions(this.$Player$p_33.onError(this.$Player$p_11.emitError)));this.$Player$p_5.set(b,j);this.$Player$p_10.addSubscriptions(j.addListener("switchRepresentation",function(){var c,a=j.getCurrentRepresentation();(c=f.$Player$p_23)==null?void 0:c.setTimeRangeProvider(a);(c=f.$Player$p_19)==null?void 0:c.setTimeRangeProvider(a);(c=f.$Player$p_20)==null?void 0:c.setTimeRangeProvider(a);b==="video"&&f.emit("switchVideoRepresentation")}),j.addListener("SegmentAppended",function(a){f.$Player$p_108("SegmentAppended",a)}),j.addListener("streamError",function(a){f.emit("streamError",a)}),j.addListener("streamErrorRetry",function(a){f.emit("streamErrorRetry",a)}))};f.$Player$p_109=function(){if(this.$Player$p_66!=null)return[new(c("oz-player/strategies/OzBufferEndLimitStrategy"))(this.$Player$p_3,this.$Player$p_66)];else return[new(c("oz-player/strategies/OzStaleManifestBufferTargetStrategy"))(i(this.$Player$p_9),this.$Player$p_4),new(c("oz-player/strategies/OzStartupBufferTargetStrategy"))(this.$Player$p_14,this.$Player$p_4),new(c("oz-player/strategies/OzBufferAheadTargetStrategy"))(this.$Player$p_4)]};f.$Player$p_110=function(){return this.$Player$p_66==null?[new(c("oz-player/strategies/OzBufferTargetCalculatorQuotaExceededConstraint"))({config:this.$Player$p_4,minimumBufferTargetSec:this.$Player$p_4.getNumber("buffer_target_constraint_minimum_sec")})]:[]};f.$Player$p_111=function(){return this.$Player$p_66==null?[new(c("oz-player/strategies/OzBufferTargetCalculatorQuotaExceededConstraint"))({config:this.$Player$p_4,minimumBufferTargetSec:0})]:[]};f.$Player$p_87=function(){return new(c("oz-player/strategies/OzBufferTargetCalculator"))(this.$Player$p_109(),this.$Player$p_110())};f.$Player$p_88=function(){return this.$Player$p_1!=null?new(c("oz-player/strategies/OzBufferTargetCalculator"))([].concat(this.$Player$p_1,this.$Player$p_109()),this.$Player$p_111()):this.$Player$p_87()};f.$Player$p_105=function(a){var b=a==="caption"?this.$Player$p_27:this.$Player$p_26;b||c("oz-player/shims/ozvariant")(0,76764,a);return b};f.$Player$p_90=function(a){var b=this;if(this.$Player$p_4.getNumber("stream_interrupt_check_mpd_stale_count_threshold")>0){var d=this.$Player$p_50,e=new(c("oz-player/strategies/OzPlayheadAtInterruptionDetector"))(this.$Player$p_3,d);this.$Player$p_10.addSubscriptions(e.addListener("playheadAtInterruption",function(a){b.$Player$p_16.getOperationLogger("stream_interrupted").log(),b.emit("streamInterruptAt",a)}),a.addListener("streamInterruptAt",function(a){var c;(c=b.$Player$p_50)==null?void 0:c.notifyStreamInterrupted();e.notifyStreamInterrupted(a)}),a.addListener("streamResumedAt",function(a){var c;b.$Player$p_16.getOperationLogger("stream_resumed").log();(c=b.$Player$p_50)==null?void 0:c.notifyStreamResumed();e.notifyStreamResumed();b.emit("streamResumedAt",a)}));this.$Player$p_32=e}};f.$Player$p_106=function(){var a=[new(c("oz-player/strategies/OzPlayingStatePriorityStrategy"))(this.$Player$p_14),new(c("oz-player/strategies/OzBufferAheadPriorityStrategy"))(this.$Player$p_4,this.$Player$p_14)].concat(this.$Player$p_4.getNumber("pixels_below_viewport_to_observe")>0&&this.$Player$p_61!=null?new(c("oz-player/strategies/OzPositionToViewportPriorityStrategy"))(this.$Player$p_61,this.$Player$p_4):[]);return new(c("oz-player/strategies/OzPriorityCalculator"))(a)};f.$Player$p_107=function(a){var b=[new(c("oz-player/strategies/OzStaticStreamSegmentsStrategy"))(this.$Player$p_4),new(c("oz-player/strategies/OzBufferTargetBoundedStreamSegmentsCountStrategy"))(this.$Player$p_105(a),this.$Player$p_4)];this.$Player$p_4.getNumber("paused_stream_segments_count")!==0&&b.push(new(c("oz-player/strategies/OzPausedStreamSegmentsCountStrategy"))(this.$Player$p_14,this.$Player$p_4));a==="video"&&b.push(new(c("oz-player/strategies/OzBandwidthBoundaryStreamSegmentsCountStrategy"))(this.$Player$p_4,this.$Player$p_102()));return new(c("oz-player/strategies/OzStreamSegmentsCountCalculator"))(b)};f.onError=function(a){return this.$Player$p_11.onError(a)};f.onWarning=function(a){return this.$Player$p_12.onError(a)};f.getSelectedVideoQuality=function(){var a=this.$Player$p_9;if(!a||this.$Player$p_2)return null;a=this.$Player$p_5.get("video");a=a&&a.getCurrentRepresentation();if(a instanceof c("oz-player/manifests/OzVideoRepresentation"))return a.getDisplayLabel();a=this.$Player$p_102();a=a[0];return a?a.getDisplayLabel():null};f.getTargetAudioTrack=function(){var a=this.$Player$p_9;if(!a)return null;a=this.$Player$p_5.get("audio");a=a&&a.getCurrentRepresentation();if(a){var b;return{id:a.getID(),displayLabel:a.getDisplayLabel(),lang:(b=a.getLang())!=null?b:null,role:a.getRole(),mimeCodecs:a.getMimeCodecs(),variantKey:a.getVariantKey()}}b=this.$Player$p_112();a=b[0];return a?a.getAudioTrack():null};f.getCurrentPlayingVideoVariant=function(){return this.$Player$p_73};f.getTargetVideoVariant=function(){return this.$Player$p_74};f.switchToVideoQuality=function(a){var b=this.$Player$p_102().find(function(b){return b.getDisplayLabel()===a});if(b){var c=this.$Player$p_5.get("video");c&&(c.switchRepresentation(b),c.disableAdaptation())}};f.switchToVideoVariant=function(a){this.$Player$p_74=a===null?{lang:null,role:null}:a;if(this.$Player$p_74.lang===((a=(a=this.$Player$p_73)==null?void 0:a.lang)!=null?a:null)&&this.$Player$p_74.role===((a=(a=this.$Player$p_73)==null?void 0:a.role)!=null?a:null)){c("oz-player/shims/ozReportUnexpectedError")(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SWITCH_VARIANT",description:"switchToVideoVariant switch to the same lang: "+((a=this.$Player$p_74.lang)!=null?a:"default lang")+", same role: "+((a=this.$Player$p_74.role)!=null?a:"default role")}),"switchToVideoVariant switch to the same lang: "+((a=this.$Player$p_74.lang)!=null?a:"default lang")+", same role: "+((a=this.$Player$p_74.role)!=null?a:"default role"),"warn");return}else if(!this.$Player$p_9){c("oz-player/shims/ozReportUnexpectedError")(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SWITCH_VARIANT",description:"switchToVideoVariant mpd is void"}),"switchToVideoVariant mpd is void","warn");return}else if(!this.$Player$p_113(this.$Player$p_74.lang)){c("oz-player/shims/ozReportUnexpectedError")(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SWITCH_VARIANT",description:"switchToVideoVariant lang not exist in mpd: "+((a=this.$Player$p_74.lang)!=null?a:"default lang")+", same role: "+((a=this.$Player$p_74.role)!=null?a:"default role")}),"switchToVideoVariant lang not exist in mpd: "+((a=this.$Player$p_74.lang)!=null?a:"default lang")+", same role: "+((a=this.$Player$p_74.role)!=null?a:"default role"),"warn");return}this.$Player$p_114();this.$Player$p_4.getBool("enable_lip_sync_abr_select_quality")&&this.$Player$p_4.getBool("enable_alternative_audio_tracks")&&this.$Player$p_75&&this.$Player$p_115();this.$Player$p_73=this.$Player$p_74};f.$Player$p_114=function(){var a=this.$Player$p_112()[0];if(a){var b=this.$Player$p_5.get("audio");b&&(b.switchRepresentation(a,[0,this.$Player$p_14.getDuration()]),b.disableAdaptation())}};f.$Player$p_115=function(){var a=this.$Player$p_5.get("video");if(!a)return;var b=a.getCurrentRepresentation();if(b.getLang()===this.$Player$p_74.lang&&b.getRole()===this.$Player$p_74.role)return;b=this.$Player$p_102();if(b.length===0){c("oz-player/shims/ozReportUnexpectedError")(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SWITCH_VARIANT",description:"switchVideoByTargetVariant no representations with target variant: "+JSON.stringify(this.$Player$p_74)}),"switchVideoByTargetVariant no representations with target variant: "+JSON.stringify(this.$Player$p_74),"warn");return}var e=this.$Player$p_6.get("video");e?(e.updateRepresentations(b),e=e.getBestRepresentation()):e=b[0];if(!e){c("oz-player/shims/ozReportUnexpectedError")(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_SWITCH_VARIANT",description:"switchVideoByTargetVariant unable to update representations given target variant: "+JSON.stringify(this.$Player$p_74)}),"switchVideoByTargetVariant unable to update representations given target variant: "+JSON.stringify(this.$Player$p_74));return}a.switchRepresentation(e,[0,this.$Player$p_14.getDuration()]);return};f.getIsVideoQualityAdaptationEnabled=function(){var a=this.$Player$p_5.get("video");return a?a.getIsAdaptationEnabled():!1};f.enableVideoQualityAdaptation=function(){var a=this.$Player$p_5.get("video");a&&a.enableAdaptation()};f.getVideoQualities=function(){var a=this,b=this.$Player$p_9;if(!b)return[];b=this.$Player$p_102().filter(function(b){var c;return!((c=a.$Player$p_67)==null?void 0:c.isBlocked(b.getID()))});var c=b.some(function(a){return d("oz-player/utils/OzMimeUtil").getParsedCodecFamily(a.getCodecs())===j}),e=b.some(function(a){return d("oz-player/utils/OzMimeUtil").getParsedCodecFamily(a.getCodecs())===k});c=c?b.filter(function(a){return d("oz-player/utils/OzMimeUtil").getParsedCodecFamily(a.getCodecs())===j}):e?b.filter(function(a){return d("oz-player/utils/OzMimeUtil").getParsedCodecFamily(a.getCodecs())===k}):b.filter(function(a){return d("oz-player/utils/OzMimeUtil").getParsedCodecFamily(a.getCodecs())===l});return c.sort(function(a,b){return a.getBandwidth()-b.getBandwidth()}).map(function(a){return a.getDisplayLabel()})};f.getVideoTracks=function(){var a=this,b=this.$Player$p_9;return!b?[]:this.$Player$p_102().filter(function(b){var c;return!((c=a.$Player$p_67)==null?void 0:c.isBlocked(b.getID()))}).map(function(a){return{mimeCodecs:a.getMimeCodecs(),codec:a.getCodecs(),codecFamily:d("oz-player/utils/OzMimeUtil").getParsedCodecFamily(a.getMimeCodecs()),displayLabel:a.getDisplayLabel(),qualityLabel:a.getDisplayLabel(),height:a.getHeight(),id:a.getID(),qualityScoreCurveString:a.getQualityScoreCurveString("csvqm"),width:a.getWidth(),lang:a.getLang(),role:a.getRole(),variantKey:a.getVariantKey()}})};f.getAudioTracks=function(){var a=this,b=this.$Player$p_9;return!b?[]:b.getAudioRepresentations().filter(function(b){var c;return!((c=a.$Player$p_67)==null?void 0:c.isBlocked(b.getID()))}).map(function(a){return a.getAudioTrack()})};f.setEnableLiveheadCatchup=function(a){if(this.$Player$p_20){var b;a?this.$Player$p_16.getOperationLogger("live_catchup_enabled").setIsLatencyCachupEnabled(!0).log():this.$Player$p_16.getOperationLogger("live_catchup_disabled").setIsLatencyCachupEnabled(!1).log();(b=this.$Player$p_20)==null?void 0:b.setTimelineBlockingEnabled(a)}this.$Player$p_5.forEach(function(b){b.setEnableLiveheadCatchup(a)});(b=this.$Player$p_19)==null?void 0:b.setEnableLiveheadCatchup(a)};f.$Player$p_94=function(){var a=this;if(this.$Player$p_4.getBool("enable_alternative_audio_tracks")&&this.$Player$p_113(this.$Player$p_72)){var c;this.$Player$p_74=babelHelpers["extends"]({},this.$Player$p_74,{lang:(c=this.$Player$p_72)!=null?c:null,role:this.$Player$p_72!=null?"dub":null})}c=this.$Player$p_102()[0];this.$Player$p_62=c&&c.canPredict()&&this.$Player$p_4.getBool("pdash_use_pdash_segmentlocator")||c&&c.canApproximateId()?new(d("oz-player/streams/OzPredictedSegmentLocator").OzPredictedSegmentLocator)({videoNode:this.$Player$p_3,liveLatencyManager:this.$Player$p_40,loggerProvider:this.$Player$p_16,dynamicVideoLibrary:this.$Player$p_65}):null;c=this.$Player$p_2?[this.$Player$p_116({liveConfigsFromAudioStream:!0}),this.$Player$p_117()]:[this.$Player$p_118(),this.$Player$p_116({liveConfigsFromAudioStream:!1}),this.$Player$p_117()];var e=[];return(h||(h=b("Promise"))).all(c).then(function(){var b=a.$Player$p_5.get("video"),c=a.$Player$p_5.get("audio"),d=a.$Player$p_5.get("caption");b&&e.push(b.getCurrentRepresentation());c&&e.push(c.getCurrentRepresentation());d&&e.push(d.getCurrentRepresentation());e.forEach(function(b){a.$Player$p_10.addSubscriptions(b.addUpdateListener(function(){a.$Player$p_108("SegmentIndexesUpdated",e)}))});a.$Player$p_108("InitialSegmentIndexes",e);a.$Player$p_3&&a.$Player$p_3.style&&(a.$Player$p_3.style.display=a.$Player$p_2?"none":"block");a.$Player$p_73=a.$Player$p_74;a.emit("initialized")})};f.$Player$p_119=function(a){if(!a.getTimeRanges().length){this.$Player$p_16.getOperationLogger("playhead_adjustment").setInitiator("setup_initial_duration").setResult("failed").log();return}a=a.getTimeRanges()[a.getTimeRanges().length-1].endTime;this.$Player$p_7.updateDuration(a,!0)};f.$Player$p_120=function(a){var b;this.$Player$p_23=new(c("oz-player/media_source/OzMediaSeekableRangeManager"))(this.$Player$p_7,this.$Player$p_4);this.$Player$p_119(a);(b=this.$Player$p_23)==null?void 0:b.setTimeRangeProvider(a);(b=this.$Player$p_19)==null?void 0:b.setTimeRangeProvider(a);(b=this.$Player$p_20)==null?void 0:b.setTimeRangeProvider(a)};f.$Player$p_118=function(){var a=this.$Player$p_102();if(!a.length)return c("oz-player/shims/OzMaybeNativePromise").reject(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_INITIALIZATION",description:"No video representations in the manifest",extra:{code:c("oz-player/utils/OzCustomErrorCode").SETUP_STREAMS_PROMISE_REJECTION.toString()}}));var b=d("oz-player/utils/OzPlaybackRestrictionsUtils").applyVideoPlaybackRestrictions(this.$Player$p_31,this.$Player$p_103(),a);if(!b.length){a=a.map(function(a){return a.getDisplayLabel()+"@"+a.getWidth()+"x"+a.getHeight()+"("+a.getMimeCodecs()+")"});return c("oz-player/shims/OzMaybeNativePromise").reject(d("oz-player/utils/OzErrorUtils").createOzError({type:"OZ_INITIALIZATION",description:"Restricted all video representations: "+a.join(", "),extra:{code:c("oz-player/utils/OzCustomErrorCode").SETUP_STREAMS_PROMISE_REJECTION.toString()}}))}this.$Player$p_120(b[0]);a=this.$Player$p_121(b);a&&this.$Player$p_4.getBool("enable_abr_logging")&&this.$Player$p_16.getOperationLogger("video_stream_setup_initial_representation_from_prefetch").setResult("success").setReason("player_initialized").setRepresentationID(a.getID()).setPreviousRepresentationID(null).setUserInfo({initialRepresentationIDs:JSON.stringify(this.$Player$p_25)}).setType("video").setInitiator("Player").log();var e=this.$Player$p_101(a||b[0]);this.$Player$p_6.set("video",e);a||(a=e.getBestRepresentation());return this.$Player$p_99(a||b[0],"video")};f.$Player$p_102=function(){var a=this.$Player$p_9;if(!a)return[];var b=a.getVideoRepresentations();if(this.$Player$p_4.getBool("enable_lip_sync_abr_select_quality")&&this.$Player$p_4.getBool("enable_alternative_audio_tracks")&&this.$Player$p_75){var c=a.getVideoRepresentationsByVariant(this.$Player$p_74);b=c.length>0?c:a.getVideoRepresentationsByVariant({lang:null,role:null})}var e=d("oz-player/utils/OzPlaybackRestrictionsUtils").applyVideoPlaybackRestrictions(this.$Player$p_31,this.$Player$p_103(),b);return b.filter(function(a){return e.includes(a)})};f.$Player$p_112=function(){var a=this.$Player$p_9;if(!a)return[];return this.$Player$p_4.getBool("enable_alternative_audio_tracks")?a.getAudioRepresentationsByVariant(this.$Player$p_74):a.getAudioRepresentations()};f.$Player$p_113=function(a){var b=this.$Player$p_9;if(!b)return!1;var c=b.getAudioRepresentations();if(a==null)return!0;if(this.$Player$p_4.getBool("enable_alternative_audio_tracks")&&b.isStaticMpd()){b=c.findIndex(function(b){return b.getLang()===a});return b>-1}return!1};f.$Player$p_116=function(a){var b=this;a=a.liveConfigsFromAudioStream;a=a===void 0?!1:a;var d=i(this.$Player$p_9),e=d.getAudioRepresentations();a&&this.$Player$p_120(e[0]);a=-1;this.$Player$p_4.getBool("enable_alternative_audio_tracks")&&e.length>1&&d.isStaticMpd()&&(this.$Player$p_72!=null&&(a=e.findIndex(function(a){return a.getLang()===b.$Player$p_72})));if(e.length){d=a>-1?a:e.findIndex(function(a){return a.getLang()==null&&a.getRole()==null});d=d>-1?d:0;return this.$Player$p_99(a>=0&&a<e.length?e[a]:this.$Player$p_122(e)||e[d],"audio")}return c("oz-player/shims/OzMaybeNativePromise").resolve()};f.$Player$p_117=function(){var a=i(this.$Player$p_9);a=a.getApplicationRepresentations();return a.length?this.$Player$p_99(this.$Player$p_123(a)||a[0],"caption"):c("oz-player/shims/OzMaybeNativePromise").resolve()};f.getMpd=function(){return this.$Player$p_9};f.destroy=function(a){var d,e,f=this;a=this.$Player$p_16.getOperationLogger("unload").setInitiator(a).start();(d=this.$Player$p_64)==null?void 0:d.destroy();this.$Player$p_97();this.$Player$p_5.forEach(function(a){a.destroy()});this.$Player$p_33&&(this.$Player$p_33.destroy(),this.$Player$p_33=null);this.$Player$p_5=new Map();this.$Player$p_10.release();this.$Player$p_10.engage();this.$Player$p_14.destroy();this.$Player$p_19&&this.$Player$p_19.destroy();this.$Player$p_40&&(this.$Player$p_40.destroy(),this.$Player$p_40=null);this.$Player$p_20&&(this.$Player$p_20.destroy(),this.$Player$p_20=null);this.$Player$p_48&&this.$Player$p_48.destroy();this.$Player$p_61&&this.$Player$p_61.destroy();this.$Player$p_13&&this.$Player$p_13.destroy();this.$Player$p_52&&(this.$Player$p_52=null);this.$Player$p_7&&this.$Player$p_7.detach();this.$Player$p_3.pause();this.$Player$p_4.getBool("enable_revoke_object_url_on_destroy")&&URL.revokeObjectURL(this.$Player$p_3.src);this.$Player$p_3.setAttribute("src","");this.$Player$p_3.removeAttribute("src");this.$Player$p_3.removeAttribute("srcObject");d=(h||(h=b("Promise"))).resolve();"function"===typeof this.$Player$p_3.setMediaKeys&&(d=this.$Player$p_3.setMediaKeys(null));this.$Player$p_21&&this.$Player$p_21.destroy();this.$Player$p_32&&this.$Player$p_32.destroy();this.$Player$p_22.destroy();(e=this.$Player$p_23)==null?void 0:e.destroy();this.$Player$p_50&&this.$Player$p_50.destroy();this.$Player$p_67&&this.$Player$p_67.destroy();a.log();this.emit("destroyed");return d["catch"](function(a){c("oz-player/shims/ozReportUnexpectedError")(a,"Player.destroy","warn")}).then(function(){f.$Player$p_3.load()})};f.$Player$p_121=function(a){var b=this,d=a.find(function(a){return b.$Player$p_25.indexOf(a.getID())!==-1});d||(d=c("oz-player/utils/getRepresentationInCache")(a,this.$Player$p_24));return d||null};f.$Player$p_122=function(a){var b=this,e,f=a.find(function(a){return b.$Player$p_25.indexOf(a.getID())!==-1});f||(f=c("oz-player/utils/getRepresentationInCache")(a,this.$Player$p_24));!f&&!((e=this.$Player$p_9)==null?void 0:e.isStaticMpd())&&a.length>1&&(f=d("oz-player/utils/OzIbrUtils").getInitialAudioRepresentation(a,this.$Player$p_4,this.$Player$p_16));return f||null};f.$Player$p_123=function(a){return null};f.$Player$p_85=function(a){var b=new(c("oz-player/parsers/OzMpdParser"))({config:this.$Player$p_4,ozParserContext:{mpdUrl:this.$Player$p_18,createXmlParser:this.$Player$p_89,customParsers:this.$Player$p_28,customRepresentationParsers:this.$Player$p_29,customSegmentTimelineParser:this.$Player$p_30},networkManager:this.$Player$p_13,perfLoggerProvider:this.$Player$p_16,drmManager:this.$Player$p_48,initialRepresentationIDs:this.$Player$p_25,blockedRepresentationsManager:this.$Player$p_67,enableAlternativeAudioTracks:this.$Player$p_4.getBool("enable_alternative_audio_tracks"),useManagedMediaSource:this.$Player$p_78});a=b.parse(a);this.$Player$p_10.addSubscriptions(b.onError(this.$Player$p_11.emitError));b=d("oz-player/utils/OzPlaybackRestrictionsUtils").applyVideoPlaybackRestrictions(this.$Player$p_31,this.$Player$p_103(),a.getVideoRepresentations());if(b.length>1){var e=d("oz-player/utils/OzMimeUtil").getParsedCodecFamily(b[0].getMimeCodecs());this.$Player$p_76=!b.reduce(function(a,b){return a&&d("oz-player/utils/OzMimeUtil").getParsedCodecFamily(b.getMimeCodecs())===e},!0)}return a};f.$Player$p_79=function(){var a=[];this.$Player$p_17&&a.push(new(d("oz-player/loggings/OzDevConsolePerfLogger").OzDevConsolePerfLoggerProvider)());return a};f.$Player$p_124=function(a,b){b=this.$Player$p_5.get(b);return b?b.getRepresentationIDAtTime(a):null};f.getAudioRepresentationIDAtTime=function(a){return this.$Player$p_124(a,"audio")};f.getVideoRepresentationIDAtTime=function(a){return this.$Player$p_124(a,"video")};f.getCurrentVideoRepresentation=function(){return this.$Player$p_125("video")};f.getCurrentAudioRepresentation=function(){return this.$Player$p_125("audio")};f.getCurrentPlayingAudioTrackID=function(){var a=this.getCurrentAudioRepresentation();return a==null?void 0:a.getID()};f.$Player$p_125=function(a){var b=this.$Player$p_124(this.$Player$p_14.getCurrentTime(),a),c=this.$Player$p_9;if(!c)return null;c=[];a==="audio"?c=this.$Player$p_112():a==="video"&&(c=this.$Player$p_102());return(a=c.find(function(a){return a.getID()===b}))!=null?a:null};f.getSelectedVideoRepresentation=function(){var a=this.$Player$p_5.get("video");return a!=null?a.getCurrentRepresentation():null};f.getSelectedAudioRepresentation=function(){var a=this.$Player$p_5.get("audio");return a!=null?a.getCurrentRepresentation():null};f.getDebug=function(){var a=this,b=this.$Player$p_5.get("video"),c=this.$Player$p_5.get("audio"),d=this.$Player$p_5.get("caption"),e,f,g,h,j,k,l;if(b){var m;e=(m=b.getDebug())==null?void 0:m.MediaStreamAbrManager;f=(m=b.getDebug())==null?void 0:m.SourceBufferManager;if(f){g=(m=f.getDebug())==null?void 0:m.SourceBuffer}}if(c){h=(m=c.getDebug())==null?void 0:m.SourceBufferManager;if(h){j=(m=h.getDebug())==null?void 0:m.SourceBuffer}}if(d){k=(m=d.getDebug())==null?void 0:m.SourceBufferManager;if(k){l=(m=k.getDebug())==null?void 0:m.SourceBuffer}}return{getManifest:function(){return i(a.$Player$p_9)},DashManifestParsed:this.$Player$p_9,VideoSourceStream:b,VideoAbrManager:e,VideoBufferManager:f,VideoSourceBuffer:g,AudioSourceStream:c,AudioBufferManager:h,AudioSourceBuffer:j,CaptionSourceStream:d,CaptionBufferManager:k,CaptionSourceBuffer:l}};f.injectExternalDebugEvent=function(a,b){this.$Player$p_108(a,b)};f.$Player$p_108=function(a,b){this.emit("debug/dashPlayerEvent",{detail:{type:a,event:b}})};f.$Player$p_82=function(){var a=this,b=new(c("oz-player/strategies/OzBufferingDetector"))(this.$Player$p_3,this.$Player$p_4.getLegacyConfig(),function(){return a.$Player$p_51?a.$Player$p_3.duration:Infinity});b.attachPerfLoggerProvider(this.$Player$p_16);this.$Player$p_10.addSubscriptions(b.addListener("enterBuffering",function(b){a.emit("enterBuffering",b)}),b.addListener("leaveBuffering",function(b){a.emit("leaveBuffering",b)}));return b};f.$Player$p_103=function(){var a=this.$Player$p_52?this.$Player$p_52():{width:this.$Player$p_3.offsetWidth,height:this.$Player$p_3.offsetHeight};return a};f.getMpdUrl=function(){return this.$Player$p_18};f.$Player$p_98=function(a){var b=this;this.$Player$p_18=a;var c=this.$Player$p_9;if(!c){this.$Player$p_60=!0;return}else{c.updateLocation(a);this.$Player$p_43&&(this.$Player$p_10.releaseOne(this.$Player$p_43),this.$Player$p_43=null);a=function(){b.$Player$p_43&&(b.$Player$p_10.releaseOne(b.$Player$p_43),b.$Player$p_43=null),b.$Player$p_5.forEach(function(a,d){var e=a.getCurrentRepresentation(),f=e.getDisplayLabel();e=(d==="video"?b.$Player$p_102():c.getAudioRepresentations()).slice();if(d==="video"){d=b.$Player$p_6.get(d);d&&d.updateRepresentations(b.$Player$p_102())}d=e[0];e.forEach(function(a){var b=a.getDisplayLabel();b===f&&(d=a)});a.switchRepresentation(d);a.restartLoopBody()})};this.$Player$p_43=c.addListener("updated",a);this.$Player$p_10.addSubscriptions(this.$Player$p_43)}};f.getPerfLoggerProvider=function(){return this.$Player$p_16};f.getApproximateFBLSToPlayerDisplayLatency=function(){var a,b;a=Number((a=(a=this.$Player$p_9)==null?void 0:a.getCustomField("lastVideoFrameTs"))!=null?a:void 0);b=Number((b=(b=this.$Player$p_9)==null?void 0:b.getCustomField("currentServerTimeMs"))!=null?b:void 0);var c=this.$Player$p_126(a,b);a=this.$Player$p_127(a,b);return{latencyFromClockSync:a,latencyFromMpdUpdate:c}};f.$Player$p_126=function(a,b){if(!this.$Player$p_14.getCurrentTime()||isNaN(a)||isNaN(b))return null;b=0;this.$Player$p_58===a?b=(c("oz-player/shims/OzPerformance").now()-Number(this.$Player$p_59))/1e3:(this.$Player$p_59=c("oz-player/shims/OzPerformance").now(),this.$Player$p_58=a);a=a/1e3+b-this.$Player$p_14.getCurrentTime();return a};f.$Player$p_127=function(a,b){var d;if(!this.$Player$p_14.getCurrentTime()||isNaN(a)||isNaN(b)||c("oz-player/networks/OzClientClock").getClientClockOffsetMs()==null||!c("oz-player/networks/OzClientClock").getClientClockOffsetMs())return null;d=this.$Player$p_14.getCurrentTimeUpdateTime()+((d=c("oz-player/networks/OzClientClock").getClientClockOffsetMs())!=null?d:0);b=b;var e=this.$Player$p_14.getCurrentTime()*1e3;a=a;d=(d-b-(e-a))/1e3;return d};f.$Player$p_80=function(){this.$Player$p_5.forEach(function(a){a.restartLoopBody();var b=a.getCurrentRepresentation();a.switchRepresentation(b)})};return e}(c("oz-player/shims/OzEventEmitter"));g["default"]=a}),98);
__d("oz-player",["oz-player/Player"],(function(a,b,c,d,e,f,g){"use strict";g["default"]=c("oz-player/Player")}),98);