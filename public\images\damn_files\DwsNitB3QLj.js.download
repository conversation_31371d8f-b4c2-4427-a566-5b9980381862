;/*FB_PKG_DELIM*/

__d("CometEmojiWithContextualSize.react",["CometTextTypography","FDSTextContext","cr:244","react","react-compiler-runtime"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react"),j=new Map([[16,16],[20,16],[24,20],[28,24],[32,30],[38,32]]);function k(){var a=d("react-compiler-runtime").c(2),b=d("FDSTextContext").useFDSTextContext();b=(b=b==null?void 0:b.type)!=null?b:"body4";var e=16;b!=null&&(b in c("CometTextTypography")&&(e=c("CometTextTypography")[b].lineHeight));if(a[0]!==e){b=(b=j.get(e))!=null?b:16;a[0]=e;a[1]=b}else b=a[1];return b}function a(a){var c=d("react-compiler-runtime").c(3),e=k();e=a.size!=null?a.size:e;var f;c[0]!==e||c[1]!==a?(f=a.renderCustomEmoji?a.renderCustomEmoji(e):i.jsx(b("cr:244"),babelHelpers["extends"]({},a,{size:e})),c[0]=e,c[1]=a,c[2]=f):f=c[2];return f}g["default"]=a}),98);
__d("CometEmojiTransform",["CometEmojiWithContextualSize.react","EmojiRenderer","baseTextTransformAllStrings","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a,b){a=a===void 0?{}:a;var e=a.size;return function(a){var f=0;return c("baseTextTransformAllStrings")(a,function(a,b){return d("EmojiRenderer").render(a,function(a){return i.jsx(c("CometEmojiWithContextualSize.react"),{emoji:a,size:e},b+"-"+f++)})},b)}}g["default"]=a}),98);
__d("EmoticonsList",[],(function(a,b,c,d,e,f){e.exports={emotes:{"O:)":"angel","O:-)":"angel","0:)":"angel","0:-)":"angel","'-_-":"coldsweat",":3":"colonthree","o.O":"confused",O_O:"confused",o_o:"confused","0_0":"confused","O.o":"confused_rev",":'(":"cry",":'-(":"cry","3:)":"devil","3:-)":"devil",":dog:":"dog","-3-":"flushkiss",":(":"frown",":-(":"frown",":[":"frown","=(":"frown",")=":"frown",":o":"gasp",":-O":"gasp",":O":"gasp",":-o":"gasp","=D":"grin",":-D":"grin",":D":"grin",">:(":"grumpy",">:-(":"grumpy","<3":"heart","&lt;3":"heart","^_^":"kiki","^~^":"kiki",":*":"kiss",":-*":"kiss","(y)":"like",":like:":"like","(Y)":"like",T_T:"loudly_crying","T-T":"loudly_crying",ToT:"loudly_crying","T.T":"loudly_crying",":-|":"neutral",":|":"neutral",":v":"pacman",":V":"pacman",'<(")':"penguin",">_<":"persevere",">.<":"persevere",":poop:":"poop",":|]":"robot","(^^^)":"shark",":)":"smile",":-)":"smile",":]":"smile","(:":"smile","=)":"smilingface","(=":"smilingface","-_-":"squint","B|":"sunglasses","8-|":"sunglasses","8|":"sunglasses","B-|":"sunglasses","B-)":"sunglasses","8-)":"sunglasses","8)":"sunglasses","(n)":"thumbsdown","(N)":"thumbsdown",":+1:":"thumbsup",":thumbsup:":"thumbsup",":P":"tongue",":-P":"tongue",":-p":"tongue",":p":"tongue","=P":"tongue",":trans:":"transflag",":/":"unsure",":-/":"unsure",":\\":"unsure",":-\\":"unsure","=/":"unsure","=\\":"unsure",">:o":"upset",">:O":"upset",">:-O":"upset",">:-o":"upset",";)":"wink",";-)":"wink",";*":"winkkiss",";-*":"winkkiss",";-P":"winktongue",";P":"winktongue",";-p":"winktongue",";p":"winktongue",":cheese:":"cheesewedge",":eyes:":"eyes",":peek:":"eyes",":clown:":"clown"},symbols:{angel:"O:)",coldsweat:"'-_-",colonthree:":3",confused:"o.O",confused_rev:"O.o",cry:":'(",devil:"3:)",dog:":dog:",flushkiss:"-3-",frown:":(",gasp:":o",grin:"=D",grumpy:">:(",heart:"<3",kiki:"^_^",kiss:":*",like:"(y)",loudly_crying:"T_T",neutral:":-|",pacman:":v",penguin:'<(")',persevere:">_<",poop:":poop:",robot:":|]",shark:"(^^^)",smile:":)",smilingface:"=)",squint:"-_-",sunglasses:"B|",thumbsdown:"(n)",thumbsup:":+1:",tongue:":P",transflag:":trans:",unsure:":/",upset:">:o",wink:";)",winkkiss:";*",winktongue:";-P",cheesewedge:":cheese:",eyes:":eyes:",clown:":clown:"},emoji:{angel:"1f607",coldsweat:"1f613",colonthree:"FACE_WITH_COLON_THREE",confused:"1f633",confused_rev:"1f633",cry:"1f622",devil:"1f608",dog:"1f436",flushkiss:"1f61a",frown:"1f641",gasp:"1f62e",grin:"1f603",grumpy:"1f620",heart:"2764",kiki:"1f60a",kiss:"1f618",like:"LIKE",loudly_crying:"1f62d",neutral:"1f610",pacman:"PACMAN",penguin:"1f427",persevere:"1f623",poop:"1f4a9",robot:"1f916",shark:"1f988",smile:"1f642",smilingface:"1f60a",squint:"1f611",sunglasses:"1f60e",thumbsdown:"1f44e",thumbsup:"1f44d",tongue:"1f61b",transflag:"1f3f3_fe0f_200d_26a7_fe0f",unsure:"1f615",upset:"1f620",wink:"1f609",winkkiss:"1f618",winktongue:"1f61c",cheesewedge:"1f9c0",eyes:"1f440",clown:"1f921"},regexp:/(^|[\s\'\".\(])(O:\)(?!\))|O:\-\)(?!\))|0:\)(?!\))|0:\-\)(?!\))|\'\-_\-|:3|o\.O|O_O|o_o|0_0|O\.o|:\'\(|:\'\-\(|3:\)(?!\))|3:\-\)(?!\))|:dog:|\-3\-|:\(|:\-\(|:\[|=\(|\)=|:o|:\-O|:O|:\-o|=D|:\-D|:D|>:\(|>:\-\(|<3|&lt;3|\^_\^|\^~\^|:\*|:\-\*|\(y\)(?!\))|:like:|\(Y\)(?!\))|T_T|T\-T|ToT|T\.T|:\-\||:\||:v|:V|<\(\"\)(?!\))|>_<|>\.<|:poop:|:\|\]|\(\^\^\^\)(?!\))|:\)(?!\))|:\-\)(?!\))|:\]|\(:|=\)(?!\))|\(=|\-_\-|B\||8\-\||8\||B\-\||B\-\)(?!\))|8\-\)(?!\))|8\)(?!\))|\(n\)(?!\))|\(N\)(?!\))|:\+1:|:thumbsup:|:P|:\-P|:\-p|:p|=P|:trans:|:\/|:\-\/|:\\|:\-\\|=\/|=\\|>:o|>:O|>:\-O|>:\-o|;\)(?!\))|;\-\)(?!\))|;\*|;\-\*|;\-P|;P|;\-p|;p|:cheese:|:eyes:|:peek:|:clown:)([\s\'\".,!?\)]|<br>|$)/,noncapturingRegexp:/(?:^|[\s\'\".\(])(O:\)(?!\))|O:\-\)(?!\))|0:\)(?!\))|0:\-\)(?!\))|\'\-_\-|:3|o\.O|O_O|o_o|0_0|O\.o|:\'\(|:\'\-\(|3:\)(?!\))|3:\-\)(?!\))|:dog:|\-3\-|:\(|:\-\(|:\[|=\(|\)=|:o|:\-O|:O|:\-o|=D|:\-D|:D|>:\(|>:\-\(|<3|&lt;3|\^_\^|\^~\^|:\*|:\-\*|\(y\)(?!\))|:like:|\(Y\)(?!\))|T_T|T\-T|ToT|T\.T|:\-\||:\||:v|:V|<\(\"\)(?!\))|>_<|>\.<|:poop:|:\|\]|\(\^\^\^\)(?!\))|:\)(?!\))|:\-\)(?!\))|:\]|\(:|=\)(?!\))|\(=|\-_\-|B\||8\-\||8\||B\-\||B\-\)(?!\))|8\-\)(?!\))|8\)(?!\))|\(n\)(?!\))|\(N\)(?!\))|:\+1:|:thumbsup:|:P|:\-P|:\-p|:p|=P|:trans:|:\/|:\-\/|:\\|:\-\\|=\/|=\\|>:o|>:O|>:\-O|>:\-o|;\)(?!\))|;\-\)(?!\))|;\*|;\-\*|;\-P|;P|;\-p|;p|:cheese:|:eyes:|:peek:|:clown:)(?:[\s\'\".,!?\)]|<br>|$)/}}),null);
__d("EmoticonRenderer",["EmoticonsList"],(function(a,b,c,d,e,f,g){"use strict";var h=["LIKE","PACMAN","FACE_WITH_COLON_THREE"];function i(a){var b=[],c=new RegExp(d("EmoticonsList").regexp),e=0,f=a.match(c);while(f!==null){var g=f[1],i=f[2].split(""),j=d("EmoticonsList").emotes[f[2]];j=d("EmoticonsList").emoji[j];b.push({chars:i,isCustom:h.includes(j),key:j,offset:e+f.index+g.length});e+=f.index+f[0].length;f=a.slice(e).match(c)}return b}function a(a,b,c){var d=i(a),e=[],f=0;d.forEach(function(d){var g=d.offset;g>f&&e.push(a.substr(f,g-f));d.isCustom?e.push(c(d.key,d.chars)):e.push(b(d.key));f=g+d.chars.length});e.push(a.substr(f,a.length-f));return e}g.parse=i;g.render=a}),98);
__d("CometEmoticonTransform",["CometEmojiWithContextualSize.react","EmoticonRenderer","FBEmojiResource","FBEmojiUtils","baseTextTransformAllStrings","react"],(function(a,b,c,d,e,f,g){"use strict";var h,i=h||d("react");function a(a,b){a=a===void 0?{}:a;var e=a.size;return function(a){var f=0;return c("baseTextTransformAllStrings")(a,function(a,b){return d("EmoticonRenderer").render(a,function(a){return i.jsx(c("CometEmojiWithContextualSize.react"),{emoji:[d("FBEmojiUtils").codepointsToString(a.split("_").map(function(a){return Number("0x"+a)}))],resource:new(c("FBEmojiResource"))(a),size:e},b+"-"+f++)},function(a,d){return i.jsx(c("CometEmojiWithContextualSize.react"),{emoji:d,resource:new(c("FBEmojiResource"))(a),size:e},b+"-"+f++)})},b)}}g["default"]=a}),98);
__d("CometStickerPickerFlyoutTagSelectorRootQuery_facebookRelayOperation",[],(function(a,b,c,d,e,f){e.exports="9341116515996575"}),null);
__d("CometStickerPickerFlyoutTagSelectorRootQuery$Parameters",["CometStickerPickerFlyoutTagSelectorRootQuery_facebookRelayOperation"],(function(a,b,c,d,e,f){"use strict";a={kind:"PreloadableConcreteRequest",params:{id:b("CometStickerPickerFlyoutTagSelectorRootQuery_facebookRelayOperation"),metadata:{},name:"CometStickerPickerFlyoutTagSelectorRootQuery",operationKind:"query",text:null}};e.exports=a}),null);
__d("CometStickerPickerFlyoutTagSelectorRoot.entrypoint",["CometStickerPickerFlyoutTagSelectorRootQuery$Parameters","JSResourceForInteraction","WebPixelRatio"],(function(a,b,c,d,e,f,g){"use strict";a={getPreloadProps:function(a){var b=a.count;a=a.cursor;return{queries:{flyoutTagsQueryReference:{parameters:c("CometStickerPickerFlyoutTagSelectorRootQuery$Parameters"),variables:{count:b,cursor:a,scale:d("WebPixelRatio").get()}}}}},root:c("JSResourceForInteraction")("CometStickerPickerFlyoutTagSelectorRoot.react").__setRef("CometStickerPickerFlyoutTagSelectorRoot.entrypoint")};g["default"]=a}),98);
__d("CometUFICommentScrollOptionsContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b={behavior:"smooth",verticalAlign:"center"};c=a.createContext({scrollAnchorOptions:babelHelpers["extends"]({},b),scrollIntoViewOptions:babelHelpers["extends"]({},b)});g["default"]=c}),98);
__d("CometUFIConversationGuideContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({addGiphyStickerToComposer:function(){},addStickerToComposer:function(){},getIsComposerEmpty:function(){return!1},onComposerEmptyStateChange:function(){},setAddGiphyStickerToComposer:function(){},setAddStickerToComposer:function(){},setGetIsComposerEmpty:function(){},setOnComposerEmptyStateChange:function(){},setWriteToComposer:function(){},unsetAddGiphyStickerToComposer:function(){},unsetAddStickerToComposer:function(){},unsetOnComposerEmptyStateChange:function(){},unsetWriteToComposer:function(){},writeToComposer:function(){}});g["default"]=b}),98);
__d("CometUFIConversationGuideLoggingContext",["react"],(function(a,b,c,d,e,f,g){"use strict";var h;a=h||d("react");b=a.createContext({callerID:"",defaultValue:!0,guideShown:null,sessionID:null,setGuideShown:function(){}});g["default"]=b}),98);
__d("ConversationGuideSuggestionSelectedFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("2132");b=d("FalcoLoggerInternal").create("conversation_guide_suggestion_selected",a);e=b;g["default"]=e}),98);
__d("ConversationGuideSuggestionVpvFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("2133");b=d("FalcoLoggerInternal").create("conversation_guide_suggestion_vpv",a);e=b;g["default"]=e}),98);
__d("CometUFIConversationGuideUtils",["ConversationGuideSuggestionSelectedFalcoEvent","ConversationGuideSuggestionVpvFalcoEvent","FBLogger","XPlatReactEnvironment"],(function(a,b,c,d,e,f,g){"use strict";e=d("XPlatReactEnvironment").isWeb()?"comet_above_composer":"fbvr_above_composer";var h={surface:"comment_composer"};function a(a){c("ConversationGuideSuggestionSelectedFalcoEvent").log(function(){return i(a)})}function b(a){c("ConversationGuideSuggestionVpvFalcoEvent").log(function(){return i(a)})}function i(a){var b=a.callerID,d=a.feedbackID,e=a.id,f=a.index,g=a.sessionID;a=a.suggestionType;if(g==null){var i=c("FBLogger")("conversation_guide");i.mustfix("Logged Comet Conversation Guide event without specifying session ID")}return babelHelpers["extends"]({caller_id:b,feedback_id:d,session_id:g,suggestion_id:e,suggestion_index:f.toString(),suggestion_type:a},h)}g.CALLER_ID=e;g.COMMON_LOGGING_VALUES=h;g.logSuggestionSelected=a;g.logSuggestionImpression=b}),98);
__d("FDSContextualMessage.react",["fbt","ix","CometRow.react","CometRowItem.react","FDSIcon.react","FDSTextPairing.react","FbtResultBase","fbicon","react","react-compiler-runtime","stylex"],(function(a,b,c,d,e,f,g,h,i){"use strict";var j,k,l=k||d("react"),m={root:{borderStartStartRadius:"xw5cjc7",borderStartEndRadius:"x1dmpuos",borderEndEndRadius:"x1vsv7so",borderEndStartRadius:"xau1kf4",overflowX:"x6ikm8r",overflowY:"x10wlt62",paddingTop:"x1iorvi4",paddingInlineEnd:"x11lfxj5",paddingInlineStart:"x135b78x",paddingBottom:"x1l90r2v",$$css:!0}},n={highlight:{backgroundColor:"xwnonoy",$$css:!0},"highlight-bg":{backgroundColor:"xfmpgtx",$$css:!0},primary:{backgroundColor:"x1jx94hy",$$css:!0},secondary:{backgroundColor:"xlhe6ec",$$css:!0}};function a(a){var b=d("react-compiler-runtime").c(36),e,f,g;b[0]!==a?(f=a.ref,g=a.xstyle,e=babelHelpers.objectWithoutPropertiesLoose(a,["ref","xstyle"]),b[0]=a,b[1]=e,b[2]=f,b[3]=g):(e=b[1],f=b[2],g=b[3]);a=e;var k=a.addOnPrimary,o=a.addOnSecondary,p=a.body,q=a.headline,r=a.headlineLineLimit,s=a.icon,t=a.level,u=a.meta,v=a.paddingTop,w=a.testid;a=a.type;t=t===void 0?3:t;a=a===void 0?"primary":a;var x=e.onClose!=null?e.onClose:null;e=e.headlineAriaLabel!=null?e.headlineAriaLabel:typeof q==="string"||q instanceof c("FbtResultBase")?q:null;var y;b[4]!==a||b[5]!==g?(y=(j||(j=c("stylex"))).props(m.root,n[a],g),b[4]=a,b[5]=g,b[6]=y):y=b[6];b[7]!==s?(g=s!=null?l.jsx(c("CometRowItem.react"),{verticalAlign:"top",children:l.jsx("div",babelHelpers["extends"]({className:"x1rdy4ex"},{children:s}))}):null,b[7]=s,b[8]=g):g=b[8];s=a==="highlight"?"white":"secondary";var z=a==="highlight"?"white":"primary";r=r!=null?r:2;var A;b[9]!==p||b[10]!==q||b[11]!==t||b[12]!==s||b[13]!==z||b[14]!==r?(A=l.jsx(c("CometRowItem.react"),{expanding:!0,children:l.jsx(c("FDSTextPairing.react"),{body:p,bodyColor:s,headline:q,headlineColor:z,headlineLineLimit:r,isSemanticHeading:!0,level:t})}),b[9]=p,b[10]=q,b[11]=t,b[12]=s,b[13]=z,b[14]=r,b[15]=A):A=b[15];b[16]!==e||b[17]!==x||b[18]!==a?(p=x!=null?l.jsx(c("CometRowItem.react"),{verticalAlign:"top",children:l.jsx("div",babelHelpers["extends"]({className:"x1lxpwgx x9otpla"},{children:l.jsx(c("FDSIcon.react"),{"aria-label":e!=null?h._(/*BTDS*/"Dismiss {card name} card",[h._param("card name",e)]):h._(/*BTDS*/"Dismiss card"),color:a==="highlight"?"white":"secondary",icon:d("fbicon")._(i("478232"),16),onPress:x,size:16,testid:void 0})}))}):null,b[16]=e,b[17]=x,b[18]=a,b[19]=p):p=b[19];b[20]!==v||b[21]!==g||b[22]!==A||b[23]!==p?(q=l.jsxs(c("CometRow.react"),{paddingTop:v,verticalAlign:"center",children:[g,A,p]}),b[20]=v,b[21]=g,b[22]=A,b[23]=p,b[24]=q):q=b[24];b[25]!==t||b[26]!==u?(s=u!=null&&l.jsx(c("CometRow.react"),{paddingTop:12,children:l.jsx(c("CometRowItem.react"),{children:l.jsx(c("FDSTextPairing.react"),{level:t,meta:u})})}),b[25]=t,b[26]=u,b[27]=s):s=b[27];b[28]!==k||b[29]!==o||b[30]!==f||b[31]!==q||b[32]!==s||b[33]!==y||b[34]!==w?(z=l.jsxs("div",babelHelpers["extends"]({},y,{"data-testid":void 0,ref:f,children:[q,k,o,s]})),b[28]=k,b[29]=o,b[30]=f,b[31]=q,b[32]=s,b[33]=y,b[34]=w,b[35]=z):z=b[35];return z}g["default"]=a}),226);
__d("FDSTriangleDownFilled16PNGIcon.react",["ix","fbicon"],(function(a,b,c,d,e,f,g,h){"use strict";a=d("fbicon")._(h("481882"),16);b=a;g["default"]=b}),98);
__d("TrustedTypesLinkTagHTMLPolicy",["TrustedTypes","err"],(function(a,b,c,d,e,f,g){"use strict";a={createHTML:function(a){if(a==="<link />")return a;throw c("err")("Violating Trusted Type policies. Only works for '<link />' strings.")}};b=c("TrustedTypes").createPolicy("link-tag-html",a);d=b;g["default"]=d}),98);
__d("getMarkupWrap",["invariant","ExecutionEnvironment","TrustedTypesLinkTagHTMLPolicy"],(function(a,b,c,d,e,f,g,h){var i,j=(i||c("ExecutionEnvironment")).canUseDOM?document.createElement("div"):null,k={};b=[1,'<select multiple="true">',"</select>"];d=[1,"<table>","</table>"];e=[3,"<table><tbody><tr>","</tr></tbody></table>"];var l=[1,'<svg xmlns="http://www.w3.org/2000/svg">',"</svg>"],m={"*":[1,"?<div>","</div>"],area:[1,"<map>","</map>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],legend:[1,"<fieldset>","</fieldset>"],param:[1,"<object>","</object>"],tr:[2,"<table><tbody>","</tbody></table>"],optgroup:b,option:b,caption:d,colgroup:d,tbody:d,tfoot:d,thead:d,td:e,th:e};f=["circle","clipPath","defs","ellipse","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","text","tspan"];f.forEach(function(a){m[a]=l,k[a]=!0});function a(a){a=a;!j&&h(0,144);Object.prototype.hasOwnProperty.call(m,a)||(a="*");Object.prototype.hasOwnProperty.call(k,a)||(a==="*"?j.innerHTML=c("TrustedTypesLinkTagHTMLPolicy").createHTML("<link />"):j.innerHTML="<"+a+"></"+a+">",k[a]=!j.firstChild);return k[a]?m[a]:null}g["default"]=a}),98);
__d("createNodesFromMarkup",["invariant","ExecutionEnvironment","TrustedTypesNoOpPolicy_DO_NOT_USE","getMarkupWrap"],(function(a,b,c,d,e,f,g,h){var i,j=(i||c("ExecutionEnvironment")).canUseDOM?document.createElement("div"):null,k=/^\s*<(\w+)/;function l(a){a=a.match(k);return a&&a[1].toLowerCase()}function a(a,b){var d=j;!j&&h(0,5001);var e=l(a);e=e&&c("getMarkupWrap")(e);if(e){d.innerHTML=e[1]+a+e[2];e=e[0];while(e--)d=d.lastChild}else d.innerHTML=c("TrustedTypesNoOpPolicy_DO_NOT_USE").createHTML(a);e=d.getElementsByTagName("script");e.length&&(b||h(0,5002),Array.from(e).forEach(b));a=Array.from(d.childNodes);while(d.lastChild)d.removeChild(d.lastChild);return a}g["default"]=a}),98);
__d("evalGlobal",[],(function(a,b,c,d,e,f){function a(a){if(typeof a!=="string")throw new TypeError("JS sent to evalGlobal is not a string. Only strings are permitted.");if(!a)return;var b=document.createElement("script");try{b.appendChild(document.createTextNode(a))}catch(c){b.text=a}a=document.getElementsByTagName("head")[0]||document.documentElement;a.appendChild(b);a.removeChild(b)}f["default"]=a}),66);
__d("HTML",["invariant","Bootloader","FBLogger","createNodesFromMarkup","emptyFunction","evalGlobal"],(function(a,b,c,d,e,f,g){var h=/(<(\w+)[^>]*?)\/>/g,i={abbr:!0,area:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0};a=function(){"use strict";function a(c){c&&typeof c.__html==="string"&&(c=c.__html);if(!(this instanceof a))return c instanceof a?c:new a(c);if(c){var d=typeof c;d==="string"||g(0,277,d)}this._markup=c||"";this._defer=!1;this._nodes=null;this._inlineJS=b("emptyFunction");this._rootNode=null;this._hasInlineJs=!1}var c=a.prototype;c.toString=function(){return this._markup};c.getContent=function(){return this._markup};c.getNodes=function(){this._fillCache();return this._nodes};c.getRootNode=function(){this._rootNode&&g(0,278);var a=this.getNodes();if(a.length===1)this._rootNode=a[0];else{var b=document.createDocumentFragment();for(var c=0;c<a.length;c++)b.appendChild(a[c]);this._rootNode=b}return this._rootNode};c.getAction=function(){var a=this;this._fillCache();var b=function(){a._inlineJS()};return this._defer?function(){setTimeout(b,0)}:b};c._fillCache=function(){if(this._nodes!==null)return;if(!this._markup){this._nodes=[];return}var a=this._markup.replace(h,function(a,b,c){return i[c.toLowerCase()]?a:b+"></"+c+">"}),c=null;a=b("createNodesFromMarkup")(a,function(a){b("FBLogger")("staticresources").warn("HTML: encountered script node while parsing, hasSrc=%s, type=%s",Boolean(a.src),a.type==null||a.type===""?"<unknown>":a.type),a.type!=="application/ld+json"&&a.type!=="application/json"&&(c=c||[],c.push(a.src?b("Bootloader").requestJSResource_UNSAFE_NEEDS_REVIEW_BY_SECURITY_AND_XFN.bind(b("Bootloader"),a.src):b("evalGlobal").bind(null,a.innerHTML)),a.parentNode.removeChild(a))});c&&(this._hasInlineJs=!0,this._inlineJS=function(){for(var a=0;a<c.length;a++)c[a]()});this._nodes=a};c.setDeferred=function(a){this._defer=!!a;return this};c.hasInlineJs=function(){return this._hasInlineJs};a.isHTML=function(b){return!!b&&(b instanceof a||b.__html!==void 0)};a.replaceJSONWrapper=function(b){return b&&b.__html!==void 0?new a(b.__html):b};return a}();e.exports=a}),null);
__d("lastx",["invariant"],(function(a,b,c,d,e,f,g,h){"use strict";function a(a){var b=null;if(Array.isArray(a))a.length&&(b={value:a[a.length-1]});else for(a of a)b=b||{},b.value=a;if(b)return b.value;h(0,1145)}g["default"]=a}),98);
__d("MultiKeyMap",["invariant","lastx"],(function(a,b,c,d,e,f,g,h){"use strict";var i=typeof WeakMap==="function";a=function(){function a(){this.$1=this.$2(),this.$3=null}var b=a.prototype;b.set=function(a,b){this.$4(a);var c=this.$1;for(var d=0;d<a.length-1;d++){var e=a[d],f=this.$5(c,e).get(e);f==null&&(f=this.$2(),this.$5(c,e).set(e,f));f.type==="map"||h(0,1732);c=f}e=a[a.length-1];this.$5(c,e).set(e,{type:"value",value:b})};b.get=function(a){this.$4(a);var b=this.$6(a);b=b&&b.get(c("lastx")(a));return b==null||b.type!=="value"?void 0:b.value};b["delete"]=function(a){this.$4(a);var b=this.$6(a);return b?b["delete"](c("lastx")(a)):!1};b.$5=function(a,b){return j(b)?a.cache:a.weakCache};b.$6=function(a){var b=this.$1;for(var d=0;d<a.length-1;d++){var e,f=a[d];if(((e=b)==null?void 0:e.type)==="map")b=this.$5(b,f).get(f);else return void 0}e=c("lastx")(a);f=b&&this.$5(b,e);return f};b.$2=function(){return{type:"map",cache:new Map(),weakCache:i?new WeakMap():new Map()}};b.$4=function(a){this.$3==null&&(this.$3=a.length);if(this.$3!==a.length)throw new Error("MultiKeyMap called with different number of keys");if(a.length<1)throw new Error("MultiKeyMap called with empty array of keys")};return a}();function j(a){var b=typeof a;return b==="number"||b==="string"||b==="boolean"||a==null}g["default"]=a}),98);
__d("StarsViewerFunnelEventFalcoEvent",["FalcoLoggerInternal","getFalcoLogPolicy_DO_NOT_USE"],(function(a,b,c,d,e,f,g){"use strict";a=c("getFalcoLogPolicy_DO_NOT_USE")("6440");b=d("FalcoLoggerInternal").create("stars_viewer_funnel_event",a);e=b;g["default"]=e}),98);
__d("cometIsMimeTypeForMedia",["FBLogger","UFICommentFileInputAcceptValues"],(function(a,b,c,d,e,f,g){"use strict";var h={file:null,photo:null,video:null},i={"image/gif":"video","image/webp":"video"};function j(a){return a.split(",").map(function(a){return a.trim()}).map(function(a){return a.replace(/\/\*$/,"/")})}function k(a,b,d){h[a]==null&&(h[a]=j(d));d=h[a];if(d==null)throw c("FBLogger")("ufi2").mustfixThrow("CometIsMimeTypeForMedia prefixes can't be null");var e=i[b];return e!=null?e===a:d.some(function(a){return b.startsWith(a)})}function a(a){return k("photo",a,d("UFICommentFileInputAcceptValues").photos)}function b(a){return k("video",a,d("UFICommentFileInputAcceptValues").videos)}function e(a){return k("file",a,d("UFICommentFileInputAcceptValues").files)}g.mimeTypeAcceptStringToArray=j;g.isMimeTypeForPhoto=a;g.isMimeTypeForVideo=b;g.isMimeTypeForFile=e}),98);
__d("memoizeWithArgsWeak",["MultiKeyMap"],(function(a,b,c,d,e,f,g){"use strict";function a(a,b){b===void 0;var d=new(c("MultiKeyMap"))();return function(){for(var b=arguments.length,c=new Array(b),e=0;e<b;e++)c[e]=arguments[e];var f=d.get(c);if(f!==void 0)return f;var g=a.apply(void 0,c);d.set(c,g);return g}}g["default"]=a}),98);
__d("nodeIsInConnection",[],(function(a,b,c,d,e,f){"use strict";function a(a,b,c){a=a.getLinkedRecords("edges");if(a==null){c&&c("Connection contains no edges field.");return!1}var d=!1;b=b.getDataID();if(b==null){c&&c("Search node has no ID.");return!1}for(var e of a){var f=e&&e.getLinkedRecord("node");if(f==null){c&&c("Edge at index "+a.indexOf(e)+" has no node.");continue}if(f.getDataID()===b){d=!0;break}}return d}f["default"]=a}),66);
__d("requireNUX",[],(function(a,b,c,d,e,f){"use strict";function a(a,b){throw new Error("Cannot use raw untransformed requireNUX.")}f["default"]=a}),66);